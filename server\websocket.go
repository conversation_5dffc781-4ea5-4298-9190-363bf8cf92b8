package server

import (
	"encoding/json"
	"github.com/gorilla/websocket"
	"sync"
)

type WebSocket struct {
	connections map[*websocket.Conn]int
	lock        sync.Mutex
}

var wsInstance *WebSocket

type WsMsg struct {
	MsgType string                 `json:"msgtype"`
	Data    map[string]interface{} `json:"data"`
}

func GetWebSocketInstance() *WebSocket {
	if wsInstance == nil {
		wsInstance = &WebSocket{
			connections: make(map[*websocket.Conn]int),
		}
	}
	return wsInstance
}

func (c *WebSocket) Listening(conn *websocket.Conn, cb func(messageType int, p []byte)) {
	c.lock.Lock()
	c.connections[conn] = 1
	c.lock.Unlock()
	for {
		mt, p, err := conn.ReadMessage()
		if err != nil {
			break
		}
		if cb != nil {
			cb(mt, p)
		}
	}
	c.lock.Lock()
	_ = conn.Close()
	delete(c.connections, conn)
	c.lock.Unlock()
}

func (c *WebSocket) WriteTextMessage(msg *WsMsg) {
	bytes, _ := json.Marshal(msg)
	c.lock.Lock()
	for k := range c.connections {
		_ = k.WriteMessage(websocket.TextMessage, bytes)
	}
	c.lock.Unlock()
}
