package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"github.com/beego/beego/logs"
	"github.com/go-sql-driver/mysql"
	"github.com/imroc/req"
	"github.com/shopspring/decimal"
	"github.com/spf13/viper"

	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
)

type KeFuController struct {
}

const TIYANJIN_OPTIONS = `[{"field":"Id","name":"Id"},
{"field":"SellerName","name":"运营商"},
{"field":"ChannelName","name":"渠道"},
{"field":"CSId","name":"客服工号"},
{"field":"CSGroup","name":"团队编号"},
{"field":"UserId","name":"玩家Id"},
{"field":"TopAgentId","name":"顶级代理Id"},
{"field":"TgName","name":"玩家TG"},
{"field":"Account","name":"玩家账号"},
{"field":"KeFuTgName","name":"跟进飞机号"},
{"field":"Address","name":"钱包地址"},
{"field":"IpCount","name":"同ip人数"},
{"field":"PwdCount","name":"同密码人数"},
{"field":"RealAmount","name":"首充金额"},
{"field":"FirstWithdrawAmount","name":"首次提款金额"},
{"field":"Symbol","name":"币种"},
{"field":"Amount","name":"赠送金额"},
{"field":"AuditAccount","name":"审核人"},

{"field":"KefuState","name":"客服审核状态","values":{"0":"", "1":"待审核","2":"客服审核拒绝", "3":"客服审核通过"}},
{"field":"FengkongState","name":"风控审核状态","values":{"0":"", "1":"待审核","2":"风控审核拒绝", "3":"风控审核通过"}},
{"field":"PaifaState","name":"派发状态","values":{"0":"", "1":"出款中","2":"出款完成"}},

{"field":"Memo","name":"备注"},
{"field":"Games","name":"游戏种类"},

{"field":"BetAmountTransferTrx","name":"转账下注金额TRX"},
{"field":"BetCountTransferTrx","name":"转账下注笔数TRX"},
{"field":"RewardAmountTransferTrx","name":"转账返奖金额TRX"},
{"field":"TransferProfitTrx","name":"转账盈亏金额TRX"},

{"field":"BetAmountTransferUsdt","name":"转账下注金额USDT"},
{"field":"BetCountTransferUsdt","name":"转账下注笔数USDT"},
{"field":"RewardAmountTransferUsdt","name":"转账返奖金额USDT"},
{"field":"TransferProfitUsdt","name":"转账盈亏金额USDT"},

{"field":"BetAmountYue","name":"余额下注金额"},
{"field":"BetCountYue","name":"余额下注笔数"},
{"field":"RewardAmountYue","name":"余额返奖金额"},
{"field":"OverageProfit","name":"余额盈亏金额"},

{"field":"BetAmountThird","name":"三方下注金额"},
{"field":"BetCountThird","name":"三方下注次数"},
{"field":"RewardAmountThird","name":"三方返奖金额"},
{"field":"ThirdProfit","name":"三方盈亏金额"},

{"field":"CreateTime","name":"申请时间"},
{"field":"HbcOrder","name":"hbc订单"},
{"field":"HbcState","name":"hbc状态"},
{"field":"LoginTime","name":"最后登录时间"},
{"field":"BetTime","name":"最后投注时间"},
{"field":"AuditTime","name":"审核时间"},
{"field":"SendAccount","name":"发放人"},
{"field":"SendTime","name":"发放时间"},
{"field":"RegisterTime","name":"玩家注册时间"},
{"field":"TxId","name":"出款哈希"}]`

func (c *KeFuController) Init() {
	server.Http().Post("/api/kefu/group_list", c.group_list)
	server.Http().Post("/api/kefu/add_group", c.add_group)

	server.Http().Post("/api/kefu/host_list", c.host_list)
	server.Http().Post("/api/kefu/add_host", c.add_host)
	server.Http().Post("/api/kefu/del_host", c.del_host)

	server.Http().Post("/api/kefu/kefu_list", c.kefu_list)
	server.Http().Post("/api/kefu/add_kefu", c.add_kefu)
	server.Http().Post("/api/kefu/modify_kefu", c.modify_kefu)

	server.Http().Post("/api/kefu/user_list", c.user_list)
	server.Http().Post("/api/kefu/user_bind", c.user_bind)
	server.Http().Post("/api/kefu/bind_history", c.bind_history)

	server.Http().Post("/api/kefu/tiyanjing_create", c.tiyanjing_create)
	server.Http().Post("/api/kefu/tiyanjing_list", c.tiyanjing_list)
	server.Http().Post("/api/kefu/tiyanjing_update", c.tiyanjing_update)
	server.Http().Post("/api/kefu/tiyanjing_audit", c.tiyanjing_audit)

	server.Http().Post("/api/kefu/tiyanjing_listex", c.tiyanjing_listex)
	server.Http().Post("/api/kefu/tiyanjing_auditex", c.tiyanjing_auditex)

	server.Http().Post("/api/kefu/binded_list", c.binded_list)
	server.Http().Post("/api/kefu/binded_update", c.binded_update)
	server.Http().Post("/api/kefu/user_update_log", c.user_update_log)

	server.Http().Post("/api/kefu/kefu_yeji", c.kefu_yeji)
	server.Http().Post("/api/kefu/caijing_detail", c.caijing_detail)

	server.Http().Post("/api/kefu/ip_detail", c.ip_detail)
	server.Http().Post("/api/kefu/login_ip_detail", c.login_ip_detail)
	server.Http().Post("/api/kefu/ip_detailex", c.ip_detailex)
	server.Http().Post("/api/kefu/batch_user_bind", c.batch_user_bind)
}

func (c *KeFuController) group_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "客服系统", "客服团队", "查", "查看客户团队")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	data, _ := server.Db().Table("x_kefu_group").Where(where).OrderBy("Id desc").GetList()
	ctx.RespOK(*data)
}

func (c *KeFuController) add_group(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		GroupName string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "客服系统", "客服团队", "增", "添加客服团队")
	if token == nil {
		return
	}
	_, err := server.Db().Table("x_kefu_group").Insert(gin.H{
		"SellerId":  reqdata.SellerId,
		"ChannelId": reqdata.ChannelId,
		"GroupName": reqdata.GroupName,
	})
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *KeFuController) host_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		GroupName string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "客服系统", "客服团队", "查", "查看客户团队域名")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "GroupName", "=", reqdata.GroupName, nil)
	data, _ := server.Db().Table("x_kefu_host").Where(where).OrderBy("Id desc").GetList()
	ctx.RespOK(*data)
}

func (c *KeFuController) add_host(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		GroupName string `validate:"required"`
		Host      string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "客服系统", "客服团队", "增", "绑定客服团队域名")
	if token == nil {
		return
	}
	_, err := server.Db().Table("x_kefu_host").Insert(gin.H{
		"SellerId":  reqdata.SellerId,
		"ChannelId": reqdata.ChannelId,
		"GroupName": reqdata.GroupName,
		"Host":      reqdata.Host,
	})
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *KeFuController) del_host(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		GroupName string `validate:"required"`
		Host      string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "客服系统", "客服团队", "删", "删除客户团队域名")
	if token == nil {
		return
	}
	server.Db().Conn().Exec("delete from x_kefu_host where GroupName = ? and Host = ?", reqdata.GroupName, reqdata.Host)
	ctx.RespOK()
}

func (c *KeFuController) kefu_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page      int
		PageSize  int
		Account   string
		SellerId  int
		ChannelId int
		CSGroup   string
		CSId      string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	AccountType := 2
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服工号", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	total, data := server.DBAdminUser_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.Account, reqdata.ChannelId, AccountType, reqdata.CSGroup, reqdata.CSId)
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *KeFuController) add_kefu(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Account    string `validate:"required"`
		SellerId   int
		Password   string `validate:"required"`
		State      int    `validate:"required"`
		Remark     string
		IpWhite    string
		ChannelId  int
		CSGroup    string `validate:"required"`
		CSId       string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	RoleName := "客服工号"

	where := abugo.AbuDbWhere{}
	where.Add("and", "GroupName", "=", reqdata.CSGroup, nil)
	csgroup, _ := server.Db().Table("x_kefu_group").Where(where).GetOne()
	if csgroup == nil {
		ctx.RespErrString(true, &errcode, "客服团队不存在")
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服工号", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "select id from admin_role  where SellerId = ? and RoleName = ?"
	var rid int
	server.Db().QueryScan(sql, []interface{}{reqdata.SellerId, RoleName}, &rid)
	if ctx.RespErrString(rid == 0, &errcode, "角色不存在") {
		return
	}
	sql = "select id from admin_user where Account = ? and SellerId = ?"
	var uid int
	server.Db().QueryScan(sql, []interface{}{reqdata.Account, reqdata.SellerId}, &uid)
	if ctx.RespErrString(uid > 0, &errcode, "账号已经存在") {
		return
	}
	sql = "insert into admin_user(Account,Password,SellerId,RoleName,State,IpWhite,Remark,ChannelId,CSGroup,CSId)values(?,?,?,?,?,?,?,?,?,?)"
	err = server.Db().QueryNoResult(sql, reqdata.Account, reqdata.Password,
		reqdata.SellerId, RoleName, reqdata.State, reqdata.IpWhite, reqdata.Remark, reqdata.ChannelId, reqdata.CSGroup, reqdata.CSId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	server.WriteAdminLog("添加客服工号", ctx, reqdata)
	ctx.RespOK()
}

func (c *KeFuController) modify_kefu(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Account    string `validate:"required"`
		SellerId   int
		Password   string
		State      int `validate:"required"`
		Remark     string
		IpWhite    string
		RecvNotice int
		ChannelId  int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服工号", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.IpWhite = strings.Trim(reqdata.IpWhite, "")
	sql := ""
	if len(reqdata.Password) > 0 {
		sql = "update admin_user set State = ?,Remark = ?,`Password` = ?,IpWhite = ? where Account = ? and ChannelId = ? and SellerId = ?"
		server.Db().QueryNoResult(sql, reqdata.State, reqdata.Remark, reqdata.Password, reqdata.IpWhite, reqdata.Account, reqdata.ChannelId, reqdata.SellerId)
	} else {
		sql = "update admin_user set State = ?,Remark = ?,IpWhite = ? where Account = ? and ChannelId = ? and SellerId = ?"
		server.Db().QueryNoResult(sql, reqdata.State, reqdata.Remark, reqdata.IpWhite,
			reqdata.Account, reqdata.ChannelId, reqdata.SellerId)
	}
	if reqdata.State != 1 {
		sql = "select Token from admin_user where Account = ? and SellerId = ? "
		var tokenstr string
		server.Db().QueryScan(sql, []interface{}{reqdata.Account, reqdata.SellerId}, &tokenstr)
		if len(tokenstr) > 0 {
			server.Http().DelToken(tokenstr)
		}
	}
	server.WriteAdminLog("修改客服工号", ctx, reqdata)
	server.KickOutAdminLogin(reqdata.Account)
	ctx.RespOK()
}

func (c *KeFuController) user_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Account  string
		UserId   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服绑定", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Account", "=", reqdata.Account, "")
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	data, _ := server.Db().Table("x_user").Select("ChannelId,Account,UserId,TgName,CSGroup,CSId,RegisterTime").Where(where).Limit(1).GetList()
	ctx.RespOK(data)
}

func (c *KeFuController) user_bind(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int
		CSGroup    string
		CSId       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服绑定", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.CSGroup == "" {
		ctx.RespErrString(true, &errcode, "客服团队不可为空")
		return
	}
	if reqdata.CSId == "" {
		ctx.RespErrString(true, &errcode, "客服工号不可为空")
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "CSGroup", "=", reqdata.CSGroup, nil)
	where.Add("and", "CSId", "=", reqdata.CSId, nil)
	adminuser, _ := server.Db().Table("admin_user").Where(where).GetOne()
	if adminuser == nil {
		ctx.RespErrString(true, &errcode, "客服工号不存在")
		return
	}
	server.Db().CallProcedure("x_kefu_bind", reqdata.UserId, reqdata.CSGroup, reqdata.CSId, token.Account)
	ctx.RespOK()
	server.WriteAdminLog("绑定客服工号", ctx, reqdata)
}

func (c *KeFuController) batch_user_bind(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     []int32 `validate:"required"`
		CSGroup    string
		CSId       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服绑定", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.CSGroup == "" {
		ctx.RespErrString(true, &errcode, "客服团队不可为空")
		return
	}
	if reqdata.CSId == "" {
		ctx.RespErrString(true, &errcode, "客服工号不可为空")
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "CSGroup", "=", reqdata.CSGroup, nil)
	where.Add("and", "CSId", "=", reqdata.CSId, nil)
	adminuser, _ := server.Db().Table("admin_user").Where(where).GetOne()
	if adminuser == nil {
		ctx.RespErrString(true, &errcode, "客服工号不存在")
		return
	}
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	userList, err := userDb.Select(userTb.UserID, userTb.SellerID, userTb.ChannelID, userTb.CSGroup, userTb.CSID, userTb.Account).Where(userTb.UserID.In(reqdata.UserId...)).Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if len(reqdata.UserId) != len(userList) {
		ctx.RespErrString(true, &errcode, "玩家ID错误")
		return
	}
	// 事务处理
	rerr := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		userTb = tx.XUser
		userDb = tx.XUser.WithContext(context.Background())
		data := map[string]interface{}{
			"CSGroup":    reqdata.CSGroup,
			"CSId":       reqdata.CSId,
			"CSBindTime": time.Now(),
		}
		_, err = userDb.Where(userTb.UserID.In(reqdata.UserId...)).Updates(data)
		if err != nil {
			logs.Error(err)
			return err
		}
		// 批量添加记录
		kefuBindHistoryDb := tx.XKefuBindHistory.WithContext(context.Background())
		kefuBindHistorydata := make([]*model.XKefuBindHistory, 0, len(userList))
		for _, user := range userList {
			kefuBindHistoryModel := &model.XKefuBindHistory{
				SellerID:     user.SellerID,
				ChannelID:    user.ChannelID,
				UserID:       user.UserID,
				BeforeGroup:  user.CSGroup,
				AfterGroup:   reqdata.CSGroup,
				BeforeID:     user.CSID,
				AfterID:      reqdata.CSId,
				Account:      user.Account,
				AdminAccount: token.Account,
			}
			kefuBindHistorydata = append(kefuBindHistorydata, kefuBindHistoryModel)
		}
		err = kefuBindHistoryDb.CreateInBatches(kefuBindHistorydata, len(kefuBindHistorydata))
		if err != nil {
			logs.Error(err)
			return err
		}
		return nil
	})
	if rerr != nil {
		ctx.RespErrString(true, &errcode, rerr.Error())
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("绑定批量客服工号", ctx, reqdata)
}

func (c *KeFuController) bind_history(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId     int
		UserId       int
		Account      string
		AdminAccount string
		Page         int
		PageSize     int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服绑定", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "Account", "=", reqdata.Account, "")
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "AdminAccount", "=", reqdata.AdminAccount, "")
	total, data := server.Db().Table("x_kefu_bind_history").OrderBy("id desc").Where(where).PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("total", total)
	ctx.Put("data", data)
	ctx.RespOK()
}

func tiyanjinNewWord(data *[]map[string]interface{}) {
	//状态 1待审核,2审核拒绝,3审核通过
	//1：客服待审核状态, 风控审核状态为空，派发状态为空
	//2：客服审核拒绝，风控审核状态为空，派发状态为空
	//3：客服审核通过，风控审核状态为待审核，派发状态为空

	//状态 4拒绝发放,5已发放,6正在出款,7出款完成
	//4：客服审核通过，风控审核拒绝，派发状态为空
	//5：客服审核通过，风控审核通过，派发状态为空
	//6：客服审核通过，风控审核通过，派发状态为待出款
	//7：客服审核通过，风控审核通过，派发状态为出款完成
	for _, v := range *data {
		stateInt, _ := v["State"].(int64)
		// 客服待审核
		if stateInt == 1 {
			v["KefuState"] = 1
			v["FengkongState"] = 0
			v["PaifaState"] = 0

			// 客服审核拒绝
		} else if stateInt == 2 {
			v["KefuState"] = 2
			v["FengkongState"] = 0
			v["PaifaState"] = 0

			// 客服审核通过
		} else if stateInt == 3 {
			v["KefuState"] = 3
			v["FengkongState"] = 1
			v["PaifaState"] = 0

			// 风控审核拒绝
		} else if stateInt == 4 {
			v["KefuState"] = 3
			v["FengkongState"] = 2
			v["PaifaState"] = 0

			// 风控审核通过
		} else if stateInt == 5 {
			v["KefuState"] = 3
			v["FengkongState"] = 3
			v["PaifaState"] = 0

			// 待出款
		} else if stateInt == 6 {
			v["KefuState"] = 3
			v["FengkongState"] = 3
			v["PaifaState"] = 1

			// 出款完成
		} else if stateInt == 7 {
			v["KefuState"] = 3
			v["FengkongState"] = 3
			v["PaifaState"] = 2
		}

		var betAmountTransferStr, betCountTransferStr, rewardAmountTransferStr string
		var ok bool
		// 转账下注金额
		betAmountTransferStr, ok = v["BetAmountTransfer"].(string)
		if !ok || betAmountTransferStr == "" {
			betAmountTransferStr = "0,0"
		}
		betAmountTransferArray := strings.Split(betAmountTransferStr, ",")
		betAmountTransferTrx, _ := strconv.ParseFloat(betAmountTransferArray[0], 64)
		betAmountTransferUsdt, _ := strconv.ParseFloat(betAmountTransferArray[1], 64)
		v["BetAmountTransferTrx"] = betAmountTransferTrx
		v["BetAmountTransferUsdt"] = betAmountTransferUsdt

		// 转账下注次数
		betCountTransferStr, ok = v["BetCountTransfer"].(string)
		if !ok || betCountTransferStr == "" {
			betCountTransferStr = "0,0"
		}
		betCountTransferArray := strings.Split(betCountTransferStr, ",")
		betCountTransferTrx, _ := strconv.ParseFloat(betCountTransferArray[0], 64)
		betCountTransferUsdt, _ := strconv.ParseFloat(betCountTransferArray[1], 64)
		v["BetCountTransferTrx"] = betCountTransferTrx
		v["BetCountTransferUsdt"] = betCountTransferUsdt

		// 转账返奖金额
		rewardAmountTransferStr, ok = v["RewardAmountTransfer"].(string)
		if !ok || rewardAmountTransferStr == "" {
			rewardAmountTransferStr = "0,0"
		}
		rewardAmountTransferArray := strings.Split(rewardAmountTransferStr, ",")
		rewardAmountTransferTrx, _ := strconv.ParseFloat(rewardAmountTransferArray[0], 64)
		rewardAmountTransferUsdt, _ := strconv.ParseFloat(rewardAmountTransferArray[1], 64)
		v["RewardAmountTransferTrx"] = rewardAmountTransferTrx
		v["RewardAmountTransferUsdt"] = rewardAmountTransferUsdt

		// 转账盈亏金额
		transferProfitTrx := rewardAmountTransferTrx - betAmountTransferTrx
		v["TransferProfitTrx"] = transferProfitTrx
		transferProfitUsdt := rewardAmountTransferUsdt - betAmountTransferUsdt
		v["TransferProfitUsdt"] = transferProfitUsdt

		// 余额下注金额
		betAmountYue, _ := v["BetAmountYue"].(float64)
		// 余额返奖金额
		rewardAmountYue, _ := v["RewardAmountYue"].(float64)
		// 余额盈亏金额
		rewardProfit := rewardAmountYue - betAmountYue
		v["OverageProfit"] = rewardProfit

		// 三方下注金额
		betAmountThirdFloat := 0.0
		if v["BetAmountThird"] != nil {
			betAmountThirdFloat, _ = v["BetAmountThird"].(float64)
		}
		// 三方返奖金额
		rewardAmountThirdFloat := 0.0
		if v["RewardAmountThird"] != nil {
			rewardAmountThirdFloat, _ = v["RewardAmountThird"].(float64)
		}
		// 三方盈亏金额
		thirdProfit := rewardAmountThirdFloat - betAmountThirdFloat
		v["ThirdProfit"] = thirdProfit

		// 运营商
		sellerName, ok := v["SellerName"].(string)
		if !ok {
			// 通过v["SellerId"]获取运营商名称
			sellerId, ok := v["SellerId"].(int64)
			if !ok {
				sellerName = ""
			} else {
				sellerNameMap, err := InitSellerNameMap()
				if err != nil {
					sellerName = ""
				} else {
					sellerName = SellerIDName(sellerNameMap, int(sellerId))
				}
				sellerName = SellerIDName(sellerNameMap, int(sellerId))
			}
		}
		v["SellerName"] = sellerName
		// 渠道
		channelName, ok := v["ChannelName"].(string)
		if !ok {
			channelId, ok := v["ChannelId"].(int64)
			if !ok {
				channelName = ""
			} else {
				channelName = ChannelName(int(channelId))
			}
		}
		v["ChannelName"] = channelName
	}
}

func (c *KeFuController) tiyanjing_create(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserIds          []int32
		Symbol           string
		Amount           decimal.Decimal
		Memo             string
		TiyanjinActiveId int32 // 体验金活动id
		RewardIndex      int   // 体验金奖励序号
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "客服系统", "体验金审核", "增", "体验金补单")
	if token == nil {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	if len(reqdata.UserIds) == 0 {
		ctx.RespErrString(true, &errcode, "请选择用户")
		return
	}
	if reqdata.Symbol == "" {
		ctx.RespErrString(true, &errcode, "请选择币种")
		return
	}
	if reqdata.Amount == decimal.Zero {
		ctx.RespErrString(true, &errcode, "请输入金额")
		return
	}
	if reqdata.Memo == "" {
		ctx.RespErrString(true, &errcode, "请输入备注")
		return
	}

	DaoU := server.DaoxHashGame().XUser
	DbU := DaoU.WithContext(ctx.Gin())
	daoT := server.DaoxHashGame().XTiyanjing
	dbT := daoT.WithContext(ctx.Gin())

	userList, err := DbU.Where(DaoU.UserID.In(reqdata.UserIds...)).Find()
	if ctx.RespErr(err, &errcode) {
		return
	}

	reqdata.Symbol = strings.ToLower(reqdata.Symbol)
	// if reqdata.Symbol == "trx" {
	// 	for _, user := range userList {
	// 		if user.Address == "" || user.Address == strconv.Itoa(int(user.UserID)) {
	// 			ctx.RespErrString(true, &errcode, fmt.Sprintf("用户%d未激活钱包，请重新选择。", user.UserID))
	// 			return
	// 		}
	// 	}
	// }

	tiyanjingList := make([]*model.XTiyanjing, 0)
	for _, user := range userList {
		RegisterIPCount, _ := DaoU.WithContext(ctx.Gin()).Where(DaoU.RegisterIP.Eq(user.RegisterIP)).Count()
		LoginIPCount, _ := DaoU.WithContext(ctx.Gin()).Where(DaoU.LoginIP.Eq(user.LoginIP)).Count()
		PwdCount, _ := DaoU.WithContext(ctx.Gin()).Where(DaoU.Password.Eq(user.Password)).Count()

		Games := make([]string, 0)
		daoOrder := server.DaoxHashGame().XOrder
		_, err := daoOrder.WithContext(ctx.Gin()).Where(daoOrder.UserID.Eq(user.UserID)).Where(daoOrder.GameID.Lt(100)).First()
		if err == nil {
			Games = append(Games, "哈希竞猜")
		}
		_, err = daoOrder.WithContext(ctx.Gin()).Where(daoOrder.UserID.Eq(user.UserID)).
			Where(daoOrder.GameID.Gt(100)).Where(daoOrder.GameID.Lt(200)).First()
		if err == nil {
			Games = append(Games, "一分哈希")
		}
		_, err = daoOrder.WithContext(ctx.Gin()).Where(daoOrder.UserID.Eq(user.UserID)).Where(daoOrder.GameID.Gt(200)).First()
		if err == nil {
			Games = append(Games, "余额哈希")
		}
		daoQipai := server.DaoxHashGame().XThirdQipai
		_, err = daoQipai.WithContext(ctx.Gin()).Where(daoQipai.UserID.Eq(user.UserID)).First()
		if err == nil {
			Games = append(Games, "棋牌游戏")
		}
		daoDianzhi := server.DaoxHashGame().XThirdDianzhi
		_, err = daoDianzhi.WithContext(ctx.Gin()).Where(daoDianzhi.UserID.Eq(user.UserID)).First()
		if err == nil {
			Games = append(Games, "电子游戏")
		}
		daoLottery := server.DaoxHashGame().XThirdLottery
		_, err = daoLottery.WithContext(ctx.Gin()).Where(daoLottery.UserID.Eq(user.UserID)).First()
		if err == nil {
			Games = append(Games, "哈希彩票")
		}
		daoQuwei := server.DaoxHashGame().XThirdQuwei
		_, err = daoQuwei.WithContext(ctx.Gin()).Where(daoQuwei.UserID.Eq(user.UserID)).First()
		if err == nil {
			Games = append(Games, "哈希电子")
		}
		daoLive := server.DaoxHashGame().XThirdLive
		_, err = daoLive.WithContext(ctx.Gin()).Where(daoLive.UserID.Eq(user.UserID)).First()
		if err == nil {
			Games = append(Games, "真人视讯")
		}
		daoSport := server.DaoxHashGame().XThirdSport
		_, err = daoSport.WithContext(ctx.Gin()).Where(daoSport.UserID.Eq(user.UserID)).First()
		if err == nil {
			Games = append(Games, "体育竞技")
		}
		daoTexas := server.DaoxHashGame().XThirdTexa
		_, err = daoTexas.WithContext(ctx.Gin()).Where(daoTexas.UserID.Eq(user.UserID)).First()
		if err == nil {
			Games = append(Games, "德州游戏")
		}
		GamesStr, _ := json.Marshal(Games)

		trxCountTransfer := struct {
			BetCountTransferTrx     int32
			BetAmountTransferTrx    float64
			RewardAmountTransferTrx float64
		}{}
		_ = daoOrder.WithContext(ctx.Gin()).Where(daoOrder.UserID.Eq(user.UserID)).
			Where(daoOrder.Symbol.Eq("trx")).Where(daoOrder.GameID.Lt(100)).
			Select(daoOrder.ID.Count().As("BetCountTransferTrx"),
				daoOrder.Amount.Sum().As("BetAmountTransferTrx"),
				daoOrder.RewardAmount.Sum().As("RewardAmountTransferTrx")).
			Scan(&trxCountTransfer)

		usdtCountTransfer := struct {
			BetCountTransferUsdt     int32
			BetAmountTransferUsdt    float64
			RewardAmountTransferUsdt float64
		}{}
		_ = daoOrder.WithContext(ctx.Gin()).Where(daoOrder.UserID.Eq(user.UserID)).
			Where(daoOrder.Symbol.Eq("usdt")).Where(daoOrder.GameID.Lt(100)).
			Select(daoOrder.ID.Count().As("BetCountTransferUsdt"),
				daoOrder.Amount.Sum().As("BetAmountTransferUsdt"),
				daoOrder.RewardAmount.Sum().As("RewardAmountTransferUsdt")).
			Scan(&usdtCountTransfer)

		yueCountTransfer := struct {
			BetCountTransferYue     int32
			BetAmountTransferYue    float64
			RewardAmountTransferYue float64
		}{}
		_ = daoOrder.WithContext(ctx.Gin()).Where(daoOrder.UserID.Eq(user.UserID)).Where(daoOrder.GameID.Gt(100)).
			Select(daoOrder.ID.Count().As("BetCountTransferYue"),
				daoOrder.Amount.Sum().As("BetAmountTransferYue"),
				daoOrder.RewardAmount.Sum().As("RewardAmountTransferYue")).
			Scan(&yueCountTransfer)

		BetCountTransfer := fmt.Sprintf("%d,%d", trxCountTransfer.BetCountTransferTrx, usdtCountTransfer.BetCountTransferUsdt)
		BetAmountTransfer := fmt.Sprintf("%f,%f", trxCountTransfer.BetAmountTransferTrx, usdtCountTransfer.BetAmountTransferUsdt)
		RewardAmountTransfer := fmt.Sprintf("%f,%f", trxCountTransfer.RewardAmountTransferTrx, usdtCountTransfer.RewardAmountTransferUsdt)

		LotteryCount := struct {
			BetCountLottery     int32
			BetAmountLottery    float64
			RewardAmountLottery float64
		}{}
		_ = daoLottery.WithContext(ctx.Gin()).Where(daoLottery.UserID.Eq(user.UserID)).
			Select(daoLottery.ID.Count().As("BetCountLottery"),
				daoLottery.BetAmount.Sum().As("BetAmountLottery"),
				daoLottery.WinAmount.Sum().As("RewardAmountLottery")).
			Scan(&LotteryCount)

		QipaiCount := struct {
			BetCountQipai     int32
			BetAmountQipai    float64
			RewardAmountQipai float64
		}{}
		_ = daoQipai.WithContext(ctx.Gin()).Where(daoQipai.UserID.Eq(user.UserID)).
			Select(daoQipai.ID.Count().As("BetCountQipai"),
				daoQipai.BetAmount.Sum().As("BetAmountQipai"),
				daoQipai.WinAmount.Sum().As("RewardAmountQipai")).
			Scan(&QipaiCount)

		DianzhiCount := struct {
			BetCountDianzhi     int32
			BetAmountDianzhi    float64
			RewardAmountDianzhi float64
		}{}
		_ = daoDianzhi.WithContext(ctx.Gin()).Where(daoDianzhi.UserID.Eq(user.UserID)).
			Select(daoDianzhi.ID.Count().As("BetCountDianzhi"),
				daoDianzhi.BetAmount.Sum().As("BetAmountDianzhi"),
				daoDianzhi.WinAmount.Sum().As("RewardAmountDianzhi")).
			Scan(&DianzhiCount)

		QuweiCount := struct {
			BetCountQuwei     int32
			BetAmountQuwei    float64
			RewardAmountQuwei float64
		}{}
		_ = daoQuwei.WithContext(ctx.Gin()).Where(daoQuwei.UserID.Eq(user.UserID)).
			Select(daoQuwei.ID.Count().As("BetCountQuwei"),
				daoQuwei.BetAmount.Sum().As("BetAmountQuwei"),
				daoQuwei.WinAmount.Sum().As("RewardAmountQuwei")).
			Scan(&QuweiCount)

		LiveCount := struct {
			BetCountLive     int32
			BetAmountLive    float64
			RewardAmountLive float64
		}{}
		_ = daoLive.WithContext(ctx.Gin()).Where(daoLive.UserID.Eq(user.UserID)).
			Select(daoLive.ID.Count().As("BetCountLive"),
				daoLive.BetAmount.Sum().As("BetAmountLive"),
				daoLive.WinAmount.Sum().As("RewardAmountLive")).
			Scan(&LiveCount)

		SportCount := struct {
			BetCountSport     int32
			BetAmountSport    float64
			RewardAmountSport float64
		}{}
		_ = daoSport.WithContext(ctx.Gin()).Where(daoSport.UserID.Eq(user.UserID)).
			Select(daoSport.ID.Count().As("BetCountSport"),
				daoSport.BetAmount.Sum().As("BetAmountSport"),
				daoSport.WinAmount.Sum().As("RewardAmountSport")).
			Scan(&SportCount)

		TexasCount := struct {
			BetCountTexas     int32
			BetAmountTexas    float64
			RewardAmountTexas float64
		}{}
		_ = daoTexas.WithContext(ctx.Gin()).Where(daoTexas.UserID.Eq(user.UserID)).
			Select(daoTexas.ID.Count().As("BetCountTexas"),
				daoTexas.BetAmount.Sum().As("BetAmountTexas"),
				daoTexas.WinAmount.Sum().As("RewardAmountTexas")).
			Scan(&TexasCount)

		BetCountThird := LotteryCount.BetCountLottery + QipaiCount.BetCountQipai + QuweiCount.BetCountQuwei + LiveCount.BetCountLive + SportCount.BetCountSport + TexasCount.BetCountTexas
		BetAmountThird := LotteryCount.BetAmountLottery + QipaiCount.BetAmountQipai + QuweiCount.BetAmountQuwei + LiveCount.BetAmountLive + SportCount.BetAmountSport + TexasCount.BetAmountTexas
		RewardAmountThird := LotteryCount.RewardAmountLottery + QipaiCount.RewardAmountQipai + QuweiCount.RewardAmountQuwei + LiveCount.RewardAmountLive + SportCount.RewardAmountSport + TexasCount.RewardAmountTexas

		tb := &model.XTiyanjing{
			SellerID:             user.SellerID,
			ChannelID:            user.ChannelID,
			UserID:               user.UserID,
			State:                1,
			Symbol:               reqdata.Symbol,
			Amount:               reqdata.Amount.InexactFloat64(),
			Memo:                 reqdata.Memo,
			Account:              user.Account,
			TgName:               user.TgName,
			RegisterTime:         user.RegisterTime,
			LoginTime:            user.LoginTime,
			CSGroup:              user.CSGroup,
			CSID:                 user.CSID,
			IPCount:              int32(RegisterIPCount),
			LoginIPCount:         int32(LoginIPCount),
			PwdCount:             int32(PwdCount),
			Address:              user.Address,
			ApplyType:            2,
			OperUserAccount:      token.Account,
			OperUserID:           int32(token.UserId),
			Games:                string(GamesStr),
			BetCountTransfer:     BetCountTransfer,
			BetAmountTransfer:    BetAmountTransfer,
			RewardAmountTransfer: RewardAmountTransfer,
			BetCountYue:          yueCountTransfer.BetCountTransferYue,
			BetAmountYue:         yueCountTransfer.BetAmountTransferYue,
			RewardAmountYue:      yueCountTransfer.RewardAmountTransferYue,
			BetCountThird:        BetCountThird,
			BetAmountThird:       BetAmountThird,
			RewardAmountThird:    RewardAmountThird,
		}
		tb.BetTime = time.Date(2000, 1, 1, 0, 0, 0, 0, time.FixedZone("CST", 8*3600))
		if !user.LastBetTime.IsZero() {
			tb.BetTime = user.LastBetTime
		}
		tiyanjingList = append(tiyanjingList, tb)
	}
	err = dbT.Select(
		daoT.SellerID, daoT.ChannelID, daoT.UserID, daoT.State, daoT.Symbol,
		daoT.Amount, daoT.Memo, daoT.Account, daoT.TgName, daoT.RegisterTime,
		daoT.LoginTime, daoT.CSGroup, daoT.CSID, daoT.IPCount, daoT.LoginIPCount,
		daoT.PwdCount, daoT.ApplyType, daoT.Address, daoT.OperUserAccount, daoT.OperUserID,
		daoT.Games, daoT.BetCountTransfer, daoT.BetAmountTransfer, daoT.RewardAmountTransfer,
		daoT.BetCountYue, daoT.BetAmountYue, daoT.RewardAmountYue,
		daoT.BetCountThird, daoT.BetAmountThird, daoT.RewardAmountThird, daoT.BetTime,
	).CreateInBatches(tiyanjingList, 100)
	if e, ok := err.(*mysql.MySQLError); ok && e.Number == 1062 {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("用户已申请过%s体验金: %s", reqdata.Symbol, e.Message))
		return
	}
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *KeFuController) tiyanjing_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId     int32
		Page         int
		PageSize     int
		StartTime    int64
		EndTime      int64
		UserId       int32
		Address      string
		CSGroup      string
		CSId         string
		Symbol       string
		AuditAccount string
		State        int32
		AccountType  int32 // 用户账号类型
		Export       int
	}
	type Result struct {
		model.XTiyanjing
		AccountType         int32
		RealAmount          float64
		FirstWithdrawAmount float64
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "体验金审核", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && int(reqdata.SellerId) != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 100000
	}
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	xTyj := server.DaoxHashGame().XTiyanjing
	xUser := server.DaoxHashGame().XUser
	xRecharge := server.DaoxHashGame().XRecharge
	xWithdraw := server.DaoxHashGame().XWithdraw
	db := xTyj.WithContext(ctx.Gin()).
		LeftJoin(xUser, xTyj.UserID.EqCol(xUser.UserID)).
		LeftJoin(xRecharge, xTyj.UserID.EqCol(xRecharge.UserID), xRecharge.IsFirst.Eq(1)).
		LeftJoin(xWithdraw, xTyj.UserID.EqCol(xWithdraw.UserID), xWithdraw.IsFirst.Eq(1))
	where := func(db gen.Dao) gen.Dao {
		if reqdata.SellerId != 0 {
			db = db.Where(xTyj.SellerID.Eq(reqdata.SellerId))
		}
		if reqdata.StartTime > 0 && reqdata.EndTime > 0 {
			startTime := time.UnixMilli(reqdata.StartTime)
			endTime := time.UnixMilli(reqdata.EndTime)
			db = db.Where(xTyj.CreateTime.Gte(startTime)).Where(xTyj.CreateTime.Lte(endTime))
		}
		if reqdata.UserId > 0 {
			db = db.Where(xTyj.UserID.Eq(reqdata.UserId))
		}
		if reqdata.Address != "" {
			db = db.Where(xTyj.Address.Eq(reqdata.Address))
		}
		if reqdata.CSGroup != "" {
			db = db.Where(xTyj.CSGroup.Eq(reqdata.CSGroup))
		}
		if reqdata.CSId != "" {
			db = db.Where(xTyj.CSID.Eq(reqdata.CSId))
		}
		if reqdata.Symbol != "" {
			db = db.Where(xTyj.Symbol.Eq(reqdata.Symbol))
		}
		if reqdata.AuditAccount != "" {
			db = db.Where(xTyj.AuditAccount.Eq(reqdata.AuditAccount))
		}
		if reqdata.AccountType > 0 {
			db = db.Where(xUser.AccountType.Eq(reqdata.AccountType))
		}
		if reqdata.State == 5 {
			db = db.Where(xTyj.State.Gt(reqdata.State))
		} else if reqdata.State > 0 {
			db = db.Where(xTyj.State.Eq(reqdata.State))
		}
		return db
	}
	conds := []field.Expr{
		xTyj.ALL, xUser.AccountType, xRecharge.RealAmount, xWithdraw.RealAmount.As("FirstWithdrawAmount"),
	}

	list := make([]Result, 0)
	if reqdata.Export != 1 {
		var total int64
		db = db.Scopes(where)
		total, err = db.Select(conds...).Order(xTyj.ID.Desc()).ScanByPage(&list, offset, limit)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("total", total)
		ctx.Put("data", list)

		amount := struct {
			Amount float64
		}{}
		err = db.Select(xTyj.Amount.Sum().As("Amount")).Scan(&amount)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("amount", amount.Amount)
	} else {
		err = db.Scopes(where).Select(conds...).Order(xTyj.ID.Desc()).Scan(&list)
		if ctx.RespErr(err, &errcode) {
			return
		}

		xdata := &xgo.XMaps{}
		xdata.RawData = []xgo.XMap{}
		data := make([]map[string]interface{}, 0)
		for _, v := range list {
			b, _ := json.Marshal(v)
			m := make(map[string]interface{})
			_ = json.Unmarshal(b, &m)
			data = append(data, m)
			xdata.RawData = append(xdata.RawData, xgo.XMap{RawData: m})
		}
		// 添加报表新字段
		tiyanjinNewWord(&data)
		filename := "export_tiyanjin_" + time.Now().Format("20060102150405")
		xgo.Export(server.ExportDir()+"/"+filename, xdata, TIYANJIN_OPTIONS)
		ctx.Put("filename", "/exports/"+filename+".xlsx")
	}
	ctx.RespOK()
}

func (c *KeFuController) tiyanjing_update(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int
		Memo       string
		KeFuTgName string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "体验金审核", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.Db().Query("update x_tiyanjing set Memo = ? ,KeFuTgName = ? where Id = ?", []interface{}{reqdata.Memo, reqdata.KeFuTgName, reqdata.Id})
	ctx.RespOK()
	server.WriteAdminLog("修改体验金", ctx, reqdata)
}

func (c *KeFuController) tiyanjing_audit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int
		State      int
		Memo       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "体验金审核", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.State != 2 && reqdata.State != 3 {
		ctx.RespErrString(true, &errcode, "审核失败,请求状态不正确")
		return
	}
	where1 := abugo.AbuDbWhere{}
	where1.Add("and", "Account", "=", token.Account, nil)
	udata, _ := server.Db().Table("admin_user").Where(where1).GetOne()

	csgroup := abugo.GetStringFromInterface((*udata)["CSGroup"])
	csid := abugo.GetStringFromInterface((*udata)["CSId"])
	if csgroup == "" || csid == "" {
		ctx.RespErrString(true, &errcode, "你不是客服,无权审核")
		return
	}

	where2 := abugo.AbuDbWhere{}
	where2.Add("and", "Id", "=", reqdata.Id, nil)
	tdata, _ := server.Db().Table("x_tiyanjing").Where(where2).GetOne()
	if tdata == nil {
		ctx.RespErrString(true, &errcode, "订单不存在")
		return
	}
	tcsgroup := abugo.GetStringFromInterface((*tdata)["CSGroup"])
	if tcsgroup == "" {
		ctx.RespErrString(true, &errcode, "订单不正确")
		return
	}
	if tcsgroup != csgroup {
		ctx.RespErrString(true, &errcode, "该订单不属于你的团队,无权审核")
		return
	}
	state := abugo.GetInt64FromInterface((*tdata)["State"])
	if state != 1 {
		ctx.RespErrString(true, &errcode, "审核失败,订单状态不正确")
		return
	}
	UserId := abugo.GetInt64FromInterface((*tdata)["UserId"])
	sql := "update x_tiyanjing set Memo = ? ,State = ?,CSId = ?,AuditAccount = ? ,AuditTime = now() where Id = ? and State = 1"
	r, _ := server.Db().Conn().Exec(sql, reqdata.Memo, reqdata.State, csid, token.Account, reqdata.Id)
	if r != nil {
		affected, _ := r.RowsAffected()
		if affected > 0 {
			server.Db().Conn().Exec("update x_user set CSId = ?,CSBindTime = now() where UserId = ? and CSId IS NULL", csid, int(UserId))
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("客服审核体验金", ctx, reqdata)
}

func (c *KeFuController) tiyanjing_listex(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId     int
		Page         int
		PageSize     int
		StartTime    int64
		EndTime      int64
		UserId       int
		Address      string
		CSGroup      string
		CSId         string
		Symbol       string
		AuditAccount string
		State        int
		Export       int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "体验金发放", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 100000
	}
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "Address", "=", reqdata.Address, "")
		where.Add("and", "CSGroup", "=", reqdata.CSGroup, "")
		where.Add("and", "CSId", "=", reqdata.CSId, "")
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "AuditAccount", "=", reqdata.AuditAccount, "")
		if reqdata.State == 5 {
			where.Add("and", "State", ">", reqdata.State, 0)
		} else {
			where.Add("and", "State", "=", reqdata.State, 0)
		}
		where.Add("and", "State", ">", 2, nil)
		total, data := server.Db().Table("x_tiyanjing").OrderBy("id desc").Where(where).PageData(reqdata.Page, reqdata.PageSize)
		//for i := 0; i < len(*data); i++ {
		//	delete((*data)[i], "TgName")
		//}

		// 获取用户ID列表
		var userIds []string
		for _, item := range *data {
			if userId, ok := item["UserId"]; ok && userId != nil {
				userIdStr := fmt.Sprintf("%v", userId)
				if userIdStr != "" && userIdStr != "0" {
					userIds = append(userIds, userIdStr)
				}
			}
		}

		// 查询顶级代理ID
		topAgentMap := make(map[int32]int32)
		if len(userIds) > 0 {
			// 构建IN查询
			query := fmt.Sprintf("UserId IN (%s)", strings.Join(userIds, ","))
			type UserTopAgent struct {
				UserId     int32
				TopAgentId int32
			}
			var userTopAgents []UserTopAgent
			err := server.Db().GormDao().Table("x_user").
				Where(query).
				Select("UserId, TopAgentId").
				Scan(&userTopAgents).Error
			if err == nil {
				for _, user := range userTopAgents {
					topAgentMap[user.UserId] = user.TopAgentId
				}
			}
		}

		// 添加顶级代理ID到结果中
		for i := range *data {
			userIdStr := fmt.Sprintf("%v", (*data)[i]["UserId"])
			userIdInt64, err := strconv.ParseInt(userIdStr, 10, 32)
			if err != nil {
				(*data)[i]["TopAgentId"] = int32(0)
			} else {
				userIdInt32 := int32(userIdInt64)
				// 检查map中是否存在该userId
				topAgentId, exists := topAgentMap[userIdInt32]
				if exists {
					(*data)[i]["TopAgentId"] = topAgentId
				} else {
					// 用户表中不存在该userId，设置默认值
					(*data)[i]["TopAgentId"] = int32(0)
				}
			}
		}

		if reqdata.Export != 1 {
			ctx.Put("total", total)
			ctx.Put("data", data)
		} else {
			options := TIYANJIN_OPTIONS
			xdata := &xgo.XMaps{}
			xdata.RawData = []xgo.XMap{}
			for _, v := range *data {
				xdata.RawData = append(xdata.RawData, xgo.XMap{RawData: v})
			}

			// 添加报表新字段
			tiyanjinNewWord(data)

			filename := "export_tiyanjin_" + time.Now().Format("20060102150405")
			xgo.Export(server.ExportDir()+"/"+filename, xdata, options)
			ctx.Put("filename", "/exports/"+filename+".xlsx")
		}
	}
	if reqdata.Export != 1 {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "Address", "=", reqdata.Address, "")
		where.Add("and", "CSGroup", "=", reqdata.CSGroup, "")
		where.Add("and", "CSId", "=", reqdata.CSId, "")
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "AuditAccount", "=", reqdata.AuditAccount, "")
		if reqdata.State == 5 {
			where.Add("and", "State", ">", reqdata.State, 0)
		} else {
			where.Add("and", "State", "=", reqdata.State, 0)
		}
		where.Add("and", "State", ">", 2, nil)
		amount, _ := server.Db().Table("x_tiyanjing").Select("sum(Amount) as Amount").Where(where).GetOne()
		ctx.Put("amount", (*amount)["Amount"])
	}
	ctx.RespOK()
}

func (c *KeFuController) tiyanjing_auditex(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int
		State      int
		Memo       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	if server.XRedis().GetLock("tiyanjing_auditex", 10) == false {
		ctx.RespErrString(true, &errcode, "系统忙,请稍后再试")
		return
	}
	defer server.XRedis().ReleaseLock("tiyanjing_auditex")

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "体验金发放", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.State != 4 && reqdata.State != 5 {
		ctx.RespErrString(true, &errcode, "审核失败,请求状态不正确")
		return
	}
	where2 := abugo.AbuDbWhere{}
	where2.Add("and", "Id", "=", reqdata.Id, nil)
	tdata, _ := server.Db().Table("x_tiyanjing").Where(where2).GetOne()
	if tdata == nil {
		ctx.RespErrString(true, &errcode, "订单不存在")
		return
	}

	state := abugo.GetInt64FromInterface((*tdata)["State"])
	if state != 3 {
		ctx.RespErrString(true, &errcode, "审核失败,订单状态不正确1")
		return
	}

	tyjex, err := server.XDb().Table("x_tiyanjinex").Where("Id = ?", reqdata.Id).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if tyjex != nil {
		ctx.RespErrString(true, &errcode, "审核失败,订单状态不正确2")
		return
	}
	_, err = server.XDb().Table("x_tiyanjinex").Insert(xgo.H{
		"Id":    reqdata.Id,
		"State": reqdata.State,
	})
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	sql := "update x_tiyanjing set Memo = ? ,State = ?,SendAccount = ? ,SendTime = now() where Id = ? and State = 3"
	server.Db().Query(sql, []interface{}{reqdata.Memo, reqdata.State, token.Account, reqdata.Id})
	server.WriteAdminLog("风控审核体验金", ctx, reqdata)

	if reqdata.State == 5 {
		sellerId := abugo.GetInt64FromInterface((*tdata)["SellerId"])
		userId := abugo.GetInt64FromInterface((*tdata)["UserId"])
		kefuAccount := abugo.GetStringFromInterface((*tdata)["CSId"])
		amount := abugo.GetFloat64FromInterface((*tdata)["Amount"])
		symbol := abugo.GetStringFromInterface((*tdata)["Symbol"])
		body := struct {
			SellerId    int64           `validate:"required"`
			Symbol      string          `validate:"required"`
			UserId      int64           `validate:"required"`
			KefuAccount string          `validate:"required"`
			Amount      decimal.Decimal `validate:"required"`
		}{
			SellerId:    sellerId,
			Symbol:      symbol,
			UserId:      userId,
			KefuAccount: kefuAccount,
			Amount:      decimal.NewFromFloat(amount),
		}
		// 语音播报
		baseurl := "/api/TyjBroadcast/broadcast"
		url := fmt.Sprintf("http://127.0.0.1:%d%s", server.Http().Port(), baseurl)
		resp, err := req.Post(url, req.BodyJSON(body))
		if err != nil {
			logs.Error("语音播报 err:", err)
		} else {
			logs.Debug("语音播报 resp:", resp.String())
		}
		if !server.Readonly() {
			url2 := fmt.Sprintf("%s%s", viper.GetString("adminapi_readonly"), baseurl)
			_, err = req.Post(url2, req.BodyJSON(body))
			if err != nil {
				logs.Error("语音播报 err:", err)
			}
		}

		// tg群组汇报
		go tgSendHuibao(int32(userId), 2, time.Now(), amount, symbol)
	}
	ctx.RespOK()
}

// 通过tg机器人发送群组汇报消息
func tgSendHuibao(UserId, Type int32, Time time.Time, Amount float64, Symbol string) {
	sendReq := struct {
		UserId int32 `validate:"required"`
		Type   int32 `validate:"required,gt=0"` // 1:充值 2:领体验金
		Time   string
		Amount decimal.Decimal
		Symbol string
	}{
		UserId: UserId,
		Type:   Type,
		Time:   Time.Format(time.DateTime),
		Amount: decimal.NewFromFloat(Amount),
		Symbol: Symbol,
	}
	tgservice := viper.GetString("tgservice")
	_, err := req.Post(tgservice+"/api/sendHuibao", req.BodyJSON(sendReq))
	if err != nil {
		logs.Error("tg群组汇报发送失败：", err)
	}
	return
}

func (c *KeFuController) binded_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page              int
		PageSize          int
		SellerId          int
		FirstBetStartTime int64
		FirstBetEndTime   int64
		QueryStartTime    int64
		QueryEndTime      int64
		ChannelId         int
		UserId            int
		Account           string
		Address           string
		Agent             int
		CSGroup           string
		CSId              string
		LastLoginTime     int
		LastBetTime       int
		LastRechargeTime  int
		FirstRechargeTime int
		Tag               string
		TgName            string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.FirstBetEndTime > 0 {
		reqdata.FirstBetEndTime += 1000
	}
	if reqdata.QueryEndTime > 0 {
		reqdata.QueryEndTime += 1000
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服绑定列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if !server.Auth2(token, "客服系统", "客服绑定列表", "查看全部团队") {
		where := abugo.AbuDbWhere{}
		where.Add("and", "Account", "=", token.Account, nil)
		admin, _ := server.Db().Table("admin_user").Where(where).GetOne()
		reqdata.CSGroup, _ = (*admin)["CSGroup"].(string)
		if reqdata.CSGroup == "" {
			reqdata.CSGroup = "-885452"
		}
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "RegisterTime", ">=", abugo.TimeStampToLocalDate(reqdata.FirstBetStartTime), "")
	where.Add("and", "RegisterTime", "<", abugo.TimeStampToLocalDate(reqdata.FirstBetEndTime), "")
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "Account", "=", reqdata.Account, "")
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "AgentId", "=", reqdata.Agent, 0)
	where.Add("and", "CSGroup", "=", reqdata.CSGroup, "")
	where.Add("and", "CSId", "=", reqdata.CSId, "")
	where.Add("and", "Tag", "=", reqdata.Tag, "")
	where.Add("and", "TgName", "=", reqdata.TgName, "")

	if reqdata.LastLoginTime == 1 {
		where.Add("and", "LoginTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
		where.Add("and", "LoginTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
	}
	if reqdata.LastBetTime == 1 {
		where.Add("and", "LastBetTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
		where.Add("and", "LastBetTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
	}
	if reqdata.LastRechargeTime == 1 {
		where.Add("and", "LastRechargeTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
		where.Add("and", "LastRechargeTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
	}
	if reqdata.FirstRechargeTime == 1 {
		where.Add("and", "FirstRechargeTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
		where.Add("and", "FirstRechargeTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
	}
	total, data := server.Db().Table("x_user").Select(`Id,SellerId,ChannelId,UserId,Account,TgName,AgentId,Address,
	CSGroup,CSId,LoginTime,RegisterTime,RegisterIp,Memo,KeFuTgName,FirstBetTime,LastBetTime,FirstRechargeTime,LastRechargeTime,RegUrl,Memo,KeFuTgName,Tag,
	CaiJingTrx,CaiJingUsdt`).Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)

	for i := 0; i < len(*data); i++ {
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			rd, _ := server.Db().Table("x_recharge").Select("IFNULL(sum(RealAmount),0) AS RechargeAmount").Where(where1).GetOne()
			(*data)[i]["RechargeAmount"] = (*rd)["RechargeAmount"]
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "State", ">=", 2, nil)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			rd, _ := server.Db().Table("x_withdraw").Select("IFNULL(sum(Amount),0) AS WithdrawAmount").Where(where1).GetOne()
			(*data)[i]["WithdrawAmount"] = (*rd)["WithdrawAmount"]
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			where1.Add("and", "Symbol", "=", "trx", nil)
			sel := `count(id) AS BetCount,
			IFNULL(sum(Amount),0) AS BetAmount,
			IFNULL(sum(RewardAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_order").Select(sel).Where(where1).GetOne()
			(*data)[i]["TransferTrx"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			where1.Add("and", "Symbol", "=", "usdt", nil)
			where1.Add("and", "GameId", "<", 100, nil)
			sel := `count(id) AS BetCount,
			IFNULL(sum(Amount),0) AS BetAmount,
			IFNULL(sum(RewardAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_order").Select(sel).Where(where1).GetOne()
			(*data)[i]["TransferUsdt"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			where1.Add("and", "Symbol", "=", "usdt", nil)
			where1.Add("and", "GameId", ">", 100, nil)
			sel := `count(id) AS BetCount,
			IFNULL(sum(Amount),0) AS BetAmount,
			IFNULL(sum(RewardAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_order").Select(sel).Where(where1).GetOne()
			(*data)[i]["YueUsdt"] = *rd
		}
		BetCount := 0
		BetAmount := 0.0
		WinAmount := 0.0
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			sel := `count(id) AS BetCount,
			IFNULL(sum(BetAmount),0) AS BetAmount,
			IFNULL(sum(WinAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_third_lottery").Select(sel).Where(where1).GetOne()
			BetCount += int(abugo.GetFloat64FromInterface((*rd)["BetCount"]))
			BetAmount += abugo.GetFloat64FromInterface((*rd)["BetAmount"])
			WinAmount += abugo.GetFloat64FromInterface((*rd)["WinAmount"])
			(*data)[i]["Third_Lottery"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			sel := `count(id) AS BetCount,
			IFNULL(sum(BetAmount),0) AS BetAmount,
			IFNULL(sum(WinAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_third_dianzhi").Select(sel).Where(where1).GetOne()
			BetCount += int(abugo.GetFloat64FromInterface((*rd)["BetCount"]))
			BetAmount += abugo.GetFloat64FromInterface((*rd)["BetAmount"])
			WinAmount += abugo.GetFloat64FromInterface((*rd)["WinAmount"])
			(*data)[i]["Third_DianZhi"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			sel := `count(id) AS BetCount,
			IFNULL(sum(BetAmount),0) AS BetAmount,
			IFNULL(sum(WinAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_third_qipai").Select(sel).Where(where1).GetOne()
			BetCount += int(abugo.GetFloat64FromInterface((*rd)["BetCount"]))
			BetAmount += abugo.GetFloat64FromInterface((*rd)["BetAmount"])
			WinAmount += abugo.GetFloat64FromInterface((*rd)["WinAmount"])
			(*data)[i]["Third_Qipai"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			sel := `count(id) AS BetCount,
			IFNULL(sum(BetAmount),0) AS BetAmount,
			IFNULL(sum(WinAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_third_quwei").Select(sel).Where(where1).GetOne()
			BetCount += int(abugo.GetFloat64FromInterface((*rd)["BetCount"]))
			BetAmount += abugo.GetFloat64FromInterface((*rd)["BetAmount"])
			WinAmount += abugo.GetFloat64FromInterface((*rd)["WinAmount"])
			(*data)[i]["Third_Quwei"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			sel := `count(id) AS BetCount,
			IFNULL(sum(BetAmount),0) AS BetAmount,
			IFNULL(sum(WinAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_third_live").Select(sel).Where(where1).GetOne()
			BetCount += int(abugo.GetFloat64FromInterface((*rd)["BetCount"]))
			BetAmount += abugo.GetFloat64FromInterface((*rd)["BetAmount"])
			WinAmount += abugo.GetFloat64FromInterface((*rd)["WinAmount"])
			(*data)[i]["Third_Live"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			sel := `count(id) AS BetCount,
			IFNULL(sum(BetAmount),0) AS BetAmount,
			IFNULL(sum(WinAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_third_sport").Select(sel).Where(where1).GetOne()
			BetCount += int(abugo.GetFloat64FromInterface((*rd)["BetCount"]))
			BetAmount += abugo.GetFloat64FromInterface((*rd)["BetAmount"])
			WinAmount += abugo.GetFloat64FromInterface((*rd)["WinAmount"])
			(*data)[i]["Third_Sport"] = *rd
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "UserId", "=", (*data)[i]["UserId"], nil)
			where1.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalDate(reqdata.QueryStartTime), "")
			where1.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalDate(reqdata.QueryEndTime), "")
			sel := `count(id) AS BetCount,
			IFNULL(sum(BetAmount),0) AS BetAmount,
			IFNULL(sum(WinAmount),0) AS WinAmount`
			rd, _ := server.Db().Table("x_third_texas").Select(sel).Where(where1).GetOne()
			BetCount += int(abugo.GetFloat64FromInterface((*rd)["BetCount"]))
			BetAmount += abugo.GetFloat64FromInterface((*rd)["BetAmount"])
			WinAmount += abugo.GetFloat64FromInterface((*rd)["WinAmount"])
			(*data)[i]["Third_Texas"] = *rd
		}
		(*data)[i]["ThirdUsdt"] = gin.H{
			"BetCount":  BetCount,
			"BetAmount": BetAmount,
			"WinAmount": WinAmount,
		}
	}

	ctx.Put("total", total)
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *KeFuController) binded_update(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int
		Memo       string
		KeFuTgName string
		Tag        string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服绑定列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.Db().Conn().Exec("update x_user set Memo = ? ,KeFuTgName = ? ,Tag = ? where Userid = ?", reqdata.Memo, reqdata.KeFuTgName, reqdata.Tag, reqdata.UserId)
	bytes, _ := json.Marshal(&reqdata)
	server.Db().Conn().Exec("insert into x_user_kefu_log(UserId,ReqData,AdminAccount) values(?,?,?)", reqdata.UserId, string(bytes), token.Account)
	ctx.RespOK()
	server.WriteAdminLog("更新绑定列表", ctx, reqdata)
}

func (c *KeFuController) user_update_log(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服绑定列表", "查"), &errcode, "权限不足") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", reqdata.UserId, nil)
	data, _ := server.Db().Table("x_user_kefu_log").Where(where).OrderBy("id desc").GetList()
	ctx.RespOK(data)
}

func (c *KeFuController) kefu_yeji(ctx *abugo.AbuHttpContent) {
	type RequestItem struct {
		CSGroup string
		CSId    []string
	}
	type RequestData struct {
		SellerId  int
		StartTime int64
		EndTime   int64
		Groups    []RequestItem
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服业绩", "查"), &errcode, "权限不足") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 1000
	}
	StartTime := abugo.TimeStampToLocalDate(reqdata.StartTime)
	EndTime := abugo.TimeStampToLocalDate(reqdata.EndTime)
	returndata := []map[string]interface{}{}
	for i := 0; i < len(reqdata.Groups); i++ {
		item := reqdata.Groups[i]
		for j := 0; j < len(item.CSId); j++ {
			csgroup := item.CSGroup
			csid := item.CSId[j]
			data := gin.H{
				"CSGroup": csgroup,
				"CSId":    csid,
			}
			{ // 团队信息
				where := abugo.AbuDbWhere{}
				where.Add("and", "GroupName", "=", csgroup, "")
				d, _ := server.Db().Table("x_kefu_group").Select("SellerId").Where(where).GetOne()
				if d != nil {
					data["SellerId"] = abugo.GetFloat64FromInterface((*d)["SellerId"])
				}
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CSBindTime", ">=", StartTime, "")
				where.Add("and", "CSBindTime", "<", EndTime, "")
				d, _ := server.Db().Table("x_user").Select("count(id) as count,sum(Amount) as Amount").Where(where).GetOne()
				data["UserCount"] = (*d)["count"]
				data["UserAmount"] = abugo.GetFloat64FromInterface((*d)["Amount"])
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CreateTime", ">=", StartTime, "")
				where.Add("and", "CreateTime", "<", EndTime, "")
				where.Add("and", "Symbol", "=", "trx", nil)
				d, _ := server.Db().Table("x_caijing_detail").Select("sum(Amount) as Amount").Where(where).GetOne()
				data["CaiJingTrx"] = abugo.GetFloat64FromInterface((*d)["Amount"])
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CreateTime", ">=", StartTime, "")
				where.Add("and", "CreateTime", "<", EndTime, "")
				where.Add("and", "Symbol", "=", "usdt", nil)
				d, _ := server.Db().Table("x_caijing_detail").Select("sum(Amount) as Amount").Where(where).GetOne()
				data["CaiJingUsdt"] = abugo.GetFloat64FromInterface((*d)["Amount"])
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CreateTime", ">=", StartTime, "")
				where.Add("and", "CreateTime", "<", EndTime, "")
				d, _ := server.Db().Table("x_recharge").Select("count(DISTINCT UserId) as count,sum(RealAmount) as Amount").Where(where).GetOne()
				data["RechargeCount"] = (*d)["count"]
				data["RechargeAmount"] = abugo.GetFloat64FromInterface((*d)["Amount"])
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CreateTime", ">=", StartTime, "")
				where.Add("and", "CreateTime", "<", EndTime, "")
				where.Add("and", "State", ">=", 2, nil)
				d, _ := server.Db().Table("x_withdraw").Select("count(DISTINCT UserId) as count,sum(Amount) as Amount").Where(where).GetOne()
				data["WithdrawCount"] = (*d)["count"]
				data["WithdrawAmount"] = abugo.GetFloat64FromInterface((*d)["Amount"])
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CreateTime", ">=", StartTime, "")
				where.Add("and", "CreateTime", "<", EndTime, "")
				where.Add("and", "Symbol", "=", "trx", nil)
				d, _ := server.Db().Table("x_order").Select("count(id) as count, sum(Amount) as BetAmount, sum(RewardAmount) as WinAmount").Where(where).GetOne()
				data["TransferBetCountTrx"] = (*d)["count"]
				data["TransferBetAmountTrx"] = abugo.GetFloat64FromInterface((*d)["BetAmount"])
				data["TransferRewardAmountTrx"] = abugo.GetFloat64FromInterface((*d)["WinAmount"])
				d1, _ := server.Db().Table("x_order").Select("count(DISTINCT UserId) as count").Where(where).GetOne()
				data["TransferUserCountTrx"] = (*d1)["count"]
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CreateTime", ">=", StartTime, "")
				where.Add("and", "CreateTime", "<", EndTime, "")
				where.Add("and", "Symbol", "=", "usdt", nil)
				where.Add("and", "GameId", "<", 100, 0)
				d, _ := server.Db().Table("x_order").Select("count(id) as count, sum(Amount) as BetAmount, sum(RewardAmount) as WinAmount").Where(where).GetOne()
				data["TransferBetCountUsdt"] = (*d)["count"]
				data["TransferBetAmountUsdt"] = abugo.GetFloat64FromInterface((*d)["BetAmount"])
				data["TransferRewardAmountUsdt"] = abugo.GetFloat64FromInterface((*d)["WinAmount"])
				d1, _ := server.Db().Table("x_order").Select("count(DISTINCT UserId) as count").Where(where).GetOne()
				data["TransferUserCountUsdt"] = (*d1)["count"]
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CSGroup", "=", csgroup, "")
				where.Add("and", "CSId", "=", csid, "")
				where.Add("and", "CreateTime", ">=", StartTime, "")
				where.Add("and", "CreateTime", "<", EndTime, "")
				where.Add("and", "Symbol", "=", "usdt", nil)
				where.Add("and", "GameId", ">", 100, 0)
				d, _ := server.Db().Table("x_order").Select("count(id) as count, sum(Amount) as BetAmount, sum(RewardAmount) as WinAmount").Where(where).GetOne()
				data["YueBetCountUsdt"] = (*d)["count"]
				data["YueBetAmountUsdt"] = abugo.GetFloat64FromInterface((*d)["BetAmount"])
				data["YueRewardAmountUsdt"] = abugo.GetFloat64FromInterface((*d)["WinAmount"])
				d1, _ := server.Db().Table("x_order").Select("count(DISTINCT UserId) as count").Where(where).GetOne()
				data["YueUserCountUsdt"] = (*d1)["count"]
			}
			{
				ThirdBetCount := 0
				ThirdBetAmount := 0.0
				ThirdWinAmount := 0.0
				ThirdUserCount := 0
				thirdusers := map[int64]int{}
				tables := []string{"x_third_dianzhi", "x_third_live", "x_third_lottery", "x_third_qipai", "x_third_quwei", "x_third_sport", "x_third_texas"}
				for k := 0; k < len(tables); k++ {
					where := abugo.AbuDbWhere{}
					where.Add("and", "CSGroup", "=", csgroup, "")
					where.Add("and", "CSId", "=", csid, "")
					where.Add("and", "ThirdTime", ">=", StartTime, "")
					where.Add("and", "ThirdTime", "<", EndTime, "")
					users, _ := server.Db().Table(tables[k]).Select(" DISTINCT UserId").Where(where).GetList()
					for l := 0; l < len(*users); l++ {
						uid := abugo.GetInt64FromInterface((*users)[l]["UserId"])
						thirdusers[uid] = 1
					}
					d, _ := server.Db().Table(tables[k]).Select("count(id) as count ,sum(BetAmount) as BetAmount, sum(WinAmount) as WinAmount").Where(where).GetOne()
					ThirdBetCount += int(abugo.GetInt64FromInterface((*d)["count"]))
					ThirdBetAmount += abugo.GetFloat64FromInterface((*d)["BetAmount"])
					ThirdWinAmount += abugo.GetFloat64FromInterface((*d)["WinAmount"])
				}
				ThirdUserCount = len(thirdusers)
				data["ThirdBetCount"] = ThirdBetCount
				data["ThirdBetAmount"] = ThirdBetAmount
				data["ThirdRewardAmount"] = ThirdWinAmount
				data["ThirdUserCount"] = ThirdUserCount
			}
			returndata = append(returndata, data)
		}
	}
	ctx.RespOK(returndata)
}

func (c *KeFuController) caijing_detail(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		StartTime int64
		EndTime   int64
		CSGroup   string
		CSId      string
		Symbol    string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "客服业绩", "查"), &errcode, "权限不足") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 1000
	}
	StartTime := abugo.TimeStampToLocalDate(reqdata.StartTime)
	EndTime := abugo.TimeStampToLocalDate(reqdata.EndTime)
	where := abugo.AbuDbWhere{}
	where.Add("and", "CreateTime", ">=", StartTime, "")
	where.Add("and", "CreateTime", "<", EndTime, "")
	where.Add("and", "CSGroup", "=", reqdata.CSGroup, nil)
	where.Add("and", "CSId", "=", reqdata.CSId, nil)
	where.Add("and", "Symbol", "=", reqdata.Symbol, nil)
	result := map[string]interface{}{}
	data, _ := server.Db().Table("x_caijing_detail").Select("DISTINCT Stype").Where(where).GetList()
	for i := 0; i < len(*data); i++ {
		stype, _ := (*data)[i]["Stype"].(string)
		where1 := abugo.AbuDbWhere{}
		where1.Add("and", "CreateTime", ">=", StartTime, "")
		where1.Add("and", "CreateTime", "<", EndTime, "")
		where1.Add("and", "CSGroup", "=", reqdata.CSGroup, nil)
		where1.Add("and", "CSId", "=", reqdata.CSId, nil)
		where1.Add("and", "Symbol", "=", reqdata.Symbol, nil)
		where1.Add("and", "SType", "=", stype, nil)
		sum, _ := server.Db().Table("x_caijing_detail").Select("sum(Amount) as Amount").Where(where1).GetOne()
		result[stype] = abugo.GetFloat64FromInterface((*sum)["Amount"])
	}
	ctx.RespOK(result)
}

func (c *KeFuController) ip_detail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		Page     int
		PageSize int
		UserId   int
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "体验金审核", "查"), &errcode, "权限不足") {
		return
	}
	userdata, err := server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).First()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	if userdata == nil {
		ctx.RespErrString(true, &errcode, "用户不存在")
		return
	}
	table := server.XDb().Table("x_user").Where("RegisterIp = ?", userdata.String("RegisterIp"))
	total, err := table.Count()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	data, err := table.Select("Account,UserId,TgName,Address,RegisterTime,RegGift,UsdtGiftStatus,TrxGiftStatus").OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("total", total)
	ctx.Put("data", data.Maps())
	ctx.RespOK()
}

func (c *KeFuController) login_ip_detail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		Page     int
		PageSize int
		UserId   int32
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "客服系统", "体验金审核", "查"), &errcode, "权限不足") {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	var total int
	sql := " FROM x_user u WHERE UserId IN (SELECT DISTINCT UserId FROM x_login_log WHERE Ip IN (SELECT DISTINCT Ip FROM x_login_log WHERE UserId = ? GROUP BY UserId,Ip))"
	sqlCount := "SELECT Count(*)" + sql
	server.Db().Gorm().Raw(sqlCount, reqdata.UserId).Count(&total)
	var list []model.XUser
	sqlLimit := "SELECT u.Account,u.UserId,u.TgName,u.Address,u.RegisterTime,u.LoginTime,u.RegGift,u.UsdtGiftStatus,u.TrxGiftStatus" + sql + " LIMIT ? OFFSET ?"
	server.Db().GormDao().Raw(sqlLimit, reqdata.UserId, limit, offset).Scan(&list)

	ctx.Put("total", total)
	ctx.Put("data", list)
	ctx.RespOK()
}

func (c *KeFuController) ip_detailex(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		Page     int
		PageSize int
		UserId   int
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "体验金发放", "查"), &errcode, "权限不足") {
		return
	}
	userdata, err := server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).First()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	if userdata == nil {
		ctx.RespErrString(true, &errcode, "用户不存在")
		return
	}
	table := server.XDb().Table("x_user").Where("RegisterIp = ?", userdata.String("RegisterIp"))
	total, err := table.Count()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	data, err := table.Select("Account,UserId,TgName,Address,RegisterTime,RegGift").OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("total", total)
	ctx.Put("data", data.Maps())
	ctx.RespOK()
}
