// 用户当前游戏状态和禁止游戏厂商的Redis键前缀
package userbrand

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
)

const (
	UserCurrentGameKey       = "user:current_game:"        // 用户当前游戏状态
	UserBlockedGameBrandsKey = "user:blocked_game_brands:" // 用户禁止的游戏厂商列表
)

// 设置用户当前游戏状态
func SetUserCurrentGame(userId int, brand string) error {
	key := UserCurrentGameKey + strconv.Itoa(userId)
	err := server.Redis().SetStringEx(key, 300, brand)
	if err != nil {
		logs.Error("设置用户当前游戏状态失败 userId=", userId, " brand=", brand, " err=", err.Error())
	}
	return err
}

// 获取用户当前游戏状态
func GetUserCurrentGame(userId int) (string, error) {
	key := UserCurrentGameKey + strconv.Itoa(userId)
	//logs.Info("GetUserCurrentGame: userId=%d key=%s", userId, key)

	rValueInterface := server.Redis().Get(key)
	//logs.Info("GetUserCurrentGame: 从Redis获取到的原始值 rValueInterface=%+v", rValueInterface)

	rValue := abugo.GetStringFromInterface(rValueInterface)
	//logs.Info("GetUserCurrentGame: 转换后的字符串值 rValue=%s", rValue)

	return rValue, nil
}

// 清除用户当前游戏状态
func ClearUserCurrentGame(userId int) error {
	key := UserCurrentGameKey + strconv.Itoa(userId)
	err := server.Redis().Del(key)
	if err != nil {
		logs.Error("清除用户当前游戏状态失败 userId=", userId, " err=", err.Error())
	}
	return err
}

// 设置用户禁止的游戏厂商列表
func SetUserBlockedGameBrands(userId int, brandsJson string) error {
	key := UserBlockedGameBrandsKey + strconv.Itoa(userId)
	err := server.XRedis().Set(key, brandsJson, 7*24*3600) // 7天过期
	if err != nil {
		logs.Error("设置用户禁止游戏厂商列表失败 userId=", userId, " err=", err.Error())
	}
	return err
}

// 获取用户禁止的游戏厂商列表
func GetUserBlockedGameBrands(userId int) (string, error) {
	key := UserBlockedGameBrandsKey + strconv.Itoa(userId)
	result, err := server.XRedis().Get(key)
	rValue := abugo.GetStringFromInterface(result)
	if err != nil {
		logs.Error("获取用户禁止游戏厂商列表失败 userId=", userId, " err=", err.Error())
		return "", err
	}
	if result == nil {
		return "", nil // key不存在返回空字符串
	}
	return rValue, nil
}

// PublishToUser 发布消息到指定用户的WebSocket
func PublishToUser(userId int, msgType string, data interface{}) error {
	// 构造消息结构 - 使用大写字段，与订阅端保持一致
	message := map[string]interface{}{
		"UserId":    userId,
		"MsgType":   msgType,
		"Data":      data,
		"Timestamp": time.Now().Unix(),
		"MessageId": fmt.Sprintf("%d_%d_%s", userId, time.Now().UnixNano(), msgType),
	}

	// 发布到统一频道 - 让Redis客户端自动处理 JSON序列化
	channel := "ws:user:*"
	err := server.Redis().Publish(channel, message)
	if err != nil {
		logs.Error("发布WebSocket消息到Redis失败: userId=", userId, " channel=", channel, " err=", err.Error())
		return err
	}

	logs.Debug("WebSocket消息已发布到Redis: userId=", userId, " msgType=", msgType, " channel=", channel)
	return nil
}

// PublishToAllUsers 发布消息到所有用户（广播）
func PublishToAllUsers(msgType string, data interface{}) error {
	message := map[string]interface{}{
		"MsgType":   msgType,
		"Data":      data,
		"Timestamp": time.Now().Unix(),
		"MessageId": fmt.Sprintf("broadcast_%d_%s", time.Now().UnixNano(), msgType),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		logs.Error("序列化广播WebSocket消息失败: msgType=", msgType, " err=", err.Error())
		return err
	}

	// 广播频道
	channel := "ws:broadcast"
	err = server.Redis().Publish(channel, string(messageBytes))
	if err != nil {
		logs.Error("发布广播WebSocket消息到Redis失败: channel=", channel, " err=", err.Error())
		return err
	}

	logs.Debug("广播WebSocket消息已发布到Redis: msgType=", msgType, " channel=", channel)
	return nil
}
