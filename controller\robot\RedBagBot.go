package robot

import (
	"fmt"
	"time"
	"xserver/abugo"
	"xserver/server"
)

func (c *Router) reportRedPacketByUser(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page       int      `json:"page"`
		PageSize   int      `json:"page_size"`
		StartTime  int64    `json:"start_time"`
		EndTime    int64    `json:"end_time"`
		SellerID   []int32  `json:"seller_id"`
		ChannelID  []int32  `json:"channel_id"`
		UserChatID []int64  `json:"user_chat_id"`
		Name       []string `json:"name"`
		UserName   []string `json:"user_chat_name"`
		UserID     []int64  `json:"user_id"`
		IsExport   int      `json:"is_export"` // 是否导出 默认0 不导出
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	results := make([]map[string]interface{}, 0)
	sum := make([]map[string]interface{}, 0)
	dao := server.DaoxHashGame().XRobotRedbagReportUser
	query := dao.WithContext(nil).Select(dao.ALL)

	if len(req.Name) > 0 {
		query.Where(dao.Name.In(req.Name...))
	}
	if len(req.UserName) > 0 {
		query.Where(dao.UserName.In(req.UserName...))
	}
	if len(req.UserChatID) > 0 {
		query.Where(dao.UserChatID.In(req.UserChatID...))
	}
	if len(req.UserID) > 0 {
		query.Where(dao.UserID.In(req.UserID...))
	}
	tm1 := time.Unix(0, req.StartTime*int64(time.Millisecond))
	tm2 := time.Unix(0, req.EndTime*int64(time.Millisecond))
	if !tm1.IsZero() && !tm2.IsZero() && req.StartTime != 0 && req.EndTime != 0 {
		query.Where(dao.CreateTime.Between(tm1, tm2))
	}

	total, err := query.WithContext(nil).
		Order(dao.CreateTime).
		ScanByPage(&results, offset, limit)

	err = query.WithContext(nil).
		Select(
			dao.TotalPoints.Sum().As("total_points"),
			dao.SignCnt.Sum().As("sign_cnt"),
			dao.SignPoints.Sum().As("sign_points"),
			dao.GrabCnt.Sum().As("grab_cnt"),
			dao.GrabPoints.Sum().As("grab_points"),
			dao.AnswerCnt.Sum().As("answer_cnt"),
			dao.AnswerPoints.Sum().As("answer_points"),
		).Scan(&sum)

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.Put("total", total)
	ctx.Put("results", results)
	ctx.Put("sum", sum)
	ctx.RespOK()
}

func (c *Router) reportRedPacketBySign(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page       int      `json:"page"`
		PageSize   int      `json:"page_size"`
		StartTime  int64    `json:"start_time"`
		EndTime    int64    `json:"end_time"`
		SellerID   []int32  `json:"seller_id"`
		ChannelID  []int32  `json:"channel_id"`
		UserChatID []int64  `json:"user_chat_id"`
		Name       []string `json:"name"`
		UserName   []string `json:"user_chat_name"`
		UserID     []int64  `json:"user_id"`
		IsExport   int      `json:"is_export"` // 是否导出 默认0 不导出
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}
	groupSQL := "GROUP BY  seller_id, channel_id ,name , date(record_time) "
	where.Add("and", "1", "=", "1", nil)

	if len(req.UserChatID) > 0 {
		where.Add("and", "user_chat_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserName) > 0 {
		where.Add("and", "user_name", "in", SliceToSQLTuple(req.UserName), nil)
	}
	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "record_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "record_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	where.Add("and", "points_type", "=", "1", nil)
	whereSQL, whereData := where.Sql()

	sql := fmt.Sprintf(`
 	SELECT  DATE(record_time) date_time ,  seller_id ,channel_id , name , user_chat_id , 
		user_chat_name, user_id,
		SUM(points) sign_points ,
		count(user_id) sign_cnt 
		FROM  x_robot_redbag_points_change_log
 	WHERE %s
	%s 
 	limit %d OFFSET %d`, whereSQL, groupSQL, limit, offset)

	results, err := server.Db().Query(sql, whereData)
	pTotal, err := server.Db().Query(fmt.Sprintf(`
  	SELECT COUNT(1) as count   FROM  x_hash_game.x_robot_redbag_points_change_log  where %s  `, whereSQL), whereData)
	pSum, err := server.Db().Query(fmt.Sprintf(`
 		SELECT  DATE(record_time) date_time ,  seller_id ,channel_id , name , user_chat_id , 
		user_chat_name, user_id,
		SUM(points) sign_points ,
		count(user_id) sign_cnt 
		FROM  x_robot_redbag_points_change_log WHERE %s `, whereSQL), whereData)
	if results == nil || pTotal == nil || pSum == nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.Put("total", pTotal)
	ctx.Put("results", results)
	ctx.Put("sum", pSum)
	ctx.RespOK()
}

func (c *Router) reportRedPacketBySignByDetail(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		Name         []string `json:"name"`
		UserChatName []string `json:"user_chat_name"`
		UserChatID   []int64  `json:"user_chat_id"`
		UserID       []int64  `json:"user_id"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
		TestFlag     int      `json:"test_flag"`
	}
	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}

	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserChatName) > 0 {
		where.Add("and", "user_chat_name", "in", SliceToSQLTuple(req.UserChatName), nil)
	}
	if len(req.UserChatID) > 0 {
		where.Add("and", "user_chat_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	where.Add("and", "points_type", "=", "1", nil)
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	sql := fmt.Sprintf(` 
	SELECT  DATE(record_time) date_time ,  seller_id ,channel_id , name , user_chat_id , user_chat_name, user_id, points
 	FROM  x_robot_redbag_points_change_log 
 	%s 
 	limit %d OFFSET %d   `, whereSQL, limit, offset)
	results, err := server.Db().Query(sql, whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT count(1) as count
 	FROM  x_robot_redbag_points_change_log  %s `, whereSQL), whereData)
	ctx.Put("results", results)
	ctx.Put("total", pTotal)
	ctx.RespOK()
}

func (c *Router) reportRedPacketByRedBag(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		Name         []string `json:"name"`
		UserChatName []string `json:"user_chat_name"`
		UserChatID   []int64  `json:"user_chat_id"`
		UserID       []int64  `json:"user_id"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
		TestFlag     int      `json:"test_flag"`
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}
	groupSQL := "GROUP BY  seller_id, channel_id ,name , date_time "
	//where.Add("and", "1", "=", "1", nil)

	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserChatID) > 0 {
		where.Add("and", "user_chat_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserChatName) > 0 {
		where.Add("and", "user_name", "in", SliceToSQLTuple(req.UserChatName), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "claimed_user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	where1 := abugo.AbuDbWhere{}
	if req.StartTime > 0 && req.EndTime > 0 {
		where1.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where1.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
	}
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	_, whereData1 := where1.Sql()
	whereDataAll := append(whereData, whereData1...)
	sql := fmt.Sprintf(` 
    SELECT tm.date_time,tm.seller_id, tm.channel_id , tm.name, 
	SUM(red_bag_count)  red_bag_count , 
	SUM(red_bag_amount) red_bag_amount ,
	SUM( red_bag_total) red_bag_total  ,
	SUM(red_bag_total-claimed_count)  can_claimed_count ,
	SUM( claimed_count) claimed_count  ,
	SUM( claimed_amount )  claimed_amount ,
	SUM( claimed_user_count) claimed_user_count
	FROM (
	SELECT date_time,seller_id, channel_id , name,  
	   SUM(red_bag_count) red_bag_count, SUM(red_bag_amount) red_bag_amount,  SUM(red_bag_total) red_bag_total
	FROM (
	SELECT DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(DISTINCT red_packet_id) red_bag_count , 
	SUM( amount ) red_bag_amount,
	SUM( total ) red_bag_total
	FROM   x_robot_redbag_claim_record %s  %s , red_packet_id 
	) tmp  %s 
	) tm  
	JOIN 
	(
	 SELECT    DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(claimed_user_id) claimed_count,
	SUM(claimed_amount) claimed_amount ,
	COUNT(DISTINCT claimed_user_id) claimed_user_count 
	FROM  x_robot_redbag_claim_record   %s %s  
	)t 
	ON tm.date_time=t.date_time AND  tm.seller_id= t.seller_id
	AND   tm.channel_id= t.channel_id  AND   tm.name= t.name 
	%s  
 	limit %d OFFSET %d 
	`, whereSQL, groupSQL, groupSQL, whereSQL, groupSQL, groupSQL, limit, offset)

	results, err := server.Db().Query(sql, whereDataAll)
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT COUNT(1) AS total FROM (
	SELECT DATE(create_time) date_time , name 
	FROM  x_hash_game.x_robot_redbag_claim_record  %s  %s  ) tmp`, whereSQL, groupSQL), whereData)
	pSum, err := server.Db().Query(fmt.Sprintf(`
	SELECT tm.date_time,tm.seller_id, tm.channel_id , tm.name, 
	SUM(red_bag_count)  red_bag_count , 
	SUM(red_bag_amount) red_bag_amount ,
	SUM( red_bag_total) red_bag_total  ,
	SUM(red_bag_total-claimed_count)  can_claimed_count ,
	SUM( claimed_count) claimed_count  ,
	SUM( claimed_amount )  claimed_amount ,
	SUM( claimed_user_count) claimed_user_count
	FROM (
	SELECT date_time,seller_id, channel_id , name,  
	   SUM(red_bag_count) red_bag_count, SUM(red_bag_amount) red_bag_amount,  SUM(red_bag_total) red_bag_total
	FROM (
	SELECT DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(DISTINCT red_packet_id) red_bag_count , 
	SUM( amount ) red_bag_amount,
	SUM( total ) red_bag_total
	FROM   x_robot_redbag_claim_record %s  %s , red_packet_id 
	) tmp  %s 
	) tm  
	JOIN 
	(
	 SELECT    DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(claimed_user_id) claimed_count,
	SUM(claimed_amount) claimed_amount ,
	COUNT(DISTINCT claimed_user_id) claimed_user_count 
	FROM  x_robot_redbag_claim_record   %s %s  
	)t 
	ON tm.date_time=t.date_time AND  tm.seller_id= t.seller_id
	AND   tm.channel_id= t.channel_id  AND   tm.name= t.name`,
		whereSQL, groupSQL, groupSQL, whereSQL, groupSQL), whereDataAll)

	if results == nil || pTotal == nil || pSum == nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	total := abugo.GetInt64FromInterface((*pTotal)[0]["total"])

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.Put("total", total)
	ctx.Put("results", results)
	ctx.Put("sum", pSum)
	ctx.RespOK()
}

func (c *Router) reportRedPacketByRedBagByDetail(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		Name         []string `json:"name"`
		UserChatName []string `json:"user_chat_name"`
		UserChatID   []int64  `json:"user_chat_id"`
		UserID       []int64  `json:"user_id"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
		TestFlag     int      `json:"test_flag"`
	}
	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}

	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserChatName) > 0 {
		where.Add("and", "user_chat_name", "in", SliceToSQLTuple(req.UserChatName), nil)
	}
	if len(req.UserChatID) > 0 {
		where.Add("and", "user_chat_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "claimed_user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	sql := fmt.Sprintf(` 
	SELECT  DATE(create_time) date_time ,  seller_id ,channel_id , name , user_chat_id , user_chat_name, claimed_user_id,
	    COUNT(claimed_user_id) claimed_count,  
	    SUM(claimed_amount) claimed_amount 
 	FROM  x_robot_redbag_claim_record
 	%s  
 	group by  seller_id ,channel_id , name , claimed_user_id
 	limit %d OFFSET %d   `, whereSQL, limit, offset)
	results, err := server.Db().Query(sql, whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT  count(1) as count FROM  x_robot_redbag_claim_record 	%s     `, whereSQL), whereData)

	ctx.Put("results", results)
	ctx.Put("total", pTotal)
	ctx.RespOK()
}

func (c *Router) reportRedPacketByAnswer(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		Name         []string `json:"name"`
		UserChatName []string `json:"user_chat_name"`
		UserChatID   []int64  `json:"user_chat_id"`
		UserID       []int64  `json:"user_id"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}
	groupSQL := "GROUP BY  seller_id, channel_id ,name ,date_time  "

	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserChatName) > 0 {
		where.Add("and", "user_chat_name", "in", SliceToSQLTuple(req.UserChatName), nil)
	}
	if len(req.UserChatID) > 0 {
		where.Add("and", "user_chat_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "answer_user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	where1 := abugo.AbuDbWhere{}
	if req.StartTime > 0 && req.EndTime > 0 {
		where1.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where1.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
	}
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	_, whereData1 := where1.Sql()
	whereDataAll := append(whereData, whereData1...)

	sql := fmt.Sprintf(`
	SELECT tm.date_time,tm.seller_id, tm.channel_id , tm.name, 
	SUM(question_count)  question_count , 
	SUM(question_points) question_points ,
	SUM( answer_count) answer_count  ,
	SUM(answer_points)  answer_points ,
	 SUM( answer_bingo_count) answer_bingo_count  
	FROM (
	SELECT date_time,seller_id, channel_id , name,  
	   SUM(question_count) question_count, SUM(question_points) question_points 
	FROM (
	SELECT DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(DISTINCT uniq_id) question_count , 
	SUM( points ) question_points 
	FROM   x_robot_redbag_question_record   %s  %s , uniq_id 
	) tmp   %s  
	) tm  
	JOIN 
	(
	 SELECT  DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(answer_user_id) answer_count,
	SUM(IF(question_answer=answer_comment,points,0)) answer_points ,
	SUM(IF(question_answer=answer_comment,1,0)) answer_bingo_count 
	FROM  x_robot_redbag_question_record      %s %s  
	)t 
	ON tm.date_time=t.date_time AND  tm.seller_id= t.seller_id
	AND   tm.channel_id= t.channel_id  AND   tm.name= t.name 
	 %s 
 	limit %d OFFSET %d 
	`, whereSQL, groupSQL, groupSQL, whereSQL, groupSQL, groupSQL, limit, offset)

	results, err := server.Db().Query(sql, whereDataAll)
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT COUNT(1) AS total FROM (
	SELECT  DATE(create_time) date_time , name 
	FROM  x_hash_game.x_robot_redbag_question_record    %s  %s  ) tmp 
`, whereSQL, groupSQL), whereData)

	pSum, err := server.Db().Query(fmt.Sprintf(`
	SELECT tm.date_time,tm.seller_id, tm.channel_id , tm.name, 
	SUM(question_count)  question_count , 
	SUM(question_points) question_points ,
	SUM( answer_count) answer_count  ,
	SUM(answer_points)  answer_points ,
	SUM( answer_bingo_count) answer_bingo_count  
	FROM (
	SELECT date_time,seller_id, channel_id , name,  
	   SUM(question_count) question_count, SUM(question_points) question_points 
	FROM (
	SELECT DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(DISTINCT uniq_id) question_count , 
	SUM( points ) question_points 
	FROM   x_robot_redbag_question_record   %s  %s , uniq_id 
	) tmp   %s  
	) tm  
	JOIN 
	(
	 SELECT  DATE(create_time) date_time,seller_id, channel_id ,name, 
	COUNT(answer_user_id) answer_count,
	SUM(IF(question_answer=answer_comment,points,0)) answer_points ,
	SUM(IF(question_answer=answer_comment,1,0)) answer_bingo_count 
	FROM  x_robot_redbag_question_record      %s %s  
	)t 
	ON tm.date_time=t.date_time AND  tm.seller_id= t.seller_id
	AND   tm.channel_id= t.channel_id  AND   tm.name= t.name
	`, whereSQL, groupSQL, groupSQL, whereSQL, groupSQL), whereDataAll)

	if results == nil || pTotal == nil || pSum == nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	total := abugo.GetInt64FromInterface((*pTotal)[0]["total"])

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.Put("total", total)
	ctx.Put("results", results)
	ctx.Put("sum", pSum)
	ctx.RespOK()
}

func (c *Router) reportRedPacketByAnswerByDetail(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		Name         []string `json:"name"`
		UserChatName []string `json:"user_chat_name"`
		UserChatID   []string `json:"user_chat_id"`
		UserID       []int64  `json:"user_id"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
		TestFlag     int      `json:"test_flag"`
	}
	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}

	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserChatName) > 0 {
		where.Add("and", "user_chat_name", "in", SliceToSQLTuple(req.UserChatName), nil)
	}
	if len(req.UserChatID) > 0 {
		where.Add("and", "user_chat_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "answer_user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	sql := fmt.Sprintf(` 
	SELECT  DATE(create_time) date_time ,  seller_id ,channel_id ,name , user_chat_id,user_chat_name , answer_user_id,
		COUNT(answer_user_id) answer_time_count,  
		SUM(IF(question_answer=answer_comment,points,0)) answer_points ,
		SUM(IF(question_answer=answer_comment,1,0)) answer_bingo_count 
	FROM  x_robot_redbag_question_record
 	%s  
 	group by  seller_id ,channel_id , name , answer_user_id
 	limit %d OFFSET %d   `, whereSQL, limit, offset)
	results, err := server.Db().Query(sql, whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT  count(1) as count 
	FROM  x_robot_redbag_question_record
 	%s   
	`, whereSQL), whereData)

	ctx.Put("total", pTotal)
	ctx.Put("results", results)
	ctx.RespOK()
}

func (c *Router) reportRedPacketByExchange(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		Name         []string `json:"name"`
		UserChatName []string `json:"user_chat_name"`
		UserChatID   []string `json:"user_chat_id"`
		UserID       []int64  `json:"user_id"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}

	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserChatID) > 0 {
		where.Add("and", "chat_user_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserChatName) > 0 {
		where.Add("and", "chat_user_name", "in", SliceToSQLTuple(req.UserChatName), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)

		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	sql := fmt.Sprintf(`
    SELECT  DATE(create_time) date_time,  
    seller_id , channel_id , name , chat_user_id , chat_user_name , 
    user_id , points ,
    remain_points , exchange_rate , exchange_amount  , bill_limit_rate 
    FROM   x_robot_redbag_exchange_record 
	 %s 
 	limit %d OFFSET %d`, whereSQL, limit, offset)

	results, err := server.Db().Query(sql, whereData)
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT COUNT(1) AS total FROM (
	SELECT name FROM  x_hash_game.x_robot_redbag_exchange_record   %s  ) tmp`, whereSQL), whereData)

	pSum, err := server.Db().Query(fmt.Sprintf(`
	SELECT  DATE(create_time) date_time,  
    seller_id , channel_id , name , chat_user_id , chat_user_name , 
    user_id ,
	SUM(points)  AS points,
    SUM(remain_points) AS remain_points ,
	  exchange_rate  , 
	SUM(exchange_amount) as exchange_amount  , bill_limit_rate 
    FROM   x_robot_redbag_exchange_record  %s `, whereSQL), whereData)

	if results == nil || pTotal == nil || pSum == nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	total := abugo.GetInt64FromInterface((*pTotal)[0]["total"])

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.Put("total", total)
	ctx.Put("results", results)
	ctx.Put("sum", pSum)
	ctx.RespOK()
}

func (c *Router) reportRedPacketByPointsDetail(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		Name         []string `json:"name"`
		UserChatName []string `json:"user_chat_name"`
		UserChatID   []string `json:"user_chat_id"`
		UserID       []int64  `json:"user_id"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
		TestFlag     int      `json:"test_flag"`
	}
	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	//results := make([]map[string]interface{}, 0)
	where := abugo.AbuDbWhere{}

	if len(req.Name) > 0 {
		where.Add("and", "name", "in", SliceToSQLTuple(req.Name), nil)
	}
	if len(req.UserChatName) > 0 {
		where.Add("and", "user_chat_name", "in", SliceToSQLTuple(req.UserChatName), nil)
	}
	if len(req.UserChatID) > 0 {
		where.Add("and", "user_chat_id", "in", SliceToSQLTuple(req.UserChatID), nil)
	}
	if len(req.UserID) > 0 {
		where.Add("and", "user_id", "in", SliceToSQLTuple(req.UserID), nil)
	}
	//tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "create_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "create_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		//tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		//tm2 = abugo.TimeStampToLocalTime(req.EndTime)
	}
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	sql := fmt.Sprintf(` 
	SELECT   record_time,  seller_id , channel_id ,  name ,user_chat_id ,user_chat_name, user_id , 
		(CASE 	WHEN points_type =1 THEN "签到"  
		WHEN points_type =2 THEN "兑换" 
		WHEN points_type =3 THEN "红包"
		WHEN points_type =4 THEN "答题"  ELSE "未知" END) points_type,
		 change_before , points , change_after , remark 
	
	FROM  x_robot_redbag_points_change_log 
 	%s  
	ORDER BY  record_time DESC  
 	limit %d OFFSET %d   `, whereSQL, limit, offset)
	results, err := server.Db().Query(sql, whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	pSum, err := server.Db().Query(fmt.Sprintf(`
	SELECT  "总计" as date_time ,   
		 sum(change_before ) change_before,sum(points) points ,sum(change_after) change_after 
	FROM  x_robot_redbag_points_change_log 
 	%s  
	ORDER BY  record_time DESC  `, whereSQL), whereData)
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT count(1) as count
	FROM  x_robot_redbag_points_change_log %s   ORDER BY  record_time DESC `, whereSQL), whereData)
	ctx.Put("results", results)
	ctx.Put("sum", pSum)
	ctx.Put("total", pTotal)
	ctx.RespOK()
}
