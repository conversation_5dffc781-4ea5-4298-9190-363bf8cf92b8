package controller

import (
	"github.com/beego/beego/logs"
	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"github.com/golang-module/carbon/v2"
	"github.com/spf13/viper"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type TgPushMsgController struct{}

func (c *TgPushMsgController) Init() {
	group := server.Http().NewGroup("/api/tg/message")
	{
		group.Post("/list", c.list)
		group.Post("/create", c.create)
		group.Post("/update", c.update)
		group.Post("/cancel", c.cancel)
		if env := viper.GetString("server.env"); env != "dev" {
			go c.pushMessageToList()
			go c.getMessageToList()
		}
	}
}

func (c *TgPushMsgController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page        int
		PageSize    int
		TgRobotType int32
		SellerId    int32
	}
	errCode := 0
	req := RequestData{}

	if err := ctx.RequestData(&req); ctx.RespErr(err, &errCode) {
		return
	}

	type Results struct {
		model.XTgMessage
		SellerName string
		RobotsName string
	}

	var list []Results
	var limit, offset int
	limit = req.PageSize
	if req.Page > 0 {
		offset = (req.Page - 1) * req.PageSize
	}
	xTgMessage := server.DaoxHashGame().XTgMessage
	xTgRobotGuide := server.DaoxHashGame().XTgRobotGuide
	xSeller := server.DaoxHashGame().XSeller

	query := xTgMessage.WithContext(nil).
		Select(xTgMessage.ALL, xSeller.SellerName.As("SellerName")).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(xTgMessage.SellerID)).
		Order(xTgMessage.ID.Desc())

	if req.SellerId != 0 {
		query.Where(xTgMessage.SellerID.Eq(req.SellerId))
	}

	if req.TgRobotType != 0 {
		query.Where(xTgMessage.RobotType.Eq(req.TgRobotType))
	}

	total, err := query.ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	for i := range list {
		ids := strings.Split(list[i].Robots, ",")
		var intIdsArr []int64 // 用于存储转换后的 int64 数组

		for _, s := range ids {
			num, err := strconv.ParseInt(strings.TrimSpace(s), 10, 64) // 去除字符串中的空格并转换为 int64
			if err != nil {
				continue // 遇到错误时跳过，不清空整个数组
			}
			intIdsArr = append(intIdsArr, num) // 将转换后的数字追加到 intArr 数组
		}

		if len(intIdsArr) > 0 {
			var robots []string
			err := xTgRobotGuide.WithContext(nil).Where(xTgRobotGuide.ID.In(intIdsArr...)).Pluck(xTgRobotGuide.TgRobotUserName, &robots) // 注意传递 &robots
			if err != nil {
				return
			}
			list[i].RobotsName = strings.Join(robots, ",") // 将机器人名称数组拼接为逗号分隔的字符串
		}
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TgPushMsgController) create(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int32
		Message    string
		Robots     string
		PushType   int32
		Users      string
		RobotType  int32
		PushTime   int64
		GoogleCode string
	}
	errCode := 0
	req := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "TG引客机器人", "增", "TG机器人创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, req.GoogleCode), &errCode) {
		return
	}

	xTgMessage := server.DaoxHashGame().XTgMessage
	err := xTgMessage.WithContext(nil).Create(&model.XTgMessage{
		SellerID:  req.SellerId,
		Message:   req.Message,
		Robots:    req.Robots,
		PushType:  req.PushType,
		Users:     req.Users,
		RobotType: req.RobotType,
		PushTime:  carbon.CreateFromTimestampMilli(req.PushTime).StdTime(),
		Operator:  token.Account,
	})

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	ctx.RespOK()
}

func (c *TgPushMsgController) cancel(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id         int32 `validate:"required"`
		GoogleCode string
	}

	errCode := 0
	req := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "TG引客机器人", "增", "TG机器人创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, req.GoogleCode), &errCode) {
		return
	}

	xTgMessage := server.DaoxHashGame().XTgMessage
	info, _ := xTgMessage.WithContext(nil).Where(xTgMessage.ID.Eq(req.Id)).Where(xTgMessage.State.Eq(2)).First()
	if info != nil {
		_, err := xTgMessage.WithContext(nil).Where(xTgMessage.ID.Eq(req.Id)).Update(xTgMessage.State, 3)
		if err != nil {
			ctx.RespErrString(true, &errCode, err.Error())
			return
		}
	} else {
		ctx.RespErrString(true, &errCode, "已撤销或数据不存在")
		return
	}

	ctx.RespOK()
}

func (c *TgPushMsgController) update(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id         int32
		SellerId   int32
		Message    string
		Robots     string
		PushType   int32
		Users      string
		RobotType  int32
		PushTime   int64
		GoogleCode string
	}

	errCode := 0
	req := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "TG引客机器人", "改", "TG机器人创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, req.GoogleCode), &errCode) {
		return
	}

	xTgMessage := server.DaoxHashGame().XTgMessage
	_, err := xTgMessage.WithContext(nil).Where(xTgMessage.ID.Eq(req.Id)).Where(xTgMessage.State.Eq(3)).Updates(&model.XTgMessage{
		SellerID:  req.SellerId,
		Message:   req.Message,
		Robots:    req.Robots,
		PushType:  req.PushType,
		Users:     req.Users,
		RobotType: req.RobotType,
		PushTime:  carbon.CreateFromTimestampMilli(req.PushTime).StdTime(),
		Operator:  token.Account,
		State:     2,
	})
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	ctx.RespOK()
}

// 将消息丢到队列中
func (c *TgPushMsgController) pushMessageToList() {
	for {
		xTgMessage := server.DaoxHashGame().XTgMessage
		info, _ := xTgMessage.WithContext(nil).Where(xTgMessage.PushTime.Lte(carbon.Now().StdTime())).Where(xTgMessage.State.Eq(2)).First()
		if info != nil {
			err := server.XRedis().RPush("MESSAGES_LIST", info.ID, "")
			if err != nil {
				logs.Info("推送消息失败：", err)
				break
			}

			if _, err := xTgMessage.WithContext(nil).Where(xTgMessage.ID.Eq(info.ID)).Update(xTgMessage.State, 4); err != nil {
				logs.Info("更新状态失败：", err)
				break
			}
		}

		time.Sleep(5 * time.Second)
	}
}

// 从队列获取消息
func (c *TgPushMsgController) getMessageToList() {
	for {
		pop, err := server.XRedis().LPop("MESSAGES_LIST")
		if err != nil {
			//logs.Info("获取消息失败：", err)
			time.Sleep(5 * time.Second)
			continue
		}

		id, err := strconv.ParseInt(string(pop), 10, 64)
		if err != nil {
			log.Fatal("Type conversion error:", err)
			return
		}
		// 发送消息
		c.sendMessage(id)
		time.Sleep(5 * time.Second)
	}
}

// 发送消息
func (c *TgPushMsgController) sendMessage(Id int64) {
	xTgMessage := server.DaoxHashGame().XTgMessage
	xTgRobotGuide := server.DaoxHashGame().XTgRobotGuide
	xTgChat := server.DaoxHashGame().XTgChat
	info, _ := xTgMessage.WithContext(nil).Where(xTgMessage.ID.Eq(int32(Id))).Where(xTgMessage.State.Eq(4)).First()
	if info != nil {
		// 指定机器人发送消息
		var ids []int64 // 用于存储转换后的 int64 数组
		var bot *tgbotapi.BotAPI
		var chats []*model.XTgChat

		if info.Robots != "" {
			robots := strings.Split(info.Robots, ",")
			for _, s := range robots {
				num, err := strconv.ParseInt(strings.TrimSpace(s), 10, 64) // 去除字符串中的空格并转换为 int64
				if err != nil {
					continue // 遇到错误时跳过，不清空整个数组
				}
				ids = append(ids, num) // 将转换后的数字追加到 intArr 数组
			}
		} else {
			// 获取所有机器人ID
			err := xTgRobotGuide.WithContext(nil).
				Where(xTgRobotGuide.IsEnable.Eq(1)).
				Where(xTgRobotGuide.TgRobotType.Eq(info.RobotType)).
				Pluck(xTgRobotGuide.ID, ids)
			if err != nil {
				logs.Error(err)
				return
			}
		}

		// 获取指定机器人详细信息
		tgRobotGuids, err := xTgRobotGuide.WithContext(nil).
			Where(xTgRobotGuide.ID.In(ids...)).
			Where(xTgRobotGuide.IsEnable.Eq(1)).
			Find()
		if err != nil {
			logs.Error("获取机器人信息失败", err)
			return
		}

		// 所有名单
		if info.PushType == 1 || info.PushType == 2 {
			// 根据 PushType 获取 ChatType
			pushTypeMaps := map[int]string{1: "group", 2: "private"}
			chatType, ok := pushTypeMaps[int(info.PushType)]
			if !ok {
				logs.Info("未找到匹配的 ChatType")
				return
			}

			// 遍历机器人获取该机器人所拥有的聊天室
			for _, robotGuide := range tgRobotGuids {
				bot, _ = tgbotapi.NewBotAPI(robotGuide.TgRobotToken)
				chats, err = xTgChat.WithContext(nil).
					Where(xTgChat.GuideID.Eq(robotGuide.ID)).
					Where(xTgChat.Type.Eq(chatType)).
					Find()
				if err != nil {
					logs.Error("获取 Chat 失败:", err)
					continue
				}
			}
		}

		// 指定名单
		if info.PushType == 3 || info.PushType == 4 && info.Users != "" {
			var chatIds []int64
			users := strings.Split(info.Users, ",")
			for _, s := range users {
				num, err := strconv.ParseInt(strings.TrimSpace(s), 10, 64) // 去除字符串中的空格并转换为 int64
				if err != nil {
					continue // 遇到错误时跳过，不清空整个数组
				}
				chatIds = append(chatIds, num) // 将转换后的数字追加到 intArr 数组
			}

			// 根据 PushType 获取 ChatType
			pushTypeMaps := map[int]string{4: "group", 3: "private"}
			chatType, ok := pushTypeMaps[int(info.PushType)]
			if !ok {
				logs.Info("未找到匹配的 ChatType")
				return
			}

			// 遍历机器人获取该机器人所拥有的聊天室
			for _, robotGuide := range tgRobotGuids {
				bot, _ = tgbotapi.NewBotAPI(robotGuide.TgRobotToken)
				chats, err = xTgChat.WithContext(nil).
					Where(xTgChat.GuideID.Eq(robotGuide.ID)).
					Where(xTgChat.Type.Eq(chatType)).
					Where(xTgChat.ChatID.In(chatIds...)).
					Find()

				if err != nil {
					logs.Error("获取 Chat 失败:", err)
					continue
				}
			}
		}

		if chats != nil {
			const batchSize = 20
			semaphore := make(chan struct{}, batchSize) // 控制并发数
			var wg sync.WaitGroup

			for _, chat := range chats {
				semaphore <- struct{}{} // 占用一个并发槽
				wg.Add(1)

				go func(chat *model.XTgChat) {
					defer wg.Done()
					defer func() { <-semaphore }() // 释放并发槽

					tgRobot := server.Robot{Bot: bot}
					err := tgRobot.SendMessage(chat.ChatID, chat.Lang, info.Message)

					if err != nil {
						//_, err := xTgChat.WithContext(nil).Where(xTgChat.ID.Eq(chat.ID)).Delete()
						//if err != nil {
						//	logs.Error("删除无用聊天室:", err)
						//}
						logs.Error("发送消息失败:", err)
					}
				}(chat)
			}

			// 等待所有消息发送完毕
			wg.Wait()
			close(semaphore)
		}

		// 更新消息状态为已发送
		if _, err := xTgMessage.WithContext(nil).
			Where(xTgMessage.ID.Eq(info.ID)).
			Update(xTgMessage.State, 1); err != nil {
			logs.Error("更新消息状态失败:", err)
		}

	}
}
