// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXActiveSellerDefine = "x_active_seller_define"

// XActiveSellerDefine 运营商活动
type XActiveSellerDefine struct {
	SellerID        int32     `gorm:"column:SellerId;primaryKey;comment:运营商Id" json:"SellerId"`                                                       // 运营商Id
	ActiveID        int32     `gorm:"column:ActiveId;primaryKey;comment:活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎" json:"ActiveId"` // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	Memo            string    `gorm:"column:Memo;comment:活动说明" json:"Memo"`                                                                           // 活动说明
	AuditType       int32     `gorm:"column:AuditType;comment:审核方式 1人工审核 2自动审核" json:"AuditType"`                                                     // 审核方式 1人工审核 2自动审核
	State           int32     `gorm:"column:State;comment:状态" json:"State"`                                                                           // 状态
	Sort            int32     `gorm:"column:Sort;comment:排序" json:"Sort"`                                                                             // 排序
	EffectStartTime int64     `gorm:"column:EffectStartTime;comment:活动开始时间" json:"EffectStartTime"`                                                   // 活动开始时间
	EffectEndTime   int64     `gorm:"column:EffectEndTime;comment:活动截止时间" json:"EffectEndTime"`                                                       // 活动截止时间
	Title           string    `gorm:"column:Title;comment:活动名称" json:"Title"`                                                                         // 活动名称
	TitleImg        string    `gorm:"column:TitleImg;comment:图片" json:"TitleImg"`                                                                     // 图片
	TitleImgCn      string    `gorm:"column:TitleImgCn;comment:图片中文" json:"TitleImgCn"`                                                               // 图片中文
	TitleImgEn      string    `gorm:"column:TitleImgEn;comment:图片英文" json:"TitleImgEn"`                                                               // 图片英文
	TopImg          string    `gorm:"column:TopImg;comment:置顶图片" json:"TopImg"`                                                                       // 置顶图片
	TopImgEn        string    `gorm:"column:TopImgEn;comment:置顶图片英文" json:"TopImgEn"`                                                                 // 置顶图片英文
	IsTop           int32     `gorm:"column:IsTop;default:2;comment:是否置顶 1是 2否" json:"IsTop"`                                                         // 是否置顶 1是 2否
	TopSort         int32     `gorm:"column:TopSort;comment:置顶顺序" json:"TopSort"`                                                                     // 置顶顺序
	GameType        string    `gorm:"column:GameType;comment:游戏分类" json:"GameType"`                                                                   // 游戏分类
	MinLiuShui      float64   `gorm:"column:MinLiuShui;comment:提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比" json:"MinLiuShui"`                                     // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	ExtReward       float64   `gorm:"column:ExtReward;comment:额外奖金 闯关活动才有的配置" json:"ExtReward"`                                                       // 额外奖金 闯关活动才有的配置
	MinDeposit      float64   `gorm:"column:MinDeposit;comment:最低存款 救援金 和 首日充次日送 活动才有的配置" json:"MinDeposit"`                                          // 最低存款 救援金 和 首日充次日送 活动才有的配置
	MaxReward       float64   `gorm:"column:MaxReward;comment:最大返还金额 救援金活动才有的配置 首日充次日送最大彩金上限" json:"MaxReward"`                                       // 最大返还金额 救援金活动才有的配置 首日充次日送最大彩金上限
	ValidRecharge   float64   `gorm:"column:ValidRecharge;comment:有效会员最低充值" json:"ValidRecharge"`                                                     // 有效会员最低充值
	ValidLiuShui    float64   `gorm:"column:ValidLiuShui;comment:有效会员最低流水" json:"ValidLiuShui"`                                                       // 有效会员最低流水
	TrxPrice        float64   `gorm:"column:TrxPrice;comment:Trx按多少倍计算下注价格(同后台配置VipTrxPrice)" json:"TrxPrice"`                                        // Trx按多少倍计算下注价格(同后台配置VipTrxPrice)
	Config          string    `gorm:"column:Config;comment:活动配置" json:"Config"`                                                                       // 活动配置
	BaseConfig      string    `gorm:"column:BaseConfig;comment:基础配置" json:"BaseConfig"`                                                               // 基础配置
	CreateTime      time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`                            // 创建时间
	UpdateTime      time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`                            // 更新时间
}

// TableName XActiveSellerDefine's table name
func (*XActiveSellerDefine) TableName() string {
	return TableNameXActiveSellerDefine
}
