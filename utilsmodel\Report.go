package utilsmodel

import "github.com/shopspring/decimal"

type Comprehensive struct {
	Type           string          `json:"type" gorm:"column:type"`
	TypeId         int             `json:"type_id" gorm:"column:type_id"`
	Amount         decimal.Decimal `gorm:"column:Amount"`
	Fee            decimal.Decimal `gorm:"column:Fee"`
	LiuSui         decimal.Decimal `gorm:"column:<PERSON><PERSON><PERSON>"`
	RewardAmount   decimal.Decimal `gorm:"column:RewardAmount"`
	TotalBetCount  int32           `gorm:"column:TotalBetCount"`
	TotalBetUser   int32           `gorm:"column:TotalBetUser"`
	WinCount       int32           `gorm:"column:WinCount"`
	ValidBetAmount decimal.Decimal `gorm:"column:ValidBetAmount"`
}

type ComprehensiveUpRateResult struct {
	Type           string          `json:"type" gorm:"column:type"`
	TypeId         int             `json:"type_id" gorm:"column:type_id"`
	Amount         decimal.Decimal `gorm:"column:Amount"`
	Fee            decimal.Decimal `gorm:"column:Fee"`
	LiuSui         decimal.Decimal `gorm:"column:LiuSui"`
	RewardAmount   decimal.Decimal `gorm:"column:RewardAmount"`
	TotalBetCount  int32           `gorm:"column:TotalBetCount"`
	TotalBetUser   int32           `gorm:"column:TotalBetUser"`
	WinCount       int32           `gorm:"column:WinCount"`
	ValidBetAmount decimal.Decimal `gorm:"column:ValidBetAmount"`
	TotalWin       decimal.Decimal `gorm:"column:TotalWin"`

	VsAmount         decimal.Decimal `gorm:"column:VsAmount"`
	VsFee            decimal.Decimal `gorm:"column:VsFee"`
	VsLiuSui         decimal.Decimal `gorm:"column:VsLiuSui"`
	VsRewardAmount   decimal.Decimal `gorm:"column:VsRewardAmount"`
	VsTotalBetCount  int32           `gorm:"column:VsTotalBetCount"`
	VsTotalBetUser   int32           `gorm:"column:VsTotalBetUser"`
	VsWinCount       int32           `gorm:"column:VsWinCount"`
	VsValidBetAmount decimal.Decimal `gorm:"column:VsValidBetAmount"`
	VsTotalWin       decimal.Decimal `gorm:"column:VsTotalWin"`

	AmountUpRate        decimal.Decimal `gorm:"column:AmountUpRate"`
	FeeUpRate           decimal.Decimal `gorm:"column:FeeUpRate"`
	LiuSuiUpRate        decimal.Decimal `gorm:"column:LiuSuiUpRate"`
	RewardUpRate        decimal.Decimal `gorm:"column:RewardUpRate"`
	TotalBetCountUpRate decimal.Decimal `gorm:"column:TotalBetCountUpRate"`
	TotalBetUserUpRate  decimal.Decimal `gorm:"column:TotalBetUserUpRate"`
	WinCountUpRate      decimal.Decimal `gorm:"column:WinCountUpRate"`
	ValidBetUpRate      decimal.Decimal `gorm:"column:ValidBetUpRate"`
	TotalWinUpRate      decimal.Decimal `gorm:"column:TotalWinUpRate"`
}
