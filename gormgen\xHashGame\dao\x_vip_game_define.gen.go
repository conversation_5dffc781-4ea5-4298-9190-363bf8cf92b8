// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXVipGameDefine(db *gorm.DB, opts ...gen.DOOption) xVipGameDefine {
	_xVipGameDefine := xVipGameDefine{}

	_xVipGameDefine.xVipGameDefineDo.UseDB(db, opts...)
	_xVipGameDefine.xVipGameDefineDo.UseModel(&model.XVipGameDefine{})

	tableName := _xVipGameDefine.xVipGameDefineDo.TableName()
	_xVipGameDefine.ALL = field.NewAsterisk(tableName)
	_xVipGameDefine.ID = field.NewInt32(tableName, "Id")
	_xVipGameDefine.SellerID = field.NewInt32(tableName, "SellerId")
	_xVipGameDefine.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xVipGameDefine.VipLevel = field.NewInt32(tableName, "VipLevel")
	_xVipGameDefine.Brand = field.NewString(tableName, "Brand")
	_xVipGameDefine.GameID = field.NewString(tableName, "GameId")
	_xVipGameDefine.CatID = field.NewInt32(tableName, "CatId")
	_xVipGameDefine.RewardRate = field.NewFloat64(tableName, "RewardRate")
	_xVipGameDefine.Memo = field.NewString(tableName, "Memo")
	_xVipGameDefine.CreateTime = field.NewTime(tableName, "CreateTime")
	_xVipGameDefine.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xVipGameDefine.fillFieldMap()

	return _xVipGameDefine
}

type xVipGameDefine struct {
	xVipGameDefineDo xVipGameDefineDo

	ALL        field.Asterisk
	ID         field.Int32
	SellerID   field.Int32
	ChannelID  field.Int32
	VipLevel   field.Int32   // vip等级
	Brand      field.String  // 品牌
	GameID     field.String  // 游戏Id
	CatID      field.Int32   // 彩票大类ID
	RewardRate field.Float64 // 反水比例
	Memo       field.String  // 备注
	CreateTime field.Time    // 创建时间
	UpdateTime field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xVipGameDefine) Table(newTableName string) *xVipGameDefine {
	x.xVipGameDefineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xVipGameDefine) As(alias string) *xVipGameDefine {
	x.xVipGameDefineDo.DO = *(x.xVipGameDefineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xVipGameDefine) updateTableName(table string) *xVipGameDefine {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.VipLevel = field.NewInt32(table, "VipLevel")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.CatID = field.NewInt32(table, "CatId")
	x.RewardRate = field.NewFloat64(table, "RewardRate")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xVipGameDefine) WithContext(ctx context.Context) *xVipGameDefineDo {
	return x.xVipGameDefineDo.WithContext(ctx)
}

func (x xVipGameDefine) TableName() string { return x.xVipGameDefineDo.TableName() }

func (x xVipGameDefine) Alias() string { return x.xVipGameDefineDo.Alias() }

func (x xVipGameDefine) Columns(cols ...field.Expr) gen.Columns {
	return x.xVipGameDefineDo.Columns(cols...)
}

func (x *xVipGameDefine) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xVipGameDefine) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["VipLevel"] = x.VipLevel
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["CatId"] = x.CatID
	x.fieldMap["RewardRate"] = x.RewardRate
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xVipGameDefine) clone(db *gorm.DB) xVipGameDefine {
	x.xVipGameDefineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xVipGameDefine) replaceDB(db *gorm.DB) xVipGameDefine {
	x.xVipGameDefineDo.ReplaceDB(db)
	return x
}

type xVipGameDefineDo struct{ gen.DO }

func (x xVipGameDefineDo) Debug() *xVipGameDefineDo {
	return x.withDO(x.DO.Debug())
}

func (x xVipGameDefineDo) WithContext(ctx context.Context) *xVipGameDefineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xVipGameDefineDo) ReadDB() *xVipGameDefineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xVipGameDefineDo) WriteDB() *xVipGameDefineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xVipGameDefineDo) Session(config *gorm.Session) *xVipGameDefineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xVipGameDefineDo) Clauses(conds ...clause.Expression) *xVipGameDefineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xVipGameDefineDo) Returning(value interface{}, columns ...string) *xVipGameDefineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xVipGameDefineDo) Not(conds ...gen.Condition) *xVipGameDefineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xVipGameDefineDo) Or(conds ...gen.Condition) *xVipGameDefineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xVipGameDefineDo) Select(conds ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xVipGameDefineDo) Where(conds ...gen.Condition) *xVipGameDefineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xVipGameDefineDo) Order(conds ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xVipGameDefineDo) Distinct(cols ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xVipGameDefineDo) Omit(cols ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xVipGameDefineDo) Join(table schema.Tabler, on ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xVipGameDefineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xVipGameDefineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xVipGameDefineDo) Group(cols ...field.Expr) *xVipGameDefineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xVipGameDefineDo) Having(conds ...gen.Condition) *xVipGameDefineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xVipGameDefineDo) Limit(limit int) *xVipGameDefineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xVipGameDefineDo) Offset(offset int) *xVipGameDefineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xVipGameDefineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xVipGameDefineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xVipGameDefineDo) Unscoped() *xVipGameDefineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xVipGameDefineDo) Create(values ...*model.XVipGameDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xVipGameDefineDo) CreateInBatches(values []*model.XVipGameDefine, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xVipGameDefineDo) Save(values ...*model.XVipGameDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xVipGameDefineDo) First() (*model.XVipGameDefine, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipGameDefine), nil
	}
}

func (x xVipGameDefineDo) Take() (*model.XVipGameDefine, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipGameDefine), nil
	}
}

func (x xVipGameDefineDo) Last() (*model.XVipGameDefine, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipGameDefine), nil
	}
}

func (x xVipGameDefineDo) Find() ([]*model.XVipGameDefine, error) {
	result, err := x.DO.Find()
	return result.([]*model.XVipGameDefine), err
}

func (x xVipGameDefineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XVipGameDefine, err error) {
	buf := make([]*model.XVipGameDefine, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xVipGameDefineDo) FindInBatches(result *[]*model.XVipGameDefine, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xVipGameDefineDo) Attrs(attrs ...field.AssignExpr) *xVipGameDefineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xVipGameDefineDo) Assign(attrs ...field.AssignExpr) *xVipGameDefineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xVipGameDefineDo) Joins(fields ...field.RelationField) *xVipGameDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xVipGameDefineDo) Preload(fields ...field.RelationField) *xVipGameDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xVipGameDefineDo) FirstOrInit() (*model.XVipGameDefine, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipGameDefine), nil
	}
}

func (x xVipGameDefineDo) FirstOrCreate() (*model.XVipGameDefine, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipGameDefine), nil
	}
}

func (x xVipGameDefineDo) FindByPage(offset int, limit int) (result []*model.XVipGameDefine, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xVipGameDefineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xVipGameDefineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xVipGameDefineDo) Delete(models ...*model.XVipGameDefine) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xVipGameDefineDo) withDO(do gen.Dao) *xVipGameDefineDo {
	x.DO = *do.(*gen.DO)
	return x
}
