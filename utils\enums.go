package utils

const UnDefineId = 0

// 活动的常量
const KActiveIdEachBetFanShui int = 1
const KActiveIdRechange int = 2
const KActiveIdHaXiBreakout int = 3
const KActiveIdQipaiBreakout int = 4
const KActiveIdDianziBreakout int = 5
const KActiveIdRescue int = 6
const KActiveIdInvite int = 7
const KActiveIdVipFanShui int = 8
const KActiveIdXianglongfuhu = 9
const KActiveIdCiRiSong = 10
const KActiveIdMeiZhouSong = 11
const KActiveIdFirstDepositGift = 57         // 新用户首存礼
const KActiveIdCumulativeWeeklyRecharge = 60 // 累计周充值，豪礼享不停
const KActiveIdSignReward = 55               // 签到奖励
const KActiveIdRecommendFriendReward = 54    // 推荐好友多重奖励
const KActiveIdDianziMeiZhouSong = 11        // 电子周末狂欢送
const KActiveIdZhenrenMeiZhouSong = 70       // 真人视讯周末狂欢送
const KActiveIdQiPaiMeiZhouSong = 71         // 棋牌游戏周末狂欢送
const KActiveIdZhenrenBreakout = 69          // 真人视讯闯关
const KActiveIdEnergyWhitelist = 13          // 能量白名单
const KActiveIdNewFirstDeposit = 77          // 新首存活动180
const KActiveIdDailyRechargeRebate = 78      // 每日充值洗码返利
const KActiveIdRecommendNewMemberGift = 79   // 推荐新会员，充值享豪礼
const KActiveIdLuckyDice = 80                // 幸运骰子
const KActiveIdBoomingReward = 81            // 爆庄奖励
const KActiveIdPointGift = 82                // 积分兑豪礼
const KActiveIdWeeklySignActiveReward = 83   // 周签到活跃奖励
const KActiveIdMidAutumnZhenrenBreakout = 84 // 中秋每日真人闯关
const KActiveIdMidAutumnBreakout = 85        // 中秋每日流水闯关
const KActiveIdCryptoGamesBreakout = 86      // 加密游戏闯关

const RegisterGift = 1000          // 注册赠送活动
const NewUserDepositGift = 1001    // 新用户充值赠送活动
const SpecialBonusGift = 1002      // 特殊礼金活动
const RedeemCodeGift_1003 = 1003   // 兑换码活动
const SocialMediaFollowGift = 1004 // 关注社媒，福利领不停
const ChatRoomBonusGift = 1005     // 聊天室红包活动
const FirstDepositGift = 1006      // 首充活动
const MultipleDepositGift = 1007   // 复充活动
const MagicFirstDepositGift = 1008 // magic首充活动
const X9FirstDepositGift = 1009    // X9首充活动
const X9SecondDepositGift = 1010   // X9第二次充值活动
const X9ThirdDepositGift = 1011    // X9第三次充值活动
const X9FourthDepositGift = 1012   // X9第四次充值活动
const X9FifthDepositGift = 1013    // X9第五次充值活动
const HashWeeklyChallenge = 1014   // 哈希周周闯关活动
const RedeemCodeGift_1015 = 1015   // 兑换码活动1015
const RedeemCodeGift_1016 = 1016   // 兑换码活动1016
const RedeemCodeGift_1017 = 1017   // 兑换码活动1017

// 自定义活动ID
const KDIYActiveIdRecharge = 1001 // 自定义充值活动

//const ActiveCiRiLiuShuiBeiShu = 10

const ActiveAuditRenGong = 1
const ActiveAuditAuto = 2

const ActiveStateOpen = 1
const ActiveStateClose = 2

const ActiveAwardAuditStateWait = 1     //等待审核
const ActiveAwardAuditStateRefuse = 2   //人工拒绝
const ActiveAwardAuditStatePass = 3     //手动通过
const ActiveAwardAuditStateAutoPass = 4 //自动通过

// 用户的常量
const UserTestType = 1

// 充值状态
const RechargeCantFindUser = 1
const RechargeTooSmall = 1
const RechargeSuccess = 5

const (
	IsActiveTest = true
)

const (
	ErrInternal = -1
)

// 账变类型
const (
	BalanceCReasonCharge         = 1
	BalanceCReasonWithdraw       = 2
	BalanceCReasonWithdrawRefuse = 6

	BalanceCReasonBet = 9
	BalanceCReasonWin = 16

	BalanceCReasonFaHongBao     = 32
	BalanceCReasonQiangHongBao  = 33
	BalanceCReasonHongBaoFanHui = 34

	BalanceCReasonASend             = 3
	BalanceCReasonARiFanShui        = 17
	BalanceCReasonAShengJi          = 18
	BalanceCReasonAYueLiJin         = 19
	BalanceCReasonANengLiang        = 20
	BalanceCReasonAChongZhiRenWu    = 25
	BalanceCReasonAHashChuangGuan   = 26
	BalanceCReasonAQiPaiChuangGuan  = 27
	BalanceCReasonADianZiChuangGuan = 28
	BalanceCReasonAJiuYuanJin       = 29
	BalanceCReasonAYaoQing          = 30
	BalanceCReasonAXiangLong        = 31

	BalanceCReasonLOut    = 7
	BalanceCReasonLIn     = 8
	BalanceCReasonDZOut   = 10
	BalanceCReasonDZIn    = 11
	BalanceCReasonQPDZOut = 12
	BalanceCReasonQPDZIn  = 13
	BalanceCReasonIGOut   = 14
	BalanceCReasonIGIn    = 15

	BalanceCReasonPGOut  = 100
	BalanceCReasonPGIn   = 101
	BalanceCReasonPPOut  = 102
	BalanceCReasonPPIn   = 103
	BalanceCReasonEVOOut = 104
	BalanceCReasonEVOIn  = 106
	BalanceCReasonWMOut  = 105
	BalanceCReasonWMIn   = 107
	BalanceCReasonAGOut  = 108
	BalanceCReasonAGIn   = 109

	BalanceCReasonBrokerage = 4
	BalanceCReasonBackend   = 5
	BalanceCReasonTrial     = 201
	BalanceCReasonTest      = 226
	BalanceCReasonClear     = 50

	BalanceCReasonLucky        = 221
	BalanceCReasonLossCall     = 222
	BalanceCReasonAHongBao     = 223
	BalanceCReasonAFirstCharge = 224
	BalanceCReasonADice        = 225
	BalanceCReasonACiRiSong    = 227
	BalanceCReasonAMeiZhouSong = 228
	BalanceCReasonAGuoQing     = 229
)

const (
	TableActiveDefine      = "x_active_define"
	TableActiveDefineOld   = "x_active_define_old"
	TableActiveInfo        = "x_active_info"
	TableActiveInfoOld     = "x_active_info_old"
	TableUser              = "x_user"
	TableActiveRewardAudit = "x_active_reward_audit"
	TableActiveReward      = "x_active_reward"
	TableAmountChangeLog   = "x_amount_change_log"
	TableVipInfo           = "x_vip_info"
	TableVipDailly         = "x_vip_dailly"
	TableUserDailly        = "x_user_dailly"
	TableCaiJinDetail      = "x_caijing_detail"
	TableRecharge          = "x_recharge"
	TableUserTransfer      = "x_user_transfer" // 用户转账表
)

const (
	SymbolUSDT = "usdt"
)

const (
	Commission_98_Type_Self = iota + 1
	Commission_98_Type_Direct
	Commission_98_Type_Team
)

var commission98TypeNick = map[int]string{
	Commission_98_Type_Self:   "自身佣金",
	Commission_98_Type_Direct: "直属佣金",
	Commission_98_Type_Team:   "团队佣金",
}

// 激励文案搜索类型
const RewardwordWinning = 11   // 中奖
const RewardwordNoWinning = 21 // 未中奖

// 人工增资限制
const ArtificialCapitalIncrease = 100000

const (
	TimeFormatStr = "2006-01-02 15:04:05"
	DateFormatStr = "2006-01-02"
)

func Commission98TypeNick(t int) string {
	if nick, ok := commission98TypeNick[t]; ok {
		return nick
	}
	return ""
}

var gameBigTypeNick = map[string]string{
	"chain":        "哈希电子",
	"dianzi":       "电子游戏",
	"haxiusdt":     "哈希游戏(U)",
	"haxitrx":      "哈希游戏(T)",
	"live":         "真人视讯",
	"lottery":      "哈希彩票",
	"qipai":        "棋牌游戏",
	"sport":        "体育竞技",
	"texas":        "德州游戏",
	"lowLottery":   "低频彩",
	"cryptoMarket": "加密市场",
}

func GameBigTypeNick(t string) string {
	if nick, ok := gameBigTypeNick[t]; ok {
		return nick
	}
	return ""
}

// 币种对应网络协议
var SymbolToChain = map[string]string{
	"tron": "TRC20",
	"eth":  "ERC20",
	"btc":  "Bitcoin",
	"ltc":  "Litecoin",
	"bsc":  "BSC",
	"bch":  "BitcoinCash",
	"sol":  "Solana",
	"doge": "Dogecoin",
	"xrp":  "Ripple",
	"sui":  "SUI",
	"ton":  "Ton",
}

// 根据币种获取网络协议
func GetSymbolToChain(t string) string {
	if chain, ok := SymbolToChain[t]; ok {
		return chain
	}
	return ""
}

// 区分上庄类型
const (
	RechargeType              = 21
	RechargeShangZhuangType   = 22
	WithdrawType              = 31
	WithdrawShangZhuangType   = 32
	AmountChangeLogType       = 1
	AmountChangeLogZhuangType = 2
)

var BettingToChain = map[int]string{
	1: "波场链",
	2: "以太链",
	3: "币安链",
}

func GetBettingToChain(t int) string {
	if chain, ok := BettingToChain[t]; ok {
		return chain
	}
	return ""
}

const (
	TopAgentDefaultPassword  = "Abc123456"
	TopAgentPromotionHost    = "https://%v/#/?AgentCode=%v"
	TopAgentAdminUserAccount = "hash%v_%v"
)

// 充值要求枚举常量
const (
	KRequiresDepositNone     = iota // 无需充值
	KRequiresDepositRecharge        // 充值
	KRequiresDepositTransfer        // 转账
	KRequiresDepositBoth            // 充值或转账
)
