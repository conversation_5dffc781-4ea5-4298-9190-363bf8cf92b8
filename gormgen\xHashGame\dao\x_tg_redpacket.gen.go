// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgRedpacket(db *gorm.DB, opts ...gen.DOOption) xTgRedpacket {
	_xTgRedpacket := xTgRedpacket{}

	_xTgRedpacket.xTgRedpacketDo.UseDB(db, opts...)
	_xTgRedpacket.xTgRedpacketDo.UseModel(&model.XTgRedpacket{})

	tableName := _xTgRedpacket.xTgRedpacketDo.TableName()
	_xTgRedpacket.ALL = field.NewAsterisk(tableName)
	_xTgRedpacket.ID = field.NewInt32(tableName, "Id")
	_xTgRedpacket.TgID = field.NewInt32(tableName, "TgId")
	_xTgRedpacket.Amount = field.NewFloat64(tableName, "Amount")
	_xTgRedpacket.Balance = field.NewFloat64(tableName, "Balance")
	_xTgRedpacket.Number = field.NewInt32(tableName, "Number")
	_xTgRedpacket.Count = field.NewInt32(tableName, "Count")
	_xTgRedpacket.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgRedpacket.UpdateTIme = field.NewTime(tableName, "UpdateTIme")

	_xTgRedpacket.fillFieldMap()

	return _xTgRedpacket
}

type xTgRedpacket struct {
	xTgRedpacketDo xTgRedpacketDo

	ALL        field.Asterisk
	ID         field.Int32
	TgID       field.Int32 // 后台TgID
	Amount     field.Float64
	Balance    field.Float64
	Number     field.Int32
	Count      field.Int32
	CreateTime field.Time
	UpdateTIme field.Time

	fieldMap map[string]field.Expr
}

func (x xTgRedpacket) Table(newTableName string) *xTgRedpacket {
	x.xTgRedpacketDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRedpacket) As(alias string) *xTgRedpacket {
	x.xTgRedpacketDo.DO = *(x.xTgRedpacketDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRedpacket) updateTableName(table string) *xTgRedpacket {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.TgID = field.NewInt32(table, "TgId")
	x.Amount = field.NewFloat64(table, "Amount")
	x.Balance = field.NewFloat64(table, "Balance")
	x.Number = field.NewInt32(table, "Number")
	x.Count = field.NewInt32(table, "Count")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTIme = field.NewTime(table, "UpdateTIme")

	x.fillFieldMap()

	return x
}

func (x *xTgRedpacket) WithContext(ctx context.Context) *xTgRedpacketDo {
	return x.xTgRedpacketDo.WithContext(ctx)
}

func (x xTgRedpacket) TableName() string { return x.xTgRedpacketDo.TableName() }

func (x xTgRedpacket) Alias() string { return x.xTgRedpacketDo.Alias() }

func (x xTgRedpacket) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRedpacketDo.Columns(cols...)
}

func (x *xTgRedpacket) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRedpacket) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["TgId"] = x.TgID
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["Balance"] = x.Balance
	x.fieldMap["Number"] = x.Number
	x.fieldMap["Count"] = x.Count
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTIme"] = x.UpdateTIme
}

func (x xTgRedpacket) clone(db *gorm.DB) xTgRedpacket {
	x.xTgRedpacketDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRedpacket) replaceDB(db *gorm.DB) xTgRedpacket {
	x.xTgRedpacketDo.ReplaceDB(db)
	return x
}

type xTgRedpacketDo struct{ gen.DO }

func (x xTgRedpacketDo) Debug() *xTgRedpacketDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRedpacketDo) WithContext(ctx context.Context) *xTgRedpacketDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRedpacketDo) ReadDB() *xTgRedpacketDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRedpacketDo) WriteDB() *xTgRedpacketDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRedpacketDo) Session(config *gorm.Session) *xTgRedpacketDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRedpacketDo) Clauses(conds ...clause.Expression) *xTgRedpacketDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRedpacketDo) Returning(value interface{}, columns ...string) *xTgRedpacketDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRedpacketDo) Not(conds ...gen.Condition) *xTgRedpacketDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRedpacketDo) Or(conds ...gen.Condition) *xTgRedpacketDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRedpacketDo) Select(conds ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRedpacketDo) Where(conds ...gen.Condition) *xTgRedpacketDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRedpacketDo) Order(conds ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRedpacketDo) Distinct(cols ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRedpacketDo) Omit(cols ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRedpacketDo) Join(table schema.Tabler, on ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRedpacketDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRedpacketDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRedpacketDo) Group(cols ...field.Expr) *xTgRedpacketDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRedpacketDo) Having(conds ...gen.Condition) *xTgRedpacketDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRedpacketDo) Limit(limit int) *xTgRedpacketDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRedpacketDo) Offset(offset int) *xTgRedpacketDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRedpacketDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRedpacketDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRedpacketDo) Unscoped() *xTgRedpacketDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRedpacketDo) Create(values ...*model.XTgRedpacket) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRedpacketDo) CreateInBatches(values []*model.XTgRedpacket, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRedpacketDo) Save(values ...*model.XTgRedpacket) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRedpacketDo) First() (*model.XTgRedpacket, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacket), nil
	}
}

func (x xTgRedpacketDo) Take() (*model.XTgRedpacket, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacket), nil
	}
}

func (x xTgRedpacketDo) Last() (*model.XTgRedpacket, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacket), nil
	}
}

func (x xTgRedpacketDo) Find() ([]*model.XTgRedpacket, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRedpacket), err
}

func (x xTgRedpacketDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRedpacket, err error) {
	buf := make([]*model.XTgRedpacket, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRedpacketDo) FindInBatches(result *[]*model.XTgRedpacket, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRedpacketDo) Attrs(attrs ...field.AssignExpr) *xTgRedpacketDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRedpacketDo) Assign(attrs ...field.AssignExpr) *xTgRedpacketDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRedpacketDo) Joins(fields ...field.RelationField) *xTgRedpacketDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRedpacketDo) Preload(fields ...field.RelationField) *xTgRedpacketDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRedpacketDo) FirstOrInit() (*model.XTgRedpacket, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacket), nil
	}
}

func (x xTgRedpacketDo) FirstOrCreate() (*model.XTgRedpacket, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacket), nil
	}
}

func (x xTgRedpacketDo) FindByPage(offset int, limit int) (result []*model.XTgRedpacket, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRedpacketDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRedpacketDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRedpacketDo) Delete(models ...*model.XTgRedpacket) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRedpacketDo) withDO(do gen.Dao) *xTgRedpacketDo {
	x.DO = *do.(*gen.DO)
	return x
}
