// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXSellerInputDataDate(db *gorm.DB, opts ...gen.DOOption) xSellerInputDataDate {
	_xSellerInputDataDate := xSellerInputDataDate{}

	_xSellerInputDataDate.xSellerInputDataDateDo.UseDB(db, opts...)
	_xSellerInputDataDate.xSellerInputDataDateDo.UseModel(&model.XSellerInputDataDate{})

	tableName := _xSellerInputDataDate.xSellerInputDataDateDo.TableName()
	_xSellerInputDataDate.ALL = field.NewAsterisk(tableName)
	_xSellerInputDataDate.RecordDate = field.NewTime(tableName, "RecordDate")
	_xSellerInputDataDate.SellerID = field.NewInt32(tableName, "SellerId")
	_xSellerInputDataDate.AskServiceUsers = field.NewInt32(tableName, "AskServiceUsers")
	_xSellerInputDataDate.BackVisitUsers = field.NewInt32(tableName, "BackVisitUsers")
	_xSellerInputDataDate.LeadRechargeUsers = field.NewInt32(tableName, "LeadRechargeUsers")
	_xSellerInputDataDate.RechargeAmount = field.NewFloat64(tableName, "RechargeAmount")
	_xSellerInputDataDate.BackRewardAmount = field.NewFloat64(tableName, "BackRewardAmount")
	_xSellerInputDataDate.WithdrawUsers = field.NewInt32(tableName, "WithdrawUsers")
	_xSellerInputDataDate.WithdrawAmount = field.NewFloat64(tableName, "WithdrawAmount")
	_xSellerInputDataDate.CTDiff = field.NewFloat64(tableName, "CTDiff")
	_xSellerInputDataDate.CreateTime = field.NewTime(tableName, "CreateTime")
	_xSellerInputDataDate.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xSellerInputDataDate.fillFieldMap()

	return _xSellerInputDataDate
}

// xSellerInputDataDate 运营商手动录入数据(按日期)
type xSellerInputDataDate struct {
	xSellerInputDataDateDo xSellerInputDataDateDo

	ALL               field.Asterisk
	RecordDate        field.Time
	SellerID          field.Int32
	AskServiceUsers   field.Int32   // 咨询客服人数
	BackVisitUsers    field.Int32   // 回访人数
	LeadRechargeUsers field.Int32   // 引导充值人数
	RechargeAmount    field.Float64 // 充值金额U
	BackRewardAmount  field.Float64 // 召回彩金U
	WithdrawUsers     field.Int32   // 提款人数
	WithdrawAmount    field.Float64 // 提款金额
	CTDiff            field.Float64 // 充提差
	CreateTime        field.Time    // 创建时间
	UpdateTime        field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xSellerInputDataDate) Table(newTableName string) *xSellerInputDataDate {
	x.xSellerInputDataDateDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xSellerInputDataDate) As(alias string) *xSellerInputDataDate {
	x.xSellerInputDataDateDo.DO = *(x.xSellerInputDataDateDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xSellerInputDataDate) updateTableName(table string) *xSellerInputDataDate {
	x.ALL = field.NewAsterisk(table)
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.AskServiceUsers = field.NewInt32(table, "AskServiceUsers")
	x.BackVisitUsers = field.NewInt32(table, "BackVisitUsers")
	x.LeadRechargeUsers = field.NewInt32(table, "LeadRechargeUsers")
	x.RechargeAmount = field.NewFloat64(table, "RechargeAmount")
	x.BackRewardAmount = field.NewFloat64(table, "BackRewardAmount")
	x.WithdrawUsers = field.NewInt32(table, "WithdrawUsers")
	x.WithdrawAmount = field.NewFloat64(table, "WithdrawAmount")
	x.CTDiff = field.NewFloat64(table, "CTDiff")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xSellerInputDataDate) WithContext(ctx context.Context) *xSellerInputDataDateDo {
	return x.xSellerInputDataDateDo.WithContext(ctx)
}

func (x xSellerInputDataDate) TableName() string { return x.xSellerInputDataDateDo.TableName() }

func (x xSellerInputDataDate) Alias() string { return x.xSellerInputDataDateDo.Alias() }

func (x xSellerInputDataDate) Columns(cols ...field.Expr) gen.Columns {
	return x.xSellerInputDataDateDo.Columns(cols...)
}

func (x *xSellerInputDataDate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xSellerInputDataDate) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["AskServiceUsers"] = x.AskServiceUsers
	x.fieldMap["BackVisitUsers"] = x.BackVisitUsers
	x.fieldMap["LeadRechargeUsers"] = x.LeadRechargeUsers
	x.fieldMap["RechargeAmount"] = x.RechargeAmount
	x.fieldMap["BackRewardAmount"] = x.BackRewardAmount
	x.fieldMap["WithdrawUsers"] = x.WithdrawUsers
	x.fieldMap["WithdrawAmount"] = x.WithdrawAmount
	x.fieldMap["CTDiff"] = x.CTDiff
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xSellerInputDataDate) clone(db *gorm.DB) xSellerInputDataDate {
	x.xSellerInputDataDateDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xSellerInputDataDate) replaceDB(db *gorm.DB) xSellerInputDataDate {
	x.xSellerInputDataDateDo.ReplaceDB(db)
	return x
}

type xSellerInputDataDateDo struct{ gen.DO }

func (x xSellerInputDataDateDo) Debug() *xSellerInputDataDateDo {
	return x.withDO(x.DO.Debug())
}

func (x xSellerInputDataDateDo) WithContext(ctx context.Context) *xSellerInputDataDateDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xSellerInputDataDateDo) ReadDB() *xSellerInputDataDateDo {
	return x.Clauses(dbresolver.Read)
}

func (x xSellerInputDataDateDo) WriteDB() *xSellerInputDataDateDo {
	return x.Clauses(dbresolver.Write)
}

func (x xSellerInputDataDateDo) Session(config *gorm.Session) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Session(config))
}

func (x xSellerInputDataDateDo) Clauses(conds ...clause.Expression) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xSellerInputDataDateDo) Returning(value interface{}, columns ...string) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xSellerInputDataDateDo) Not(conds ...gen.Condition) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xSellerInputDataDateDo) Or(conds ...gen.Condition) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xSellerInputDataDateDo) Select(conds ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xSellerInputDataDateDo) Where(conds ...gen.Condition) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xSellerInputDataDateDo) Order(conds ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xSellerInputDataDateDo) Distinct(cols ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xSellerInputDataDateDo) Omit(cols ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xSellerInputDataDateDo) Join(table schema.Tabler, on ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xSellerInputDataDateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xSellerInputDataDateDo) RightJoin(table schema.Tabler, on ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xSellerInputDataDateDo) Group(cols ...field.Expr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xSellerInputDataDateDo) Having(conds ...gen.Condition) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xSellerInputDataDateDo) Limit(limit int) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xSellerInputDataDateDo) Offset(offset int) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xSellerInputDataDateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xSellerInputDataDateDo) Unscoped() *xSellerInputDataDateDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xSellerInputDataDateDo) Create(values ...*model.XSellerInputDataDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xSellerInputDataDateDo) CreateInBatches(values []*model.XSellerInputDataDate, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xSellerInputDataDateDo) Save(values ...*model.XSellerInputDataDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xSellerInputDataDateDo) First() (*model.XSellerInputDataDate, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSellerInputDataDate), nil
	}
}

func (x xSellerInputDataDateDo) Take() (*model.XSellerInputDataDate, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSellerInputDataDate), nil
	}
}

func (x xSellerInputDataDateDo) Last() (*model.XSellerInputDataDate, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSellerInputDataDate), nil
	}
}

func (x xSellerInputDataDateDo) Find() ([]*model.XSellerInputDataDate, error) {
	result, err := x.DO.Find()
	return result.([]*model.XSellerInputDataDate), err
}

func (x xSellerInputDataDateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XSellerInputDataDate, err error) {
	buf := make([]*model.XSellerInputDataDate, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xSellerInputDataDateDo) FindInBatches(result *[]*model.XSellerInputDataDate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xSellerInputDataDateDo) Attrs(attrs ...field.AssignExpr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xSellerInputDataDateDo) Assign(attrs ...field.AssignExpr) *xSellerInputDataDateDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xSellerInputDataDateDo) Joins(fields ...field.RelationField) *xSellerInputDataDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xSellerInputDataDateDo) Preload(fields ...field.RelationField) *xSellerInputDataDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xSellerInputDataDateDo) FirstOrInit() (*model.XSellerInputDataDate, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSellerInputDataDate), nil
	}
}

func (x xSellerInputDataDateDo) FirstOrCreate() (*model.XSellerInputDataDate, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSellerInputDataDate), nil
	}
}

func (x xSellerInputDataDateDo) FindByPage(offset int, limit int) (result []*model.XSellerInputDataDate, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xSellerInputDataDateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xSellerInputDataDateDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xSellerInputDataDateDo) Delete(models ...*model.XSellerInputDataDate) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xSellerInputDataDateDo) withDO(do gen.Dao) *xSellerInputDataDateDo {
	x.DO = *do.(*gen.DO)
	return x
}
