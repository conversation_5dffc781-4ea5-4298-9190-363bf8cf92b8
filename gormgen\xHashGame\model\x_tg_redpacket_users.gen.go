// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRedpacketUser = "x_tg_redpacket_users"

// XTgRedpacketUser mapped from table <x_tg_redpacket_users>
type XTgRedpacketUser struct {
	ID                  int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	TgID                int32     `gorm:"column:TgId;not null;comment:Tg后台ID" json:"TgId"`         // Tg后台ID
	TgUserID            int64     `gorm:"column:TgUserId;not null;comment:Tg用户ID" json:"TgUserId"` // Tg用户ID
	SellerID            int32     `gorm:"column:SellerId;not null;comment:运营商ID" json:"SellerId"`  // 运营商ID
	WithDrawCount       int32     `gorm:"column:WithDrawCount" json:"WithDrawCount"`
	GrabCount           int32     `gorm:"column:GrabCount" json:"GrabCount"`
	ArtificialGrabCount int32     `gorm:"column:ArtificialGrabCount;not null;comment:人工次数" json:"ArtificialGrabCount"` // 人工次数
	TgName              string    `gorm:"column:TgName" json:"TgName"`
	Balance             float64   `gorm:"column:Balance;not null;default:0.00" json:"Balance"`
	Withdraw            float64   `gorm:"column:Withdraw;not null;default:0.00;comment:提现金额" json:"Withdraw"` // 提现金额
	TgInviteUID         int64     `gorm:"column:TgInviteUid;comment:Tg邀请用户ID" json:"TgInviteUid"`             // Tg邀请用户ID
	OperatorID          int32     `gorm:"column:OperatorId;not null;comment:操作人id" json:"OperatorId"`         // 操作人id
	CreateTime          time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime          time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
}

// TableName XTgRedpacketUser's table name
func (*XTgRedpacketUser) TableName() string {
	return TableNameXTgRedpacketUser
}
