// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRedpacket = "x_tg_redpacket"

// XTgRedpacket mapped from table <x_tg_redpacket>
type XTgRedpacket struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	TgID       int32     `gorm:"column:TgId;not null;comment:后台TgID" json:"TgId"` // 后台TgID
	Amount     float64   `gorm:"column:Amount" json:"Amount"`
	Balance    float64   `gorm:"column:Balance;default:0.00" json:"Balance"`
	Number     int32     `gorm:"column:Number" json:"Number"`
	Count      int32     `gorm:"column:Count" json:"Count"`
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTIme time.Time `gorm:"column:UpdateTIme;default:CURRENT_TIMESTAMP" json:"UpdateTIme"`
}

// TableName XTgRedpacket's table name
func (*XTgRedpacket) TableName() string {
	return TableNameXTgRedpacket
}
