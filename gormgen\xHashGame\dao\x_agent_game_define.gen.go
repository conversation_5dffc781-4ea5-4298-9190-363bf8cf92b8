// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentGameDefine(db *gorm.DB, opts ...gen.DOOption) xAgentGameDefine {
	_xAgentGameDefine := xAgentGameDefine{}

	_xAgentGameDefine.xAgentGameDefineDo.UseDB(db, opts...)
	_xAgentGameDefine.xAgentGameDefineDo.UseModel(&model.XAgentGameDefine{})

	tableName := _xAgentGameDefine.xAgentGameDefineDo.TableName()
	_xAgentGameDefine.ALL = field.NewAsterisk(tableName)
	_xAgentGameDefine.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentGameDefine.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentGameDefine.AgentLevel = field.NewInt32(tableName, "AgentLevel")
	_xAgentGameDefine.Brand = field.NewString(tableName, "Brand")
	_xAgentGameDefine.GameID = field.NewString(tableName, "GameId")
	_xAgentGameDefine.CatID = field.NewInt32(tableName, "CatId")
	_xAgentGameDefine.LiuSui = field.NewFloat64(tableName, "LiuSui")
	_xAgentGameDefine.Reward = field.NewFloat64(tableName, "Reward")
	_xAgentGameDefine.Memo = field.NewString(tableName, "Memo")
	_xAgentGameDefine.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentGameDefine.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentGameDefine.fillFieldMap()

	return _xAgentGameDefine
}

type xAgentGameDefine struct {
	xAgentGameDefineDo xAgentGameDefineDo

	ALL        field.Asterisk
	SellerID   field.Int32
	ChannelID  field.Int32
	AgentLevel field.Int32  // 代理等级
	Brand      field.String // 品牌
	GameID     field.String // 游戏Id
	CatID      field.Int32  // 彩票大类ID
	LiuSui     field.Float64
	Reward     field.Float64
	Memo       field.String // 备注
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentGameDefine) Table(newTableName string) *xAgentGameDefine {
	x.xAgentGameDefineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentGameDefine) As(alias string) *xAgentGameDefine {
	x.xAgentGameDefineDo.DO = *(x.xAgentGameDefineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentGameDefine) updateTableName(table string) *xAgentGameDefine {
	x.ALL = field.NewAsterisk(table)
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.AgentLevel = field.NewInt32(table, "AgentLevel")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.CatID = field.NewInt32(table, "CatId")
	x.LiuSui = field.NewFloat64(table, "LiuSui")
	x.Reward = field.NewFloat64(table, "Reward")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentGameDefine) WithContext(ctx context.Context) *xAgentGameDefineDo {
	return x.xAgentGameDefineDo.WithContext(ctx)
}

func (x xAgentGameDefine) TableName() string { return x.xAgentGameDefineDo.TableName() }

func (x xAgentGameDefine) Alias() string { return x.xAgentGameDefineDo.Alias() }

func (x xAgentGameDefine) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentGameDefineDo.Columns(cols...)
}

func (x *xAgentGameDefine) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentGameDefine) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["AgentLevel"] = x.AgentLevel
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["CatId"] = x.CatID
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["Reward"] = x.Reward
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentGameDefine) clone(db *gorm.DB) xAgentGameDefine {
	x.xAgentGameDefineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentGameDefine) replaceDB(db *gorm.DB) xAgentGameDefine {
	x.xAgentGameDefineDo.ReplaceDB(db)
	return x
}

type xAgentGameDefineDo struct{ gen.DO }

func (x xAgentGameDefineDo) Debug() *xAgentGameDefineDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentGameDefineDo) WithContext(ctx context.Context) *xAgentGameDefineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentGameDefineDo) ReadDB() *xAgentGameDefineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentGameDefineDo) WriteDB() *xAgentGameDefineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentGameDefineDo) Session(config *gorm.Session) *xAgentGameDefineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentGameDefineDo) Clauses(conds ...clause.Expression) *xAgentGameDefineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentGameDefineDo) Returning(value interface{}, columns ...string) *xAgentGameDefineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentGameDefineDo) Not(conds ...gen.Condition) *xAgentGameDefineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentGameDefineDo) Or(conds ...gen.Condition) *xAgentGameDefineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentGameDefineDo) Select(conds ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentGameDefineDo) Where(conds ...gen.Condition) *xAgentGameDefineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentGameDefineDo) Order(conds ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentGameDefineDo) Distinct(cols ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentGameDefineDo) Omit(cols ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentGameDefineDo) Join(table schema.Tabler, on ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentGameDefineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentGameDefineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentGameDefineDo) Group(cols ...field.Expr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentGameDefineDo) Having(conds ...gen.Condition) *xAgentGameDefineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentGameDefineDo) Limit(limit int) *xAgentGameDefineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentGameDefineDo) Offset(offset int) *xAgentGameDefineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentGameDefineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentGameDefineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentGameDefineDo) Unscoped() *xAgentGameDefineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentGameDefineDo) Create(values ...*model.XAgentGameDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentGameDefineDo) CreateInBatches(values []*model.XAgentGameDefine, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentGameDefineDo) Save(values ...*model.XAgentGameDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentGameDefineDo) First() (*model.XAgentGameDefine, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDefine), nil
	}
}

func (x xAgentGameDefineDo) Take() (*model.XAgentGameDefine, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDefine), nil
	}
}

func (x xAgentGameDefineDo) Last() (*model.XAgentGameDefine, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDefine), nil
	}
}

func (x xAgentGameDefineDo) Find() ([]*model.XAgentGameDefine, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentGameDefine), err
}

func (x xAgentGameDefineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentGameDefine, err error) {
	buf := make([]*model.XAgentGameDefine, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentGameDefineDo) FindInBatches(result *[]*model.XAgentGameDefine, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentGameDefineDo) Attrs(attrs ...field.AssignExpr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentGameDefineDo) Assign(attrs ...field.AssignExpr) *xAgentGameDefineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentGameDefineDo) Joins(fields ...field.RelationField) *xAgentGameDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentGameDefineDo) Preload(fields ...field.RelationField) *xAgentGameDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentGameDefineDo) FirstOrInit() (*model.XAgentGameDefine, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDefine), nil
	}
}

func (x xAgentGameDefineDo) FirstOrCreate() (*model.XAgentGameDefine, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDefine), nil
	}
}

func (x xAgentGameDefineDo) FindByPage(offset int, limit int) (result []*model.XAgentGameDefine, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentGameDefineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentGameDefineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentGameDefineDo) Delete(models ...*model.XAgentGameDefine) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentGameDefineDo) withDO(do gen.Dao) *xAgentGameDefineDo {
	x.DO = *do.(*gen.DO)
	return x
}
