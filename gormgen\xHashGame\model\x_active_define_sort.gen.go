// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXActiveDefineSort = "x_active_define_sort"

// XActiveDefineSort mapped from table <x_active_define_sort>
type XActiveDefineSort struct {
	ID         int32     `gorm:"column:Id;primaryKey;comment:x_active_define.id" json:"Id"` // x_active_define.id
	Lang       int32     `gorm:"column:Lang;primaryKey;comment:x_lang_list.id" json:"Lang"` // x_lang_list.id
	Sort       int32     `gorm:"column:Sort;not null" json:"Sort"`
	TopSort    int32     `gorm:"column:TopSort;not null" json:"TopSort"`
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
}

// TableName XActiveDefineSort's table name
func (*XActiveDefineSort) TableName() string {
	return TableNameXActiveDefineSort
}
