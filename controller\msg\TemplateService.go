package msg

import (
	"fmt"
	"xserver/abugo"
	"xserver/controller/msg/model"
	"xserver/server"
)

// TemplateService 模板管理接口
type TemplateService interface {
	// 创建模板
	CreateTemplate(template *model.StationMessageTemplate) error
	// 更新模板状态（启用/停用）
	UpdateTemplateStatus(templateId int64, status int) error
	// 获取系统消息模板类型存在情况
	GetSystemTemplateTypes() ([]map[string]interface{}, error)
	// 根据ID获取模板及其内容
	GetTemplateById(templateId int64) (*model.StationMessageTemplate, error)
}

// templateServiceImpl 模板服务实现
type templateServiceImpl struct {
	db *abugo.AbuDb
}

// CreateTemplate 创建模板
func (s *templateServiceImpl) CreateTemplate(template *model.StationMessageTemplate) error {
	result := server.Db().GormDao().Create(template)
	if result.Error != nil {
		return fmt.Errorf("创建模板: %w", result.Error)
	}
	return nil
}

// UpdateTemplateStatus 更新模板状态（启用/停用）
func (s *templateServiceImpl) UpdateTemplateStatus(templateId int64, status int) error {
	if status != 0 && status != 1 {
		return fmt.Errorf("无效的状态值，状态必须为0(停用)或1(启用)")
	}

	result := server.Db().GormDao().Model(&model.StationMessageTemplate{}).
		Where("Id = ?", templateId).
		Updates(map[string]interface{}{
			"Status": status,
		})

	if result.Error != nil {
		return fmt.Errorf("更新模板状态: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("模板不存在")
	}

	return nil
}

// GetSystemTemplateTypes 获取系统消息模板类型存在情况
func (s *templateServiceImpl) GetSystemTemplateTypes() ([]map[string]interface{}, error) {
	// 使用辅助函数获取所有模板类型及其名称
	typeNames := GetSystemTemplateTypeNames()

	// 查询所有启用的系统消息模板
	var templates []struct {
		ID   int64  `gorm:"column:Id"`
		Type string `gorm:"column:Type"`
	}

	result := server.Db().GormDao().Model(&model.StationMessageTemplate{}).
		Select("Id, Type").
		Where("PushType = 0").
		Order("Id ASC"). // 添加按Id升序排序
		Find(&templates) // AND Status = 1

	if result.Error != nil {
		return nil, fmt.Errorf("查询系统消息模板: %w", result.Error)
	}

	// 构建模板类型映射，记录每种类型的模板ID
	templateMap := make(map[string]int64)
	for _, template := range templates {
		templateMap[template.Type] = template.ID
	}

	// 构建结果数组，按照typeNames的顺序
	var results []map[string]interface{}
	for typeCode, typeName := range typeNames {
		item := map[string]interface{}{
			"Type":     typeCode,
			"TypeName": typeName,
			"Exists":   false,
			"Id":       int64(0),
		}

		if id, exists := templateMap[typeCode]; exists {
			item["Exists"] = true
			item["Id"] = id
		}

		results = append(results, item)
	}

	return results, nil
}

// GetTemplateById 根据ID获取模板及其内容
func (s *templateServiceImpl) GetTemplateById(templateId int64) (*model.StationMessageTemplate, error) {
	// 查询模板基本信息
	template := &model.StationMessageTemplate{}
	result := server.Db().GormDao().Where("Id = ?", templateId).First(template)
	if result.Error != nil {
		return nil, fmt.Errorf("获取模板信息: %w", result.Error)
	}
	//
	//// 查询模板内容
	//var contents []model.TemplateContent
	//result = server.Db().GormDao().Where("TemplateId = ?", templateId).Find(&contents)
	//if result.Error != nil {
	//	return nil, fmt.Errorf("获取模板内容: %w", result.Error)
	//}
	//
	//// 将内容关联到模板
	//template.Contents = contents

	return template, nil
}

// NewTemplateService 创建模板服务实例
func NewTemplateService() TemplateService {
	return &templateServiceImpl{
		db: server.Db(),
	}
}
