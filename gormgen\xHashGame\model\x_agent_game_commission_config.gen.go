// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentGameCommissionConfig = "x_agent_game_commission_config"

// XAgentGameCommissionConfig mapped from table <x_agent_game_commission_config>
type XAgentGameCommissionConfig struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"`                        // id
	Brand      string    `gorm:"column:Brand;primaryKey;comment:品牌" json:"Brand"`                                     // 品牌
	GameID     string    `gorm:"column:GameId;primaryKey;comment:游戏Id" json:"GameId"`                                 // 游戏Id
	CatID      int32     `gorm:"column:CatId;primaryKey;comment:彩票大类ID" json:"CatId"`                                 // 彩票大类ID
	RewardRate float64   `gorm:"column:RewardRate;default:0.000000;comment:返佣比例" json:"RewardRate"`                   // 返佣比例
	Memo       string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XAgentGameCommissionConfig's table name
func (*XAgentGameCommissionConfig) TableName() string {
	return TableNameXAgentGameCommissionConfig
}
