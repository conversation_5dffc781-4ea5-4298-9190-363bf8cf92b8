// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsUserProfile(db *gorm.DB, opts ...gen.DOOption) xAdsUserProfile {
	_xAdsUserProfile := xAdsUserProfile{}

	_xAdsUserProfile.xAdsUserProfileDo.UseDB(db, opts...)
	_xAdsUserProfile.xAdsUserProfileDo.UseModel(&model.XAdsUserProfile{})

	tableName := _xAdsUserProfile.xAdsUserProfileDo.TableName()
	_xAdsUserProfile.ALL = field.NewAsterisk(tableName)
	_xAdsUserProfile.UserID = field.NewInt64(tableName, "user_id")
	_xAdsUserProfile.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsUserProfile.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsUserProfile.TgChatID = field.NewInt64(tableName, "tg_chat_id")
	_xAdsUserProfile.Account = field.NewString(tableName, "account")
	_xAdsUserProfile.AccountType = field.NewInt32(tableName, "account_type")
	_xAdsUserProfile.UserBetTypes = field.NewInt32(tableName, "user_bet_types")
	_xAdsUserProfile.IsAgent = field.NewInt32(tableName, "is_agent")
	_xAdsUserProfile.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsUserProfile.Amount = field.NewFloat64(tableName, "amount")
	_xAdsUserProfile.BonusAmount = field.NewFloat64(tableName, "bonus_amount")
	_xAdsUserProfile.BetCount = field.NewInt32(tableName, "bet_count")
	_xAdsUserProfile.MaxStreakBet = field.NewInt32(tableName, "max_streak_bet")
	_xAdsUserProfile.RegisterTime = field.NewTime(tableName, "register_time")
	_xAdsUserProfile.FirstBetTime = field.NewTime(tableName, "first_bet_time")
	_xAdsUserProfile.LastBetTime = field.NewTime(tableName, "last_bet_time")
	_xAdsUserProfile.FirstRechargeTime = field.NewTime(tableName, "first_recharge_time")
	_xAdsUserProfile.LastRechargeTime = field.NewTime(tableName, "last_recharge_time")
	_xAdsUserProfile.RechargeMaxStreakBetCnt = field.NewInt32(tableName, "recharge_max_streak_bet_cnt")
	_xAdsUserProfile.RechargeMaxStreakNotBetCnt = field.NewInt32(tableName, "recharge_max_streak_not_bet_cnt")
	_xAdsUserProfile.FirstWithdrawFinishTime = field.NewTime(tableName, "first_withdraw_finish_time")
	_xAdsUserProfile.LastWithdrawFinishTime = field.NewTime(tableName, "last_withdraw_finish_time")
	_xAdsUserProfile.WithdrawSuccessCount = field.NewInt32(tableName, "withdraw_success_count")
	_xAdsUserProfile.LastRechargeSinceDays = field.NewInt32(tableName, "last_recharge_since_days")
	_xAdsUserProfile.IsInResourceDb = field.NewInt32(tableName, "is_in_resource_db")
	_xAdsUserProfile.IsUseRobot = field.NewInt32(tableName, "is_use_robot")
	_xAdsUserProfile.AllBet = field.NewFloat64(tableName, "all_bet")
	_xAdsUserProfile.AllWin = field.NewFloat64(tableName, "all_win")
	_xAdsUserProfile.AllWinLoss = field.NewFloat64(tableName, "all_win_loss")
	_xAdsUserProfile.UsdtWinLoss = field.NewFloat64(tableName, "usdt_win_loss")
	_xAdsUserProfile.TrxWinLoss = field.NewFloat64(tableName, "trx_win_loss")
	_xAdsUserProfile.GiftUsdtStat = field.NewInt32(tableName, "gift_usdt_stat")
	_xAdsUserProfile.GiftTrxStat = field.NewInt32(tableName, "gift_trx_stat")
	_xAdsUserProfile.RechargeAmount = field.NewFloat64(tableName, "recharge_amount")
	_xAdsUserProfile.RechargeCount = field.NewInt32(tableName, "recharge_count")
	_xAdsUserProfile.WithdrawCount = field.NewInt32(tableName, "withdraw_count")
	_xAdsUserProfile.WithdrawAmount = field.NewFloat64(tableName, "withdraw_amount")
	_xAdsUserProfile.FirstRechargeAmount = field.NewFloat64(tableName, "first_recharge_amount")
	_xAdsUserProfile.FirstRechargeTime1 = field.NewTime(tableName, "first_recharge_time1")
	_xAdsUserProfile.HashBalanceBetCount = field.NewInt32(tableName, "hash_balance_bet_count")
	_xAdsUserProfile.HashBalanceWinCount = field.NewInt32(tableName, "hash_balance_win_count")
	_xAdsUserProfile.HashBalanceBetAmount = field.NewFloat64(tableName, "hash_balance_bet_amount")
	_xAdsUserProfile.HashBalanceWinAmount = field.NewFloat64(tableName, "hash_balance_win_amount")
	_xAdsUserProfile.HashBalanceValidBetAmount = field.NewFloat64(tableName, "hash_balance_valid_bet_amount")
	_xAdsUserProfile.HashBalanceFee = field.NewFloat64(tableName, "hash_balance_fee")
	_xAdsUserProfile.HashTransferUsdtWinLoss = field.NewFloat64(tableName, "hash_transfer_usdt_win_loss")
	_xAdsUserProfile.HashTransferTrxWinLoss = field.NewFloat64(tableName, "hash_transfer_trx_win_loss")
	_xAdsUserProfile.HashTransferUsdtBetCount = field.NewInt32(tableName, "hash_transfer_usdt_bet_count")
	_xAdsUserProfile.HashTransferTrxBetCount = field.NewInt32(tableName, "hash_transfer_trx_bet_count")
	_xAdsUserProfile.HashTransferUsdtWinCount = field.NewInt32(tableName, "hash_transfer_usdt_win_count")
	_xAdsUserProfile.HashTransferTrxWinCount = field.NewInt32(tableName, "hash_transfer_trx_win_count")
	_xAdsUserProfile.HashTransferUsdtBetAmount = field.NewFloat64(tableName, "hash_transfer_usdt_bet_amount")
	_xAdsUserProfile.HashTransferTrxBetAmountU = field.NewFloat64(tableName, "hash_transfer_trx_bet_amount_u")
	_xAdsUserProfile.HashTransferTrxBetAmount = field.NewFloat64(tableName, "hash_transfer_trx_bet_amount")
	_xAdsUserProfile.HashTransferTrxWinAmountU = field.NewFloat64(tableName, "hash_transfer_trx_win_amount_u")
	_xAdsUserProfile.HashTransferUsdtWinAmount = field.NewFloat64(tableName, "hash_transfer_usdt_win_amount")
	_xAdsUserProfile.HashTransferTrxWinAmount = field.NewFloat64(tableName, "hash_transfer_trx_win_amount")
	_xAdsUserProfile.HashTransferUsdtValidBetAmount = field.NewFloat64(tableName, "hash_transfer_usdt_valid_bet_amount")
	_xAdsUserProfile.HashTransferTrxValidBetAmount = field.NewFloat64(tableName, "hash_transfer_trx_valid_bet_amount")
	_xAdsUserProfile.HashTransferUsdtFee = field.NewFloat64(tableName, "hash_transfer_usdt_fee")
	_xAdsUserProfile.HashTransferTrxFee = field.NewFloat64(tableName, "hash_transfer_trx_fee")
	_xAdsUserProfile.ThirdElecBetCount = field.NewInt32(tableName, "third_elec_bet_count")
	_xAdsUserProfile.ThirdElecWinCount = field.NewInt32(tableName, "third_elec_win_count")
	_xAdsUserProfile.ThirdElecBetAmount = field.NewFloat64(tableName, "third_elec_bet_amount")
	_xAdsUserProfile.ThirdElecWinAmount = field.NewFloat64(tableName, "third_elec_win_amount")
	_xAdsUserProfile.ThirdElecValidBetAmount = field.NewFloat64(tableName, "third_elec_valid_bet_amount")
	_xAdsUserProfile.ThirdElecFee = field.NewFloat64(tableName, "third_elec_fee")
	_xAdsUserProfile.ThirdLotteryBetCount = field.NewInt32(tableName, "third_lottery_bet_count")
	_xAdsUserProfile.ThirdLotteryWinCount = field.NewInt32(tableName, "third_lottery_win_count")
	_xAdsUserProfile.ThirdLotteryBetAmount = field.NewFloat64(tableName, "third_lottery_bet_amount")
	_xAdsUserProfile.ThirdLotteryWinAmount = field.NewFloat64(tableName, "third_lottery_win_amount")
	_xAdsUserProfile.ThirdLotteryValidBetAmount = field.NewFloat64(tableName, "third_lottery_valid_bet_amount")
	_xAdsUserProfile.ThirdLotteryFee = field.NewFloat64(tableName, "third_lottery_fee")
	_xAdsUserProfile.ThirdChessBetCount = field.NewInt32(tableName, "third_chess_bet_count")
	_xAdsUserProfile.ThirdChessWinCount = field.NewInt32(tableName, "third_chess_win_count")
	_xAdsUserProfile.ThirdChessBetAmount = field.NewFloat64(tableName, "third_chess_bet_amount")
	_xAdsUserProfile.ThirdChessWinAmount = field.NewFloat64(tableName, "third_chess_win_amount")
	_xAdsUserProfile.ThirdChessValidBetAmount = field.NewFloat64(tableName, "third_chess_valid_bet_amount")
	_xAdsUserProfile.ThirdChessFee = field.NewFloat64(tableName, "third_chess_fee")
	_xAdsUserProfile.ThirdSmallBetCount = field.NewInt32(tableName, "third_small_bet_count")
	_xAdsUserProfile.ThirdSmallWinCount = field.NewInt32(tableName, "third_small_win_count")
	_xAdsUserProfile.ThirdSmallBetAmount = field.NewFloat64(tableName, "third_small_bet_amount")
	_xAdsUserProfile.ThirdSmallWinAmount = field.NewFloat64(tableName, "third_small_win_amount")
	_xAdsUserProfile.ThirdSmallValidBetAmount = field.NewFloat64(tableName, "third_small_valid_bet_amount")
	_xAdsUserProfile.ThirdSmallFee = field.NewFloat64(tableName, "third_small_fee")
	_xAdsUserProfile.ThirdLiveBetCount = field.NewInt32(tableName, "third_live_bet_count")
	_xAdsUserProfile.ThirdLiveWinCount = field.NewInt32(tableName, "third_live_win_count")
	_xAdsUserProfile.ThirdLiveBetAmount = field.NewFloat64(tableName, "third_live_bet_amount")
	_xAdsUserProfile.ThirdLiveWinAmount = field.NewFloat64(tableName, "third_live_win_amount")
	_xAdsUserProfile.ThirdLiveValidBetAmount = field.NewFloat64(tableName, "third_live_valid_bet_amount")
	_xAdsUserProfile.ThirdLiveFee = field.NewFloat64(tableName, "third_live_fee")
	_xAdsUserProfile.ThirdSportBetCount = field.NewInt32(tableName, "third_sport_bet_count")
	_xAdsUserProfile.ThirdSportWinCount = field.NewInt32(tableName, "third_sport_win_count")
	_xAdsUserProfile.ThirdSportBetAmount = field.NewFloat64(tableName, "third_sport_bet_amount")
	_xAdsUserProfile.ThirdSportWinAmount = field.NewFloat64(tableName, "third_sport_win_amount")
	_xAdsUserProfile.ThirdSportValidBetAmount = field.NewFloat64(tableName, "third_sport_valid_bet_amount")
	_xAdsUserProfile.ThirdSportFee = field.NewFloat64(tableName, "third_sport_fee")
	_xAdsUserProfile.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsUserProfile.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsUserProfile.fillFieldMap()

	return _xAdsUserProfile
}

// xAdsUserProfile 用户画像
type xAdsUserProfile struct {
	xAdsUserProfileDo xAdsUserProfileDo

	ALL                            field.Asterisk
	UserID                         field.Int64   // 用户ID
	SellerID                       field.Int32   // 运营商
	ChannelID                      field.Int32   // 渠道
	TgChatID                       field.Int64   // 用户飞机ID
	Account                        field.String  // 用户账号
	AccountType                    field.Int32   //  1:账号, 2:手机号, 3:Email, 4:Google, 5:Telegram
	UserBetTypes                   field.Int32   // 用户类型 0:未投注,1:综合,2:哈希,3.电子,4:彩票,5:棋牌,6:体育,7:真人
	IsAgent                        field.Int32   // 是否代理 1是,2不是
	TopAgentID                     field.Int64   // 顶级代理ID
	Amount                         field.Float64 // 用户游戏账户余额
	BonusAmount                    field.Float64 // 用户额外奖金余额
	BetCount                       field.Int32   // 用户投注次数
	MaxStreakBet                   field.Int32   // 用户最大连胜投注数
	RegisterTime                   field.Time    // 用户注册时间
	FirstBetTime                   field.Time    // 用户首次投注时间
	LastBetTime                    field.Time    // 用户最后投注时间
	FirstRechargeTime              field.Time    // 首次充值时间
	LastRechargeTime               field.Time    // 最后充值时间
	RechargeMaxStreakBetCnt        field.Int32   // 充值后连续多少天投注
	RechargeMaxStreakNotBetCnt     field.Int32   // 充值后连续多少天未投注
	FirstWithdrawFinishTime        field.Time    // 首次出款时间
	LastWithdrawFinishTime         field.Time    // 最后出款时间
	WithdrawSuccessCount           field.Int32   // 出款次数
	LastRechargeSinceDays          field.Int32   // 充值后多少天未复充
	IsInResourceDb                 field.Int32   // 是否在库(0:否 1:是)
	IsUseRobot                     field.Int32   // 是否使用机器人0：否 1是
	AllBet                         field.Float64 // 总投注
	AllWin                         field.Float64 // 玩家总赢
	AllWinLoss                     field.Float64 // 总输赢
	UsdtWinLoss                    field.Float64 // usdt输赢
	TrxWinLoss                     field.Float64 // trx输赢
	GiftUsdtStat                   field.Int32   // 用户体验金U  领取状态 0：没领取
	GiftTrxStat                    field.Int32   // 用户体验金Trx 领取状态 0：没领取
	RechargeAmount                 field.Float64 // 充值额
	RechargeCount                  field.Int32   // 充值次数
	WithdrawCount                  field.Int32   // 兑换次数
	WithdrawAmount                 field.Float64 // 兑换金额
	FirstRechargeAmount            field.Float64 // 首充金额
	FirstRechargeTime1             field.Time    // 首充时间
	HashBalanceBetCount            field.Int32   // 哈希余额游戏总投注次数
	HashBalanceWinCount            field.Int32   // 哈希余额游戏赢次数
	HashBalanceBetAmount           field.Float64 // 哈希余额游戏总投注额
	HashBalanceWinAmount           field.Float64 // 哈希余额游戏赢额
	HashBalanceValidBetAmount      field.Float64 // 哈希余额有效
	HashBalanceFee                 field.Float64 // 哈希余额手续费
	HashTransferUsdtWinLoss        field.Float64 // 转账u输赢
	HashTransferTrxWinLoss         field.Float64 // 转账trx输赢
	HashTransferUsdtBetCount       field.Int32   // 转账usdt次数
	HashTransferTrxBetCount        field.Int32   // 转账trx投注次数
	HashTransferUsdtWinCount       field.Int32   // 转账赢次数usdt
	HashTransferTrxWinCount        field.Int32   // 转账赢次数
	HashTransferUsdtBetAmount      field.Float64 // 转账总投注usdt
	HashTransferTrxBetAmountU      field.Float64 // trx转账总投注 算U单位
	HashTransferTrxBetAmount       field.Float64 // 转账总投注
	HashTransferTrxWinAmountU      field.Float64 // 转账赢 U单位
	HashTransferUsdtWinAmount      field.Float64 // 转账赢次数
	HashTransferTrxWinAmount       field.Float64 // 转账赢次数
	HashTransferUsdtValidBetAmount field.Float64 // 转账有效
	HashTransferTrxValidBetAmount  field.Float64 // 转账有效
	HashTransferUsdtFee            field.Float64 // 转账玩法手续费
	HashTransferTrxFee             field.Float64 // 转账玩法手续费
	ThirdElecBetCount              field.Int32   // 三方电子投注次数
	ThirdElecWinCount              field.Int32   // 三方电子赢次数
	ThirdElecBetAmount             field.Float64 // 三方电子投注
	ThirdElecWinAmount             field.Float64 // 三方电子赢额
	ThirdElecValidBetAmount        field.Float64 // 三方电子有效投注额
	ThirdElecFee                   field.Float64 // 三方电子手续费
	ThirdLotteryBetCount           field.Int32   // 三方彩票
	ThirdLotteryWinCount           field.Int32   // 三方彩票
	ThirdLotteryBetAmount          field.Float64 // 三方彩票
	ThirdLotteryWinAmount          field.Float64 // 三方彩票
	ThirdLotteryValidBetAmount     field.Float64 // 三方彩票
	ThirdLotteryFee                field.Float64 // 三方彩票手续费
	ThirdChessBetCount             field.Int32   // 棋牌
	ThirdChessWinCount             field.Int32   // 棋牌
	ThirdChessBetAmount            field.Float64 // 棋牌
	ThirdChessWinAmount            field.Float64 // 棋牌
	ThirdChessValidBetAmount       field.Float64 // 棋牌
	ThirdChessFee                  field.Float64 // 棋牌
	ThirdSmallBetCount             field.Int32   // 小游戏
	ThirdSmallWinCount             field.Int32   // 小游戏
	ThirdSmallBetAmount            field.Float64 // 小游戏
	ThirdSmallWinAmount            field.Float64 // 小游戏
	ThirdSmallValidBetAmount       field.Float64 // 小游戏
	ThirdSmallFee                  field.Float64 // 小游戏
	ThirdLiveBetCount              field.Int32   // 真人
	ThirdLiveWinCount              field.Int32   // 真人
	ThirdLiveBetAmount             field.Float64 // 真人
	ThirdLiveWinAmount             field.Float64 // 真人
	ThirdLiveValidBetAmount        field.Float64 // 真人
	ThirdLiveFee                   field.Float64 // 真人
	ThirdSportBetCount             field.Int32   // 体育
	ThirdSportWinCount             field.Int32   // 体育
	ThirdSportBetAmount            field.Float64 // 体育
	ThirdSportWinAmount            field.Float64 // 体育
	ThirdSportValidBetAmount       field.Float64 // 体育
	ThirdSportFee                  field.Float64 // 体育
	CreateTime                     field.Time    // 数据创建日期
	UpdateTime                     field.Time    // 数据更新日期

	fieldMap map[string]field.Expr
}

func (x xAdsUserProfile) Table(newTableName string) *xAdsUserProfile {
	x.xAdsUserProfileDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsUserProfile) As(alias string) *xAdsUserProfile {
	x.xAdsUserProfileDo.DO = *(x.xAdsUserProfileDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsUserProfile) updateTableName(table string) *xAdsUserProfile {
	x.ALL = field.NewAsterisk(table)
	x.UserID = field.NewInt64(table, "user_id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TgChatID = field.NewInt64(table, "tg_chat_id")
	x.Account = field.NewString(table, "account")
	x.AccountType = field.NewInt32(table, "account_type")
	x.UserBetTypes = field.NewInt32(table, "user_bet_types")
	x.IsAgent = field.NewInt32(table, "is_agent")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.Amount = field.NewFloat64(table, "amount")
	x.BonusAmount = field.NewFloat64(table, "bonus_amount")
	x.BetCount = field.NewInt32(table, "bet_count")
	x.MaxStreakBet = field.NewInt32(table, "max_streak_bet")
	x.RegisterTime = field.NewTime(table, "register_time")
	x.FirstBetTime = field.NewTime(table, "first_bet_time")
	x.LastBetTime = field.NewTime(table, "last_bet_time")
	x.FirstRechargeTime = field.NewTime(table, "first_recharge_time")
	x.LastRechargeTime = field.NewTime(table, "last_recharge_time")
	x.RechargeMaxStreakBetCnt = field.NewInt32(table, "recharge_max_streak_bet_cnt")
	x.RechargeMaxStreakNotBetCnt = field.NewInt32(table, "recharge_max_streak_not_bet_cnt")
	x.FirstWithdrawFinishTime = field.NewTime(table, "first_withdraw_finish_time")
	x.LastWithdrawFinishTime = field.NewTime(table, "last_withdraw_finish_time")
	x.WithdrawSuccessCount = field.NewInt32(table, "withdraw_success_count")
	x.LastRechargeSinceDays = field.NewInt32(table, "last_recharge_since_days")
	x.IsInResourceDb = field.NewInt32(table, "is_in_resource_db")
	x.IsUseRobot = field.NewInt32(table, "is_use_robot")
	x.AllBet = field.NewFloat64(table, "all_bet")
	x.AllWin = field.NewFloat64(table, "all_win")
	x.AllWinLoss = field.NewFloat64(table, "all_win_loss")
	x.UsdtWinLoss = field.NewFloat64(table, "usdt_win_loss")
	x.TrxWinLoss = field.NewFloat64(table, "trx_win_loss")
	x.GiftUsdtStat = field.NewInt32(table, "gift_usdt_stat")
	x.GiftTrxStat = field.NewInt32(table, "gift_trx_stat")
	x.RechargeAmount = field.NewFloat64(table, "recharge_amount")
	x.RechargeCount = field.NewInt32(table, "recharge_count")
	x.WithdrawCount = field.NewInt32(table, "withdraw_count")
	x.WithdrawAmount = field.NewFloat64(table, "withdraw_amount")
	x.FirstRechargeAmount = field.NewFloat64(table, "first_recharge_amount")
	x.FirstRechargeTime1 = field.NewTime(table, "first_recharge_time1")
	x.HashBalanceBetCount = field.NewInt32(table, "hash_balance_bet_count")
	x.HashBalanceWinCount = field.NewInt32(table, "hash_balance_win_count")
	x.HashBalanceBetAmount = field.NewFloat64(table, "hash_balance_bet_amount")
	x.HashBalanceWinAmount = field.NewFloat64(table, "hash_balance_win_amount")
	x.HashBalanceValidBetAmount = field.NewFloat64(table, "hash_balance_valid_bet_amount")
	x.HashBalanceFee = field.NewFloat64(table, "hash_balance_fee")
	x.HashTransferUsdtWinLoss = field.NewFloat64(table, "hash_transfer_usdt_win_loss")
	x.HashTransferTrxWinLoss = field.NewFloat64(table, "hash_transfer_trx_win_loss")
	x.HashTransferUsdtBetCount = field.NewInt32(table, "hash_transfer_usdt_bet_count")
	x.HashTransferTrxBetCount = field.NewInt32(table, "hash_transfer_trx_bet_count")
	x.HashTransferUsdtWinCount = field.NewInt32(table, "hash_transfer_usdt_win_count")
	x.HashTransferTrxWinCount = field.NewInt32(table, "hash_transfer_trx_win_count")
	x.HashTransferUsdtBetAmount = field.NewFloat64(table, "hash_transfer_usdt_bet_amount")
	x.HashTransferTrxBetAmountU = field.NewFloat64(table, "hash_transfer_trx_bet_amount_u")
	x.HashTransferTrxBetAmount = field.NewFloat64(table, "hash_transfer_trx_bet_amount")
	x.HashTransferTrxWinAmountU = field.NewFloat64(table, "hash_transfer_trx_win_amount_u")
	x.HashTransferUsdtWinAmount = field.NewFloat64(table, "hash_transfer_usdt_win_amount")
	x.HashTransferTrxWinAmount = field.NewFloat64(table, "hash_transfer_trx_win_amount")
	x.HashTransferUsdtValidBetAmount = field.NewFloat64(table, "hash_transfer_usdt_valid_bet_amount")
	x.HashTransferTrxValidBetAmount = field.NewFloat64(table, "hash_transfer_trx_valid_bet_amount")
	x.HashTransferUsdtFee = field.NewFloat64(table, "hash_transfer_usdt_fee")
	x.HashTransferTrxFee = field.NewFloat64(table, "hash_transfer_trx_fee")
	x.ThirdElecBetCount = field.NewInt32(table, "third_elec_bet_count")
	x.ThirdElecWinCount = field.NewInt32(table, "third_elec_win_count")
	x.ThirdElecBetAmount = field.NewFloat64(table, "third_elec_bet_amount")
	x.ThirdElecWinAmount = field.NewFloat64(table, "third_elec_win_amount")
	x.ThirdElecValidBetAmount = field.NewFloat64(table, "third_elec_valid_bet_amount")
	x.ThirdElecFee = field.NewFloat64(table, "third_elec_fee")
	x.ThirdLotteryBetCount = field.NewInt32(table, "third_lottery_bet_count")
	x.ThirdLotteryWinCount = field.NewInt32(table, "third_lottery_win_count")
	x.ThirdLotteryBetAmount = field.NewFloat64(table, "third_lottery_bet_amount")
	x.ThirdLotteryWinAmount = field.NewFloat64(table, "third_lottery_win_amount")
	x.ThirdLotteryValidBetAmount = field.NewFloat64(table, "third_lottery_valid_bet_amount")
	x.ThirdLotteryFee = field.NewFloat64(table, "third_lottery_fee")
	x.ThirdChessBetCount = field.NewInt32(table, "third_chess_bet_count")
	x.ThirdChessWinCount = field.NewInt32(table, "third_chess_win_count")
	x.ThirdChessBetAmount = field.NewFloat64(table, "third_chess_bet_amount")
	x.ThirdChessWinAmount = field.NewFloat64(table, "third_chess_win_amount")
	x.ThirdChessValidBetAmount = field.NewFloat64(table, "third_chess_valid_bet_amount")
	x.ThirdChessFee = field.NewFloat64(table, "third_chess_fee")
	x.ThirdSmallBetCount = field.NewInt32(table, "third_small_bet_count")
	x.ThirdSmallWinCount = field.NewInt32(table, "third_small_win_count")
	x.ThirdSmallBetAmount = field.NewFloat64(table, "third_small_bet_amount")
	x.ThirdSmallWinAmount = field.NewFloat64(table, "third_small_win_amount")
	x.ThirdSmallValidBetAmount = field.NewFloat64(table, "third_small_valid_bet_amount")
	x.ThirdSmallFee = field.NewFloat64(table, "third_small_fee")
	x.ThirdLiveBetCount = field.NewInt32(table, "third_live_bet_count")
	x.ThirdLiveWinCount = field.NewInt32(table, "third_live_win_count")
	x.ThirdLiveBetAmount = field.NewFloat64(table, "third_live_bet_amount")
	x.ThirdLiveWinAmount = field.NewFloat64(table, "third_live_win_amount")
	x.ThirdLiveValidBetAmount = field.NewFloat64(table, "third_live_valid_bet_amount")
	x.ThirdLiveFee = field.NewFloat64(table, "third_live_fee")
	x.ThirdSportBetCount = field.NewInt32(table, "third_sport_bet_count")
	x.ThirdSportWinCount = field.NewInt32(table, "third_sport_win_count")
	x.ThirdSportBetAmount = field.NewFloat64(table, "third_sport_bet_amount")
	x.ThirdSportWinAmount = field.NewFloat64(table, "third_sport_win_amount")
	x.ThirdSportValidBetAmount = field.NewFloat64(table, "third_sport_valid_bet_amount")
	x.ThirdSportFee = field.NewFloat64(table, "third_sport_fee")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsUserProfile) WithContext(ctx context.Context) *xAdsUserProfileDo {
	return x.xAdsUserProfileDo.WithContext(ctx)
}

func (x xAdsUserProfile) TableName() string { return x.xAdsUserProfileDo.TableName() }

func (x xAdsUserProfile) Alias() string { return x.xAdsUserProfileDo.Alias() }

func (x xAdsUserProfile) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsUserProfileDo.Columns(cols...)
}

func (x *xAdsUserProfile) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsUserProfile) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 99)
	x.fieldMap["user_id"] = x.UserID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["tg_chat_id"] = x.TgChatID
	x.fieldMap["account"] = x.Account
	x.fieldMap["account_type"] = x.AccountType
	x.fieldMap["user_bet_types"] = x.UserBetTypes
	x.fieldMap["is_agent"] = x.IsAgent
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["amount"] = x.Amount
	x.fieldMap["bonus_amount"] = x.BonusAmount
	x.fieldMap["bet_count"] = x.BetCount
	x.fieldMap["max_streak_bet"] = x.MaxStreakBet
	x.fieldMap["register_time"] = x.RegisterTime
	x.fieldMap["first_bet_time"] = x.FirstBetTime
	x.fieldMap["last_bet_time"] = x.LastBetTime
	x.fieldMap["first_recharge_time"] = x.FirstRechargeTime
	x.fieldMap["last_recharge_time"] = x.LastRechargeTime
	x.fieldMap["recharge_max_streak_bet_cnt"] = x.RechargeMaxStreakBetCnt
	x.fieldMap["recharge_max_streak_not_bet_cnt"] = x.RechargeMaxStreakNotBetCnt
	x.fieldMap["first_withdraw_finish_time"] = x.FirstWithdrawFinishTime
	x.fieldMap["last_withdraw_finish_time"] = x.LastWithdrawFinishTime
	x.fieldMap["withdraw_success_count"] = x.WithdrawSuccessCount
	x.fieldMap["last_recharge_since_days"] = x.LastRechargeSinceDays
	x.fieldMap["is_in_resource_db"] = x.IsInResourceDb
	x.fieldMap["is_use_robot"] = x.IsUseRobot
	x.fieldMap["all_bet"] = x.AllBet
	x.fieldMap["all_win"] = x.AllWin
	x.fieldMap["all_win_loss"] = x.AllWinLoss
	x.fieldMap["usdt_win_loss"] = x.UsdtWinLoss
	x.fieldMap["trx_win_loss"] = x.TrxWinLoss
	x.fieldMap["gift_usdt_stat"] = x.GiftUsdtStat
	x.fieldMap["gift_trx_stat"] = x.GiftTrxStat
	x.fieldMap["recharge_amount"] = x.RechargeAmount
	x.fieldMap["recharge_count"] = x.RechargeCount
	x.fieldMap["withdraw_count"] = x.WithdrawCount
	x.fieldMap["withdraw_amount"] = x.WithdrawAmount
	x.fieldMap["first_recharge_amount"] = x.FirstRechargeAmount
	x.fieldMap["first_recharge_time1"] = x.FirstRechargeTime1
	x.fieldMap["hash_balance_bet_count"] = x.HashBalanceBetCount
	x.fieldMap["hash_balance_win_count"] = x.HashBalanceWinCount
	x.fieldMap["hash_balance_bet_amount"] = x.HashBalanceBetAmount
	x.fieldMap["hash_balance_win_amount"] = x.HashBalanceWinAmount
	x.fieldMap["hash_balance_valid_bet_amount"] = x.HashBalanceValidBetAmount
	x.fieldMap["hash_balance_fee"] = x.HashBalanceFee
	x.fieldMap["hash_transfer_usdt_win_loss"] = x.HashTransferUsdtWinLoss
	x.fieldMap["hash_transfer_trx_win_loss"] = x.HashTransferTrxWinLoss
	x.fieldMap["hash_transfer_usdt_bet_count"] = x.HashTransferUsdtBetCount
	x.fieldMap["hash_transfer_trx_bet_count"] = x.HashTransferTrxBetCount
	x.fieldMap["hash_transfer_usdt_win_count"] = x.HashTransferUsdtWinCount
	x.fieldMap["hash_transfer_trx_win_count"] = x.HashTransferTrxWinCount
	x.fieldMap["hash_transfer_usdt_bet_amount"] = x.HashTransferUsdtBetAmount
	x.fieldMap["hash_transfer_trx_bet_amount_u"] = x.HashTransferTrxBetAmountU
	x.fieldMap["hash_transfer_trx_bet_amount"] = x.HashTransferTrxBetAmount
	x.fieldMap["hash_transfer_trx_win_amount_u"] = x.HashTransferTrxWinAmountU
	x.fieldMap["hash_transfer_usdt_win_amount"] = x.HashTransferUsdtWinAmount
	x.fieldMap["hash_transfer_trx_win_amount"] = x.HashTransferTrxWinAmount
	x.fieldMap["hash_transfer_usdt_valid_bet_amount"] = x.HashTransferUsdtValidBetAmount
	x.fieldMap["hash_transfer_trx_valid_bet_amount"] = x.HashTransferTrxValidBetAmount
	x.fieldMap["hash_transfer_usdt_fee"] = x.HashTransferUsdtFee
	x.fieldMap["hash_transfer_trx_fee"] = x.HashTransferTrxFee
	x.fieldMap["third_elec_bet_count"] = x.ThirdElecBetCount
	x.fieldMap["third_elec_win_count"] = x.ThirdElecWinCount
	x.fieldMap["third_elec_bet_amount"] = x.ThirdElecBetAmount
	x.fieldMap["third_elec_win_amount"] = x.ThirdElecWinAmount
	x.fieldMap["third_elec_valid_bet_amount"] = x.ThirdElecValidBetAmount
	x.fieldMap["third_elec_fee"] = x.ThirdElecFee
	x.fieldMap["third_lottery_bet_count"] = x.ThirdLotteryBetCount
	x.fieldMap["third_lottery_win_count"] = x.ThirdLotteryWinCount
	x.fieldMap["third_lottery_bet_amount"] = x.ThirdLotteryBetAmount
	x.fieldMap["third_lottery_win_amount"] = x.ThirdLotteryWinAmount
	x.fieldMap["third_lottery_valid_bet_amount"] = x.ThirdLotteryValidBetAmount
	x.fieldMap["third_lottery_fee"] = x.ThirdLotteryFee
	x.fieldMap["third_chess_bet_count"] = x.ThirdChessBetCount
	x.fieldMap["third_chess_win_count"] = x.ThirdChessWinCount
	x.fieldMap["third_chess_bet_amount"] = x.ThirdChessBetAmount
	x.fieldMap["third_chess_win_amount"] = x.ThirdChessWinAmount
	x.fieldMap["third_chess_valid_bet_amount"] = x.ThirdChessValidBetAmount
	x.fieldMap["third_chess_fee"] = x.ThirdChessFee
	x.fieldMap["third_small_bet_count"] = x.ThirdSmallBetCount
	x.fieldMap["third_small_win_count"] = x.ThirdSmallWinCount
	x.fieldMap["third_small_bet_amount"] = x.ThirdSmallBetAmount
	x.fieldMap["third_small_win_amount"] = x.ThirdSmallWinAmount
	x.fieldMap["third_small_valid_bet_amount"] = x.ThirdSmallValidBetAmount
	x.fieldMap["third_small_fee"] = x.ThirdSmallFee
	x.fieldMap["third_live_bet_count"] = x.ThirdLiveBetCount
	x.fieldMap["third_live_win_count"] = x.ThirdLiveWinCount
	x.fieldMap["third_live_bet_amount"] = x.ThirdLiveBetAmount
	x.fieldMap["third_live_win_amount"] = x.ThirdLiveWinAmount
	x.fieldMap["third_live_valid_bet_amount"] = x.ThirdLiveValidBetAmount
	x.fieldMap["third_live_fee"] = x.ThirdLiveFee
	x.fieldMap["third_sport_bet_count"] = x.ThirdSportBetCount
	x.fieldMap["third_sport_win_count"] = x.ThirdSportWinCount
	x.fieldMap["third_sport_bet_amount"] = x.ThirdSportBetAmount
	x.fieldMap["third_sport_win_amount"] = x.ThirdSportWinAmount
	x.fieldMap["third_sport_valid_bet_amount"] = x.ThirdSportValidBetAmount
	x.fieldMap["third_sport_fee"] = x.ThirdSportFee
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsUserProfile) clone(db *gorm.DB) xAdsUserProfile {
	x.xAdsUserProfileDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsUserProfile) replaceDB(db *gorm.DB) xAdsUserProfile {
	x.xAdsUserProfileDo.ReplaceDB(db)
	return x
}

type xAdsUserProfileDo struct{ gen.DO }

func (x xAdsUserProfileDo) Debug() *xAdsUserProfileDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsUserProfileDo) WithContext(ctx context.Context) *xAdsUserProfileDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsUserProfileDo) ReadDB() *xAdsUserProfileDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsUserProfileDo) WriteDB() *xAdsUserProfileDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsUserProfileDo) Session(config *gorm.Session) *xAdsUserProfileDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsUserProfileDo) Clauses(conds ...clause.Expression) *xAdsUserProfileDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsUserProfileDo) Returning(value interface{}, columns ...string) *xAdsUserProfileDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsUserProfileDo) Not(conds ...gen.Condition) *xAdsUserProfileDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsUserProfileDo) Or(conds ...gen.Condition) *xAdsUserProfileDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsUserProfileDo) Select(conds ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsUserProfileDo) Where(conds ...gen.Condition) *xAdsUserProfileDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsUserProfileDo) Order(conds ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsUserProfileDo) Distinct(cols ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsUserProfileDo) Omit(cols ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsUserProfileDo) Join(table schema.Tabler, on ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsUserProfileDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsUserProfileDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsUserProfileDo) Group(cols ...field.Expr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsUserProfileDo) Having(conds ...gen.Condition) *xAdsUserProfileDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsUserProfileDo) Limit(limit int) *xAdsUserProfileDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsUserProfileDo) Offset(offset int) *xAdsUserProfileDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsUserProfileDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsUserProfileDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsUserProfileDo) Unscoped() *xAdsUserProfileDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsUserProfileDo) Create(values ...*model.XAdsUserProfile) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsUserProfileDo) CreateInBatches(values []*model.XAdsUserProfile, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsUserProfileDo) Save(values ...*model.XAdsUserProfile) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsUserProfileDo) First() (*model.XAdsUserProfile, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserProfile), nil
	}
}

func (x xAdsUserProfileDo) Take() (*model.XAdsUserProfile, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserProfile), nil
	}
}

func (x xAdsUserProfileDo) Last() (*model.XAdsUserProfile, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserProfile), nil
	}
}

func (x xAdsUserProfileDo) Find() ([]*model.XAdsUserProfile, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsUserProfile), err
}

func (x xAdsUserProfileDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsUserProfile, err error) {
	buf := make([]*model.XAdsUserProfile, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsUserProfileDo) FindInBatches(result *[]*model.XAdsUserProfile, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsUserProfileDo) Attrs(attrs ...field.AssignExpr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsUserProfileDo) Assign(attrs ...field.AssignExpr) *xAdsUserProfileDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsUserProfileDo) Joins(fields ...field.RelationField) *xAdsUserProfileDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsUserProfileDo) Preload(fields ...field.RelationField) *xAdsUserProfileDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsUserProfileDo) FirstOrInit() (*model.XAdsUserProfile, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserProfile), nil
	}
}

func (x xAdsUserProfileDo) FirstOrCreate() (*model.XAdsUserProfile, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserProfile), nil
	}
}

func (x xAdsUserProfileDo) FindByPage(offset int, limit int) (result []*model.XAdsUserProfile, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsUserProfileDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsUserProfileDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsUserProfileDo) Delete(models ...*model.XAdsUserProfile) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsUserProfileDo) withDO(do gen.Dao) *xAdsUserProfileDo {
	x.DO = *do.(*gen.DO)
	return x
}
