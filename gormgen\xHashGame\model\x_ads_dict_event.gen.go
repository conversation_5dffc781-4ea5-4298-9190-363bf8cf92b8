// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsDictEvent = "x_ads_dict_event"

// XAdsDictEvent 埋点统计事件
type XAdsDictEvent struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                        // 主键ID
	EventType  int32     `gorm:"column:event_type;not null;comment:事件类型" json:"event_type"`                             // 事件类型
	EventName  string    `gorm:"column:event_name;not null;comment:事件名称" json:"event_name"`                             // 事件名称
	ParamsTpl  string    `gorm:"column:params_tpl;not null;comment:参数tpl" json:"params_tpl"`                            // 参数tpl
	Desc       string    `gorm:"column:desc;comment:描述" json:"desc"`                                                    // 描述
	Status     int32     `gorm:"column:status;not null;default:1;comment:0无效 1有效" json:"status"`                        // 0无效 1有效
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XAdsDictEvent's table name
func (*XAdsDictEvent) TableName() string {
	return TableNameXAdsDictEvent
}
