package userManger

import (
	"encoding/csv"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"io"
	"log"
	"math"
	"mime/multipart"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

const (
	// 有效充值: >= 10 usdt
	ValidRechargeCond1 int = 1
	// 有效充值: >= 50 usdt
	ValidRechargeCond2 int = 2
	// 有效充值: >= 100 usdt
	ValidRechargeCond3 int = 3
)

type MarketDataListReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	StartTime     int64 `json:"startTime"`     // 开始日期
	EndTime       int64 `json:"endTime"`       // 结束日期
	ValidRecharge int   `json:"validRecharge"` // 有效充值
	Export        int   `json:"export"`        // 是否导出
}

type MarketDataListResp struct {
	DataType           int        `gorm:"-" json:"dataType"`                                   // 数据类型：0 -- 普通 1 -- 合计  2 -- 平均
	RecordDate         *time.Time `gorm:"column:recordDate" json:"recordDate"`                 // 日期
	TotalBettors       float64    `gorm:"column:totalBettors" json:"totalBettors"`             // 总投注人数
	InquiryCustomers   float64    `gorm:"column:inquiryCustomers" json:"inquiryCustomers"`     // 咨询客服人数
	TotalRechargeUsers float64    `gorm:"column:totalRechargeUsers" json:"totalRechargeUsers"` // 总充值人数
	ValidRechargeUsers float64    `gorm:"column:validRechargeUsers" json:"validRechargeUsers"` // 有效充值人数

	TotalRechargeAmountU float64 `gorm:"column:totalRechargeAmountU" json:"totalRechargeAmountU"` // 总充值金额U
	FirstRechargeUsers   float64 `gorm:"column:firstRechargeUsers" json:"firstRechargeUsers"`     // 首充人数
	FirstRechargeAmount  float64 `gorm:"column:firstRechargeAmount" json:"firstRechargeAmount"`   // 首充金额
	WithdrawUsers        float64 `gorm:"column:withdrawUsers" json:"withdrawUsers"`               // 提款人数
	WithdrawAmountU      float64 `gorm:"column:withdrawAmountU" json:"withdrawAmountU"`           // 提款金额U

	CtDiffU      float64 `gorm:"column:ctDiffU" json:"ctDiffU"`           // 充提差U
	TransUsersT  float64 `gorm:"column:transUsersT" json:"transUsersT"`   // 转账TRX人数
	TransFlowT   float64 `gorm:"column:transFlowT" json:"transFlowT"`     // 转账TRX流水
	TransProfitT float64 `gorm:"column:transProfitT" json:"transProfitT"` // 转账TRX盈亏
	TransUsersU  float64 `gorm:"column:transUsersU" json:"transUsersU"`   // 转账U人数

	TransFlowU         float64 `gorm:"column:transFlowU" json:"transFlowU"`                 // 转账U流水
	TransProfitU       float64 `gorm:"column:transProfitU" json:"transProfitU"`             // 转账UX盈亏
	ReturnUsers        float64 `gorm:"column:returnUsers" json:"returnUsers"`               // 回访人数
	GuideRechargeUsers float64 `gorm:"column:guideRechargeUsers" json:"guideRechargeUsers"` // 引导充值人数
	RechargeAmountU    float64 `gorm:"column:rechargeAmountU" json:"rechargeAmountU"`       // 充值金额U

	RecallBonusAmountU   float64 `gorm:"column:recallBonusAmountU" json:"recallBonusAmountU"`     // 召回彩金U
	GuideWithdrawUsers   float64 `gorm:"column:guideWithdrawUsers" json:"guideWithdrawUsers"`     // 提款人数
	GuideWithdrawAmountU float64 `gorm:"column:guideWithdrawAmountU" json:"guideWithdrawAmountU"` // 提款金额U
	GuideCtDiffU         float64 `gorm:"column:guideCTDiffU" json:"guideCTDiffU"`                 // 充提差U
}

type MarketDataController struct {
}

func (this *MarketDataController) Init() {
	group := server.Http().NewGroup("/api/market")
	{
		group.Post("/list", this.List)
		// 上传咨询客服和回访数据
		group.Post("/upload", this.Upload)
	}

}

// list 列表查询接口
func (this *MarketDataController) List(ctx *abugo.AbuHttpContent) {
	var total int64
	errcode := 0
	reqdata := MarketDataListReq{}
	sumResult := new(MarketDataListResp)
	avgResult := new(MarketDataListResp)
	var list []*MarketDataListResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "市场部数据报表", "查", "查看市场部数据报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	if 1 == reqdata.Export {
		// 处理时间和导出参数
		if reqdata.EndTime > 0 {
			reqdata.EndTime += 86400000
		}
		reqdata.Page = 1
		reqdata.PageSize = 1000

	}

	// 获取有效充值用户条件
	ValidRechargeCondStr := this.GetValidUserCond(&reqdata, 1)

	// 获取合计数据
	// 构建原生SQL查询
	rawSumSql := this.GetRawSumSql(1)
	strWhereA := this.GetWhere2(&reqdata)
	strWhereB := this.GetWhere3(&reqdata)
	strSql := fmt.Sprintf(rawSumSql, ValidRechargeCondStr, strWhereA, strWhereB)
	sumSql := server.Db().GormDao().Raw(strSql)

	// 扫描结果到sumResult
	if err := sumSql.Scan(&sumResult).Error; err != nil {
		logs.Error("执行合计汇总SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	// 获取平均数据
	// 构建原生SQL查询
	//rawAvgSql := this.GetRawSumSql(0)
	//strSql = fmt.Sprintf(rawAvgSql, ValidRechargeCondStr, strWhereA, strWhereB)
	//avgSql := server.Db().GormDao().Raw(strSql)
	//
	//// 扫描结果到avgResult
	//if err := avgSql.Scan(&avgResult).Error; err != nil {
	//	logs.Error("执行平均汇总SQL失败:", err)
	//	ctx.RespErr(err, &errcode)
	//	return
	//}

	// 获取统计数据
	// 构建原生SQL查询
	rawCountgSql := this.GetRawCountSql()
	strSql = fmt.Sprintf(rawCountgSql, strWhereA, strWhereB)
	countgSql := server.Db().GormDao().Raw(strSql)
	// 扫描结果到total
	if err := countgSql.Scan(&total).Error; err != nil {
		logs.Error("执行count汇总SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	// 获取分页查询数据
	// 构建原生SQL查询
	rawQuerygSql := this.GetRawQuerySql()
	strSql = fmt.Sprintf(rawQuerygSql, ValidRechargeCondStr,
		strWhereA, strWhereB,
		limit, offset)
	querySql := server.Db().GormDao().Raw(strSql)
	// 扫描结果到avgResult
	if err := querySql.Find(&list).Error; err != nil {
		logs.Error("执行分页查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}
	if total > 0 {
		avgResult = this.GetAvgResult(sumResult, total)
	}
	sumResult.DataType = 1
	list = append(list, sumResult)
	avgResult.DataType = 2
	list = append(list, avgResult)

	if 1 == reqdata.Export {
		filename, err := this.ExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

}

func (this *MarketDataController) GetAvgResult(sum *MarketDataListResp, cnt int64) *MarketDataListResp {
	avg := &MarketDataListResp{
		DataType:           2,
		RecordDate:         sum.RecordDate,
		TotalBettors:       math.Round(sum.TotalBettors/float64(cnt)*100) / 100,
		InquiryCustomers:   math.Round(sum.InquiryCustomers/float64(cnt)*100) / 100,
		TotalRechargeUsers: math.Round(sum.TotalRechargeUsers/float64(cnt)*100) / 100,
		ValidRechargeUsers: math.Round(sum.ValidRechargeUsers/float64(cnt)*100) / 100,

		TotalRechargeAmountU: math.Round(sum.TotalRechargeAmountU/float64(cnt)*100) / 100,
		FirstRechargeUsers:   math.Round(sum.FirstRechargeUsers/float64(cnt)*100) / 100,
		FirstRechargeAmount:  math.Round(sum.FirstRechargeAmount/float64(cnt)*100) / 100,
		WithdrawUsers:        math.Round(sum.WithdrawUsers/float64(cnt)*100) / 100,
		WithdrawAmountU:      math.Round(sum.WithdrawAmountU/float64(cnt)*100) / 100,

		CtDiffU:      math.Round(sum.CtDiffU/float64(cnt)*100) / 100,
		TransUsersT:  math.Round(sum.TransUsersT/float64(cnt)*100) / 100,
		TransFlowT:   math.Round(sum.TransFlowT/float64(cnt)*100) / 100,
		TransProfitT: math.Round(sum.TransProfitT/float64(cnt)*100) / 100,
		TransUsersU:  math.Round(sum.TransUsersU/float64(cnt)*100) / 100,

		TransFlowU:         math.Round(sum.TransFlowU/float64(cnt)*100) / 100,
		TransProfitU:       math.Round(sum.TransProfitU/float64(cnt)*100) / 100,
		ReturnUsers:        math.Round(sum.ReturnUsers/float64(cnt)*100) / 100,
		GuideRechargeUsers: math.Round(sum.GuideRechargeUsers/float64(cnt)*100) / 100,
		RechargeAmountU:    math.Round(sum.RechargeAmountU/float64(cnt)*100) / 100,

		RecallBonusAmountU:   math.Round(sum.RecallBonusAmountU/float64(cnt)*100) / 100,
		GuideWithdrawUsers:   math.Round(sum.GuideWithdrawUsers/float64(cnt)*100) / 100,
		GuideWithdrawAmountU: math.Round(sum.GuideWithdrawAmountU/float64(cnt)*100) / 100,
		GuideCtDiffU:         math.Round(sum.GuideCtDiffU/float64(cnt)*100) / 100,
	}

	return avg
}

func (this *MarketDataController) GetRawSumSql(sum int) string {
	// 构建原生SQL查询
	rawSumSql := `
WITH CTE_A AS (
  SELECT
    ROUND(SUM(A.BetUsers),2) AS totalBettors,
    ROUND(SUM(A.RechargeUsers),2) AS totalRechargeUsers,
	%s, 
    ROUND(SUM(A.RechargeAmount),2) AS totalRechargeAmountU,
    ROUND(SUM(A.NewRechargeUsers),2) AS firstRechargeUsers,
    ROUND(SUM(A.NewRechargeAmount),2) AS firstRechargeAmount,
    ROUND(SUM(A.WithdrawUsers),2) AS withdrawUsers,
    ROUND(SUM(A.WithdrawAmount),2) AS withdrawAmountU,
    ROUND(SUM(A.RechargeAmount - A.WithdrawAmount),2) AS ctDiffU,
    ROUND(SUM(A.TransferTrxBetUsers),2) AS transUsersT,
    ROUND(SUM(A.TransferTrxLiuShuiAmount),2) AS transFlowT,
    ROUND(SUM(A.TransferTrxBetAmount - A.TransferTrxWinAmount),2) AS transProfitT,
    ROUND(SUM(A.TransferUsdtBetUsers),2) AS transUsersU,
    ROUND(SUM(A.TransferUsdtLiuShuiAmount),2) AS transFlowU,
    ROUND(SUM(A.TransferUsdtBetAmount - A.TransferUsdtWinAmount),2) AS transProfitU
  FROM x_agent_data_date AS A
 %s
)
,CTE_B AS(
  SELECT
	ROUND(SUM(B.AskServiceUsers),2) AS inquiryCustomers,
    ROUND(SUM(B.BackVisitUsers),2) AS returnUsers,
    ROUND(SUM(B.LeadRechargeUsers),2) AS guideRechargeUsers,
    ROUND(SUM(B.RechargeAmount),2) AS rechargeAmountU,
    ROUND(SUM(B.BackRewardAmount),2) AS recallBonusAmountU,
    ROUND(SUM(B.WithdrawUsers),2) AS guideWithdrawUsers,
    ROUND(SUM(B.WithdrawAmount),2) AS guideWithdrawAmountU,
    ROUND(SUM(B.CTDiff),2) AS guideCTDiffU
  FROM x_seller_input_data_date AS B
  %s
)
SELECT
   A.totalBettors,
  IFNULL(B.inquiryCustomers,0) AS inquiryCustomers,
  A.totalRechargeUsers,
  A.validRechargeUsers,
  A.totalRechargeAmountU,
  A.firstRechargeUsers,
  A.firstRechargeAmount,
  A.withdrawUsers,
  A.withdrawAmountU,
  A.ctDiffU,
  A.transUsersT,
  A.transFlowT,
  A.transProfitT,
  A.transUsersU,
  A.transFlowU,
  A.transProfitU,
  IFNULL(B.returnUsers,0) AS returnUsers,
  IFNULL(B.guideRechargeUsers,0) AS guideRechargeUsers,
  IFNULL(B.rechargeAmountU,0) AS rechargeAmountU,
  IFNULL(B.recallBonusAmountU,0) AS recallBonusAmountU,
  IFNULL(B.guideWithdrawUsers,0) AS guideWithdrawUsers,
  IFNULL(B.guideWithdrawAmountU,0) AS guideWithdrawAmountU,
  IFNULL(B.guideCTDiffU,0) AS guideCTDiffU
FROM CTE_A AS A
CROSS JOIN CTE_B AS B`

	rawAvgSql := `
WITH CTE_A AS (
  SELECT
    ROUND(AVG(A.BetUsers),2) AS totalBettors,
    ROUND(AVG(A.RechargeUsers),2) AS totalRechargeUsers,
	%s, 
    ROUND(AVG(A.RechargeAmount),2) AS totalRechargeAmountU,
    ROUND(AVG(A.NewRechargeUsers),2) AS firstRechargeUsers,
    ROUND(AVG(A.NewRechargeAmount),2) AS firstRechargeAmount,
    ROUND(AVG(A.WithdrawUsers),2) AS withdrawUsers,
    ROUND(AVG(A.WithdrawAmount),2) AS withdrawAmountU,
    ROUND(AVG(A.RechargeAmount - A.WithdrawAmount),2) AS ctDiffU,
    ROUND(AVG(A.TransferTrxBetUsers),2) AS transUsersT,
    ROUND(AVG(A.TransferTrxLiuShuiAmount),2) AS transFlowT,
    ROUND(AVG(A.TransferTrxBetAmount - A.TransferTrxWinAmount),2) AS transProfitT,
    ROUND(AVG(A.TransferUsdtBetUsers),2) AS transUsersU,
    ROUND(AVG(A.TransferUsdtLiuShuiAmount),2) AS transFlowU,
    ROUND(AVG(A.TransferUsdtBetAmount - A.TransferUsdtWinAmount),2) AS transProfitU
  FROM x_agent_data_date AS A
 %s
)
,CTE_B AS(
  SELECT
	ROUND(AVG(B.AskServiceUsers),2) AS inquiryCustomers,
    ROUND(AVG(B.BackVisitUsers),2) AS returnUsers,
    ROUND(AVG(B.LeadRechargeUsers),2) AS guideRechargeUsers,
    ROUND(AVG(B.RechargeAmount),2) AS rechargeAmountU,
    ROUND(AVG(B.BackRewardAmount),2) AS recallBonusAmountU,
    ROUND(AVG(B.WithdrawUsers),2) AS guideWithdrawUsers,
    ROUND(AVG(B.WithdrawAmount),2) AS guideWithdrawAmountU,
    ROUND(AVG(B.CTDiff),2) AS guideCTDiffU
  FROM x_seller_input_data_date AS B
  %s
)
SELECT
   A.totalBettors,
  IFNULL(B.inquiryCustomers,0) AS inquiryCustomers,
  A.totalRechargeUsers,
  A.validRechargeUsers,
  A.totalRechargeAmountU,
  A.firstRechargeUsers,
  A.firstRechargeAmount,
  A.withdrawUsers,
  A.withdrawAmountU,
  A.ctDiffU,
  A.transUsersT,
  A.transFlowT,
  A.transProfitT,
  A.transUsersU,
  A.transFlowU,
  A.transProfitU,
  IFNULL(B.returnUsers,0) AS returnUsers,
  IFNULL(B.guideRechargeUsers,0) AS guideRechargeUsers,
  IFNULL(B.rechargeAmountU,0) AS rechargeAmountU,
  IFNULL(B.recallBonusAmountU,0) AS recallBonusAmountU,
  IFNULL(B.guideWithdrawUsers,0) AS guideWithdrawUsers,
  IFNULL(B.guideWithdrawAmountU,0) AS guideWithdrawAmountU,
  IFNULL(B.guideCTDiffU,0) AS guideCTDiffU
FROM CTE_A AS A
CROSS JOIN CTE_B AS B`

	rawSql := rawSumSql
	if 0 == sum {
		rawSql = rawAvgSql
	}
	return rawSql
}

func (this *MarketDataController) GetRawCountSql() string {
	//WITH CTE_A AS (
	//	SELECT
	//A.RecordDate
	//FROM x_agent_data_date AS A
	//WHERE A.RecordDate >= '2025-07-01 00:00:00'
	//AND A.RecordDate <= '2025-07-08 00:00:00'
	//GROUP BY A.RecordDate
	//)
	//,CTE_B AS(
	//	SELECT
	//B.RecordDate
	//FROM x_seller_input_data_date AS B
	//WHERE B.RecordDate >= '2025-07-01 00:00:00'
	//AND B.RecordDate <= '2025-07-08 00:00:00'
	//GROUP BY B.RecordDate
	//)
	//
	//SELECT COUNT(*) AS record_count
	//FROM CTE_A AS A
	//LEFT JOIN CTE_B AS B ON A.RecordDate = B.RecordDate;

	rawSql := `
WITH CTE_A AS (
  SELECT
    A.RecordDate
  FROM x_agent_data_date AS A
  %s
  GROUP BY A.RecordDate
)
,CTE_B AS(
  SELECT
    B.RecordDate
  FROM x_seller_input_data_date AS B
  %s
  GROUP BY B.RecordDate
)

SELECT COUNT(*) AS record_count
FROM CTE_A AS A
LEFT JOIN CTE_B AS B ON A.RecordDate = B.RecordDate;
`

	return rawSql
}

func (this *MarketDataController) GetRawQuerySql() string {
	//WITH CTE_A AS (
	//	SELECT
	//A.RecordDate,
	//	SUM(A.BetUsers) AS totalBettors,
	//#SUM(B.AskServiceUsers) AS inquiryCustomers,
	//	SUM(A.RechargeUsers) AS totalRechargeUsers,
	//	SUM(A.ValidRecharge10Users) AS validRechargeUsers,
	//	SUM(A.SumRechargeAmount) AS totalRechargeAmountU,
	//	SUM(A.NewRechargeUsers) AS firstRechargeUsers,
	//	SUM(A.NewRechargeAmount) AS firstRechargeAmount,
	//	SUM(A.WithdrawUsers) AS withdrawUsers,
	//	SUM(A.WithdrawAmount) AS withdrawAmountU,
	//	SUM(A.RechargeAmount - A.WithdrawAmount) AS ctDiffU,
	//	SUM(A.TransferTrxBetUsers) AS transUsersT,
	//	SUM(A.TransferTrxLiuShuiAmount) AS transFlowT,
	//	SUM(A.TransferTrxBetAmount - A.TransferTrxWinAmount) AS transProfitT,
	//	SUM(A.TransferUsdtBetUsers) AS transUsersU,
	//	SUM(A.TransferUsdtLiuShuiAmount) AS transFlowU,
	//	SUM(A.TransferUsdtBetAmount - A.TransferUsdtWinAmount) AS transProfitU
	//
	//FROM x_agent_data_date AS A
	//WHERE A.RecordDate >= '2025-07-01 00:00:00'
	//AND A.RecordDate <= '2025-07-08 00:00:00'
	//GROUP BY A.RecordDate
	//)
	//,CTE_B AS(
	//	SELECT
	//B.RecordDate,
	//	SUM(B.AskServiceUsers) AS inquiryCustomers,
	//	SUM(B.BackVisitUsers) AS returnUsers,
	//	SUM(B.LeadRechargeUsers) AS guideRechargeUsers,
	//	SUM(B.RechargeAmount) AS rechargeAmountU,
	//	SUM(B.BackRewardAmount) AS recallBonusAmountU,
	//	SUM(B.WithdrawUsers) AS guideWithdrawUsers,
	//	SUM(B.WithdrawAmount) AS guideWithdrawAmountU,
	//	SUM(B.CTDiff) AS guideCTDiffU
	//
	//FROM x_seller_input_data_date AS B
	//WHERE B.RecordDate >= '2025-07-01 00:00:00'
	//AND B.RecordDate <= '2025-07-08 00:00:00'
	//GROUP BY B.RecordDate
	//)
	//
	//SELECT
	//A.*
	//,IFNULL(B.inquiryCustomers,0) AS inquiryCustomers
	//,IFNULL(B.returnUsers,0) AS returnUsers
	//,IFNULL(B.guideRechargeUsers,0) AS guideRechargeUsers
	//,IFNULL(B.rechargeAmountU,0) AS rechargeAmountU
	//,IFNULL(B.recallBonusAmountU,0) AS recallBonusAmountU
	//,IFNULL(B.guideWithdrawUsers,0) AS guideWithdrawUsers
	//,IFNULL(B.guideWithdrawAmountU,0) AS guideWithdrawAmountU
	//,IFNULL(B.guideCTDiffU,0) AS guideCTDiffU
	//FROM CTE_A AS A
	//LEFT JOIN CTE_B AS B
	//ON A.RecordDate=B.RecordDate
	//LIMIT 3
	//OFFSET 2;

	rawSql := `
WITH CTE_A AS (
  SELECT
    A.RecordDate AS recordDate,
    ROUND(SUM(A.TotalBetUsers),2) AS totalBettors,
    ROUND(SUM(A.RechargeUsers),2) AS totalRechargeUsers,
    %s,
    ROUND(SUM(A.RechargeAmount),2) AS totalRechargeAmountU,
    ROUND(SUM(A.NewRechargeUsers),2) AS firstRechargeUsers,
    ROUND(SUM(A.NewRechargeAmount),2) AS firstRechargeAmount,
    ROUND(SUM(A.WithdrawUsers),2) AS withdrawUsers,
    ROUND(SUM(A.WithdrawAmount),2) AS withdrawAmountU,
    ROUND(SUM(A.RechargeAmount - A.WithdrawAmount),2) AS ctDiffU,
    ROUND(SUM(A.TransferTrxBetUsers),2) AS transUsersT,
    ROUND(SUM(A.TransferTrxLiuShuiAmount),2) AS transFlowT,
    ROUND(SUM(A.TransferTrxBetAmount - A.TransferTrxWinAmount),2) AS transProfitT,
    ROUND(SUM(A.TransferUsdtBetUsers),2) AS transUsersU,
    ROUND(SUM(A.TransferUsdtLiuShuiAmount),2) AS transFlowU,
    ROUND(SUM(A.TransferUsdtBetAmount - A.TransferUsdtWinAmount),2) AS transProfitU

  FROM x_agent_data_date AS A
  %s
  GROUP BY A.recordDate
)
,CTE_B AS(
  SELECT
    B.RecordDate AS recordDate,
	ROUND(SUM(B.AskServiceUsers),2) AS inquiryCustomers,
    ROUND(SUM(B.BackVisitUsers),2) AS returnUsers,
    ROUND(SUM(B.LeadRechargeUsers),2) AS guideRechargeUsers,
    ROUND(SUM(B.RechargeAmount),2) AS rechargeAmountU,
    ROUND(SUM(B.BackRewardAmount),2) AS recallBonusAmountU,
    ROUND(SUM(B.WithdrawUsers),2) AS guideWithdrawUsers,
    ROUND(SUM(B.WithdrawAmount),2) AS guideWithdrawAmountU,
    ROUND(SUM(B.CTDiff),2) AS guideCTDiffU

  FROM x_seller_input_data_date AS B
  %s
  GROUP BY B.recordDate
)

SELECT
  A.*
  ,IFNULL(B.inquiryCustomers,0) AS inquiryCustomers
  ,IFNULL(B.returnUsers,0) AS returnUsers
  ,IFNULL(B.guideRechargeUsers,0) AS guideRechargeUsers
  ,IFNULL(B.rechargeAmountU,0) AS rechargeAmountU
  ,IFNULL(B.recallBonusAmountU,0) AS recallBonusAmountU
  ,IFNULL(B.guideWithdrawUsers,0) AS guideWithdrawUsers
  ,IFNULL(B.guideWithdrawAmountU,0) AS guideWithdrawAmountU
  ,IFNULL(B.guideCTDiffU,0) AS guideCTDiffU
FROM CTE_A AS A
LEFT JOIN CTE_B AS B
  ON A.recordDate=B.recordDate
  LIMIT %d
  OFFSET %d`

	return rawSql
}

func (this *MarketDataController) GetValidUserCond(req *MarketDataListReq, cmd int) string {
	ValidRechargeCondStr := ""
	switch cmd {
	case 0:
		{
			switch req.ValidRecharge {
			case ValidRechargeCond1:
				ValidRechargeCondStr = "ROUND(SUM(A.ValidRecharge10Users), 2) AS validRechargeUsers "
			case ValidRechargeCond2:
				ValidRechargeCondStr = "ROUND(SUM(A.ValidRecharge50Users), 2) AS validRechargeUsers "
			case ValidRechargeCond3:
				ValidRechargeCondStr = "ROUND(SUM(A.ValidRecharge100Users), 2) AS validRechargeUsers "
			}
		}
	case 1:
		{
			switch req.ValidRecharge {
			case ValidRechargeCond1:
				ValidRechargeCondStr = "ROUND(SUM(A.ValidRecharge10Users), 2) AS validRechargeUsers "
			case ValidRechargeCond2:
				ValidRechargeCondStr = "ROUND(SUM(A.ValidRecharge50Users), 2) AS validRechargeUsers "
			case ValidRechargeCond3:
				ValidRechargeCondStr = "ROUND(SUM(A.ValidRecharge100Users), 2) AS validRechargeUsers "
			}
		}
	case 2:
		{
			switch req.ValidRecharge {
			case ValidRechargeCond1:
				ValidRechargeCondStr = "ROUND(AVG(A.ValidRecharge10Users), 2) AS validRechargeUsers "
			case ValidRechargeCond2:
				ValidRechargeCondStr = "ROUND(AVG(A.ValidRecharge50Users), 2) AS validRechargeUsers "
			case ValidRechargeCond3:
				ValidRechargeCondStr = "ROUND(AVG(A.ValidRecharge100Users), 2) AS validRechargeUsers "
			}
		}
	}
	return ValidRechargeCondStr
}

func (this *MarketDataController) GetWhere(req *MarketDataListReq, db *gorm.DB) {
	// 商户id
	if req.SellerId > 0 {
		db.Where("x_agent_data_date.SellerId = ?", req.SellerId)
	}
	// 渠道id
	if len(req.ChannelId) > 0 {
		db.Where("x_agent_data_date.ChannelId IN ?", req.ChannelId)
	}

	// 顶级代理id
	if len(req.TopAgentId) > 0 {
		db.Where("x_agent_data_date.TopAgentId IN ?", req.TopAgentId)
	}

	//时间区间
	if req.StartTime > 0 {
		db.Where("x_agent_data_date.RecordDate >= ?", abugo.TimeStampToLocalTime(req.StartTime))
	}
	// 注册时间区间
	if req.EndTime > 0 && req.EndTime > req.StartTime {
		db.Where("x_agent_data_date.RecordDate <= ?", abugo.TimeStampToLocalTime(req.EndTime))
	}

}

func (this *MarketDataController) GetWhere2(req *MarketDataListReq) string {
	strSql := ""
	has := false
	before := false
	// 商户id
	if req.SellerId > 0 {
		has = true
		before = true
		strSql = fmt.Sprintf(" A.SellerId = %d ", req.SellerId)
	}
	// 渠道id
	if len(req.ChannelId) > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.ChannelId IN (%s) ", this.arr2string(req.ChannelId))
		before = true
	}

	// 顶级代理id
	if len(req.TopAgentId) > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.TopAgentId IN (%s) ", this.arr2string(req.TopAgentId))
		before = true
	}

	//时间区间
	if req.StartTime > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.RecordDate >= '%s' ", abugo.TimeStampToLocalTime(req.StartTime))
		before = true
	}
	// 注册时间区间
	if req.EndTime > 0 && req.EndTime > req.StartTime {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.RecordDate <= '%s' ", abugo.TimeStampToLocalTime(req.EndTime))
		before = true
	}
	if has {
		strSql = "WHERE " + strSql
	}
	return strSql
}

func (this *MarketDataController) GetWhere3(req *MarketDataListReq) string {
	strSql := ""
	has := false
	before := false

	// 商户id
	if req.SellerId > 0 {
		has = true
		before = true
		strSql = fmt.Sprintf(" B.SellerId = %d ", req.SellerId)
	}

	//时间区间
	if req.StartTime > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" B.RecordDate >= '%s' ", abugo.TimeStampToLocalTime(req.StartTime))
		before = true
	}
	// 注册时间区间
	if req.EndTime > 0 && req.EndTime > req.StartTime {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" B.RecordDate <= '%s' ", abugo.TimeStampToLocalTime(req.EndTime))
		before = true
	}
	if has {
		strSql = "WHERE " + strSql
	}
	return strSql
}

func (this *MarketDataController) arr2string(arr []int) string {
	var str string
	for _, v := range arr {
		str += strconv.Itoa(v) + ","
	}
	return str[:len(str)-1]
}

// 导出Excel文件
func (this *MarketDataController) ExportExcel(list []*MarketDataListResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"日期", "总投注人数", "咨询客服人数", "总充值人数", "有效充值人数",
		"总充值金额U", "首充人数", "首充金额", "提款人数", "提款金额U",
		"充提差U", "转账TRX人数", "转账TRX流水", "转账TRX盈亏", "转账U人数",
		"转账U流水", "转账U盈亏", "回访人数", "引导充值人数", "充值金额U",
		"召回彩金U", "提款人数", "提款金额U", "充提差U",
	}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始
				recordDateStr := this.timePtrToDayString(d.RecordDate)
				switch d.DataType {
				case 1:
					recordDateStr = "合 计"
				case 2:
					recordDateStr = "每日平均统计"
				}
				row := []interface{}{
					recordDateStr, d.TotalBettors, d.InquiryCustomers, d.TotalRechargeUsers, d.ValidRechargeUsers,
					d.TotalRechargeAmountU, d.FirstRechargeUsers, d.FirstRechargeAmount, d.WithdrawUsers, d.WithdrawAmountU,
					d.CtDiffU, d.TransUsersT, d.TransFlowT, d.TransProfitT, d.TransUsersU,
					d.TransFlowU, d.TransProfitU, d.ReturnUsers, d.GuideRechargeUsers, d.RechargeAmountU,
					d.RecallBonusAmountU, d.GuideWithdrawUsers, d.GuideWithdrawAmountU, d.GuideCtDiffU,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "market_sum_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

func (this *MarketDataController) timePtrToDayString(t *time.Time) string {
	if t == nil {
		return "" // 或根据需求返回默认值，如 "0000-00-00"
	}
	return t.Format("2006-01-02") // 输出格式示例: "2023-10-05"
}

// 上传咨询客服和回访数据
func (this *MarketDataController) Upload(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 获取token进行权限验证
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "市场部数据报表", "改"), &errcode, "权限不足") {
		return
	}

	// 获取上传的文件
	file, header, err := ctx.Gin().Request.FormFile("file")
	if err != nil {
		ctx.RespErrString(false, &errcode, "获取上传文件失败: "+err.Error())
		return
	}
	defer file.Close()

	// 验证文件类型
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".csv") {
		ctx.RespErrString(false, &errcode, "文件格式错误，请上传CSV文件")
		return
	}

	// 解析CSV文件
	records, err := this.parseCSVFile(file)
	if err != nil {
		ctx.RespErrString(false, &errcode, "解析CSV文件失败: "+err.Error())
		return
	}

	// 验证CSV数据格式
	if len(records) < 2 {
		ctx.RespErrString(false, &errcode, "CSV文件内容为空，至少需要2行数据（包含表头）")
		return
	}

	// 忽略表头，直接从第二行开始处理数据
	// 验证第一行数据的列数是否足够
	if len(records) > 1 && len(records[1]) < 3 {
		ctx.RespErrString(false, &errcode, "CSV文件数据格式错误")
		return
	}

	// 处理数据行
	var updateCount int
	var errorCount int
	var errorMessages []string
	itemList := make([]*model.XSellerInputDataDate, 0)
	tdb := server.Db().GormDao().Table("x_seller_input_data_date")

	for i, record := range records[1:] {
		if len(record) < 2 {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行数据不完整", i+2))
			continue
		}

		item := new(model.XSellerInputDataDate)
		// 解析日期
		dateStr := strings.TrimSpace(record[0])
		recordDate, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行日期格式错误: %s", i+2, dateStr))
			continue
		}

		// 使用本地时区，确保日期时间为当天的 00:00:00
		loc, _ := time.LoadLocation("Local")
		recordDate = time.Date(recordDate.Year(), recordDate.Month(), recordDate.Day(), 0, 0, 0, 0, loc)
		item.RecordDate = recordDate
		// 添加调试信息，显示实际使用的日期时间
		fmt.Printf("第%d行：原始日期字符串: %s, 解析后的日期时间: %s\n", i+2, dateStr, recordDate.Format("2006-01-02 15:04:05"))

		// 解析运营商号
		sellerStr := strings.TrimSpace(record[1])
		sellerID, err := strconv.ParseInt(sellerStr, 10, 32)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, sellerStr))
			continue
		}
		item.SellerID = int32(sellerID)
		// 解析咨询客服人数
		customCountStr := strings.TrimSpace(record[2])
		customCount, err := strconv.ParseInt(customCountStr, 10, 32)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, customCountStr))
			continue
		}
		item.AskServiceUsers = int32(customCount)

		// 解析回访人数
		recallCountStr := strings.TrimSpace(record[3])
		recallCount, err := strconv.ParseInt(recallCountStr, 10, 32)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, recallCountStr))
			continue
		}
		item.BackVisitUsers = int32(recallCount)

		// 解析引导充值人数
		rechargeCountStr := strings.TrimSpace(record[4])
		rechargeCount, err := strconv.ParseInt(rechargeCountStr, 10, 32)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, rechargeCountStr))
			continue
		}
		item.LeadRechargeUsers = int32(rechargeCount)

		// 解析充值金额U
		rechargeAmountUStr := strings.TrimSpace(record[5])
		rechargeAmount, err := strconv.ParseFloat(rechargeAmountUStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, rechargeAmountUStr))
			continue
		}
		item.RechargeAmount = rechargeAmount

		// 解析召回采金U
		recallRewardAmountUStr := strings.TrimSpace(record[6])
		recallRewardAmountU, err := strconv.ParseFloat(recallRewardAmountUStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, recallRewardAmountUStr))
			continue
		}
		item.BackRewardAmount = recallRewardAmountU

		// 解析提款人数
		withdrawCountStr := strings.TrimSpace(record[7])
		withdrawCount, err := strconv.ParseInt(withdrawCountStr, 10, 32)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行提款人数格式错误: %s", i+2, withdrawCountStr))
			continue
		}
		item.WithdrawUsers = int32(withdrawCount)

		// 解析提款金额U
		withdrawAmountUStr := strings.TrimSpace(record[8])
		withdrawAmount, err := strconv.ParseFloat(withdrawAmountUStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, withdrawAmountUStr))
			continue
		}
		item.WithdrawAmount = withdrawAmount

		// 解析提款金额U
		ctDiffStr := strings.TrimSpace(record[9])
		ctDiff, err := strconv.ParseFloat(ctDiffStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, withdrawAmountUStr))
			continue
		}
		item.CTDiff = ctDiff

		itemList = append(itemList, item)
		updateCount++
	}

	if len(itemList) > 0 {
		err = tdb.CreateInBatches(itemList, len(itemList)).Error
		if err != nil {
			logs.Error("批量插入数据错误：", err)
			ctx.RespErr(err, &errcode)
			return
		}
	}

	logs.Info("CSV文件解析成功，共%d行数据（包含表头），将处理%d行数据\n", len(records), len(records)-1)
	fmt.Printf("CSV文件解析成功，共%d行数据（包含表头），将处理%d行数据\n", len(records), len(records)-1)
}

// parseCSVFile 解析CSV文件，支持多种编码格式
func (this *MarketDataController) parseCSVFile(file multipart.File) ([][]string, error) {
	// 重置文件指针到开头
	file.Seek(0, 0)

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 尝试检测和转换编码
	var reader *csv.Reader

	// 检查是否为UTF-8编码
	if utf8.Valid(content) {
		reader = csv.NewReader(strings.NewReader(string(content)))
	} else {
		// 尝试GBK编码转换
		gbkContent, err := this.convertGBKToUTF8(content)
		if err != nil {
			// 如果GBK转换失败，尝试直接使用原内容
			reader = csv.NewReader(strings.NewReader(string(content)))
		} else {
			reader = csv.NewReader(strings.NewReader(gbkContent))
		}
	}

	// 读取所有记录
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("解析CSV失败: %v", err)
	}

	return records, nil
}

// convertGBKToUTF8 将GBK编码转换为UTF-8
func (this *MarketDataController) convertGBKToUTF8(gbkData []byte) (string, error) {
	// 尝试多种编码转换方式

	// 方法1：直接转换为字符串
	str := string(gbkData)

	// 方法2：尝试检测BOM并处理
	if len(gbkData) >= 3 {
		// 检查UTF-8 BOM
		if gbkData[0] == 0xEF && gbkData[1] == 0xBB && gbkData[2] == 0xBF {
			return string(gbkData[3:]), nil
		}
		// 检查UTF-16 BOM
		if (gbkData[0] == 0xFF && gbkData[1] == 0xFE) || (gbkData[0] == 0xFE && gbkData[1] == 0xFF) {
			return string(gbkData[2:]), nil
		}
	}

	// 方法3：尝试替换常见的乱码字符
	str = this.fixCommonEncodingIssues(str)

	// 直接返回处理后的字符串
	return str, nil
}

// fixCommonEncodingIssues 修复常见的编码问题
func (this *MarketDataController) fixCommonEncodingIssues(str string) string {
	// 替换常见的乱码模式
	replacements := map[string]string{
		"??":   "",
		"???":  "",
		"????": "",
	}

	for old, new := range replacements {
		str = strings.ReplaceAll(str, old, new)
	}

	return str
}
