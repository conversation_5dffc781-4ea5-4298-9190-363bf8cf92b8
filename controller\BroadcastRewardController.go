package controller

import (
	"encoding/json"
	"errors"
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

type BroadcastRewardController struct {
}

type BroadcastRewardConfig struct {
	BaseConfig struct {
		Hash       int
		Dianzi     int
		Qukuailian int
		Zhenren    int
		Qipai      int
		Sport      int
		Texas      int
		Lottery    int
		Og         int
	}
	RepeatConfig *[]struct {
		Min   int
		Max   int
		Times int
	}
	Template string
}

func (c *BroadcastRewardController) Init() {
	group := server.Http().NewGroup("/api/broadcastReward")
	{
		group.Post("/index", c.index)
		group.Post("/update", c.update)
	}
}

func (c *BroadcastRewardController) index(ctx *abugo.AbuHttpContent) {
	var config BroadcastRewardConfig
	// 从redis hash list中获取配置数据
	redisData := server.Redis().HGet("CONFIG", "BROADCAST_REWARD_CONFIG")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)

		// 反序列化为 config
		if err := json.Unmarshal([]byte(dataStr), &config); err != nil {
			fmt.Println("JSON 反序列化失败:", err)
			return
		}
	}

	ctx.RespOK(config)
}

func (c *BroadcastRewardController) update(ctx *abugo.AbuHttpContent) {
	reqdata := BroadcastRewardConfig{}
	errcode := 0

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "播报管理", "改", "修改在玩人数配置")
	if token == nil {
		return
	}

	if reqdata.RepeatConfig != nil {
		configs := *reqdata.RepeatConfig
		for i := range configs {
			if configs[i].Min >= configs[i].Max {
				ctx.RespErr(errors.New(fmt.Sprintf("重复播报配置第%d行中奖金额最小值不能大于等于最大值", i+1)), &errcode)
				return
			}

			if i+1 < len(configs) && configs[i+1].Min < configs[i].Max {
				ctx.RespErr(errors.New(fmt.Sprintf("重复播报配置第%d行中奖金额最小值不能小于第%d行最大值", i+2, i+1)), &errcode)
				return
			}
		}
	}

	server.Redis().HSet("CONFIG", "BROADCAST_REWARD_CONFIG", reqdata)

	ctx.RespOK()
}
