package active

import (
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"strings"
	"time"
)

// RechargeCheck 新用户首充复充参数校验
// 检查基础配置和奖励配置的合法性，支持新的配置字段结构
func RechargeCheck(reqdata DefineModReq) error {
	// 1. 验证基础配置
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	}

	var baseConfig RechargeBaseConfig
	err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
	if err != nil {
		return errors.New("基础参数格式错误")
	}

	// 2. 验证参与条件相关参数
	// 2.1 验证注册天数和领取天数
	if baseConfig.RegisterDay < 1 {
		return errors.New("注册天数必须大于等于1")
	}
	if baseConfig.ReceiveDay < 1 {
		return errors.New("领取天数必须大于等于1")
	}

	// 2.2 验证充值次数
	if baseConfig.RechargeCount < 0 {
		return errors.New("充值次数不能小于0")
	}

	// 2.3 验证是否活动期间内注册的账号
	if baseConfig.IsDuringReg && reqdata.EffectStartTime == 0 {
		return errors.New("当设置为仅活动期间内注册的账号可参与时，必须设置活动开始时间")
	}

	// 2.4 验证IP限制
	if baseConfig.MaxIPAttempts < 0 {
		return errors.New("同IP最大领取次数不能小于0")
	}

	// 2.5 验证ID限制
	if baseConfig.MaxIDAttempts < 0 {
		return errors.New("同ID最大领取次数不能小于0")
	}

	// 2.6 验证IP黑名单格式
	if len(baseConfig.BlockedIPList) > 0 {
		blackIPList := strings.Split(baseConfig.BlockedIPList, ",")
		for _, ip := range blackIPList {
			if net.ParseIP(ip) == nil {
				return errors.New("IP格式错误")
			}
		}
	}

	// 2.7 验证投注金额限制
	if baseConfig.MinBetAmount < 0 {
		return errors.New("最小投注金额不能小于0")
	}
	if baseConfig.MaxBetAmount > 0 && baseConfig.MaxBetAmount < baseConfig.MinBetAmount {
		return errors.New("最大投注金额不能小于最小投注金额")
	}

	// 2.8 游戏类型验证已移除

	// 3. 验证活动方式和打码条件
	// 3.1 验证活动方式
	if baseConfig.ActivityMethod != 1 && baseConfig.ActivityMethod != 2 {
		return errors.New("活动方式必须为1（前置）或2（后置）")
	}

	// 3.2 验证打码条件
	if baseConfig.BetType != 1 && baseConfig.BetType != 2 && baseConfig.BetType != 3 {
		return errors.New("打码条件必须为1（真金）或2（彩金）或3（彩金+真金）")
	}

	// 4. 验证奖励相关参数
	// 4.1 验证奖励账户类型
	if baseConfig.RewardWalletType != 0 && baseConfig.RewardWalletType != 1 {
		return errors.New("奖励账户类型必须为0（真金账户）或1（彩金账户）")
	}

	// 4.3 验证玩家流水计入统计参数
	// IsCalcActiveWager 表示玩家参与此活动所产生的流水是否纳入会员返水及代理返佣统计

	// 4.4 验证总派发金额上限和每日派发金额上限
	if baseConfig.TotalRewardLimit < 0 {
		return errors.New("总派发金额上限不能小于0")
	}
	if baseConfig.DailyRewardLimit < 0 {
		return errors.New("每日派发金额上限不能小于0")
	}
	if baseConfig.DailyRewardLimit > 0 && baseConfig.TotalRewardLimit > 0 && baseConfig.DailyRewardLimit > baseConfig.TotalRewardLimit {
		return errors.New("每日派发金额上限不能大于总派发金额上限")
	}

	// 5. 流水要求验证已移除

	// 6. 更新基础配置
	b, _ := json.Marshal(baseConfig)
	reqdata.BaseConfig = string(b)

	// 7. 验证奖励档位配置
	if reqdata.Config == "" {
		return errors.New("奖励参数需要配置")
	}

	var config []RechargeConfig
	err = json.Unmarshal([]byte(reqdata.Config), &config)
	if err != nil {
		return errors.New("奖励参数格式错误")
	}
	if len(config) == 0 {
		return errors.New("奖励参数需要上传")
	}

	// 7.1 验证每个档位的参数
	idMap := make(map[int32]bool)
	for i, v := range config {
		// 验证档位ID
		if v.ID < 1 {
			return fmt.Errorf("奖励档位 %d 的ID必须大于0", i+1)
		}

		// 验证档位ID唯一性
		if idMap[v.ID] {
			return fmt.Errorf("奖励档位 %d 的ID与其他档位重复", i+1)
		}
		idMap[v.ID] = true

		// 验证首次单笔最低充值
		if v.FirstChargeUstdLimit < 0 {
			return fmt.Errorf("奖励档位 %d 的首次单笔最低充值(U)不能小于0", i+1)
		}

		// 验证真金流水倍数
		if v.LiushuiMultiple < 0 {
			return fmt.Errorf("奖励档位 %d 的真金流水倍数不能小于0", i+1)
		}

		// 验证彩金流水倍数
		if v.BonusMultiple < 0 {
			return fmt.Errorf("奖励档位 %d 的彩金流水倍数不能小于0", i+1)
		}

		// 验证赠送方式：确保两种方式互斥且必须选择一种
		if v.GiveProportion > 0 && v.GiveAmount > 0 {
			return fmt.Errorf("奖励档位 %d 不能同时设置赠送比例和赠送金额，请选择其中一种方式", i+1)
		}
		if v.GiveProportion == 0 && v.GiveAmount == 0 {
			return fmt.Errorf("奖励档位 %d 必须设置赠送比例或赠送金额其中一种", i+1)
		}

		// 确保互斥性：如果设定了固定金额，百分比必须为0；如果设定了百分比，固定金额必须为0
		if v.GiveAmount > 0 && v.GiveProportion != 0 {
			return fmt.Errorf("奖励档位 %d 设定了固定赠送金额时，百分比必须为0", i+1)
		}
		if v.GiveProportion > 0 && v.GiveAmount != 0 {
			return fmt.Errorf("奖励档位 %d 设定了赠送比例时，固定金额必须为0", i+1)
		}

		// 验证赠送上限
		if v.GiveLimit < 0 {
			return fmt.Errorf("奖励档位 %d 的赠送上限(U)不能小于0", i+1)
		}

		// 如果使用固定金额赠送，验证赠送金额不能超过赠送上限
		if v.GiveAmount > 0 && v.GiveLimit > 0 && v.GiveAmount > v.GiveLimit {
			return fmt.Errorf("奖励档位 %d 的赠送金额不能超过赠送上限", i+1)
		}
	}

	// 8. 验证赠送钱包类型
	if !IsValidGiveWalletType(int(reqdata.GiftWallet)) {
		return errors.New("赠送钱包类型必须为1（真金钱包）或2（彩金钱包）")
	}

	// 9. 验证奖金类别
	if reqdata.AwardType != 0 && !IsValidAwardType(reqdata.AwardType) {
		return errors.New("奖金类别必须为1-7之间的有效值")
	}

	// 10. 验证奖金页面分类
	if reqdata.AwardTab != 0 && !IsValidAwardTab(reqdata.AwardTab) {
		return errors.New("奖金页面分类必须为1、2、3、4或1000")
	}

	// 11. 验证彩金钱包下的活动奖励配置数据
	if reqdata.AwardData != "" {
		var awardData RechargeAwardData
		if err := json.Unmarshal([]byte(reqdata.AwardData), &awardData); err != nil {
			return errors.New("彩金钱包活动奖励配置格式错误")
		}

		// 验证奖励打码倍数类型
		if awardData.AwardBetType != 2 && awardData.AwardBetType != 3 {
			return errors.New("奖励打码倍数类型必须为2（彩金）或3（彩金+真金）")
		}

		// 验证激活期限配置
		if err := validateTimeConfig(awardData.ActivationType, awardData.ActivetionCountdownCount,
			awardData.ActivetionCountdownType, awardData.ActivetionDate, "激活期限"); err != nil {
			return err
		}

		// 验证流水完成期限配置
		if err := validateTimeConfig(awardData.TurnoverType, awardData.TurnoverCountdownCount,
			awardData.TurnoverCountdownType, awardData.TurnoverDate, "流水完成期限"); err != nil {
			return err
		}

		// 验证领取期限配置
		if err := validateTimeConfig(awardData.ReceiveType, awardData.ReceiveCountdownCount,
			awardData.ReceiveCountdownType, awardData.ReceiveDate, "领取期限"); err != nil {
			return err
		}

		// 验证投注限制
		if awardData.MinBetAmount < 0 {
			return errors.New("奖励单笔投注限制最小值不能小于0")
		}
		if awardData.MaxBetAmount > 0 && awardData.MaxBetAmount < awardData.MinBetAmount {
			return errors.New("奖励单笔投注限制最大值不能小于最小值")
		}

		// 验证游戏限制配置
		if awardData.LimitGameType != 1 && awardData.LimitGameType != 2 {
			return errors.New("奖励投注游戏限制必须为1（不限制）或2（限制）")
		}

		// 验证限制游戏ID列表格式
		if awardData.LimitGameIds != "" {
			if err := validateGameIdList(awardData.LimitGameIds); err != nil {
				return fmt.Errorf("限制游戏ID列表格式错误: %v", err)
			}
		}

		// 验证最高盈利金额限制
		if awardData.LimitMaxWinAmount < 0 {
			return errors.New("奖励投注最高盈利金额限制不能小于0")
		}

		// 验证是否计入有效流水
		if awardData.IsCalcAwardValid != 0 && awardData.IsCalcAwardValid != 1 {
			return errors.New("奖励投注金额是否计入有效流水必须为0（否）或1（是）")
		}

		// 验证奖励配置数组
		if len(awardData.AwardConfig) == 0 {
			return errors.New("奖励配置不能为空，至少需要配置一个档位")
		}

		// 验证每个奖励配置档位
		idMap := make(map[int32]bool)
		for i, config := range awardData.AwardConfig {
			// 验证档位ID
			if config.ID < 1 {
				return fmt.Errorf("奖励档位 %d 的ID必须大于0", i+1)
			}

			// 验证档位ID唯一性
			if idMap[config.ID] {
				return fmt.Errorf("奖励档位 %d 的ID与其他档位重复", i+1)
			}
			idMap[config.ID] = true

			// 验证首次单笔最低充值
			if config.FirstChargeUstdLimit < 0 {
				return fmt.Errorf("奖励档位 %d 的首次单笔最低充值(U)不能小于0", i+1)
			}

			// 验证真金流水倍数
			if config.LiushuiMultiple < 0 {
				return fmt.Errorf("奖励档位 %d 的真金流水倍数不能小于0", i+1)
			}

			// 验证彩金流水倍数
			if config.BonusMultiple < 0 {
				return fmt.Errorf("奖励档位 %d 的彩金流水倍数不能小于0", i+1)
			}

			// 验证赠送方式：确保两种方式互斥且必须选择一种
			if config.GiveProportion > 0 && config.GiveAmount > 0 {
				return fmt.Errorf("奖励档位 %d 不能同时设置赠送比例和赠送金额，请选择其中一种方式", i+1)
			}
			if config.GiveProportion == 0 && config.GiveAmount == 0 {
				return fmt.Errorf("奖励档位 %d 必须设置赠送比例或赠送金额其中一种", i+1)
			}

			// 确保互斥性：如果设定了固定金额，百分比必须为0；如果设定了百分比，固定金额必须为0
			if config.GiveAmount > 0 && config.GiveProportion != 0 {
				return fmt.Errorf("奖励档位 %d 设定了固定赠送金额时，百分比必须为0", i+1)
			}
			if config.GiveProportion > 0 && config.GiveAmount != 0 {
				return fmt.Errorf("奖励档位 %d 设定了赠送比例时，固定金额必须为0", i+1)
			}

			// 验证赠送上限
			if config.GiveLimit < 0 {
				return fmt.Errorf("奖励档位 %d 的赠送上限(U)不能小于0", i+1)
			}

			// 如果使用固定金额赠送，验证赠送金额不能超过赠送上限
			if config.GiveAmount > 0 && config.GiveLimit > 0 && config.GiveAmount > config.GiveLimit {
				return fmt.Errorf("奖励档位 %d 的赠送金额不能超过赠送上限", i+1)
			}

			// 验证最高盈利限制
			if config.MaxWinLimit < 0 {
				return fmt.Errorf("奖励档位 %d 的奖励投注最高盈利限制不能小于0", i+1)
			}
		}
	}

	// 12. 验证图片和时间
	// 12.1 验证图片是否上传
	if reqdata.TopImgLang == "" || reqdata.TitleImgLang == "" {
		return errors.New("请上传中英文默认图片")
	}

	// 12.2 设置默认开始时间
	if reqdata.EffectStartTime == 0 {
		reqdata.EffectStartTime = time.Now().UnixMilli()
	}

	// 12.3 验证结束时间是否合法
	if reqdata.EffectEndTime != 0 && reqdata.EffectEndTime < reqdata.EffectStartTime {
		return errors.New("结束时间不可早于开始时间")
	}

	return nil
}

// validateTimeConfig 验证时间配置
func validateTimeConfig(timeType, countdownCount, countdownType int, customDate int64, configName string) error {
	switch timeType {
	case 1: // 无期限
		// 无需额外验证
	case 2: // 倒计时
		if countdownCount <= 0 {
			return fmt.Errorf("%s配置中倒计时数值必须大于0", configName)
		}
		if countdownType != 1 && countdownType != 2 {
			return fmt.Errorf("%s配置中倒计时类型必须为1（天）或2（小时）", configName)
		}
	case 3: // 自定义
		if customDate <= 0 {
			return fmt.Errorf("%s配置中自定义结束时间必须设置", configName)
		}
		if customDate <= time.Now().UnixMilli() {
			return fmt.Errorf("%s配置中自定义结束时间必须晚于当前时间", configName)
		}
	default:
		return fmt.Errorf("%s配置中类型必须为1（无期限）、2（倒计时）或3（自定义）", configName)
	}
	return nil
}

// validateGameIdList 验证游戏ID列表格式
// 支持逗号分隔的游戏ID字符串，支持数字和字符串格式，如 "1,2,3" 或 "gfg_hash,pg_slot"
func validateGameIdList(gameIds string) error {
	if gameIds == "" {
		return nil
	}

	// 去除首尾空格
	gameIds = strings.TrimSpace(gameIds)
	if gameIds == "" {
		return nil
	}

	// 按逗号分割
	idList := strings.Split(gameIds, ",")
	for i, idStr := range idList {
		// 去除每个ID的首尾空格
		idStr = strings.TrimSpace(idStr)
		if idStr == "" {
			return fmt.Errorf("第 %d 个游戏ID为空", i+1)
		}

		// 验证游戏ID格式：支持数字、字母、下划线、连字符
		if !isValidGameId(idStr) {
			return fmt.Errorf("第 %d 个游戏ID '%s' 格式无效，只能包含字母、数字、下划线和连字符", i+1, idStr)
		}
	}

	return nil
}

// isValidGameId 验证单个游戏ID是否有效
// 支持数字、字母、下划线、连字符的组合
func isValidGameId(gameId string) bool {
	if gameId == "" {
		return false
	}

	// 检查每个字符是否为字母、数字、下划线或连字符
	for _, char := range gameId {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return false
		}
	}

	return true
}
