// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotPostUserInfo = "x_robot_post_user_info"

// XRobotPostUserInfo mapped from table <x_robot_post_user_info>
type XRobotPostUserInfo struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`               // pk
	ChatID      int64     `gorm:"column:chat_id;comment:飞机ID" json:"chat_id"`                                 // 飞机ID
	UserName    string    `gorm:"column:user_name;comment:用户名" json:"user_name"`                              // 用户名
	PhoneNumber string    `gorm:"column:phone_number;comment:手机号" json:"phone_number"`                        // 手机号
	Type        int32     `gorm:"column:type;comment:类型：拉人0，私信1，采集2" json:"type"`                             // 类型：拉人0，私信1，采集2
	CreateTime  time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建" json:"create_time"` // 创建
	UpdateTime  time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新" json:"update_time"` // 更新
}

// TableName XRobotPostUserInfo's table name
func (*XRobotPostUserInfo) TableName() string {
	return TableNameXRobotPostUserInfo
}
