// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgAccount = "x_tg_account"

// XTgAccount mapped from table <x_tg_account>
type XTgAccount struct {
	ID              int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:自增id" json:"Id"`             // 自增id
	SellerID        int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                // 运营商
	Phone           string    `gorm:"column:Phone;comment:注册tg时使用的手机号" json:"Phone"`                              // 注册tg时使用的手机号
	TgUsername      string    `gorm:"column:TgUsername;comment:TG号的Username" json:"TgUsername"`                   // TG号的Username
	KefuAccount     string    `gorm:"column:KefuAccount;comment:客服后台账号（admin_user.Account）" json:"KefuAccount"`   // 客服后台账号（admin_user.Account）
	Status          int32     `gorm:"column:Status;default:1;comment:TG号状态（1正常 2禁用）" json:"Status"`               // TG号状态（1正常 2禁用）
	OperUserAccount string    `gorm:"column:OperUserAccount;comment:操作员账号" json:"OperUserAccount"`                // 操作员账号
	CreateTime      time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime      time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XTgAccount's table name
func (*XTgAccount) TableName() string {
	return TableNameXTgAccount
}
