// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsRechargeDailyStat = "x_ads_recharge_daily_stats"

// XAdsRechargeDailyStat 充值偏好每日统计表
type XAdsRechargeDailyStat struct {
	ID                     int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                         // 主键ID
	SellerID               int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                                        // 运营商ID
	ChannelID              int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                                       // 渠道ID
	TopAgentID             int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                                 // 顶级代理ID
	StatDate               time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                                                // 统计日期
	RechargeType           int32     `gorm:"column:recharge_type;not null;comment:充值类型: 1:法币 2:加密货币" json:"recharge_type"`                           // 充值类型: 1:法币 2:加密货币
	RechargeChannel        string    `gorm:"column:recharge_channel;not null;comment:充值渠道" json:"recharge_channel"`                                  // 充值渠道
	RechargeCountPc        int32     `gorm:"column:recharge_count_pc;not null;comment:充值次数pc" json:"recharge_count_pc"`                              // 充值次数pc
	RechargeCountH5        int32     `gorm:"column:recharge_count_h5;not null;comment:充值次数h5" json:"recharge_count_h5"`                              // 充值次数h5
	RechargeCountSuccessPc int32     `gorm:"column:recharge_count_success_pc;not null;comment:充值成功次数pc" json:"recharge_count_success_pc"`            // 充值成功次数pc
	RechargeCountSuccessH5 int32     `gorm:"column:recharge_count_success_h5;not null;comment:充值成功次数h5" json:"recharge_count_success_h5"`            // 充值成功次数h5
	RechargeCount100Pc     int32     `gorm:"column:recharge_count_100_pc;not null;comment:充值≤100 U次数 pc" json:"recharge_count_100_pc"`               // 充值≤100 U次数 pc
	RechargeCount100H5     int32     `gorm:"column:recharge_count_100_h5;not null;comment:充值≤100 U次数 h5" json:"recharge_count_100_h5"`               // 充值≤100 U次数 h5
	RechargeCount100500Pc  int32     `gorm:"column:recharge_count_100_500_pc;not null;comment:充值100-500 U次数 pc" json:"recharge_count_100_500_pc"`    // 充值100-500 U次数 pc
	RechargeCount100500H5  int32     `gorm:"column:recharge_count_100_500_h5;not null;comment:充值100-500 U次数 h5" json:"recharge_count_100_500_h5"`    // 充值100-500 U次数 h5
	RechargeCount5001000Pc int32     `gorm:"column:recharge_count_500_1000_pc;not null;comment:充值500-1000 U次数 pc" json:"recharge_count_500_1000_pc"` // 充值500-1000 U次数 pc
	RechargeCount5001000H5 int32     `gorm:"column:recharge_count_500_1000_h5;not null;comment:充值500-1000 U次数 h5" json:"recharge_count_500_1000_h5"` // 充值500-1000 U次数 h5
	RechargeCount1000Pc    int32     `gorm:"column:recharge_count_1000_pc;not null;comment:充值＞1000 U次数 pc" json:"recharge_count_1000_pc"`            // 充值＞1000 U次数 pc
	RechargeCount1000H5    int32     `gorm:"column:recharge_count_1000_h5;not null;comment:充值＞1000  U次数 h5" json:"recharge_count_1000_h5"`           // 充值＞1000  U次数 h5
	RechargeRatePc         float32   `gorm:"column:recharge_rate_pc;not null;comment:充值占比pc" json:"recharge_rate_pc"`                                // 充值占比pc
	RechargeRateH5         float32   `gorm:"column:recharge_rate_h5;not null;comment:充值占比h5" json:"recharge_rate_h5"`                                // 充值占比h5
	RechargeSuccessRatePc  float32   `gorm:"column:recharge_success_rate_pc;not null;comment:充值成功率 pc" json:"recharge_success_rate_pc"`              // 充值成功率 pc
	RechargeSuccessRateH5  float32   `gorm:"column:recharge_success_rate_h5;not null;comment:充值成功率 h5" json:"recharge_success_rate_h5"`              // 充值成功率 h5
	CreateTime             time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                  // 创建时间
	UpdateTime             time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                  // 更新时间
}

// TableName XAdsRechargeDailyStat's table name
func (*XAdsRechargeDailyStat) TableName() string {
	return TableNameXAdsRechargeDailyStat
}
