// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXKefuBindHistory = "x_kefu_bind_history"

// XKefuBindHistory mapped from table <x_kefu_bind_history>
type XKefuBindHistory struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID     int32     `gorm:"column:SellerId" json:"SellerId"`
	ChannelID    int32     `gorm:"column:ChannelId" json:"ChannelId"`
	UserID       int32     `gorm:"column:UserId" json:"UserId"`
	BeforeGroup  string    `gorm:"column:BeforeGroup" json:"BeforeGroup"`
	AfterGroup   string    `gorm:"column:AfterGroup" json:"AfterGroup"`
	BeforeID     string    `gorm:"column:BeforeId" json:"BeforeId"`
	AfterID      string    `gorm:"column:AfterId" json:"AfterId"`
	Account      string    `gorm:"column:Account" json:"Account"`
	CreateTime   time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	AdminAccount string    `gorm:"column:AdminAccount" json:"AdminAccount"`
}

// TableName XKefuBindHistory's table name
func (*XKefuBindHistory) TableName() string {
	return TableNameXKefuBindHistory
}
