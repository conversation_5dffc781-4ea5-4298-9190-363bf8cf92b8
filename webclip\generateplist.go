package webclip

import (
	"bytes"
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"os"
	"text/template"
	"time"
	"xserver/gormgen/xHashGame/dao"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/spf13/viper"
	//"xserver/server"
)

type ReplaceData struct {
	Id      int
	Icon    string
	AppName string
	Host    string
}

func GenerateWebConfig(sellerId int32, channelHostId int32, dao *dao.Query) error {
	data := GetAppInfos(int32(sellerId), int32(channelHostId), dao)
	//pwd, _ := os.Getwd()
	//err := os.Chdir("./webclip")
	templateFile := "config/client.mobileconfig"
	outputDir := "./output"

	//executablePath, err := os.Executable()
	//if err != nil {
	//	fmt.Printf("Error getting executable path: %v\n", err)
	//	return err
	//}
	//
	//executableDir := filepath.Dir(executablePath)
	//fmt.Println("Executable Directory:", executableDir)
	//
	//templateFile := filepath.Join(executableDir, "template", "client.mobileconfig")
	//outputDir := "output"
	// 读取模板文件
	content, err := os.ReadFile(templateFile)
	if err != nil {
		fmt.Printf("Error reading template file: %v\n", err)
		return err
	}

	// 确保目标目录存在，不存在则创建
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		fmt.Printf("Error creating output directory: %v\n", err)
		return err
	}

	// 解析模板
	tmpl, err := template.New("plist").Parse(string(content))
	if err != nil {
		fmt.Printf("Error parsing template: %v\n", err)
		return err
	}

	// 渲染模板
	for _, v := range data {
		var renderedContent bytes.Buffer
		var outputFile string
		if err := tmpl.Execute(&renderedContent, v); err != nil {
			fmt.Printf("Error executing template: %v\n", err)
			return err
		}

		timestamp := time.Now().Unix()
		outputFile = GenerateMD5FileName(fmt.Sprintf("%s%d%v", v.AppName, v.Id, timestamp))

		// 写入输出文件
		if err := os.WriteFile(fmt.Sprintf("%s/%s", outputDir, outputFile), renderedContent.Bytes(), 0644); err != nil {
			fmt.Printf("Error writing output file: %v\n", err)
			return err
		}

		err = UploadS3(fmt.Sprintf("./%s/%s", outputDir, outputFile), fmt.Sprintf("/%s/%s", "webclip", outputFile))
		if err != nil {
			fmt.Printf("upload webclip error: %v\n", err)
			return err
		}

		// 更新数据库
		xChannelHost := dao.XChannelHost
		_, err := xChannelHost.WithContext(nil).Where(xChannelHost.ID.Eq(int32(v.Id))).Update(xChannelHost.Webclip, fmt.Sprintf("/%s/%s", "webclip", outputFile))
		if err != nil {
			fmt.Printf("update webclip error: %v\n", err)
			return err
		}

		fmt.Println("Template processed and saved to", fmt.Sprintf("/%s/%s", "webclip", outputFile))
	}
	return nil
}

func GetAppInfos(sellerId int32, channelHostId int32, dao *dao.Query) []ReplaceData {
	// 查看当前的渠道域名是否有生成webclip文件
	xChannelHost := dao.XChannelHost
	xChannel := dao.XChannel
	xSeller := dao.XSeller
	xAgentIndependence := dao.XAgentIndependence
	type result struct {
		Id            int    `gorm:"column:Id"`
		ChannelHost   string `gorm:"column:Host"`
		SellerLogo    string `gorm:"column:SellerLogo"`
		AgentLogo     string `gorm:"column:AgentLogo"`
		ShowName      string `gorm:"column:ShowName"`
		AgentShowName string `gorm:"column:AgentShowName"`
	}
	var results []result
	query := xChannelHost.WithContext(nil).
		Select(xChannelHost.ID, xChannelHost.Host.As("ChannelHost"), xSeller.IosIcon.As("SellerLogo"), xSeller.ShowName, xAgentIndependence.IosIcon.As("AgentLogo"), xAgentIndependence.ShowName.As("AgentShowName")).
		LeftJoin(xChannel, xChannelHost.ChannelID.EqCol(xChannel.ChannelID)).
		LeftJoin(xSeller, xChannel.SellerID.EqCol(xSeller.SellerID)).
		LeftJoin(xAgentIndependence, xChannelHost.Host.EqCol(xAgentIndependence.Host)).
		Where(xChannelHost.State.Eq(1)).
		Where(xSeller.State.Eq(1))

	if sellerId > 0 {
		query.Where(xSeller.SellerID.Eq(sellerId))
	}

	if channelHostId > 0 {
		query.Where(xChannelHost.ID.Eq(channelHostId))
	}

	err := query.Scan(&results)

	if err != nil {
		fmt.Printf("Error get channelHost: %v\n", err)
		return []ReplaceData{}
	}

	var replaceData []ReplaceData
	for _, r := range results {
		logo := r.SellerLogo
		showName := r.ShowName
		if r.AgentLogo != "" {
			logo = r.AgentLogo
		}
		if r.AgentShowName != "" {
			showName = r.ShowName
		}
		base64Logo := ""
		if logo != "" {
			base64Logo, err = GenerateImgUrlToBase64(fmt.Sprintf("%s%s", viper.GetString("image.url"), logo))
			if err != nil {
				fmt.Printf("Error generating base64 logo: %v\n", err)
			}
		}

		replaceData = append(replaceData, ReplaceData{
			Id:      r.Id,
			Icon:    base64Logo,
			AppName: showName,
			Host:    fmt.Sprintf("https://%s", r.ChannelHost),
		})
	}

	return replaceData
}

func GenerateImgUrlToBase64(imgUrl string) (string, error) {
	response, err := http.Get(imgUrl)
	if err != nil {
		//fmt.Printf("Error downloading image: %v\n", err)
		//return err
		return "", err
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		//fmt.Printf("Error: Received non-200 status code: %d\n", response.StatusCode)
		//os.Exit(1)
		return "", err
	}

	// 读取图片数据
	var imageData bytes.Buffer
	_, err = io.Copy(&imageData, response.Body)
	if err != nil {
		//fmt.Printf("Error reading image data: %v\n", err)
		//os.Exit(1)
		return "", err
	}

	// 转换为 Base64
	return base64.StdEncoding.EncodeToString(imageData.Bytes()), nil
}

func GenerateMD5FileName(input string) string {
	// 计算 MD5 哈希值
	hash := md5.New()
	io.WriteString(hash, input)
	md5Hash := hash.Sum(nil)
	// 将哈希值转为16进制字符串并作为文件名
	fileName := fmt.Sprintf("%x.mobileconfig", md5Hash)
	return fileName
}

func UploadS3(filePath, objectKey string) error {
	awsKey := viper.GetString("image.aws.key")
	awsSecret := viper.GetString("image.aws.secret")
	awsEndpoint := viper.GetString("image.aws.endpoint")
	awsBucket := viper.GetString("image.aws.bucket")
	awsRegion := viper.GetString("image.aws.region")
	// 创建一个 S3 客户端
	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(awsRegion), // 设置区域
		Endpoint:    aws.String(awsEndpoint),
		Credentials: credentials.NewStaticCredentials(awsKey, awsSecret, ""),
	})
	if err != nil {
		return fmt.Errorf("failed to create session: %v", err)
	}

	s3Client := s3.New(sess)

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// 创建 S3 上传请求
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket: aws.String(awsBucket), // S3 存储桶名称
		Key:    aws.String(objectKey), // S3 文件路径
		Body:   file,                  // 上传文件内容
		//ACL:    aws.String(s3.), // 设置访问控制列表，允许公开读取（可选）
	})

	if err != nil {
		return fmt.Errorf("failed to upload file to S3: %v", err)
	}

	return nil
}
