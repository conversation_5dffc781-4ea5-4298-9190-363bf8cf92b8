// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentBlacklist = "x_agent_blacklist"

// XAgentBlacklist mapped from table <x_agent_blacklist>
type XAgentBlacklist struct {
	ID            int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID      int32     `gorm:"column:SellerId" json:"SellerId"`
	ChannelID     int32     `gorm:"column:ChannelId" json:"ChannelId"`
	UserType      int32     `gorm:"column:UserType;default:1;comment:1用户 2代理 3渠道 4运营商" json:"UserType"` // 1用户 2代理 3渠道 4运营商
	UserID        int32     `gorm:"column:UserId" json:"UserId"`
	TopAgentID    int32     `gorm:"column:TopAgentId" json:"TopAgentId"`
	AgentID       int32     `gorm:"column:AgentId" json:"AgentId"`
	Address       string    `gorm:"column:Address" json:"Address"`
	CreateTime    time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	CreateAccount string    `gorm:"column:CreateAccount" json:"CreateAccount"`
}

// TableName XAgentBlacklist's table name
func (*XAgentBlacklist) TableName() string {
	return TableNameXAgentBlacklist
}
