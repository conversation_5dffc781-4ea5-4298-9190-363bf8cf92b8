// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRedpacketWithdrawLog = "x_tg_redpacket_withdraw_log"

// XTgRedpacketWithdrawLog mapped from table <x_tg_redpacket_withdraw_log>
type XTgRedpacketWithdrawLog struct {
	ID             int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	TgUserID       int64     `gorm:"column:TgUserId;not null" json:"TgUserId"`
	TgUsername     string    `gorm:"column:TgUsername;not null" json:"TgUsername"`
	SellerID       int32     `gorm:"column:SellerId;not null;comment:运营商id" json:"SellerId"`                         // 运营商id
	TgID           int32     `gorm:"column:TgId;not null;comment:后台机器人id" json:"TgId"`                               // 后台机器人id
	WithdrawAmount float64   `gorm:"column:WithdrawAmount;not null;default:0.00;comment:提现金额" json:"WithdrawAmount"` // 提现金额
	OrderNumber    string    `gorm:"column:OrderNumber;not null;comment:订单号" json:"OrderNumber"`                     // 订单号
	PaltformUserID int64     `gorm:"column:PaltformUserId;not null;comment:平台用户ID" json:"PaltformUserId"`            // 平台用户ID
	State          int32     `gorm:"column:State;not null;default:1;comment:1:未审核 2:通过 3:拒绝 4:没收" json:"State"`      // 1:未审核 2:通过 3:拒绝 4:没收
	OperatorID     int32     `gorm:"column:OperatorId;not null;comment:操作人" json:"OperatorId"`                       // 操作人
	AuditAt        time.Time `gorm:"column:AuditAt;comment:审核时间" json:"AuditAt"`                                     // 审核时间
	CreatedAt      time.Time `gorm:"column:CreatedAt;default:CURRENT_TIMESTAMP" json:"CreatedAt"`
	UpdatedAt      time.Time `gorm:"column:UpdatedAt;default:CURRENT_TIMESTAMP" json:"UpdatedAt"`
}

// TableName XTgRedpacketWithdrawLog's table name
func (*XTgRedpacketWithdrawLog) TableName() string {
	return TableNameXTgRedpacketWithdrawLog
}
