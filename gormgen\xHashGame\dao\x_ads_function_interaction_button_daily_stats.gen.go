// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsFunctionInteractionButtonDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsFunctionInteractionButtonDailyStat {
	_xAdsFunctionInteractionButtonDailyStat := xAdsFunctionInteractionButtonDailyStat{}

	_xAdsFunctionInteractionButtonDailyStat.xAdsFunctionInteractionButtonDailyStatDo.UseDB(db, opts...)
	_xAdsFunctionInteractionButtonDailyStat.xAdsFunctionInteractionButtonDailyStatDo.UseModel(&model.XAdsFunctionInteractionButtonDailyStat{})

	tableName := _xAdsFunctionInteractionButtonDailyStat.xAdsFunctionInteractionButtonDailyStatDo.TableName()
	_xAdsFunctionInteractionButtonDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsFunctionInteractionButtonDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsFunctionInteractionButtonDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsFunctionInteractionButtonDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsFunctionInteractionButtonDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsFunctionInteractionButtonDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsFunctionInteractionButtonDailyStat.ButtonName = field.NewString(tableName, "button_name")
	_xAdsFunctionInteractionButtonDailyStat.ClickCountPc = field.NewInt32(tableName, "click_count_pc")
	_xAdsFunctionInteractionButtonDailyStat.ClickCountH5 = field.NewInt32(tableName, "click_count_h5")
	_xAdsFunctionInteractionButtonDailyStat.ResponseTimePc = field.NewFloat32(tableName, "response_time_pc")
	_xAdsFunctionInteractionButtonDailyStat.ResponseTimeH5 = field.NewFloat32(tableName, "response_time_h5")
	_xAdsFunctionInteractionButtonDailyStat.AvgResponseTimePc = field.NewFloat32(tableName, "avg_response_time_pc")
	_xAdsFunctionInteractionButtonDailyStat.AvgResponseTimeH5 = field.NewFloat32(tableName, "avg_response_time_h5")
	_xAdsFunctionInteractionButtonDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsFunctionInteractionButtonDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsFunctionInteractionButtonDailyStat.fillFieldMap()

	return _xAdsFunctionInteractionButtonDailyStat
}

// xAdsFunctionInteractionButtonDailyStat 功能交互按钮每日统计表
type xAdsFunctionInteractionButtonDailyStat struct {
	xAdsFunctionInteractionButtonDailyStatDo xAdsFunctionInteractionButtonDailyStatDo

	ALL               field.Asterisk
	ID                field.Int64   // 主键ID
	SellerID          field.Int32   // 运营商ID
	ChannelID         field.Int32   // 渠道ID
	TopAgentID        field.Int64   // 顶级代理ID
	StatDate          field.Time    // 统计日期
	ButtonName        field.String  // 按钮名称
	ClickCountPc      field.Int32   // 点击次数 pc
	ClickCountH5      field.Int32   // 点击次数 h5
	ResponseTimePc    field.Float32 // 响应时长(s) pc
	ResponseTimeH5    field.Float32 // 响应时长(s) h5
	AvgResponseTimePc field.Float32 // 平均响应时长(s) pc
	AvgResponseTimeH5 field.Float32 // 平均响应时长(s) h5
	CreateTime        field.Time    // 创建时间
	UpdateTime        field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsFunctionInteractionButtonDailyStat) Table(newTableName string) *xAdsFunctionInteractionButtonDailyStat {
	x.xAdsFunctionInteractionButtonDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsFunctionInteractionButtonDailyStat) As(alias string) *xAdsFunctionInteractionButtonDailyStat {
	x.xAdsFunctionInteractionButtonDailyStatDo.DO = *(x.xAdsFunctionInteractionButtonDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsFunctionInteractionButtonDailyStat) updateTableName(table string) *xAdsFunctionInteractionButtonDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.StatDate = field.NewTime(table, "stat_date")
	x.ButtonName = field.NewString(table, "button_name")
	x.ClickCountPc = field.NewInt32(table, "click_count_pc")
	x.ClickCountH5 = field.NewInt32(table, "click_count_h5")
	x.ResponseTimePc = field.NewFloat32(table, "response_time_pc")
	x.ResponseTimeH5 = field.NewFloat32(table, "response_time_h5")
	x.AvgResponseTimePc = field.NewFloat32(table, "avg_response_time_pc")
	x.AvgResponseTimeH5 = field.NewFloat32(table, "avg_response_time_h5")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsFunctionInteractionButtonDailyStat) WithContext(ctx context.Context) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.xAdsFunctionInteractionButtonDailyStatDo.WithContext(ctx)
}

func (x xAdsFunctionInteractionButtonDailyStat) TableName() string {
	return x.xAdsFunctionInteractionButtonDailyStatDo.TableName()
}

func (x xAdsFunctionInteractionButtonDailyStat) Alias() string {
	return x.xAdsFunctionInteractionButtonDailyStatDo.Alias()
}

func (x xAdsFunctionInteractionButtonDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsFunctionInteractionButtonDailyStatDo.Columns(cols...)
}

func (x *xAdsFunctionInteractionButtonDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsFunctionInteractionButtonDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["button_name"] = x.ButtonName
	x.fieldMap["click_count_pc"] = x.ClickCountPc
	x.fieldMap["click_count_h5"] = x.ClickCountH5
	x.fieldMap["response_time_pc"] = x.ResponseTimePc
	x.fieldMap["response_time_h5"] = x.ResponseTimeH5
	x.fieldMap["avg_response_time_pc"] = x.AvgResponseTimePc
	x.fieldMap["avg_response_time_h5"] = x.AvgResponseTimeH5
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsFunctionInteractionButtonDailyStat) clone(db *gorm.DB) xAdsFunctionInteractionButtonDailyStat {
	x.xAdsFunctionInteractionButtonDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsFunctionInteractionButtonDailyStat) replaceDB(db *gorm.DB) xAdsFunctionInteractionButtonDailyStat {
	x.xAdsFunctionInteractionButtonDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsFunctionInteractionButtonDailyStatDo struct{ gen.DO }

func (x xAdsFunctionInteractionButtonDailyStatDo) Debug() *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsFunctionInteractionButtonDailyStatDo) WithContext(ctx context.Context) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) ReadDB() *xAdsFunctionInteractionButtonDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsFunctionInteractionButtonDailyStatDo) WriteDB() *xAdsFunctionInteractionButtonDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Session(config *gorm.Session) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Clauses(conds ...clause.Expression) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Returning(value interface{}, columns ...string) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Not(conds ...gen.Condition) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Or(conds ...gen.Condition) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Select(conds ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Where(conds ...gen.Condition) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Order(conds ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Distinct(cols ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Omit(cols ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Group(cols ...field.Expr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Having(conds ...gen.Condition) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Limit(limit int) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Offset(offset int) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Unscoped() *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Create(values ...*model.XAdsFunctionInteractionButtonDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsFunctionInteractionButtonDailyStatDo) CreateInBatches(values []*model.XAdsFunctionInteractionButtonDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsFunctionInteractionButtonDailyStatDo) Save(values ...*model.XAdsFunctionInteractionButtonDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsFunctionInteractionButtonDailyStatDo) First() (*model.XAdsFunctionInteractionButtonDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionButtonDailyStat), nil
	}
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Take() (*model.XAdsFunctionInteractionButtonDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionButtonDailyStat), nil
	}
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Last() (*model.XAdsFunctionInteractionButtonDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionButtonDailyStat), nil
	}
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Find() ([]*model.XAdsFunctionInteractionButtonDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsFunctionInteractionButtonDailyStat), err
}

func (x xAdsFunctionInteractionButtonDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsFunctionInteractionButtonDailyStat, err error) {
	buf := make([]*model.XAdsFunctionInteractionButtonDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsFunctionInteractionButtonDailyStatDo) FindInBatches(result *[]*model.XAdsFunctionInteractionButtonDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsFunctionInteractionButtonDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Joins(fields ...field.RelationField) *xAdsFunctionInteractionButtonDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Preload(fields ...field.RelationField) *xAdsFunctionInteractionButtonDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsFunctionInteractionButtonDailyStatDo) FirstOrInit() (*model.XAdsFunctionInteractionButtonDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionButtonDailyStat), nil
	}
}

func (x xAdsFunctionInteractionButtonDailyStatDo) FirstOrCreate() (*model.XAdsFunctionInteractionButtonDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionButtonDailyStat), nil
	}
}

func (x xAdsFunctionInteractionButtonDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsFunctionInteractionButtonDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsFunctionInteractionButtonDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsFunctionInteractionButtonDailyStatDo) Delete(models ...*model.XAdsFunctionInteractionButtonDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsFunctionInteractionButtonDailyStatDo) withDO(do gen.Dao) *xAdsFunctionInteractionButtonDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
