package userManger

import (
	"encoding/csv"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"io"
	"log"
	"math"
	"mime/multipart"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

const (
	// 全部
	ValidPaymentUserLv int = 1
	// 10档位（满足任一条件即算）
	// 1、余额玩法【充值 ≥ 10且＜50U】
	// 2、转账玩法 (未领彩金)【10 ≤ T流水＜700 或 10 ≤ U流水＜500，T按汇率转U】
	// 3、转账玩法 (领取彩金)【🔴会员赢钱：(领取彩金数额*25) ≤ T流水＜(领取彩金数额*100)🟢会员输钱：领取彩金数额 ≤ T亏损＜700 或 10 ≤ U亏损＜50】

	ValidPaymentUserLv1 int = 2
	// 50档位（满足任一条件即算）
	// 1、余额玩法【充值 ≥ 50且＜100U】；
	// 2、转账玩法 (未领彩金)【700 ≤ T流水＜1400 或 500 ≤ U流水＜1000，T按汇率转U】；
	// 3、转账玩法 (领取彩金)【🔴会员赢钱：7000 ≤ T流水＜14000；🟢会员输钱：700 ≤ T亏损＜1400 或 50 ≤ U亏损＜100】;

	ValidPaymentUserLv2 int = 3
	// 100档位（满足任一条件即算）
	//1、余额玩法【充值 ≥ 100U】；
	//2、转账玩法 (未领彩金)【T流水 ≥ 1400 或 U流水 ≥ 1000，T按汇率转U】；满足任一条件即算
	//3、转账玩法 (领取彩金)【🔴会员赢钱：T流水 ≥ 14000；🟢会员输钱：T亏损 ≥ 1400 或 U亏损 ≥ 100】；

	ValidPaymentUserLv3 int = 4
)

const (
	// 有效新增用户: 转账投注(T按汇率转U) >= 10U
	ValidNewUserCond1 int = 1
	// 有效新增用户: 转账投注(T按汇率转U) >= 50U
	ValidNewUserCond2 int = 2
	// 有效新增用户: 转账投注(T按汇率转U) >= 100U
	ValidNewUserCond3 int = 3
)

type OperationsPlatListReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	StartTime          int64 `json:"startTime"`          // 开始日期
	EndTime            int64 `json:"endTime"`            // 结束日期
	ValidPaymentUserLv []int `json:"validPaymentUserLv"` // 有效付费用户档位
	Export             int   `json:"export"`             // 是否导出
}

type OperationsPlatWindowListReq struct {
	Page               int    `json:"page"`               // 页码
	PageSize           int    `json:"pageSize"`           // 页大小
	SellerId           int    `json:"sellerId"`           // 运营商
	ChannelId          []int  `json:"channelId"`          // 渠道ID
	TopAgentId         []int  `json:"topAgentId"`         // 顶级代理
	FieldName          string `json:"fieldName"`          // 字段名称
	FieldValue         int    `json:"fieldValue"`         // 字段值
	DataTime           int64  `json:"dataTime"`           // 数据日期
	ValidPaymentUserLv []int  `json:"validPaymentUserLv"` // 有效付费用户档位
	Export             int    `json:"export"`             // 是否导出
}

type OperationsPlatListResp struct {
	DataType       int        `gorm:"-" json:"dataType"`                           // 数据类型：0 -- 普通 1 -- 合计  2 -- 平均
	RecordDate     *time.Time `gorm:"column:recordDate" json:"recordDate"`         // 日期
	TotalBettors   float64    `gorm:"column:totalBettors" json:"totalBettors"`     // 总投注人数
	NewBettors     float64    `gorm:"column:newBettors" json:"newBettors"`         // 新增投注人数
	OldBettors     float64    `gorm:"column:oldBettors" json:"oldBettors"`         // 旧用户投注人数
	PaymentBettors float64    `gorm:"column:paymentBettors" json:"paymentBettors"` // 付费投注总人数

	NewPaymentUsers      float64 `gorm:"column:newPaymentUsers" json:"newPaymentUsers"`           // 新增付费人数
	OldPaymentUsers      float64 `gorm:"column:oldPaymentUsers" json:"oldPaymentUsers"`           // 旧付费人数
	ValidPaymentUsers    float64 `gorm:"column:validPaymentUsers" json:"validPaymentUsers"`       // 总有效付费人数
	NewValidPaymentUsers float64 `gorm:"column:newValidPaymentUsers" json:"newValidPaymentUsers"` // 新增有效付费人数
	OldValidPaymentUsers float64 `gorm:"column:oldValidPaymentUsers" json:"oldValidPaymentUsers"` // 旧有效付费人数

	BetCount        float64 `gorm:"column:betCount" json:"betCount"`               // 投注次数
	BetAmount       float64 `gorm:"column:betAmount" json:"betAmount"`             // 投注金额
	RebateAmount    float64 `gorm:"column:rebateAmount" json:"rebateAmount"`       // 返奖金额
	ProfitAmountGGR float64 `gorm:"column:profitAmountGGR" json:"profitAmountGGR"` // 盘内盈利 (负数客赢)GGR
	GasAmount       float64 `gorm:"column:gasAmount" json:"gasAmount"`             // 手续费

	ActiveAmount     float64 `gorm:"column:activeAmount" json:"activeAmount"`         // 活动金额
	SubActiveAmount  float64 `gorm:"column:subActiveAmount" json:"subActiveAmount"`   // 扣除活动金额
	SubOtherAmount   float64 `gorm:"column:subOtherAmount" json:"subOtherAmount"`     // 其它扣除金额
	VipRebateAmount  float64 `gorm:"column:vipRebateAmount" json:"vipRebateAmount"`   // VIP返水
	CommissionAmount float64 `gorm:"column:commissionAmount" json:"commissionAmount"` // 佣金

	ResourcesAmount float64 `gorm:"column:resourcesAmount" json:"resourcesAmount"` // 资源部
	MarketAmount    float64 `gorm:"column:marketAmount" json:"marketAmount"`       // 市场部
	AdAmount        float64 `gorm:"column:adAmount" json:"adAmount"`               // 广告部
	OpAmount        float64 `gorm:"column:opAmount" json:"opAmount"`               // 运营部
	AgentAmount     float64 `gorm:"column:agentAmount" json:"agentAmount"`         // 代理分红收支

	ProfitAmountNGR float64 `gorm:"column:profitAmountNGR" json:"profitAmountNGR"` // 实际盘内盈利 (负数客赢)NGR
	ARPUAmount      float64 `gorm:"column:arpuAmount" json:"arpuAmount"`           //  ARPU
	ARPPUAmount     float64 `gorm:"column:arppuAmount" json:"arppuAmount"`         //  ARPPU
	PaymentRate     float64 `gorm:"column:paymentRate" json:"paymentRate"`         //  付费率
	AvgBetAmount    float64 `gorm:"column:avgBetAmount" json:"avgBetAmount"`       //  平均投注量ATPU
}

type OperationsPlatWindowListResp struct {
	UserId           int64   `gorm:"column:userId" json:"userId"`                     // 玩家 ID
	RechargeAmount   float64 `gorm:"column:rechargeAmount" json:"rechargeAmount"`     // 充值金额
	BetAmount        float64 `gorm:"column:betAmount" json:"betAmount"`               // 投注金额
	PlatProfitAmount float64 `gorm:"column:platProfitAmount" json:"platProfitAmount"` //平台收益
}

type OperationsTransTRXReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	StartTime int64 `json:"startTime"` // 开始日期
	EndTime   int64 `json:"endTime"`   // 结束日期
	//ValidNewUserCond int   `json:"validNewUserCond"` // 有效新增用户
	Export int `json:"export"` // 是否导出
}

type OperationsTransTRXWindowReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	FieldName  string `json:"fieldName"`  // 字段名称
	FieldValue int    `json:"fieldValue"` // 字段值
	DataTime   int64  `json:"dataTime"`   // 数据日期
	//ValidNewUserCond int    `json:"validNewUserCond"` // 有效新增用户
	Export int `json:"export"` // 是否导出
}

type OperationsTransTRXResp struct {
	DataType      int        `gorm:"-" json:"dataType"`                         // 数据类型：0 -- 普通 1 -- 合计  2 -- 平均
	RecordDate    *time.Time `gorm:"column:recordDate" json:"recordDate"`       // 日期
	NewUsers      float64    `gorm:"column:newUsers" json:"newUsers"`           // 新增会员人数
	ValidNewUsers float64    `gorm:"column:validNewUsers" json:"validNewUsers"` // 有效新增人数
	OldUsers      float64    `gorm:"column:oldUsers" json:"oldUsers"`           // 老会员人数
	Bettors       float64    `gorm:"column:bettors" json:"bettors"`             // 投注人数

	PaymentBettors float64 `gorm:"column:paymentBettors" json:"paymentBettors"` // 付费投注人数
	BetCount       float64 `gorm:"column:betCount" json:"betCount"`             // 投注次数
	WinCount       float64 `gorm:"column:winCount" json:"winCount"`             // 中奖次数
	WinRate        float64 `gorm:"column:winRate" json:"winRate"`               // 中奖率
	BetAmount      float64 `gorm:"column:betAmount" json:"betAmount"`           // 投注金额

	RebateAmount    float64 `gorm:"column:rebateAmount" json:"rebateAmount"`       // 返奖金额
	GasAmount       float64 `gorm:"column:gasAmount" json:"gasAmount"`             // 手续费/系统磨损
	ProfitAmountGGR float64 `gorm:"column:profitAmountGGR" json:"profitAmountGGR"` // 盘内盈利 (负数客赢)GGR
	ActiveAmount    float64 `gorm:"column:activeAmount" json:"activeAmount"`       // 活动金额
	ProfitAmountNGR float64 `gorm:"column:profitAmountNGR" json:"profitAmountNGR"` // 实际盘内盈利 (负数客赢)NGR

	AvgBetAmount float64 `gorm:"column:avgBetAmount" json:"avgBetAmount"` //  平均投注量ATPU
	D1Retention  float64 `gorm:"column:d1Retention" json:"d1Retention"`   // 次日留存
	D3Retention  float64 `gorm:"column:d3Retention" json:"d3Retention"`   // 3日留存
	D7Retention  float64 `gorm:"column:d7Retention" json:"d7Retention"`   // 7日留存
	D15Retention float64 `gorm:"column:d15Retention" json:"d15Retention"` // 15日留存
}

type WindowData struct {
	UserId         float64 `gorm:"column:UserId" json:"UserId"`                 // 投注金额
	Address        string  `gorm:"column:Address" json:"Address"`               // 钱包地址
	WalletAddress  string  `gorm:"column:walletAddress" json:"walletAddress"`   // 钱包地址
	BetAmount      float64 `gorm:"column:BetAmount" json:"BetAmount"`           // 投注金额
	RewardAmount   float64 `gorm:"column:RewardAmount" json:"RewardAmount"`     // 返奖金额
	RechargeAmount float64 `gorm:"column:RechargeAmount" json:"RechargeAmount"` // 充值金额
}

type OperationsTransTRXWindowResp struct {
	WalletAddress    string  `gorm:"column:walletAddress" json:"walletAddress"`       // 钱包地址
	BetAmount        float64 `gorm:"column:betAmount" json:"betAmount"`               // 投注金额
	PlatProfitAmount float64 `gorm:"column:platProfitAmount" json:"platProfitAmount"` //平台收益
}

type OperationsTransUSDTReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	StartTime int64 `json:"startTime"` // 开始日期
	EndTime   int64 `json:"endTime"`   // 结束日期
	//ValidNewUserCond int   `json:"validNewUserCond"` // 有效新增用户
	Export int `json:"export"` // 是否导出
}

type OperationsTransUSDTWindowReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	FieldName  string `json:"fieldName"`  // 字段名称
	FieldValue int    `json:"fieldValue"` // 字段值
	DataTime   int64  `json:"dataTime"`   // 数据日期
	//ValidNewUserCond int    `json:"validNewUserCond"` // 有效新增用户
	Export int `json:"export"` // 是否导出
}

type OperationsTransUSDTResp struct {
	DataType      int        `gorm:"-" json:"dataType"`                         // 数据类型：0 -- 普通 1 -- 合计  2 -- 平均
	RecordDate    *time.Time `gorm:"column:recordDate" json:"recordDate"`       // 日期
	NewUsers      float64    `gorm:"column:newUsers" json:"newUsers"`           // 新增会员人数
	ValidNewUsers float64    `gorm:"column:validNewUsers" json:"validNewUsers"` // 有效新增人数
	OldUsers      float64    `gorm:"column:oldUsers" json:"oldUsers"`           // 老会员人数
	Bettors       float64    `gorm:"column:bettors" json:"bettors"`             // 投注人数

	BetCount     float64 `gorm:"column:betCount" json:"betCount"`         // 投注次数
	WinCount     float64 `gorm:"column:winCount" json:"winCount"`         // 中奖次数
	WinRate      float64 `gorm:"column:winRate" json:"winRate"`           // 中奖率
	BetAmount    float64 `gorm:"column:betAmount" json:"betAmount"`       // 投注金额
	RebateAmount float64 `gorm:"column:rebateAmount" json:"rebateAmount"` // 返奖金额

	GasAmount       float64 `gorm:"column:gasAmount" json:"gasAmount"`             // 手续费/系统磨损
	ProfitAmountGGR float64 `gorm:"column:profitAmountGGR" json:"profitAmountGGR"` // 盘内盈利 (负数客赢)GGR
	AvgBetAmount    float64 `gorm:"column:avgBetAmount" json:"avgBetAmount"`       //  平均投注量ATPU
	D1Retention     float64 `gorm:"column:d1Retention" json:"d1Retention"`         // 次日留存
	D3Retention     float64 `gorm:"column:d3Retention" json:"d3Retention"`         // 3日留存

	D7Retention  float64 `gorm:"column:d7Retention" json:"d7Retention"`   // 7日留存
	D15Retention float64 `gorm:"column:d15Retention" json:"d15Retention"` // 15日留存
}

type OperationsTransUSDTWindowResp struct {
	WalletAddress    string  `gorm:"column:walletAddress" json:"walletAddress"`       // 钱包地址
	BetAmount        float64 `gorm:"column:betAmount" json:"betAmount"`               // 投注金额
	PlatProfitAmount float64 `gorm:"column:platProfitAmount" json:"platProfitAmount"` //平台收益
}

type OperationsBalanceGameplayReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	StartTime             int64 `json:"startTime"`             // 开始日期
	EndTime               int64 `json:"endTime"`               // 结束日期
	ValidRechargeUserCond int   `json:"validRechargeUserCond"` // 有效充值用户
	Export                int   `json:"export"`                // 是否导出
}

type OperationsBalanceGameplayWindowReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	SellerId   int   `json:"sellerId"`   // 运营商
	ChannelId  []int `json:"channelId"`  // 渠道ID
	TopAgentId []int `json:"topAgentId"` // 顶级代理

	FieldName  string `json:"fieldName"`  // 字段名称
	FieldValue int    `json:"fieldValue"` // 字段值
	DataTime   int64  `json:"dataTime"`   // 数据日期
	//ValidNewUserCond int    `json:"validNewUserCond"` // 有效新增用户
	Export int `json:"export"` // 是否导出
}

type OperationsBalanceGameplayResp struct {
	DataType            int        `gorm:"-" json:"dataType"`                                     // 数据类型：0 -- 普通 1 -- 合计  2 -- 平均
	RecordDate          *time.Time `gorm:"column:recordDate" json:"recordDate"`                   // 日期
	FirstRechargeUsers  float64    `gorm:"column:firstRechargeUsers" json:"firstRechargeUsers"`   // 首充人数
	FirstRechargeAmount float64    `gorm:"column:firstRechargeAmount" json:"firstRechargeAmount"` // 首充金额
	RechargeCount       float64    `gorm:"column:rechargeCount" json:"rechargeCount"`             // 充值人数
	ValidRechargeCount  float64    `gorm:"column:validRechargeCount" json:"validRechargeCount"`   // 有效充值人数
	RechargeAmount      float64    `gorm:"column:rechargeAmount" json:"rechargeAmount"`           // 充值金额

	WithdrawCount   float64 `gorm:"column:withdrawCount" json:"withdrawCount"`     // 提现人数
	WithdrawAmount  float64 `gorm:"column:withdrawAmount" json:"withdrawAmount"`   // 提现金额
	WithdrawAmount2 float64 `gorm:"column:withdrawAmount2" json:"withdrawAmount2"` // 去除返水返佣提现金额
	CTDiff          float64 `gorm:"column:ctDiff" json:"ctDiff"`                   // 充提差
	TotalBettors    float64 `gorm:"column:totalBettors" json:"totalBettors"`       // 投注人数

	PaymentBettors float64 `gorm:"column:paymentBettors" json:"paymentBettors"` // 付费投注人数
	BetCount       float64 `gorm:"column:betCount" json:"betCount"`             // 投注次数
	WinCount       float64 `gorm:"column:winCount" json:"winCount"`             // 中奖次数
	BetAmount      float64 `gorm:"column:betAmount" json:"betAmount"`           // 投注金额
	RebateAmount   float64 `gorm:"column:rebateAmount" json:"rebateAmount"`     // 返奖金额

	GasAmount       float64 `gorm:"column:gasAmount" json:"gasAmount"`             // 手续费/系统磨损
	ProfitAmountGGR float64 `gorm:"column:profitAmountGGR" json:"profitAmountGGR"` // 盘内盈利 (负数客赢)GGR
	ProfitAmountNGR float64 `gorm:"column:profitAmountNGR" json:"profitAmountNGR"` // 实际盘内盈利 (负数客赢)NGR
	AvgBetAmount    float64 `gorm:"column:avgBetAmount" json:"avgBetAmount"`       //  平均投注量ATPU
	D1Retention     float64 `gorm:"column:d1Retention" json:"d1Retention"`         // 次日留存

	D3Retention  float64 `gorm:"column:d3Retention" json:"d3Retention"`   // 3日留存
	D7Retention  float64 `gorm:"column:d7Retention" json:"d7Retention"`   // 7日留存
	D15Retention float64 `gorm:"column:d15Retention" json:"d15Retention"` // 15日留存
}

type OperationsBalanceGameplayWindowResp struct {
	UserId           int64   `gorm:"column:userId" json:"userId"`                     // 玩家 ID
	RechargeAmount   float64 `gorm:"column:rechargeAmount" json:"rechargeAmount"`     // 充值金额
	BetAmount        float64 `gorm:"column:betAmount" json:"betAmount"`               // 投注金额
	PlatProfitAmount float64 `gorm:"column:platProfitAmount" json:"platProfitAmount"` //平台收益
}

type CommonWindowResp struct {
	UserId           int64   `gorm:"column:userId" json:"userId"`                     // 玩家 ID
	RechargeAmount   float64 `gorm:"column:rechargeAmount" json:"rechargeAmount"`     // 充值金额
	BetAmount        float64 `gorm:"column:betAmount" json:"betAmount"`               // 投注金额
	PlatProfitAmount float64 `gorm:"column:platProfitAmount" json:"platProfitAmount"` //平台收益
}

type OperationsDataController struct {
}

func (this *OperationsDataController) Init() {
	group := server.Http().NewGroup("/api/operations")
	{
		// 平台汇总
		group.Post("/plat_list", this.PlatList)
		// 平台汇总:二级弹窗
		group.Post("/plat_list_window", this.PlatListWindow)
		// 平台汇总-上传补充数据
		group.Post("/plat_upload", this.PlatUpload)
		// TRX转账汇总
		group.Post("/trans_trx", this.TransTRX)
		// TRX转账汇总:二级弹窗
		group.Post("/trans_trx_window", this.TransTRXWindow)
		// USDT转账汇总
		group.Post("/trans_usdt", this.TransUSDT)
		// USDT转账汇总:二级弹窗
		group.Post("/trans_usdt_window", this.TransUSDTWindow)
		// 余额玩法汇总
		group.Post("/balance_gameplay", this.BalanceGameplay)
		// 余额玩法汇总:二级弹窗
		group.Post("/balance_gameplay_window", this.BalanceGameplayWindow)
	}

}

// 平台汇总
func (this *OperationsDataController) PlatList(ctx *abugo.AbuHttpContent) {
	var total int64
	errcode := 0
	reqdata := OperationsPlatListReq{}
	sumResult := new(OperationsPlatListResp)
	avgResult := new(OperationsPlatListResp)
	var list []*OperationsPlatListResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	if 1 == reqdata.Export {
		// 处理时间和导出参数
		if reqdata.EndTime > 0 {
			reqdata.EndTime += 86400000
		}
		reqdata.Page = 1
		reqdata.PageSize = 1000

	}

	// 获取查询数据
	strWhereA := this.GetPlatWhere(&reqdata)
	strWhereB := this.GetPlatWhere2(&reqdata)
	//validRechargeCondStr := this.GetValidPaymentUserCond(reqdata.ValidPaymentUserLv, "validPaymentUsers")
	//newValidRechargeCondStr := this.GetValidPaymentUserCond(reqdata.ValidPaymentUserLv, "newValidPaymentUsers")
	rawQuerySql := this.GetRawQuerySql()
	strSql := fmt.Sprintf(rawQuerySql, strWhereA, strWhereB)
	querySql := server.Db().GormDao().Raw(strSql)

	if err := querySql.Find(&list).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}
	total = int64(len(list))
	if total <= 0 {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
		return
	}
	list = this.PlatListDataProper(list)
	sumResult = this.GetPlatSum(list)
	avgResult = this.GetPlatAvg(sumResult, total)
	list = this.GetPlatPageList(list, offset, limit)
	sumResult.DataType = 1
	list = append(list, sumResult)
	avgResult.DataType = 2
	list = append(list, avgResult)
	if 1 == reqdata.Export {
		filename, err := this.PlatExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}
}

// 平台汇总数据数据计算
func (this *OperationsDataController) PlatListDataProper(list []*OperationsPlatListResp) []*OperationsPlatListResp {
	for _, v := range list {
		// 计算平台汇总数据GGR: 投注 - 返奖
		v.ProfitAmountGGR = v.BetAmount - v.RebateAmount
		// 计算平台汇总数据NGR: GGR + 手续费 - 活动金额 + 扣除活动金额 + 其他扣除金额 -
		// VIP返水 - 佣金 - 资源部 - 市场部 - 广告部 - 运营部 - 代理分红收支
		v.ProfitAmountNGR = v.ProfitAmountGGR + v.GasAmount - v.ActiveAmount + v.SubActiveAmount + v.SubOtherAmount -
			v.VipRebateAmount - v.CommissionAmount - v.ResourcesAmount - v.MarketAmount -
			v.AdAmount - v.OpAmount - v.AgentAmount
		// 计算平台汇总数据ARPU: NGR / 总投注人数
		if v.TotalBettors > 0 {
			v.ARPUAmount = math.Round(v.ProfitAmountNGR/float64(v.TotalBettors)*100) / 100
		}
		// 计算平台汇总数据ARPPU: NGR / 付费总投注人数
		// 计算平台汇总数据付费率: 付费总投注人数 / 总投注人数
		// 计算平台汇总数据ATPU: NGR / 总投注人数
		v.AvgBetAmount = math.Round(v.ProfitAmountNGR/float64(v.TotalBettors)*100) / 100

	}

	return list
}

// 平台汇总:二级弹窗
func (this *OperationsDataController) PlatListWindow(ctx *abugo.AbuHttpContent) {
	var total int64
	errcode := 0
	reqdata := OperationsPlatWindowListReq{}
	var list []*OperationsPlatWindowListResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	total = int64(offset + limit)

	for i := int64(0); i < 2; i++ {
		item := &OperationsPlatWindowListResp{
			UserId:           i + 100,
			RechargeAmount:   100.88,
			BetAmount:        200.88,
			PlatProfitAmount: 300.88,
		}
		list = append(list, item)
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// TRX转账汇总
func (this *OperationsDataController) TransTRX(ctx *abugo.AbuHttpContent) {
	var total int64
	errcode := 0
	reqdata := OperationsTransTRXReq{}
	sumResult := new(OperationsTransTRXResp)
	avgResult := new(OperationsTransTRXResp)
	var list []*OperationsTransTRXResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	if 1 == reqdata.Export {
		// 处理时间和导出参数
		if reqdata.EndTime > 0 {
			reqdata.EndTime += 86400000
		}
		reqdata.Page = 1
		reqdata.PageSize = 1000
	}

	tdb := server.Db().GormDao().Table("x_agent_data_date")
	this.GetWhere(reqdata.SellerId, reqdata.ChannelId,
		reqdata.TopAgentId,
		reqdata.StartTime, reqdata.EndTime,
		tdb)
	tdb.Group("x_agent_data_date.RecordDate")

	//ValidUserCondStr := this.GetValidUserCond(reqdata.ValidNewUserCond)
	querySql := tdb.Select(
		"x_agent_data_date.RecordDate AS recordDate",
		"ROUND(SUM(x_agent_data_date.NewTransferTrxBetUsers), 2) AS newUsers",
		"ROUND(SUM(x_agent_data_date.TransferTrxBetUsers - x_agent_data_date.NewTransferTrxBetUsers), 2) AS oldUsers",
		"ROUND(SUM(x_agent_data_date.TransferTrxBetUsers), 2) AS bettors",
		//"ROUND(SUM(x_agent_data_date.TotalPayUsers), 2) AS paymentBettors",
		"ROUND(SUM(x_agent_data_date.TransferTrxBetCount), 2) AS betCount",
		"ROUND(SUM(x_agent_data_date.TransferTrxWinCount), 2) AS winCount",
		"ROUND(SUM(x_agent_data_date.TransferTrxBetAmount), 2) AS betAmount",
		"ROUND(SUM(x_agent_data_date.TransferTrxWinAmount), 2) AS rebateAmount",
		"ROUND(SUM(x_agent_data_date.TransferTrxFeeAmount), 2) AS gasAmount",
		"ROUND(SUM(x_agent_data_date.TransferTrxBetAmount - x_agent_data_date.TransferTrxWinAmount), 2)  AS profitAmountGGR",
		"ROUND(SUM(x_agent_data_date.TyTrxRewardAmount), 2) AS activeAmount",
		"ROUND(SUM(x_agent_data_date.TransferTrxBetAmount - x_agent_data_date.TransferTrxWinAmount - x_agent_data_date.TyTrxRewardAmount), 2)  AS profitAmountNGR",
		//"ROUND(SUM(CASE WHEN x_agent_data_date.NewBetUsers > 0 THEN x_agent_data_date.BetAmount / x_agent_data_date.NewBetUsers ELSE 0 END), 4) AS avgBetAmount",
		"ROUND(SUM(x_agent_data_date.NewTransferTrxBetUsers2), 2) AS d1Retention",
		"ROUND(SUM(x_agent_data_date.NewTransferTrxBetUsers3), 2) AS d3Retention",
		"ROUND(SUM(x_agent_data_date.NewTransferTrxBetUsers7), 2) AS d7Retention",
		"ROUND(SUM(x_agent_data_date.NewTransferTrxBetUsers15), 2) AS d15Retention",
	)

	err := querySql.Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	total = int64(len(list))
	if total <= 0 {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
		return
	}
	list = this.TransTRXDataProper(list)
	sumResult = this.GetTransTRXSum(list)
	avgResult = this.GetTransTRXAvg(sumResult, total)
	list = this.GetTransTRXPageList(list, offset, limit)
	sumResult.DataType = 1
	list = append(list, sumResult)
	avgResult.DataType = 2
	list = append(list, avgResult)

	if 1 == reqdata.Export {
		filename, err := this.TransTRXExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}
}

// TRX转账汇总数据计算
func (this *OperationsDataController) TransTRXDataProper(list []*OperationsTransTRXResp) []*OperationsTransTRXResp {
	for _, v := range list {
		// 计算中奖率: 中奖次数/ 投注次数
		v.WinRate = math.Round(v.WinCount/v.BetCount*100) / 100
		// GGR: 投注 - 返奖
		//v.ProfitAmountGGR = v.BetAmount - v.RebateAmount
		// NGR: GGR - 活动金额 - 佣金
		// 计算平台汇总数据ARPU: NGR / 总投注人数

		// 计算平均投注量: 投注金额 / 投注人数
		v.AvgBetAmount = math.Round(v.BetAmount/float64(v.Bettors)*100) / 100

	}
	return list
}

// TRX转账汇总:二级弹窗
func (this *OperationsDataController) TransTRXWindow(ctx *abugo.AbuHttpContent) {
	var total int64
	var err error
	errcode := 0
	reqdata := OperationsTransTRXWindowReq{}
	var list []*OperationsTransTRXWindowResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}

	if 1 == reqdata.Export {
		reqdata.Page = 1
		reqdata.PageSize = 1000
	}

	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	queryStrSql := this.GetTransTRXWindowCountSql("trx", reqdata.FieldName,
		reqdata.SellerId, reqdata.ChannelId,
		reqdata.TopAgentId, reqdata.DataTime)

	// 扫描结果
	querySql := server.Db().GormDao().Raw(queryStrSql)
	if err = querySql.Scan(&total).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}
	if total <= 0 {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
		return
	}

	queryStrSql = this.GetTransTRXWindowSql("trx", reqdata.FieldName,
		reqdata.SellerId, reqdata.ChannelId,
		reqdata.TopAgentId, reqdata.DataTime,
		limit, offset)

	// 扫描结果
	querySql = server.Db().GormDao().Raw(queryStrSql)
	if err = querySql.Find(&list).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	if 1 == reqdata.Export {
		filename, err := this.TransTRXWindowExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}
}

// 获取TRX转账汇总:二级弹窗sql
func (this *OperationsDataController) GetTransTRXWindowSql(symbol, fieldName string,
	sellerId int,
	channelId, topAgentId []int,
	dateTime int64,
	limit, offset int) string {

	//WITH CTE AS (
	//	SELECT
	//UserId
	//,Address
	//
	//FROM x_custom_dailly
	//WHERE NewGuys=1
	//AND Symbol='trx'
	//AND IsTest=2
	//AND IsGameAddress=2
	//AND BetCount>0
	//AND SellerId=1
	//AND ChannelId=1
	//AND TopAgentId=1
	//AND RecordDate='2025-08-11'
	//GROUP BY UserId,Address
	//)
	//
	//SELECT
	//C.UserId
	//,C.Address
	//,CASE WHEN C.UserId>0 THEN C.UserId ELSE C.Address END AS WalletAddress
	//,IFNULL(SUM(D.BetAmount),0) AS BetAmount
	//,IFNULL(SUM(D.RewardAmount),0) AS RewardAmount
	//,IFNULL(SUM(RechargeAmount),0) AS RechargeAmount
	//FROM CTE AS C
	//INNER JOIN x_custom_dailly AS D
	//ON C.UserId=D.UserId
	//AND C.Address=D.Address
	//LEFT JOIN x_user_recharge_withard_date AS E
	//ON C.UserId=E.UserId
	//AND E.RecordDate='2025-08-11'
	//WHERE D.Symbol='trx'
	//AND D.IsTest=2
	//AND D.IsGameAddress=2
	//AND D.BetCount>0
	//AND D.RecordDate='2025-08-11'
	//GROUP BY 1,2;

	strSellerId := ""
	strChannelId := ""
	strTopAgentId := ""
	if sellerId > 0 {
		strSellerId = fmt.Sprintf("AND SellerId=%d", sellerId)
	}
	if len(channelId) > 0 {
		strChannelId = fmt.Sprintf("AND ChannelId IN %s ", IntSliceToSQLIn(channelId))
	}
	if len(topAgentId) > 0 {
		strTopAgentId = fmt.Sprintf("AND TopAgentId IN %s ", IntSliceToSQLIn(topAgentId))
	}
	strDateTime := fmt.Sprintf("%s", abugo.TimeStampToLocalTime(dateTime))

	rawSql := ""
	strSql := ""
	switch fieldName {
	case "newUsers":
		rawSql = `WITH CTE AS (
  SELECT 
    UserId
    ,Address

  FROM x_custom_dailly 
  WHERE NewGuys=1
  AND Symbol='%s'
  AND IsTest=2
  AND IsGameAddress=2
  AND BetCount>0
  AND SellerId=%d
  AND ChannelId=%d
  AND TopAgentId=%d
  AND RecordDate='%s'
  GROUP BY UserId,Address
)

SELECT
  C.UserId
  ,C.Address
    ,CASE WHEN C.UserId>0 THEN C.UserId ELSE C.Address END AS WalletAddress
    ,IFNULL(SUM(D.BetAmount),0) AS BetAmount
    ,IFNULL(SUM(D.RewardAmount),0) AS RewardAmount
    ,IFNULL(SUM(RechargeAmount),0) AS RechargeAmount
FROM CTE AS C
INNER JOIN x_custom_dailly AS D
  ON C.UserId=D.UserId
  AND C.Address=D.Address
LEFT JOIN x_user_recharge_withard_date AS E
  ON C.UserId=E.UserId
    AND E.RecordDate='%s'
WHERE D.Symbol='%s'
  AND D.IsTest=2
  AND D.IsGameAddress=2
  AND D.BetCount>0
    AND D.RecordDate='%s'
GROUP BY 1,2
LIMIT %d
OFFSET %d
`
	}
	strSql = fmt.Sprintf(rawSql, symbol,
		strSellerId,
		strChannelId, strTopAgentId,
		strDateTime,
		strDateTime,
		symbol,
		strDateTime,
		limit,
		offset)
	return strSql
}

func IntSliceToSQLIn(slice []int) string {
	if len(slice) == 0 {
		return ""
	}

	strSlice := make([]string, len(slice))
	for i, v := range slice {
		strSlice[i] = fmt.Sprintf("%d", v)
	}

	return "(" + strings.Join(strSlice, ",") + ")"
}

// 获取TRX转账汇总:二级弹窗sql
func (this *OperationsDataController) GetTransTRXWindowCountSql(symbol, fieldName string,
	sellerId int,
	channelId, topAgentId []int,
	dateTime int64) string {

	//WITH CTE AS (
	//	SELECT
	//UserId
	//,Address
	//FROM x_custom_dailly
	//WHERE NewGuys=1
	//AND Symbol='trx'
	//AND IsTest=2
	//AND IsGameAddress=2
	//AND BetCount>0
	//AND SellerId=1
	//AND ChannelId=1
	//AND TopAgentId=1
	//AND RecordDate='2025-08-11'
	//GROUP BY UserId,Address
	//)
	//
	//SELECT COUNT(*) AS total_count
	//FROM CTE AS C
	//INNER JOIN x_custom_dailly AS D
	//ON C.UserId=D.UserId
	//AND C.Address=D.Address
	//LEFT JOIN x_user_recharge_withard_date AS E
	//ON C.UserId=E.UserId
	//AND E.RecordDate='2025-08-11'
	//WHERE D.Symbol='trx'
	//AND D.IsTest=2
	//AND D.IsGameAddress=2
	//AND D.BetCount>0
	//AND D.RecordDate='2025-08-11';

	strSellerId := ""
	strChannelId := ""
	strTopAgentId := ""
	if sellerId > 0 {
		strSellerId = fmt.Sprintf("AND SellerId=%d", sellerId)
	}
	if len(channelId) > 0 {
		strChannelId = fmt.Sprintf("AND ChannelId IN %s ", IntSliceToSQLIn(channelId))
	}
	if len(topAgentId) > 0 {
		strTopAgentId = fmt.Sprintf("AND TopAgentId IN %s ", IntSliceToSQLIn(topAgentId))
	}
	strDateTime := fmt.Sprintf("%s", abugo.TimeStampToLocalTime(dateTime))

	rawSql := ""
	strSql := ""
	switch fieldName {
	case "newUsers":
		rawSql = `WITH CTE AS (
  SELECT 
    UserId
    ,Address
  FROM x_custom_dailly 
  WHERE NewGuys=1
  AND Symbol='%s'
  AND IsTest=2
  AND IsGameAddress=2
  AND BetCount>0
  %s
  %s
  %s
  AND RecordDate='%s'
  GROUP BY UserId,Address
)

SELECT COUNT(*) AS total_count
FROM CTE AS C
INNER JOIN x_custom_dailly AS D
  ON C.UserId=D.UserId
  AND C.Address=D.Address
LEFT JOIN x_user_recharge_withard_date AS E
  ON C.UserId=E.UserId
    AND E.RecordDate='%s'
WHERE D.Symbol='%s'
  AND D.IsTest=2
  AND D.IsGameAddress=2
  AND D.BetCount>0
  AND D.RecordDate='%s'
`
	}

	strSql = fmt.Sprintf(rawSql, symbol,
		strSellerId,
		strChannelId, strTopAgentId,
		strDateTime,
		strDateTime,
		symbol,
		strDateTime)
	return strSql
}

// 导出Excel文件
func (this *OperationsDataController) TransTRXWindowExportExcel(list []*OperationsTransTRXWindowResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"钱包地址", "投注金额", "平台收益"}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				row := []interface{}{d.WalletAddress, d.BetAmount, d.PlatProfitAmount}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "trans_trx_window_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

// USDT转账汇总
func (this *OperationsDataController) TransUSDT(ctx *abugo.AbuHttpContent) {
	var total int64
	errcode := 0
	reqdata := OperationsTransUSDTReq{}
	sumResult := new(OperationsTransUSDTResp)
	avgResult := new(OperationsTransUSDTResp)
	var list []*OperationsTransUSDTResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	if 1 == reqdata.Export {
		// 处理时间和导出参数
		if reqdata.EndTime > 0 {
			reqdata.EndTime += 86400000
		}
		reqdata.Page = 1
		reqdata.PageSize = 1000
	}

	tdb := server.Db().GormDao().Table("x_agent_data_date")
	this.GetWhere(reqdata.SellerId, reqdata.ChannelId,
		reqdata.TopAgentId,
		reqdata.StartTime, reqdata.EndTime,
		tdb)
	tdb.Group("x_agent_data_date.RecordDate")

	//ValidUserCondStr := this.GetValidUserCond(reqdata.ValidNewUserCond)
	querySql := tdb.Select(
		"x_agent_data_date.RecordDate AS recordDate",
		"ROUND(SUM(x_agent_data_date.NewTransferUsdtBetUsers), 2) AS newUsers",
		"ROUND(SUM(x_agent_data_date.TransferUsdtBetUsers - x_agent_data_date.NewTransferUsdtBetUsers), 2) AS oldUsers",
		"ROUND(SUM(x_agent_data_date.TransferUsdtBetUsers), 2) AS bettors",
		"ROUND(SUM(x_agent_data_date.TransferUsdtBetCount), 2) AS betCount",
		"ROUND(SUM(x_agent_data_date.TransferUsdtWinCount), 2) AS winCount",
		//"ROUND(SUM(TransferUsdtWinAmount), 2) AS winRate",
		"ROUND(SUM(x_agent_data_date.TransferUsdtBetAmount), 2) AS betAmount",
		"ROUND(SUM(x_agent_data_date.TransferUsdtWinAmount), 2) AS rebateAmount",
		"ROUND(SUM(x_agent_data_date.TransferUsdtFeeAmount), 2) AS gasAmount",
		"ROUND(SUM(x_agent_data_date.TransferUsdtBetAmount - x_agent_data_date.TransferUsdtWinAmount), 2)  AS profitAmountGGR",
		//"ROUND(SUM(CASE WHEN x_agent_data_date.BetUsers > 0 THEN x_agent_data_date.BetAmount / x_agent_data_date.BetUsers ELSE 0 END), 4) AS avgBetAmount",
		"ROUND(SUM(x_agent_data_date.NewTransferUsdtBetUsers2), 2) AS d1Retention",
		"ROUND(SUM(x_agent_data_date.NewTransferUsdtBetUsers3), 2) AS d3Retention",
		"ROUND(SUM(x_agent_data_date.NewTransferUsdtBetUsers7), 2) AS d7Retention",
		"ROUND(SUM(x_agent_data_date.NewTransferUsdtBetUsers15), 2) AS d15Retention",
	)

	err := querySql.Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	total = int64(len(list))
	if total > 0 {
		sumResult = this.GetTransUSDTSum(list)
		avgResult = this.GetTransUSDTAvg(sumResult, total)
	}
	respList := this.GetTransUSDTPageList(list, offset, limit)
	sumResult.DataType = 1
	respList = append(respList, sumResult)
	avgResult.DataType = 2
	respList = append(respList, avgResult)

	if 1 == reqdata.Export {
		list = append(list, sumResult)
		avgResult.DataType = 2
		list = append(list, avgResult)
		filename, err := this.TransUSDTExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", respList)
		ctx.Put("total", total)
		ctx.RespOK()
	}
}

// USDt转账汇总数据计算
func (this *OperationsDataController) TransUSDTDataProper(list []*OperationsTransUSDTResp) []*OperationsTransUSDTResp {
	for _, v := range list {
		// 计算中奖率: 中奖次数/ 投注次数
		v.WinRate = math.Round(v.WinCount/v.BetCount*100) / 100
		// 计算平均投注量: 投注金额 / 投注人数
		v.AvgBetAmount = math.Round(v.BetAmount/float64(v.Bettors)*100) / 100

	}
	return list
}

// USDT转账汇总:二级弹窗
func (this *OperationsDataController) TransUSDTWindow(ctx *abugo.AbuHttpContent) {
	var total int64
	var err error
	errcode := 0
	reqdata := OperationsTransUSDTWindowReq{}
	var list []*OperationsTransUSDTWindowResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}

	if 1 == reqdata.Export {
		reqdata.Page = 1
		reqdata.PageSize = 1000
	}

	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	queryStrSql := this.GetTransTRXWindowCountSql("usdt", reqdata.FieldName,
		reqdata.SellerId, reqdata.ChannelId,
		reqdata.TopAgentId, reqdata.DataTime)

	// 扫描结果
	querySql := server.Db().GormDao().Raw(queryStrSql)
	if err = querySql.Scan(&total).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}
	if total <= 0 {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
		return
	}

	queryStrSql = this.GetTransTRXWindowSql("usdt", reqdata.FieldName,
		reqdata.SellerId, reqdata.ChannelId,
		reqdata.TopAgentId, reqdata.DataTime,
		limit, offset)

	// 扫描结果
	querySql = server.Db().GormDao().Raw(queryStrSql)
	if err = querySql.Find(&list).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	if 1 == reqdata.Export {
		filename, err := this.TransUSDTWindowExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}
}

// 导出Excel文件
func (this *OperationsDataController) TransUSDTWindowExportExcel(list []*OperationsTransUSDTWindowResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"钱包地址", "投注金额", "平台收益"}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				row := []interface{}{d.WalletAddress, d.BetAmount, d.PlatProfitAmount}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "trans_usdt_window_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

// 余额玩法汇总
func (this *OperationsDataController) BalanceGameplay(ctx *abugo.AbuHttpContent) {
	var total int64
	errcode := 0
	reqdata := OperationsBalanceGameplayReq{}
	sumResult := new(OperationsBalanceGameplayResp)
	avgResult := new(OperationsBalanceGameplayResp)
	var list []*OperationsBalanceGameplayResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	if 1 == reqdata.Export {
		// 处理时间和导出参数
		if reqdata.EndTime > 0 {
			reqdata.EndTime += 86400000
		}
		reqdata.Page = 1
		reqdata.PageSize = 1000
	}

	tdb := server.Db().GormDao().Table("x_agent_data_date")
	this.GetWhere(reqdata.SellerId, reqdata.ChannelId,
		reqdata.TopAgentId,
		reqdata.StartTime, reqdata.EndTime,
		tdb)
	tdb.Group("x_agent_data_date.RecordDate")

	ValidUserCondStr := this.GetValidRechargeUserCond(reqdata.ValidRechargeUserCond)
	querySql := tdb.Select(
		"x_agent_data_date.RecordDate AS recordDate",
		"ROUND(SUM(x_agent_data_date.NewRechargeUsers), 2) AS firstRechargeUsers",
		ValidUserCondStr,
		"ROUND(SUM(x_agent_data_date.NewRechargeAmount), 2) AS firstRechargeAmount",
		"ROUND(SUM(x_agent_data_date.RechargeUsers), 2) AS rechargeCount",
		"ROUND(SUM(x_agent_data_date.RechargeAmount), 2) AS rechargeAmount",
		"ROUND(SUM(x_agent_data_date.WithdrawUsers), 2) AS withdrawCount",
		"ROUND(SUM(x_agent_data_date.WithdrawAmount), 2) AS withdrawAmount",
		"ROUND(SUM(x_agent_data_date.withdrawAmount - x_agent_data_date.VipRewardAmount - x_agent_data_date.GetCommissionAmount), 2) AS withdrawAmount2",
		"ROUND(SUM(x_agent_data_date.RechargeAmount - x_agent_data_date.WithdrawAmount), 2) AS ctDiff",
		"ROUND(SUM(x_agent_data_date.TotalBetUsers), 2) AS totalBettors",
		"ROUND(SUM(x_agent_data_date.TotalBetUsers), 2)  AS paymentBettors",
		"ROUND(SUM(x_agent_data_date.TotalBetCount), 2) AS betCount",
		"ROUND(SUM(x_agent_data_date.TransferUsdtWinCount +  x_agent_data_date.TransferTrxWinCount +  x_agent_data_date.WinCount), 2) AS winCount",
		"ROUND(SUM(x_agent_data_date.TotalBetAmount), 2) AS betAmount",
		"ROUND(SUM(x_agent_data_date.TotalWinAmount), 2) AS rebateAmount",
		"ROUND(SUM(x_agent_data_date.TotalFeeAmount), 2) AS gasAmount",
		//"ROUND(SUM(x_agent_data_date.TotalBetAmount - x_agent_data_date.TotalWinAmount), 2)  AS profitAmountGGR",
		//"ROUND(SUM(x_agent_data_date.TotalBetAmount - x_agent_data_date.TotalWinAmountt + x_agent_data_date.TotalFeeAmount), 2)  AS profitAmountNGR",
		//"ROUND(SUM(CASE WHEN x_agent_data_date.TotalBetUsers > 0 THEN x_agent_data_date.TotalBetAmount / x_agent_data_date.TotalBetUsers ELSE 0 END), 2) AS avgBetAmount",
		"ROUND(SUM(x_agent_data_date.NewRechargeUsers2), 2) AS d1Retention",
		"ROUND(SUM(x_agent_data_date.NewRechargeUsers3), 2) AS d3Retention",
		"ROUND(SUM(x_agent_data_date.NewRechargeUsers7), 2) AS d7Retention",
		"ROUND(SUM(x_agent_data_date.NewRechargeUsers15), 2) AS d15Retention",
	)

	err := querySql.Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	total = int64(len(list))
	if total <= 0 {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
		return
	}
	list = this.BalanceGameplayDataProper(list)
	sumResult = this.GetBalanceSum(list)
	avgResult = this.GetBalanceAvg(sumResult, total)

	list = this.GetBalancePageList(list, offset, limit)
	sumResult.DataType = 1
	list = append(list, sumResult)
	avgResult.DataType = 2
	list = append(list, avgResult)

	if 1 == reqdata.Export {
		filename, err := this.GamePlayExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}
}

// 余额玩法汇总: 可以进行字段数据相应计算
func (this *OperationsDataController) BalanceGameplayDataProper(list []*OperationsBalanceGameplayResp) []*OperationsBalanceGameplayResp {
	for _, v := range list {
		// 计算GGR: 投注金额 - 返奖金额
		v.ProfitAmountGGR = v.BetAmount - v.RebateAmount
		// 计算NGR: 投注金额 - 返奖金额 + 手续费
		v.ProfitAmountNGR = v.ProfitAmountGGR + v.GasAmount
		// 计算平均投注量: 投注金额 / 投注人数
		if v.TotalBettors > 0 {
			v.AvgBetAmount = v.BetAmount / v.TotalBettors
		}
	}
	return list
}

// 余额玩法汇总:二级弹窗
func (this *OperationsDataController) BalanceGameplayWindow(ctx *abugo.AbuHttpContent) {
	var total int64
	errcode := 0
	reqdata := OperationsBalanceGameplayWindowReq{}
	var list []*OperationsBalanceGameplayWindowResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "运营总报表", "查", "查看运营总报表")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	total = int64(offset + limit)

	for i := int64(0); i < 2; i++ {
		item := &OperationsBalanceGameplayWindowResp{
			UserId:           i + 100,
			RechargeAmount:   100.88,
			BetAmount:        200.88,
			PlatProfitAmount: 300.88,
		}
		list = append(list, item)
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 平台汇总-上传补充数据
func (this *OperationsDataController) PlatUpload(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 获取token进行权限验证
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "运营总报表", "改"), &errcode, "权限不足") {
		return
	}

	// 获取上传的文件
	file, header, err := ctx.Gin().Request.FormFile("file")
	if err != nil {
		ctx.RespErrString(false, &errcode, "获取上传文件失败: "+err.Error())
		return
	}
	defer file.Close()

	// 验证文件类型
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".csv") {
		ctx.RespErrString(false, &errcode, "文件格式错误，请上传CSV文件")
		return
	}

	// 解析CSV文件
	records, err := this.parseCSVFile(file)
	if err != nil {
		ctx.RespErrString(false, &errcode, "解析CSV文件失败: "+err.Error())
		return
	}

	// 验证CSV数据格式
	if len(records) < 2 {
		ctx.RespErrString(false, &errcode, "CSV文件内容为空，至少需要2行数据（包含表头）")
		return
	}

	// 忽略表头，直接从第二行开始处理数据
	// 验证第一行数据的列数是否足够
	if len(records) > 1 && len(records[1]) < 3 {
		ctx.RespErrString(false, &errcode, "CSV文件数据格式错误")
		return
	}

	// 处理数据行
	var updateCount int
	var errorCount int
	var errorMessages []string
	itemList := make([]*model.XOperationsInputDataDate, 0)
	tdb := server.Db().GormDao().Table("x_operations_input_data_date")

	for i, record := range records[1:] {
		if len(record) < 2 {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行数据不完整", i+2))
			continue
		}

		item := new(model.XOperationsInputDataDate)
		// 解析日期
		dateStr := strings.TrimSpace(record[0])
		recordDate, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行日期格式错误: %s", i+2, dateStr))
			continue
		}

		// 使用本地时区，确保日期时间为当天的 00:00:00
		loc, _ := time.LoadLocation("Local")
		recordDate = time.Date(recordDate.Year(), recordDate.Month(), recordDate.Day(), 0, 0, 0, 0, loc)
		item.RecordDate = recordDate
		// 添加调试信息，显示实际使用的日期时间
		fmt.Printf("第%d行：原始日期字符串: %s, 解析后的日期时间: %s\n", i+2, dateStr, recordDate.Format("2006-01-02 15:04:05"))

		// 解析运营商号
		sellerStr := strings.TrimSpace(record[1])
		sellerID, err := strconv.ParseInt(sellerStr, 10, 32)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, sellerStr))
			continue
		}
		item.SellerID = int32(sellerID)
		// 解析其它扣除金额
		otherAmountStr := strings.TrimSpace(record[2])
		otherAmount, err := strconv.ParseFloat(otherAmountStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, otherAmount))
			continue
		}
		item.SubOtherAmount = otherAmount

		// 解析资源部
		resourcesStr := strings.TrimSpace(record[3])
		resources, err := strconv.ParseFloat(otherAmountStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, resourcesStr))
			continue
		}
		item.ResourcesAmount = resources

		// 解析市场部
		marketStr := strings.TrimSpace(record[4])
		market, err := strconv.ParseFloat(marketStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, marketStr))
			continue
		}
		item.MarketAmount = market

		// 解析广告部
		adAmountStr := strings.TrimSpace(record[5])
		adAmount, err := strconv.ParseFloat(adAmountStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, adAmountStr))
			continue
		}
		item.AgentAmount = adAmount

		// 解析运营部
		opAmountStr := strings.TrimSpace(record[6])
		opAmount, err := strconv.ParseFloat(opAmountStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, opAmountStr))
			continue
		}
		item.OpAmount = opAmount

		// 解析代理分红收支
		agentAmountStr := strings.TrimSpace(record[7])
		agentAmount, err := strconv.ParseFloat(agentAmountStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行运营商号格式错误: %s", i+2, agentAmountStr))
			continue
		}
		item.AgentAmount = agentAmount

		itemList = append(itemList, item)
		updateCount++
	}

	if len(itemList) > 0 {
		err = tdb.CreateInBatches(itemList, len(itemList)).Error
		if err != nil {
			logs.Error("批量插入数据错误：", err)
			ctx.RespErr(err, &errcode)
			return
		}
	}

	logs.Info("CSV文件解析成功，共%d行数据（包含表头），将处理%d行数据\n", len(records), len(records)-1)
	fmt.Printf("CSV文件解析成功，共%d行数据（包含表头），将处理%d行数据\n", len(records), len(records)-1)
}

// parseCSVFile 解析CSV文件，支持多种编码格式
func (this *OperationsDataController) parseCSVFile(file multipart.File) ([][]string, error) {
	// 重置文件指针到开头
	file.Seek(0, 0)

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 尝试检测和转换编码
	var reader *csv.Reader

	// 检查是否为UTF-8编码
	if utf8.Valid(content) {
		reader = csv.NewReader(strings.NewReader(string(content)))
	} else {
		// 尝试GBK编码转换
		gbkContent, err := this.convertGBKToUTF8(content)
		if err != nil {
			// 如果GBK转换失败，尝试直接使用原内容
			reader = csv.NewReader(strings.NewReader(string(content)))
		} else {
			reader = csv.NewReader(strings.NewReader(gbkContent))
		}
	}

	// 读取所有记录
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("解析CSV失败: %v", err)
	}

	return records, nil
}

// convertGBKToUTF8 将GBK编码转换为UTF-8
func (this *OperationsDataController) convertGBKToUTF8(gbkData []byte) (string, error) {
	// 尝试多种编码转换方式

	// 方法1：直接转换为字符串
	str := string(gbkData)

	// 方法2：尝试检测BOM并处理
	if len(gbkData) >= 3 {
		// 检查UTF-8 BOM
		if gbkData[0] == 0xEF && gbkData[1] == 0xBB && gbkData[2] == 0xBF {
			return string(gbkData[3:]), nil
		}
		// 检查UTF-16 BOM
		if (gbkData[0] == 0xFF && gbkData[1] == 0xFE) || (gbkData[0] == 0xFE && gbkData[1] == 0xFF) {
			return string(gbkData[2:]), nil
		}
	}

	// 方法3：尝试替换常见的乱码字符
	str = this.fixCommonEncodingIssues(str)

	// 直接返回处理后的字符串
	return str, nil
}

// fixCommonEncodingIssues 修复常见的编码问题
func (this *OperationsDataController) fixCommonEncodingIssues(str string) string {
	// 替换常见的乱码模式
	replacements := map[string]string{
		"??":   "",
		"???":  "",
		"????": "",
	}

	for old, new := range replacements {
		str = strings.ReplaceAll(str, old, new)
	}

	return str
}

// 导出Excel文件
func (this *OperationsDataController) PlatExportExcel(list []*OperationsPlatListResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"日期", "总投注人数", "新增投注人数", "旧用户投注人数", "付费投注总人数",
		"新增付费人数", "旧付费人数", "总有效付费人数", "新增有效付费人数", "旧有效付费人数",
		"投注次数", "投注金额", "返奖金额", "盘内盈利 (负数客赢)GGR", "手续费",
		"活动金额", "扣除活动金额", "其它扣除金额", "　VIP返水", "佣金",
		"资源部", "市场部", "广告部", "运营部", "代理分红收支",
		"实际盘内盈利 (负数客赢)NGR", "ARPU", "ARPPU", "付费率", "平均投注量ATPU",
	}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				recordDateStr := this.timePtrToDayString(d.RecordDate)
				switch d.DataType {
				case 1:
					recordDateStr = "合 计"
				case 2:
					recordDateStr = "每日平均统计"
				}

				row := []interface{}{
					recordDateStr, d.TotalBettors, d.NewBettors, d.OldBettors, d.PaymentBettors,
					d.NewPaymentUsers, d.OldPaymentUsers, d.ValidPaymentUsers, d.NewValidPaymentUsers, d.OldValidPaymentUsers,
					d.BetCount, d.BetAmount, d.RebateAmount, d.ProfitAmountGGR, d.GasAmount,
					d.ActiveAmount, d.SubActiveAmount, d.SubOtherAmount, d.VipRebateAmount, d.CommissionAmount,
					d.ResourcesAmount, d.MarketAmount, d.AdAmount, d.OpAmount, d.AgentAmount,
					d.ProfitAmountNGR, d.ARPUAmount, d.ARPPUAmount, d.PaymentRate, d.AvgBetAmount,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "plat_sum_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

// 导出Excel文件
func (this *OperationsDataController) TransTRXExportExcel(list []*OperationsTransTRXResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"日期", "新增会员人数", "有效新增人数", "老会员人数", "投注人数",
		"付费投注人数", "投注次数", "中奖次数", "中奖率", "投注金额",
		"返奖金额", "系统磨损", "盘内盈利 (负数客赢)GGR", "活动金额", "实际盘内盈利 (负数客赢)NGR",
		"平均投注量", "次日留存", "3日留存", "　7日留存", "15日留存",
	}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				recordDateStr := this.timePtrToDayString(d.RecordDate)
				switch d.DataType {
				case 1:
					recordDateStr = "合 计"
				case 2:
					recordDateStr = "每日平均统计"
				}

				row := []interface{}{
					recordDateStr, d.NewUsers, d.ValidNewUsers, d.OldUsers, d.Bettors,
					d.Bettors, d.PaymentBettors, d.BetCount, d.BetAmount, d.RebateAmount,
					d.GasAmount, d.ProfitAmountGGR, d.ActiveAmount, d.ProfitAmountNGR, d.AvgBetAmount,
					d.D1Retention, d.D3Retention, d.D7Retention, d.D15Retention,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "trans_trx_sum_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

func (this *OperationsDataController) timePtrToDayString(t *time.Time) string {
	if t == nil {
		return "" // 或根据需求返回默认值，如 "0000-00-00"
	}
	return t.Format("2006-01-02") // 输出格式示例: "2023-10-05"
}

// 导出Excel文件
func (this *OperationsDataController) TransUSDTExportExcel(list []*OperationsTransUSDTResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"日期", "新增会员人数", "有效新增人数", "老会员人数", "投注人数",
		"投注次数", "中奖次数", "中奖率", "投注金额", "返奖金额",
		"系统磨损", "盘内盈利 (负数客赢)GGR", "平均投注量", "次日留存", "3日留存",
		"7日留存", "15日留存",
	}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				recordDateStr := this.timePtrToDayString(d.RecordDate)
				switch d.DataType {
				case 1:
					recordDateStr = "合 计"
				case 2:
					recordDateStr = "每日平均统计"
				}

				row := []interface{}{
					recordDateStr, d.NewUsers, d.ValidNewUsers, d.OldUsers, d.Bettors,
					d.BetCount, d.BetAmount, d.WinCount, d.WinRate, d.RebateAmount,
					d.GasAmount, d.ProfitAmountGGR, d.AvgBetAmount, d.D1Retention, d.D3Retention,
					d.D7Retention, d.D15Retention,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "trans_usdt_sum_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

func (this *OperationsDataController) GamePlayExportExcel(list []*OperationsBalanceGameplayResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"日期", "首充人数", "首充金额", "充值人数", "有效充值人数",
		"充值金额", "提现人数", "提现金额", "去除返水返佣提现金额", "充提差",
		"投注人数", "付费投注人数", "投注次数", "中奖次数", "投注金额",
		"返奖金额R", "手续费", "盘内盈利 (负数客赢)GGR", "实际盘内盈利 (负数客赢)NGR", "平均投注量",
		"次日留存", "3日留存", "7日留存", "15日留存",
	}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				recordDateStr := this.timePtrToDayString(d.RecordDate)
				switch d.DataType {
				case 1:
					recordDateStr = "合 计"
				case 2:
					recordDateStr = "每日平均统计"
				}

				row := []interface{}{
					recordDateStr, d.FirstRechargeUsers, d.FirstRechargeAmount, d.RechargeCount, d.ValidRechargeCount,
					d.RechargeAmount, d.WithdrawCount, d.WithdrawAmount, d.WithdrawAmount2, d.CTDiff,
					d.TotalBettors, d.PaymentBettors, d.BetCount, d.WinCount, d.BetAmount,
					d.RebateAmount, d.GasAmount, d.ProfitAmountGGR, d.ProfitAmountNGR, d.AvgBetAmount,
					d.D1Retention, d.D3Retention, d.D7Retention, d.D15Retention,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "balance_gameplay_sum_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

func (this *OperationsDataController) CommonWindowExportExcel(list []*CommonWindowResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"玩家ID", "充值金额", "投注金额", "平台收益"}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				row := []interface{}{
					d.UserId, d.RechargeAmount,
					d.BetAmount, d.PlatProfitAmount,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "common_window_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

func (this *OperationsDataController) GetValidUserCond(cond int) string {
	ValidRechargeCondStr := ""
	switch cond {
	case ValidNewUserCond1:
		ValidRechargeCondStr = "ROUND(SUM(x_agent_data_date.ValidRecharge10Users), 2) AS validNewUsers "
	case ValidNewUserCond2:
		ValidRechargeCondStr = "ROUND(SUM(x_agent_data_date.ValidRecharge50Users), 2) AS validNewUsers "
	case ValidNewUserCond3:
		ValidRechargeCondStr = "ROUND(SUM(x_agent_data_date.ValidRecharge100Users), 2) AS validNewUsers "
	}
	return ValidRechargeCondStr
}

func (this *OperationsDataController) GetValidRechargeUserCond(cond int) string {
	ValidRechargeCondStr := ""
	switch cond {
	case ValidNewUserCond1:
		ValidRechargeCondStr = "ROUND(SUM(x_agent_data_date.ValidRecharge10Users), 2) AS validRechargeCount "
	case ValidNewUserCond2:
		ValidRechargeCondStr = "ROUND(SUM(x_agent_data_date.ValidRecharge50Users), 2) AS validRechargeCount "
	case ValidNewUserCond3:
		ValidRechargeCondStr = "ROUND(SUM(x_agent_data_date.ValidRecharge100Users), 2) AS validRechargeCount "
	}
	return ValidRechargeCondStr
}

func (this *OperationsDataController) GetWhere(sellerId int,
	channelId, topAgentId []int,
	startTime, EndTime int64, db *gorm.DB) {
	// 商户id
	if sellerId > 0 {
		db.Where("x_agent_data_date.SellerId = ?", sellerId)
	}

	// 渠道id
	if len(channelId) > 0 {
		db.Where("x_agent_data_date.ChannelId IN ?", channelId)
	}

	// 顶级代理id
	if len(topAgentId) > 0 {
		db.Where("x_agent_data_date.TopAgentId IN ?", topAgentId)
	}

	//时间区间
	if startTime > 0 {
		db.Where("x_agent_data_date.RecordDate >= ?", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	if EndTime > 0 && startTime > EndTime {
		db.Where("x_agent_data_date.RecordDate <= ?", abugo.TimeStampToLocalTime(EndTime))
	}

}

// 获取总和
func (this *OperationsDataController) GetTransTRXSum(list []*OperationsTransTRXResp) *OperationsTransTRXResp {
	sumResult := new(OperationsTransTRXResp)
	for _, item := range list {
		sumResult.NewUsers += item.NewUsers
		sumResult.ValidNewUsers += item.ValidNewUsers
		sumResult.OldUsers += item.OldUsers
		sumResult.Bettors += item.Bettors
		sumResult.PaymentBettors += item.PaymentBettors

		sumResult.BetCount += item.BetCount
		sumResult.WinCount += item.WinCount
		sumResult.WinRate += item.WinRate
		sumResult.BetAmount += item.BetAmount
		sumResult.RebateAmount += item.RebateAmount

		sumResult.GasAmount += item.GasAmount
		sumResult.ProfitAmountGGR += item.ProfitAmountGGR
		sumResult.ActiveAmount += item.ActiveAmount
		sumResult.ProfitAmountNGR += item.ProfitAmountNGR
		sumResult.AvgBetAmount += item.AvgBetAmount

		sumResult.D1Retention += item.D1Retention
		sumResult.D3Retention += item.D3Retention
		sumResult.D7Retention += item.D7Retention
		sumResult.D15Retention += item.D15Retention
	}
	return sumResult
}

// 获取平均值
func (this *OperationsDataController) GetTransTRXAvg(sum *OperationsTransTRXResp, cnt int64) *OperationsTransTRXResp {
	avgResult := new(OperationsTransTRXResp)
	switch cnt {
	case 0:
		return avgResult
	case 1:
		avgResult.NewUsers = sum.NewUsers
		avgResult.ValidNewUsers = sum.ValidNewUsers
		avgResult.OldUsers = sum.OldUsers
		avgResult.Bettors = sum.Bettors
		avgResult.PaymentBettors = sum.PaymentBettors

		avgResult.BetCount = sum.BetCount
		avgResult.WinCount = sum.WinCount
		avgResult.WinRate = sum.WinRate
		avgResult.BetAmount = sum.BetAmount
		avgResult.RebateAmount = sum.RebateAmount

		avgResult.GasAmount = sum.GasAmount
		avgResult.ProfitAmountGGR = sum.ProfitAmountGGR
		avgResult.ActiveAmount = sum.ActiveAmount
		avgResult.ProfitAmountNGR = sum.ProfitAmountNGR
		avgResult.AvgBetAmount = sum.AvgBetAmount

		avgResult.D1Retention = sum.D1Retention
		avgResult.D3Retention = sum.D3Retention
		avgResult.D7Retention = sum.D7Retention
		avgResult.D15Retention = sum.D15Retention
	default:
		avgResult.NewUsers = math.Round(sum.NewUsers/float64(cnt)*100) / 100
		avgResult.ValidNewUsers = math.Round(sum.ValidNewUsers/float64(cnt)*100) / 100
		avgResult.OldUsers = math.Round(sum.OldUsers/float64(cnt)*100) / 100
		avgResult.Bettors = math.Round(sum.Bettors/float64(cnt)*100) / 100
		avgResult.PaymentBettors = math.Round(sum.PaymentBettors/float64(cnt)*100) / 100

		avgResult.BetCount = math.Round(sum.BetCount/float64(cnt)*100) / 100
		avgResult.WinCount = math.Round(sum.WinCount/float64(cnt)*100) / 100
		avgResult.WinRate = math.Round(sum.WinRate/float64(cnt)*100) / 100
		avgResult.BetAmount = math.Round(sum.BetAmount/float64(cnt)*100) / 100
		avgResult.RebateAmount = math.Round(sum.RebateAmount/float64(cnt)*100) / 100

		avgResult.GasAmount = math.Round(sum.GasAmount/float64(cnt)*100) / 100
		avgResult.ProfitAmountGGR = math.Round(sum.ProfitAmountGGR/float64(cnt)*100) / 100
		avgResult.ActiveAmount = math.Round(sum.ActiveAmount/float64(cnt)*100) / 100
		avgResult.ProfitAmountNGR = math.Round(sum.ProfitAmountNGR/float64(cnt)*100) / 100
		avgResult.AvgBetAmount = math.Round(sum.AvgBetAmount/float64(cnt)*100) / 100

		avgResult.D1Retention = math.Round(sum.D1Retention/float64(cnt)*100) / 100
		avgResult.D3Retention = math.Round(sum.D3Retention/float64(cnt)*100) / 100
		avgResult.D7Retention = math.Round(sum.D7Retention/float64(cnt)*100) / 100
		avgResult.D15Retention = math.Round(sum.D15Retention/float64(cnt)*100) / 100
	}
	return avgResult
}

// 获取分页数据
func (this *OperationsDataController) GetTransTRXPageList(list []*OperationsTransTRXResp, offset, limit int) []*OperationsTransTRXResp {
	if len(list) <= 1 {
		return list
	}
	end := offset + limit
	if end >= len(list) {
		end = len(list)
	}
	retList := make([]*OperationsTransTRXResp, end-offset)
	copy(retList, list[offset:end])
	return retList
}

// 获取总和
func (this *OperationsDataController) GetTransUSDTSum(list []*OperationsTransUSDTResp) *OperationsTransUSDTResp {
	sumResult := new(OperationsTransUSDTResp)
	for _, item := range list {
		sumResult.NewUsers += item.NewUsers
		sumResult.ValidNewUsers += item.ValidNewUsers
		sumResult.OldUsers += item.OldUsers
		sumResult.Bettors += item.Bettors
		sumResult.BetCount += item.BetCount

		sumResult.WinCount += item.WinCount
		sumResult.WinRate += item.WinRate
		sumResult.BetAmount += item.BetAmount
		sumResult.RebateAmount += item.RebateAmount
		sumResult.GasAmount += item.GasAmount

		sumResult.ProfitAmountGGR += item.ProfitAmountGGR
		sumResult.AvgBetAmount += item.AvgBetAmount
		sumResult.D1Retention += item.D1Retention
		sumResult.D3Retention += item.D3Retention
		sumResult.D7Retention += item.D7Retention

		sumResult.D15Retention += item.D15Retention
	}
	return sumResult
}

// 获取平均值
func (this *OperationsDataController) GetTransUSDTAvg(sum *OperationsTransUSDTResp, cnt int64) *OperationsTransUSDTResp {
	avgResult := new(OperationsTransUSDTResp)
	switch cnt {
	case 0:
		return avgResult
	case 1:
		avgResult.NewUsers = sum.NewUsers
		avgResult.ValidNewUsers = sum.ValidNewUsers
		avgResult.OldUsers = sum.OldUsers
		avgResult.Bettors = sum.Bettors
		avgResult.BetCount = sum.BetCount

		avgResult.WinCount = sum.WinCount
		avgResult.WinRate = sum.WinRate
		avgResult.BetAmount = sum.BetAmount
		avgResult.RebateAmount = sum.RebateAmount
		avgResult.GasAmount = sum.GasAmount

		avgResult.ProfitAmountGGR = sum.ProfitAmountGGR
		avgResult.AvgBetAmount = sum.AvgBetAmount
		avgResult.D1Retention = sum.D1Retention
		avgResult.D3Retention = sum.D3Retention
		avgResult.D7Retention = sum.D7Retention

		avgResult.D15Retention = sum.D15Retention
	default:
		avgResult.NewUsers = math.Round(sum.NewUsers/float64(cnt)*100) / 100
		avgResult.ValidNewUsers = math.Round(sum.ValidNewUsers/float64(cnt)*100) / 100
		avgResult.OldUsers = math.Round(sum.OldUsers/float64(cnt)*100) / 100
		avgResult.Bettors = math.Round(sum.Bettors/float64(cnt)*100) / 100
		avgResult.BetCount = math.Round(sum.BetCount/float64(cnt)*100) / 100

		avgResult.WinCount = math.Round(sum.WinCount/float64(cnt)*100) / 100
		avgResult.WinRate = math.Round(sum.WinRate/float64(cnt)*100) / 100
		avgResult.BetAmount = math.Round(sum.BetAmount/float64(cnt)*100) / 100
		avgResult.RebateAmount = math.Round(sum.RebateAmount/float64(cnt)*100) / 100
		avgResult.GasAmount = math.Round(sum.GasAmount/float64(cnt)*100) / 100

		avgResult.ProfitAmountGGR = math.Round(sum.ProfitAmountGGR/float64(cnt)*100) / 100
		avgResult.AvgBetAmount = math.Round(sum.AvgBetAmount/float64(cnt)*100) / 100
		avgResult.D1Retention = math.Round(sum.D1Retention/float64(cnt)*100) / 100
		avgResult.D3Retention = math.Round(sum.D3Retention/float64(cnt)*100) / 100
		avgResult.D7Retention = math.Round(sum.D7Retention/float64(cnt)*100) / 100

		avgResult.D15Retention = math.Round(sum.D15Retention/float64(cnt)*100) / 100
	}
	return avgResult
}

// 获取分页数据
func (this *OperationsDataController) GetTransUSDTPageList(list []*OperationsTransUSDTResp, offset, limit int) []*OperationsTransUSDTResp {
	if len(list) <= 1 {
		return list
	}
	end := offset + limit
	if end >= len(list) {
		end = len(list)
	}
	retList := make([]*OperationsTransUSDTResp, end-offset)
	copy(retList, list[offset:end])
	return retList
}

// 获取总和
func (this *OperationsDataController) GetBalanceSum(list []*OperationsBalanceGameplayResp) *OperationsBalanceGameplayResp {
	sumResult := new(OperationsBalanceGameplayResp)
	for _, item := range list {
		sumResult.FirstRechargeUsers += item.FirstRechargeUsers
		sumResult.FirstRechargeAmount += item.FirstRechargeAmount
		sumResult.RechargeCount += item.RechargeCount
		sumResult.RechargeAmount += item.RechargeAmount
		sumResult.WithdrawCount += item.WithdrawCount

		sumResult.WithdrawAmount += item.WithdrawAmount
		sumResult.WithdrawAmount2 += item.WithdrawAmount2
		sumResult.CTDiff += item.CTDiff
		sumResult.TotalBettors += item.TotalBettors
		sumResult.PaymentBettors += item.PaymentBettors

		sumResult.BetCount += item.BetCount
		sumResult.WinCount += item.WinCount
		sumResult.BetAmount += item.BetAmount
		sumResult.RebateAmount += item.RebateAmount
		sumResult.GasAmount += item.GasAmount

		sumResult.ProfitAmountGGR += item.ProfitAmountGGR
		sumResult.ProfitAmountNGR += item.ProfitAmountNGR
		sumResult.AvgBetAmount += item.AvgBetAmount
		sumResult.D1Retention += item.D1Retention
		sumResult.D3Retention += item.D3Retention

		sumResult.D7Retention += item.D7Retention
		sumResult.D15Retention += item.D15Retention

	}
	return sumResult
}

// 获取平均值
func (this *OperationsDataController) GetBalanceAvg(sum *OperationsBalanceGameplayResp, cnt int64) *OperationsBalanceGameplayResp {
	avgResult := new(OperationsBalanceGameplayResp)
	switch cnt {
	case 0:
		return avgResult
	case 1:
		avgResult.FirstRechargeUsers = sum.FirstRechargeUsers
		avgResult.FirstRechargeAmount = sum.FirstRechargeAmount
		avgResult.RechargeCount = sum.RechargeCount
		avgResult.RechargeAmount = sum.RechargeAmount
		avgResult.WithdrawCount = sum.WithdrawCount

		avgResult.WithdrawAmount = sum.WithdrawAmount
		avgResult.WithdrawAmount2 = sum.WithdrawAmount2
		avgResult.CTDiff = sum.CTDiff
		avgResult.TotalBettors = sum.TotalBettors
		avgResult.PaymentBettors = sum.PaymentBettors

		avgResult.BetCount = sum.BetCount
		avgResult.WinCount = sum.WinCount
		avgResult.BetAmount = sum.BetAmount
		avgResult.RebateAmount = sum.RebateAmount
		avgResult.GasAmount = sum.GasAmount

		avgResult.ProfitAmountGGR = sum.ProfitAmountGGR
		avgResult.ProfitAmountNGR = sum.ProfitAmountNGR
		avgResult.AvgBetAmount = sum.AvgBetAmount
		avgResult.D1Retention = sum.D1Retention
		avgResult.D3Retention = sum.D3Retention

		avgResult.D7Retention = sum.D7Retention
		avgResult.D15Retention = sum.D15Retention

	default:
		avgResult.FirstRechargeUsers = math.Round(sum.FirstRechargeUsers/float64(cnt)*100) / 100
		avgResult.FirstRechargeAmount = math.Round(sum.FirstRechargeAmount/float64(cnt)*100) / 100
		avgResult.RechargeCount = math.Round(sum.RechargeCount/float64(cnt)*100) / 100
		avgResult.RechargeAmount = math.Round(sum.RechargeAmount/float64(cnt)*100) / 100
		avgResult.WithdrawCount = math.Round(sum.WithdrawCount/float64(cnt)*100) / 100

		avgResult.WithdrawAmount = math.Round(sum.WithdrawAmount/float64(cnt)*100) / 100
		avgResult.WithdrawAmount2 = math.Round(sum.WithdrawAmount2/float64(cnt)*100) / 100
		avgResult.CTDiff = math.Round(sum.CTDiff/float64(cnt)*100) / 100
		avgResult.TotalBettors = math.Round(sum.TotalBettors/float64(cnt)*100) / 100
		avgResult.PaymentBettors = math.Round(sum.PaymentBettors/float64(cnt)*100) / 100

		avgResult.BetCount = math.Round(sum.BetCount/float64(cnt)*100) / 100
		avgResult.WinCount = math.Round(sum.WinCount/float64(cnt)*100) / 100
		avgResult.BetAmount = math.Round(sum.BetAmount/float64(cnt)*100) / 100
		avgResult.RebateAmount = math.Round(sum.RebateAmount/float64(cnt)*100) / 100
		avgResult.GasAmount = math.Round(sum.GasAmount/float64(cnt)*100) / 100

		avgResult.ProfitAmountGGR = math.Round(sum.ProfitAmountGGR/float64(cnt)*100) / 100
		avgResult.ProfitAmountNGR = math.Round(sum.ProfitAmountNGR/float64(cnt)*100) / 100
		avgResult.AvgBetAmount = math.Round(sum.AvgBetAmount/float64(cnt)*100) / 100
		avgResult.D1Retention = math.Round(sum.D1Retention/float64(cnt)*100) / 100
		avgResult.D3Retention = math.Round(sum.D3Retention/float64(cnt)*100) / 100

		avgResult.D7Retention = math.Round(sum.D7Retention/float64(cnt)*100) / 100
		avgResult.D15Retention = math.Round(sum.D15Retention/float64(cnt)*100) / 100
	}
	return avgResult
}

// 获取分页数据
func (this *OperationsDataController) GetBalancePageList(list []*OperationsBalanceGameplayResp, offset, limit int) []*OperationsBalanceGameplayResp {
	if len(list) <= 1 {
		return list
	}
	end := offset + limit
	if end >= len(list) {
		end = len(list)
	}
	retList := make([]*OperationsBalanceGameplayResp, end-offset)
	copy(retList, list[offset:end])
	return retList
}

func (this *OperationsDataController) GetValidPaymentUserCond(condArr []int, fieldName string) string {
	ValidRechargeCondStr := ""
	nLen := len(condArr)
	if nLen == 0 {
		return ValidRechargeCondStr
	}
	for i, cond := range condArr {
		if i == nLen-1 {
			ValidRechargeCondStr += this.GetValidPaymentUserCondFieldName(cond)
		} else {
			ValidRechargeCondStr += this.GetValidPaymentUserCondFieldName(cond) + " + "
		}
	}

	strRet := fmt.Sprintf("ROUND(SUM(%s), 2) AS %s ", ValidRechargeCondStr, fieldName)
	return strRet
}

func (this *OperationsDataController) GetValidPaymentUserCondFieldName(cond int) string {
	fieldName := ""
	switch cond {
	case ValidNewUserCond1:
		fieldName = "A.ValidRecharge10Users"
	case ValidNewUserCond2:
		fieldName = "A.ValidRecharge50Users"
	case ValidNewUserCond3:
		fieldName = "A.ValidRecharge100Users"
	}
	return fieldName
}

func (this *OperationsDataController) GetRawQuerySql() string {
	//WITH CTE_A AS (
	//	SELECT
	//A.RecordDate AS recordDate,
	//	ROUND(SUM(A.TotalBetUsers),2) AS totalBettors,
	//	ROUND(SUM(A.RechargeUsers),2) AS totalRechargeUsers,
	//	ROUND(SUM(A.NewTransferBetUsers + A.NewBetUsers),2) AS newBettors,
	//	ROUND(SUM(A.TotalBetUsers),2) AS paymentBettors,
	//	ROUND(SUM(A.TotalBetUsers),2) AS newPaymentUsers,
	//	ROUND(SUM(A.ValidRecharge10Users + A.ValidRecharge50Users + A.ValidRecharge100Users), 2) AS validPaymentUsers,
	//	ROUND(SUM(A.ValidRecharge10Users + A.ValidRecharge50Users + A.ValidRecharge100Users), 2) AS newValidPaymentUsers,
	//	ROUND(SUM(A.TransferBetCount + A.BetCount),2) AS betCount,
	//	ROUND(SUM(A.TransferBetAmount + A.BetAmount),2) AS betAmount,
	//	ROUND(SUM(A.TransferWinAmount + A.WinAmount),2) AS rebateAmount,
	//	ROUND(SUM(A.WithdrawUsers),2) AS gasAmount,
	//	ROUND(SUM(A.WithdrawAmount),2) AS activeAmount,
	//	ROUND(SUM(A.RechargeAmount),2) AS subActiveAmount,
	//	ROUND(SUM(A.TransferTrxBetUsers),2) AS vipRebateAmount,
	//	ROUND(SUM(A.TransferTrxLiuShuiAmount),2) AS commissionAmount  -- 移除末尾逗号
	//FROM x_agent_data_date AS A
	//WHERE A.ChannelId IN (100073,100082)
	//AND A.TopAgentId IN (0,1,3)
	//AND A.RecordDate >= '2025-05-01 00:00:00'
	//AND A.RecordDate <= '2025-06-01 23:59:59'
	//GROUP BY A.recordDate
	//)
	//,CTE_B AS(
	//	SELECT
	//B.record_date AS recordDate,
	//	ROUND(SUM(B.sub_other_amount),2) AS subOtherAmount,
	//	ROUND(SUM(B.resources_amount),2) AS resourcesAmount,
	//	ROUND(SUM(B.market_amount),2) AS marketAmount,
	//	ROUND(SUM(B.ad_amount),2) AS adAmount,
	//	ROUND(SUM(B.op_amount),2) AS opAmount,
	//	ROUND(SUM(B.agent_amount),2) AS agentAmount
	//FROM x_operations_input_data_date AS B
	//WHERE B.record_date >= '2025-05-01 00:00:00'
	//AND B.record_date <= '2025-06-01 23:59:59'
	//GROUP BY B.record_date
	//)
	//
	//SELECT
	//A.*,
	//	IFNULL(B.subOtherAmount,0) AS subOtherAmount,
	//	IFNULL(B.resourcesAmount,0) AS resourcesAmount,
	//	IFNULL(B.marketAmount,0) AS marketAmount,
	//	IFNULL(B.adAmount,0) AS adAmount,
	//	IFNULL(B.opAmount,0) AS opAmount,
	//	IFNULL(B.agentAmount,0) AS agentAmount
	//FROM CTE_A AS A
	//LEFT JOIN CTE_B AS B ON A.recordDate = B.recordDate;
	rawSql := `
WITH CTE_A AS (
  SELECT
    A.RecordDate AS recordDate,
    ROUND(SUM(A.TotalBetUsers),2) AS totalBettors,
    ROUND(SUM(A.NewTotalBetUsers),2) AS newBettors,
    ROUND(SUM(A.TotalBetUsers - A.NewTotalBetUsers),2) AS oldBettors,
    ROUND(SUM(A.TotalBetCount),2) AS betCount,
    ROUND(SUM(A.TotalBetAmount),2) AS betAmount,
    ROUND(SUM(A.TotalWinAmount),2) AS rebateAmount,
    ROUND(SUM(A.TotalFeeAmount),2) AS gasAmount,
    ROUND(SUM(A.RewardAmount),2) AS activeAmount,
    ROUND(SUM(A.ManReduceRewardAmount),2) AS subActiveAmount,
    ROUND(SUM(A.VipRewardAmount),2) AS vipRebateAmount,
    ROUND(SUM(A.GetCommissionAmount),2) AS commissionAmount
  FROM x_agent_data_date AS A
  %s
  GROUP BY A.recordDate
)
,CTE_B AS(
  SELECT
    B.record_date AS recordDate,
	ROUND(SUM(B.sub_other_amount),2) AS subOtherAmount,
    ROUND(SUM(B.resources_amount),2) AS resourcesAmount,
    ROUND(SUM(B.market_amount),2) AS marketAmount,
    ROUND(SUM(B.ad_amount),2) AS adAmount,
    ROUND(SUM(B.op_amount),2) AS opAmount,
    ROUND(SUM(B.agent_amount),2) AS agentAmount
  FROM x_operations_input_data_date AS B
  %s
  GROUP BY B.record_date
)

SELECT
  A.*
  ,IFNULL(B.subOtherAmount,0) AS subOtherAmount
  ,IFNULL(B.resourcesAmount,0) AS resourcesAmount
  ,IFNULL(B.marketAmount,0) AS marketAmount
  ,IFNULL(B.adAmount,0) AS adAmount
  ,IFNULL(B.opAmount,0) AS opAmount
  ,IFNULL(B.agentAmount,0) AS agentAmount
FROM CTE_A AS A
LEFT JOIN CTE_B AS B
  ON A.recordDate=B.recordDate
`

	return rawSql
}

func (this *OperationsDataController) GetPlatWhere(req *OperationsPlatListReq) string {
	strSql := ""
	has := false
	before := false
	// 商户id
	if req.SellerId > 0 {
		has = true
		before = true
		strSql = fmt.Sprintf(" A.SellerId = %d ", req.SellerId)
	}
	// 渠道id
	if len(req.ChannelId) > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.ChannelId IN (%s) ", this.arr2string(req.ChannelId))
		before = true
	}

	// 顶级代理id
	if len(req.TopAgentId) > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.TopAgentId IN (%s) ", this.arr2string(req.TopAgentId))
		before = true
	}

	//时间区间
	if req.StartTime > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.RecordDate >= '%s' ", abugo.TimeStampToLocalTime(req.StartTime))
		before = true
	}
	// 注册时间区间
	if req.EndTime > 0 && req.EndTime > req.StartTime {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" A.RecordDate <= '%s' ", abugo.TimeStampToLocalTime(req.EndTime))
		before = true
	}
	if has {
		strSql = "WHERE " + strSql
	}
	return strSql
}

func (this *OperationsDataController) GetPlatWhere2(req *OperationsPlatListReq) string {
	strSql := ""
	has := false
	before := false

	// 商户id
	if req.SellerId > 0 {
		has = true
		before = true
		strSql = fmt.Sprintf(" B.seller_id = %d ", req.SellerId)
	}

	//时间区间
	if req.StartTime > 0 {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" B.record_date >= '%s' ", abugo.TimeStampToLocalTime(req.StartTime))
		before = true
	}
	// 注册时间区间
	if req.EndTime > 0 && req.EndTime > req.StartTime {
		has = true
		strAnd := ""
		if before {
			strAnd = "AND"
		}
		strSql = strSql + strAnd + fmt.Sprintf(" B.record_date <= '%s' ", abugo.TimeStampToLocalTime(req.EndTime))
		before = true
	}
	if has {
		strSql = "WHERE " + strSql
	}
	return strSql
}

func (this *OperationsDataController) arr2string(arr []int) string {
	var str string
	for _, v := range arr {
		str += strconv.Itoa(v) + ","
	}
	return str[:len(str)-1]
}

// 获取总和
func (this *OperationsDataController) GetPlatSum(list []*OperationsPlatListResp) *OperationsPlatListResp {
	sumResult := new(OperationsPlatListResp)
	for _, item := range list {
		sumResult.TotalBettors += item.TotalBettors
		sumResult.NewBettors += item.NewBettors
		sumResult.OldBettors += item.OldBettors
		sumResult.PaymentBettors += item.PaymentBettors
		sumResult.NewPaymentUsers += item.NewPaymentUsers

		sumResult.OldPaymentUsers += item.OldPaymentUsers
		sumResult.ValidPaymentUsers += item.ValidPaymentUsers
		sumResult.NewValidPaymentUsers += item.NewValidPaymentUsers
		sumResult.OldValidPaymentUsers += item.OldValidPaymentUsers
		sumResult.BetCount += item.BetCount
		sumResult.BetAmount += item.BetAmount

		sumResult.RebateAmount += item.RebateAmount
		sumResult.ProfitAmountGGR += item.ProfitAmountGGR
		sumResult.GasAmount += item.GasAmount
		sumResult.ActiveAmount += item.ActiveAmount
		sumResult.SubActiveAmount += item.SubActiveAmount

		sumResult.SubOtherAmount += item.SubOtherAmount
		sumResult.VipRebateAmount += item.VipRebateAmount
		sumResult.CommissionAmount += item.CommissionAmount
		sumResult.ResourcesAmount += item.ResourcesAmount
		sumResult.MarketAmount += item.MarketAmount

		sumResult.AdAmount += item.AdAmount
		sumResult.OpAmount += item.OpAmount
		sumResult.AgentAmount += item.AgentAmount
		sumResult.ProfitAmountNGR += item.ProfitAmountNGR
		sumResult.ARPUAmount += item.ARPUAmount

		sumResult.ARPPUAmount += item.ARPPUAmount
		sumResult.PaymentRate += item.PaymentRate
		sumResult.AvgBetAmount += item.AvgBetAmount
	}
	return sumResult
}

// 获取平均值
func (this *OperationsDataController) GetPlatAvg(sum *OperationsPlatListResp, cnt int64) *OperationsPlatListResp {
	avgResult := new(OperationsPlatListResp)
	if cnt <= 0 {
		return avgResult
	}

	avgResult.TotalBettors = math.Round(sum.TotalBettors/float64(cnt)*100) / 100
	avgResult.NewBettors = math.Round(sum.NewBettors/float64(cnt)*100) / 100
	avgResult.OldBettors = math.Round(sum.OldBettors/float64(cnt)*100) / 100
	avgResult.PaymentBettors = math.Round(sum.PaymentBettors/float64(cnt)*100) / 100
	avgResult.NewPaymentUsers = math.Round(sum.NewPaymentUsers/float64(cnt)*100) / 100

	avgResult.OldPaymentUsers = math.Round(sum.OldPaymentUsers/float64(cnt)*100) / 100
	avgResult.ValidPaymentUsers = math.Round(sum.ValidPaymentUsers/float64(cnt)*100) / 100
	avgResult.NewValidPaymentUsers = math.Round(sum.NewValidPaymentUsers/float64(cnt)*100) / 100
	avgResult.OldValidPaymentUsers = math.Round(sum.OldValidPaymentUsers/float64(cnt)*100) / 100
	avgResult.BetCount = math.Round(sum.BetCount/float64(cnt)*100) / 100

	avgResult.BetAmount = math.Round(sum.BetAmount/float64(cnt)*100) / 100

	avgResult.RebateAmount = math.Round(sum.RebateAmount/float64(cnt)*100) / 100
	avgResult.ProfitAmountGGR = math.Round(sum.ProfitAmountGGR/float64(cnt)*100) / 100
	avgResult.GasAmount = math.Round(sum.GasAmount/float64(cnt)*100) / 100
	avgResult.ActiveAmount = math.Round(sum.ActiveAmount/float64(cnt)*100) / 100
	avgResult.SubActiveAmount = math.Round(sum.SubActiveAmount/float64(cnt)*100) / 100

	avgResult.SubOtherAmount = math.Round(sum.SubOtherAmount/float64(cnt)*100) / 100
	avgResult.VipRebateAmount = math.Round(sum.VipRebateAmount/float64(cnt)*100) / 100
	avgResult.CommissionAmount = math.Round(sum.CommissionAmount/float64(cnt)*100) / 100
	avgResult.ResourcesAmount = math.Round(sum.ResourcesAmount/float64(cnt)*100) / 100
	avgResult.MarketAmount = math.Round(sum.MarketAmount/float64(cnt)*100) / 100

	avgResult.AdAmount = math.Round(sum.AdAmount/float64(cnt)*100) / 100
	avgResult.OpAmount = math.Round(sum.OpAmount/float64(cnt)*100) / 100
	avgResult.AgentAmount = math.Round(sum.AgentAmount/float64(cnt)*100) / 100
	avgResult.ProfitAmountNGR = math.Round(sum.ProfitAmountNGR/float64(cnt)*100) / 100
	avgResult.ARPUAmount = math.Round(sum.ARPUAmount/float64(cnt)*100) / 100

	avgResult.ARPPUAmount = math.Round(sum.ARPPUAmount/float64(cnt)*100) / 100
	avgResult.PaymentRate = math.Round(sum.PaymentRate/float64(cnt)*100) / 100
	avgResult.AvgBetAmount = math.Round(sum.AvgBetAmount/float64(cnt)*100) / 100
	return avgResult
}

// 获取分页数据
func (this *OperationsDataController) GetPlatPageList(list []*OperationsPlatListResp, offset, limit int) []*OperationsPlatListResp {
	if len(list) <= 1 {
		return list
	}
	end := offset + limit
	if end >= len(list) {
		end = len(list)
	}
	retList := make([]*OperationsPlatListResp, end-offset)
	copy(retList, list[offset:end])
	return retList
}
