package db

import (
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

type SellerDailly struct {
	SellerId            int     `gorm:"column:SellerId"`            //
	RecordDate          string  `gorm:"column:RecordDate"`          //
	LiuSuiTrx           float64 `gorm:"column:LiuSuiTrx"`           //业绩trx
	LiuSuiUsdt          float64 `gorm:"column:LiuSuiUsdt"`          //业绩usdt
	BetTrx              float64 `gorm:"column:BetTrx"`              //下注trx
	BetUsdt             float64 `gorm:"column:BetUsdt"`             //下注usdt
	RewardTrx           float64 `gorm:"column:RewardTrx"`           //返奖trx
	RewardUsdt          float64 `gorm:"column:RewardUsdt"`          //返奖usdt
	CommissionTrx       float64 `gorm:"column:CommissionTrx"`       //佣金trx
	CommissionUsdt      float64 `gorm:"column:CommissionUsdt"`      //佣金usdt
	BetUserCount        int     `gorm:"column:BetUserCount"`        //下注人数
	OrderCount          int     `gorm:"column:OrderCount"`          //订单数量
	GetedCommissionTrx  float64 `gorm:"column:GetedCommissionTrx"`  //领取佣金trx
	GetedCommissionUsdt float64 `gorm:"column:GetedCommissionUsdt"` //领取佣金usdt
	UserWastageTrx      float64 `gorm:"column:UserWastageTrx"`      //玩家损耗trx
	UserWastageUsdt     float64 `gorm:"column:UserWastageUsdt"`     //玩家损耗usdt
	GameLiuSuiTrx1      float64 `gorm:"column:GameLiuSuiTrx1"`      //
	GameLiuSuiTrx2      float64 `gorm:"column:GameLiuSuiTrx2"`      //
	GameLiuSuiTrx3      float64 `gorm:"column:GameLiuSuiTrx3"`      //
	GameLiuSuiTrx4      float64 `gorm:"column:GameLiuSuiTrx4"`      //
	GameLiuSuiTrx5      float64 `gorm:"column:GameLiuSuiTrx5"`      //
	GameLiuSuiUsdt1     float64 `gorm:"column:GameLiuSuiUsdt1"`     //
	GameLiuSuiUsdt2     float64 `gorm:"column:GameLiuSuiUsdt2"`     //
	GameLiuSuiUsdt3     float64 `gorm:"column:GameLiuSuiUsdt3"`     //
	GameLiuSuiUsdt4     float64 `gorm:"column:GameLiuSuiUsdt4"`     //
	GameLiuSuiUsdt5     float64 `gorm:"column:GameLiuSuiUsdt5"`     //
	GameBetTrx1         float64 `gorm:"column:GameBetTrx1"`         //
	GameBetTrx2         float64 `gorm:"column:GameBetTrx2"`         //
	GameBetTrx3         float64 `gorm:"column:GameBetTrx3"`         //
	GameBetTrx4         float64 `gorm:"column:GameBetTrx4"`         //
	GameBetTrx5         float64 `gorm:"column:GameBetTrx5"`         //
	GameBetUsdt1        float64 `gorm:"column:GameBetUsdt1"`        //
	GameBetUsdt2        float64 `gorm:"column:GameBetUsdt2"`        //
	GameBetUsdt3        float64 `gorm:"column:GameBetUsdt3"`        //
	GameBetUsdt4        float64 `gorm:"column:GameBetUsdt4"`        //
	GameBetUsdt5        float64 `gorm:"column:GameBetUsdt5"`        //
	GameRewardTrx1      float64 `gorm:"column:GameRewardTrx1"`      //
	GameRewardTrx2      float64 `gorm:"column:GameRewardTrx2"`      //
	GameRewardTrx3      float64 `gorm:"column:GameRewardTrx3"`      //
	GameRewardTrx4      float64 `gorm:"column:GameRewardTrx4"`      //
	GameRewardTrx5      float64 `gorm:"column:GameRewardTrx5"`      //
	GameRewardUsdt1     float64 `gorm:"column:GameRewardUsdt1"`     //
	GameRewardUsdt2     float64 `gorm:"column:GameRewardUsdt2"`     //
	GameRewardUsdt3     float64 `gorm:"column:GameRewardUsdt3"`     //
	GameRewardUsdt4     float64 `gorm:"column:GameRewardUsdt4"`     //
	GameRewardUsdt5     float64 `gorm:"column:GameRewardUsdt5"`     //
}

func (*SellerDailly) TableName() string {
	return "x_seller_dailly"
}

func SellerDailly_Page_Data(Page int, PageSize int, SellerId int, StartTime int64, EndTime int64) (int, []SellerDailly) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := SellerDailly{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "RecordDate", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	if EndTime > 0 {
		EndTime += 86400000
	}
	server.Db().AddWhere(&sql, &params, "and", "RecordDate", "<", abugo.TimeStampToLocalTime(EndTime), "")
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []SellerDailly{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []SellerDailly{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}
