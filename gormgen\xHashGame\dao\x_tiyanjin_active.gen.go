// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTiyanjinActive(db *gorm.DB, opts ...gen.DOOption) xTiyanjinActive {
	_xTiyanjinActive := xTiyanjinActive{}

	_xTiyanjinActive.xTiyanjinActiveDo.UseDB(db, opts...)
	_xTiyanjinActive.xTiyanjinActiveDo.UseModel(&model.XTiyanjinActive{})

	tableName := _xTiyanjinActive.xTiyanjinActiveDo.TableName()
	_xTiyanjinActive.ALL = field.NewAsterisk(tableName)
	_xTiyanjinActive.ID = field.NewInt32(tableName, "Id")
	_xTiyanjinActive.SellerID = field.NewInt32(tableName, "SellerId")
	_xTiyanjinActive.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTiyanjinActive.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xTiyanjinActive.Status = field.NewInt32(tableName, "Status")
	_xTiyanjinActive.Name = field.NewString(tableName, "Name")
	_xTiyanjinActive.HostTagIds = field.NewString(tableName, "HostTagIds")
	_xTiyanjinActive.CoolingDate = field.NewInt32(tableName, "CoolingDate")
	_xTiyanjinActive.RewardConfig = field.NewString(tableName, "RewardConfig")
	_xTiyanjinActive.Rule = field.NewString(tableName, "Rule")
	_xTiyanjinActive.Creator = field.NewString(tableName, "Creator")
	_xTiyanjinActive.Operator = field.NewString(tableName, "Operator")

	_xTiyanjinActive.fillFieldMap()

	return _xTiyanjinActive
}

// xTiyanjinActive 体验金活动表
type xTiyanjinActive struct {
	xTiyanjinActiveDo xTiyanjinActiveDo

	ALL          field.Asterisk
	ID           field.Int32 // 体验金活动id
	SellerID     field.Int32 // 运营商
	CreateTime   field.Time
	UpdateTime   field.Time
	Status       field.Int32  // 状态：1启用 2禁用
	Name         field.String // 活动名称
	HostTagIds   field.String // 域名标签id（英文逗号隔开）
	CoolingDate  field.Int32  // 参与频率限制（0：仅限一次；>0：x天一次）
	RewardConfig field.String // 奖励配置
	Rule         field.String // 发放规则
	Creator      field.String // 创建人账号
	Operator     field.String // 修改人账号

	fieldMap map[string]field.Expr
}

func (x xTiyanjinActive) Table(newTableName string) *xTiyanjinActive {
	x.xTiyanjinActiveDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTiyanjinActive) As(alias string) *xTiyanjinActive {
	x.xTiyanjinActiveDo.DO = *(x.xTiyanjinActiveDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTiyanjinActive) updateTableName(table string) *xTiyanjinActive {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.Status = field.NewInt32(table, "Status")
	x.Name = field.NewString(table, "Name")
	x.HostTagIds = field.NewString(table, "HostTagIds")
	x.CoolingDate = field.NewInt32(table, "CoolingDate")
	x.RewardConfig = field.NewString(table, "RewardConfig")
	x.Rule = field.NewString(table, "Rule")
	x.Creator = field.NewString(table, "Creator")
	x.Operator = field.NewString(table, "Operator")

	x.fillFieldMap()

	return x
}

func (x *xTiyanjinActive) WithContext(ctx context.Context) *xTiyanjinActiveDo {
	return x.xTiyanjinActiveDo.WithContext(ctx)
}

func (x xTiyanjinActive) TableName() string { return x.xTiyanjinActiveDo.TableName() }

func (x xTiyanjinActive) Alias() string { return x.xTiyanjinActiveDo.Alias() }

func (x xTiyanjinActive) Columns(cols ...field.Expr) gen.Columns {
	return x.xTiyanjinActiveDo.Columns(cols...)
}

func (x *xTiyanjinActive) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTiyanjinActive) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["Status"] = x.Status
	x.fieldMap["Name"] = x.Name
	x.fieldMap["HostTagIds"] = x.HostTagIds
	x.fieldMap["CoolingDate"] = x.CoolingDate
	x.fieldMap["RewardConfig"] = x.RewardConfig
	x.fieldMap["Rule"] = x.Rule
	x.fieldMap["Creator"] = x.Creator
	x.fieldMap["Operator"] = x.Operator
}

func (x xTiyanjinActive) clone(db *gorm.DB) xTiyanjinActive {
	x.xTiyanjinActiveDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTiyanjinActive) replaceDB(db *gorm.DB) xTiyanjinActive {
	x.xTiyanjinActiveDo.ReplaceDB(db)
	return x
}

type xTiyanjinActiveDo struct{ gen.DO }

func (x xTiyanjinActiveDo) Debug() *xTiyanjinActiveDo {
	return x.withDO(x.DO.Debug())
}

func (x xTiyanjinActiveDo) WithContext(ctx context.Context) *xTiyanjinActiveDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTiyanjinActiveDo) ReadDB() *xTiyanjinActiveDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTiyanjinActiveDo) WriteDB() *xTiyanjinActiveDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTiyanjinActiveDo) Session(config *gorm.Session) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTiyanjinActiveDo) Clauses(conds ...clause.Expression) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTiyanjinActiveDo) Returning(value interface{}, columns ...string) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTiyanjinActiveDo) Not(conds ...gen.Condition) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTiyanjinActiveDo) Or(conds ...gen.Condition) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTiyanjinActiveDo) Select(conds ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTiyanjinActiveDo) Where(conds ...gen.Condition) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTiyanjinActiveDo) Order(conds ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTiyanjinActiveDo) Distinct(cols ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTiyanjinActiveDo) Omit(cols ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTiyanjinActiveDo) Join(table schema.Tabler, on ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTiyanjinActiveDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTiyanjinActiveDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTiyanjinActiveDo) Group(cols ...field.Expr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTiyanjinActiveDo) Having(conds ...gen.Condition) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTiyanjinActiveDo) Limit(limit int) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTiyanjinActiveDo) Offset(offset int) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTiyanjinActiveDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTiyanjinActiveDo) Unscoped() *xTiyanjinActiveDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTiyanjinActiveDo) Create(values ...*model.XTiyanjinActive) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTiyanjinActiveDo) CreateInBatches(values []*model.XTiyanjinActive, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTiyanjinActiveDo) Save(values ...*model.XTiyanjinActive) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTiyanjinActiveDo) First() (*model.XTiyanjinActive, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinActive), nil
	}
}

func (x xTiyanjinActiveDo) Take() (*model.XTiyanjinActive, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinActive), nil
	}
}

func (x xTiyanjinActiveDo) Last() (*model.XTiyanjinActive, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinActive), nil
	}
}

func (x xTiyanjinActiveDo) Find() ([]*model.XTiyanjinActive, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTiyanjinActive), err
}

func (x xTiyanjinActiveDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTiyanjinActive, err error) {
	buf := make([]*model.XTiyanjinActive, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTiyanjinActiveDo) FindInBatches(result *[]*model.XTiyanjinActive, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTiyanjinActiveDo) Attrs(attrs ...field.AssignExpr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTiyanjinActiveDo) Assign(attrs ...field.AssignExpr) *xTiyanjinActiveDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTiyanjinActiveDo) Joins(fields ...field.RelationField) *xTiyanjinActiveDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTiyanjinActiveDo) Preload(fields ...field.RelationField) *xTiyanjinActiveDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTiyanjinActiveDo) FirstOrInit() (*model.XTiyanjinActive, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinActive), nil
	}
}

func (x xTiyanjinActiveDo) FirstOrCreate() (*model.XTiyanjinActive, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinActive), nil
	}
}

func (x xTiyanjinActiveDo) FindByPage(offset int, limit int) (result []*model.XTiyanjinActive, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTiyanjinActiveDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTiyanjinActiveDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTiyanjinActiveDo) Delete(models ...*model.XTiyanjinActive) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTiyanjinActiveDo) withDO(do gen.Dao) *xTiyanjinActiveDo {
	x.DO = *do.(*gen.DO)
	return x
}
