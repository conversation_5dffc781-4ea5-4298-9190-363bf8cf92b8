// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXLoginLog(db *gorm.DB, opts ...gen.DOOption) xLoginLog {
	_xLoginLog := xLoginLog{}

	_xLoginLog.xLoginLogDo.UseDB(db, opts...)
	_xLoginLog.xLoginLogDo.UseModel(&model.XLoginLog{})

	tableName := _xLoginLog.xLoginLogDo.TableName()
	_xLoginLog.ALL = field.NewAsterisk(tableName)
	_xLoginLog.ID = field.NewInt32(tableName, "Id")
	_xLoginLog.UserID = field.NewInt32(tableName, "UserId")
	_xLoginLog.SellerID = field.NewInt32(tableName, "SellerId")
	_xLoginLog.IP = field.NewString(tableName, "Ip")
	_xLoginLog.Lang = field.NewString(tableName, "Lang")
	_xLoginLog.LoginTime = field.NewTime(tableName, "LoginTime")
	_xLoginLog.RegisterIP = field.NewString(tableName, "RegisterIp")
	_xLoginLog.RegisterTime = field.NewTime(tableName, "RegisterTime")
	_xLoginLog.Account = field.NewString(tableName, "Account")
	_xLoginLog.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xLoginLog.Address = field.NewString(tableName, "Address")
	_xLoginLog.DeviceID = field.NewString(tableName, "DeviceId")
	_xLoginLog.DeviceType = field.NewString(tableName, "DeviceType")

	_xLoginLog.fillFieldMap()

	return _xLoginLog
}

type xLoginLog struct {
	xLoginLogDo xLoginLogDo

	ALL          field.Asterisk
	ID           field.Int32
	UserID       field.Int32 // 玩家id
	SellerID     field.Int32 // 运营商
	IP           field.String
	Lang         field.String // 登录语言
	LoginTime    field.Time   // 登录时间
	RegisterIP   field.String // 注册ip
	RegisterTime field.Time   // 注册时间
	Account      field.String // 账号
	ChannelID    field.Int32
	Address      field.String
	DeviceID     field.String
	DeviceType   field.String

	fieldMap map[string]field.Expr
}

func (x xLoginLog) Table(newTableName string) *xLoginLog {
	x.xLoginLogDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xLoginLog) As(alias string) *xLoginLog {
	x.xLoginLogDo.DO = *(x.xLoginLogDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xLoginLog) updateTableName(table string) *xLoginLog {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.LoginTime = field.NewTime(table, "LoginTime")
	x.RegisterIP = field.NewString(table, "RegisterIp")
	x.RegisterTime = field.NewTime(table, "RegisterTime")
	x.Account = field.NewString(table, "Account")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Address = field.NewString(table, "Address")
	x.DeviceID = field.NewString(table, "DeviceId")
	x.DeviceType = field.NewString(table, "DeviceType")

	x.fillFieldMap()

	return x
}

func (x *xLoginLog) WithContext(ctx context.Context) *xLoginLogDo {
	return x.xLoginLogDo.WithContext(ctx)
}

func (x xLoginLog) TableName() string { return x.xLoginLogDo.TableName() }

func (x xLoginLog) Alias() string { return x.xLoginLogDo.Alias() }

func (x xLoginLog) Columns(cols ...field.Expr) gen.Columns { return x.xLoginLogDo.Columns(cols...) }

func (x *xLoginLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xLoginLog) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["LoginTime"] = x.LoginTime
	x.fieldMap["RegisterIp"] = x.RegisterIP
	x.fieldMap["RegisterTime"] = x.RegisterTime
	x.fieldMap["Account"] = x.Account
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["DeviceId"] = x.DeviceID
	x.fieldMap["DeviceType"] = x.DeviceType
}

func (x xLoginLog) clone(db *gorm.DB) xLoginLog {
	x.xLoginLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xLoginLog) replaceDB(db *gorm.DB) xLoginLog {
	x.xLoginLogDo.ReplaceDB(db)
	return x
}

type xLoginLogDo struct{ gen.DO }

func (x xLoginLogDo) Debug() *xLoginLogDo {
	return x.withDO(x.DO.Debug())
}

func (x xLoginLogDo) WithContext(ctx context.Context) *xLoginLogDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xLoginLogDo) ReadDB() *xLoginLogDo {
	return x.Clauses(dbresolver.Read)
}

func (x xLoginLogDo) WriteDB() *xLoginLogDo {
	return x.Clauses(dbresolver.Write)
}

func (x xLoginLogDo) Session(config *gorm.Session) *xLoginLogDo {
	return x.withDO(x.DO.Session(config))
}

func (x xLoginLogDo) Clauses(conds ...clause.Expression) *xLoginLogDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xLoginLogDo) Returning(value interface{}, columns ...string) *xLoginLogDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xLoginLogDo) Not(conds ...gen.Condition) *xLoginLogDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xLoginLogDo) Or(conds ...gen.Condition) *xLoginLogDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xLoginLogDo) Select(conds ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xLoginLogDo) Where(conds ...gen.Condition) *xLoginLogDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xLoginLogDo) Order(conds ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xLoginLogDo) Distinct(cols ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xLoginLogDo) Omit(cols ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xLoginLogDo) Join(table schema.Tabler, on ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xLoginLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xLoginLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xLoginLogDo) Group(cols ...field.Expr) *xLoginLogDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xLoginLogDo) Having(conds ...gen.Condition) *xLoginLogDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xLoginLogDo) Limit(limit int) *xLoginLogDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xLoginLogDo) Offset(offset int) *xLoginLogDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xLoginLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xLoginLogDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xLoginLogDo) Unscoped() *xLoginLogDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xLoginLogDo) Create(values ...*model.XLoginLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xLoginLogDo) CreateInBatches(values []*model.XLoginLog, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xLoginLogDo) Save(values ...*model.XLoginLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xLoginLogDo) First() (*model.XLoginLog, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLoginLog), nil
	}
}

func (x xLoginLogDo) Take() (*model.XLoginLog, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLoginLog), nil
	}
}

func (x xLoginLogDo) Last() (*model.XLoginLog, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLoginLog), nil
	}
}

func (x xLoginLogDo) Find() ([]*model.XLoginLog, error) {
	result, err := x.DO.Find()
	return result.([]*model.XLoginLog), err
}

func (x xLoginLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XLoginLog, err error) {
	buf := make([]*model.XLoginLog, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xLoginLogDo) FindInBatches(result *[]*model.XLoginLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xLoginLogDo) Attrs(attrs ...field.AssignExpr) *xLoginLogDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xLoginLogDo) Assign(attrs ...field.AssignExpr) *xLoginLogDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xLoginLogDo) Joins(fields ...field.RelationField) *xLoginLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xLoginLogDo) Preload(fields ...field.RelationField) *xLoginLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xLoginLogDo) FirstOrInit() (*model.XLoginLog, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLoginLog), nil
	}
}

func (x xLoginLogDo) FirstOrCreate() (*model.XLoginLog, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLoginLog), nil
	}
}

func (x xLoginLogDo) FindByPage(offset int, limit int) (result []*model.XLoginLog, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xLoginLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xLoginLogDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xLoginLogDo) Delete(models ...*model.XLoginLog) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xLoginLogDo) withDO(do gen.Dao) *xLoginLogDo {
	x.DO = *do.(*gen.DO)
	return x
}
