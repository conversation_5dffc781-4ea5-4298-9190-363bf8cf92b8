package controller

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"
)

type LotteryController struct {
}

func (c *LotteryController) Init() {
	group := server.Http().NewGroup("/api/lottery")
	{
		group.Post("/openlist", c.openlist)
		group.Post("/rewardword_list", c.rewardword_list)
		group.Post("/rewardword_add", c.rewardword_add)
		group.Post("/rewardword_modify", c.rewardword_modify)
		group.Post("/rewardword_delete", c.rewardword_delete)
		group.Post("/rewardword_update_status", c.rewardword_update_status)
	}
}

func (c *LotteryController) openlist(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		GameId    int
		Period    string
		StartTime int64
		EndTime   int64
		ChainType int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "彩票开奖", "查", "查看彩票开奖历史")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	if reqdata.Page == 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize == 0 {
		reqdata.PageSize = 15
	}
	if reqdata.GameId > 300 {
		reqdata.GameId = reqdata.GameId - 200
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "State", ">", 1, 0)
	where.Add("and", "GameId", "=", reqdata.GameId, 0)
	where.Add("and", "Period", "=", reqdata.Period, "")
	where.Add("and", "OpenTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "OpenTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	if reqdata.GameId > 0 && reqdata.GameId < 100 {
		if reqdata.ChainType > 1 {
			ctx.Put("data", []map[string]interface{}{})
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		total, presult := server.Db().Table("x_lottery_period").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
		for index := range *presult {
			(*presult)[index]["ChainType"] = 1
		}
		ctx.Put("data", *presult)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		where.Add("and", "ChainType", "=", reqdata.ChainType, 0)
		total, presult := server.Db().Table("x_game_period").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
		for index := range *presult {
			(*presult)[index]["OpenTime"] = (*presult)[index]["BlockTime"]
		}
		ctx.Put("data", *presult)
		ctx.Put("total", total)
		ctx.RespOK()
	}
}

func (c *LotteryController) rewardword_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		RewardType int // 11中奖 21不中奖
		Status     *int32
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "激励文案", "查", "查看激励文案列表")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	rewardwordTb := server.DaoxHashGame().XTbRewardword
	rewardwordDb := server.DaoxHashGame().XTbRewardword.WithContext(context.Background())
	if reqdata.RewardType == utils.RewardwordWinning {
		rewardwordDb = rewardwordDb.Where(rewardwordTb.RewardType.In(1, 11))
	} else if reqdata.RewardType == utils.RewardwordNoWinning {
		rewardwordDb = rewardwordDb.Where(rewardwordTb.RewardType.In(2, 21))
	}
	if reqdata.Status != nil {
		rewardwordDb = rewardwordDb.Where(rewardwordTb.Status.Eq(*reqdata.Status))
	}
	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)
	result, count, err := rewardwordDb.Order(rewardwordTb.ID.Desc()).FindByPage(offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("data", result)
	ctx.Put("total", count)
	server.WriteAdminLog("激励文案列表", ctx, reqdata)
	ctx.RespOK()
}

func (c *LotteryController) rewardword_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Content    string `validate:"required"`
		Remark     string `validate:"required"`
		RewardType int    `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "激励文案", "增", "添加激励文案")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	presult, err := server.Db().CallProcedure("ConfigManage_x_tb_rewardword_Insert", reqdata.Content, reqdata.RewardType, reqdata.Remark, token.Account, token.UserId, 4, ctx.GetIp())
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureCodeErr(presult) {
		return
	}
	server.WriteAdminLog("添加激励文案", ctx, reqdata)
	ctx.RespOK()
}

func (c *LotteryController) rewardword_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id         int `validate:"required"`
		SellerId   int
		Content    string `validate:"required"`
		Remark     string `validate:"required"`
		Status     int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "激励文案", "改", "修改激励文案")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	rewardwordTb := server.DaoxHashGame().XTbRewardword
	rewardwordDb := server.DaoxHashGame().XTbRewardword.WithContext(context.Background())
	_, err := rewardwordDb.Where(rewardwordTb.ID.Eq(int32(reqdata.Id))).First()
	if err != nil {
		errmsg := "系统错误,请稍后再试"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			errmsg = "数据不存在"
		}
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}
	presult, err := server.Db().CallProcedure("ConfigManage_x_tb_rewardword_Update", reqdata.Id, reqdata.Content, reqdata.Status, reqdata.Remark, token.Account, token.UserId, 4, ctx.GetIp())
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureCodeErr(presult) {
		return
	}
	server.WriteAdminLog("修改激励文案", ctx, reqdata)
	ctx.RespOK()
}
func (c *LotteryController) rewardword_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id         []int `validate:"required,gt=0"`
		SellerId   int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "激励文案", "删", "删除激励文案")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	rewardwordDb := server.DaoxHashGame().XTbRewardword.WithContext(context.Background())
	rewardworddata := make([]*model.XTbRewardword, 0, len(reqdata.Id))
	for _, id := range reqdata.Id {
		rewardworddata = append(rewardworddata, &model.XTbRewardword{
			ID: int32(id),
		})
	}
	_, err := rewardwordDb.Delete(rewardworddata...)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	server.WriteAdminLog("删除激励文案", ctx, reqdata)
	ctx.RespOK()
}

func (c *LotteryController) rewardword_update_status(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id         []int32 `validate:"required,gt=0"`
		SellerId   int
		GoogleCode string
		Status     int `validate:"oneof=0 1"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "激励文案", "改", "修改激励文案")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	rewardwordTb := server.DaoxHashGame().XTbRewardword
	rewardwordDb := server.DaoxHashGame().XTbRewardword.WithContext(context.Background())
	data := map[string]interface{}{
		"Status": reqdata.Status,
	}
	_, err := rewardwordDb.Select(rewardwordTb.Status).Where(rewardwordTb.ID.In(reqdata.Id...)).Updates(data)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	server.WriteAdminLog("修改激励文案状态", ctx, reqdata)
	ctx.RespOK()
}
