// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentBlacklist(db *gorm.DB, opts ...gen.DOOption) xAgentBlacklist {
	_xAgentBlacklist := xAgentBlacklist{}

	_xAgentBlacklist.xAgentBlacklistDo.UseDB(db, opts...)
	_xAgentBlacklist.xAgentBlacklistDo.UseModel(&model.XAgentBlacklist{})

	tableName := _xAgentBlacklist.xAgentBlacklistDo.TableName()
	_xAgentBlacklist.ALL = field.NewAsterisk(tableName)
	_xAgentBlacklist.ID = field.NewInt32(tableName, "Id")
	_xAgentBlacklist.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentBlacklist.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentBlacklist.UserType = field.NewInt32(tableName, "UserType")
	_xAgentBlacklist.UserID = field.NewInt32(tableName, "UserId")
	_xAgentBlacklist.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xAgentBlacklist.AgentID = field.NewInt32(tableName, "AgentId")
	_xAgentBlacklist.Address = field.NewString(tableName, "Address")
	_xAgentBlacklist.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentBlacklist.CreateAccount = field.NewString(tableName, "CreateAccount")

	_xAgentBlacklist.fillFieldMap()

	return _xAgentBlacklist
}

type xAgentBlacklist struct {
	xAgentBlacklistDo xAgentBlacklistDo

	ALL           field.Asterisk
	ID            field.Int32
	SellerID      field.Int32
	ChannelID     field.Int32
	UserType      field.Int32 // 1用户 2代理 3渠道 4运营商
	UserID        field.Int32
	TopAgentID    field.Int32
	AgentID       field.Int32
	Address       field.String
	CreateTime    field.Time
	CreateAccount field.String

	fieldMap map[string]field.Expr
}

func (x xAgentBlacklist) Table(newTableName string) *xAgentBlacklist {
	x.xAgentBlacklistDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentBlacklist) As(alias string) *xAgentBlacklist {
	x.xAgentBlacklistDo.DO = *(x.xAgentBlacklistDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentBlacklist) updateTableName(table string) *xAgentBlacklist {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserType = field.NewInt32(table, "UserType")
	x.UserID = field.NewInt32(table, "UserId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.AgentID = field.NewInt32(table, "AgentId")
	x.Address = field.NewString(table, "Address")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CreateAccount = field.NewString(table, "CreateAccount")

	x.fillFieldMap()

	return x
}

func (x *xAgentBlacklist) WithContext(ctx context.Context) *xAgentBlacklistDo {
	return x.xAgentBlacklistDo.WithContext(ctx)
}

func (x xAgentBlacklist) TableName() string { return x.xAgentBlacklistDo.TableName() }

func (x xAgentBlacklist) Alias() string { return x.xAgentBlacklistDo.Alias() }

func (x xAgentBlacklist) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentBlacklistDo.Columns(cols...)
}

func (x *xAgentBlacklist) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentBlacklist) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 10)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserType"] = x.UserType
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["AgentId"] = x.AgentID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CreateAccount"] = x.CreateAccount
}

func (x xAgentBlacklist) clone(db *gorm.DB) xAgentBlacklist {
	x.xAgentBlacklistDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentBlacklist) replaceDB(db *gorm.DB) xAgentBlacklist {
	x.xAgentBlacklistDo.ReplaceDB(db)
	return x
}

type xAgentBlacklistDo struct{ gen.DO }

func (x xAgentBlacklistDo) Debug() *xAgentBlacklistDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentBlacklistDo) WithContext(ctx context.Context) *xAgentBlacklistDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentBlacklistDo) ReadDB() *xAgentBlacklistDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentBlacklistDo) WriteDB() *xAgentBlacklistDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentBlacklistDo) Session(config *gorm.Session) *xAgentBlacklistDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentBlacklistDo) Clauses(conds ...clause.Expression) *xAgentBlacklistDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentBlacklistDo) Returning(value interface{}, columns ...string) *xAgentBlacklistDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentBlacklistDo) Not(conds ...gen.Condition) *xAgentBlacklistDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentBlacklistDo) Or(conds ...gen.Condition) *xAgentBlacklistDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentBlacklistDo) Select(conds ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentBlacklistDo) Where(conds ...gen.Condition) *xAgentBlacklistDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentBlacklistDo) Order(conds ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentBlacklistDo) Distinct(cols ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentBlacklistDo) Omit(cols ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentBlacklistDo) Join(table schema.Tabler, on ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentBlacklistDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentBlacklistDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentBlacklistDo) Group(cols ...field.Expr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentBlacklistDo) Having(conds ...gen.Condition) *xAgentBlacklistDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentBlacklistDo) Limit(limit int) *xAgentBlacklistDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentBlacklistDo) Offset(offset int) *xAgentBlacklistDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentBlacklistDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentBlacklistDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentBlacklistDo) Unscoped() *xAgentBlacklistDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentBlacklistDo) Create(values ...*model.XAgentBlacklist) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentBlacklistDo) CreateInBatches(values []*model.XAgentBlacklist, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentBlacklistDo) Save(values ...*model.XAgentBlacklist) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentBlacklistDo) First() (*model.XAgentBlacklist, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentBlacklist), nil
	}
}

func (x xAgentBlacklistDo) Take() (*model.XAgentBlacklist, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentBlacklist), nil
	}
}

func (x xAgentBlacklistDo) Last() (*model.XAgentBlacklist, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentBlacklist), nil
	}
}

func (x xAgentBlacklistDo) Find() ([]*model.XAgentBlacklist, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentBlacklist), err
}

func (x xAgentBlacklistDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentBlacklist, err error) {
	buf := make([]*model.XAgentBlacklist, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentBlacklistDo) FindInBatches(result *[]*model.XAgentBlacklist, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentBlacklistDo) Attrs(attrs ...field.AssignExpr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentBlacklistDo) Assign(attrs ...field.AssignExpr) *xAgentBlacklistDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentBlacklistDo) Joins(fields ...field.RelationField) *xAgentBlacklistDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentBlacklistDo) Preload(fields ...field.RelationField) *xAgentBlacklistDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentBlacklistDo) FirstOrInit() (*model.XAgentBlacklist, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentBlacklist), nil
	}
}

func (x xAgentBlacklistDo) FirstOrCreate() (*model.XAgentBlacklist, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentBlacklist), nil
	}
}

func (x xAgentBlacklistDo) FindByPage(offset int, limit int) (result []*model.XAgentBlacklist, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentBlacklistDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentBlacklistDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentBlacklistDo) Delete(models ...*model.XAgentBlacklist) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentBlacklistDo) withDO(do gen.Dao) *xAgentBlacklistDo {
	x.DO = *do.(*gen.DO)
	return x
}
