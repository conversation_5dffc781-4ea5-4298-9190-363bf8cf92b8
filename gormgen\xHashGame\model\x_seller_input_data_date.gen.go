// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXSellerInputDataDate = "x_seller_input_data_date"

// XSellerInputDataDate 运营商手动录入数据(按日期)
type XSellerInputDataDate struct {
	RecordDate        time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	SellerID          int32     `gorm:"column:SellerId;primaryKey" json:"SellerId"`
	AskServiceUsers   int32     `gorm:"column:AskServiceUsers;comment:咨询客服人数" json:"AskServiceUsers"`                        // 咨询客服人数
	BackVisitUsers    int32     `gorm:"column:BackVisitUsers;comment:回访人数" json:"BackVisitUsers"`                            // 回访人数
	LeadRechargeUsers int32     `gorm:"column:LeadRechargeUsers;comment:引导充值人数" json:"LeadRechargeUsers"`                    // 引导充值人数
	RechargeAmount    float64   `gorm:"column:RechargeAmount;default:0.000000;comment:充值金额U" json:"RechargeAmount"`          // 充值金额U
	BackRewardAmount  float64   `gorm:"column:BackRewardAmount;default:0.000000;comment:召回彩金U" json:"BackRewardAmount"`      // 召回彩金U
	WithdrawUsers     int32     `gorm:"column:WithdrawUsers;comment:提款人数" json:"WithdrawUsers"`                              // 提款人数
	WithdrawAmount    float64   `gorm:"column:WithdrawAmount;default:0.000000;comment:提款金额" json:"WithdrawAmount"`           // 提款金额
	CTDiff            float64   `gorm:"column:CTDiff;comment:充提差" json:"CTDiff"`                                             // 充提差
	CreateTime        time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime        time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XSellerInputDataDate's table name
func (*XSellerInputDataDate) TableName() string {
	return TableNameXSellerInputDataDate
}
