package robot

import "xserver/server"

type Router struct{}

func (c *Router) Init() {
	group := server.Http().NewGroup("/api/robot")
	{
		group.Post("/report/list", c.reportList)
		group.Post("/report/guest/detail/list", c.reportListByRobotName)
		//  活动 任务
		group.Post("/mission/template/list", c.missionTempList)
		group.Post("/mission/template/create", c.missionTempCreate)
		group.Post("/mission/template/update", c.missionTempUpdate)
		group.Post("/mission/template/delete", c.missionTempDelete)

		// 机器人配置
		group.Post("/config/template/list", c.configList)
		group.Post("/config/template/create", c.configCreate)
		group.Post("/config/template/update", c.configUpdate)
		group.Post("/config/template/delete", c.configDelete)

		// 消息配置
		group.Post("/message/template/list", c.messagesList)
		group.Post("/message/template/create", c.messagesCreate)
		group.Post("/message/template/update", c.messagesUpdate)
		group.Post("/message/template/delete", c.messagesDelete)

		//广告机器人数据报表
		group.Post("/report/ad/list", c.reportListByAD)

		// 红包 答题 配置
		group.Post("/config/questions/list", c.questionsList)
		group.Post("/config/questions/create", c.questionsCreate)
		group.Post("/config/questions/update", c.questionsUpdate)
		group.Post("/config/questions/delete", c.questionsDelete)

		// 红包 报表
		group.Post("/report/red_bag/list", c.reportRedPacketByUser)
		group.Post("/report/red_bag/sign/list", c.reportRedPacketBySign)
		group.Post("/report/red_bag/sign/detail", c.reportRedPacketBySignByDetail)
		group.Post("/report/red_bag/red_bag/list", c.reportRedPacketByRedBag)
		group.Post("/report/red_bag/red_bag/detail", c.reportRedPacketByRedBagByDetail)
		group.Post("/report/red_bag/question/list", c.reportRedPacketByAnswer)
		group.Post("/report/red_bag/question/detail", c.reportRedPacketByAnswerByDetail)
		group.Post("/report/red_bag/exchange/list", c.reportRedPacketByExchange)
		group.Post("/report/red_bag/points/detail", c.reportRedPacketByPointsDetail)

		// 推送消息活动类型配置
		group.Post("/config/push_activity/list", c.pushActivityList)
		group.Post("/config/push_activity/create", c.pushActivityCreate)
		group.Post("/config/push_activity/update", c.pushActivityUpdate)
		group.Post("/config/push_activity/delete", c.pushActivityDelete)

		// 推广活动报表
		group.Post("/report/push_activity/list", c.pushPromoteReport)
		group.Post("/report/push_activity/total/list", c.pushPromoteReportBySum)
		// 三方推送飞机号
		group.Post("/report/third_push/list", c.thirdPushDataReport)

		// 机器人引流报表
		group.Post("/report/import_flow/upload", c.uploadExtendFileData)
		group.Post("/report/import_flow/list", c.reportData)

		//转发机器人  reportByForward
		group.Post("/report/forward/list", c.reportByForward)
		group.Post("/monitor/send_code", c.sendCodeByTelegram)
		group.Post("/monitor/verify_code", c.verifyCodeByTelegram)
	}
}
