package controller

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/xuri/excelize/v2"
)

type ThreeLevelAgentController struct{}

// GameOrderData 游戏注单数据结构（用于游戏佣金查询）
type GameOrderData struct {
	UserId             *int     `json:"UserId" gorm:"column:UserId"`                         // 用户ID
	UserAddress        *string  `json:"UserAddress" gorm:"column:UserAddress"`               // 用户地址
	AgentLevel         *int     `json:"AgentLevel" gorm:"column:AgentLevel"`                 // 代理等级
	GameType           *string  `json:"GameType" gorm:"column:GameType"`                     // 游戏类型
	OrderId            *string  `json:"OrderId" gorm:"column:OrderId"`                       // 注单号
	BetAmount          *float64 `json:"BetAmount" gorm:"column:BetAmount"`                   // 投注金额
	Symbol             *string  `json:"Symbol" gorm:"column:Symbol"`                         // 币种
	ValidBetAmount     *float64 `json:"ValidBetAmount" gorm:"column:ValidBetAmount"`         // 有效投注
	CommissionRate     *float64 `json:"CommissionRate" gorm:"column:CommissionRate"`         // 返佣比例
	CommissionAmount   *float64 `json:"CommissionAmount" gorm:"column:CommissionAmount"`     // 返佣金额
	UserName           *string  `json:"UserName" gorm:"column:UserName"`                     // 用户名
	WinAmount          *float64 `json:"WinAmount" gorm:"column:WinAmount"`                   // 中奖金额
	BetTime            *string  `json:"BetTime" gorm:"column:BetTime"`                       // 下注时间
	SettleTime         *string  `json:"SettleTime" gorm:"column:SettleTime"`                 // 结算时间
	CommissionDateTime *string  `json:"CommissionDateTime" gorm:"column:CommissionDateTime"` // 返佣计算时间
}

func (c *ThreeLevelAgentController) Init() {
	group := server.Http().NewGroup("/api/three_level_agent")
	{
		// 方案管理（包含团队返佣配置和佣金配置）
		group.Post("/scheme/list", c.schemeList)
		group.Post("/scheme/detail", c.schemeDetail)
		group.Post("/scheme/save", c.schemeSave)
		group.Post("/scheme/delete", c.schemeDelete)
		group.Post("/scheme/toggle_status", c.schemeToggleStatus)

		// // 三级代理数据报表
		group.Post("/report/list", c.reportList)

		// 团队自身
		group.Post("/team/:agentId", c.teamSelf)

		// 查询团队人数数据
		group.Post("/team/list/:agentId", c.teamList)

		// 查询团队等级人数数据
		group.Post("/team/list/:agentId/:level", c.teamLevelList)

		// 查询直属团队数据（代理人数大于0)
		group.Post("/team/strict/list/:agentId", c.teamStrictList)

		// 首充人数
		group.Post("/team/firstRecharge/list/:agentId", c.teamFirstRecharge)

		// // 三级代理佣金数据报表
		group.Post("/commission/report/list", c.commissionReportList)

		// 有效人数数据
		group.Post("/commission/valid/list/:agentId", c.commissionValidUserList)

		// 游戏佣金数据
		group.Post("/commission/gameOrder/list/:agentId", c.commissionGameList)

		// // 三级代理佣金审核列表
		// group.Post("/commission/audit/list", c.commissionAuditList)

		// // 三级代理佣金审核通过
		// group.Post("/commission/audit/pass", c.commissionAuditPass)

		// // 三级代理佣金审核拒绝
		// group.Post("/commission/audit/reject", c.commissionAuditReject)
	}
}

// 方案列表
func (c *ThreeLevelAgentController) schemeList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Name string `json:"Name"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理", "查"), &errcode, "权限不足") {
		return
	}

	schemeTb := server.DaoxHashGame().XAgentCommissionScheme
	query := schemeTb.WithContext(ctx.Gin())

	// 构建查询条件
	if reqdata.Name != "" {
		query = query.Where(schemeTb.SchemeName.Like("%" + reqdata.Name + "%"))
	}

	type Result struct {
		*model.XAgentCommissionScheme
		IsUsed int32 `json:"IsUsed"`
	}
	var results []*Result
	_ = query.Order(schemeTb.CreateTime.Desc()).Scan(&results)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 判断是否被使用
	xChannel := server.DaoxHashGame().XChannel
	for i, _ := range results {
		count, err := xChannel.WithContext(ctx.Gin()).Where(
			xChannel.AgentCaseID.Eq(results[i].SchemeID),
		).Count()
		if err != nil {
			continue
		}
		if count > 0 {
			results[i].IsUsed = 1
		}
	}

	ctx.RespOK(results)
	server.WriteAdminLog("三级代理方案列表查询", ctx, reqdata)
}

// 方案详情（包含团队等级配置）
func (c *ThreeLevelAgentController) schemeDetail(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SchemeID int32 `json:"SchemeId" validate:"required"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理", "查"), &errcode, "权限不足") {
		return
	}

	// 查询方案基本信息
	schemeTb := server.DaoxHashGame().XAgentCommissionScheme
	scheme, err := schemeTb.WithContext(ctx.Gin()).Where(
		schemeTb.SchemeID.Eq(reqdata.SchemeID),
	).First()
	if ctx.RespErrString(err != nil, &errcode, "方案不存在") {
		return
	}

	// 查询团队等级定义
	teamDefineTb := server.DaoxHashGame().XAgentCommissionTeamDefine
	teamDefines, err := teamDefineTb.WithContext(ctx.Gin()).Where(
		teamDefineTb.SchemeID.Eq(reqdata.SchemeID),
	).Order(teamDefineTb.TeamLevel.Asc()).Find()
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询佣金等级配置
	levelDefineTb := server.DaoxHashGame().XAgentCommissionLevelDefine
	levelDefines, err := levelDefineTb.WithContext(ctx.Gin()).Where(
		levelDefineTb.SchemeID.Eq(reqdata.SchemeID),
	).Order(levelDefineTb.TeamLevel.Asc(), levelDefineTb.AgentLevel.Asc()).Find()
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 组装返回数据
	result := map[string]interface{}{
		"scheme":       scheme,
		"teamDefines":  teamDefines,
		"levelDefines": levelDefines,
	}

	ctx.RespOK(result)
	// server.WriteAdminLog("查询三级代理方案详情", ctx, reqdata)
}

// 保存方案（创建或更新，包含团队等级配置和佣金配置）
func (c *ThreeLevelAgentController) schemeSave(ctx *abugo.AbuHttpContent) {
	type TeamDefineData struct {
		TeamLevel         int32   `json:"TeamLevel"`
		TotalValidUsers   int32   `json:"TotalValidUsers"`
		TotalValidLiuShui float64 `json:"TotalValidLiuShui"`
	}

	type LevelDefineData struct {
		TeamLevel          int32   `json:"TeamLevel"`          // 团队等级
		AgentLevel         int32   `json:"AgentLevel"`         // 代理等级
		RewardHaXi         float64 `json:"RewardHaXi"`         // 哈希返佣(百分比)
		RewardHaXiRoulette float64 `json:"RewardHaXiRoulette"` // 哈希轮盘返佣(百分比)
		RewardLottery      float64 `json:"RewardLottery"`      // 彩票返佣(百分比)
		RewardLowLottery   float64 `json:"RewardLowLottery"`   // 低频彩返佣(百分比)
		RewardQiPai        float64 `json:"RewardQiPai"`        // 棋牌返佣(百分比)
		RewardDianZhi      float64 `json:"RewardDianZhi"`      // 电子返佣(百分比)
		RewardXiaoYouXi    float64 `json:"RewardXiaoYouXi"`    // 小游戏返佣(百分比)
		RewardLive         float64 `json:"RewardLive"`         // 真人返佣(百分比)
		RewardSport        float64 `json:"RewardSport"`        // 体育返佣(百分比)
		RewardTexas        float64 `json:"RewardTexas"`        // 德州返佣(百分比)
	}

	type RequestData struct {
		SchemeID            *int32            `json:"SchemeId"` // 为空表示新增，有值表示更新
		SchemeName          string            `json:"SchemeName" validate:"required"`
		GetType             int32             `json:"GetType"`             // 发放方式 1人工发放 2自动发放
		GetAmountType       int32             `json:"GetAmountType"`       // 发放钱包 1真金钱包 2bonus钱包
		RollingTimes        float64           `json:"RollingTimes"`        // 打码倍数
		IsSumTeam           int32             `json:"IsSumTeam"`           // 自身流水是否累计团队:1累计 2不累计
		ValidRechargeAmount float64           `json:"ValidRechargeAmount"` // 有效充值金额
		ValidLiuShui        float64           `json:"ValidLiuShui"`        // 有效流水
		TeamDefines         []TeamDefineData  `json:"TeamDefines"`         // 团队等级配置
		LevelDefines        []LevelDefineData `json:"LevelDefines"`        // 佣金等级配置
		GoogleCode          string            `json:"GoogleCode" validate:"required"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	authAction := "增"
	if reqdata.SchemeID != nil {
		authAction = "改"
		// 判断渠道中是否有被使用
		xChannel := server.DaoxHashGame().XChannel
		count, err := xChannel.WithContext(ctx.Gin()).Where(
			xChannel.AgentCaseID.Eq(*reqdata.SchemeID),
		).Count()
		if ctx.RespErr(err, &errcode) {
			return
		}
		if count > 0 {
			ctx.RespErrString(true, &errcode, "方案已被使用，无法修改")
			return
		}
	}
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理", authAction), &errcode, "权限不足") {
		return
	}

	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	// 使用事务保存方案和团队等级配置
	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		var schemeID int32

		if reqdata.SchemeID == nil {
			// 新增方案
			scheme := &model.XAgentCommissionScheme{
				SchemeName:          reqdata.SchemeName,
				GetType:             reqdata.GetType,
				GetAmountType:       reqdata.GetAmountType,
				RollingTimes:        reqdata.RollingTimes,
				IsSumTeam:           reqdata.IsSumTeam,
				ValidRechargeAmount: reqdata.ValidRechargeAmount,
				ValidLiuShui:        reqdata.ValidLiuShui,
				Status:              1, // 默认启用
				CreateTime:          time.Now(),
				UpdateTime:          time.Now(),
			}

			err := tx.XAgentCommissionScheme.WithContext(ctx.Gin()).Create(scheme)
			if err != nil {
				return err
			}
			schemeID = scheme.SchemeID

			// 新增方案时，保存团队等级配置
			for _, teamDefine := range reqdata.TeamDefines {
				teamDefineModel := &model.XAgentCommissionTeamDefine{
					SchemeID:          schemeID,
					TeamLevel:         teamDefine.TeamLevel,
					TotalValidUsers:   teamDefine.TotalValidUsers,
					TotalValidLiuShui: teamDefine.TotalValidLiuShui,
					CreateTime:        time.Now(),
					UpdateTime:        time.Now(),
				}

				err := tx.XAgentCommissionTeamDefine.WithContext(ctx.Gin()).Create(teamDefineModel)
				if err != nil {
					return err
				}
			}

			// 新增方案时，保存佣金等级配置
			for _, levelDefine := range reqdata.LevelDefines {
				levelDefineModel := &model.XAgentCommissionLevelDefine{
					SchemeID:           schemeID,
					TeamLevel:          levelDefine.TeamLevel,
					AgentLevel:         levelDefine.AgentLevel,
					RewardHaXi:         levelDefine.RewardHaXi,
					RewardHaXiRoulette: levelDefine.RewardHaXiRoulette,
					RewardLottery:      levelDefine.RewardLottery,
					RewardLowLottery:   levelDefine.RewardLowLottery,
					RewardQiPai:        levelDefine.RewardQiPai,
					RewardDianZhi:      levelDefine.RewardDianZhi,
					RewardXiaoYouXi:    levelDefine.RewardXiaoYouXi,
					RewardLive:         levelDefine.RewardLive,
					RewardSport:        levelDefine.RewardSport,
					RewardTexas:        levelDefine.RewardTexas,
					CreateTime:         time.Now(),
					UpdateTime:         time.Now(),
				}

				err := tx.XAgentCommissionLevelDefine.WithContext(ctx.Gin()).Create(levelDefineModel)
				if err != nil {
					return err
				}
			}
		} else {
			// 更新方案
			schemeID = *reqdata.SchemeID
			_, err := tx.XAgentCommissionScheme.WithContext(ctx.Gin()).Where(
				tx.XAgentCommissionScheme.SchemeID.Eq(schemeID),
			).Updates(&model.XAgentCommissionScheme{
				SchemeName:          reqdata.SchemeName,
				GetType:             reqdata.GetType,
				GetAmountType:       reqdata.GetAmountType,
				RollingTimes:        reqdata.RollingTimes,
				IsSumTeam:           reqdata.IsSumTeam,
				ValidRechargeAmount: reqdata.ValidRechargeAmount,
				ValidLiuShui:        reqdata.ValidLiuShui,
				UpdateTime:          time.Now(),
			})
			if err != nil {
				return err
			}

			// 删除原有的团队等级配置
			_, err = tx.XAgentCommissionTeamDefine.WithContext(ctx.Gin()).Where(
				tx.XAgentCommissionTeamDefine.SchemeID.Eq(schemeID),
			).Delete()
			if err != nil {
				return err
			}

			// 删除原有的佣金等级配置
			_, err = tx.XAgentCommissionLevelDefine.WithContext(ctx.Gin()).Where(
				tx.XAgentCommissionLevelDefine.SchemeID.Eq(schemeID),
			).Delete()
			if err != nil {
				return err
			}

			// 更新方案时，重新保存团队等级配置
			for _, teamDefine := range reqdata.TeamDefines {
				teamDefineModel := &model.XAgentCommissionTeamDefine{
					SchemeID:          schemeID,
					TeamLevel:         teamDefine.TeamLevel,
					TotalValidUsers:   teamDefine.TotalValidUsers,
					TotalValidLiuShui: teamDefine.TotalValidLiuShui,
					CreateTime:        time.Now(),
					UpdateTime:        time.Now(),
				}

				err := tx.XAgentCommissionTeamDefine.WithContext(ctx.Gin()).Create(teamDefineModel)
				if err != nil {
					return err
				}
			}

			// 更新方案时，重新保存佣金等级配置
			for _, levelDefine := range reqdata.LevelDefines {
				levelDefineModel := &model.XAgentCommissionLevelDefine{
					SchemeID:           schemeID,
					TeamLevel:          levelDefine.TeamLevel,
					AgentLevel:         levelDefine.AgentLevel,
					RewardHaXi:         levelDefine.RewardHaXi,
					RewardHaXiRoulette: levelDefine.RewardHaXiRoulette,
					RewardLottery:      levelDefine.RewardLottery,
					RewardLowLottery:   levelDefine.RewardLowLottery,
					RewardQiPai:        levelDefine.RewardQiPai,
					RewardDianZhi:      levelDefine.RewardDianZhi,
					RewardXiaoYouXi:    levelDefine.RewardXiaoYouXi,
					RewardLive:         levelDefine.RewardLive,
					RewardSport:        levelDefine.RewardSport,
					RewardTexas:        levelDefine.RewardTexas,
					CreateTime:         time.Now(),
					UpdateTime:         time.Now(),
				}

				err := tx.XAgentCommissionLevelDefine.WithContext(ctx.Gin()).Create(levelDefineModel)
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

	if ctx.RespErr(err, &errcode) {
		return
	}

	actionText := "创建"
	if reqdata.SchemeID != nil {
		actionText = "更新"
	}

	ctx.RespOK()
	server.WriteAdminLog(fmt.Sprintf("%s三级代理方案", actionText), ctx, reqdata)
}

// 删除方案
func (c *ThreeLevelAgentController) schemeDelete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SchemeID   int32  `json:"SchemeId" validate:"required"`
		GoogleCode string `json:"GoogleCode" validate:"required"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理", "删"), &errcode, "权限不足") {
		return
	}

	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	// 检查方案是否存在
	schemeTb := server.DaoxHashGame().XAgentCommissionScheme
	_, err = schemeTb.WithContext(ctx.Gin()).Where(
		schemeTb.SchemeID.Eq(reqdata.SchemeID),
	).First()
	if ctx.RespErrString(err != nil, &errcode, "方案不存在") {
		return
	}

	// 是否被使用
	xChannel := server.DaoxHashGame().XChannel
	count, err := xChannel.WithContext(ctx.Gin()).Where(
		xChannel.AgentCaseID.Eq(reqdata.SchemeID),
	).Count()

	if ctx.RespErr(err, &errcode) {
		return
	}
	if count > 0 {
		ctx.RespErrString(true, &errcode, "方案已被使用，无法删除")
		return
	}

	// 使用事务删除方案及相关数据
	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		// 删除团队等级定义
		_, err := tx.XAgentCommissionTeamDefine.WithContext(ctx.Gin()).Where(
			tx.XAgentCommissionTeamDefine.SchemeID.Eq(reqdata.SchemeID),
		).Delete()
		if err != nil {
			return err
		}

		// 删除方案
		_, err = tx.XAgentCommissionScheme.WithContext(ctx.Gin()).Where(
			tx.XAgentCommissionScheme.SchemeID.Eq(reqdata.SchemeID),
		).Delete()
		return err
	})

	if ctx.RespErr(err, &errcode) {
		return
	}

	ctx.RespOK()
	server.WriteAdminLog("删除三级代理方案", ctx, reqdata)
}

// 切换方案状态
func (c *ThreeLevelAgentController) schemeToggleStatus(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SchemeID   int32  `json:"SchemeId" validate:"required"`
		Status     int32  `json:"Status" validate:"required"` // 1-启用, 2-禁用
		GoogleCode string `json:"GoogleCode" validate:"required"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理", "改"), &errcode, "权限不足") {
		return
	}

	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	// 是否被使用
	xChannel := server.DaoxHashGame().XChannel
	count, err := xChannel.WithContext(ctx.Gin()).Where(
		xChannel.AgentCaseID.Eq(reqdata.SchemeID),
	).Count()

	if ctx.RespErr(err, &errcode) {
		return
	}
	if count > 0 {
		ctx.RespErrString(true, &errcode, "方案已被使用，无法修改")
		return
	}

	// 更新状态
	schemeTb := server.DaoxHashGame().XAgentCommissionScheme
	_, err = schemeTb.WithContext(ctx.Gin()).Where(
		schemeTb.SchemeID.Eq(reqdata.SchemeID),
	).Updates(&model.XAgentCommissionScheme{
		Status:     reqdata.Status,
		UpdateTime: time.Now(),
	})

	if ctx.RespErr(err, &errcode) {
		return
	}

	statusText := "启用"
	if reqdata.Status == 2 {
		statusText = "禁用"
	}

	ctx.RespOK()
	server.WriteAdminLog(fmt.Sprintf("%s三级代理方案", statusText), ctx, reqdata)
}

// 三级代理数据报表
func (c *ThreeLevelAgentController) reportList(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		GameType  int
		AgentId   int
		Symbol    string
		SellerId  int
		ChannelId int
		Export    int // 0-分页查询，1-导出报表
	}

	type ListData struct {
		AgentId                      int     `json:"AgentId" gorm:"column:AgentId"`                                           // 代理ID
		AccountName                  string  `json:"AccountName" gorm:"column:AccountName"`                                   // 账户名
		TeamUserCount                int     `json:"TeamUserCount" gorm:"column:TeamUserCount"`                               // 团队人数
		YesterDayLevel               int     `json:"YesterDayLevel" gorm:"column:YesterDayLevel"`                             // 昨日团队等级
		Level1UserCount              int     `json:"Level1UserCount" gorm:"column:Level1UserCount"`                           // 一级代理人数
		Level2UserCount              int     `json:"Level2UserCount" gorm:"column:Level2UserCount"`                           // 二级代理人数
		Level3UserCount              int     `json:"Level3UserCount" gorm:"column:Level3UserCount"`                           // 三级代理人数
		Level1RechargeAmount         float64 `json:"Level1RechargeAmount" gorm:"column:Level1RechargeAmount"`                 // 一级代理充值金额
		Level1FirstRechargeAmount    float64 `json:"Level1FirstRechargeAmount" gorm:"column:Level1FirstRechargeAmount"`       // 一级代理首充金额
		Level1FirstRechargeUserCount int     `json:"Level1FirstRechargeUserCount" gorm:"column:Level1FirstRechargeUserCount"` // 一级代理首充人数
		Level1BetAmount              float64 `json:"Level1BetAmount" gorm:"column:Level1BetAmount"`                           // 一级代理有效投注
		Level1ValidLiuShui           float64 `json:"Level1ValidLiuShui" gorm:"column:Level1ValidLiuShui"`                     // 一级代理有效流水
		Level2RechargeAmount         float64 `json:"Level2RechargeAmount" gorm:"column:Level2RechargeAmount"`                 // 二级代理充值金额
		Level2FirstRechargeAmount    float64 `json:"Level2FirstRechargeAmount" gorm:"column:Level2FirstRechargeAmount"`       // 二级代理首充金额
		Level2FirstRechargeUserCount int     `json:"Level2FirstRechargeUserCount" gorm:"column:Level2FirstRechargeUserCount"` // 二级代理首充人数
		Level2BetAmount              float64 `json:"Level2BetAmount" gorm:"column:Level2BetAmount"`                           // 二级代理有效投注
		Level2ValidLiuShui           float64 `json:"Level2ValidLiuShui" gorm:"column:Level2ValidLiuShui"`                     // 二级代理有效流水
		Level3RechargeAmount         float64 `json:"Level3RechargeAmount" gorm:"column:Level3RechargeAmount"`                 // 三级代理充值金额
		Level3FirstRechargeAmount    float64 `json:"Level3FirstRechargeAmount" gorm:"column:Level3FirstRechargeAmount"`       // 三级代理首充金额
		Level3FirstRechargeUserCount int     `json:"Level3FirstRechargeUserCount" gorm:"column:Level3FirstRechargeUserCount"` // 三级代理首充人数
		Level3BetAmount              float64 `json:"Level3BetAmount" gorm:"column:Level3BetAmount"`                           // 三级代理有效投注
		Level3ValidLiuShui           float64 `json:"Level3ValidLiuShui" gorm:"column:Level3ValidLiuShui"`                     // 三级代理有效流水

		TotalRechargeAmount   float64 `json:"TotalRechargeAmount" gorm:"column:TotalRechargeAmount"`     // 总充值金额
		TotalBetAmount        float64 `json:"TotalBetAmount" gorm:"column:TotalBetAmount"`               // 总有效投注
		TotalValidLiuShui     float64 `json:"TotalValidLiuShui" gorm:"column:TotalValidLiuShui"`         // 总有效流水
		TotalWinAmount        float64 `json:"TotalWinAmount" gorm:"column:TotalWinAmount"`               // 总中奖金额
		TotalCaijinAmount     float64 `json:"TotalCaijinAmount" gorm:"column:TotalCaijinAmount"`         // 总彩金金额
		TotalFanshuiAmount    float64 `json:"TotalFanshuiAmount" gorm:"column:TotalFanshuiAmount"`       // 总返水金额
		TotalCommissionAmount float64 `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"` // 总佣金金额
		TotalWinLossAmount    float64 `json:"TotalWinLossAmount" gorm:"column:TotalWinLossAmount"`       // 总派彩金额
	}

	type ResponseData struct {
		Total     int64
		Data      []ListData
		TotalData ListData
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理数据报表", "查"), &errcode, "权限不足") {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	// 时间条件
	if reqdata.StartTime > 0 {
		whereConditions = append(whereConditions, "d.StatDate >= ?")
		whereParams = append(whereParams, abugo.TimeStampToLocalTime(reqdata.StartTime))
	}
	if reqdata.EndTime > 0 {
		whereConditions = append(whereConditions, "d.StatDate <= ?")
		whereParams = append(whereParams, abugo.TimeStampToLocalTime(reqdata.EndTime))
	}

	// 代理ID条件
	if reqdata.AgentId > 0 {
		whereConditions = append(whereConditions, "d.AgentId = ?")
		whereParams = append(whereParams, reqdata.AgentId)
	}

	// 运营商条件
	if reqdata.SellerId > 0 {
		whereConditions = append(whereConditions, "d.SellerId = ?")
		whereParams = append(whereParams, reqdata.SellerId)
	}

	// 渠道条件
	if reqdata.ChannelId > 0 {
		whereConditions = append(whereConditions, "d.ChannelId = ?")
		whereParams = append(whereParams, reqdata.ChannelId)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.AgentId,
			IFNULL(u.Account, '') AS AccountName,
			-- IFNULL(SUM(d.BetTotalUsers),0) AS BetTotalUsers,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId) as BetTotalUsers,
			-- IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.VaildUsers ELSE 0 END),0) AS VaildUsers1,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId and AgentLevel = 1) as VaildUsers1,
			IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.RechargeAmount ELSE 0 END),0) AS RechargeAmount1,
			IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount1,
			IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers1,
			d.TeamLevel AS YesterDayLevel,
			-- 一级代理有效投注
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount1,
			
			-- 一级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount1,

			-- IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.VaildUsers ELSE 0 END),0) AS VaildUsers2,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId and AgentLevel = 2) as VaildUsers2,
			IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.RechargeAmount ELSE 0 END),0) AS RechargeAmount2,
			IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount2,
			IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers2,
			-- 二级代理有效投注
			IFNULL(SUM(CASE
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount2,
			
			-- 二级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount2,

			-- IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.VaildUsers ELSE 0 END),0) AS VaildUsers3,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId and AgentLevel = 3) as VaildUsers3,
			IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.RechargeAmount ELSE 0 END),0) AS RechargeAmount3,
			IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount3,
			IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers3,
			-- 三级代理有效投注
			IFNULL(SUM(CASE
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount3,
			
			-- 三级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount3,

			IFNULL(SUM(d.RechargeAmount),0) AS RechargeAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS BetAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS LiuShuiAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi+d.WinTranferTrxHaXiRoulette+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS WinAmount,
			IFNULL(SUM(d.ActivityRewardAmount),0) AS ActivityRewardAmount,
			IFNULL(SUM(d.VipRewardAmount),0) AS VipRewardAmount,
			IFNULL(SUM(d.GetCommissionAmount),0) AS GetCommissionAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas) - SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi+d.WinTranferTrxHaXiRoulette+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinLossAmount
		FROM x_agent_commission_level_date d
		LEFT JOIN x_user u ON d.AgentId = u.UserId
		%s
		GROUP BY d.AgentId
		ORDER BY DATE(d.StatDate) DESC
		LIMIT ? OFFSET ?`, whereClause)

	// 处理币种参数
	symbolValue := 0
	if reqdata.Symbol == "USDT" {
		symbolValue = 1
	} else if reqdata.Symbol == "TRX" {
		symbolValue = 2
	}

	// 构建参数数组 - 需要重复多次GameType和Symbol参数
	var queryParams []interface{}

	// 每个代理等级有2个部分（投注金额+流水），每个部分有8个WHEN条件，每个条件2个占位符
	// 总共：3个等级 × 2个部分 × 8个条件 × 2个占位符 = 96个占位符
	for i := 0; i < 72; i++ {
		queryParams = append(queryParams, reqdata.GameType, symbolValue)
	}

	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)
	// 添加分页参数
	queryParams = append(queryParams, limit, offset)

	// 打印SQL日志用于调试
	// fmt.Printf("=== 分页查询SQL ===\n")
	// fmt.Printf("SQL: %s\n", sql)
	// fmt.Printf("参数数量: %d\n", len(queryParams))
	// fmt.Printf("参数: %+v\n", queryParams)
	// fmt.Printf("==================\n")

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		err := rows.Scan(
			&data.AgentId,
			&data.AccountName,                  // AccountName
			&data.TeamUserCount,                // BetTotalUsers
			&data.Level1UserCount,              // VaildUsers1
			&data.Level1RechargeAmount,         // RechargeAmount1
			&data.Level1FirstRechargeAmount,    // FirstRechargeAmount1
			&data.Level1FirstRechargeUserCount, // FirstRechargeUsers1
			&data.YesterDayLevel,
			&data.Level1BetAmount,              // BetAmount1
			&data.Level1ValidLiuShui,           // LiuShuiAmount1
			&data.Level2UserCount,              // VaildUsers2
			&data.Level2RechargeAmount,         // RechargeAmount2
			&data.Level2FirstRechargeAmount,    // FirstRechargeAmount2
			&data.Level2FirstRechargeUserCount, // FirstRechargeUsers2
			&data.Level2BetAmount,              // BetAmount2
			&data.Level2ValidLiuShui,           // LiuShuiAmount2
			&data.Level3UserCount,              // VaildUsers3
			&data.Level3RechargeAmount,         // RechargeAmount3
			&data.Level3FirstRechargeAmount,    // FirstRechargeAmount3
			&data.Level3FirstRechargeUserCount, // FirstRechargeUsers3
			&data.Level3BetAmount,              // BetAmount3
			&data.Level3ValidLiuShui,           // LiuShuiAmount3
			&data.TotalRechargeAmount,          // RechargeAmount
			&data.TotalBetAmount,               // BetAmount
			&data.TotalValidLiuShui,            // LiuShuiAmount
			&data.TotalWinAmount,               // WinAmount
			&data.TotalCaijinAmount,            // ActivityRewardAmount
			&data.TotalFanshuiAmount,           // VipRewardAmount
			&data.TotalCommissionAmount,        // GetCommissionAmount
			&data.TotalWinLossAmount,           // TotalWinLossAmount
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	// 查询总数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(DISTINCT d.AgentId) as total
		FROM x_agent_commission_level_date as d
		%s`, whereClause)

	// 打印总数查询SQL日志用于调试
	// fmt.Printf("=== 总数查询SQL ===\n")
	// fmt.Printf("SQL: %s\n", countSQL)
	// fmt.Printf("参数数量: %d\n", len(whereParams))
	// fmt.Printf("参数: %+v\n", whereParams)
	// fmt.Printf("==================\n")

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, whereParams...).Scan(&total).Error

	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据 - 使用SQL直接计算
	totalSQL := fmt.Sprintf(`
		SELECT
			-- IFNULL(SUM(BetTotalUsers),0) AS BetTotalUsers,
			(SELECT COUNT(DISTINCT sub.UserId,sub.AgentId)
				FROM x_agent_commission_user_date sub
				LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
				WHERE (sub.StatDate >= ? OR ? = 0)
				AND (sub.AgentId IN (SELECT AgentId FROM x_agent_commission_level_date GROUP BY AgentId))
				AND (sub.ParentAgentId = ? OR ? = 0)
				AND (sub.SellerId = ? OR ? = 0)
				AND (sub.ChannelId = ? OR ? = 0)
				AND (u2.Account LIKE ? OR ? = '')
				AND (sub.AgentId = ? OR ? = 0)
			) AS BetTotalUsers,
			-- IFNULL(SUM(CASE WHEN AgentLevel=1 THEN VaildUsers ELSE 0 END),0) AS VaildUsers1,
			(SELECT COUNT(DISTINCT sub.UserId)
				FROM x_agent_commission_user_date sub
				LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
				WHERE sub.AgentLevel = 1
				AND (sub.AgentId IN (SELECT AgentId FROM x_agent_commission_level_date GROUP BY AgentId))
				AND (sub.StatDate >= ? OR ? = 0)
				AND (sub.ParentAgentId = ? OR ? = 0)
				AND (sub.SellerId = ? OR ? = 0)
				AND (sub.ChannelId = ? OR ? = 0)
				AND (u2.Account LIKE ? OR ? = '')
				AND (sub.AgentId = ? OR ? = 0)
			) as VaildUsers1,
			IFNULL(SUM(CASE WHEN AgentLevel=1 THEN RechargeAmount ELSE 0 END),0) AS RechargeAmount1,
			IFNULL(SUM(CASE WHEN AgentLevel=1 THEN FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount1,
			IFNULL(SUM(CASE WHEN AgentLevel=1 THEN FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers1,
			-- 一级代理有效投注
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount1,
			
			-- 一级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount1,

			-- IFNULL(SUM(CASE WHEN AgentLevel=2 THEN VaildUsers ELSE 0 END),0) AS VaildUsers2,
			(SELECT COUNT(DISTINCT sub.UserId)
				FROM x_agent_commission_user_date sub
				LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
				WHERE sub.AgentLevel = 2
				AND (sub.AgentId IN (SELECT AgentId FROM x_agent_commission_level_date GROUP BY AgentId))
				AND (sub.StatDate >= ? OR ? = 0)
				AND (sub.ParentAgentId = ? OR ? = 0)
				AND (sub.SellerId = ? OR ? = 0)
				AND (sub.ChannelId = ? OR ? = 0)
				AND (u2.Account LIKE ? OR ? = '')
				AND (sub.AgentId = ? OR ? = 0)
			) as VaildUsers2,
			IFNULL(SUM(CASE WHEN AgentLevel=2 THEN RechargeAmount ELSE 0 END),0) AS RechargeAmount2,
			IFNULL(SUM(CASE WHEN AgentLevel=2 THEN FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount2,
			IFNULL(SUM(CASE WHEN AgentLevel=2 THEN FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers2,
			-- 二级代理有效投注
			IFNULL(SUM(CASE
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount2,
			
			-- 二级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount2,

			-- IFNULL(SUM(CASE WHEN AgentLevel=3 THEN VaildUsers ELSE 0 END),0) AS VaildUsers3,
			(SELECT COUNT(DISTINCT sub.UserId)
				FROM x_agent_commission_user_date sub
				LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
				WHERE sub.AgentLevel = 3
				AND (sub.AgentId IN (SELECT AgentId FROM x_agent_commission_level_date GROUP BY AgentId))
				AND (sub.StatDate >= ? OR ? = 0)
				AND (sub.ParentAgentId = ? OR ? = 0)
				AND (sub.SellerId = ? OR ? = 0)
				AND (sub.ChannelId = ? OR ? = 0)
				AND (u2.Account LIKE ? OR ? = '')
				AND (sub.AgentId = ? OR ? = 0)
			) as VaildUsers3,
			IFNULL(SUM(CASE WHEN AgentLevel=3 THEN RechargeAmount ELSE 0 END),0) AS RechargeAmount3,
			IFNULL(SUM(CASE WHEN AgentLevel=3 THEN FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount3,
			IFNULL(SUM(CASE WHEN AgentLevel=3 THEN FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers3,
			-- 三级代理有效投注
			IFNULL(SUM(CASE
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount3,
			
			-- 三级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount3,

			IFNULL(SUM(RechargeAmount),0) AS RechargeAmount,
			IFNULL(SUM(BetTranferUsdtHaXi+BetTranferUsdtHaXiRoulette+BetTranferTrxHaXi+BetTranferTrxHaXiRoulette+BetHaXi+BetHaXiRoulette+BetLottery+BetLowLottery+BetQiPai+BetDianZhi+BetXiaoYouXi+BetLive+BetSport+BetTexas),0) AS BetAmount,
			IFNULL(SUM(LiuShuiTranferUsdtHaXi+LiuShuiTranferUsdtHaXiRoulette+LiuShuiTranferTrxHaXi+LiuShuiTranferTrxHaXiRoulette+LiuShuiHaXi+LiuShuiHaXiRoulette+LiuShuiLottery+LiuShuiLowLottery+LiuShuiQiPai+LiuShuiDianZhi+LiuShuiXiaoYouXi+LiuShuiLive+LiuShuiSport+LiuShuiTexas),0) AS LiuShuiAmount,
			IFNULL(SUM(WinTranferUsdtHaXi+WinTranferUsdtHaXiRoulette+WinTranferTrxHaXi+WinTranferTrxHaXiRoulette+WinHaXi+WinHaXiRoulette+WinLottery+WinLowLottery+WinQiPai+WinDianZhi+WinXiaoYouXi+WinLive+WinSport+WinTexas),0) AS WinAmount,
			IFNULL(SUM(ActivityRewardAmount),0) AS ActivityRewardAmount,
			IFNULL(SUM(VipRewardAmount),0) AS VipRewardAmount,
			IFNULL(SUM(GetCommissionAmount),0) AS GetCommissionAmount,
			IFNULL(SUM(BetTranferUsdtHaXi+BetTranferUsdtHaXiRoulette+BetTranferTrxHaXi+BetTranferTrxHaXiRoulette+BetHaXi+BetHaXiRoulette+BetLottery+BetLowLottery+BetQiPai+BetDianZhi+BetXiaoYouXi+BetLive+BetSport+BetTexas) - SUM(WinTranferUsdtHaXi+WinTranferUsdtHaXiRoulette+WinTranferTrxHaXi+WinTranferTrxHaXiRoulette+WinHaXi+WinHaXiRoulette+WinLottery+WinLowLottery+WinQiPai+WinDianZhi+WinXiaoYouXi+WinLive+WinSport+WinTexas),0) AS TotalWinLossAmount
		FROM x_agent_commission_level_date as d
		%s`, whereClause)

	// 构建汇总查询参数
	var totalQueryParams []interface{}

	// 添加BetTotalUsers子查询的参数（10个参数）
	startTimeParam := abugo.TimeStampToLocalTime(reqdata.StartTime)
	totalQueryParams = append(totalQueryParams, startTimeParam)
	totalQueryParams = append(totalQueryParams, reqdata.StartTime)
	totalQueryParams = append(totalQueryParams, 0, 0) // ParentAgentId
	totalQueryParams = append(totalQueryParams, reqdata.SellerId, reqdata.SellerId)
	totalQueryParams = append(totalQueryParams, reqdata.ChannelId, reqdata.ChannelId)
	totalQueryParams = append(totalQueryParams, "", "")                           // AccountName
	totalQueryParams = append(totalQueryParams, reqdata.AgentId, reqdata.AgentId) // AgentId

	// 添加VaildUsers1子查询的参数（10个参数）
	totalQueryParams = append(totalQueryParams, startTimeParam)
	totalQueryParams = append(totalQueryParams, reqdata.StartTime)
	totalQueryParams = append(totalQueryParams, 0, 0) // ParentAgentId
	totalQueryParams = append(totalQueryParams, reqdata.SellerId, reqdata.SellerId)
	totalQueryParams = append(totalQueryParams, reqdata.ChannelId, reqdata.ChannelId)
	totalQueryParams = append(totalQueryParams, "", "")                           // AccountName
	totalQueryParams = append(totalQueryParams, reqdata.AgentId, reqdata.AgentId) // AgentId

	// 添加VaildUsers2子查询的参数（10个参数）
	totalQueryParams = append(totalQueryParams, startTimeParam)
	totalQueryParams = append(totalQueryParams, reqdata.StartTime)
	totalQueryParams = append(totalQueryParams, 0, 0) // ParentAgentId
	totalQueryParams = append(totalQueryParams, reqdata.SellerId, reqdata.SellerId)
	totalQueryParams = append(totalQueryParams, reqdata.ChannelId, reqdata.ChannelId)
	totalQueryParams = append(totalQueryParams, "", "")                           // AccountName
	totalQueryParams = append(totalQueryParams, reqdata.AgentId, reqdata.AgentId) // AgentId

	// 添加VaildUsers3子查询的参数（10个参数）
	totalQueryParams = append(totalQueryParams, startTimeParam)
	totalQueryParams = append(totalQueryParams, reqdata.StartTime)
	totalQueryParams = append(totalQueryParams, 0, 0) // ParentAgentId
	totalQueryParams = append(totalQueryParams, reqdata.SellerId, reqdata.SellerId)
	totalQueryParams = append(totalQueryParams, reqdata.ChannelId, reqdata.ChannelId)
	totalQueryParams = append(totalQueryParams, "", "")                           // AccountName
	totalQueryParams = append(totalQueryParams, reqdata.AgentId, reqdata.AgentId) // AgentId

	// 为每个CASE WHEN条件添加GameType和Symbol参数
	// 每个代理等级有2个部分（投注金额+流水），每个部分有8个WHEN条件，每个条件2个占位符
	// 总共：3个等级 × 2个部分 × 8个条件 × 2个占位符 = 96个占位符
	for i := 0; i < 72; i++ {
		totalQueryParams = append(totalQueryParams, reqdata.GameType, symbolValue)
	}
	// 添加主查询WHERE条件参数
	totalQueryParams = append(totalQueryParams, whereParams...)

	// // 打印汇总查询SQL日志用于调试
	// fmt.Printf("=== 汇总查询SQL ===\n")
	// fmt.Printf("SQL: %s\n", totalSQL)
	// fmt.Printf("参数数量: %d\n", len(totalQueryParams))
	// fmt.Printf("参数: %+v\n", totalQueryParams)
	// fmt.Printf("==================\n")

	// 执行汇总查询
	totalRows, err := server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer totalRows.Close()

	var totalData ListData
	if totalRows.Next() {
		err := totalRows.Scan(
			&totalData.TeamUserCount,
			&totalData.Level1UserCount,
			&totalData.Level1RechargeAmount,
			&totalData.Level1FirstRechargeAmount,
			&totalData.Level1FirstRechargeUserCount,
			&totalData.Level1BetAmount,
			&totalData.Level1ValidLiuShui,
			&totalData.Level2UserCount,
			&totalData.Level2RechargeAmount,
			&totalData.Level2FirstRechargeAmount,
			&totalData.Level2FirstRechargeUserCount,
			&totalData.Level2BetAmount,
			&totalData.Level2ValidLiuShui,
			&totalData.Level3UserCount,
			&totalData.Level3RechargeAmount,
			&totalData.Level3FirstRechargeAmount,
			&totalData.Level3FirstRechargeUserCount,
			&totalData.Level3BetAmount,
			&totalData.Level3ValidLiuShui,
			&totalData.TotalRechargeAmount,
			&totalData.TotalBetAmount,
			&totalData.TotalValidLiuShui,
			&totalData.TotalWinAmount,
			&totalData.TotalCaijinAmount,
			&totalData.TotalFanshuiAmount,
			&totalData.TotalCommissionAmount,
			&totalData.TotalWinLossAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 检查是否需要导出
	if reqdata.Export == 1 {
		// 导出时需要获取所有数据，重新构建不带分页的SQL
		exportSQL := fmt.Sprintf(`
			SELECT
			d.AgentId,
			IFNULL(u.Account, '') AS AccountName,
			-- IFNULL(SUM(d.BetTotalUsers),0) AS BetTotalUsers,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId) as BetTotalUsers,
			-- IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.VaildUsers ELSE 0 END),0) AS VaildUsers1,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId and AgentLevel = 1) as VaildUsers1,
			IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.RechargeAmount ELSE 0 END),0) AS RechargeAmount1,
			IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount1,
			IFNULL(SUM(CASE WHEN d.AgentLevel=1 THEN d.FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers1,
			d.TeamLevel AS YesterDayLevel,
			-- 一级代理有效投注
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount1,
			
			-- 一级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=1 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=1 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount1,

			-- IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.VaildUsers ELSE 0 END),0) AS VaildUsers2,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId and AgentLevel = 2) as VaildUsers2,
			IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.RechargeAmount ELSE 0 END),0) AS RechargeAmount2,
			IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount2,
			IFNULL(SUM(CASE WHEN d.AgentLevel=2 THEN d.FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers2,
			-- 二级代理有效投注
			IFNULL(SUM(CASE
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount2,
			
			-- 二级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=2 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=2 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount2,

			-- IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.VaildUsers ELSE 0 END),0) AS VaildUsers3,
			(SELECT COUNT(DISTINCT UserId) FROM x_agent_commission_user_date WHERE AgentId=d.AgentId and AgentLevel = 3) as VaildUsers3,
			IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.RechargeAmount ELSE 0 END),0) AS RechargeAmount3,
			IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.FirstRechargeAmount ELSE 0 END),0) AS FirstRechargeAmount3,
			IFNULL(SUM(CASE WHEN d.AgentLevel=3 THEN d.FirstRechargeUsers ELSE 0 END),0) AS FirstRechargeUsers3,
			-- 三级代理有效投注
			IFNULL(SUM(CASE
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 0 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 1 THEN d.BetHaXi+d.BetHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 0 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 1 THEN d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 0 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+(d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette)*d.TrxRate+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 1 THEN d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 2 THEN d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette
						ELSE 0 END),0) AS BetAmount3,
			
			-- 三级代理有效流水
			IFNULL(SUM(CASE 
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 1 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi*d.TrxRate+d.LiuShuiTranferTrxHaXiRoulette*d.TrxRate
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 0 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 1 THEN d.LiuShuiHaXi+d.LiuShuiHaXiRoulette
						WHEN d.AgentLevel=3 AND ? = 2 AND ? = 2 THEN 0
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 0 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 1 THEN d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 3 AND ? = 2 THEN 0	
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 0 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+(d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette)*d.TrxRate+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas 
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 1 THEN d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas
						WHEN d.AgentLevel=3 AND ? = 0 AND ? = 2 THEN d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette
						ELSE 0 END),0) AS LiuShuiAmount3,

			IFNULL(SUM(d.RechargeAmount),0) AS RechargeAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS BetAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi+d.LiuShuiTranferTrxHaXiRoulette+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS LiuShuiAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi+d.WinTranferTrxHaXiRoulette+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS WinAmount,
			IFNULL(SUM(d.ActivityRewardAmount),0) AS ActivityRewardAmount,
			IFNULL(SUM(d.VipRewardAmount),0) AS VipRewardAmount,
			IFNULL(SUM(d.GetCommissionAmount),0) AS GetCommissionAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi+d.BetTranferTrxHaXiRoulette+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas) - SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi+d.WinTranferTrxHaXiRoulette+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinLossAmount
		FROM x_agent_commission_level_date d
		LEFT JOIN x_user u ON d.AgentId = u.UserId
		%s
		GROUP BY d.AgentId
		ORDER BY DATE(d.StatDate) DESC`, whereClause)

		// 构建参数数组 - 需要重复多次GameType和Symbol参数
		var exportQueryParams []interface{}
		// 为每个CASE WHEN条件添加GameType和Symbol参数
		// 每个代理等级有2个部分（投注金额+流水），每个部分有8个WHEN条件，每个条件2个占位符
		// 总共：3个等级 × 2个部分 × 8个条件 × 2个占位符 = 96个占位符
		for i := 0; i < 72; i++ {
			exportQueryParams = append(exportQueryParams, reqdata.GameType, symbolValue)
		}

		// 添加WHERE条件参数
		exportQueryParams = append(exportQueryParams, whereParams...)

		// 打印导出查询SQL日志用于调试
		// fmt.Printf("=== 导出查询SQL ===\n")
		// fmt.Printf("SQL: %s\n", exportSQL)
		// fmt.Printf("参数数量: %d\n", len(exportQueryParams))
		// fmt.Printf("参数: %+v\n", exportQueryParams)
		// fmt.Printf("==================\n")

		// 执行导出查询
		exportRows, err := server.DbReport().GormDao().Raw(exportSQL, exportQueryParams...).Rows()
		if ctx.RespErr(err, &errcode) {
			return
		}
		defer exportRows.Close()

		// 扫描导出数据
		var exportResults []ListData
		for exportRows.Next() {
			var item ListData
			var totalRechargeAmount, totalBetAmount, totalValidLiuShui, totalWinAmount, activityRewardAmount, vipRewardAmount, getCommissionAmount float64
			err := exportRows.Scan(
				&item.AgentId,
				&item.AccountName,
				&item.TeamUserCount,
				&item.Level1UserCount,
				&item.Level1RechargeAmount,
				&item.Level1FirstRechargeAmount,
				&item.Level1FirstRechargeUserCount,
				&item.YesterDayLevel,
				&item.Level1BetAmount,
				&item.Level1ValidLiuShui,
				&item.Level2UserCount,
				&item.Level2RechargeAmount,
				&item.Level2FirstRechargeAmount,
				&item.Level2FirstRechargeUserCount,
				&item.Level2BetAmount,
				&item.Level2ValidLiuShui,
				&item.Level3UserCount,
				&item.Level3RechargeAmount,
				&item.Level3FirstRechargeAmount,
				&item.Level3FirstRechargeUserCount,
				&item.Level3BetAmount,
				&item.Level3ValidLiuShui,
				&totalRechargeAmount,
				&totalBetAmount,
				&totalValidLiuShui,
				&totalWinAmount,
				&activityRewardAmount,
				&vipRewardAmount,
				&getCommissionAmount,
				&item.TotalWinLossAmount,
			)
			if ctx.RespErr(err, &errcode) {
				return
			}

			// 使用从数据库查询到的汇总字段
			item.TotalRechargeAmount = totalRechargeAmount
			item.TotalBetAmount = totalBetAmount
			item.TotalValidLiuShui = totalValidLiuShui
			item.TotalWinAmount = totalWinAmount
			item.TotalCaijinAmount = activityRewardAmount
			item.TotalFanshuiAmount = vipRewardAmount
			item.TotalCommissionAmount = getCommissionAmount

			exportResults = append(exportResults, item)
		}

		// 生成文件名
		timestamp := time.Now().Format("20060102_150405")
		fileName := fmt.Sprintf("三级代理数据报表_%s.xlsx", timestamp)
		// filePath := fmt.Sprintf("./exports/%s", fileName)

		// 确保导出目录存在
		err = os.MkdirAll("./exports", 0755)
		if ctx.RespErr(err, &errcode) {
			return
		}

		// 创建Excel文件
		excel := excelize.NewFile()

		sheetName := "Sheet1"
		excel.SetSheetName("Sheet1", sheetName)

		// 写入CSV头部
		headers := []string{
			"代理ID", "账户名", "团队投注人数", "昨日团队等级", "一级代理人数", "一级代理充值金额", "一级代理首充金额", "一级代理首充人数",
			"一级代理投注金额", "一级代理有效流水", "二级代理人数", "二级代理充值金额", "二级代理首充金额",
			"二级代理首充人数", "二级代理投注金额", "二级代理有效流水", "三级代理人数", "三级代理充值金额",
			"三级代理首充金额", "三级代理首充人数", "三级代理投注金额", "三级代理有效流水", "总充值金额",
			"总投注金额", "总有效流水", "总派彩金额", "总彩金金额", "总返水金额", "总佣金金额", "总盈亏金额",
		}

		// 写入头部行
		if err := excel.SetSheetRow(sheetName, "A1", &headers); err != nil {
			log.Println("❌ 设置表头失败:", err)
			return
		}

		// 写入数据行
		for i, item := range exportResults {
			cell := fmt.Sprintf("A%d", i+2)
			row := []interface{}{
				item.AgentId,
				item.AccountName,
				item.TeamUserCount,
				item.YesterDayLevel,
				item.Level1UserCount,
				item.Level1RechargeAmount,
				item.Level1FirstRechargeAmount,
				item.Level1FirstRechargeUserCount,
				item.Level1BetAmount,
				item.Level1ValidLiuShui,
				item.Level2UserCount,
				item.Level2RechargeAmount,
				item.Level2FirstRechargeAmount,
				item.Level2FirstRechargeUserCount,
				item.Level2BetAmount,
				item.Level2ValidLiuShui,
				item.Level3UserCount,
				item.Level3RechargeAmount,
				item.Level3FirstRechargeAmount,
				item.Level3FirstRechargeUserCount,
				item.Level3BetAmount,
				item.Level3ValidLiuShui,
				item.TotalRechargeAmount,
				item.TotalBetAmount,
				item.TotalValidLiuShui,
				item.TotalWinAmount,
				item.TotalCaijinAmount,
				item.TotalFanshuiAmount,
				item.TotalCommissionAmount,
				item.TotalWinLossAmount,
			}
			if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
				// log.Println("❌ 写入数据行失败:", err)
				ctx.RespErrString(true, &errcode, "写入数据行失败")
				return
			}
		}

		// 写入汇总行
		if len(exportResults) > 0 {

			cell := fmt.Sprintf("A%d", len(exportResults)+2)
			row := []interface{}{
				"汇总",
				"汇总",
				totalData.TeamUserCount,
				0,
				totalData.Level1UserCount,
				totalData.Level1RechargeAmount,
				totalData.Level1FirstRechargeAmount,
				totalData.Level1FirstRechargeUserCount,
				totalData.Level1BetAmount,
				totalData.Level1ValidLiuShui,
				totalData.Level2UserCount,
				totalData.Level2RechargeAmount,
				totalData.Level2FirstRechargeAmount,
				totalData.Level2FirstRechargeUserCount,
				totalData.Level2BetAmount,
				totalData.Level2ValidLiuShui,
				totalData.Level3UserCount,
				totalData.Level3RechargeAmount,
				totalData.Level3FirstRechargeAmount,
				totalData.Level3FirstRechargeUserCount,
				totalData.Level3BetAmount,
				totalData.Level3ValidLiuShui,
				totalData.TotalRechargeAmount,
				totalData.TotalBetAmount,
				totalData.TotalValidLiuShui,
				totalData.TotalWinAmount,
				totalData.TotalCaijinAmount,
				totalData.TotalFanshuiAmount,
				totalData.TotalCommissionAmount,
				totalData.TotalWinLossAmount,
			}
			err := excel.SetSheetRow(sheetName, cell, &row)
			if ctx.RespErr(err, &errcode) {
				return
			}
		}

		// 保存Excel文件
		if err := excel.SaveAs(server.ExportDir() + "/" + fileName); err != nil {
			ctx.RespErrString(true, &errcode, "保存Excel失败")
			return
		}

		// 返回导出结果
		exportResponse := map[string]interface{}{
			"fileName": "/exports/" + fileName,
			"message":  "导出成功",
			"count":    len(exportResults),
		}
		ctx.RespOK(exportResponse)
		server.WriteAdminLog("导出三级代理数据报表", ctx, reqdata)
		return
	}

	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)
	server.WriteAdminLog("查询三级代理数据报表", ctx, reqdata)
}

func (c *ThreeLevelAgentController) teamSelf(ctx *abugo.AbuHttpContent) {
	errcode := 0
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理数据报表", "查"), &errcode, "权限不足") {
		return
	}

	type ListData struct {
		UserId                int     `json:"UserId" gorm:"column:UserId"`                               // 玩家ID
		AccountName           string  `json:"AccountName" gorm:"column:AccountName"`                     // 账户名
		AgentLevel            int     `json:"AgentLevel" gorm:"column:AgentLevel"`                       // 代理等级
		TeamLevel             int     `json:"TeamLevel" gorm:"column:TeamLevel"`                         // 团队等级
		IsAgent               int     `json:"IsAgent" gorm:"column:IsAgent"`                             // 是否是代理
		TotalRechargeAmount   float64 `json:"TotalRechargeAmount" gorm:"column:TotalRechargeAmount"`     // 充值金额
		TotalWithdrawAmount   float64 `json:"TotalWithdrawAmount" gorm:"column:TotalWithdrawAmount"`     // 提现金额
		TotalBetAmount        float64 `json:"TotalBetAmount" gorm:"column:TotalBetAmount"`               // 总投注额
		TotalWinAmount        float64 `json:"TotalWinAmount" gorm:"column:TotalWinAmount"`               // 总派奖额
		TotalLiushui          float64 `json:"TotalLiushui" gorm:"column:TotalLiushui"`                   // 总流水
		TotalCaijin           float64 `json:"TotalCaijin" gorm:"column:TotalCaijin"`                     // 总彩金
		TotalVipRewardAmount  float64 `json:"TotalVipRewardAmount" gorm:"column:TotalVipRewardAmount"`   // VIP活动彩金金额
		TotalCommissionAmount float64 `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"` // 结算佣金USDT
	}

	type ResponseData struct {
		Data []ListData `json:"Data"`
	}

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	agentIdStr := ctx.Gin().Param("agentId")
	if agentIdStr == "" {
		ctx.RespErrString(true, &errcode, "代理ID不能为空")
		return
	}

	// 将字符串转换为整数
	agentId, err := strconv.Atoi(agentIdStr)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理ID格式错误")
		return
	}

	whereConditions = append(whereConditions, "d.UserId = ?")
	whereParams = append(whereParams, agentId)

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 构建查询语句
	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId`, whereClause)

	// 构建参数数组 - 需要重复多次GameType和Symbol参数
	var queryParams []interface{}
	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		err := rows.Scan(
			&data.UserId,
			&data.AccountName,
			&data.AgentLevel,
			&data.TeamLevel,
			&data.IsAgent,
			&data.TotalRechargeAmount,
			&data.TotalWithdrawAmount,
			&data.TotalBetAmount,
			&data.TotalWinAmount,
			&data.TotalLiushui,
			&data.TotalCaijin,
			&data.TotalVipRewardAmount,
			&data.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	ctx.Put("data", results)
	ctx.RespOK()
}

func (c *ThreeLevelAgentController) teamList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int   `json:"Page"`
		PageSize int   `json:"PageSize"`
		Date     int64 `json:"Date"`
		UserId   int   `json:"UserId"`
		IsVaild  int   `json:"IsVaild"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理数据报表", "查"), &errcode, "权限不足") {
		return
	}

	// 设置默认分页参数
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	type ListData struct {
		UserId                int     `json:"UserId" gorm:"column:UserId"`           // 玩家ID
		AccountName           string  `json:"AccountName" gorm:"column:AccountName"` // 账户名
		AgentLevel            int     `json:"AgentLevel" gorm:"column:AgentLevel"`   // 代理等级
		TeamLevel             int     `json:"TeamLevel" gorm:"column:TeamLevel"`     // 团队等级
		IsAgent               int     `json:"IsAgent" gorm:"column:IsAgent"`         // 是否是代理
		IsVaild               int     `json:"IsVaild" gorm:"column:IsVaild"`
		TotalRechargeAmount   float64 `json:"TotalRechargeAmount" gorm:"column:TotalRechargeAmount"`     // 充值金额
		TotalWithdrawAmount   float64 `json:"TotalWithdrawAmount" gorm:"column:TotalWithdrawAmount"`     // 提现金额
		TotalBetAmount        float64 `json:"TotalBetAmount" gorm:"column:TotalBetAmount"`               // 总投注额
		TotalWinAmount        float64 `json:"TotalWinAmount" gorm:"column:TotalWinAmount"`               // 总派奖额
		TotalLiushui          float64 `json:"TotalLiushui" gorm:"column:TotalLiushui"`                   // 总流水
		TotalCaijin           float64 `json:"TotalCaijin" gorm:"column:TotalCaijin"`                     // 总彩金
		TotalVipRewardAmount  float64 `json:"TotalVipRewardAmount" gorm:"column:TotalVipRewardAmount"`   // VIP活动彩金金额
		TotalCommissionAmount float64 `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"` // 结算佣金USDT
	}

	type ResponseData struct {
		Total     int64      `json:"Total"`
		Data      []ListData `json:"Data"`
		TotalData ListData   `json:"TotalData"`
	}

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	agentIdStr := ctx.Gin().Param("agentId")
	if agentIdStr == "" {
		ctx.RespErrString(true, &errcode, "代理ID不能为空")
		return
	}

	// 将字符串转换为整数
	agentId, err := strconv.Atoi(agentIdStr)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理ID格式错误")
		return
	}

	whereClause := ""
	if agentId > 0 {
		whereConditions = append(whereConditions, "d.AgentId = ?")
		whereParams = append(whereParams, agentId)
	}
	if reqdata.UserId > 0 {
		whereConditions = append(whereConditions, "d.UserId = ?")
		whereParams = append(whereParams, reqdata.UserId)
	}

	if reqdata.Date > 0 {
		whereConditions = append(whereConditions, "d.StatDate = ?")
		whereParams = append(whereParams, abugo.TimeStampToLocalTime(reqdata.Date))
	}

	if reqdata.IsVaild > 0 {
		whereConditions = append(whereConditions, "d.IsVaild = ?")
		whereParams = append(whereParams, reqdata.IsVaild)
	}

	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			d.IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId
		ORDER BY d.UserId
		LIMIT ? OFFSET ?`, whereClause)

	// 构建参数数组 - 需要重复多次GameType和Symbol参数
	var queryParams []interface{}
	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)
	// 添加分页参数
	queryParams = append(queryParams, limit, offset)

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		err := rows.Scan(
			&data.UserId,
			&data.AccountName,
			&data.AgentLevel,
			&data.TeamLevel,
			&data.IsAgent,
			&data.IsVaild,
			&data.TotalRechargeAmount,
			&data.TotalWithdrawAmount,
			&data.TotalBetAmount,
			&data.TotalWinAmount,
			&data.TotalLiushui,
			&data.TotalCaijin,
			&data.TotalVipRewardAmount,
			&data.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	// 查询总数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) FROM (SELECT COUNT(*) as total
		FROM x_agent_commission_user_date as d
		%s
		GROUP BY d.UserId) a`, whereClause)

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, whereParams...).Scan(&total).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据
	totalSQL := fmt.Sprintf(`
		SELECT
			0 AS UserId,
			'' AS AccountName,
			0 AS AgentLevel,
			0 AS TeamLevel,
			0 AS IsAgent,
			0 AS IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		%s`, whereClause)

	// 构建汇总查询参数
	var totalQueryParams []interface{}
	// 添加WHERE条件参数
	totalQueryParams = append(totalQueryParams, whereParams...)

	// 执行汇总查询
	totalRows, err := server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer totalRows.Close()

	var totalData ListData
	if totalRows.Next() {
		err := totalRows.Scan(
			&totalData.UserId,
			&totalData.AccountName,
			&totalData.AgentLevel,
			&totalData.TeamLevel,
			&totalData.IsAgent,
			&totalData.IsVaild,
			&totalData.TotalRechargeAmount,
			&totalData.TotalWithdrawAmount,
			&totalData.TotalBetAmount,
			&totalData.TotalWinAmount,
			&totalData.TotalLiushui,
			&totalData.TotalCaijin,
			&totalData.TotalVipRewardAmount,
			&totalData.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 构建响应数据
	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)
}

// 三级代理佣金数据报表
func (c *ThreeLevelAgentController) teamLevelList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int `json:"Page"`
		PageSize int `json:"PageSize"`
		UserId   int `json:"UserId"`
		Export   int `json:"Export"`
		IsVaild  int `json:"IsVaild"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理数据报表", "查"), &errcode, "权限不足") {
		return
	}

	// 设置默认分页参数
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	type ListData struct {
		UserId                int     `json:"UserId" gorm:"column:UserId"`                               // 玩家ID
		AccountName           string  `json:"AccountName" gorm:"column:AccountName"`                     // 账户名
		AgentLevel            int     `json:"AgentLevel" gorm:"column:AgentLevel"`                       // 代理等级
		TeamLevel             int     `json:"TeamLevel" gorm:"column:TeamLevel"`                         // 团队等级
		IsAgent               int     `json:"IsAgent" gorm:"column:IsAgent"`                             // 是否是代理
		IsVaild               int     `json:"IsVaild" gorm:"column:IsVaild"`                             // 是否有效
		TotalRechargeAmount   float64 `json:"TotalRechargeAmount" gorm:"column:TotalRechargeAmount"`     // 充值金额
		TotalWithdrawAmount   float64 `json:"TotalWithdrawAmount" gorm:"column:TotalWithdrawAmount"`     // 提现金额
		TotalBetAmount        float64 `json:"TotalBetAmount" gorm:"column:TotalBetAmount"`               // 总投注额
		TotalWinAmount        float64 `json:"TotalWinAmount" gorm:"column:TotalWinAmount"`               // 总派奖额
		TotalLiushui          float64 `json:"TotalLiushui" gorm:"column:TotalLiushui"`                   // 总流水
		TotalCaijin           float64 `json:"TotalCaijin" gorm:"column:TotalCaijin"`                     // 总彩金
		TotalVipRewardAmount  float64 `json:"TotalVipRewardAmount" gorm:"column:TotalVipRewardAmount"`   // VIP活动彩金金额
		TotalCommissionAmount float64 `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"` // 结算佣金USDT
	}

	type ResponseData struct {
		Total     int64      `json:"Total"`
		Data      []ListData `json:"Data"`
		TotalData ListData   `json:"TotalData"`
	}

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	agentIdStr := ctx.Gin().Param("agentId")
	agentLevel := ctx.Gin().Param("level")
	if agentIdStr == "" {
		ctx.RespErrString(true, &errcode, "代理ID不能为空")
		return
	}
	if agentLevel == "" {
		ctx.RespErrString(true, &errcode, "代理等级不能为空")
		return
	}

	// 将字符串转换为整数
	agentId, err := strconv.Atoi(agentIdStr)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理ID格式错误")
		return
	}

	agentLevelInt, err := strconv.Atoi(agentLevel)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理等级格式错误")
		return
	}

	whereClause := ""
	if agentId > 0 {
		whereConditions = append(whereConditions, "d.AgentId = ?")
		whereParams = append(whereParams, agentId)
	}

	if agentLevelInt > 0 {
		whereConditions = append(whereConditions, "d.AgentLevel = ?")
		whereParams = append(whereParams, agentLevelInt)
	}

	if reqdata.UserId > 0 {
		whereConditions = append(whereConditions, "d.UserId = ?")
		whereParams = append(whereParams, reqdata.UserId)
	}

	if reqdata.IsVaild > 0 {
		whereConditions = append(whereConditions, "d.IsVaild = ?")
		whereParams = append(whereParams, reqdata.IsVaild)
	}

	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}
	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			d.IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId
		ORDER BY d.UserId
		LIMIT ? OFFSET ?`, whereClause)

	// 构建参数数组 - 需要重复多次GameType和Symbol参数
	var queryParams []interface{}
	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)
	// 添加分页参数
	queryParams = append(queryParams, limit, offset)

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		err := rows.Scan(
			&data.UserId,
			&data.AccountName,
			&data.AgentLevel,
			&data.TeamLevel,
			&data.IsAgent,
			&data.IsVaild,
			&data.TotalRechargeAmount,
			&data.TotalWithdrawAmount,
			&data.TotalBetAmount,
			&data.TotalWinAmount,
			&data.TotalLiushui,
			&data.TotalCaijin,
			&data.TotalVipRewardAmount,
			&data.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	// 查询总数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) FROM (
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date as d
			%s
			GROUP BY d.UserId) a`, whereClause)

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, whereParams...).Scan(&total).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据
	totalSQL := fmt.Sprintf(`
		SELECT
			0 AS UserId,
			'' AS AccountName,
			0 AS AgentLevel,
			0 AS TeamLevel,
			0 AS IsAgent,
			0 AS IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		%s`, whereClause)

	// 构建汇总查询参数
	var totalQueryParams []interface{}
	// 添加WHERE条件参数
	totalQueryParams = append(totalQueryParams, whereParams...)

	// 执行汇总查询
	totalRows, err := server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer totalRows.Close()

	var totalData ListData
	if totalRows.Next() {
		err := totalRows.Scan(
			&totalData.UserId,
			&totalData.AccountName,
			&totalData.AgentLevel,
			&totalData.TeamLevel,
			&totalData.IsAgent,
			&totalData.IsVaild,
			&totalData.TotalRechargeAmount,
			&totalData.TotalWithdrawAmount,
			&totalData.TotalBetAmount,
			&totalData.TotalWinAmount,
			&totalData.TotalLiushui,
			&totalData.TotalCaijin,
			&totalData.TotalVipRewardAmount,
			&totalData.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 导出
	if reqdata.Export == 1 {
		excelSql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			d.IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId
		ORDER BY d.UserId`, whereClause)

		excelRows, err := server.DbReport().GormDao().Raw(excelSql, whereParams...).Rows()
		if ctx.RespErr(err, &errcode) {
			return
		}
		defer excelRows.Close()

		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{
			"玩家ID",
			"账户名",
			"代理等级",
			"团队等级",
			"是否代理",
			"是否有效",
			"充值金额",
			"提现金额",
			"投注金额",
			"派奖金额",
			"流水金额",
			"彩金金额",
			"VIP彩金金额",
			"佣金金额",
		})

		i := 2
		for excelRows.Next() {
			var data ListData
			err := excelRows.Scan(
				&data.UserId,
				&data.AccountName,
				&data.AgentLevel,
				&data.TeamLevel,
				&data.IsAgent,
				&data.IsVaild,
				&data.TotalRechargeAmount,
				&data.TotalWithdrawAmount,
				&data.TotalBetAmount,
				&data.TotalWinAmount,
				&data.TotalLiushui,
				&data.TotalCaijin,
				&data.TotalVipRewardAmount,
				&data.TotalCommissionAmount,
			)
			if ctx.RespErr(err, &errcode) {
				return
			}

			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i), &[]interface{}{
				data.UserId,
				data.AccountName,
				data.AgentLevel,
				data.TeamLevel,
				data.IsAgent,
				data.IsVaild,
				data.TotalRechargeAmount,
				data.TotalWithdrawAmount,
				data.TotalBetAmount,
				data.TotalWinAmount,
				data.TotalLiushui,
				data.TotalCaijin,
				data.TotalVipRewardAmount,
				data.TotalCommissionAmount,
			})
			i++
		}

		totalRowIndex := i // 数据行之后
		totalRowData := []interface{}{
			"总计", "", "", "", "", "", totalData.TotalRechargeAmount, totalData.TotalWithdrawAmount, totalData.TotalBetAmount, totalData.TotalWinAmount, totalData.TotalLiushui, totalData.TotalCaijin, totalData.TotalVipRewardAmount, totalData.TotalCommissionAmount,
		}
		totalCell := fmt.Sprintf("A%d", totalRowIndex)
		err = excel.SetSheetRow("Sheet1", totalCell, &totalRowData)
		if ctx.RespErr(err, &errcode) {
			return
		}

		filename := "三级代理团队等级数据_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		return
	}

	// 构建响应数据
	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)
}

func (c *ThreeLevelAgentController) teamStrictList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int `json:"Page"`
		PageSize int `json:"PageSize"`
		UserId   int `json:"UserId"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理数据报表", "查"), &errcode, "权限不足") {
		return
	}

	// 设置默认分页参数
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	type ListData struct {
		UserId                int     `json:"UserId" gorm:"column:UserId"`           // 玩家ID
		AccountName           string  `json:"AccountName" gorm:"column:AccountName"` // 账户名
		AgentLevel            int     `json:"AgentLevel" gorm:"column:AgentLevel"`   // 代理等级
		TeamLevel             int     `json:"TeamLevel" gorm:"column:TeamLevel"`     // 团队等级
		IsAgent               int     `json:"IsAgent" gorm:"column:IsAgent"`
		IsVaild               int     `json:"IsVaild" gorm:"column:IsVaild"`                             // 是否是代理
		TotalRechargeAmount   float64 `json:"TotalRechargeAmount" gorm:"column:TotalRechargeAmount"`     // 充值金额
		TotalWithdrawAmount   float64 `json:"TotalWithdrawAmount" gorm:"column:TotalWithdrawAmount"`     // 提现金额
		TotalBetAmount        float64 `json:"TotalBetAmount" gorm:"column:TotalBetAmount"`               // 总投注额
		TotalWinAmount        float64 `json:"TotalWinAmount" gorm:"column:TotalWinAmount"`               // 总派奖额
		TotalLiushui          float64 `json:"TotalLiushui" gorm:"column:TotalLiushui"`                   // 总流水
		TotalCaijin           float64 `json:"TotalCaijin" gorm:"column:TotalCaijin"`                     // 总彩金
		TotalVipRewardAmount  float64 `json:"TotalVipRewardAmount" gorm:"column:TotalVipRewardAmount"`   // VIP活动彩金金额
		TotalCommissionAmount float64 `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"` // 结算佣金USDT
	}

	type ResponseData struct {
		Total     int64      `json:"Total"`
		Data      []ListData `json:"Data"`
		TotalData ListData   `json:"TotalData"`
	}

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	agentIdStr := ctx.Gin().Param("agentId")
	if agentIdStr == "" {
		ctx.RespErrString(true, &errcode, "代理ID不能为空")
		return
	}

	// 将字符串转换为整数
	agentId, err := strconv.Atoi(agentIdStr)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理ID格式错误")
		return
	}

	whereClause := ""
	if agentId > 0 {
		whereConditions = append(whereConditions, "d.AgentId = ?")
		whereParams = append(whereParams, agentId)
	}

	// 构建WHERE子句
	if reqdata.UserId > 0 {
		whereConditions = append(whereConditions, "d.UserId = ?")
		whereParams = append(whereParams, reqdata.UserId)
	}

	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			d.IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s And a.ChildCount > 0 And d.AgentLevel = 1
		GROUP BY d.UserId
		ORDER BY d.UserId
		LIMIT ? OFFSET ?`, whereClause)

	// 构建参数数组 - 需要重复多次GameType和Symbol参数
	var queryParams []interface{}
	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)
	// 添加分页参数
	queryParams = append(queryParams, limit, offset)

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		err := rows.Scan(
			&data.UserId,
			&data.AccountName,
			&data.AgentLevel,
			&data.TeamLevel,
			&data.IsAgent,
			&data.IsVaild,
			&data.TotalRechargeAmount,
			&data.TotalWithdrawAmount,
			&data.TotalBetAmount,
			&data.TotalWinAmount,
			&data.TotalLiushui,
			&data.TotalCaijin,
			&data.TotalVipRewardAmount,
			&data.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	// 查询总数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) FROM (SELECT COUNT(*) as total
		FROM x_agent_commission_user_date as d
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s And a.ChildCount > 0 And d.AgentLevel = 1
		GROUP BY d.UserId) a`, whereClause)

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, whereParams...).Scan(&total).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据
	totalSQL := fmt.Sprintf(`
		SELECT
			0 AS UserId,
			'' AS AccountName,
			0 AS AgentLevel,
			0 AS TeamLevel,
			0 AS IsAgent,
			0 AS IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s And a.ChildCount > 0 And d.AgentLevel = 1`, whereClause)

	// 构建汇总查询参数
	var totalQueryParams []interface{}
	// 添加WHERE条件参数
	totalQueryParams = append(totalQueryParams, whereParams...)

	// 执行汇总查询
	totalRows, err := server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer totalRows.Close()

	var totalData ListData
	if totalRows.Next() {
		err := totalRows.Scan(
			&totalData.UserId,
			&totalData.AccountName,
			&totalData.AgentLevel,
			&totalData.TeamLevel,
			&totalData.IsAgent,
			&totalData.IsVaild,
			&totalData.TotalRechargeAmount,
			&totalData.TotalWithdrawAmount,
			&totalData.TotalBetAmount,
			&totalData.TotalWinAmount,
			&totalData.TotalLiushui,
			&totalData.TotalCaijin,
			&totalData.TotalVipRewardAmount,
			&totalData.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 构建响应数据
	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)
}

func (c *ThreeLevelAgentController) teamFirstRecharge(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int `json:"Page"`
		PageSize int `json:"PageSize"`
		UserId   int `json:"UserId"`
		Level    int `json:"Level"`
		IsVaild  int `json:"IsVaild"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理数据报表", "查"), &errcode, "权限不足") {
		return
	}

	// 设置默认分页参数
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	type ListData struct {
		UserId                int     `json:"UserId" gorm:"column:UserId"`                               // 玩家ID
		AccountName           string  `json:"AccountName" gorm:"column:AccountName"`                     // 账户名
		AgentLevel            int     `json:"AgentLevel" gorm:"column:AgentLevel"`                       // 代理等级
		TeamLevel             int     `json:"TeamLevel" gorm:"column:TeamLevel"`                         // 团队等级
		IsAgent               int     `json:"IsAgent" gorm:"column:IsAgent"`                             // 是否是代理
		IsVaild               int     `json:"IsVaild" gorm:"column:IsVaild"`                             // 是否有效
		TotalRechargeAmount   float64 `json:"TotalRechargeAmount" gorm:"column:TotalRechargeAmount"`     // 充值金额
		TotalWithdrawAmount   float64 `json:"TotalWithdrawAmount" gorm:"column:TotalWithdrawAmount"`     // 提现金额
		TotalBetAmount        float64 `json:"TotalBetAmount" gorm:"column:TotalBetAmount"`               // 总投注额
		TotalWinAmount        float64 `json:"TotalWinAmount" gorm:"column:TotalWinAmount"`               // 总派奖额
		TotalLiushui          float64 `json:"TotalLiushui" gorm:"column:TotalLiushui"`                   // 总流水
		TotalCaijin           float64 `json:"TotalCaijin" gorm:"column:TotalCaijin"`                     // 总彩金
		TotalVipRewardAmount  float64 `json:"TotalVipRewardAmount" gorm:"column:TotalVipRewardAmount"`   // VIP活动彩金金额
		TotalCommissionAmount float64 `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"` // 结算佣金USDT
	}

	type ResponseData struct {
		Total     int64      `json:"Total"`
		Data      []ListData `json:"Data"`
		TotalData ListData   `json:"TotalData"`
	}

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	agentIdStr := ctx.Gin().Param("agentId")
	if agentIdStr == "" {
		ctx.RespErrString(true, &errcode, "代理ID不能为空")
		return
	}

	// 将字符串转换为整数
	agentId, err := strconv.Atoi(agentIdStr)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理ID格式错误")
		return
	}

	whereClause := ""
	if agentId > 0 {
		whereConditions = append(whereConditions, "d.AgentId = ? and d.FirstRechargeAmount>0 and d.IsVaild = 1")
		whereParams = append(whereParams, agentId)
	}
	// 构建WHERE子句
	if reqdata.UserId > 0 {
		whereConditions = append(whereConditions, "d.UserId = ?")
		whereParams = append(whereParams, reqdata.UserId)
	}

	if reqdata.Level > 0 {
		whereConditions = append(whereConditions, "d.AgentLevel = ?")
		whereParams = append(whereParams, reqdata.Level)
	}

	if reqdata.IsVaild > 0 {
		whereConditions = append(whereConditions, "d.IsVaild = ?")
		whereParams = append(whereParams, reqdata.IsVaild)
	}

	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			d.IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId
		ORDER BY d.UserId
		LIMIT ? OFFSET ?`, whereClause)

	// 构建参数数组 - 需要重复多次GameType和Symbol参数
	var queryParams []interface{}
	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)
	// 添加分页参数
	queryParams = append(queryParams, limit, offset)

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		err := rows.Scan(
			&data.UserId,
			&data.AccountName,
			&data.AgentLevel,
			&data.TeamLevel,
			&data.IsAgent,
			&data.IsVaild,
			&data.TotalRechargeAmount,
			&data.TotalWithdrawAmount,
			&data.TotalBetAmount,
			&data.TotalWinAmount,
			&data.TotalLiushui,
			&data.TotalCaijin,
			&data.TotalVipRewardAmount,
			&data.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	// 查询总数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) FROM (SELECT COUNT(*) as total
		FROM x_agent_commission_user_date as d
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId) a`, whereClause)

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, whereParams...).Scan(&total).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据
	totalSQL := fmt.Sprintf(`
		SELECT
			0 AS UserId,
			'' AS AccountName,
			0 AS AgentLevel,
			0 AS TeamLevel,
			0 AS IsAgent,
			0 AS IsVaild,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s `, whereClause)

	// 构建汇总查询参数
	var totalQueryParams []interface{}
	// 添加WHERE条件参数
	totalQueryParams = append(totalQueryParams, whereParams...)

	// 执行汇总查询
	totalRows, err := server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer totalRows.Close()

	var totalData ListData
	if totalRows.Next() {
		err := totalRows.Scan(
			&totalData.UserId,
			&totalData.AccountName,
			&totalData.AgentLevel,
			&totalData.TeamLevel,
			&totalData.IsAgent,
			&totalData.IsVaild,
			&totalData.TotalRechargeAmount,
			&totalData.TotalWithdrawAmount,
			&totalData.TotalBetAmount,
			&totalData.TotalWinAmount,
			&totalData.TotalLiushui,
			&totalData.TotalCaijin,
			&totalData.TotalVipRewardAmount,
			&totalData.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 构建响应数据
	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)

}

func (c *ThreeLevelAgentController) commissionReportList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page          int
		PageSize      int
		Date          int64
		AgentId       int
		ParentAgentId int
		Symbol        string
		SellerId      int
		ChannelId     int
		AccountName   string
		Export        int // 0-分页查询，1-导出报表
	}

	type LevelData struct {
		RewardHaXi  float64 `json:"RewardHaXi" gorm:"column:RewardHaXi"` // 哈希返佣(百分比)
		LiuShuiHaXi float64 `json:"BetHaXi" gorm:"column:LiuShuiHaXi"`   // 哈希有效投注额

		RewardHaXiRoulette  float64 `json:"RewardHaXiRoulette" gorm:"column:RewardHaXiRoulette"` // 哈希轮盘返佣(百分比)
		LiuShuiHaXiRoulette float64 `json:"BetHaXiRoulette" gorm:"column:LiuShuiHaXiRoulette"`   // 哈希轮盘有效投注额

		RewardLottery  float64 `json:"RewardLottery" gorm:"column:RewardLottery"` // 彩票返佣(百分比)
		LiuShuiLottery float64 `json:"BetLottery" gorm:"column:LiuShuiLottery"`   // 彩票有效投注额

		RewardLowLottery  float64 `json:"RewardLowLottery" gorm:"column:RewardLowLottery"` // 低频彩返佣(百分比)
		LiuShuiLowLottery float64 `json:"BetLowLottery" gorm:"column:LiuShuiLowLottery"`   // 低频彩有效投注额

		RewardQiPai  float64 `json:"RewardQiPai" gorm:"column:RewardQiPai"` // 棋牌返佣(百分比)
		LiuShuiQiPai float64 `json:"BetQiPai" gorm:"column:LiuShuiQiPai"`   // 棋牌有效投注额

		RewardDianZhi  float64 `json:"RewardDianZhi" gorm:"column:RewardDianZhi"` // 电子返佣(百分比)
		LiuShuiDianZhi float64 `json:"BetDianZhi" gorm:"column:LiuShuiDianZhi"`   // 电子有效投注额

		RewardXiaoYouXi  float64 `json:"RewardXiaoYouXi" gorm:"column:RewardXiaoYouXi"` // 小游戏返佣(百分比)
		LiuShuiXiaoYouXi float64 `json:"BetXiaoYouXi" gorm:"column:LiuShuiXiaoYouXi"`   // 小游戏有效投注额

		RewardLive  float64 `json:"RewardLive" gorm:"column:RewardLive"` // 真人返佣(百分比)
		LiuShuiLive float64 `json:"BetLive" gorm:"column:LiuShuiLive"`   // 真人有效投注额

		RewardSport  float64 `json:"RewardSport" gorm:"column:RewardSport"` // 体育返佣(百分比)
		LiuShuiSport float64 `json:"BetSport" gorm:"column:LiuShuiSport"`   // 体育有效投注额

		RewardTexas  float64 `json:"RewardTexas" gorm:"column:RewardTexas"` // 德州返佣(百分比)
		LiuShuiTexas float64 `json:"BetTexas" gorm:"column:LiuShuiTexas"`   // 德州有效投注额
	}

	type ListData struct {
		AgentId                  int       `json:"AgentId" gorm:"column:AgentId"`
		AccountName              string    `json:"AccountName" gorm:"column:AccountName"`                           // 账户名
		SellerName               string    `json:"SellerName" gorm:"column:SellerName"`                             // 运营商名称
		ChannelName              string    `json:"ChannelName" gorm:"column:ChannelName"`                           // 渠道名称
		Date                     string    `json:"Date" gorm:"column:Date"`                                         // 日期
		ParentAgentId            int       `json:"ParentAgentId" gorm:"column:ParentAgentId"`                       // 上级代理ID
		TeamUserCount            int       `json:"TeamUserCount" gorm:"column:TeamUserCount"`                       // 团队人数
		TeamValidUserCount       int       `json:"TeamValidUserCount" gorm:"column:TeamValidUserCount"`             // 团队有效用户数
		TeamValidUserBetAmount   float64   `json:"TeamValidUserBetAmount" gorm:"column:TeamValidUserBetAmount"`     // 团队有效用户投注金额
		TeamLevel                float64   `json:"TeamLevel" gorm:"column:TeamLevel"`                               // 团队等级
		Level1Data               LevelData `json:"Level1Data" gorm:"column:Level1Data"`                             // 一级代理数据
		Level2Data               LevelData `json:"Level2Data" gorm:"column:Level2Data"`                             // 二级代理数据
		Level3Data               LevelData `json:"Level3Data" gorm:"column:Level3Data"`                             // 三级代理数据
		TotalCommissionAmount    float64   `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"`       // 历史总佣金
		TotalGetCommissionAmount float64   `json:"TotalGetCommissionAmount" gorm:"column:TotalGetCommissionAmount"` // 已领取佣金
		// LessTotalCommissionAmount float64   `json:"LessTotalCommissionAmount" gorm:"column:LessTotalCommissionAmount"` // 剩余佣金
	}

	type ResponseData struct {
		Total     int64
		Data      []ListData
		TotalData ListData
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理佣金数据", "查"), &errcode, "权限不足") {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	// 处理币种参数
	symbolValue := 0
	if reqdata.Symbol == "USDT" {
		symbolValue = 1
	} else if reqdata.Symbol == "TRX" {
		symbolValue = 2
	}

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	// 日期条件
	if reqdata.Date > 0 {
		whereConditions = append(whereConditions, "d.StatDate = ?")
		whereParams = append(whereParams, abugo.TimeStampToLocalTime(reqdata.Date))
	}

	// 代理ID条件
	if reqdata.AgentId > 0 {
		whereConditions = append(whereConditions, "d.AgentId = ?")
		whereParams = append(whereParams, reqdata.AgentId)
	}

	// 上级代理ID条件
	if reqdata.ParentAgentId > 0 {
		whereConditions = append(whereConditions, "d.ParentAgentId = ?")
		whereParams = append(whereParams, reqdata.ParentAgentId)
	}

	// 运营商ID条件
	if reqdata.SellerId > 0 {
		whereConditions = append(whereConditions, "d.SellerId = ?")
		whereParams = append(whereParams, reqdata.SellerId)
	}

	// 渠道ID条件
	if reqdata.ChannelId > 0 {
		whereConditions = append(whereConditions, "d.ChannelId = ?")
		whereParams = append(whereParams, reqdata.ChannelId)
	}

	// 账户名条件
	if len(reqdata.AccountName) > 0 {
		whereConditions = append(whereConditions, "u.Account LIKE ?")
		whereParams = append(whereParams, "%"+reqdata.AccountName+"%")
	}

	// 构建WHERE子句
	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 构建子查询的日期条件
	dateCondition := "1=1"
	if reqdata.Date > 0 {
		dateCondition = "sub.StatDate = ?"
	}

	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.AgentId,
			IFNULL(u.Account, '') AS AccountName,
			IFNULL(s.SellerName, '') AS SellerName,
			IFNULL(ch.ChannelName, '') AS ChannelName,
			DATE(d.StatDate) AS Date,
			d.ParentAgentId,
			-- IFNULL(SUM(d.BetTotalUsers), 0) AS TeamUserCount,
			IFNULL((SELECT COUNT(DISTINCT sub.UserId)
				FROM x_agent_commission_user_date sub
				LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
				WHERE sub.AgentId = d.AgentId
				AND (sub.StatDate = d.StatDate)
				AND (%s OR ? = 0)
				AND (sub.ParentAgentId = ? OR ? = 0)
				AND (sub.SellerId = ? OR ? = 0)
				AND (sub.ChannelId = ? OR ? = 0)
				AND (u2.Account LIKE ? OR ? = '')
			), 0) as TeamUserCount,
			IFNULL(SUM(d.VaildUsers), 0) AS TeamValidUserCount,
			IFNULL(SUM(
				CASE
					WHEN ? = 1 THEN d.BetHaXi + d.BetHaXiRoulette + d.BetTranferUsdtHaXi + d.BetTranferUsdtHaXiRoulette + d.BetLottery + d.BetLowLottery + d.BetQiPai + d.BetDianZhi + d.BetXiaoYouXi + d.BetLive + d.BetSport + d.BetTexas
					WHEN ? = 2 THEN (d.BetTranferTrxHaXi + d.BetTranferTrxHaXiRoulette) * d.TrxRate
					ELSE d.BetHaXi + d.BetHaXiRoulette + d.BetTranferUsdtHaXi + d.BetTranferUsdtHaXiRoulette + (d.BetTranferTrxHaXi + d.BetTranferTrxHaXiRoulette) * d.TrxRate + d.BetLottery + d.BetLowLottery + d.BetQiPai + d.BetDianZhi + d.BetXiaoYouXi + d.BetLive + d.BetSport + d.BetTexas
				END
			), 0) AS TeamValidUserBetAmount,
			d.TeamLevel,

			-- 一级代理数据（根据币种区分）
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardHaXi ELSE 0 END), 0) AS Level1_RewardHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
					ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level1_LiuShuiHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardHaXiRoulette ELSE 0 END), 0) AS Level1_RewardHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
					ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level1_LiuShuiHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardLottery ELSE 0 END), 0) AS Level1_RewardLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardLowLottery ELSE 0 END), 0) AS Level1_RewardLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardQiPai ELSE 0 END), 0) AS Level1_RewardQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardDianZhi ELSE 0 END), 0) AS Level1_RewardDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardXiaoYouXi ELSE 0 END), 0) AS Level1_RewardXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardLive ELSE 0 END), 0) AS Level1_RewardLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardSport ELSE 0 END), 0) AS Level1_RewardSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardTexas ELSE 0 END), 0) AS Level1_RewardTexas,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiTexas,

			-- 二级代理数据（根据币种区分）
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardHaXi ELSE 0 END), 0) AS Level2_RewardHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
					ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level2_LiuShuiHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardHaXiRoulette ELSE 0 END), 0) AS Level2_RewardHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
					ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level2_LiuShuiHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardLottery ELSE 0 END), 0) AS Level2_RewardLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardLowLottery ELSE 0 END), 0) AS Level2_RewardLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardQiPai ELSE 0 END), 0) AS Level2_RewardQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardDianZhi ELSE 0 END), 0) AS Level2_RewardDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardXiaoYouXi ELSE 0 END), 0) AS Level2_RewardXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardLive ELSE 0 END), 0) AS Level2_RewardLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardSport ELSE 0 END), 0) AS Level2_RewardSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardTexas ELSE 0 END), 0) AS Level2_RewardTexas,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiTexas,

			-- 三级代理数据（根据币种区分）
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardHaXi ELSE 0 END), 0) AS Level3_RewardHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
					ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level3_LiuShuiHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardHaXiRoulette ELSE 0 END), 0) AS Level3_RewardHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
					ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level3_LiuShuiHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardLottery ELSE 0 END), 0) AS Level3_RewardLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardLowLottery ELSE 0 END), 0) AS Level3_RewardLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardQiPai ELSE 0 END), 0) AS Level3_RewardQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardDianZhi ELSE 0 END), 0) AS Level3_RewardDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardXiaoYouXi ELSE 0 END), 0) AS Level3_RewardXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardLive ELSE 0 END), 0) AS Level3_RewardLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardSport ELSE 0 END), 0) AS Level3_RewardSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardTexas ELSE 0 END), 0) AS Level3_RewardTexas,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiTexas,

			-- 佣金相关（根据币种区分）
			IFNULL(SUM(CASE
				WHEN ? = 1 THEN d.CommissionAmount
				WHEN ? = 2 THEN d.CommissionTrxAmount
				ELSE d.CommissionAmount + d.CommissionTrxAmount * d.TrxRate
			END), 0) AS TotalCommissionAmount,

			IFNULL(CASE WHEN E.GetStatus=1 AND ? = 1 THEN E.CommissionAmount
						WHEN E.GetStatus=1 AND ? = 2 THEN E.CommissionTrxAmount
						WHEN E.GetStatus=1 AND ? = 0 THEN E.CommissionAmount+E.CommissionTrxAmount*E.TrxRate
						ELSE 0 END,0) AS TotalGetCommissionAmount

		FROM x_agent_commission_level_date d
		INNER JOIN x_agent_commission_data_date AS E 
			ON d.StatDate=E.StatDate
			AND d.AgentId=E.AgentId
		LEFT JOIN x_user u ON d.AgentId = u.UserId
		LEFT JOIN x_seller s ON d.SellerId = s.SellerId
		LEFT JOIN x_channel ch ON d.ChannelId = ch.ChannelId
		%s
		GROUP BY d.AgentId, d.StatDate
		ORDER BY DATE(d.StatDate) DESC
		LIMIT ? OFFSET ?`, dateCondition, whereClause)

	// 构建查询参数
	var queryParams []interface{}

	// 添加TeamUserCount子查询的参数（10个参数）
	// 日期条件参数
	if reqdata.Date > 0 {
		queryParams = append(queryParams, abugo.TimeStampToLocalTime(reqdata.Date))
	}
	queryParams = append(queryParams, reqdata.Date) // 用于判断是否有日期条件

	// ParentAgentId条件参数
	queryParams = append(queryParams, reqdata.ParentAgentId, reqdata.ParentAgentId)

	// SellerId条件参数
	queryParams = append(queryParams, reqdata.SellerId, reqdata.SellerId)

	// ChannelId条件参数
	queryParams = append(queryParams, reqdata.ChannelId, reqdata.ChannelId)

	// AccountName条件参数
	accountNameParam := ""
	if len(reqdata.AccountName) > 0 {
		accountNameParam = "%" + reqdata.AccountName + "%"
	}
	queryParams = append(queryParams, accountNameParam, reqdata.AccountName)

	// 添加币种参数（共67个占位符）
	// TeamValidUserBetAmount：2个
	queryParams = append(queryParams, symbolValue, symbolValue)
	// 一级代理哈希相关：4个
	queryParams = append(queryParams, symbolValue, symbolValue, symbolValue, symbolValue)
	// 一级代理其他游戏：16个（8种游戏 × 2个占位符）
	for i := 0; i < 16; i++ {
		queryParams = append(queryParams, symbolValue)
	}
	// 二级代理哈希相关：4个
	queryParams = append(queryParams, symbolValue, symbolValue, symbolValue, symbolValue)
	// 二级代理其他游戏：16个（8种游戏 × 2个占位符）
	for i := 0; i < 16; i++ {
		queryParams = append(queryParams, symbolValue)
	}
	// 三级代理哈希相关：4个
	queryParams = append(queryParams, symbolValue, symbolValue, symbolValue, symbolValue)
	// 三级代理其他游戏：16个（8种游戏 × 2个占位符）
	for i := 0; i < 16; i++ {
		queryParams = append(queryParams, symbolValue)
	}
	// TotalCommissionAmount相关：2个
	queryParams = append(queryParams, symbolValue, symbolValue)
	// TotalGetCommissionAmount相关：3个
	queryParams = append(queryParams, symbolValue, symbolValue, symbolValue)
	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)
	// 添加分页参数
	queryParams = append(queryParams, limit, offset)

	// 打印SQL日志用于调试
	// fmt.Printf("=== 佣金报表查询SQL ===\n")
	// fmt.Printf("SQL: %s\n", sql)
	// fmt.Printf("币种参数数量: %d\n", 67)
	// fmt.Printf("WHERE参数数量: %d\n", len(whereParams))
	// fmt.Printf("分页参数数量: %d\n", 2)
	// fmt.Printf("总参数数量: %d\n", len(queryParams))
	// fmt.Printf("参数: %+v\n", queryParams)
	// fmt.Printf("==================\n")
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		var level1Data, level2Data, level3Data LevelData

		err := rows.Scan(
			&data.AgentId,
			&data.AccountName,
			&data.SellerName,
			&data.ChannelName,
			&data.Date,
			&data.ParentAgentId,
			&data.TeamUserCount,
			&data.TeamValidUserCount,
			&data.TeamValidUserBetAmount,
			&data.TeamLevel,

			// 一级代理数据
			&level1Data.RewardHaXi, &level1Data.LiuShuiHaXi,
			&level1Data.RewardHaXiRoulette, &level1Data.LiuShuiHaXiRoulette,
			&level1Data.RewardLottery, &level1Data.LiuShuiLottery,
			&level1Data.RewardLowLottery, &level1Data.LiuShuiLowLottery,
			&level1Data.RewardQiPai, &level1Data.LiuShuiQiPai,
			&level1Data.RewardDianZhi, &level1Data.LiuShuiDianZhi,
			&level1Data.RewardXiaoYouXi, &level1Data.LiuShuiXiaoYouXi,
			&level1Data.RewardLive, &level1Data.LiuShuiLive,
			&level1Data.RewardSport, &level1Data.LiuShuiSport,
			&level1Data.RewardTexas, &level1Data.LiuShuiTexas,

			// 二级代理数据
			&level2Data.RewardHaXi, &level2Data.LiuShuiHaXi,
			&level2Data.RewardHaXiRoulette, &level2Data.LiuShuiHaXiRoulette,
			&level2Data.RewardLottery, &level2Data.LiuShuiLottery,
			&level2Data.RewardLowLottery, &level2Data.LiuShuiLowLottery,
			&level2Data.RewardQiPai, &level2Data.LiuShuiQiPai,
			&level2Data.RewardDianZhi, &level2Data.LiuShuiDianZhi,
			&level2Data.RewardXiaoYouXi, &level2Data.LiuShuiXiaoYouXi,
			&level2Data.RewardLive, &level2Data.LiuShuiLive,
			&level2Data.RewardSport, &level2Data.LiuShuiSport,
			&level2Data.RewardTexas, &level2Data.LiuShuiTexas,

			// 三级代理数据
			&level3Data.RewardHaXi, &level3Data.LiuShuiHaXi,
			&level3Data.RewardHaXiRoulette, &level3Data.LiuShuiHaXiRoulette,
			&level3Data.RewardLottery, &level3Data.LiuShuiLottery,
			&level3Data.RewardLowLottery, &level3Data.LiuShuiLowLottery,
			&level3Data.RewardQiPai, &level3Data.LiuShuiQiPai,
			&level3Data.RewardDianZhi, &level3Data.LiuShuiDianZhi,
			&level3Data.RewardXiaoYouXi, &level3Data.LiuShuiXiaoYouXi,
			&level3Data.RewardLive, &level3Data.LiuShuiLive,
			&level3Data.RewardSport, &level3Data.LiuShuiSport,
			&level3Data.RewardTexas, &level3Data.LiuShuiTexas,

			// 佣金数据
			&data.TotalCommissionAmount,
			&data.TotalGetCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}

		data.Level1Data = level1Data
		data.Level2Data = level2Data
		data.Level3Data = level3Data

		results = append(results, data)
	}

	// 查询总数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) as total FROM (SELECT count(*)
		FROM x_agent_commission_level_date d
		LEFT JOIN x_user u ON d.AgentId = u.UserId
		LEFT JOIN x_seller s ON d.SellerId = s.SellerId
		LEFT JOIN x_channel ch ON d.ChannelId = ch.ChannelId
		%s
		GROUP BY d.AgentId, d.StatDate) a`, whereClause)

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, whereParams...).Scan(&total).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据
	totalSQL := fmt.Sprintf(`
		SELECT
			-- IFNULL(SUM(d.BetTotalUsers), 0) AS TeamUserCount,
			IFNULL((SELECT COUNT(DISTINCT sub.UserId,sub.AgentId)
				FROM x_agent_commission_user_date sub
				LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
				WHERE (sub.StatDate = ? OR ? = 0)
				AND (sub.AgentId = ? OR ? = 0)
				AND (sub.ParentAgentId = ? OR ? = 0)
				AND (sub.SellerId = ? OR ? = 0)
				AND (sub.ChannelId = ? OR ? = 0)
				AND (u2.Account LIKE ? OR ? = '')
				AND sub.AgentId IN (SELECT AgentId FROM x_agent_commission_level_date GROUP BY AgentId)
			), 0) AS TeamUserCount,
			IFNULL((SELECT COUNT(DISTINCT sub.UserId,sub.AgentId)
				FROM x_agent_commission_user_date sub
				LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
				WHERE (sub.StatDate = ? OR ? = 0)
				AND (sub.AgentId = ? OR ? = 0)
				AND (sub.ParentAgentId = ? OR ? = 0)
				AND (sub.SellerId = ? OR ? = 0)
				AND (sub.ChannelId = ? OR ? = 0)
				AND (u2.Account LIKE ? OR ? = '')
				AND (sub.IsVaild = 1)
				AND sub.AgentId IN (SELECT AgentId FROM x_agent_commission_level_date GROUP BY AgentId)
			), 0) AS TeamValidUserCount,
			IFNULL(SUM(
				CASE
					WHEN ? = 1 THEN d.BetHaXi + d.BetHaXiRoulette + d.BetTranferUsdtHaXi + d.BetTranferUsdtHaXiRoulette + d.BetLottery + d.BetLowLottery + d.BetQiPai + d.BetDianZhi + d.BetXiaoYouXi + d.BetLive + d.BetSport + d.BetTexas
					WHEN ? = 2 THEN (d.BetTranferTrxHaXi + d.BetTranferTrxHaXiRoulette) * d.TrxRate
					ELSE d.BetHaXi + d.BetHaXiRoulette + d.BetTranferUsdtHaXi + d.BetTranferUsdtHaXiRoulette + (d.BetTranferTrxHaXi + d.BetTranferTrxHaXiRoulette) * d.TrxRate + d.BetLottery + d.BetLowLottery + d.BetQiPai + d.BetDianZhi + d.BetXiaoYouXi + d.BetLive + d.BetSport + d.BetTexas
				END
			), 0) AS TeamValidUserBetAmount,

			-- 一级代理汇总数据（只汇总流水，不汇总比例）
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
					ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level1_LiuShuiHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
					ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level1_LiuShuiHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
					ELSE 0
				END
			ELSE 0 END), 0) AS Level1_LiuShuiTexas,

			-- 二级代理汇总数据（只汇总流水，不汇总比例）
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
					ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level2_LiuShuiHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
					ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level2_LiuShuiHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
					ELSE 0
				END
			ELSE 0 END), 0) AS Level2_LiuShuiTexas,

			-- 三级代理汇总数据（只汇总流水，不汇总比例）
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
					ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level3_LiuShuiHaXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
					WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
					ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
				END
			ELSE 0 END), 0) AS Level3_LiuShuiHaXiRoulette,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiLowLottery,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiQiPai,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiDianZhi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiXiaoYouXi,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiLive,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiSport,
			IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
				CASE
					WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
					ELSE 0
				END
			ELSE 0 END), 0) AS Level3_LiuShuiTexas,

			-- 佣金汇总数据
			IFNULL(SUM(CASE
				WHEN ? = 1 THEN d.CommissionAmount
				WHEN ? = 2 THEN d.CommissionTrxAmount
				ELSE d.CommissionAmount + d.CommissionTrxAmount * d.TrxRate
			END), 0) AS TotalCommissionAmount,

			IFNULL(SUM(CASE WHEN E.GetStatus=1 AND ? = 1 THEN E.CommissionAmount
						WHEN E.GetStatus=1 AND ? = 2 THEN E.CommissionTrxAmount
						WHEN E.GetStatus=1 AND ? = 0 THEN E.CommissionAmount+E.CommissionTrxAmount*E.TrxRate
						ELSE 0 END), 0) AS TotalGetCommissionAmount

		FROM x_agent_commission_level_date d
		INNER JOIN x_agent_commission_data_date AS E
			ON d.StatDate=E.StatDate
			AND d.AgentId=E.AgentId
		LEFT JOIN x_user u ON d.AgentId = u.UserId
		LEFT JOIN x_seller s ON d.SellerId = s.SellerId
		LEFT JOIN x_channel ch ON d.ChannelId = ch.ChannelId
		%s`, whereClause)

	// 构建汇总查询参数
	var totalQueryParams []interface{}

	// 添加TeamValidUserCount子查询的参数
	// 注意：只有当dateCondition包含?占位符时才需要添加参数
	// 当reqdata.Date = 0时，dateCondition = "1=1"，不需要参数
	// 当reqdata.Date > 0时，dateCondition = "sub.StatDate = ?"，需要参数

	totalQueryParams = append(totalQueryParams, abugo.TimeStampToLocalTime(reqdata.Date), abugo.TimeStampToLocalTime(reqdata.Date))
	// 用于判断是否有日期条件

	// AgentId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.AgentId, reqdata.AgentId)

	// ParentAgentId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.ParentAgentId, reqdata.ParentAgentId)

	// SellerId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.SellerId, reqdata.SellerId)

	// ChannelId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.ChannelId, reqdata.ChannelId)

	// AccountName条件参数
	totalQueryParams = append(totalQueryParams, accountNameParam, reqdata.AccountName)

	totalQueryParams = append(totalQueryParams, abugo.TimeStampToLocalTime(reqdata.Date), abugo.TimeStampToLocalTime(reqdata.Date))

	// AgentId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.AgentId, reqdata.AgentId)

	// ParentAgentId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.ParentAgentId, reqdata.ParentAgentId)

	// SellerId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.SellerId, reqdata.SellerId)

	// ChannelId条件参数
	totalQueryParams = append(totalQueryParams, reqdata.ChannelId, reqdata.ChannelId)

	// AccountName条件参数
	totalQueryParams = append(totalQueryParams, accountNameParam, reqdata.AccountName)

	// TeamValidUserBetAmount：2个
	totalQueryParams = append(totalQueryParams, symbolValue, symbolValue)
	// 一级代理流水数据：20个（哈希4个 + 哈希轮盘4个 + 其他游戏12个）
	for i := 0; i < 20; i++ {
		totalQueryParams = append(totalQueryParams, symbolValue)
	}
	// 二级代理流水数据：20个（哈希4个 + 哈希轮盘4个 + 其他游戏12个）
	for i := 0; i < 20; i++ {
		totalQueryParams = append(totalQueryParams, symbolValue)
	}
	// 三级代理流水数据：20个（哈希4个 + 哈希轮盘4个 + 其他游戏12个）
	for i := 0; i < 20; i++ {
		totalQueryParams = append(totalQueryParams, symbolValue)
	}
	// TotalCommissionAmount：2个
	totalQueryParams = append(totalQueryParams, symbolValue, symbolValue)
	// TotalGetCommissionAmount：3个
	totalQueryParams = append(totalQueryParams, symbolValue, symbolValue, symbolValue)
	// WHERE条件参数
	totalQueryParams = append(totalQueryParams, whereParams...)

	var totalData ListData
	totalRow := server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Row()
	err = totalRow.Scan(
		&totalData.TeamUserCount,
		&totalData.TeamValidUserCount,
		&totalData.TeamValidUserBetAmount,
		// 一级代理流水数据（只有流水，没有比例）
		&totalData.Level1Data.LiuShuiHaXi,
		&totalData.Level1Data.LiuShuiHaXiRoulette,
		&totalData.Level1Data.LiuShuiLottery,
		&totalData.Level1Data.LiuShuiLowLottery,
		&totalData.Level1Data.LiuShuiQiPai,
		&totalData.Level1Data.LiuShuiDianZhi,
		&totalData.Level1Data.LiuShuiXiaoYouXi,
		&totalData.Level1Data.LiuShuiLive,
		&totalData.Level1Data.LiuShuiSport,
		&totalData.Level1Data.LiuShuiTexas,
		// 二级代理流水数据（只有流水，没有比例）
		&totalData.Level2Data.LiuShuiHaXi,
		&totalData.Level2Data.LiuShuiHaXiRoulette,
		&totalData.Level2Data.LiuShuiLottery,
		&totalData.Level2Data.LiuShuiLowLottery,
		&totalData.Level2Data.LiuShuiQiPai,
		&totalData.Level2Data.LiuShuiDianZhi,
		&totalData.Level2Data.LiuShuiXiaoYouXi,
		&totalData.Level2Data.LiuShuiLive,
		&totalData.Level2Data.LiuShuiSport,
		&totalData.Level2Data.LiuShuiTexas,
		// 三级代理流水数据（只有流水，没有比例）
		&totalData.Level3Data.LiuShuiHaXi,
		&totalData.Level3Data.LiuShuiHaXiRoulette,
		&totalData.Level3Data.LiuShuiLottery,
		&totalData.Level3Data.LiuShuiLowLottery,
		&totalData.Level3Data.LiuShuiQiPai,
		&totalData.Level3Data.LiuShuiDianZhi,
		&totalData.Level3Data.LiuShuiXiaoYouXi,
		&totalData.Level3Data.LiuShuiLive,
		&totalData.Level3Data.LiuShuiSport,
		&totalData.Level3Data.LiuShuiTexas,
		// 佣金数据
		&totalData.TotalCommissionAmount,
		&totalData.TotalGetCommissionAmount,
	)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 检查是否需要导出
	if reqdata.Export == 1 {

		// 重新执行查询获取所有数据（不分页）- 完整的SQL查询
		exportSQL := fmt.Sprintf(`
				SELECT
					d.AgentId,
					IFNULL(u.Account, '') AS AccountName,
					IFNULL(s.SellerName, '') AS SellerName,
					IFNULL(ch.ChannelName, '') AS ChannelName,
					DATE(d.StatDate) AS Date,
					d.ParentAgentId,
					-- IFNULL(SUM(d.BetTotalUsers), 0) AS TeamUserCount,
					IFNULL((SELECT COUNT(DISTINCT sub.UserId)
						FROM x_agent_commission_user_date sub
						LEFT JOIN x_user u2 ON sub.AgentId = u2.UserId
						WHERE sub.AgentId = d.AgentId
						AND (sub.StatDate = d.StatDate)
						AND (%s OR ? = 0)
						AND (sub.ParentAgentId = ? OR ? = 0)
						AND (sub.SellerId = ? OR ? = 0)
						AND (sub.ChannelId = ? OR ? = 0)
						AND (u2.Account LIKE ? OR ? = '')
					),0) as TeamUserCount,
					IFNULL(SUM(d.VaildUsers), 0) AS TeamValidUserCount,
					IFNULL(SUM(
						CASE
							WHEN ? = 1 THEN d.BetHaXi + d.BetHaXiRoulette + d.BetTranferUsdtHaXi + d.BetTranferUsdtHaXiRoulette + d.BetLottery + d.BetLowLottery + d.BetQiPai + d.BetDianZhi + d.BetXiaoYouXi + d.BetLive + d.BetSport + d.BetTexas
							WHEN ? = 2 THEN (d.BetTranferTrxHaXi + d.BetTranferTrxHaXiRoulette) * d.TrxRate
							ELSE d.BetHaXi + d.BetHaXiRoulette + d.BetTranferUsdtHaXi + d.BetTranferUsdtHaXiRoulette + (d.BetTranferTrxHaXi + d.BetTranferTrxHaXiRoulette) * d.TrxRate + d.BetLottery + d.BetLowLottery + d.BetQiPai + d.BetDianZhi + d.BetXiaoYouXi + d.BetLive + d.BetSport + d.BetTexas
						END
					), 0) AS TeamValidUserBetAmount,
					d.TeamLevel,

					-- 一级代理数据（完整的币种区分逻辑）
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardHaXi ELSE 0 END), 0) AS Level1_RewardHaXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
							WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
							ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
						END
					ELSE 0 END), 0) AS Level1_LiuShuiHaXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardHaXiRoulette ELSE 0 END), 0) AS Level1_RewardHaXiRoulette,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
							WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
							ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
						END
					ELSE 0 END), 0) AS Level1_LiuShuiHaXiRoulette,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardLottery ELSE 0 END), 0) AS Level1_RewardLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardLowLottery ELSE 0 END), 0) AS Level1_RewardLowLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiLowLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardQiPai ELSE 0 END), 0) AS Level1_RewardQiPai,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiQiPai,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardDianZhi ELSE 0 END), 0) AS Level1_RewardDianZhi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiDianZhi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardXiaoYouXi ELSE 0 END), 0) AS Level1_RewardXiaoYouXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiXiaoYouXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardLive ELSE 0 END), 0) AS Level1_RewardLive,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiLive,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardSport ELSE 0 END), 0) AS Level1_RewardSport,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiSport,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN d.RewardTexas ELSE 0 END), 0) AS Level1_RewardTexas,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 1 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
							ELSE 0
						END
					ELSE 0 END), 0) AS Level1_LiuShuiTexas,

					-- 二级代理数据（完整的币种区分逻辑）
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardHaXi ELSE 0 END), 0) AS Level2_RewardHaXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
							WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
							ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
						END
					ELSE 0 END), 0) AS Level2_LiuShuiHaXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardHaXiRoulette ELSE 0 END), 0) AS Level2_RewardHaXiRoulette,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
							WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
							ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
						END
					ELSE 0 END), 0) AS Level2_LiuShuiHaXiRoulette,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardLottery ELSE 0 END), 0) AS Level2_RewardLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardLowLottery ELSE 0 END), 0) AS Level2_RewardLowLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiLowLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardQiPai ELSE 0 END), 0) AS Level2_RewardQiPai,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiQiPai,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardDianZhi ELSE 0 END), 0) AS Level2_RewardDianZhi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiDianZhi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardXiaoYouXi ELSE 0 END), 0) AS Level2_RewardXiaoYouXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiXiaoYouXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardLive ELSE 0 END), 0) AS Level2_RewardLive,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiLive,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardSport ELSE 0 END), 0) AS Level2_RewardSport,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiSport,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN d.RewardTexas ELSE 0 END), 0) AS Level2_RewardTexas,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 2 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
							ELSE 0
						END
					ELSE 0 END), 0) AS Level2_LiuShuiTexas,

					-- 三级代理数据（完整的币种区分逻辑）
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardHaXi ELSE 0 END), 0) AS Level3_RewardHaXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 THEN d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi
							WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXi * d.TrxRate)
							ELSE d.LiuShuiHaXi + d.LiuShuiTranferUsdtHaXi + (d.LiuShuiTranferTrxHaXi * d.TrxRate)
						END
					ELSE 0 END), 0) AS Level3_LiuShuiHaXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardHaXiRoulette ELSE 0 END), 0) AS Level3_RewardHaXiRoulette,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 THEN d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette
							WHEN ? = 2 THEN (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
							ELSE d.LiuShuiHaXiRoulette + d.LiuShuiTranferUsdtHaXiRoulette + (d.LiuShuiTranferTrxHaXiRoulette * d.TrxRate)
						END
					ELSE 0 END), 0) AS Level3_LiuShuiHaXiRoulette,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardLottery ELSE 0 END), 0) AS Level3_RewardLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLottery
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardLowLottery ELSE 0 END), 0) AS Level3_RewardLowLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLowLottery
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiLowLottery,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardQiPai ELSE 0 END), 0) AS Level3_RewardQiPai,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiQiPai
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiQiPai,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardDianZhi ELSE 0 END), 0) AS Level3_RewardDianZhi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiDianZhi
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiDianZhi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardXiaoYouXi ELSE 0 END), 0) AS Level3_RewardXiaoYouXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiXiaoYouXi
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiXiaoYouXi,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardLive ELSE 0 END), 0) AS Level3_RewardLive,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiLive
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiLive,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardSport ELSE 0 END), 0) AS Level3_RewardSport,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiSport
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiSport,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN d.RewardTexas ELSE 0 END), 0) AS Level3_RewardTexas,
					IFNULL(SUM(CASE WHEN d.AgentLevel = 3 THEN
						CASE
							WHEN ? = 1 OR ? = 0 THEN d.LiuShuiTexas
							ELSE 0
						END
					ELSE 0 END), 0) AS Level3_LiuShuiTexas,

					-- 佣金相关
					IFNULL(SUM(CASE
						WHEN ? = 1 THEN d.CommissionAmount
						WHEN ? = 2 THEN d.CommissionTrxAmount
						ELSE d.CommissionAmount + d.CommissionTrxAmount * d.TrxRate
					END), 0) AS TotalCommissionAmount,

					IFNULL(SUM(CASE WHEN E.GetStatus=1 AND ? = 1 THEN E.CommissionAmount
								WHEN E.GetStatus=1 AND ? = 2 THEN E.CommissionTrxAmount
								WHEN E.GetStatus=1 AND ? = 0 THEN E.CommissionAmount+E.CommissionTrxAmount*E.TrxRate
								ELSE 0 END), 0) AS TotalGetCommissionAmount

				FROM x_agent_commission_level_date d
				INNER JOIN x_agent_commission_data_date AS E
					ON d.StatDate=E.StatDate
					AND d.AgentId=E.AgentId
				LEFT JOIN x_user u ON d.AgentId = u.UserId
				LEFT JOIN x_seller s ON d.SellerId = s.SellerId
				LEFT JOIN x_channel ch ON d.ChannelId = ch.ChannelId
				%s
				GROUP BY d.AgentId, d.StatDate
				ORDER BY DATE(d.StatDate) DESC`, dateCondition, whereClause)

		// 构建导出查询参数（与主查询相同的参数构建逻辑，但不包含分页参数）
		var exportQueryParams []interface{}

		// 添加TeamUserCount子查询的参数（10个参数）
		// 日期条件参数
		if reqdata.Date > 0 {
			exportQueryParams = append(exportQueryParams, abugo.TimeStampToLocalTime(reqdata.Date))
		}
		exportQueryParams = append(exportQueryParams, reqdata.Date) // 用于判断是否有日期条件

		// ParentAgentId条件参数
		exportQueryParams = append(exportQueryParams, reqdata.ParentAgentId, reqdata.ParentAgentId)

		// SellerId条件参数
		exportQueryParams = append(exportQueryParams, reqdata.SellerId, reqdata.SellerId)

		// ChannelId条件参数
		exportQueryParams = append(exportQueryParams, reqdata.ChannelId, reqdata.ChannelId)

		// AccountName条件参数
		exportQueryParams = append(exportQueryParams, accountNameParam, reqdata.AccountName)

		// 添加币种参数（共67个占位符，与主查询完全一致）
		// TeamValidUserBetAmount：2个
		exportQueryParams = append(exportQueryParams, symbolValue, symbolValue)
		// 一级代理数据：20个（哈希4个 + 哈希轮盘4个 + 其他游戏12个）
		for i := 0; i < 20; i++ {
			exportQueryParams = append(exportQueryParams, symbolValue)
		}
		// 二级代理数据：20个（哈希4个 + 哈希轮盘4个 + 其他游戏12个）
		for i := 0; i < 20; i++ {
			exportQueryParams = append(exportQueryParams, symbolValue)
		}
		// 三级代理数据：20个（哈希4个 + 哈希轮盘4个 + 其他游戏12个）
		for i := 0; i < 20; i++ {
			exportQueryParams = append(exportQueryParams, symbolValue)
		}
		// TotalCommissionAmount：2个
		exportQueryParams = append(exportQueryParams, symbolValue, symbolValue)
		// TotalGetCommissionAmount：3个
		exportQueryParams = append(exportQueryParams, symbolValue, symbolValue, symbolValue)
		// 添加WHERE条件参数
		exportQueryParams = append(exportQueryParams, whereParams...)

		// 执行导出查询
		exportRows, err := server.DbReport().GormDao().Raw(exportSQL, exportQueryParams...).Rows()
		if ctx.RespErr(err, &errcode) {
			return
		}
		defer exportRows.Close()

		var exportResults []ListData
		for exportRows.Next() {
			var data ListData
			var level1Data, level2Data, level3Data LevelData

			err := exportRows.Scan(
				&data.AgentId,
				&data.AccountName,
				&data.SellerName,
				&data.ChannelName,
				&data.Date,
				&data.ParentAgentId,
				&data.TeamUserCount,
				&data.TeamValidUserCount,
				&data.TeamValidUserBetAmount,
				&data.TeamLevel,

				// 一级代理数据扫描（20个字段）
				&level1Data.RewardHaXi, &level1Data.LiuShuiHaXi,
				&level1Data.RewardHaXiRoulette, &level1Data.LiuShuiHaXiRoulette,
				&level1Data.RewardLottery, &level1Data.LiuShuiLottery,
				&level1Data.RewardLowLottery, &level1Data.LiuShuiLowLottery,
				&level1Data.RewardQiPai, &level1Data.LiuShuiQiPai,
				&level1Data.RewardDianZhi, &level1Data.LiuShuiDianZhi,
				&level1Data.RewardXiaoYouXi, &level1Data.LiuShuiXiaoYouXi,
				&level1Data.RewardLive, &level1Data.LiuShuiLive,
				&level1Data.RewardSport, &level1Data.LiuShuiSport,
				&level1Data.RewardTexas, &level1Data.LiuShuiTexas,

				// 二级代理数据扫描（20个字段）
				&level2Data.RewardHaXi, &level2Data.LiuShuiHaXi,
				&level2Data.RewardHaXiRoulette, &level2Data.LiuShuiHaXiRoulette,
				&level2Data.RewardLottery, &level2Data.LiuShuiLottery,
				&level2Data.RewardLowLottery, &level2Data.LiuShuiLowLottery,
				&level2Data.RewardQiPai, &level2Data.LiuShuiQiPai,
				&level2Data.RewardDianZhi, &level2Data.LiuShuiDianZhi,
				&level2Data.RewardXiaoYouXi, &level2Data.LiuShuiXiaoYouXi,
				&level2Data.RewardLive, &level2Data.LiuShuiLive,
				&level2Data.RewardSport, &level2Data.LiuShuiSport,
				&level2Data.RewardTexas, &level2Data.LiuShuiTexas,

				// 三级代理数据扫描（20个字段）
				&level3Data.RewardHaXi, &level3Data.LiuShuiHaXi,
				&level3Data.RewardHaXiRoulette, &level3Data.LiuShuiHaXiRoulette,
				&level3Data.RewardLottery, &level3Data.LiuShuiLottery,
				&level3Data.RewardLowLottery, &level3Data.LiuShuiLowLottery,
				&level3Data.RewardQiPai, &level3Data.LiuShuiQiPai,
				&level3Data.RewardDianZhi, &level3Data.LiuShuiDianZhi,
				&level3Data.RewardXiaoYouXi, &level3Data.LiuShuiXiaoYouXi,
				&level3Data.RewardLive, &level3Data.LiuShuiLive,
				&level3Data.RewardSport, &level3Data.LiuShuiSport,
				&level3Data.RewardTexas, &level3Data.LiuShuiTexas,

				// 佣金数据
				&data.TotalCommissionAmount,
				&data.TotalGetCommissionAmount,
			)
			if ctx.RespErr(err, &errcode) {
				return
			}

			data.Level1Data = level1Data
			data.Level2Data = level2Data
			data.Level3Data = level3Data
			exportResults = append(exportResults, data)
		}

		fileName := fmt.Sprintf("三级代理佣金数据_%d.xlsx", time.Now().Unix())
		excel := excelize.NewFile()
		sheetName := "佣金报表"
		excel.NewSheet(sheetName)
		excel.DeleteSheet("Sheet1")

		// 设置表头
		headers := []interface{}{
			"代理ID", "账户名", "运营商名称", "渠道名称", "日期", "上级代理ID", "团队用户数", "团队有效用户数", "团队有效用户投注金额", "团队等级",
			"一级哈希返佣比例", "一级哈希流水", "一级哈希轮盘返佣比例", "一级哈希轮盘流水", "一级彩票返佣比例", "一级彩票流水",
			"一级低频彩返佣比例", "一级低频彩流水", "一级棋牌返佣比例", "一级棋牌流水", "一级电子返佣比例", "一级电子流水",
			"一级小游戏返佣比例", "一级小游戏流水", "一级真人返佣比例", "一级真人流水", "一级体育返佣比例", "一级体育流水",
			"一级德州返佣比例", "一级德州流水",
			"二级哈希返佣比例", "二级哈希流水", "二级哈希轮盘返佣比例", "二级哈希轮盘流水", "二级彩票返佣比例", "二级彩票流水",
			"二级低频彩返佣比例", "二级低频彩流水", "二级棋牌返佣比例", "二级棋牌流水", "二级电子返佣比例", "二级电子流水",
			"二级小游戏返佣比例", "二级小游戏流水", "二级真人返佣比例", "二级真人流水", "二级体育返佣比例", "二级体育流水",
			"二级德州返佣比例", "二级德州流水",
			"三级哈希返佣比例", "三级哈希流水", "三级哈希轮盘返佣比例", "三级哈希轮盘流水", "三级彩票返佣比例", "三级彩票流水",
			"三级低频彩返佣比例", "三级低频彩流水", "三级棋牌返佣比例", "三级棋牌流水", "三级电子返佣比例", "三级电子流水",
			"三级小游戏返佣比例", "三级小游戏流水", "三级真人返佣比例", "三级真人流水", "三级体育返佣比例", "三级体育流水",
			"三级德州返佣比例", "三级德州流水",
			"历史总佣金", "已领取佣金", "剩余取佣金",
		}
		err = excel.SetSheetRow(sheetName, "A1", &headers)
		if ctx.RespErr(err, &errcode) {
			return
		}

		// 写入所有导出数据
		for i, item := range exportResults {
			row := []interface{}{
				item.AgentId, item.AccountName, item.SellerName, item.ChannelName, item.Date, item.ParentAgentId,
				item.TeamUserCount, item.TeamValidUserCount, item.TeamValidUserBetAmount, item.TeamLevel,
				item.Level1Data.RewardHaXi, item.Level1Data.LiuShuiHaXi, item.Level1Data.RewardHaXiRoulette, item.Level1Data.LiuShuiHaXiRoulette,
				item.Level1Data.RewardLottery, item.Level1Data.LiuShuiLottery, item.Level1Data.RewardLowLottery, item.Level1Data.LiuShuiLowLottery,
				item.Level1Data.RewardQiPai, item.Level1Data.LiuShuiQiPai, item.Level1Data.RewardDianZhi, item.Level1Data.LiuShuiDianZhi,
				item.Level1Data.RewardXiaoYouXi, item.Level1Data.LiuShuiXiaoYouXi, item.Level1Data.RewardLive, item.Level1Data.LiuShuiLive,
				item.Level1Data.RewardSport, item.Level1Data.LiuShuiSport, item.Level1Data.RewardTexas, item.Level1Data.LiuShuiTexas,
				item.Level2Data.RewardHaXi, item.Level2Data.LiuShuiHaXi, item.Level2Data.RewardHaXiRoulette, item.Level2Data.LiuShuiHaXiRoulette,
				item.Level2Data.RewardLottery, item.Level2Data.LiuShuiLottery, item.Level2Data.RewardLowLottery, item.Level2Data.LiuShuiLowLottery,
				item.Level2Data.RewardQiPai, item.Level2Data.LiuShuiQiPai, item.Level2Data.RewardDianZhi, item.Level2Data.LiuShuiDianZhi,
				item.Level2Data.RewardXiaoYouXi, item.Level2Data.LiuShuiXiaoYouXi, item.Level2Data.RewardLive, item.Level2Data.LiuShuiLive,
				item.Level2Data.RewardSport, item.Level2Data.LiuShuiSport, item.Level2Data.RewardTexas, item.Level2Data.LiuShuiTexas,
				item.Level3Data.RewardHaXi, item.Level3Data.LiuShuiHaXi, item.Level3Data.RewardHaXiRoulette, item.Level3Data.LiuShuiHaXiRoulette,
				item.Level3Data.RewardLottery, item.Level3Data.LiuShuiLottery, item.Level3Data.RewardLowLottery, item.Level3Data.LiuShuiLowLottery,
				item.Level3Data.RewardQiPai, item.Level3Data.LiuShuiQiPai, item.Level3Data.RewardDianZhi, item.Level3Data.LiuShuiDianZhi,
				item.Level3Data.RewardXiaoYouXi, item.Level3Data.LiuShuiXiaoYouXi, item.Level3Data.RewardLive, item.Level3Data.LiuShuiLive,
				item.Level3Data.RewardSport, item.Level3Data.LiuShuiSport, item.Level3Data.RewardTexas, item.Level3Data.LiuShuiTexas,
				item.TotalCommissionAmount, item.TotalGetCommissionAmount, item.TotalCommissionAmount - item.TotalGetCommissionAmount,
			}
			cell := fmt.Sprintf("A%d", i+2)
			err := excel.SetSheetRow(sheetName, cell, &row)
			if ctx.RespErr(err, &errcode) {
				return
			}
		}

		// 使用已经查询到的总计数据（避免重复查询）

		// 写入总计行（使用已查询的totalData，避免重复查询）
		totalRowIndex := len(exportResults) + 2 // 数据行之后
		totalRowData := []interface{}{
			"总计", "", "", "", "", "",
			totalData.TeamUserCount, totalData.TeamValidUserCount, totalData.TeamValidUserBetAmount, "",
			0, totalData.Level1Data.LiuShuiHaXi, 0, totalData.Level1Data.LiuShuiHaXiRoulette,
			0, totalData.Level1Data.LiuShuiLottery, 0, totalData.Level1Data.LiuShuiLowLottery,
			0, totalData.Level1Data.LiuShuiQiPai, 0, totalData.Level1Data.LiuShuiDianZhi,
			0, totalData.Level1Data.LiuShuiXiaoYouXi, 0, totalData.Level1Data.LiuShuiLive,
			0, totalData.Level1Data.LiuShuiSport, 0, totalData.Level1Data.LiuShuiTexas,
			0, totalData.Level2Data.LiuShuiHaXi, 0, totalData.Level2Data.LiuShuiHaXiRoulette,
			0, totalData.Level2Data.LiuShuiLottery, 0, totalData.Level2Data.LiuShuiLowLottery,
			0, totalData.Level2Data.LiuShuiQiPai, 0, totalData.Level2Data.LiuShuiDianZhi,
			0, totalData.Level2Data.LiuShuiXiaoYouXi, 0, totalData.Level2Data.LiuShuiLive,
			0, totalData.Level2Data.LiuShuiSport, 0, totalData.Level2Data.LiuShuiTexas,
			0, totalData.Level3Data.LiuShuiHaXi, 0, totalData.Level3Data.LiuShuiHaXiRoulette,
			0, totalData.Level3Data.LiuShuiLottery, 0, totalData.Level3Data.LiuShuiLowLottery,
			0, totalData.Level3Data.LiuShuiQiPai, 0, totalData.Level3Data.LiuShuiDianZhi,
			0, totalData.Level3Data.LiuShuiXiaoYouXi, 0, totalData.Level3Data.LiuShuiLive,
			0, totalData.Level3Data.LiuShuiSport, 0, totalData.Level3Data.LiuShuiTexas,
			totalData.TotalCommissionAmount, totalData.TotalGetCommissionAmount, totalData.TotalCommissionAmount - totalData.TotalGetCommissionAmount,
		}
		totalCell := fmt.Sprintf("A%d", totalRowIndex)
		err = excel.SetSheetRow(sheetName, totalCell, &totalRowData)
		if ctx.RespErr(err, &errcode) {
			return
		}

		// 保存Excel文件
		if err := excel.SaveAs(server.ExportDir() + "/" + fileName); err != nil {
			ctx.RespErrString(true, &errcode, "保存Excel失败")
			return
		}

		// 返回导出结果（与reportList保持一致的格式）
		ctx.RespOK(map[string]interface{}{
			"fileName": "/exports/" + fileName,
			"message":  "导出成功",
			"count":    len(exportResults),
		})
		server.WriteAdminLog("导出三级代理佣金数据报表", ctx, reqdata)
		return
	}

	// 构建响应数据
	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)
	server.WriteAdminLog("查询三级代理佣金数据报表", ctx, reqdata)
}

// 有效人数数据
func (c *ThreeLevelAgentController) commissionValidUserList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int   `json:"Page"`
		PageSize int   `json:"PageSize"`
		UserId   int   `json:"UserId"`
		Date     int64 `json:"Date"`
		Export   int   `json:"Export"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理佣金数据", "查"), &errcode, "权限不足") {
		return
	}

	// 设置默认分页参数
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	type ListData struct {
		UserId                int     `json:"UserId" gorm:"column:UserId"`                               // 玩家ID
		AccountName           string  `json:"AccountName" gorm:"column:AccountName"`                     // 账户名
		AgentLevel            int     `json:"AgentLevel" gorm:"column:AgentLevel"`                       // 代理等级
		TeamLevel             int     `json:"TeamLevel" gorm:"column:TeamLevel"`                         // 团队等级
		IsAgent               int     `json:"IsAgent" gorm:"column:IsAgent"`                             // 是否是代理
		TotalRechargeAmount   float64 `json:"TotalRechargeAmount" gorm:"column:TotalRechargeAmount"`     // 充值金额
		TotalWithdrawAmount   float64 `json:"TotalWithdrawAmount" gorm:"column:TotalWithdrawAmount"`     // 提现金额
		TotalBetAmount        float64 `json:"TotalBetAmount" gorm:"column:TotalBetAmount"`               // 总投注额
		TotalWinAmount        float64 `json:"TotalWinAmount" gorm:"column:TotalWinAmount"`               // 总派奖额
		TotalLiushui          float64 `json:"TotalLiushui" gorm:"column:TotalLiushui"`                   // 总流水
		TotalCaijin           float64 `json:"TotalCaijin" gorm:"column:TotalCaijin"`                     // 总彩金
		TotalVipRewardAmount  float64 `json:"TotalVipRewardAmount" gorm:"column:TotalVipRewardAmount"`   // VIP活动彩金金额
		TotalCommissionAmount float64 `json:"TotalCommissionAmount" gorm:"column:TotalCommissionAmount"` // 结算佣金USDT
	}

	type ResponseData struct {
		Total     int64      `json:"Total"`
		Data      []ListData `json:"Data"`
		TotalData ListData   `json:"TotalData"`
	}

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	agentIdStr := ctx.Gin().Param("agentId")
	if agentIdStr == "" {
		ctx.RespErrString(true, &errcode, "代理ID不能为空")
		return
	}

	// 将字符串转换为整数
	agentId, err := strconv.Atoi(agentIdStr)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理ID格式错误")
		return
	}

	whereConditions = append(whereConditions, "d.AgentId = ?")
	whereParams = append(whereParams, agentId)

	whereClause := ""
	if reqdata.UserId > 0 {
		whereConditions = append(whereConditions, "d.UserId = ?")
		whereParams = append(whereParams, reqdata.UserId)
	}
	if reqdata.Date > 0 {
		whereConditions = append(whereConditions, "d.StatDate = ?")
		whereParams = append(whereParams, abugo.TimeStampToLocalTime(reqdata.Date))
	}

	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	whereClause = whereClause + " AND d.IsVaild = 1"

	// 构建SQL查询
	sql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId
		ORDER BY d.UserId
		LIMIT ? OFFSET ?`, whereClause)

	// 构建参数数组 - 需要重复多次GameType和Symbol参数
	var queryParams []interface{}
	// 添加WHERE条件参数
	queryParams = append(queryParams, whereParams...)
	// 添加分页参数
	queryParams = append(queryParams, limit, offset)

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	var results []ListData
	for rows.Next() {
		var data ListData
		err := rows.Scan(
			&data.UserId,
			&data.AccountName,
			&data.AgentLevel,
			&data.TeamLevel,
			&data.IsAgent,
			&data.TotalRechargeAmount,
			&data.TotalWithdrawAmount,
			&data.TotalBetAmount,
			&data.TotalWinAmount,
			&data.TotalLiushui,
			&data.TotalCaijin,
			&data.TotalVipRewardAmount,
			&data.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	// 查询总数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) FROM (SELECT COUNT(*) as total
		FROM x_agent_commission_user_date as d
		%s
		GROUP BY d.UserId) a`, whereClause)

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, whereParams...).Scan(&total).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据
	totalSQL := fmt.Sprintf(`
		SELECT
			0 AS UserId,
			'' AS AccountName,
			0 AS AgentLevel,
			0 AS TeamLevel,
			0 AS IsAgent,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		%s`, whereClause)

	// 构建汇总查询参数
	var totalQueryParams []interface{}
	// 添加WHERE条件参数
	totalQueryParams = append(totalQueryParams, whereParams...)

	// 执行汇总查询
	totalRows, err := server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer totalRows.Close()

	var totalData ListData
	if totalRows.Next() {
		err := totalRows.Scan(
			&totalData.UserId,
			&totalData.AccountName,
			&totalData.AgentLevel,
			&totalData.TeamLevel,
			&totalData.IsAgent,
			&totalData.TotalRechargeAmount,
			&totalData.TotalWithdrawAmount,
			&totalData.TotalBetAmount,
			&totalData.TotalWinAmount,
			&totalData.TotalLiushui,
			&totalData.TotalCaijin,
			&totalData.TotalVipRewardAmount,
			&totalData.TotalCommissionAmount,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 导出
	if reqdata.Export == 1 {
		excelSql := fmt.Sprintf(`
		SELECT
			d.UserId,
			IFNULL(u.Account, '') AS AccountName,
			MAX(d.AgentLevel) AS AgentLevel,
			MAX(d.TeamLevel) AS TeamLevel,
			CASE WHEN a.ChildCount > 0 THEN 1 ELSE 0 END AS IsAgent,
			IFNULL(SUM(d.RechargeAmount),0) AS TotalRechargeAmount,
			IFNULL(SUM(d.WithdrawAmount),0) AS TotalWithdrawAmount,
			IFNULL(SUM(d.BetTranferUsdtHaXi+d.BetTranferUsdtHaXiRoulette+d.BetTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.BetTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.BetHaXi+d.BetHaXiRoulette+d.BetLottery+d.BetLowLottery+d.BetQiPai+d.BetDianZhi+d.BetXiaoYouXi+d.BetLive+d.BetSport+d.BetTexas),0) AS TotalBetAmount,
			IFNULL(SUM(d.WinTranferUsdtHaXi+d.WinTranferUsdtHaXiRoulette+d.WinTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.WinTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.WinHaXi+d.WinHaXiRoulette+d.WinLottery+d.WinLowLottery+d.WinQiPai+d.WinDianZhi+d.WinXiaoYouXi+d.WinLive+d.WinSport+d.WinTexas),0) AS TotalWinAmount,
			IFNULL(SUM(d.LiuShuiTranferUsdtHaXi+d.LiuShuiTranferUsdtHaXiRoulette+d.LiuShuiTranferTrxHaXi*IFNULL(d.TrxRate,1)+d.LiuShuiTranferTrxHaXiRoulette*IFNULL(d.TrxRate,1)+d.LiuShuiHaXi+d.LiuShuiHaXiRoulette+d.LiuShuiLottery+d.LiuShuiLowLottery+d.LiuShuiQiPai+d.LiuShuiDianZhi+d.LiuShuiXiaoYouXi+d.LiuShuiLive+d.LiuShuiSport+d.LiuShuiTexas),0) AS TotalLiushui,
			IFNULL(SUM(d.ActivityRewardAmount+d.VipRewardAmount+d.ManRewardAmount),0) AS TotalCaijin,
			IFNULL(SUM(d.VipRewardAmount),0) AS TotalVipRewardAmount,
			IFNULL(SUM(d.CommissionAmount + d.CommissionTrxAmount * IFNULL(d.TrxRate,1)),0) AS TotalCommissionAmount
		FROM x_agent_commission_user_date d
		LEFT JOIN x_user u ON d.UserId = u.UserId
		LEFT JOIN x_agent a ON d.UserId = a.UserId
		%s
		GROUP BY d.UserId
		ORDER BY d.UserId`, whereClause)

		excelRows, err := server.DbReport().GormDao().Raw(excelSql, whereParams...).Rows()
		if ctx.RespErr(err, &errcode) {
			return
		}
		defer excelRows.Close()

		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{
			"玩家ID",
			"账户名",
			"代理等级",
			"团队等级",
			"是否代理",
			"充值金额",
			"提现金额",
			"投注金额",
			"派奖金额",
			"流水金额",
			"彩金金额",
			"VIP彩金金额",
			"佣金金额",
		})

		i := 2
		for excelRows.Next() {
			var data ListData
			err := excelRows.Scan(
				&data.UserId,
				&data.AccountName,
				&data.AgentLevel,
				&data.TeamLevel,
				&data.IsAgent,
				&data.TotalRechargeAmount,
				&data.TotalWithdrawAmount,
				&data.TotalBetAmount,
				&data.TotalWinAmount,
				&data.TotalLiushui,
				&data.TotalCaijin,
				&data.TotalVipRewardAmount,
				&data.TotalCommissionAmount,
			)
			if ctx.RespErr(err, &errcode) {
				return
			}

			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i), &[]interface{}{
				data.UserId,
				data.AccountName,
				data.AgentLevel,
				data.TeamLevel,
				data.IsAgent,
				data.TotalRechargeAmount,
				data.TotalWithdrawAmount,
				data.TotalBetAmount,
				data.TotalWinAmount,
				data.TotalLiushui,
				data.TotalCaijin,
				data.TotalVipRewardAmount,
				data.TotalCommissionAmount,
			})
			i++
		}

		totalRowIndex := i // 数据行之后
		totalRowData := []interface{}{
			"总计", "", "", "", "", totalData.TotalRechargeAmount, totalData.TotalWithdrawAmount, totalData.TotalBetAmount, totalData.TotalWinAmount, totalData.TotalLiushui, totalData.TotalCaijin, totalData.TotalVipRewardAmount, totalData.TotalCommissionAmount,
		}
		totalCell := fmt.Sprintf("A%d", totalRowIndex)
		err = excel.SetSheetRow("Sheet1", totalCell, &totalRowData)
		if ctx.RespErr(err, &errcode) {
			return
		}

		filename := "三级代理团队等级数据_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		return
	}

	// 构建响应数据
	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)
}

//  commissionGameList

func (c *ThreeLevelAgentController) commissionGameList(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Date     int64  `json:"Date"`     // 查询日期时间戳
		Export   int    `json:"Export"`   // 是否导出：0-分页查询，1-导出Excel
		Level    int    `json:"Level"`    // 代理等级：1-一级代理，2-二级代理，3-三级代理
		GameType string `json:"GameType"` // 游戏类型：哈希、小游戏、真人、电子、体育、棋牌、彩票、低频彩、轮盘等
		OrderNum string `json:"OrderNum"` // 注单号
		Page     int    `json:"Page"`     // 页码
		PageSize int    `json:"PageSize"` // 每页大小
		Symbol   string `json:"Symbol"`   // 币种：trx、usdt
		UserId   int    `json:"UserId"`   // 用户ID
	}

	type ResponseData struct {
		Total     int64           `json:"Total"`
		Data      []GameOrderData `json:"Data"`
		TotalData GameOrderData   `json:"TotalData"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "三级代理佣金数据", "查"), &errcode, "权限不足") {
		return
	}
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}

	// 如果是导出，设置大的页面大小
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}

	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	agentIdStr := ctx.Gin().Param("agentId")
	if agentIdStr == "" {
		ctx.RespErrString(true, &errcode, "代理ID不能为空")
		return
	}

	// 将字符串转换为整数
	agentId, err := strconv.Atoi(agentIdStr)
	if err != nil {
		ctx.RespErrString(true, &errcode, "代理ID格式错误")
		return
	}

	// // 获取该代理的所有下级代理ids
	// userIds := []int{}
	// // 从x_agent_commission_user_date表中获取
	// userIdsSql := "SELECT UserId FROM x_agent_commission_user_date"
	// // 根据代理层级、时间拼装查询条件
	// var whereUserIdsConditions []string
	// var whereUserIdsParams []interface{}
	// whereUserIdsConditions = append(whereUserIdsConditions, "AgentId = ?")
	// whereUserIdsParams = append(whereUserIdsParams, agentId)
	// if reqdata.Level > 0 {
	// 	whereUserIdsConditions = append(whereUserIdsConditions, "AgentLevel = ?")
	// 	whereUserIdsParams = append(whereUserIdsParams, reqdata.Level)
	// }
	// if reqdata.Date > 0 {
	// 	whereUserIdsConditions = append(whereUserIdsConditions, "StatDate = ?")
	// 	whereUserIdsParams = append(whereUserIdsParams, abugo.TimeStampToLocalTime(reqdata.Date))
	// }
	// if len(whereUserIdsConditions) > 0 {
	// 	userIdsSql += " WHERE " + strings.Join(whereUserIdsConditions, " AND ")
	// }

	// rows, err := server.DbReport().GormDao().Raw(userIdsSql, whereUserIdsParams...).Rows()
	// if ctx.RespErr(err, &errcode) {
	// 	return
	// }
	// defer rows.Close()

	// for rows.Next() {
	// 	var userId int
	// 	err := rows.Scan(&userId)
	// 	if ctx.RespErr(err, &errcode) {
	// 		return
	// 	}
	// 	userIds = append(userIds, userId)
	// }

	// 构建查询条件
	var whereConditions []string
	var whereParams []interface{}

	// if len(userIds) > 0 {
	// 	// 构建IN查询的占位符
	// 	placeholders := make([]string, len(userIds))
	// 	for i, userId := range userIds {
	// 		placeholders[i] = "?"
	// 		whereParams = append(whereParams, userId)
	// 	}
	// 	whereConditions = append(whereConditions, fmt.Sprintf("o.UserId IN (%s)", strings.Join(placeholders, ",")))
	// } else {
	// 	// 返回空数据
	// 	ctx.RespOK(ResponseData{})
	// 	return
	// }

	// 代理ID条件
	if agentId > 0 {
		whereConditions = append(whereConditions, "cd.AgentId = ? AND o.UserId IS NOT NULL AND cd.IsVaild = 1")
		whereParams = append(whereParams, agentId)
	}

	// 代理等级条件
	if reqdata.Level > 0 {
		whereConditions = append(whereConditions, "cd.AgentLevel = ?")
		whereParams = append(whereParams, reqdata.Level)
	}

	// 用户ID条件
	if reqdata.UserId > 0 {
		whereConditions = append(whereConditions, "o.UserId = ?")
		whereParams = append(whereParams, reqdata.UserId)
	}

	// 日期条件
	if reqdata.Date > 0 {
		startTime := abugo.TimeStampToLocalTime(reqdata.Date)
		endTime := abugo.TimeStampToLocalTime(reqdata.Date + 86400000) // 加一天
		whereConditions = append(whereConditions, "cd.StatDate >= ? AND cd.StatDate < ?")
		whereParams = append(whereParams, startTime, endTime)
	}

	// 注单号条件
	if reqdata.OrderNum != "" {
		whereConditions = append(whereConditions, "o.Id = ?")
		whereParams = append(whereParams, reqdata.OrderNum)
	}

	// 币种条件
	if reqdata.Symbol != "" {
		reqdata.Symbol = strings.ToLower(reqdata.Symbol)
		if reqdata.GameType == "哈希" || reqdata.GameType == "轮盘" {
			whereConditions = append(whereConditions, "o.Symbol = ?")
			whereParams = append(whereParams, reqdata.Symbol)
		}
	}

	// 游戏类型条件
	gameTypeCondition := ""
	switch reqdata.GameType {
	case "哈希":
		gameTypeCondition = "o.GameId NOT IN (13,106,116,126,136,206,303,313,323,333)"
	case "轮盘":
		gameTypeCondition = "o.GameId IN (13,106,116,126,136,206,303,313,323,333)"
	case "真人":
		gameTypeCondition = "1=1" // 需要查询 x_third_live 表
	case "电子":
		gameTypeCondition = "1=1" // 需要查询 x_third_dianzhi 表
	case "体育":
		gameTypeCondition = "1=1" // 需要查询 x_third_sport 表
	case "棋牌":
		gameTypeCondition = "1=1" // 需要查询 x_third_qipai 表
	case "彩票":
		gameTypeCondition = "o.GameId NOT IN ('796','797')" // 需要查询 x_third_lottery 表
	case "德州":
		gameTypeCondition = "1=1" // 需要查询 x_third_texas 表
	case "小游戏":
		gameTypeCondition = "1=1" // 需要查询 x_third_quwei 表
	case "低频彩":
		gameTypeCondition = "o.GameId IN ('796', '797')" // 需要查询 x_third_lottery 表
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
		if gameTypeCondition != "" && gameTypeCondition != "1=1" {
			whereClause += " AND " + gameTypeCondition
		}
	} else if gameTypeCondition != "" && gameTypeCondition != "1=1" {
		whereClause = "WHERE " + gameTypeCondition
	}

	// 根据游戏类型构建不同的SQL查询
	var sql string
	var results []GameOrderData
	var queryParams []interface{}

	// 根据游戏类型选择查询表和字段
	if reqdata.GameType == "" {
		// 当游戏类型为空时，联合查询所有游戏类型
		results, total, totalData, err := c.getAllGameTypesData(whereClause, whereParams, reqdata, limit, offset)
		if ctx.RespErr(err, &errcode) {
			return
		}

		// 检查是否需要导出Excel
		if reqdata.Export == 1 {
			c.exportGameOrderExcel(ctx, results, totalData)
			return
		}

		response := ResponseData{
			Total:     total,
			Data:      results,
			TotalData: totalData,
		}

		ctx.RespOK(response)
		server.WriteAdminLog("查询游戏佣金数据", ctx, reqdata)
		return
	}

	switch reqdata.GameType {
	case "哈希", "轮盘":
		// 查询哈希/轮盘游戏注单 (x_order表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserId, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				CASE
					WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN '轮盘'
					ELSE '哈希'
				END as GameType,
				o.Symbol,
				IFNULL(o.Amount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.RewardAmount,0) as WinAmount,
				CASE
					WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN IFNULL(cd.RewardHaXiRoulette, 0)
					ELSE IFNULL(cd.RewardHaXi, 0)
				END as CommissionRate,
				CASE
					WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN o.LiuSui * IFNULL(cd.RewardHaXiRoulette, 0) / 100
					ELSE o.LiuSui * IFNULL(cd.RewardHaXi, 0) / 100
				END as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.CreateTime as BetTime,
				o.RewardTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime and o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.CreateTime DESC
			LIMIT ? OFFSET ?`, whereClause)

		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	case "真人":
		// 查询真人游戏注单 (x_third_live表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				o.UserID as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'真人' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardLive, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardLive, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_live o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.ThirdTime DESC
			LIMIT ? OFFSET ?`, whereClause)

		// 替换参数中的表前缀
		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	case "电子":
		// 查询电子游戏注单 (x_third_dianzhi表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'电子' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardDianZhi, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardDianZhi, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_dianzhi o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.ThirdTime DESC
			LIMIT ? OFFSET ?`, whereClause)
		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	case "体育":
		// 查询体育游戏注单 (x_third_sport表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'体育' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardSport, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardSport, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_sport o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.ThirdTime DESC
			LIMIT ? OFFSET ?`, whereClause)
		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	case "棋牌":
		// 查询棋牌游戏注单 (x_third_qipai表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'棋牌' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardQiPai, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardQiPai, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_qipai o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.ThirdTime DESC
			LIMIT ? OFFSET ?`, whereClause)

		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	case "彩票", "低频彩":
		// 查询彩票游戏注单 (x_third_lottery表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				CASE
					WHEN o.GameId IN ('796', '797') THEN '低频彩'
					ELSE '彩票'
				END as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				CASE
					WHEN o.GameId IN ('796', '797') THEN IFNULL(cd.RewardLowLottery, 0)
					ELSE IFNULL(cd.RewardLottery, 0)
				END as CommissionRate,
				CASE
					WHEN o.GameId IN ('796', '797') THEN o.ValidBet * IFNULL(cd.RewardLowLottery, 0) / 100
					ELSE o.ValidBet * IFNULL(cd.RewardLottery, 0) / 100
				END as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_lottery o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.ThirdTime DESC
			LIMIT ? OFFSET ?`, whereClause)
		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	case "德州":
		// 查询德州游戏注单 (x_third_texas表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'德州' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardTexas, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardTexas, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_texas o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.ThirdTime DESC
			LIMIT ? OFFSET ?`, whereClause)

		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	case "小游戏":
		// 查询小游戏注单 (x_third_quwei表)
		sql = fmt.Sprintf(`
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'小游戏' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardXiaoYouXi, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardXiaoYouXi, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_quwei o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.ThirdTime DESC
			LIMIT ? OFFSET ?`, whereClause)

		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)

	default:
		// 默认查询哈希游戏
		sql = fmt.Sprintf(`
			SELECT
				o.Id as OrderId,
				o.UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'哈希' as GameType,
				o.Symbol,
				o.Amount as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				o.RewardAmount as WinAmount,
				IFNULL(cd.RewardHaXi, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardHaXi, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.CreateTime as BetTime,
				o.RewardTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			ORDER BY o.CreateTime DESC
			LIMIT ? OFFSET ?`, whereClause)

		queryParams = append(queryParams, whereParams...)
		queryParams = append(queryParams, limit, offset)
	}

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(sql, queryParams...).Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var data GameOrderData
		err := rows.Scan(
			&data.OrderId,
			&data.UserId,
			&data.UserName,
			&data.UserAddress,
			&data.GameType,
			&data.Symbol,
			&data.BetAmount,
			&data.ValidBetAmount,
			&data.WinAmount,
			&data.CommissionRate,
			&data.CommissionAmount,
			&data.AgentLevel,
			&data.BetTime,
			&data.SettleTime,
			&data.CommissionDateTime,
		)
		if ctx.RespErr(err, &errcode) {
			return
		}
		results = append(results, data)
	}

	// 查询总数 - 根据游戏类型选择不同的表
	var countSQL string
	var countParams []interface{}

	switch reqdata.GameType {
	case "哈希", "轮盘", "":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	case "真人":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_live o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	case "电子":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_dianzhi o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	case "体育":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_sport o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	case "棋牌":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_qipai o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	case "彩票", "低频彩":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_lottery o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	case "德州":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_texas o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	case "小游戏":
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_quwei o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	default:
		countSQL = fmt.Sprintf(`
			SELECT COUNT(*) as total
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)
		countParams = append(countParams, whereParams...)
	}

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, countParams...).Scan(&total).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询汇总数据 - 根据游戏类型选择不同的表和字段
	var totalSQL string
	var totalQueryParams []interface{}

	switch reqdata.GameType {
	case "哈希", "轮盘", "":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.Amount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.RewardAmount), 0) as WinAmount,
				IFNULL(SUM(CASE
					WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN o.LiuSui * IFNULL(cd.RewardHaXiRoulette, 0) / 100
					ELSE o.LiuSui * IFNULL(cd.RewardHaXi, 0) / 100
				END), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	case "真人":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.BetAmount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.WinAmount), 0) as WinAmount,
				IFNULL(SUM(o.ValidBet * IFNULL(cd.RewardLive, 0) / 100), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_live o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	case "电子":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.BetAmount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.WinAmount), 0) as WinAmount,
				IFNULL(SUM(o.ValidBet * IFNULL(cd.RewardDianZhi, 0) / 100), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_dianzhi o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	case "体育":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.BetAmount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.WinAmount), 0) as WinAmount,
				IFNULL(SUM(o.ValidBet * IFNULL(cd.RewardSport, 0) / 100), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_sport o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	case "棋牌":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.BetAmount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.WinAmount), 0) as WinAmount,
				IFNULL(SUM(o.ValidBet * IFNULL(cd.RewardQiPai, 0) / 100), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_qipai o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	case "彩票", "低频彩":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.BetAmount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.WinAmount), 0) as WinAmount,
				CASE
					WHEN o.GameId IN ('796', '797') THEN IFNULL(cd.RewardLowLottery, 0)
					ELSE IFNULL(cd.RewardLottery, 0)
				END as CommissionRate,
				CASE
					WHEN o.GameId IN ('796', '797') THEN o.ValidBet * IFNULL(cd.RewardLowLottery, 0) / 100
					ELSE o.ValidBet * IFNULL(cd.RewardLottery, 0) / 100
				END as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_lottery o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	case "德州":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.BetAmount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.WinAmount), 0) as WinAmount,
				IFNULL(SUM(o.ValidBet * IFNULL(cd.RewardTexas, 0) / 100), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_texas o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	case "小游戏":
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.BetAmount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.WinAmount), 0) as WinAmount,
				IFNULL(SUM(o.ValidBet * IFNULL(cd.RewardXiaoYouXi, 0) / 100), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_quwei o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)

	default:
		totalSQL = fmt.Sprintf(`
			SELECT
				IFNULL(SUM(o.Amount), 0) as BetAmount,
				IFNULL(SUM(o.ValidBetAmount), 0) as ValidBetAmount,
				IFNULL(SUM(o.RewardAmount), 0) as WinAmount,
				IFNULL(SUM(o.ValidBet * IFNULL(cd.RewardHaXi, 0) / 100), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s`, whereClause)

		totalQueryParams = append(totalQueryParams, whereParams...)
	}

	var totalData GameOrderData
	err = server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Scan(&totalData).Error
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 检查是否需要导出Excel
	if reqdata.Export == 1 {
		c.exportGameOrderExcel(ctx, results, totalData)
		return
	}

	response := ResponseData{
		Total:     total,
		Data:      results,
		TotalData: totalData,
	}

	ctx.RespOK(response)
	server.WriteAdminLog("查询游戏佣金数据", ctx, reqdata)
}

// getAllGameTypesData 获取所有游戏类型的联合数据
func (c *ThreeLevelAgentController) getAllGameTypesData(whereClause string, whereParams []interface{}, reqdata struct {
	Date     int64  `json:"Date"`
	Export   int    `json:"Export"`
	Level    int    `json:"Level"`
	GameType string `json:"GameType"`
	OrderNum string `json:"OrderNum"`
	Page     int    `json:"Page"`
	PageSize int    `json:"PageSize"`
	Symbol   string `json:"Symbol"`
	UserId   int    `json:"UserId"`
}, limit, offset int) ([]GameOrderData, int64, GameOrderData, error) {
	var allResults []GameOrderData
	var totalData GameOrderData

	// 构建联合查询SQL
	unionSQL := fmt.Sprintf(`
		(
			SELECT
				o.Id as OrderId,
				o.UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				CASE
					WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN '轮盘'
					ELSE '哈希'
				END as GameType,
				o.Symbol,
				o.Amount as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				o.RewardAmount as WinAmount,
				CASE
					WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN IFNULL(cd.RewardHaXiRoulette, 0)
					ELSE IFNULL(cd.RewardHaXi, 0)
				END as CommissionRate,
				CASE
					WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN o.LiuSui * IFNULL(cd.RewardHaXiRoulette, 0) / 100
					ELSE o.LiuSui * IFNULL(cd.RewardHaXi, 0) / 100
				END as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.CreateTime as BetTime,
				o.RewardTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		UNION ALL
		(
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'真人' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardLive, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardLive, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_live o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		UNION ALL
		(
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'电子' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardDianZhi, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardDianZhi, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_dianzhi o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		UNION ALL
		(
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'体育' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardSport, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardSport, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_sport o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		UNION ALL
		(
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'棋牌' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardQiPai, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardQiPai, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_qipai o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		UNION ALL
		(
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				CASE
					WHEN o.GameId IN ('796', '797') THEN '低频彩'
					ELSE '彩票'
				END as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				CASE
					WHEN o.GameId IN ('796', '797') THEN IFNULL(cd.RewardLowLottery, 0)
					ELSE IFNULL(cd.RewardLottery, 0)
				END as CommissionRate,
				CASE
					WHEN o.GameId IN ('796', '797') THEN o.ValidBet * IFNULL(cd.RewardLowLottery, 0) / 100
					ELSE o.ValidBet * IFNULL(cd.RewardLottery, 0) / 100
				END as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_lottery o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		UNION ALL
		(
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'德州' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardTexas, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardTexas, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_texas o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		UNION ALL
		(
			SELECT
				IFNULL(o.Id, '') as OrderId,
				IFNULL(o.UserID, 0) as UserId,
				IFNULL(u.Account, '') as UserName,
				IFNULL(u.Address, '') as UserAddress,
				'小游戏' as GameType,
				'USDT' as Symbol,
				IFNULL(o.BetAmount,0) as BetAmount,
				IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
				IFNULL(o.WinAmount,0) as WinAmount,
				IFNULL(cd.RewardXiaoYouXi, 0) as CommissionRate,
				o.ValidBet * IFNULL(cd.RewardXiaoYouXi, 0) / 100 as CommissionAmount,
				cd.AgentLevel as AgentLevel,
				o.ThirdTime as BetTime,
				o.CreateTime as SettleTime,
				cd.StatDate as CommissionDateTime
			FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_quwei o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		)
		ORDER BY BetTime DESC
		LIMIT ? OFFSET ?`,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause)

	// 构建查询参数
	var queryParams []interface{}
	// 哈希/轮盘查询参数
	queryParams = append(queryParams, whereParams...)
	// 真人查询参数
	queryParams = append(queryParams, whereParams...)
	// 电子查询参数
	queryParams = append(queryParams, whereParams...)
	// 体育查询参数
	queryParams = append(queryParams, whereParams...)
	// 棋牌查询参数
	queryParams = append(queryParams, whereParams...)
	// 彩票查询参数
	queryParams = append(queryParams, whereParams...)
	// 德州查询参数
	queryParams = append(queryParams, whereParams...)
	// 小游戏查询参数
	queryParams = append(queryParams, whereParams...)
	// 分页参数
	queryParams = append(queryParams, limit, offset)

	// 执行查询
	rows, err := server.DbReport().GormDao().Raw(unionSQL, queryParams...).Rows()
	if err != nil {
		return nil, 0, GameOrderData{}, err
	}
	defer rows.Close()

	for rows.Next() {
		var data GameOrderData
		err := rows.Scan(
			&data.OrderId,
			&data.UserId,
			&data.UserName,
			&data.UserAddress,
			&data.GameType,
			&data.Symbol,
			&data.BetAmount,
			&data.ValidBetAmount,
			&data.WinAmount,
			&data.CommissionRate,
			&data.CommissionAmount,
			&data.AgentLevel,
			&data.BetTime,
			&data.SettleTime,
			&data.CommissionDateTime,
		)
		if err != nil {
			return nil, 0, GameOrderData{}, err
		}
		allResults = append(allResults, data)
	}

	// 查询总数 - 使用联合查询统计
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) as total FROM (
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			UNION ALL
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_live o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			UNION ALL
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_dianzhi o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			UNION ALL
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_sport o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			UNION ALL
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_qipai o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			UNION ALL
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_lottery o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			UNION ALL
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_texas o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
			UNION ALL
			SELECT 1 FROM x_agent_commission_user_date cd
			LEFT JOIN x_user u ON cd.UserId = u.UserId
			LEFT JOIN x_third_quwei o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
			%s
		) as combined`,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause)

	var countParams []interface{}
	// 添加每个子查询的参数
	for i := 0; i < 8; i++ {
		countParams = append(countParams, whereParams...)
	}

	var total int64
	err = server.DbReport().GormDao().Raw(countSQL, countParams...).Scan(&total).Error
	if err != nil {
		return nil, 0, GameOrderData{}, err
	}

	// 通过SQL查询计算汇总数据 - 使用DISTINCT确保不重复计算
	totalSQL := fmt.Sprintf(`
		SELECT
			IFNULL(SUM(BetAmount), 0) as BetAmount,
			IFNULL(SUM(ValidBetAmount), 0) as ValidBetAmount,
			IFNULL(SUM(WinAmount), 0) as WinAmount,
			IFNULL(SUM(CommissionAmount), 0) as CommissionAmount
		FROM (
			(
				SELECT
					o.Amount as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					o.RewardAmount as WinAmount,
					CASE
						WHEN o.GameId IN (13,106,116,126,136,206,303,313,323,333) THEN o.LiuSui * IFNULL(cd.RewardHaXiRoulette, 0) / 100
						ELSE o.LiuSui * IFNULL(cd.RewardHaXi, 0) / 100
					END as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_order o ON cd.UserId = o.UserId AND cd.StatDate <= o.CreateTime AND o.CreateTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
			UNION ALL
			(
				SELECT
					IFNULL(o.BetAmount,0) as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					IFNULL(o.WinAmount,0) as WinAmount,
					o.ValidBet * IFNULL(cd.RewardLive, 0) / 100 as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_third_live o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
			UNION ALL
			(
				SELECT
					IFNULL(o.BetAmount,0) as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					IFNULL(o.WinAmount,0) as WinAmount,
					o.ValidBet * IFNULL(cd.RewardDianZhi, 0) / 100 as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_third_dianzhi o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
			UNION ALL
			(
				SELECT
					IFNULL(o.BetAmount,0) as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					IFNULL(o.WinAmount,0) as WinAmount,
					o.ValidBet * IFNULL(cd.RewardSport, 0) / 100 as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_third_sport o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
			UNION ALL
			(
				SELECT
					IFNULL(o.BetAmount,0) as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					IFNULL(o.WinAmount,0) as WinAmount,
					o.ValidBet * IFNULL(cd.RewardQiPai, 0) / 100 as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_third_qipai o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
			UNION ALL
			(
				SELECT
					IFNULL(o.BetAmount,0) as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					IFNULL(o.WinAmount,0) as WinAmount,
					CASE
						WHEN o.GameId IN ('796', '797') THEN o.ValidBet * IFNULL(cd.RewardLowLottery, 0) / 100
						ELSE o.ValidBet * IFNULL(cd.RewardLottery, 0) / 100
					END as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_third_lottery o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
			UNION ALL
			(
				SELECT
					IFNULL(o.BetAmount,0) as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					IFNULL(o.WinAmount,0) as WinAmount,
					o.ValidBet * IFNULL(cd.RewardTexas, 0) / 100 as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_third_texas o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
			UNION ALL
			(
				SELECT
					IFNULL(o.BetAmount,0) as BetAmount,
					IFNULL(o.ValidBetAmount,0) as ValidBetAmount,
					IFNULL(o.WinAmount,0) as WinAmount,
					o.ValidBet * IFNULL(cd.RewardXiaoYouXi, 0) / 100 as CommissionAmount
				FROM x_agent_commission_user_date cd
				LEFT JOIN x_user u ON cd.UserId = u.UserId
				LEFT JOIN x_third_quwei o ON cd.UserId = o.UserID AND cd.StatDate <= o.ThirdTime AND o.ThirdTime < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				%s
			)
		) as combined`,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause,
		whereClause)

	var totalQueryParams []interface{}
	// 添加每个子查询的参数
	for i := 0; i < 8; i++ {
		totalQueryParams = append(totalQueryParams, whereParams...)
	}

	err = server.DbReport().GormDao().Raw(totalSQL, totalQueryParams...).Scan(&totalData).Error
	if err != nil {
		return nil, 0, GameOrderData{}, err
	}

	// 使用分别查询每个游戏类型的汇总，避免UNION ALL可能的重复问题
	correctedTotalData, err := c.calculateCorrectTotalData(whereClause, whereParams)
	if err != nil {
		log.Printf("计算修正汇总数据失败: %v", err)
	} else {
		log.Printf("=== 修正汇总查询结果 ===")
		log.Printf("投注金额: %.2f", *correctedTotalData.BetAmount)
		log.Printf("有效投注: %.2f", *correctedTotalData.ValidBetAmount)
		log.Printf("中奖金额: %.2f", *correctedTotalData.WinAmount)
		log.Printf("返佣金额: %.2f", *correctedTotalData.CommissionAmount)

		// 使用修正后的数据
		totalData = correctedTotalData
	}

	// 调试信息：打印汇总查询结果
	log.Printf("=== 汇总查询结果 ===")
	log.Printf("投注金额: %.2f", *totalData.BetAmount)
	log.Printf("有效投注: %.2f", *totalData.ValidBetAmount)
	log.Printf("中奖金额: %.2f", *totalData.WinAmount)
	log.Printf("返佣金额: %.2f", *totalData.CommissionAmount)
	log.Printf("明细记录数: %d", len(allResults))

	// 按游戏类型分组统计
	gameTypeStats := make(map[string]struct {
		Count            int
		BetAmount        float64
		ValidBetAmount   float64
		WinAmount        float64
		CommissionAmount float64
	})

	for _, item := range allResults {
		gameType := "未知"
		if item.GameType != nil {
			gameType = *item.GameType
		}

		stats := gameTypeStats[gameType]
		stats.Count++
		if item.BetAmount != nil {
			stats.BetAmount += *item.BetAmount
		}
		if item.ValidBetAmount != nil {
			stats.ValidBetAmount += *item.ValidBetAmount
		}
		if item.WinAmount != nil {
			stats.WinAmount += *item.WinAmount
		}
		if item.CommissionAmount != nil {
			stats.CommissionAmount += *item.CommissionAmount
		}
		gameTypeStats[gameType] = stats
	}

	log.Printf("=== 按游戏类型统计 ===")
	for gameType, stats := range gameTypeStats {
		log.Printf("%s: 记录数=%d, 投注=%.2f, 有效投注=%.2f, 中奖=%.2f, 返佣=%.2f",
			gameType, stats.Count, stats.BetAmount, stats.ValidBetAmount, stats.WinAmount, stats.CommissionAmount)
	}

	// 手动计算明细数据的汇总，用于对比
	var manualBetAmount, manualValidBetAmount, manualWinAmount, manualCommissionAmount float64
	for _, item := range allResults {
		if item.BetAmount != nil {
			manualBetAmount += *item.BetAmount
		}
		if item.ValidBetAmount != nil {
			manualValidBetAmount += *item.ValidBetAmount
		}
		if item.WinAmount != nil {
			manualWinAmount += *item.WinAmount
		}
		if item.CommissionAmount != nil {
			manualCommissionAmount += *item.CommissionAmount
		}
	}
	log.Printf("=== 手动计算结果 ===")
	log.Printf("投注金额: %.2f", manualBetAmount)
	log.Printf("有效投注: %.2f", manualValidBetAmount)
	log.Printf("中奖金额: %.2f", manualWinAmount)
	log.Printf("返佣金额: %.2f", manualCommissionAmount)
	log.Printf("==================")

	return allResults, total, totalData, nil
}

// calculateCorrectTotalData 分别计算每个游戏类型的汇总数据，避免UNION ALL重复问题
func (c *ThreeLevelAgentController) calculateCorrectTotalData(whereClause string, whereParams []interface{}) (GameOrderData, error) {
	var totalData GameOrderData

	// 分别查询每个游戏类型的汇总
	gameTypes := []struct {
		name      string
		table     string
		timeField string
	}{
		{"哈希/轮盘", "x_order", "CreateTime"},
		{"真人", "x_third_live", "ThirdTime"},
		{"电子", "x_third_dianzhi", "ThirdTime"},
		{"体育", "x_third_sport", "ThirdTime"},
		{"棋牌", "x_third_qipai", "ThirdTime"},
		{"彩票", "x_third_lottery", "ThirdTime"},
		{"德州", "x_third_texas", "ThirdTime"},
		{"小游戏", "x_third_quwei", "ThirdTime"},
	}

	var totalBetAmount, totalValidBetAmount, totalWinAmount, totalCommissionAmount float64

	for _, gameType := range gameTypes {
		var gameData struct {
			BetAmount        float64
			ValidBetAmount   float64
			WinAmount        float64
			CommissionAmount float64
		}

		// 构建每个游戏类型的查询SQL
		sql := fmt.Sprintf(`
			SELECT
				IFNULL(SUM(CASE WHEN o.GameType = '%s' THEN o.BetAmount ELSE 0 END), 0) as BetAmount,
				IFNULL(SUM(CASE WHEN o.GameType = '%s' THEN o.ValidBetAmount ELSE 0 END), 0) as ValidBetAmount,
				IFNULL(SUM(CASE WHEN o.GameType = '%s' THEN o.WinAmount ELSE 0 END), 0) as WinAmount,
				IFNULL(SUM(cd.CommissionAmount), 0) as CommissionAmount
			FROM x_agent_commission_user_date cd
			LEFT JOIN %s o ON cd.UserId = o.UserId
				AND cd.StatDate <= o.%s
				AND o.%s < DATE_ADD(cd.StatDate, INTERVAL 1 DAY)
				AND o.GameType = '%s'
			%s
		`, gameType.name, gameType.name, gameType.name, gameType.table, gameType.timeField, gameType.timeField, gameType.name, whereClause)

		err := server.DbReport().GormDao().Raw(sql, whereParams...).Scan(&gameData).Error
		if err != nil {
			log.Printf("查询游戏类型 %s 汇总数据失败: %v", gameType.name, err)
			continue
		}

		totalBetAmount += gameData.BetAmount
		totalValidBetAmount += gameData.ValidBetAmount
		totalWinAmount += gameData.WinAmount
		totalCommissionAmount += gameData.CommissionAmount

		log.Printf("游戏类型 %s: 投注=%.2f, 有效投注=%.2f, 中奖=%.2f, 返佣=%.2f",
			gameType.name, gameData.BetAmount, gameData.ValidBetAmount, gameData.WinAmount, gameData.CommissionAmount)
	}

	totalData.BetAmount = &totalBetAmount
	totalData.ValidBetAmount = &totalValidBetAmount
	totalData.WinAmount = &totalWinAmount
	totalData.CommissionAmount = &totalCommissionAmount

	return totalData, nil
}

// exportGameOrderExcel 导出游戏注单Excel
func (c *ThreeLevelAgentController) exportGameOrderExcel(ctx *abugo.AbuHttpContent, data []GameOrderData, totalData GameOrderData) {
	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Println(err)
		}
	}()

	// 设置工作表名称
	sheetName := "游戏注单数据"
	f.SetSheetName("Sheet1", sheetName)

	// 设置表头
	headers := []string{
		"注单号", "用户ID", "用户名", "游戏类型", "币种",
		"投注金额", "有效投注", "中奖金额", "返佣比例(%)", "返佣金额",
		"代理等级", "下注时间", "结算时间", "返佣计算时间",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for i, item := range data {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), item.OrderId)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.UserId)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.UserName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.GameType)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Symbol)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.BetAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.ValidBetAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.WinAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.CommissionRate)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.CommissionAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), item.AgentLevel)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.BetTime)
		f.SetCellValue(sheetName, fmt.Sprintf("M%d", row), item.SettleTime)
		f.SetCellValue(sheetName, fmt.Sprintf("N%d", row), item.CommissionDateTime)
	}

	// 添加汇总行
	if len(data) > 0 {
		totalRow := len(data) + 3
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", totalRow), "汇总")
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", totalRow), totalData.BetAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", totalRow), totalData.ValidBetAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", totalRow), totalData.WinAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", totalRow), totalData.CommissionAmount)
	}

	// 设置文件名
	fileName := fmt.Sprintf("游戏注单数据_%s.xlsx", time.Now().Format("20060102_150405"))

	// 保存Excel文件
	filePath := server.ExportDir() + "/" + fileName
	if err := f.SaveAs(filePath); err != nil {
		log.Println("保存Excel文件失败:", err)
		ctx.RespErrString(true, nil, "导出失败")
		return
	}

	// 返回文件下载链接
	ctx.Put("filename", "/exports/"+fileName)
	ctx.RespOK()
	server.WriteAdminLog("导出游戏注单数据", ctx, nil)
}
