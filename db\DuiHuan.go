package db

import (
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

type DuiHuan struct {
	Id                int     `gorm:"column:Id"`                //id
	State             int     `gorm:"column:State"`             //状态
	SellerId          int     `gorm:"column:SellerId"`          //运营商
	FromAddress       string  `gorm:"column:FromAddress"`       //转出地址
	ToAddress         string  `gorm:"column:ToAddress"`         //收款地址
	Symbol            string  `gorm:"column:Symbol"`            //币种
	Amount            float64 `gorm:"column:Amount"`            //金额
	TxId              string  `gorm:"column:TxId"`              //转账哈希
	Block             int     `gorm:"column:Block"`             //转账区块
	CreateTime        string  `gorm:"column:CreateTime"`        //创建时间
	BackAmount        float64 `gorm:"column:BackAmount"`        //返还金额
	BackOrder         string  `gorm:"column:BackOrder"`         //返还订单
	BackSymbol        string  `gorm:"column:BackSymbol"`        //返还币种
	BackTxId          string  `gorm:"column:BackTxId"`          //返还交易哈希
	TrxPrice          float64 `gorm:"column:TrxPrice"`          //汇率
	ExchangeRateFixed float64 `gorm:"column:ExchangeRateFixed"` //兑换汇率修正
	Memo              string  `gorm:"column:Memo"`              //
	GasFee            float64 `gorm:"column:GasFee"`
}

func (*DuiHuan) TableName() string {
	return "x_duihuan"
}

func DuiHuan_Page_Data(Page int, PageSize int, SellerId int, Id int, Address string, Symbol string, State int, StartTime int64, EndTime int64) (int, []DuiHuan) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := DuiHuan{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	//server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "Id", "=", Id, 0)
	server.Db().AddWhere(&sql, &params, "and", "FromAddress", "=", Address, "")
	server.Db().AddWhere(&sql, &params, "and", "Symbol", "=", Symbol, "")
	server.Db().AddWhere(&sql, &params, "and", "State", "=", State, 0)
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	if EndTime > 0 {
		EndTime += +86400000
	}
	server.Db().AddWhere(&sql, &params, "and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []DuiHuan{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []DuiHuan{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}
