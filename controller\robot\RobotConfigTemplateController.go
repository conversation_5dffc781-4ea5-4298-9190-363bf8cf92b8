package robot

import (
	"fmt"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/viper"
)

// list 获取TG接待机器人列表
func (c *Router) configList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page      int      `json:"page"`
		PageSize  int      `json:"page_size"`
		ChannelId int32    `json:"channel_id"`
		SellerId  int32    `json:"seller_id"`
		RobotType int32    `json:"robot_type"`
		BotName   []string `json:"bot_name"`
		IsEnable  int32    `json:"is_enable"`
	}

	reqData := RequestData{}
	var token *server.TokenData

	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData,
		"机器人管理", "接待机器人", "查", "查询TG接待机器人")

	if token == nil {
		return
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}

	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize
	type Result struct {
		model.XRobotConfig
		SellerName  string `json:"seller_name"`
		ChannelName string `json:"channel_name"`
	}
	var results []*Result
	dao := server.DaoxHashGame().XRobotConfig

	query := dao.WithContext(nil).Select(dao.ALL)

	if reqData.ChannelId != 0 {
		query.Where(dao.ChannelID.Eq(reqData.ChannelId))
	}

	if reqData.SellerId != 0 {
		query.Where(dao.SellerID.Eq(reqData.SellerId))
	}

	if reqData.RobotType != 0 {
		query.Where(dao.RobotType.Eq(reqData.RobotType))
	}

	if len(reqData.BotName) > 0 {
		query.Where(dao.Name.In(reqData.BotName...))
	}
	if reqData.IsEnable != 0 {
		query.Where(dao.IsEnable.Eq(reqData.IsEnable))
	}

	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	total, err := query.WithContext(nil).
		Select(dao.ALL, xSeller.SellerName, xChannel.ChannelName).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		LeftJoin(xChannel, xChannel.ChannelID.EqCol(dao.ChannelID)).
		Where(dao.IsDel.Eq(0), dao.RobotType.Eq(reqData.RobotType)).
		Order(dao.ID.Desc()).
		ScanByPage(&results, offset, limit)

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	ctx.Put("total", total)
	ctx.Put("data", results)
	ctx.RespOK()
}

// create 创建TG接待机器人
func (c *Router) configCreate(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := model.XRobotConfig{}
	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人", "增", "新增接待机器人")

	if token == nil {
		return
	}

	err := server.DaoxHashGame().XRobotConfig.WithContext(nil).Create(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	go restartRobot(reqData.RobotType)

	ctx.RespOK()
}

// update 更新TG接待机器人
func (c *Router) configUpdate(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := model.XRobotConfig{}
	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人", "改", "修改接待机器人")
	if token == nil {
		return
	}

	err := server.DaoxHashGame().XRobotConfig.WithContext(nil).
		Where(server.DaoxHashGame().XRobotConfig.ID.Eq(reqData.ID)).
		Save(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	go restartRobot(reqData.RobotType)

	ctx.RespOK()
}

// delete 删除TG接待机器人
func (c *Router) configDelete(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		ID        int64 `json:"id"`
		RobotType int32 `json:"robot_type"`
	}

	reqData := RequestData{}

	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人", "删", "删除接待机器人")

	if token == nil {
		return
	}

	// 软删除
	_, err := server.DaoxHashGame().XRobotConfig.WithContext(nil).
		Where(server.DaoxHashGame().XRobotConfig.ID.Eq(reqData.ID)).
		Update(server.DaoxHashGame().XRobotConfig.IsDel, 1)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	go restartRobot(reqData.RobotType)

	ctx.RespOK()
}

var (
	restartMutex  sync.Mutex
	restartTimers = make(map[int32]*time.Timer) // botType -> Timer
)

func restartRobot(botType int32) {
	subUrl := ""
	switch botType {
	case GuestBotType:
		subUrl = "/robot/restart"
	case ADBotType:
		subUrl = "/robot/restart/ad"
	case RedBagBotType:
		subUrl = "/robot/restart/redbag"
	case ForwardBotType:
		subUrl = "/robot/restart/forward"
	case GuessBotType:
		subUrl = "/robot/restart/guess"
	default:
		return
	}

	// 使用锁保护对 Timer 的访问
	restartMutex.Lock()
	defer restartMutex.Unlock()

	// 如果已有 timer，则重置它的时间
	if timer, exists := restartTimers[botType]; exists {
		if timer.Stop() {
			timer.Reset(5 * time.Minute)
			logs.Info("机器人类型 %d 的重启操作已重新计时", botType)
		} else {
			// Timer 已经触发，直接重新设置一个新的
			delete(restartTimers, botType)
			restartTimers[botType] = scheduleRestart(botType, subUrl)
			logs.Info("机器人类型 %d 的重启计时器重新创建（原 Timer 已触发）", botType)
		}
		return
	}

	// 首次设置 Timer
	restartTimers[botType] = scheduleRestart(botType, subUrl)
	logs.Info("将在 5 分钟后热重载机器人配置，botType=%d", botType)
}

func scheduleRestart(botType int32, subUrl string) *time.Timer {
	return time.AfterFunc(5*time.Minute, func() {
		client := resty.New()
		_, err := client.R().Get(fmt.Sprintf("%s%s", viper.GetString("newtgservie"), subUrl))

		// 删除 timer 映射
		restartMutex.Lock()
		delete(restartTimers, botType)
		restartMutex.Unlock()

		if err != nil {
			logs.Error("机器人重启请求失败 botType=%d：%v", botType, err)
		} else {
			logs.Info("机器人重启请求已发送成功，botType=%d", botType)
		}
	})
}

// list 答题 列表
func (c *Router) questionsList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page       int   `json:"page"`
		PageSize   int   `json:"page_size"`
		ChannelId  int32 `json:"channel_id"`
		SellerId   int32 `json:"seller_id"`
		QuestionID int64 `json:"question_id"`
	}

	reqData := RequestData{}
	var token *server.TokenData

	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData,
		"机器人管理", "接待机器人", "查", "查询TG接待机器人")

	if token == nil {
		return
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}

	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize
	type Result struct {
		model.XRobotRedbagQuestion
		SellerName  string `json:"seller_name"`
		ChannelName string `json:"channel_name"`
	}
	var results []*Result
	dao := server.DaoxHashGame().XRobotRedbagQuestion

	query := dao.WithContext(nil).Select(dao.ALL)

	if reqData.ChannelId != 0 {
		query.Where(dao.ChannelID.Eq(reqData.ChannelId))
	}

	if reqData.SellerId != 0 {
		query.Where(dao.SellerID.Eq(reqData.SellerId))
	}
	if reqData.QuestionID != 0 {
		query.Where(dao.ID.Eq(reqData.QuestionID))
	}

	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	total, err := query.WithContext(nil).
		Select(dao.ALL, xSeller.SellerName, xChannel.ChannelName).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		LeftJoin(xChannel, xChannel.ChannelID.EqCol(dao.ChannelID)).
		Order(dao.ID.Desc()).
		ScanByPage(&results, offset, limit)

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	ctx.Put("total", total)
	ctx.Put("data", results)
	ctx.RespOK()
}

func (c *Router) questionsCreate(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := model.XRobotRedbagQuestion{}
	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人", "增", "新增接待机器人")

	if token == nil {
		return
	}

	err := server.DaoxHashGame().XRobotRedbagQuestion.WithContext(nil).Create(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	ctx.RespOK()
}

func (c *Router) questionsUpdate(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := model.XRobotRedbagQuestion{}
	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人", "改", "修改接待机器人")
	if token == nil {
		return
	}

	err := server.DaoxHashGame().XRobotRedbagQuestion.WithContext(nil).
		Where(server.DaoxHashGame().XRobotRedbagQuestion.ID.Eq(reqData.ID)).
		Save(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	ctx.RespOK()
}

func (c *Router) questionsDelete(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		ID int64 `json:"id"`
	}

	reqData := RequestData{}

	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人", "删", "删除接待机器人")

	if token == nil {
		return
	}

	// 软删除
	_, err := server.DaoxHashGame().XRobotRedbagQuestion.WithContext(nil).
		Where(server.DaoxHashGame().XRobotRedbagQuestion.ID.Eq(reqData.ID)).
		Delete()
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	ctx.RespOK()
}
