package controller

import (
	"fmt"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/xuri/excelize/v2"
)

type PandaController struct {
}

func (c *PandaController) Init() {
	group := server.Http().NewGroup("/api/panda")
	{
		group.Post("/account_list", c.account_list)
		group.Post("/recharge_list", c.recharge_list)
		group.Post("/withdraw_list", c.withdraw_list)
		group.Post("/withdraw_audit", c.withdraw_audit)
		group.Post("/win_list", c.win_list)
		group.Post("/agent_list", c.agent_list)
		group.Post("/report", c.report)
		group.Post("/agent_report", c.agent_report)
		group.Post("/add_amount", c.add_amount)
	}
}

func (c *PandaController) account_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Address   string
		Export    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "账号管理", "查", "查看量化工具账号管理")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	where.Add("and", "Address", "=", reqdata.Address, "")
	total, data := server.Db().Table("x_panda").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("data", *data)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"Id", "用户地址", "Trx余额", "Trx已用余额", "Usdt余额", "Usdt已用余额", "trx分红", "usdt分红", "可提现trx", "可提现usdt", "代理地址", "是否代理", "下级人数", "成为代理时间", "注册时间"})
		for i, d := range *data {
			isagent := "否"
			if d["IsAgent"].(int64) == 1 {
				isagent = "是"
			}
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["Address"],
				d["AmountTrx"],
				d["UsedAmountTrx"],
				d["AmountUsdt"],
				d["UsedAmountUsdt"],
				d["RewardTrx"],
				d["RewardUsdt"],
				d["RewardValidTrx"],
				d["RewardValidUsdt"],
				d["Agent"],
				isagent,
				d["ChildCount"],
				d["AgentTime"],
				d["CreateTime"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出熊猫用户", ctx, reqdata)
	}
	//server.WriteAdminLog("查看量化工具账号管理", ctx, reqdata)
}

func (c *PandaController) recharge_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Address   string
		Symbol    string
		Export    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "充值列表", "查", "查看量化工具充值列表")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	total, data := server.Db().Table("x_panda_recharge").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("data", *data)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"Id", "用户地址", "币种", "充值金额", "到账金额", "充值交易哈希", "充值时间"})
		for i, d := range *data {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["Address"],
				d["Symbol"],
				d["Amount"],
				d["RealAmount"],
				d["TxId"],
				d["CreateTime"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出熊猫充值", ctx, reqdata)
	}
	//server.WriteAdminLog("查看量化工具充值列表", ctx, reqdata)
}

func (c *PandaController) withdraw_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Address   string
		Symbol    string
		State     int
		Export    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "提现列表", "查", "查看量化工具提现列表")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<=", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	where.Add("and", "State", "=", reqdata.State, 0)
	total, data := server.Db().Table("x_panda_withdraw").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("data", *data)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"Id", "用户地址", "币种", "金额", "状态", "审核时间", "审核账号", "审核备注", "提现时间"})
		for i, d := range *data {
			State := "待处理"
			if d["State"].(int64) == 3 {
				State = "通过"
			}
			if d["State"].(int64) == 2 {
				State = "拒绝"
			}
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["Address"],
				d["Symbol"],
				d["Amount"],
				State,
				d["AuditTime"],
				d["AuditAccount"],
				d["AuditMemo"],
				d["CreateTime"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出熊猫提现", ctx, reqdata)
	}

	//server.WriteAdminLog("查看量化工具提现列表", ctx, reqdata)
}

func (c *PandaController) withdraw_audit(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Ids       []int `validate:"required"`
		State     int   `validate:"required,min=2,max=3"`
		AuditMemo string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "提现列表", "改", "查看量化工具提现列表")
	if token == nil {
		return
	}

	for _, id := range reqdata.Ids {
		err := server.Db().QueryNoResult(fmt.Sprintf("update x_panda_withdraw set State=%d, AuditAccount='%s', AuditMemo='%s', AuditTime=Now() where id=%d and State=1", reqdata.State, token.Account, reqdata.AuditMemo, id))
		if ctx.RespErr(err, &errcode) {
			return
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("审核量化工具提现列表", ctx, reqdata)
}

func (c *PandaController) win_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Address   string
		Symbol    string
		Export    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "结算数据", "查", "查看量化工具结算数据")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	total, data := server.Db().Table("x_panda_win").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("data", *data)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"Id", "用户地址", "币种", "盈利金额", "结算金额", "结算前余额", "结算后余额", "结算时间"})
		for i, d := range *data {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["Address"],
				d["Symbol"],
				d["Amount"],
				d["Fee"],
				d["BeforeAmount"],
				d["AfterAmount"],
				d["CreateTime"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出熊猫结算", ctx, reqdata)
	}
	//server.WriteAdminLog("查看量化工具结算数据", ctx, reqdata)
}

func (c *PandaController) agent_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Address   string
		Agent     string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "代理数据", "查", "查看量化工具代理数据")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}

	where := abugo.AbuDbWhere{}

	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "Agent", "=", reqdata.Address, "")
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")

	total, data := server.Db().Table("x_panda_agent_data").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()

}

func (c *PandaController) add_amount(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Address    string
		AmountTrx  float64
		AmountUsdt float64
		Memo       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "账号管理", "改", "修改量化余额")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.Db().Query("update x_panda set AmountTrx = AmountTrx + ? ,AmountUsdt = AmountUsdt + ? where Address = ?", []interface{}{reqdata.AmountTrx, reqdata.AmountUsdt, reqdata.Address})
	ctx.RespOK()
}

func (c *PandaController) report(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Address   string
		Symbol    string
		IsFirst   int // 1首次,2非首次
		Agent     string
		Export    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "代理数据", "查", "熊猫报表查询")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	if reqdata.StartTime == 0 {
		reqdata.StartTime = abugo.LocalDateToTimeStamp("2023-01-01") * 1000
	}
	if reqdata.EndTime == 0 {
		reqdata.EndTime = abugo.LocalDateToTimeStamp("2030-01-01") * 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Agent", "=", reqdata.Agent, "")
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "LastLoginTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	where.Add("and", "LastLoginTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	if reqdata.IsFirst == 1 {
		if reqdata.Symbol == "" {
			where.Add("and", "FirstBetTime", ">", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		}
		if reqdata.Symbol == "trx" {
			where.Add("and", "FirstBetTimeTrx", ">", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTimeTrx", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		}
		if reqdata.Symbol == "usdt" {
			where.Add("and", "FirstBetTimeUsdt", ">", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTimeUsdt", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		}
	}
	if reqdata.IsFirst == 2 {
		if reqdata.Symbol == "" {
			where.Add("and", "(FirstBetTime", "<", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("or", "FirstBetTime", ">", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			where.End(")")
		}
		if reqdata.Symbol == "trx" {
			where.Add("and", "(FirstBetTimeTrx", "<", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("or", "FirstBetTimeTrx", ">", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			where.End(")")
		}
		if reqdata.Symbol == "usdt" {
			where.Add("and", "(FirstBetTimeUsdt", "<", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTimeUsdt", ">", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			where.End(")")
		}
	}
	sel := "Address,AmountTrx,UsedAmountTrx,AmountUsdt,UsedAmountUsdt,GiftTrx,GiftUsdt,FirstBetTime,FirstBetTimeTrx,FirstBetTimeUsdt,RechargeTrx,RechargeUsdt,AgentTrx,AgentUsdt"
	total, users := server.Db().Table("x_panda").Select(sel).Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	for i := 0; i < len(*users); i++ {
		address := (*users)[i]["Address"].(string)

		sql := `SELECT COUNT(Id) AS BetCount FROM x_panda_order WHERE Address = ? AND Symbol = ? and CreateTime >= ? and CreateTime < ? and TxId is not null`
		d1, _ := server.Db().Query(sql, []interface{}{address, reqdata.Symbol, abugo.TimeStampToLocalDate(reqdata.StartTime), abugo.TimeStampToLocalDate(reqdata.EndTime)})
		(*users)[i]["BetCount"] = (*d1)[0]["BetCount"]

		sql = `SELECT COUNT(Id) AS WinCount FROM x_panda_order WHERE Address = ? AND Symbol = ? and IsWin = 1 and CreateTime >= ? and CreateTime < ?  and TxId is not null`
		d2, _ := server.Db().Query(sql, []interface{}{address, reqdata.Symbol, abugo.TimeStampToLocalDate(reqdata.StartTime), abugo.TimeStampToLocalDate(reqdata.EndTime)})
		(*users)[i]["WinCount"] = (*d2)[0]["WinCount"]

		(*users)[i]["IsFirst"] = reqdata.IsFirst

		if reqdata.IsFirst == 0 {
			fd := ""
			if reqdata.Symbol == "" {
				fd = (*users)[i]["FirstBetTime"].(string)

			}
			if reqdata.Symbol == "trx" {
				fd = (*users)[i]["FirstBetTimeTrx"].(string)
			}
			if reqdata.Symbol == "usdt" {
				fd = (*users)[i]["FirstBetTimeUsdt"].(string)
			}
			sfd := abugo.LocalTimeToTimeStamp(fd)
			if reqdata.StartTime < sfd && sfd < reqdata.EndTime {
				(*users)[i]["IsFirst"] = 1
			} else {
				(*users)[i]["IsFirst"] = 2
			}
		}
		delete((*users)[i], "FirstBetTime")
		delete((*users)[i], "FirstBetTimeTrx")
		delete((*users)[i], "FirstBetTimeUsdt")
		sql = `SELECT IFNULL(SUM(Amount),0) AS BetAmount,IFNULL(SUM(RewardAmount),0) AS RewardAmount FROM x_order WHERE FromAddress = ? AND Symbol = ? and GameId = 2 and CreateTime >= ? and CreateTime < ?`
		d3, _ := server.Db().Query(sql, []interface{}{address, reqdata.Symbol, abugo.TimeStampToLocalDate(reqdata.StartTime), abugo.TimeStampToLocalDate(reqdata.EndTime)})
		(*users)[i]["BetAmount"] = (*d3)[0]["BetAmount"]
		(*users)[i]["RewardAmount"] = (*d3)[0]["RewardAmount"]

	}
	if reqdata.Export != 1 {
		ctx.Put("data", users)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"Id", "用户地址", "投注次数", "盈利次数", "亏损次数", "胜率", "投注金额", "返奖金额", "盈亏", "充值金额", "账户余额", "已用余额", "优惠金额", "软件收益", "首次参与"})
		for i, d := range *users {
			isfirst := "否"
			if d["IsFirst"].(int) == 1 {
				isfirst = "是"
			}
			BetCount := float64(d["BetCount"].(int64))
			WinCount := float64(d["WinCount"].(int64))
			WinRate := 0.0
			if BetCount > 0 {
				WinRate = WinCount / BetCount
			}
			recharge := 0.0
			amount := 0.0
			usedamount := 0.0
			gift := 0.0
			if reqdata.Symbol == "trx" {
				recharge = d["RechargeTrx"].(float64)
				amount = d["AmountTrx"].(float64)
				usedamount = d["UsedAmountTrx"].(float64)
				gift = d["GiftTrx"].(float64)
			} else {
				recharge = d["RechargeUsdt"].(float64)
				amount = d["AmountUsdt"].(float64)
				usedamount = d["UsedAmountUsdt"].(float64)
				gift = d["GiftUsdt"].(float64)
			}
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["Address"],
				BetCount,
				WinCount,
				BetCount - WinCount,
				WinRate,
				d["BetAmount"],
				d["RewardAmount"],
				d["RewardAmount"].(float64) - d["BetAmount"].(float64),
				recharge,
				amount,
				usedamount,
				gift,
				recharge,
				isfirst,
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出熊猫报表", ctx, reqdata)
	}
}

func (c *PandaController) agent_report(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Address   string
		Symbol    string
		IsFirst   int // 1首次,2非首次
		Agent     string
		Export    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "量化工具", "代理数据", "查", "熊猫代理报表查询")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	if reqdata.StartTime == 0 {
		reqdata.StartTime = abugo.LocalDateToTimeStamp("2023-01-01") * 1000
	}
	if reqdata.EndTime == 0 {
		reqdata.EndTime = abugo.LocalDateToTimeStamp("2030-01-01") * 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Agent", "=", reqdata.Agent, "")
	if reqdata.Agent == "" {
		where.Add("and", "IsAgent", "=", 1, nil)
	}
	where.Add("and", "Address", "=", reqdata.Address, "")
	// where.Add("and", "LastLoginTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	// where.Add("and", "LastLoginTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	if reqdata.IsFirst == 1 {
		if reqdata.Symbol == "" {
			where.Add("and", "FirstBetTime", ">", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		}
		if reqdata.Symbol == "trx" {
			where.Add("and", "FirstBetTimeTrx", ">", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTimeTrx", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		}
		if reqdata.Symbol == "usdt" {
			where.Add("and", "FirstBetTimeUsdt", ">", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTimeUsdt", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		}
	}
	if reqdata.IsFirst == 2 {
		if reqdata.Symbol == "" {
			where.Add("and", "(FirstBetTime", "<", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("or", "FirstBetTime", ">", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			where.End(")")
		}
		if reqdata.Symbol == "trx" {
			where.Add("and", "(FirstBetTimeTrx", "<", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("or", "FirstBetTimeTrx", ">", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			where.End(")")
		}
		if reqdata.Symbol == "usdt" {
			where.Add("and", "(FirstBetTimeUsdt", "<", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where.Add("and", "FirstBetTimeUsdt", ">", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			where.End(")")
		}
	}
	sel := "Address,ChildCount,AmountTrx,UsedAmountTrx,AmountUsdt,UsedAmountUsdt,GiftTrx,GiftUsdt,FirstBetTime,FirstBetTimeTrx,FirstBetTimeUsdt,RechargeTrx,RechargeUsdt,RewardTrx,RewardUsdt,AgentTrx,AgentUsdt"
	total, users := server.Db().Table("x_panda").Select(sel).Where(where).OrderBy("ChildCount desc").PageData(reqdata.Page, reqdata.PageSize)
	for i := 0; i < len(*users); i++ {
		address := (*users)[i]["Address"].(string)

		sql := `SELECT COUNT(Id) AS BetCount FROM x_panda_order WHERE Address = ? AND Symbol = ? and CreateTime >= ? and CreateTime < ?`
		d1, _ := server.Db().Query(sql, []interface{}{address, reqdata.Symbol, abugo.TimeStampToLocalDate(reqdata.StartTime), abugo.TimeStampToLocalDate(reqdata.EndTime)})
		(*users)[i]["BetCount"] = (*d1)[0]["BetCount"]

		sql = `SELECT COUNT(Id) AS WinCount FROM x_panda_order WHERE Address = ? AND Symbol = ? and IsWin = 1 and CreateTime >= ? and CreateTime < ?`
		d2, _ := server.Db().Query(sql, []interface{}{address, reqdata.Symbol, abugo.TimeStampToLocalDate(reqdata.StartTime), abugo.TimeStampToLocalDate(reqdata.EndTime)})
		(*users)[i]["WinCount"] = (*d2)[0]["WinCount"]

		(*users)[i]["IsFirst"] = reqdata.IsFirst

		if reqdata.IsFirst == 0 {
			fd := ""
			if reqdata.Symbol == "" {
				fd = (*users)[i]["FirstBetTime"].(string)
			}
			if reqdata.Symbol == "trx" {
				fd = (*users)[i]["FirstBetTimeTrx"].(string)
			}
			if reqdata.Symbol == "usdt" {
				fd = (*users)[i]["FirstBetTimeUsdt"].(string)
			}

			sfd := abugo.LocalTimeToTimeStamp(fd) * 1000

			if reqdata.StartTime < sfd && sfd < reqdata.EndTime {
				(*users)[i]["IsFirst"] = 1
			} else {
				(*users)[i]["IsFirst"] = 2
			}
		}
		delete((*users)[i], "FirstBetTime")
		delete((*users)[i], "FirstBetTimeTrx")
		delete((*users)[i], "FirstBetTimeUsdt")
		sql = `SELECT IFNULL(SUM(Amount),0) AS BetAmount,IFNULL(SUM(RewardAmount),0) AS RewardAmount FROM x_order WHERE FromAddress = ? AND Symbol = ? and GameId = 2 and CreateTime >= ? and CreateTime < ?`
		d3, _ := server.Db().Query(sql, []interface{}{address, reqdata.Symbol, abugo.TimeStampToLocalDate(reqdata.StartTime), abugo.TimeStampToLocalDate(reqdata.EndTime)})
		(*users)[i]["BetAmount"] = (*d3)[0]["BetAmount"]
		(*users)[i]["RewardAmount"] = (*d3)[0]["RewardAmount"]
	}
	if reqdata.Export != 1 {
		ctx.Put("data", users)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"用户地址", "下级人数", "投注金额", "返奖金额", "盈亏", "充值金额", "账户余额", "已用金额", "优惠金额", "代理收益", "软件收益", "首次参与"})
		for i, d := range *users {
			rechargeamount := 0.0
			amount := 0.0
			usedamount := 0.0
			gift := 0.0
			isfirst := "否"
			reward := 0.0
			if reqdata.Symbol == "trx" {
				rechargeamount = d["RechargeTrx"].(float64)
				amount = d["AmountTrx"].(float64)
				usedamount = d["UsedAmountTrx"].(float64)
				gift = d["GiftTrx"].(float64)
				reward = d["RewardTrx"].(float64)
			} else {
				rechargeamount = d["RechargeUsdt"].(float64)
				amount = d["AmountUsdt"].(float64)
				usedamount = d["UsedAmountUsdt"].(float64)
				gift = d["GiftUsdt"].(float64)
				reward = d["RewardUsdt"].(float64)
			}
			if d["IsFirst"].(int) == 1 {
				isfirst = "是"
			}
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Address"],
				d["ChildCount"],
				d["BetAmount"],
				d["RewardAmount"],
				d["RewardAmount"].(float64) - d["BetAmount"].(float64),
				rechargeamount,
				amount,
				usedamount,
				gift,
				reward,
				rechargeamount,
				isfirst,
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出熊猫代理", ctx, reqdata)
	}
}
