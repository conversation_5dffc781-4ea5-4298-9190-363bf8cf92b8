package controller

import (
	"context"
	"errors"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"gorm.io/gorm"
)

type ShuangZhuangController struct {
}

func (c *ShuangZhuangController) Init() {
	group := server.Http().NewGroup("/api/shuangzhuang")
	{
		group.Post("/banker_config_list", c.banker_config_list)
		group.Post("/banker_config_add", c.banker_config_add)
		group.Post("/banker_config_update", c.banker_config_update)
	}
}

func (c *ShuangZhuangController) banker_config_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		ChannelId []int32
		Status    int32
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "上庄管理", "上庄配置管理", "查", "查看上庄配置管理列表")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	bankerConfigTb := server.DaoxHashGame().XTbBankerConfig
	bankerConfigDb := server.DaoxHashGame().XTbBankerConfig.WithContext(context.Background())
	if reqdata.SellerId > 0 {
		bankerConfigDb = bankerConfigDb.Where(bankerConfigTb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if reqdata.Status > 0 {
		bankerConfigDb = bankerConfigDb.Where(bankerConfigTb.Status.Eq(reqdata.Status))
	}
	if len(reqdata.ChannelId) > 0 {
		bankerConfigDb = bankerConfigDb.Where(bankerConfigTb.ChannelID.In(reqdata.ChannelId...))
	}
	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)
	result, count, err := bankerConfigDb.Order(bankerConfigTb.CreateTime.Desc()).FindByPage(offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("data", result)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *ShuangZhuangController) banker_config_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId        int     `validate:"required"`
		ChannelId       int32   `validate:"required"`
		BankerMinTime   int32   `validate:"required"`
		BankerMaxTime   int32   `validate:"required"`
		BankerMinAmount float64 `validate:"required"`
		BankerTgGroup   string
		Status          int32 `validate:"oneof=1 2"`
		Memo            string
		GoogleCode      string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "上庄管理", "上庄配置管理", "增", "添加上庄配置")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.BankerMinTime = reqdata.BankerMinTime * 60
	reqdata.BankerMaxTime = reqdata.BankerMaxTime * 60
	bankerConfigTb := server.DaoxHashGame().XTbBankerConfig
	bankerConfigDb := server.DaoxHashGame().XTbBankerConfig.WithContext(context.Background())
	bankerConfig, err := bankerConfigDb.Where(bankerConfigTb.ChannelID.Eq(reqdata.ChannelId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if bankerConfig != nil {
		ctx.RespErrString(true, &errcode, "渠道已存在")
		return
	}
	data := &model.XTbBankerConfig{
		SellerID:        int32(reqdata.SellerId),
		ChannelID:       reqdata.ChannelId,
		BankerMinTime:   reqdata.BankerMinTime,
		BankerMaxTime:   reqdata.BankerMaxTime,
		BankerMinAmount: reqdata.BankerMinAmount,
		BankerTgGroup:   reqdata.BankerTgGroup,
		Memo:            reqdata.Memo,
		Status:          reqdata.Status,
		Operator:        token.Account,
		OperUserID:      int32(token.UserId),
	}
	err = bankerConfigDb.Create(data)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.RespOK()
}

func (c *ShuangZhuangController) banker_config_update(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId        int     `validate:"required"`
		ChannelId       int32   `validate:"required"`
		BankerMinTime   int32   `validate:"required"`
		BankerMaxTime   int32   `validate:"required"`
		BankerMinAmount float64 `validate:"required"`
		BankerTgGroup   string
		Status          int32 `validate:"oneof=1 2"`
		Memo            string
		GoogleCode      string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "上庄管理", "上庄配置管理", "改", "修改上庄配置")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.BankerMinTime = reqdata.BankerMinTime * 60
	reqdata.BankerMaxTime = reqdata.BankerMaxTime * 60
	bankerConfigTb := server.DaoxHashGame().XTbBankerConfig
	bankerConfigDb := server.DaoxHashGame().XTbBankerConfig.WithContext(context.Background())
	data := make(map[string]any)
	data["BankerMinTime"] = reqdata.BankerMinTime
	data["BankerMaxTime"] = reqdata.BankerMaxTime
	data["BankerMinAmount"] = reqdata.BankerMinAmount
	data["BankerTgGroup"] = reqdata.BankerTgGroup
	data["Memo"] = reqdata.Memo
	data["Status"] = reqdata.Status
	data["Operator"] = token.Account
	data["OperUserID"] = int32(token.UserId)
	_, err := bankerConfigDb.Where(bankerConfigTb.SellerID.Eq(int32(reqdata.SellerId))).
		Where(bankerConfigTb.ChannelID.Eq(reqdata.ChannelId)).Updates(data)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.RespOK()
}
