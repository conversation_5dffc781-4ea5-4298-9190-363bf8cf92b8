// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentCommissionLevelDefine(db *gorm.DB, opts ...gen.DOOption) xAgentCommissionLevelDefine {
	_xAgentCommissionLevelDefine := xAgentCommissionLevelDefine{}

	_xAgentCommissionLevelDefine.xAgentCommissionLevelDefineDo.UseDB(db, opts...)
	_xAgentCommissionLevelDefine.xAgentCommissionLevelDefineDo.UseModel(&model.XAgentCommissionLevelDefine{})

	tableName := _xAgentCommissionLevelDefine.xAgentCommissionLevelDefineDo.TableName()
	_xAgentCommissionLevelDefine.ALL = field.NewAsterisk(tableName)
	_xAgentCommissionLevelDefine.SchemeID = field.NewInt32(tableName, "SchemeId")
	_xAgentCommissionLevelDefine.TeamLevel = field.NewInt32(tableName, "TeamLevel")
	_xAgentCommissionLevelDefine.AgentLevel = field.NewInt32(tableName, "AgentLevel")
	_xAgentCommissionLevelDefine.RewardHaXi = field.NewFloat64(tableName, "RewardHaXi")
	_xAgentCommissionLevelDefine.RewardHaXiRoulette = field.NewFloat64(tableName, "RewardHaXiRoulette")
	_xAgentCommissionLevelDefine.RewardLottery = field.NewFloat64(tableName, "RewardLottery")
	_xAgentCommissionLevelDefine.RewardLowLottery = field.NewFloat64(tableName, "RewardLowLottery")
	_xAgentCommissionLevelDefine.RewardQiPai = field.NewFloat64(tableName, "RewardQiPai")
	_xAgentCommissionLevelDefine.RewardDianZhi = field.NewFloat64(tableName, "RewardDianZhi")
	_xAgentCommissionLevelDefine.RewardXiaoYouXi = field.NewFloat64(tableName, "RewardXiaoYouXi")
	_xAgentCommissionLevelDefine.RewardLive = field.NewFloat64(tableName, "RewardLive")
	_xAgentCommissionLevelDefine.RewardSport = field.NewFloat64(tableName, "RewardSport")
	_xAgentCommissionLevelDefine.RewardTexas = field.NewFloat64(tableName, "RewardTexas")
	_xAgentCommissionLevelDefine.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentCommissionLevelDefine.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentCommissionLevelDefine.fillFieldMap()

	return _xAgentCommissionLevelDefine
}

// xAgentCommissionLevelDefine 三级返佣等级定义
type xAgentCommissionLevelDefine struct {
	xAgentCommissionLevelDefineDo xAgentCommissionLevelDefineDo

	ALL                field.Asterisk
	SchemeID           field.Int32   // 方案Id
	TeamLevel          field.Int32   // 团队等级
	AgentLevel         field.Int32   // 代理等级
	RewardHaXi         field.Float64 // 哈希返佣(百分比)
	RewardHaXiRoulette field.Float64 // 哈希轮盘返佣(百分比)
	RewardLottery      field.Float64 // 彩票返佣(百分比)
	RewardLowLottery   field.Float64 // 低频彩返佣(百分比)
	RewardQiPai        field.Float64 // 棋牌返佣(百分比)
	RewardDianZhi      field.Float64 // 电子返佣(百分比)
	RewardXiaoYouXi    field.Float64 // 小游戏返佣(百分比)
	RewardLive         field.Float64 // 真人返佣(百分比)
	RewardSport        field.Float64 // 体育返佣(百分比)
	RewardTexas        field.Float64 // 德州返佣(百分比)
	CreateTime         field.Time    // 创建时间
	UpdateTime         field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentCommissionLevelDefine) Table(newTableName string) *xAgentCommissionLevelDefine {
	x.xAgentCommissionLevelDefineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentCommissionLevelDefine) As(alias string) *xAgentCommissionLevelDefine {
	x.xAgentCommissionLevelDefineDo.DO = *(x.xAgentCommissionLevelDefineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentCommissionLevelDefine) updateTableName(table string) *xAgentCommissionLevelDefine {
	x.ALL = field.NewAsterisk(table)
	x.SchemeID = field.NewInt32(table, "SchemeId")
	x.TeamLevel = field.NewInt32(table, "TeamLevel")
	x.AgentLevel = field.NewInt32(table, "AgentLevel")
	x.RewardHaXi = field.NewFloat64(table, "RewardHaXi")
	x.RewardHaXiRoulette = field.NewFloat64(table, "RewardHaXiRoulette")
	x.RewardLottery = field.NewFloat64(table, "RewardLottery")
	x.RewardLowLottery = field.NewFloat64(table, "RewardLowLottery")
	x.RewardQiPai = field.NewFloat64(table, "RewardQiPai")
	x.RewardDianZhi = field.NewFloat64(table, "RewardDianZhi")
	x.RewardXiaoYouXi = field.NewFloat64(table, "RewardXiaoYouXi")
	x.RewardLive = field.NewFloat64(table, "RewardLive")
	x.RewardSport = field.NewFloat64(table, "RewardSport")
	x.RewardTexas = field.NewFloat64(table, "RewardTexas")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentCommissionLevelDefine) WithContext(ctx context.Context) *xAgentCommissionLevelDefineDo {
	return x.xAgentCommissionLevelDefineDo.WithContext(ctx)
}

func (x xAgentCommissionLevelDefine) TableName() string {
	return x.xAgentCommissionLevelDefineDo.TableName()
}

func (x xAgentCommissionLevelDefine) Alias() string { return x.xAgentCommissionLevelDefineDo.Alias() }

func (x xAgentCommissionLevelDefine) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentCommissionLevelDefineDo.Columns(cols...)
}

func (x *xAgentCommissionLevelDefine) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentCommissionLevelDefine) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 15)
	x.fieldMap["SchemeId"] = x.SchemeID
	x.fieldMap["TeamLevel"] = x.TeamLevel
	x.fieldMap["AgentLevel"] = x.AgentLevel
	x.fieldMap["RewardHaXi"] = x.RewardHaXi
	x.fieldMap["RewardHaXiRoulette"] = x.RewardHaXiRoulette
	x.fieldMap["RewardLottery"] = x.RewardLottery
	x.fieldMap["RewardLowLottery"] = x.RewardLowLottery
	x.fieldMap["RewardQiPai"] = x.RewardQiPai
	x.fieldMap["RewardDianZhi"] = x.RewardDianZhi
	x.fieldMap["RewardXiaoYouXi"] = x.RewardXiaoYouXi
	x.fieldMap["RewardLive"] = x.RewardLive
	x.fieldMap["RewardSport"] = x.RewardSport
	x.fieldMap["RewardTexas"] = x.RewardTexas
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentCommissionLevelDefine) clone(db *gorm.DB) xAgentCommissionLevelDefine {
	x.xAgentCommissionLevelDefineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentCommissionLevelDefine) replaceDB(db *gorm.DB) xAgentCommissionLevelDefine {
	x.xAgentCommissionLevelDefineDo.ReplaceDB(db)
	return x
}

type xAgentCommissionLevelDefineDo struct{ gen.DO }

func (x xAgentCommissionLevelDefineDo) Debug() *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentCommissionLevelDefineDo) WithContext(ctx context.Context) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentCommissionLevelDefineDo) ReadDB() *xAgentCommissionLevelDefineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentCommissionLevelDefineDo) WriteDB() *xAgentCommissionLevelDefineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentCommissionLevelDefineDo) Session(config *gorm.Session) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentCommissionLevelDefineDo) Clauses(conds ...clause.Expression) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentCommissionLevelDefineDo) Returning(value interface{}, columns ...string) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentCommissionLevelDefineDo) Not(conds ...gen.Condition) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentCommissionLevelDefineDo) Or(conds ...gen.Condition) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentCommissionLevelDefineDo) Select(conds ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentCommissionLevelDefineDo) Where(conds ...gen.Condition) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentCommissionLevelDefineDo) Order(conds ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentCommissionLevelDefineDo) Distinct(cols ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentCommissionLevelDefineDo) Omit(cols ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentCommissionLevelDefineDo) Join(table schema.Tabler, on ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentCommissionLevelDefineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentCommissionLevelDefineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentCommissionLevelDefineDo) Group(cols ...field.Expr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentCommissionLevelDefineDo) Having(conds ...gen.Condition) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentCommissionLevelDefineDo) Limit(limit int) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentCommissionLevelDefineDo) Offset(offset int) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentCommissionLevelDefineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentCommissionLevelDefineDo) Unscoped() *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentCommissionLevelDefineDo) Create(values ...*model.XAgentCommissionLevelDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentCommissionLevelDefineDo) CreateInBatches(values []*model.XAgentCommissionLevelDefine, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentCommissionLevelDefineDo) Save(values ...*model.XAgentCommissionLevelDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentCommissionLevelDefineDo) First() (*model.XAgentCommissionLevelDefine, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionLevelDefine), nil
	}
}

func (x xAgentCommissionLevelDefineDo) Take() (*model.XAgentCommissionLevelDefine, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionLevelDefine), nil
	}
}

func (x xAgentCommissionLevelDefineDo) Last() (*model.XAgentCommissionLevelDefine, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionLevelDefine), nil
	}
}

func (x xAgentCommissionLevelDefineDo) Find() ([]*model.XAgentCommissionLevelDefine, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentCommissionLevelDefine), err
}

func (x xAgentCommissionLevelDefineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentCommissionLevelDefine, err error) {
	buf := make([]*model.XAgentCommissionLevelDefine, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentCommissionLevelDefineDo) FindInBatches(result *[]*model.XAgentCommissionLevelDefine, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentCommissionLevelDefineDo) Attrs(attrs ...field.AssignExpr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentCommissionLevelDefineDo) Assign(attrs ...field.AssignExpr) *xAgentCommissionLevelDefineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentCommissionLevelDefineDo) Joins(fields ...field.RelationField) *xAgentCommissionLevelDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentCommissionLevelDefineDo) Preload(fields ...field.RelationField) *xAgentCommissionLevelDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentCommissionLevelDefineDo) FirstOrInit() (*model.XAgentCommissionLevelDefine, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionLevelDefine), nil
	}
}

func (x xAgentCommissionLevelDefineDo) FirstOrCreate() (*model.XAgentCommissionLevelDefine, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionLevelDefine), nil
	}
}

func (x xAgentCommissionLevelDefineDo) FindByPage(offset int, limit int) (result []*model.XAgentCommissionLevelDefine, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentCommissionLevelDefineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentCommissionLevelDefineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentCommissionLevelDefineDo) Delete(models ...*model.XAgentCommissionLevelDefine) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentCommissionLevelDefineDo) withDO(do gen.Dao) *xAgentCommissionLevelDefineDo {
	x.DO = *do.(*gen.DO)
	return x
}
