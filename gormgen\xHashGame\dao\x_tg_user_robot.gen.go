// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgUserRobot(db *gorm.DB, opts ...gen.DOOption) xTgUserRobot {
	_xTgUserRobot := xTgUserRobot{}

	_xTgUserRobot.xTgUserRobotDo.UseDB(db, opts...)
	_xTgUserRobot.xTgUserRobotDo.UseModel(&model.XTgUserRobot{})

	tableName := _xTgUserRobot.xTgUserRobotDo.TableName()
	_xTgUserRobot.ALL = field.NewAsterisk(tableName)
	_xTgUserRobot.ID = field.NewInt32(tableName, "Id")
	_xTgUserRobot.AccountID = field.NewInt32(tableName, "AccountId")
	_xTgUserRobot.TgUsername = field.NewString(tableName, "TgUsername")
	_xTgUserRobot.TgToken = field.NewString(tableName, "TgToken")
	_xTgUserRobot.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgUserRobot.SellerName = field.NewString(tableName, "SellerName")
	_xTgUserRobot.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTgUserRobot.ChannelName = field.NewString(tableName, "ChannelName")
	_xTgUserRobot.Remark = field.NewString(tableName, "Remark")
	_xTgUserRobot.Status = field.NewString(tableName, "Status")
	_xTgUserRobot.CreatedAt = field.NewTime(tableName, "CreatedAt")
	_xTgUserRobot.UpdatedAt = field.NewTime(tableName, "UpdatedAt")

	_xTgUserRobot.fillFieldMap()

	return _xTgUserRobot
}

// xTgUserRobot 会员集成TG登录机器人
type xTgUserRobot struct {
	xTgUserRobotDo xTgUserRobotDo

	ALL         field.Asterisk
	ID          field.Int32
	AccountID   field.Int32  // TG帐号id
	TgUsername  field.String // 机器人Username
	TgToken     field.String // 机器人token
	SellerID    field.Int32  // 运营商ID
	SellerName  field.String // 运营商名称
	ChannelID   field.Int32  // 渠道id
	ChannelName field.String // 渠道名称
	Remark      field.String // 备注信息
	Status      field.String // 状态
	CreatedAt   field.Time   // 创建时间
	UpdatedAt   field.Time   // 修改时间

	fieldMap map[string]field.Expr
}

func (x xTgUserRobot) Table(newTableName string) *xTgUserRobot {
	x.xTgUserRobotDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgUserRobot) As(alias string) *xTgUserRobot {
	x.xTgUserRobotDo.DO = *(x.xTgUserRobotDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgUserRobot) updateTableName(table string) *xTgUserRobot {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.AccountID = field.NewInt32(table, "AccountId")
	x.TgUsername = field.NewString(table, "TgUsername")
	x.TgToken = field.NewString(table, "TgToken")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.SellerName = field.NewString(table, "SellerName")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.ChannelName = field.NewString(table, "ChannelName")
	x.Remark = field.NewString(table, "Remark")
	x.Status = field.NewString(table, "Status")
	x.CreatedAt = field.NewTime(table, "CreatedAt")
	x.UpdatedAt = field.NewTime(table, "UpdatedAt")

	x.fillFieldMap()

	return x
}

func (x *xTgUserRobot) WithContext(ctx context.Context) *xTgUserRobotDo {
	return x.xTgUserRobotDo.WithContext(ctx)
}

func (x xTgUserRobot) TableName() string { return x.xTgUserRobotDo.TableName() }

func (x xTgUserRobot) Alias() string { return x.xTgUserRobotDo.Alias() }

func (x xTgUserRobot) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgUserRobotDo.Columns(cols...)
}

func (x *xTgUserRobot) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgUserRobot) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["AccountId"] = x.AccountID
	x.fieldMap["TgUsername"] = x.TgUsername
	x.fieldMap["TgToken"] = x.TgToken
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["SellerName"] = x.SellerName
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["ChannelName"] = x.ChannelName
	x.fieldMap["Remark"] = x.Remark
	x.fieldMap["Status"] = x.Status
	x.fieldMap["CreatedAt"] = x.CreatedAt
	x.fieldMap["UpdatedAt"] = x.UpdatedAt
}

func (x xTgUserRobot) clone(db *gorm.DB) xTgUserRobot {
	x.xTgUserRobotDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgUserRobot) replaceDB(db *gorm.DB) xTgUserRobot {
	x.xTgUserRobotDo.ReplaceDB(db)
	return x
}

type xTgUserRobotDo struct{ gen.DO }

func (x xTgUserRobotDo) Debug() *xTgUserRobotDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgUserRobotDo) WithContext(ctx context.Context) *xTgUserRobotDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgUserRobotDo) ReadDB() *xTgUserRobotDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgUserRobotDo) WriteDB() *xTgUserRobotDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgUserRobotDo) Session(config *gorm.Session) *xTgUserRobotDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgUserRobotDo) Clauses(conds ...clause.Expression) *xTgUserRobotDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgUserRobotDo) Returning(value interface{}, columns ...string) *xTgUserRobotDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgUserRobotDo) Not(conds ...gen.Condition) *xTgUserRobotDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgUserRobotDo) Or(conds ...gen.Condition) *xTgUserRobotDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgUserRobotDo) Select(conds ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgUserRobotDo) Where(conds ...gen.Condition) *xTgUserRobotDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgUserRobotDo) Order(conds ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgUserRobotDo) Distinct(cols ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgUserRobotDo) Omit(cols ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgUserRobotDo) Join(table schema.Tabler, on ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgUserRobotDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgUserRobotDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgUserRobotDo) Group(cols ...field.Expr) *xTgUserRobotDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgUserRobotDo) Having(conds ...gen.Condition) *xTgUserRobotDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgUserRobotDo) Limit(limit int) *xTgUserRobotDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgUserRobotDo) Offset(offset int) *xTgUserRobotDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgUserRobotDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgUserRobotDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgUserRobotDo) Unscoped() *xTgUserRobotDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgUserRobotDo) Create(values ...*model.XTgUserRobot) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgUserRobotDo) CreateInBatches(values []*model.XTgUserRobot, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgUserRobotDo) Save(values ...*model.XTgUserRobot) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgUserRobotDo) First() (*model.XTgUserRobot, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgUserRobot), nil
	}
}

func (x xTgUserRobotDo) Take() (*model.XTgUserRobot, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgUserRobot), nil
	}
}

func (x xTgUserRobotDo) Last() (*model.XTgUserRobot, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgUserRobot), nil
	}
}

func (x xTgUserRobotDo) Find() ([]*model.XTgUserRobot, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgUserRobot), err
}

func (x xTgUserRobotDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgUserRobot, err error) {
	buf := make([]*model.XTgUserRobot, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgUserRobotDo) FindInBatches(result *[]*model.XTgUserRobot, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgUserRobotDo) Attrs(attrs ...field.AssignExpr) *xTgUserRobotDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgUserRobotDo) Assign(attrs ...field.AssignExpr) *xTgUserRobotDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgUserRobotDo) Joins(fields ...field.RelationField) *xTgUserRobotDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgUserRobotDo) Preload(fields ...field.RelationField) *xTgUserRobotDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgUserRobotDo) FirstOrInit() (*model.XTgUserRobot, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgUserRobot), nil
	}
}

func (x xTgUserRobotDo) FirstOrCreate() (*model.XTgUserRobot, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgUserRobot), nil
	}
}

func (x xTgUserRobotDo) FindByPage(offset int, limit int) (result []*model.XTgUserRobot, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgUserRobotDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgUserRobotDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgUserRobotDo) Delete(models ...*model.XTgUserRobot) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgUserRobotDo) withDO(do gen.Dao) *xTgUserRobotDo {
	x.DO = *do.(*gen.DO)
	return x
}
