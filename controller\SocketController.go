package controller

import (
	"fmt"
	"net/http"
	"time"
	"xserver/abugo"
	"xserver/controller/warning"
	"xserver/server"

	"github.com/gorilla/websocket"
	"github.com/imroc/req"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type SocketController struct {
	webSocket *server.WebSocket
}

var upGrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func (c *SocketController) Init() {
	c.webSocket = server.GetWebSocketInstance()
	server.Http().GetNoAuth("/api/ws", c.ws)
	server.Http().PostNoAuth("/api/uvawmbpyvikb", c.push)
	server.Http().PostNoAuth("/api/nmxgqtpmlbrn", c.new_tip)
	server.Http().Post("/api/tip_count", c.tip_count)
	go func() {
		for {
			req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
			time.Sleep(time.Second * 5)
		}
	}()
}

func (c *SocketController) push(ctx *abugo.AbuHttpContent) {
	tipdata := c.get_tip_count()
	c.webSocket.WriteTextMessage(&server.WsMsg{
		MsgType: "new_tip",
		Data:    tipdata,
	})

	c.webSocket.WriteTextMessage(&server.WsMsg{MsgType: "new"})
	ctx.RespOK()
}

func (c *SocketController) new_tip(ctx *abugo.AbuHttpContent) {
	tipdata := c.get_tip_count()
	c.webSocket.WriteTextMessage(&server.WsMsg{
		MsgType: "new_tip",
		Data:    tipdata,
	})
	ctx.RespOK()
}

func (c *SocketController) get_tip_count() map[string]interface{} {
	rdata := map[string]interface{}{}
	db := server.Db().GormDao().Session(&gorm.Session{
		Logger: logger.Default.LogMode(logger.Silent),
	})

	{
		count := -1
		db.Table("x_order").Select("count(*) as count").Where("State = 12").Scan(&count)
		rdata["order_count"] = count
	}
	{
		count := -1
		db.Table("x_commission_audit").Select("count(*) as count").Where("State = 1").Scan(&count)
		rdata["commission_count"] = count
	}
	{
		count := -1
		db.Table("x_withdraw").Select("count(*) as count").Where("State = 0").Scan(&count)
		rdata["withdraw_count"] = count
	}
	{
		count := -1
		db.Table("x_active_reward_audit").Select("count(*) as count").Where("AuditState = 1").Scan(&count)
		rdata["active_count"] = count
	}
	{
		count := -1
		db.Table("x_duihuan").Select("count(*) as count").Where("Id > 4812 AND State = 200 AND BackTxId IS NULL AND CreateTime < DATE_ADD(NOW(),INTERVAL -1 MINUTE)").Order("Id DESC").Scan(&count)
		rdata["duihuan_count"] = count
	}
	{
		count := -1
		db.Table("x_duihuan").Select("count(*) as count").Where("State = 1").Scan(&count)
		rdata["duihuan_exception_count"] = count
	}
	{
		count := -1
		db.Table("x_tiyanjing").Select("count(*) as count").Where("State = 3").Scan(&count)
		rdata["tiyanjinex_count"] = count
	}
	{
		// 赢分预警数量 - 从Redis获取所有运营商的预警数量总和
		count, err := warning.GetAllWarningCount()
		if err != nil {
			count = 0
		}
		//logs.Info(" 推送赢分预警数据：win_score_warning_count=", count)
		rdata["win_score_warning_count"] = count
	}
	return rdata
}

func (c *SocketController) tip_count(ctx *abugo.AbuHttpContent) {
	ctx.RespOK(c.get_tip_count())
}

func (c *SocketController) ws(ctx *abugo.AbuHttpContent) {
	conn, err := upGrader.Upgrade(ctx.Gin().Writer, ctx.Gin().Request, nil)
	if err != nil {
		return
	}
	c.webSocket.Listening(conn, nil)
}
