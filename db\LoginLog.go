package db

import (
	"fmt"
	"xserver/abugo"
	"xserver/server"

	"github.com/yinheli/qqwry"
)

type LoginLog struct {
	Id        int    `gorm:"column:Id"`        //
	UserId    int    `gorm:"column:UserId"`    //玩家id
	SellerId  int    `gorm:"column:SellerId"`  //运营商
	Ip        string `gorm:"column:Ip"`        //登录IP
	LoginTime string `gorm:"column:LoginTime"` //登录时间
	//以为 x_user left jon 字段
	Account      string `gorm:"column:Account"`
	RegisterTime string `gorm:"column:RegisterTime"`
	RegisterIp   string `gorm:"column:RegisterIp"`
	IpAddr       string
}

func (*LoginLog) TableName() string {
	return "x_login_log"
}

func LoginLog_Page_Data(Page int, PageSize int, SellerId int, UserId int, StartTime int64, EndTime int64) (int, []LoginLog) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}

	OrderBy := "Id desc"
	PageKey := "Id"
	data := LoginLog{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", fmt.Sprintf("%s.SellerId", data.TableName()), "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", fmt.Sprintf("%s.UserId", data.TableName()), "=", UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", fmt.Sprintf("%s.LoginTime", data.TableName()), ">=", abugo.TimeStampToLocalTime(StartTime), "")
	if EndTime > 0 {
		EndTime += +********
	}
	server.Db().AddWhere(&sql, &params, "and", fmt.Sprintf("%s.LoginTime", data.TableName()), "<", abugo.TimeStampToLocalTime(EndTime), "")
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)

	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)

	if dt.Total == 0 {
		return dt.Total, []LoginLog{}
	}

	result := []LoginLog{}
	u := User{}
	selectsql := fmt.Sprintf("%s.*,%s.RegisterTime,%s.RegisterIp,%s.Account", data.TableName(), u.TableName(), u.TableName(), u.TableName())
	joinsql := fmt.Sprintf("left join %s on %s.UserId = %s.UserId", u.TableName(), u.TableName(), data.TableName())
	dbtable.Select(selectsql).Where(fmt.Sprintf("%s.%s <= ?", data.TableName(), PageKey), md.MinValue).Where(sql, params...).Joins(joinsql).Limit(PageSize).Order(OrderBy).Find(&result)
	for i := 0; i < len(result); i++ {
		q := qqwry.NewQQwry("./config/ipdata.dat")
		q.Find(result[i].Ip)
		result[i].IpAddr = fmt.Sprintf("%s %s", q.Country, q.City)
	}
	return dt.Total, result
}
