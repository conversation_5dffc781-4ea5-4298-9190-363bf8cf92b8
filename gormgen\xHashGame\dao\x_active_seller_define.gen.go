// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXActiveSellerDefine(db *gorm.DB, opts ...gen.DOOption) xActiveSellerDefine {
	_xActiveSellerDefine := xActiveSellerDefine{}

	_xActiveSellerDefine.xActiveSellerDefineDo.UseDB(db, opts...)
	_xActiveSellerDefine.xActiveSellerDefineDo.UseModel(&model.XActiveSellerDefine{})

	tableName := _xActiveSellerDefine.xActiveSellerDefineDo.TableName()
	_xActiveSellerDefine.ALL = field.NewAsterisk(tableName)
	_xActiveSellerDefine.SellerID = field.NewInt32(tableName, "SellerId")
	_xActiveSellerDefine.ActiveID = field.NewInt32(tableName, "ActiveId")
	_xActiveSellerDefine.Memo = field.NewString(tableName, "Memo")
	_xActiveSellerDefine.AuditType = field.NewInt32(tableName, "AuditType")
	_xActiveSellerDefine.State = field.NewInt32(tableName, "State")
	_xActiveSellerDefine.Sort = field.NewInt32(tableName, "Sort")
	_xActiveSellerDefine.EffectStartTime = field.NewInt64(tableName, "EffectStartTime")
	_xActiveSellerDefine.EffectEndTime = field.NewInt64(tableName, "EffectEndTime")
	_xActiveSellerDefine.Title = field.NewString(tableName, "Title")
	_xActiveSellerDefine.TitleImg = field.NewString(tableName, "TitleImg")
	_xActiveSellerDefine.TitleImgCn = field.NewString(tableName, "TitleImgCn")
	_xActiveSellerDefine.TitleImgEn = field.NewString(tableName, "TitleImgEn")
	_xActiveSellerDefine.TopImg = field.NewString(tableName, "TopImg")
	_xActiveSellerDefine.TopImgEn = field.NewString(tableName, "TopImgEn")
	_xActiveSellerDefine.IsTop = field.NewInt32(tableName, "IsTop")
	_xActiveSellerDefine.TopSort = field.NewInt32(tableName, "TopSort")
	_xActiveSellerDefine.GameType = field.NewString(tableName, "GameType")
	_xActiveSellerDefine.MinLiuShui = field.NewFloat64(tableName, "MinLiuShui")
	_xActiveSellerDefine.ExtReward = field.NewFloat64(tableName, "ExtReward")
	_xActiveSellerDefine.MinDeposit = field.NewFloat64(tableName, "MinDeposit")
	_xActiveSellerDefine.MaxReward = field.NewFloat64(tableName, "MaxReward")
	_xActiveSellerDefine.ValidRecharge = field.NewFloat64(tableName, "ValidRecharge")
	_xActiveSellerDefine.ValidLiuShui = field.NewFloat64(tableName, "ValidLiuShui")
	_xActiveSellerDefine.TrxPrice = field.NewFloat64(tableName, "TrxPrice")
	_xActiveSellerDefine.Config = field.NewString(tableName, "Config")
	_xActiveSellerDefine.BaseConfig = field.NewString(tableName, "BaseConfig")
	_xActiveSellerDefine.CreateTime = field.NewTime(tableName, "CreateTime")
	_xActiveSellerDefine.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xActiveSellerDefine.fillFieldMap()

	return _xActiveSellerDefine
}

// xActiveSellerDefine 运营商活动
type xActiveSellerDefine struct {
	xActiveSellerDefineDo xActiveSellerDefineDo

	ALL             field.Asterisk
	SellerID        field.Int32   // 运营商Id
	ActiveID        field.Int32   // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	Memo            field.String  // 活动说明
	AuditType       field.Int32   // 审核方式 1人工审核 2自动审核
	State           field.Int32   // 状态
	Sort            field.Int32   // 排序
	EffectStartTime field.Int64   // 活动开始时间
	EffectEndTime   field.Int64   // 活动截止时间
	Title           field.String  // 活动名称
	TitleImg        field.String  // 图片
	TitleImgCn      field.String  // 图片中文
	TitleImgEn      field.String  // 图片英文
	TopImg          field.String  // 置顶图片
	TopImgEn        field.String  // 置顶图片英文
	IsTop           field.Int32   // 是否置顶 1是 2否
	TopSort         field.Int32   // 置顶顺序
	GameType        field.String  // 游戏分类
	MinLiuShui      field.Float64 // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	ExtReward       field.Float64 // 额外奖金 闯关活动才有的配置
	MinDeposit      field.Float64 // 最低存款 救援金 和 首日充次日送 活动才有的配置
	MaxReward       field.Float64 // 最大返还金额 救援金活动才有的配置 首日充次日送最大彩金上限
	ValidRecharge   field.Float64 // 有效会员最低充值
	ValidLiuShui    field.Float64 // 有效会员最低流水
	TrxPrice        field.Float64 // Trx按多少倍计算下注价格(同后台配置VipTrxPrice)
	Config          field.String  // 活动配置
	BaseConfig      field.String  // 基础配置
	CreateTime      field.Time    // 创建时间
	UpdateTime      field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xActiveSellerDefine) Table(newTableName string) *xActiveSellerDefine {
	x.xActiveSellerDefineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xActiveSellerDefine) As(alias string) *xActiveSellerDefine {
	x.xActiveSellerDefineDo.DO = *(x.xActiveSellerDefineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xActiveSellerDefine) updateTableName(table string) *xActiveSellerDefine {
	x.ALL = field.NewAsterisk(table)
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ActiveID = field.NewInt32(table, "ActiveId")
	x.Memo = field.NewString(table, "Memo")
	x.AuditType = field.NewInt32(table, "AuditType")
	x.State = field.NewInt32(table, "State")
	x.Sort = field.NewInt32(table, "Sort")
	x.EffectStartTime = field.NewInt64(table, "EffectStartTime")
	x.EffectEndTime = field.NewInt64(table, "EffectEndTime")
	x.Title = field.NewString(table, "Title")
	x.TitleImg = field.NewString(table, "TitleImg")
	x.TitleImgCn = field.NewString(table, "TitleImgCn")
	x.TitleImgEn = field.NewString(table, "TitleImgEn")
	x.TopImg = field.NewString(table, "TopImg")
	x.TopImgEn = field.NewString(table, "TopImgEn")
	x.IsTop = field.NewInt32(table, "IsTop")
	x.TopSort = field.NewInt32(table, "TopSort")
	x.GameType = field.NewString(table, "GameType")
	x.MinLiuShui = field.NewFloat64(table, "MinLiuShui")
	x.ExtReward = field.NewFloat64(table, "ExtReward")
	x.MinDeposit = field.NewFloat64(table, "MinDeposit")
	x.MaxReward = field.NewFloat64(table, "MaxReward")
	x.ValidRecharge = field.NewFloat64(table, "ValidRecharge")
	x.ValidLiuShui = field.NewFloat64(table, "ValidLiuShui")
	x.TrxPrice = field.NewFloat64(table, "TrxPrice")
	x.Config = field.NewString(table, "Config")
	x.BaseConfig = field.NewString(table, "BaseConfig")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xActiveSellerDefine) WithContext(ctx context.Context) *xActiveSellerDefineDo {
	return x.xActiveSellerDefineDo.WithContext(ctx)
}

func (x xActiveSellerDefine) TableName() string { return x.xActiveSellerDefineDo.TableName() }

func (x xActiveSellerDefine) Alias() string { return x.xActiveSellerDefineDo.Alias() }

func (x xActiveSellerDefine) Columns(cols ...field.Expr) gen.Columns {
	return x.xActiveSellerDefineDo.Columns(cols...)
}

func (x *xActiveSellerDefine) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xActiveSellerDefine) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 28)
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ActiveId"] = x.ActiveID
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["AuditType"] = x.AuditType
	x.fieldMap["State"] = x.State
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["EffectStartTime"] = x.EffectStartTime
	x.fieldMap["EffectEndTime"] = x.EffectEndTime
	x.fieldMap["Title"] = x.Title
	x.fieldMap["TitleImg"] = x.TitleImg
	x.fieldMap["TitleImgCn"] = x.TitleImgCn
	x.fieldMap["TitleImgEn"] = x.TitleImgEn
	x.fieldMap["TopImg"] = x.TopImg
	x.fieldMap["TopImgEn"] = x.TopImgEn
	x.fieldMap["IsTop"] = x.IsTop
	x.fieldMap["TopSort"] = x.TopSort
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["MinLiuShui"] = x.MinLiuShui
	x.fieldMap["ExtReward"] = x.ExtReward
	x.fieldMap["MinDeposit"] = x.MinDeposit
	x.fieldMap["MaxReward"] = x.MaxReward
	x.fieldMap["ValidRecharge"] = x.ValidRecharge
	x.fieldMap["ValidLiuShui"] = x.ValidLiuShui
	x.fieldMap["TrxPrice"] = x.TrxPrice
	x.fieldMap["Config"] = x.Config
	x.fieldMap["BaseConfig"] = x.BaseConfig
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xActiveSellerDefine) clone(db *gorm.DB) xActiveSellerDefine {
	x.xActiveSellerDefineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xActiveSellerDefine) replaceDB(db *gorm.DB) xActiveSellerDefine {
	x.xActiveSellerDefineDo.ReplaceDB(db)
	return x
}

type xActiveSellerDefineDo struct{ gen.DO }

func (x xActiveSellerDefineDo) Debug() *xActiveSellerDefineDo {
	return x.withDO(x.DO.Debug())
}

func (x xActiveSellerDefineDo) WithContext(ctx context.Context) *xActiveSellerDefineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xActiveSellerDefineDo) ReadDB() *xActiveSellerDefineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xActiveSellerDefineDo) WriteDB() *xActiveSellerDefineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xActiveSellerDefineDo) Session(config *gorm.Session) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xActiveSellerDefineDo) Clauses(conds ...clause.Expression) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xActiveSellerDefineDo) Returning(value interface{}, columns ...string) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xActiveSellerDefineDo) Not(conds ...gen.Condition) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xActiveSellerDefineDo) Or(conds ...gen.Condition) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xActiveSellerDefineDo) Select(conds ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xActiveSellerDefineDo) Where(conds ...gen.Condition) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xActiveSellerDefineDo) Order(conds ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xActiveSellerDefineDo) Distinct(cols ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xActiveSellerDefineDo) Omit(cols ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xActiveSellerDefineDo) Join(table schema.Tabler, on ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xActiveSellerDefineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xActiveSellerDefineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xActiveSellerDefineDo) Group(cols ...field.Expr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xActiveSellerDefineDo) Having(conds ...gen.Condition) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xActiveSellerDefineDo) Limit(limit int) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xActiveSellerDefineDo) Offset(offset int) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xActiveSellerDefineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xActiveSellerDefineDo) Unscoped() *xActiveSellerDefineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xActiveSellerDefineDo) Create(values ...*model.XActiveSellerDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xActiveSellerDefineDo) CreateInBatches(values []*model.XActiveSellerDefine, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xActiveSellerDefineDo) Save(values ...*model.XActiveSellerDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xActiveSellerDefineDo) First() (*model.XActiveSellerDefine, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveSellerDefine), nil
	}
}

func (x xActiveSellerDefineDo) Take() (*model.XActiveSellerDefine, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveSellerDefine), nil
	}
}

func (x xActiveSellerDefineDo) Last() (*model.XActiveSellerDefine, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveSellerDefine), nil
	}
}

func (x xActiveSellerDefineDo) Find() ([]*model.XActiveSellerDefine, error) {
	result, err := x.DO.Find()
	return result.([]*model.XActiveSellerDefine), err
}

func (x xActiveSellerDefineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XActiveSellerDefine, err error) {
	buf := make([]*model.XActiveSellerDefine, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xActiveSellerDefineDo) FindInBatches(result *[]*model.XActiveSellerDefine, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xActiveSellerDefineDo) Attrs(attrs ...field.AssignExpr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xActiveSellerDefineDo) Assign(attrs ...field.AssignExpr) *xActiveSellerDefineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xActiveSellerDefineDo) Joins(fields ...field.RelationField) *xActiveSellerDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xActiveSellerDefineDo) Preload(fields ...field.RelationField) *xActiveSellerDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xActiveSellerDefineDo) FirstOrInit() (*model.XActiveSellerDefine, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveSellerDefine), nil
	}
}

func (x xActiveSellerDefineDo) FirstOrCreate() (*model.XActiveSellerDefine, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveSellerDefine), nil
	}
}

func (x xActiveSellerDefineDo) FindByPage(offset int, limit int) (result []*model.XActiveSellerDefine, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xActiveSellerDefineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xActiveSellerDefineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xActiveSellerDefineDo) Delete(models ...*model.XActiveSellerDefine) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xActiveSellerDefineDo) withDO(do gen.Dao) *xActiveSellerDefineDo {
	x.DO = *do.(*gen.DO)
	return x
}
