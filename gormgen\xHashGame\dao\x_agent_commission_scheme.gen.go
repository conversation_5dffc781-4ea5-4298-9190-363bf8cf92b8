// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentCommissionScheme(db *gorm.DB, opts ...gen.DOOption) xAgentCommissionScheme {
	_xAgentCommissionScheme := xAgentCommissionScheme{}

	_xAgentCommissionScheme.xAgentCommissionSchemeDo.UseDB(db, opts...)
	_xAgentCommissionScheme.xAgentCommissionSchemeDo.UseModel(&model.XAgentCommissionScheme{})

	tableName := _xAgentCommissionScheme.xAgentCommissionSchemeDo.TableName()
	_xAgentCommissionScheme.ALL = field.NewAsterisk(tableName)
	_xAgentCommissionScheme.SchemeID = field.NewInt32(tableName, "SchemeId")
	_xAgentCommissionScheme.SchemeName = field.NewString(tableName, "SchemeName")
	_xAgentCommissionScheme.GetType = field.NewInt32(tableName, "GetType")
	_xAgentCommissionScheme.GetAmountType = field.NewInt32(tableName, "GetAmountType")
	_xAgentCommissionScheme.RollingTimes = field.NewFloat64(tableName, "RollingTimes")
	_xAgentCommissionScheme.IsSumTeam = field.NewInt32(tableName, "IsSumTeam")
	_xAgentCommissionScheme.ValidRechargeAmount = field.NewFloat64(tableName, "ValidRechargeAmount")
	_xAgentCommissionScheme.ValidLiuShui = field.NewFloat64(tableName, "ValidLiuShui")
	_xAgentCommissionScheme.Status = field.NewInt32(tableName, "Status")
	_xAgentCommissionScheme.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentCommissionScheme.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentCommissionScheme.fillFieldMap()

	return _xAgentCommissionScheme
}

// xAgentCommissionScheme 三级返佣方案
type xAgentCommissionScheme struct {
	xAgentCommissionSchemeDo xAgentCommissionSchemeDo

	ALL                 field.Asterisk
	SchemeID            field.Int32   // 方案Id
	SchemeName          field.String  // 方案名
	GetType             field.Int32   // 发放方式 1人工发放 2自动发放
	GetAmountType       field.Int32   // 发放钱包 1真金钱包 2bonus钱包
	RollingTimes        field.Float64 // 打码倍数
	IsSumTeam           field.Int32   // 自身流水是否累计团队:1累计 2不累计
	ValidRechargeAmount field.Float64 // 有效充值金额
	ValidLiuShui        field.Float64 // 有效流水
	Status              field.Int32   // 方案状态 1启用 2禁用
	CreateTime          field.Time    // 创建时间
	UpdateTime          field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentCommissionScheme) Table(newTableName string) *xAgentCommissionScheme {
	x.xAgentCommissionSchemeDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentCommissionScheme) As(alias string) *xAgentCommissionScheme {
	x.xAgentCommissionSchemeDo.DO = *(x.xAgentCommissionSchemeDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentCommissionScheme) updateTableName(table string) *xAgentCommissionScheme {
	x.ALL = field.NewAsterisk(table)
	x.SchemeID = field.NewInt32(table, "SchemeId")
	x.SchemeName = field.NewString(table, "SchemeName")
	x.GetType = field.NewInt32(table, "GetType")
	x.GetAmountType = field.NewInt32(table, "GetAmountType")
	x.RollingTimes = field.NewFloat64(table, "RollingTimes")
	x.IsSumTeam = field.NewInt32(table, "IsSumTeam")
	x.ValidRechargeAmount = field.NewFloat64(table, "ValidRechargeAmount")
	x.ValidLiuShui = field.NewFloat64(table, "ValidLiuShui")
	x.Status = field.NewInt32(table, "Status")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentCommissionScheme) WithContext(ctx context.Context) *xAgentCommissionSchemeDo {
	return x.xAgentCommissionSchemeDo.WithContext(ctx)
}

func (x xAgentCommissionScheme) TableName() string { return x.xAgentCommissionSchemeDo.TableName() }

func (x xAgentCommissionScheme) Alias() string { return x.xAgentCommissionSchemeDo.Alias() }

func (x xAgentCommissionScheme) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentCommissionSchemeDo.Columns(cols...)
}

func (x *xAgentCommissionScheme) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentCommissionScheme) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["SchemeId"] = x.SchemeID
	x.fieldMap["SchemeName"] = x.SchemeName
	x.fieldMap["GetType"] = x.GetType
	x.fieldMap["GetAmountType"] = x.GetAmountType
	x.fieldMap["RollingTimes"] = x.RollingTimes
	x.fieldMap["IsSumTeam"] = x.IsSumTeam
	x.fieldMap["ValidRechargeAmount"] = x.ValidRechargeAmount
	x.fieldMap["ValidLiuShui"] = x.ValidLiuShui
	x.fieldMap["Status"] = x.Status
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentCommissionScheme) clone(db *gorm.DB) xAgentCommissionScheme {
	x.xAgentCommissionSchemeDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentCommissionScheme) replaceDB(db *gorm.DB) xAgentCommissionScheme {
	x.xAgentCommissionSchemeDo.ReplaceDB(db)
	return x
}

type xAgentCommissionSchemeDo struct{ gen.DO }

func (x xAgentCommissionSchemeDo) Debug() *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentCommissionSchemeDo) WithContext(ctx context.Context) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentCommissionSchemeDo) ReadDB() *xAgentCommissionSchemeDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentCommissionSchemeDo) WriteDB() *xAgentCommissionSchemeDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentCommissionSchemeDo) Session(config *gorm.Session) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentCommissionSchemeDo) Clauses(conds ...clause.Expression) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentCommissionSchemeDo) Returning(value interface{}, columns ...string) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentCommissionSchemeDo) Not(conds ...gen.Condition) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentCommissionSchemeDo) Or(conds ...gen.Condition) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentCommissionSchemeDo) Select(conds ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentCommissionSchemeDo) Where(conds ...gen.Condition) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentCommissionSchemeDo) Order(conds ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentCommissionSchemeDo) Distinct(cols ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentCommissionSchemeDo) Omit(cols ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentCommissionSchemeDo) Join(table schema.Tabler, on ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentCommissionSchemeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentCommissionSchemeDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentCommissionSchemeDo) Group(cols ...field.Expr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentCommissionSchemeDo) Having(conds ...gen.Condition) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentCommissionSchemeDo) Limit(limit int) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentCommissionSchemeDo) Offset(offset int) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentCommissionSchemeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentCommissionSchemeDo) Unscoped() *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentCommissionSchemeDo) Create(values ...*model.XAgentCommissionScheme) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentCommissionSchemeDo) CreateInBatches(values []*model.XAgentCommissionScheme, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentCommissionSchemeDo) Save(values ...*model.XAgentCommissionScheme) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentCommissionSchemeDo) First() (*model.XAgentCommissionScheme, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionScheme), nil
	}
}

func (x xAgentCommissionSchemeDo) Take() (*model.XAgentCommissionScheme, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionScheme), nil
	}
}

func (x xAgentCommissionSchemeDo) Last() (*model.XAgentCommissionScheme, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionScheme), nil
	}
}

func (x xAgentCommissionSchemeDo) Find() ([]*model.XAgentCommissionScheme, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentCommissionScheme), err
}

func (x xAgentCommissionSchemeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentCommissionScheme, err error) {
	buf := make([]*model.XAgentCommissionScheme, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentCommissionSchemeDo) FindInBatches(result *[]*model.XAgentCommissionScheme, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentCommissionSchemeDo) Attrs(attrs ...field.AssignExpr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentCommissionSchemeDo) Assign(attrs ...field.AssignExpr) *xAgentCommissionSchemeDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentCommissionSchemeDo) Joins(fields ...field.RelationField) *xAgentCommissionSchemeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentCommissionSchemeDo) Preload(fields ...field.RelationField) *xAgentCommissionSchemeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentCommissionSchemeDo) FirstOrInit() (*model.XAgentCommissionScheme, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionScheme), nil
	}
}

func (x xAgentCommissionSchemeDo) FirstOrCreate() (*model.XAgentCommissionScheme, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionScheme), nil
	}
}

func (x xAgentCommissionSchemeDo) FindByPage(offset int, limit int) (result []*model.XAgentCommissionScheme, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentCommissionSchemeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentCommissionSchemeDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentCommissionSchemeDo) Delete(models ...*model.XAgentCommissionScheme) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentCommissionSchemeDo) withDO(do gen.Dao) *xAgentCommissionSchemeDo {
	x.DO = *do.(*gen.DO)
	return x
}
