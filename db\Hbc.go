package db

import (
	"xserver/server"

	"github.com/beego/beego/logs"
)

type Hbc struct {
	SellerId             int    `gorm:"column:SellerId;primaryKey"`
	GAppId               string `gorm:"column:GAppId"`               // 游戏商户 速度快
	GApiUrl              string `gorm:"column:GApiUrl"`              // 接口地址
	GLocalPublicKey      string `gorm:"column:GLocalPublicKey"`      // 给hbc，我方调用hbc，用此公钥解密
	GLocalPrivateKey     string `gorm:"column:GLocalPrivateKey"`     // 我方调用hbc加密密钥
	GRiskLocalPublicKey  string `gorm:"column:GRiskLocalPublicKey"`  // 风控回调，hbcrisk工程，给hbc，我方http回复hbc，hbc用此密钥解密
	GRiskLocalPrivateKey string `gorm:"column:GRiskLocalPrivateKey"` // 我方http回复hbc，用此密钥加密
	GServerPublicKey     string `gorm:"column:GServerPublicKey"`     // hbc给我方，我方请求hbc，得到回复用此密钥解密
	GRiskServerPublicKey string `gorm:"column:GRiskServerPublicKey"` // 风控，hbcrisk工程，hbc给我方，我方请求hbc，得到回复用此密钥解密
	DAppId               string `gorm:"column:DAppId"`               // 兑换商户 速度慢
	DApiUrl              string `gorm:"column:DApiUrl"`
	DLocalPublicKey      string `gorm:"column:DLocalPublicKey"`
	DLocalPrivateKey     string `gorm:"column:DLocalPrivateKey"`
	DRiskLocalPublicKey  string `gorm:"column:DRiskLocalPublicKey"`
	DRiskLocalPrivateKey string `gorm:"column:DRiskLocalPrivateKey"`
	DServerPublicKey     string `gorm:"column:DServerPublicKey"`
	DRiskServerPublicKey string `gorm:"column:DRiskServerPublicKey"`
	GMpcServerPublicKey  string `gorm:"column:GMpcServerPublicKey"`
	GMpcLoclPublicKey    string `gorm:"column:GMpcLoclPublicKey"`
	GMpcLocalPrivateKey  string `gorm:"column:GMpcLocalPrivateKey"`
	WAppId               string `gorm:"column:WAppId"` // 充提商户 速度慢
	WApiUrl              string `gorm:"column:WApiUrl"`
	WLocalPublicKey      string `gorm:"column:WLocalPublicKey"`
	WLocalPrivateKey     string `gorm:"column:WLocalPrivateKey"`
	WRiskLocalPublicKey  string `gorm:"column:WRiskLocalPublicKey"`
	WRiskLocalPrivateKey string `gorm:"column:WRiskLocalPrivateKey"`
	WServerPublicKey     string `gorm:"column:WServerPublicKey"`
	WRiskServerPublicKey string `gorm:"column:WRiskServerPublicKey"`
}

func (*Hbc) TableName() string {
	return "x_hbc"
}

func Hbc_Get(SellerId int) *Hbc {
	data_out := Hbc{}
	dbresult := server.Db().Gorm().Table(data_out.TableName()).Where("SellerId = ?", SellerId).First(&data_out)
	err := dbresult.Error
	if dbresult.RowsAffected <= 0 {
		return nil
	}
	if err != nil {
		logs.Error(err)
		return nil
	}
	return &data_out
}

// Hbc_CopyFromTemplate 复制SellerId=20的配置到新的SellerId
func Hbc_CopyFromTemplate(newSellerId int) error {
	// 获取模板配置（SellerId = 20）
	templateConfig := Hbc_Get(20)
	if templateConfig == nil {
		logs.Error("Template HBC config (SellerId=20) not found")
		return nil // 如果模板不存在，不报错，只是不创建配置
	}

	// 检查新SellerId是否已存在配置
	existingConfig := Hbc_Get(newSellerId)
	if existingConfig != nil {
		logs.Info("HBC config for SellerId %d already exists, skipping copy", newSellerId)
		return nil
	}

	// 创建新配置，复制模板数据
	newConfig := *templateConfig
	newConfig.SellerId = newSellerId

	// 插入新配置
	dbresult := server.Db().Gorm().Table(newConfig.TableName()).Create(&newConfig)
	if dbresult.Error != nil {
		logs.Error("Failed to copy HBC config for SellerId %d: %v", newSellerId, dbresult.Error)
		return dbresult.Error
	}

	logs.Info("Successfully copied HBC config from SellerId=20 to SellerId=%d", newSellerId)
	return nil
}
