// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgUserRobot = "x_tg_user_robot"

// XTgUserRobot 会员集成TG登录机器人
type XTgUserRobot struct {
	ID          int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	AccountID   int32     `gorm:"column:AccountId;not null;comment:TG帐号id" json:"AccountId"`                         // TG帐号id
	TgUsername  string    `gorm:"column:TgUsername;not null;comment:机器人Username" json:"TgUsername"`                  // 机器人Username
	TgToken     string    `gorm:"column:TgToken;not null;comment:机器人token" json:"TgToken"`                           // 机器人token
	SellerID    int32     `gorm:"column:SellerId;not null;comment:运营商ID" json:"SellerId"`                            // 运营商ID
	SellerName  string    `gorm:"column:SellerName;not null;default:0;comment:运营商名称" json:"SellerName"`              // 运营商名称
	ChannelID   int32     `gorm:"column:ChannelId;not null;comment:渠道id" json:"ChannelId"`                           // 渠道id
	ChannelName string    `gorm:"column:ChannelName;not null;default:0;comment:渠道名称" json:"ChannelName"`             // 渠道名称
	Remark      string    `gorm:"column:Remark;not null;comment:备注信息" json:"Remark"`                                 // 备注信息
	Status      string    `gorm:"column:Status;not null;default:yes;comment:状态" json:"Status"`                       // 状态
	CreatedAt   time.Time `gorm:"column:CreatedAt;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreatedAt"` // 创建时间
	UpdatedAt   time.Time `gorm:"column:UpdatedAt;not null;default:CURRENT_TIMESTAMP;comment:修改时间" json:"UpdatedAt"` // 修改时间
}

// TableName XTgUserRobot's table name
func (*XTgUserRobot) TableName() string {
	return TableNameXTgUserRobot
}
