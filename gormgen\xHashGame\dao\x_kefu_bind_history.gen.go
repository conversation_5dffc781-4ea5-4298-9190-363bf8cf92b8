// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXKefuBindHistory(db *gorm.DB, opts ...gen.DOOption) xKefuBindHistory {
	_xKefuBindHistory := xKefuBindHistory{}

	_xKefuBindHistory.xKefuBindHistoryDo.UseDB(db, opts...)
	_xKefuBindHistory.xKefuBindHistoryDo.UseModel(&model.XKefuBindHistory{})

	tableName := _xKefuBindHistory.xKefuBindHistoryDo.TableName()
	_xKefuBindHistory.ALL = field.NewAsterisk(tableName)
	_xKefuBindHistory.ID = field.NewInt32(tableName, "Id")
	_xKefuBindHistory.SellerID = field.NewInt32(tableName, "SellerId")
	_xKefuBindHistory.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xKefuBindHistory.UserID = field.NewInt32(tableName, "UserId")
	_xKefuBindHistory.BeforeGroup = field.NewString(tableName, "BeforeGroup")
	_xKefuBindHistory.AfterGroup = field.NewString(tableName, "AfterGroup")
	_xKefuBindHistory.BeforeID = field.NewString(tableName, "BeforeId")
	_xKefuBindHistory.AfterID = field.NewString(tableName, "AfterId")
	_xKefuBindHistory.Account = field.NewString(tableName, "Account")
	_xKefuBindHistory.CreateTime = field.NewTime(tableName, "CreateTime")
	_xKefuBindHistory.AdminAccount = field.NewString(tableName, "AdminAccount")

	_xKefuBindHistory.fillFieldMap()

	return _xKefuBindHistory
}

type xKefuBindHistory struct {
	xKefuBindHistoryDo xKefuBindHistoryDo

	ALL          field.Asterisk
	ID           field.Int32
	SellerID     field.Int32
	ChannelID    field.Int32
	UserID       field.Int32
	BeforeGroup  field.String
	AfterGroup   field.String
	BeforeID     field.String
	AfterID      field.String
	Account      field.String
	CreateTime   field.Time
	AdminAccount field.String

	fieldMap map[string]field.Expr
}

func (x xKefuBindHistory) Table(newTableName string) *xKefuBindHistory {
	x.xKefuBindHistoryDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xKefuBindHistory) As(alias string) *xKefuBindHistory {
	x.xKefuBindHistoryDo.DO = *(x.xKefuBindHistoryDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xKefuBindHistory) updateTableName(table string) *xKefuBindHistory {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.BeforeGroup = field.NewString(table, "BeforeGroup")
	x.AfterGroup = field.NewString(table, "AfterGroup")
	x.BeforeID = field.NewString(table, "BeforeId")
	x.AfterID = field.NewString(table, "AfterId")
	x.Account = field.NewString(table, "Account")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.AdminAccount = field.NewString(table, "AdminAccount")

	x.fillFieldMap()

	return x
}

func (x *xKefuBindHistory) WithContext(ctx context.Context) *xKefuBindHistoryDo {
	return x.xKefuBindHistoryDo.WithContext(ctx)
}

func (x xKefuBindHistory) TableName() string { return x.xKefuBindHistoryDo.TableName() }

func (x xKefuBindHistory) Alias() string { return x.xKefuBindHistoryDo.Alias() }

func (x xKefuBindHistory) Columns(cols ...field.Expr) gen.Columns {
	return x.xKefuBindHistoryDo.Columns(cols...)
}

func (x *xKefuBindHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xKefuBindHistory) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["BeforeGroup"] = x.BeforeGroup
	x.fieldMap["AfterGroup"] = x.AfterGroup
	x.fieldMap["BeforeId"] = x.BeforeID
	x.fieldMap["AfterId"] = x.AfterID
	x.fieldMap["Account"] = x.Account
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["AdminAccount"] = x.AdminAccount
}

func (x xKefuBindHistory) clone(db *gorm.DB) xKefuBindHistory {
	x.xKefuBindHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xKefuBindHistory) replaceDB(db *gorm.DB) xKefuBindHistory {
	x.xKefuBindHistoryDo.ReplaceDB(db)
	return x
}

type xKefuBindHistoryDo struct{ gen.DO }

func (x xKefuBindHistoryDo) Debug() *xKefuBindHistoryDo {
	return x.withDO(x.DO.Debug())
}

func (x xKefuBindHistoryDo) WithContext(ctx context.Context) *xKefuBindHistoryDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xKefuBindHistoryDo) ReadDB() *xKefuBindHistoryDo {
	return x.Clauses(dbresolver.Read)
}

func (x xKefuBindHistoryDo) WriteDB() *xKefuBindHistoryDo {
	return x.Clauses(dbresolver.Write)
}

func (x xKefuBindHistoryDo) Session(config *gorm.Session) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Session(config))
}

func (x xKefuBindHistoryDo) Clauses(conds ...clause.Expression) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xKefuBindHistoryDo) Returning(value interface{}, columns ...string) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xKefuBindHistoryDo) Not(conds ...gen.Condition) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xKefuBindHistoryDo) Or(conds ...gen.Condition) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xKefuBindHistoryDo) Select(conds ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xKefuBindHistoryDo) Where(conds ...gen.Condition) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xKefuBindHistoryDo) Order(conds ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xKefuBindHistoryDo) Distinct(cols ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xKefuBindHistoryDo) Omit(cols ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xKefuBindHistoryDo) Join(table schema.Tabler, on ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xKefuBindHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xKefuBindHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xKefuBindHistoryDo) Group(cols ...field.Expr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xKefuBindHistoryDo) Having(conds ...gen.Condition) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xKefuBindHistoryDo) Limit(limit int) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xKefuBindHistoryDo) Offset(offset int) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xKefuBindHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xKefuBindHistoryDo) Unscoped() *xKefuBindHistoryDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xKefuBindHistoryDo) Create(values ...*model.XKefuBindHistory) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xKefuBindHistoryDo) CreateInBatches(values []*model.XKefuBindHistory, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xKefuBindHistoryDo) Save(values ...*model.XKefuBindHistory) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xKefuBindHistoryDo) First() (*model.XKefuBindHistory, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XKefuBindHistory), nil
	}
}

func (x xKefuBindHistoryDo) Take() (*model.XKefuBindHistory, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XKefuBindHistory), nil
	}
}

func (x xKefuBindHistoryDo) Last() (*model.XKefuBindHistory, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XKefuBindHistory), nil
	}
}

func (x xKefuBindHistoryDo) Find() ([]*model.XKefuBindHistory, error) {
	result, err := x.DO.Find()
	return result.([]*model.XKefuBindHistory), err
}

func (x xKefuBindHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XKefuBindHistory, err error) {
	buf := make([]*model.XKefuBindHistory, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xKefuBindHistoryDo) FindInBatches(result *[]*model.XKefuBindHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xKefuBindHistoryDo) Attrs(attrs ...field.AssignExpr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xKefuBindHistoryDo) Assign(attrs ...field.AssignExpr) *xKefuBindHistoryDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xKefuBindHistoryDo) Joins(fields ...field.RelationField) *xKefuBindHistoryDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xKefuBindHistoryDo) Preload(fields ...field.RelationField) *xKefuBindHistoryDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xKefuBindHistoryDo) FirstOrInit() (*model.XKefuBindHistory, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XKefuBindHistory), nil
	}
}

func (x xKefuBindHistoryDo) FirstOrCreate() (*model.XKefuBindHistory, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XKefuBindHistory), nil
	}
}

func (x xKefuBindHistoryDo) FindByPage(offset int, limit int) (result []*model.XKefuBindHistory, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xKefuBindHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xKefuBindHistoryDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xKefuBindHistoryDo) Delete(models ...*model.XKefuBindHistory) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xKefuBindHistoryDo) withDO(do gen.Dao) *xKefuBindHistoryDo {
	x.DO = *do.(*gen.DO)
	return x
}
