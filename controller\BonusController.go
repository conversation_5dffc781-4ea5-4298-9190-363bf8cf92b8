package controller

import (
	"context"
	"fmt"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/jinzhu/copier"

	_ "github.com/samber/lo"
)

type BonusController struct {
}

func (c *BonusController) Init() {
	group := server.Http().NewGroup("/api")
	{
		group.Post("/bonus_task_template_add", c.bonus_task_template_add)
		group.Post("/bonus_task_template_list", c.bonus_task_template_list)
		group.Post("/bonus_task_user_add", c.bonus_task_user_add)
		group.Post("/bonus_task_user_list", c.bonus_task_user_list)
		group.Post("/bonus_task_user_delete", c.bonus_task_user_delete)
	}
}

func (c *BonusController) bonus_task_template_add(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int

		Id         int
		Nick       string `validate:"required"`
		Typ        int
		Bonus      int64
		Condition1 int64
		Condition2 int64
		Condition3 int64
		LiuShuiOdd int

		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "礼金活动", "增", "新增礼金活动方案")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tb := server.DaoxHashGame().XBonusTaskTemplate
	db := tb.WithContext(context.Background())
	db.FirstOrInit()
	err := db.Save(&model.XBonusTaskTemplate{
		ID:         int32(reqdata.Id),
		Typ:        int32(reqdata.Typ),
		Nick:       reqdata.Nick,
		Bonus:      float64(reqdata.Bonus),
		LiuShuiOdd: int32(reqdata.LiuShuiOdd),
		Condition1: reqdata.Condition1,
		Condition2: reqdata.Condition2,
		Condition3: reqdata.Condition3,
	})
	if err != nil {
		logs.Error("bonus_task_template_add Create ", err)
		ctx.RespErrString(true, &errcode, "db创建失败")
		return
	}
	ctx.RespOK()
}

func (c *BonusController) bonus_task_template_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Nick     string
		Typ      int
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "礼金活动", "查", "查询礼金活动方案")
	if token == nil {
		return
	}
	tb := server.DaoxHashGame().XBonusTaskTemplate
	db := tb.WithContext(context.Background())
	if reqdata.Typ > 0 {
		db = db.Where(tb.Typ.Eq(int32(reqdata.Typ)))
	}
	if len(reqdata.Nick) > 0 {
		db = db.Where(tb.Nick.Like(fmt.Sprintf("%%%s%%", reqdata.Nick)))
	}
	datas, count, err := db.FindByPage((reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
	if err != nil {
		logs.Error("bonus_task_template_list FindByPage ", err)
		ctx.RespErrString(true, &errcode, "db创建失败")
		return
	}
	ctx.Put("count", count)
	ctx.Put("data", datas)
	ctx.RespOK()
}

func (c *BonusController) bonus_task_user_add(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int

		TemplateId int
		UserIds    []int32

		Typ        int32
		Bonus      float64
		Condition1 float64
		Condition2 float64
		Condition3 float64
		LiuShuiOdd int32

		Memo string

		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "礼金", "给玩家新增礼金")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.TemplateId > 0 {
		tb := server.DaoxHashGame().XBonusTaskTemplate
		db := tb.WithContext(context.Background())
		first, err := db.Where(tb.ID.Eq(int32(reqdata.TemplateId))).First()
		if ctx.RespErr(err, &errcode) {
			return
		}
		copier.Copy(&reqdata, &first)
	}
	if ctx.RespErrString(reqdata.Condition1 == 0, &errcode, "条件未配置") {
		return
	}
	tb := server.DaoxHashGame().XBonusTaskUser
	db := tb.WithContext(context.Background())
	list := []*model.XBonusTaskUser{}
	for _, v := range reqdata.UserIds {
		list = append(list, &model.XBonusTaskUser{
			UserID:     v,
			Type:       int32(reqdata.Typ),
			Bonus:      float64(reqdata.Bonus),
			LiuShuiOdd: reqdata.LiuShuiOdd,
			Memo:       reqdata.Memo,
			Condition1: reqdata.Condition1,
			Condition2: reqdata.Condition2,
			Condition3: reqdata.Condition3,
			TimeLine:   carbon.Parse(utils.TimeDefaultTimeKey).StdTime(),
		})
	}
	err := db.Create(list...)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *BonusController) bonus_task_user_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int

		UserId    int32
		Typ       int32
		StartTime int64 // 秒时间戳
		EndTime   int64
		State     []int32

		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "查", "查玩家礼金列表")
	if token == nil {
		return
	}
	tb := server.DaoxHashGame().XBonusTaskUser
	db := tb.WithContext(context.Background())

	if reqdata.UserId > 0 {
		db = db.Where(tb.UserID.Eq(reqdata.UserId))
	}
	if reqdata.Typ > 0 {
		db = db.Where(tb.Type.Eq(reqdata.Typ))
	}
	if reqdata.StartTime > 0 {
		db = db.Where(tb.CreateTime.Gte(carbon.CreateFromTimestamp(reqdata.StartTime).StdTime()))
	}
	if reqdata.EndTime > 0 {
		db = db.Where(tb.CreateTime.Lte(carbon.CreateFromTimestamp(reqdata.EndTime).StdTime()))
	}
	if len(reqdata.State) > 0 {
		db = db.Where(tb.State.In(reqdata.State...))
	}
	finds, count, err := db.FindByPage((reqdata.Page-1)*reqdata.Page, reqdata.Page)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.Put("count", count)
	ctx.Put("data", finds)
	ctx.RespOK()
}

func (c *BonusController) bonus_task_user_delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int

		Id   int64 `validate:"required"`
		Memo string

		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "礼金", "清零玩家礼金")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tb := server.DaoxHashGame().XBonusTaskUser
	db := tb.WithContext(context.Background())
	_, err := db.UpdateSimple(tb.Bonus.Value(0), tb.State.Value(4))
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}
