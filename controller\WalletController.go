package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"path"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/msg"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"
	"xserver/utilsmodel"

	"github.com/spf13/viper"

	"github.com/shopspring/decimal"
	"gorm.io/gen"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
	"gorm.io/gorm"
	"gorm.io/hints"
)

var (
	tronscangoapi      string
	addressTransferUrl = "/api/AddressTransfer_GetList"
)

type WalletController struct {
}

// TopAgentName converts a top agent ID to their nickname from x_user table
func TopAgentName(id int) string {
	if id == 0 {
		return ""
	}
	data, _ := server.Db().Query("SELECT UserId, NickName FROM x_user WHERE UserId = ?", []interface{}{id})
	if len(*data) > 0 {
		return abugo.GetStringFromInterface((*data)[0]["NickName"])
	}
	return fmt.Sprint(id)
}

func (c *WalletController) Init() {
	tronscangoapi = viper.GetString("tronscangoapi")
	group := server.Http().NewGroup("/api/wallet")
	{
		group.Post("/rechargelist", c.rechargelist)
		group.Post("/transak_rechargelist", c.transakRechargelist)
		group.Post("/withdrawlist", c.withdrawlist)
		group.Post("/withdrawaudit", c.withdrawaudit)
		group.Post("/withdraw_mannal", c.withdraw_mannal)
		group.Post("/binded_address", c.binded_address)
		group.Post("/changelog", c.changelog)
		group.Post("/report", c.report)
		group.Post("/changetype_list", c.changetype_list)
		group.Post("/changetype_add", c.changetype_add)
		group.Post("/changetype_modify", c.changetype_modify)
		group.Post("/changetype_search", c.changetype_search)
		group.Post("/get_chain", c.get_chain)
		group.Post("/withdraw_lock", c.withdrawLock)
		group.Post("/deductlist", c.deductlist)
		group.Post("/totalProfitLoss", c.totalProfitLoss)
		group.Post("/ipRepeat", c.ipRepeat)
		group.Post("/activityBonus", c.activityBonus)
		group.Post("/withdraw_address_list", c.withdraw_address_list)
		group.Post("/user_tron_address", c.user_tron_address)
		group.Post("/symbols", c.symbols)
		// 批量订单撤回
		group.Post("/withdraw_order", c.withdrawOrder)
	}
}

func (c *WalletController) transakRechargelist(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page         int
		PageSize     int
		Id           int
		SellerId     int
		ChannelId    []int
		UserId       int
		StartTime    int64
		EndTime      int64
		Address      string
		Symbol       string
		Net          string
		TopAgentId   []int
		SpecialAgent int
		State        int
		Export       int //0表示分页查询，1表示导出报表
		ThirdId      string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "钱包管理", "Transak", "查", "充币记录")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_充币记录_%s", time.Now().Format("**************")))
	var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("Id", "订单编号")
		xlsx.SetTitle("ThirdId", "三方订单")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("Symbol", "充值币种")
		xlsx.SetTitle("GetSymbol", "到账币种")
		xlsx.SetTitle("Net", "网络")
		xlsx.SetTitle("Amount", "充值数量")
		xlsx.SetTitle("TransferRate", "实时汇率")
		xlsx.SetTitle("RealAmount", "到账数量")
		xlsx.SetTitle("State", "到账状态")
		xlsx.SetTitle("ToAddress", "平台地址")
		xlsx.SetTitle("TxId", "交易哈希")
		xlsx.SetTitle("CSGroup", "客服组")
		xlsx.SetTitle("TopAgentId", "顶级代理")
		xlsx.SetTitle("SpecialAgent", "玩家来源")
		xlsx.SetTitle("CreateTime", "充值时间")
		xlsx.SetTitleStyle()
		xlsx.SetColumnWidth("Id", 20)
		xlsx.SetColumnWidth("ThirdId", 20)
		xlsx.SetColumnWidth("ChannelId", 20)
		xlsx.SetColumnWidth("UserId", 20)
		xlsx.SetColumnWidth("Symbol", 20)
		xlsx.SetColumnWidth("GetSymbol", 20)
		xlsx.SetColumnWidth("Net", 20)
		xlsx.SetColumnWidth("Amount", 20)
		xlsx.SetColumnWidth("TransferRate", 20)
		xlsx.SetColumnWidth("RealAmount", 20)
		xlsx.SetColumnWidth("State", 20)
		xlsx.SetColumnWidth("ToAddress", 20)
		xlsx.SetColumnWidth("TxId", 20)
		xlsx.SetColumnWidth("CSGroup", 20)
		xlsx.SetColumnWidth("TopAgentId", 20)
		xlsx.SetColumnWidth("SpecialAgent", 20)
		xlsx.SetColumnWidth("CreateTime", 20)
	}

	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "Id", "=", reqdata.Id, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "FromAddress", "=", reqdata.Address, "")
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		where.Add("and", "Net", "=", reqdata.Net, "")
		where.Add("and", "ThirdId", "=", reqdata.ThirdId, "")
		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "State", "=", reqdata.State, 0)

		total, presult := server.Db().Table("x_recharge_transak").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)

		netFormat := map[string]string{
			"tron": "TRC-20",
		}
		for i := 0; i < len(*presult); i++ {
			(*presult)[i]["Symbol"] = strings.ToUpper((*presult)[i]["Symbol"].(string))
			(*presult)[i]["GetSymbol"] = "USDT"
			(*presult)[i]["Net"] = netFormat[(*presult)[i]["Net"].(string)]

		}
		if reqdata.Export != 1 {
			ctx.Put("total", total)
			ctx.Put("data", *presult)
		} else {
			for i := 0; i < len(*presult); i++ {
				for k, v := range (*presult)[i] {
					if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "State" {
						if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "未找到玩家", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 2 {
							xlsx.SetValue(k, "小于最低充值金额", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 5 {
							xlsx.SetValue(k, "充值成功", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 3 {
							xlsx.SetValue(k, "待支付", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 6 {
							xlsx.SetValue(k, "订单超时", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 7 {
							xlsx.SetValue(k, "支付失败", int64(i+2))
						} else {
							xlsx.SetValue(k, v, int64(i+2))
						}
					} else if k == "Net" {
						if abugo.GetStringFromInterface(v) == "tron" {
							xlsx.SetValue(k, "TRC-20", int64(i+2))
						} else if abugo.GetStringFromInterface(v) == "eth" {
							xlsx.SetValue(k, "ERC-20", int64(i+2))
						} else {
							xlsx.SetValue(k, v, int64(i+2))
						}
					} else if k == "PayType" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "区块链", int64(i+2))
						} else if xgo.ToInt(v) == 2 {
							xlsx.SetValue(k, "pix", int64(i+2))
						} else if xgo.ToInt(v) == 3 {
							xlsx.SetValue(k, "易币付", int64(i+2))
						} else if xgo.ToInt(v) == 4 {
							xlsx.SetValue(k, "ebpay", int64(i+2))
						}
					} else if k == "IsFirst" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "是", int64(i+2))
						} else {
							xlsx.SetValue(k, "否", int64(i+2))
						}
					} else if k == "SpecialAgent" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "独立代理", int64(i+2))
						} else {
							xlsx.SetValue(k, "公司官网", int64(i+2))
						}
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
			}
		}
		totalSize = total
	}
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "Id", "=", reqdata.Id, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "FromAddress", "=", reqdata.Address, "")
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		where.Add("and", "Net", "=", reqdata.Net, "")
		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "State", "=", reqdata.State, 0)

		sel := "IFNULL(sum(Amount),0) as TotalAmount,IFNULL(sum(RealAmount),0) as TotalRealAmount,count(DISTINCT UserId) as TotalUserCount"
		presult, _ := server.Db().Table("x_recharge_transak").Select(sel).Where(where).GetList()
		if reqdata.Export != 1 {
			ctx.Put("totaldata", (*presult)[0])
		} else {
			xlsx.SetValue("Id", "合计", totalSize+2)
			xlsx.SetValue("Amount", (*presult)[0]["TotalAmount"], totalSize+2)
			xlsx.SetValue("RealAmount", (*presult)[0]["TotalRealAmount"], totalSize+2)
			xlsx.SetValueStyle(totalSize + 2)
		}
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()

}

// ALTER TABLE `x_hash_game`.`x_recharge` ADD COLUMN `FromAddress` VARCHAR(255) NULL COMMENT '打款地址' AFTER `Net`;

func (c *WalletController) rechargelist(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page         int
		PageSize     int
		Id           int
		SellerId     []int32
		ChannelId    []int
		UserId       int
		StartTime    int64
		EndTime      int64
		Address      string
		Symbol       string
		Net          string
		TopAgentId   []int
		SpecialAgent int
		IsFirst      int //0全部，1首充，2非首充
		State        int
		PayType      int
		Export       int //0表示分页查询，1表示导出报表
		ThirdId      string
		OrderType    int
		IsTest       int
		HostTagId    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndTokenJudgeSellerId(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, reqdata.SellerId, "钱包管理", "充币记录", "查", "充币记录")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_充币记录_%s", time.Now().Format("**************")))
	var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("SellerId", "运营商")
		xlsx.SetTitle("Id", "订单编号")
		xlsx.SetTitle("ThirdId", "三方订单")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("Symbol", "币种")
		xlsx.SetTitle("PayType", "支付类型")
		xlsx.SetTitle("Net", "网络")
		xlsx.SetTitle("Amount", "充值数量")
		xlsx.SetTitle("TransferRate", "实时汇率")
		xlsx.SetTitle("RealAmount", "到账数量")
		xlsx.SetTitle("State", "到账状态")
		xlsx.SetTitle("FromAddress", "充值地址")
		xlsx.SetTitle("ToAddress", "平台地址")
		xlsx.SetTitle("TxId", "交易哈希")
		xlsx.SetTitle("CSGroup", "客服组")
		xlsx.SetTitle("TopAgentId", "顶级代理")
		xlsx.SetTitle("SpecialAgent", "玩家来源")
		xlsx.SetTitle("IsFirst", "是否首充")
		xlsx.SetTitle("CreateTime", "充值时间")
		xlsx.SetTitle("TagName", "标签")
		xlsx.SetTitleStyle()
		xlsx.SetColumnWidth("SellerId", 20)
		xlsx.SetColumnWidth("Id", 20)
		xlsx.SetColumnWidth("ThirdId", 20)
		xlsx.SetColumnWidth("ChannelId", 20)
		xlsx.SetColumnWidth("UserId", 20)
		xlsx.SetColumnWidth("Symbol", 20)
		xlsx.SetColumnWidth("PayType", 20)
		xlsx.SetColumnWidth("Net", 20)
		xlsx.SetColumnWidth("Amount", 20)
		xlsx.SetColumnWidth("TransferRate", 20)
		xlsx.SetColumnWidth("RealAmount", 20)
		xlsx.SetColumnWidth("State", 20)
		xlsx.SetColumnWidth("FromAddress", 20)
		xlsx.SetColumnWidth("ToAddress", 20)
		xlsx.SetColumnWidth("TxId", 20)
		xlsx.SetColumnWidth("CSGroup", 20)
		xlsx.SetColumnWidth("TopAgentId", 20)
		xlsx.SetColumnWidth("SpecialAgent", 20)
		xlsx.SetColumnWidth("IsFirst", 20)
		xlsx.SetColumnWidth("CreateTime", 20)
		xlsx.SetColumnWidth("TagName", 20)
	}

	{
		where := abugo.AbuDbWhere{}
		//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		// 运营商多选
		sellerIdStr := utils.ToSellers(reqdata.SellerId)
		if len(sellerIdStr) > 0 {
			where.Add("and", "x_recharge.SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
		}
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "x_recharge.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "x_recharge.Id", "=", reqdata.Id, 0)
		where.Add("and", "x_recharge.UserId", "=", reqdata.UserId, 0)
		where.Add("and", "x_recharge.FromAddress", "=", reqdata.Address, "")
		where.Add("and", "x_recharge.Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "x_recharge.CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		where.Add("and", "x_recharge.CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		where.Add("and", "x_recharge.Net", "=", reqdata.Net, "")
		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "x_recharge.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "x_recharge.SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "x_recharge.IsFirst", "=", reqdata.IsFirst, 0)
		where.Add("and", "x_recharge.State", "=", reqdata.State, 0)
		where.Add("and", "x_recharge.PayType", "=", reqdata.PayType, 0)
		where.Add("and", "x_recharge.ThirdId", "=", reqdata.ThirdId, "")
		where.Add("and", "x_recharge.OrderType", "=", reqdata.OrderType, 0)
		where.Add("and", "x_user.IsTest", "=", reqdata.IsTest, 0)
		where.Add("and", "x_channel_host.HostTagId", "=", reqdata.HostTagId, 0)
		total, presult := server.Db().Table("x_recharge").
			Join("left join x_user on x_recharge.userid = x_user.userid left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id").
			Where(where).
			OrderBy("x_recharge.id desc").
			Select("x_recharge.*, x_user.IsTest, x_host_tag.TagName").
			PageData(reqdata.Page, reqdata.PageSize)
		for i := 0; i < len(*presult); i++ {
			if (*presult)[i]["PayType"].(int64) == 1 {
				(*presult)[i]["Net"] = utils.GetSymbolToChain((*presult)[i]["Net"].(string))
			}
			(*presult)[i]["Symbol"] = strings.ToUpper((*presult)[i]["Symbol"].(string))
		}
		if reqdata.Export != 1 {
			ctx.Put("total", total)
			ctx.Put("data", *presult)
		} else {
			sellerNameMap, err := InitSellerNameMap()
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
				return
			}
			for i := 0; i < len(*presult); i++ {
				for k, v := range (*presult)[i] {
					if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "SellerId" {
						xlsx.SetValue(k, SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "State" {
						if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "未找到玩家", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 2 {
							xlsx.SetValue(k, "小于最低充值金额", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 5 {
							xlsx.SetValue(k, "充值成功", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 3 {
							xlsx.SetValue(k, "待支付", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 6 {
							xlsx.SetValue(k, "订单超时", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 7 {
							xlsx.SetValue(k, "支付失败", int64(i+2))
						} else {
							xlsx.SetValue(k, v, int64(i+2))
						}
					} else if k == "Net" {
						if abugo.GetStringFromInterface(v) == "tron" {
							xlsx.SetValue(k, "TRC-20", int64(i+2))
						} else if abugo.GetStringFromInterface(v) == "eth" {
							xlsx.SetValue(k, "ERC-20", int64(i+2))
						} else {
							xlsx.SetValue(k, v, int64(i+2))
						}
					} else if k == "PayType" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "区块链", int64(i+2))
						} else if xgo.ToInt(v) == 2 {
							xlsx.SetValue(k, "pix", int64(i+2))
						} else if xgo.ToInt(v) == 3 {
							xlsx.SetValue(k, "易币付", int64(i+2))
						} else if xgo.ToInt(v) == 4 {
							xlsx.SetValue(k, "ebpay", int64(i+2))
						} else if xgo.ToInt(v) == 5 {
							xlsx.SetValue(k, "hambit", int64(i+2))
						} else if xgo.ToInt(v) == 7 {
							xlsx.SetValue(k, "pay24", int64(i+2))
						} else if xgo.ToInt(v) == 10 {
							xlsx.SetValue(k, "Epay", int64(i+2))
						} else if xgo.ToInt(v) == 11 {
							xlsx.SetValue(k, "DgPay", int64(i+2))
						} else if xgo.ToInt(v) == 12 {
							xlsx.SetValue(k, "PaymentIQ", int64(i+2))
						} else if xgo.ToInt(v) == 13 {
							xlsx.SetValue(k, "lpay", int64(i+2))
						} else if xgo.ToInt(v) == 14 {
							xlsx.SetValue(k, "richway", int64(i+2))
						} else if xgo.ToInt(v) == 15 {
							xlsx.SetValue(k, "btcash", int64(i+2))
						} else if xgo.ToInt(v) == 16 {
							xlsx.SetValue(k, "powerpay", int64(i+2))
						} else if xgo.ToInt(v) == 17 {
							xlsx.SetValue(k, "mg99", int64(i+2))
						} else if xgo.ToInt(v) == 18 {
							xlsx.SetValue(k, "being", int64(i+2))
						} else if xgo.ToInt(v) == 19 {
							xlsx.SetValue(k, "feibaopay", int64(i+2))
						} else if xgo.ToInt(v) == 20 {
							xlsx.SetValue(k, "ytpay", int64(i+2))
						}
					} else if k == "IsFirst" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "是", int64(i+2))
						} else {
							xlsx.SetValue(k, "否", int64(i+2))
						}
					} else if k == "IsTest" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "是", int64(i+2))
						} else {
							xlsx.SetValue(k, "否", int64(i+2))
						}
					} else if k == "SpecialAgent" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "独立代理", int64(i+2))
						} else {
							xlsx.SetValue(k, "公司官网", int64(i+2))
						}
					} else if k == "TopAgentId" {
						xlsx.SetValue(k, v, int64(i+2))
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
			}
		}
		totalSize = total
	}
	{
		// 构建统计查询的WHERE条件
		where := abugo.AbuDbWhere{}
		//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		// 运营商多选
		sellerIdStr := utils.ToSellers(reqdata.SellerId)
		if len(sellerIdStr) > 0 {
			where.Add("and", "SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
		}
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "Id", "=", reqdata.Id, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "FromAddress", "=", reqdata.Address, "")
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		where.Add("and", "Net", "=", reqdata.Net, "")
		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "IsFirst", "=", reqdata.IsFirst, 0)
		where.Add("and", "PayType", "=", reqdata.PayType, 0)
		where.Add("and", "ThirdId", "=", reqdata.ThirdId, "")
		where.Add("and", "OrderType", "=", reqdata.OrderType, 0)

		// 分别统计所有订单和成功订单
		// 1. 统计所有符合条件的订单（用于总笔数和总用户数）
		allWhere := where
		if reqdata.State != 0 {
			allWhere.Add("and", "State", "=", reqdata.State, 0)
		}
		sel := "count(*) as TotalCount,count(DISTINCT UserId) as TotalUserCount"
		allResult, _ := server.Db().Table("x_recharge").Select(sel).Where(allWhere).GetList()

		// 2. 统计充值成功的订单（用于金额统计）
		// 重新构建WHERE条件，避免与前面的State条件冲突
		successWhere := abugo.AbuDbWhere{}
		// 运营商多选
		if len(sellerIdStr) > 0 {
			successWhere.Add("and", "x_recharge.SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
		}
		if len(channelid) > 0 {
			successWhere.Add("and", "x_recharge.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		successWhere.Add("and", "x_recharge.Id", "=", reqdata.Id, 0)
		successWhere.Add("and", "x_recharge.UserId", "=", reqdata.UserId, 0)
		successWhere.Add("and", "x_recharge.FromAddress", "=", reqdata.Address, "")
		successWhere.Add("and", "x_recharge.Symbol", "=", reqdata.Symbol, "")
		successWhere.Add("and", "x_recharge.CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		successWhere.Add("and", "x_recharge.CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		successWhere.Add("and", "x_recharge.Net", "=", reqdata.Net, "")
		if len(topagentid) > 0 {
			successWhere.Add("and", "x_recharge.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		successWhere.Add("and", "x_recharge.SpecialAgent", "=", reqdata.SpecialAgent, 0)
		successWhere.Add("and", "x_recharge.IsFirst", "=", reqdata.IsFirst, 0)
		successWhere.Add("and", "x_recharge.PayType", "=", reqdata.PayType, 0)
		successWhere.Add("and", "x_recharge.ThirdId", "=", reqdata.ThirdId, "")
		successWhere.Add("and", "x_recharge.OrderType", "=", reqdata.OrderType, 0)
		successWhere.Add("and", "x_recharge.State", "=", reqdata.State, 0)

		successWhere.Add("and", "x_channel_host.HostTagId", "=", reqdata.HostTagId, 0)
		sel = "IFNULL(sum(x_recharge.Amount),0) as TotalAmount,IFNULL(sum(x_recharge.RealAmount),0) as TotalRealAmount,count(DISTINCT x_recharge.UserId) as SuccessUserCount"
		successResult, _ := server.Db().Table("x_recharge").
			Select(sel).
			Join("left join x_user on x_recharge.userid = x_user.userid left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id").
			Where(successWhere).
			GetList()

		// 合并统计结果
		totalData := map[string]interface{}{
			"TotalCount":       (*allResult)[0]["TotalCount"],           // 总订单数
			"TotalUserCount":   (*allResult)[0]["TotalUserCount"],       // 总用户数
			"TotalAmount":      (*successResult)[0]["TotalAmount"],      // 成功充值总金额
			"TotalRealAmount":  (*successResult)[0]["TotalRealAmount"],  // 成功充值总到账金额
			"SuccessUserCount": (*successResult)[0]["SuccessUserCount"], // 成功充值用户数
		}

		if reqdata.Export != 1 {
			ctx.Put("totaldata", totalData)
		} else {
			xlsx.SetValue("Id", "合计", totalSize+2)
			xlsx.SetValue("Amount", (*successResult)[0]["TotalAmount"], totalSize+2)
			xlsx.SetValue("RealAmount", (*successResult)[0]["TotalRealAmount"], totalSize+2)
			xlsx.SetValueStyle(totalSize + 2)
		}
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	server.WriteAdminLog("充币记录列表", ctx, reqdata)
	ctx.RespOK()
}

func (c *WalletController) withdrawlist(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page         int
		PageSize     int
		SellerId     []int32
		ChannelId    []int
		Id           int
		UserId       int
		StartTime    int64
		EndTime      int64
		Address      string
		State        int
		Net          string
		TopAgentId   []int
		SpecialAgent int
		Symbol       string
		PayType      int
		Export       int //0表示分页查询，1表示导出报表
		LockState    int
		OrderType    int
		IsFirst      int
		HostTagId    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndTokenJudgeSellerId(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, reqdata.SellerId, "钱包管理", "提币记录", "查", "提币记录")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_提币记录_%s", time.Now().Format("**************")))
	var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("SellerId", "运营商")
		xlsx.SetTitle("Id", "订单编号")
		xlsx.SetTitle("ThirdId", "三方订单")
		xlsx.SetTitle("ChannelId", "所属渠道商")
		xlsx.SetTitle("BetChannelId", "提币渠道商")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("Account", "玩家账号")
		xlsx.SetTitle("Symbol", "提币币种")
		xlsx.SetTitle("PayType", "支付类型")
		xlsx.SetTitle("Net", "网络")
		xlsx.SetTitle("Amount", "提币数量")
		xlsx.SetTitle("Fee", "扣除手续费")
		xlsx.SetTitle("PayoutAmount", "到账额度")
		xlsx.SetTitle("Address", "提币地址")
		xlsx.SetTitle("AduitMemo", "备注")
		xlsx.SetTitle("CreateTime", "发起时间")
		xlsx.SetTitle("AduitTime", "审核时间")
		xlsx.SetTitle("AuditAccount", "审核人")
		xlsx.SetTitle("SendTime", "发放时间")
		xlsx.SetTitle("SendAccount", "发放人")
		xlsx.SetTitle("State", "状态")
		xlsx.SetTitle("HbcOrder", "HBC订单")
		xlsx.SetTitle("TxId", "出款哈希")
		xlsx.SetTitle("SpecialAgent", "玩家来源")
		xlsx.SetTitle("CSGroup", "客服组")
		xlsx.SetTitle("TopAgentId", "顶级代理")
		xlsx.SetTitle("IsFirst", "是否首提")
		xlsx.SetTitle("TagName", "标签")
		xlsx.SetTitleStyle()
		xlsx.SetColumnWidth("SellerId", 20)
		xlsx.SetColumnWidth("Id", 20)
		xlsx.SetColumnWidth("ThirdId", 20)
		xlsx.SetColumnWidth("ChannelId", 20)
		xlsx.SetColumnWidth("BetChannelId", 20)
		xlsx.SetColumnWidth("UserId", 20)
		xlsx.SetColumnWidth("Account", 20)
		xlsx.SetColumnWidth("Symbol", 20)
		xlsx.SetColumnWidth("PayType", 20)
		xlsx.SetColumnWidth("Net", 20)
		xlsx.SetColumnWidth("Amount", 20)
		xlsx.SetColumnWidth("Fee", 20)
		xlsx.SetColumnWidth("PayoutAmount", 20)
		xlsx.SetColumnWidth("Address", 20)
		xlsx.SetColumnWidth("AduitMemo", 20)
		xlsx.SetColumnWidth("CreateTime", 20)
		xlsx.SetColumnWidth("AduitTime", 20)
		xlsx.SetColumnWidth("AuditAccount", 20)
		xlsx.SetColumnWidth("SendTime", 20)
		xlsx.SetColumnWidth("SendAccount", 20)
		xlsx.SetColumnWidth("State", 20)
		xlsx.SetColumnWidth("HbcOrder", 20)
		xlsx.SetColumnWidth("TxId", 20)
		xlsx.SetColumnWidth("SpecialAgent", 20)
		xlsx.SetColumnWidth("CSGroup", 20)
		xlsx.SetColumnWidth("TopAgentId", 20)
		xlsx.SetColumnWidth("IsFirst", 20)
		xlsx.SetColumnWidth("TagName", 20)
	}

	{
		where := abugo.AbuDbWhere{}
		//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		// 运营商多选
		sellerIdStr := utils.ToSellers(reqdata.SellerId)
		if len(sellerIdStr) > 0 {
			where.Add("and", "x_withdraw.SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
		}
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "x_withdraw.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "x_withdraw.Id", "=", reqdata.Id, 0)
		where.Add("and", "x_withdraw.UserId", "=", reqdata.UserId, 0)
		where.Add("and", "x_withdraw.Address", "=", reqdata.Address, "")
		where.Add("and", "x_withdraw.State", "=", reqdata.State, -1)
		where.Add("and", "x_withdraw.CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		where.Add("and", "x_withdraw.CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		where.Add("and", "x_withdraw.Net", "=", reqdata.Net, "")
		where.Add("and", "x_withdraw.IsFirst", "=", reqdata.IsFirst, 0)

		if reqdata.LockState > 0 {
			where.Add("and", "x_withdraw.LockState", "=", reqdata.LockState, 0)
			where.Add("and", "x_withdraw.State", "=", 0, -1)
		}
		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "x_withdraw.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "x_withdraw.SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "x_withdraw.Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "x_withdraw.PayType", "=", reqdata.PayType, 0)
		where.Add("and", "x_withdraw.OrderType", "=", reqdata.OrderType, 0)
		where.Add("and", "x_channel_host.HostTagId", "=", reqdata.HostTagId, 0)
		total, presult := server.Db().Table("x_withdraw").
			Join("left join x_user on x_withdraw.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id").
			Select("x_withdraw.*,x_host_tag.TagName as TagName").
			Where(where).
			OrderBy("x_withdraw.id desc").
			PageData(reqdata.Page, reqdata.PageSize)

		// 处理查询结果
		for i := 0; i < len(*presult); i++ {
			(*presult)[i]["Symbol"] = strings.ToUpper((*presult)[i]["Symbol"].(string))

			// 直接获取BetChannelId和ChannelId进行比较
			var betChannelId int32
			var channelId int32

			// 获取BetChannelId
			if betChannelIdVal, ok := (*presult)[i]["BetChannelId"]; ok && betChannelIdVal != nil {
				betChannelId = int32(abugo.GetInt64FromInterface(betChannelIdVal))
			}

			// 获取ChannelId
			if channelIdVal, ok := (*presult)[i]["ChannelId"]; ok && channelIdVal != nil {
				channelId = int32(abugo.GetInt64FromInterface(channelIdVal))
			}

			// 默认为true，如果两个渠道ID不同，则设为false
			isSameChannel := true
			if betChannelId > 0 && channelId > 0 && betChannelId != channelId {
				isSameChannel = false
			}

			(*presult)[i]["IsSameChannel"] = isSameChannel
		}
		if reqdata.Export != 1 {
			ctx.Put("total", total)
			ctx.Put("data", *presult)
		} else {
			sellerNameMap, err := InitSellerNameMap()
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
				return
			}
			for i := 0; i < len(*presult); i++ {
				for k, v := range (*presult)[i] {
					if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "BetChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "SellerId" {
						xlsx.SetValue(k, SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "State" {
						if abugo.GetInt64FromInterface(v) == 0 {
							xlsx.SetValue(k, "待审核", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "审核拒绝", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 2 {
							xlsx.SetValue(k, "待发放", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 4 {
							xlsx.SetValue(k, "已发放", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 5 {
							xlsx.SetValue(k, "正在出款", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 6 {
							xlsx.SetValue(k, "出款完成", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 7 {
							xlsx.SetValue(k, "出款失败", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 8 {
							xlsx.SetValue(k, "发放拒绝", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 9 {
							xlsx.SetValue(k, "人工发放", int64(i+2))
						} else {
							xlsx.SetValue(k, v, int64(i+2))
						}
					} else if k == "Net" {
						if abugo.GetStringFromInterface(v) == "tron" {
							xlsx.SetValue(k, "TRC-20", int64(i+2))
						} else if abugo.GetStringFromInterface(v) == "eth" {
							xlsx.SetValue(k, "ERC-20", int64(i+2))
						} else {
							xlsx.SetValue(k, v, int64(i+2))
						}
					} else if k == "IsFirst" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "是", int64(i+2))
						} else if xgo.ToInt(v) == 2 {
							xlsx.SetValue(k, "否", int64(i+2))
						} else {
							xlsx.SetValue(k, "--", int64(i+2))
						}
					} else if k == "PayType" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "区块链", int64(i+2))
						} else if xgo.ToInt(v) == 2 {
							xlsx.SetValue(k, "pix", int64(i+2))
						} else if xgo.ToInt(v) == 3 {
							xlsx.SetValue(k, "易币付", int64(i+2))
						} else if xgo.ToInt(v) == 4 {
							xlsx.SetValue(k, "ebpay", int64(i+2))
						} else if xgo.ToInt(v) == 7 {
							xlsx.SetValue(k, "24Pay", int64(i+2))
						} else if xgo.ToInt(v) == 10 {
							xlsx.SetValue(k, "Epay", int64(i+2))
						} else if xgo.ToInt(v) == 11 {
							xlsx.SetValue(k, "DgPay", int64(i+2))
						} else if xgo.ToInt(v) == 12 {
							xlsx.SetValue(k, "PaymentIQ", int64(i+2))
						} else if xgo.ToInt(v) == 13 {
							xlsx.SetValue(k, "lpay", int64(i+2))
						} else if xgo.ToInt(v) == 14 {
							xlsx.SetValue(k, "richway", int64(i+2))
						} else if xgo.ToInt(v) == 15 {
							xlsx.SetValue(k, "btcash", int64(i+2))
						} else if xgo.ToInt(v) == 16 {
							xlsx.SetValue(k, "powerpay", int64(i+2))
						} else if xgo.ToInt(v) == 17 {
							xlsx.SetValue(k, "mg99", int64(i+2))
						} else if xgo.ToInt(v) == 18 {
							xlsx.SetValue(k, "being", int64(i+2))
						} else if xgo.ToInt(v) == 19 {
							xlsx.SetValue(k, "feibaopay", int64(i+2))
						} else if xgo.ToInt(v) == 20 {
							xlsx.SetValue(k, "ytpay", int64(i+2))
						} else if xgo.ToInt(v) == 21 {
							xlsx.SetValue(k, "expay", int64(i+2))
						} else if xgo.ToInt(v) == 22 {
							xlsx.SetValue(k, "praxispay", int64(i+2))
						} else if xgo.ToInt(v) == 23 {
							xlsx.SetValue(k, "u2cpay", int64(i+2))
						} else if xgo.ToInt(v) == 24 {
							xlsx.SetValue(k, "rmpay", int64(i+2))
						} else if xgo.ToInt(v) == 25 {
							xlsx.SetValue(k, "mainpay", int64(i+2))
						}

					} else if k == "SpecialAgent" {
						if xgo.ToInt(v) == 1 {
							xlsx.SetValue(k, "独立代理", int64(i+2))
						} else {
							xlsx.SetValue(k, "公司官网", int64(i+2))
						}
					} else if k == "TopAgentId" {
						xlsx.SetValue(k, v, int64(i+2))
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
					xlsx.SetValue("PayoutAmount", xgo.ToFloat((*presult)[i]["Amount"])-xgo.ToFloat((*presult)[i]["Fee"]), int64(i+2))
				}
			}
		}
		totalSize = total
	}
	{
		// 构建统计查询的WHERE条件
		where := abugo.AbuDbWhere{}
		//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		// 运营商多选
		sellerIdStr := utils.ToSellers(reqdata.SellerId)
		if len(sellerIdStr) > 0 {
			where.Add("and", "x_withdraw.SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
		}
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "x_withdraw.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "x_withdraw.Id", "=", reqdata.Id, 0)
		where.Add("and", "x_withdraw.UserId", "=", reqdata.UserId, 0)
		where.Add("and", "x_withdraw.Address", "=", reqdata.Address, "")
		where.Add("and", "x_withdraw.CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		where.Add("and", "x_withdraw.CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		where.Add("and", "x_withdraw.Net", "=", reqdata.Net, "")
		where.Add("and", "x_withdraw.IsFirst", "=", reqdata.IsFirst, 0)
		where.Add("and", "x_channel_host.HostTagId", "=", reqdata.HostTagId, 0)

		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "x_withdraw.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "x_withdraw.SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "x_withdraw.Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "x_withdraw.PayType", "=", reqdata.PayType, 0)
		where.Add("and", "x_withdraw.OrderType", "=", reqdata.OrderType, 0)

		// 分别统计所有订单和成功订单
		// 1. 统计所有符合条件的订单（用于总笔数、总用户数和发起提现总金额）
		allWhere := where
		allWhere.Add("and", "x_withdraw.State", "=", reqdata.State, -1)
		if reqdata.LockState > 0 {
			allWhere.Add("and", "x_withdraw.LockState", "=", reqdata.LockState, 0)
			allWhere.Add("and", "x_withdraw.State", "=", 0, -1)
		}
		sel := "count(*) as TotalCount,count(DISTINCT x_withdraw.UserId) as TotalUserCount,IFNULL(Sum(x_withdraw.Amount),0) as TotalAmount"
		allResult, _ := server.Db().Table("x_withdraw").
			Join("left join x_user on x_withdraw.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id").
			Select(sel).
			Where(allWhere).
			GetList()

		// 2. 统计成功提币的订单（用于金额统计）
		// 重新构建WHERE条件，避免与前面的State条件冲突
		successWhere := abugo.AbuDbWhere{}
		// 运营商多选
		if len(sellerIdStr) > 0 {
			successWhere.Add("and", "x_withdraw.SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
		}
		if len(channelid) > 0 {
			successWhere.Add("and", "x_withdraw.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		successWhere.Add("and", "x_withdraw.Id", "=", reqdata.Id, 0)
		successWhere.Add("and", "x_withdraw.UserId", "=", reqdata.UserId, 0)
		successWhere.Add("and", "x_withdraw.Address", "=", reqdata.Address, "")
		successWhere.Add("and", "x_withdraw.CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
		successWhere.Add("and", "x_withdraw.CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
		successWhere.Add("and", "x_withdraw.Net", "=", reqdata.Net, "")
		successWhere.Add("and", "x_withdraw.IsFirst", "=", reqdata.IsFirst, 0)
		if len(topagentid) > 0 {
			successWhere.Add("and", "x_withdraw.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		successWhere.Add("and", "x_withdraw.SpecialAgent", "=", reqdata.SpecialAgent, 0)
		successWhere.Add("and", "x_withdraw.Symbol", "=", reqdata.Symbol, "")
		successWhere.Add("and", "x_withdraw.PayType", "=", reqdata.PayType, 0)
		successWhere.Add("and", "x_withdraw.OrderType", "=", reqdata.OrderType, 0)
		successWhere.Add("and", "x_withdraw.State", "in", "(6,9)", nil) // 只统计出款完成(6)和人工发放(9)的订单
		successWhere.Add("and", "x_channel_host.HostTagId", "=", reqdata.HostTagId, 0)
		sel = "IFNULL(Sum(x_withdraw.Amount),0) as SuccessAmount,IFNULL(sum(x_withdraw.Fee),0) as TotalFee,IFNULL(sum(x_withdraw.NetFee),0) as TotalNetFee,count(DISTINCT x_withdraw.UserId) as SuccessUserCount"
		successResult, _ := server.Db().Table("x_withdraw").
			Join("left join x_user on x_withdraw.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id").
			Select(sel).
			Where(successWhere).
			GetList()

		// 计算提现到账总金额（成功提现金额 - 手续费 - 网络手续费）
		successAmount := (*successResult)[0]["SuccessAmount"].(float64)
		successFee := (*successResult)[0]["TotalFee"].(float64)
		successNetFee := (*successResult)[0]["TotalNetFee"].(float64)
		totalSuccessAmount := successAmount - successFee - successNetFee

		// 合并统计结果
		totalData := map[string]interface{}{
			"TotalCount":         (*allResult)[0]["TotalCount"],           // 总订单数
			"TotalUserCount":     (*allResult)[0]["TotalUserCount"],       // 总用户数
			"TotalAmount":        (*allResult)[0]["TotalAmount"],          // 发起提现的金额（所有符合条件的订单）
			"TotalSuccessAmount": totalSuccessAmount,                      // 提现到账总金额（成功提现金额-手续费）
			"TotalFee":           (*successResult)[0]["TotalFee"],         // 成功提币总手续费
			"TotalNetFee":        (*successResult)[0]["TotalNetFee"],      // 成功提币总网络手续费
			"SuccessUserCount":   (*successResult)[0]["SuccessUserCount"], // 成功提币用户数
		}

		if reqdata.Export != 1 {
			ctx.Put("totaldata", totalData)
		} else {
			xlsx.SetValue("Id", "合计", totalSize+2)
			xlsx.SetValue("Amount", (*successResult)[0]["SuccessAmount"], totalSize+2)
			xlsx.SetValue("Fee", (*successResult)[0]["TotalFee"], totalSize+2)
			// Excel中的PayoutAmount显示提现到账总金额
			xlsx.SetValue("PayoutAmount", totalSuccessAmount, totalSize+2)
			xlsx.SetValueStyle(totalSize + 2)
		}
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	server.WriteAdminLog("提币记录列表", ctx, reqdata)
	ctx.RespOK()
}

func (c *WalletController) withdrawaudit(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Ids        []int32
		State      int
		Memo       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.State == 1 || reqdata.State == 2 {
		if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "提币审核"), &errcode, "权限不足") {
			return
		}
	}
	if reqdata.State == 4 {
		if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "提币发放"), &errcode, "权限不足") {
			return
		}
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErrString(reqdata.State != 1 && reqdata.State != 2 && reqdata.State != 4 && reqdata.State != 8, &errcode, "状态不正确") {
		return
	}
	// 将withdraws变量声明移到最外层
	var withdraws []*model.XWithdraw

	if reqdata.State == 1 || reqdata.State == 2 {
		withdrawTb := server.DaoxHashGame().XWithdraw
		withdrawDb := server.DaoxHashGame().XWithdraw.WithContext(context.Background())
		var err error
		withdraws, err = withdrawDb.Select(withdrawTb.ID, withdrawTb.UserID, withdrawTb.Amount, withdrawTb.LockState, withdrawTb.LockUserID).Where(withdrawTb.ID.In(reqdata.Ids...)).Find()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		for _, withdraw := range withdraws {
			if withdraw.LockState == 1 {
				if withdraw.LockUserID != int32(token.UserId) {
					ctx.RespErrString(true, &errcode, "无法审核其他人锁定的单")
					return
				}
			}
		}
	}
	for i := 0; i < len(reqdata.Ids); i++ {
		server.Db().CallProcedure("x_admin_withdraw_audit", reqdata.Ids[i], reqdata.State, reqdata.Memo, token.Account)
	}
	// 如果是拒绝提现，则循环发送消息
	if reqdata.State == 1 && len(withdraws) > 0 {
		// 创建消息发送服务
		messageService := msg.NewSendMessageAPIService()
		// 循环遍历withdraws，为每个提现记录发送消息
		for _, withdraw := range withdraws {
			// 触发发送消息（使用withdraw.UserID作为接收者ID）
			err := messageService.SendAccountMessage(msg.TemplateTypeWithdrawFail, int(withdraw.UserID), withdraw.Amount, nil)
			if err != nil {
				logs.Error("发送消息失败: ", " userId=", withdraw.UserID, " id=", withdraw.ID, " err=", err)
			}
		}
	}
	server.WriteAdminLog("提币审核", ctx, reqdata)

	// 2通过 1拒绝
	ctx.RespOK()
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}

func (c *WalletController) withdraw_mannal(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int `validate:"required"`

		Id         int    `validate:"required"`
		Symbol     string `validate:"required"`
		Net        string `validate:"required"`
		TxId       string `validate:"required"`
		Rate       string `validate:"required"`
		RealAmount string `validate:"required"`
		Memo       string `validate:"required"`

		GoogleCode string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "钱包管理", "提币记录", "提币发放", "人工转账发放")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tb := server.DaoxHashGame().XWithdraw
	order, err := tb.WithContext(context.Background()).Where(tb.ID.Eq(int32(reqdata.Id))).First()
	if err != nil {
		logs.Info("withdraw_mannal ", err)
	}
	if order == nil {
		ctx.RespErrString(true, &errcode, "订单不存在")
		return
	}
	if order.State != 2 {
		ctx.RespErrString(true, &errcode, "订单状态不对")
		return
	}
	amount, err := decimal.NewFromString(reqdata.RealAmount)
	if err != nil {
		ctx.RespErrString(true, &errcode, "RealAmount 不对")
		return
	}
	result, err := tb.WithContext(context.Background()).Where(tb.ID.Eq(int32(reqdata.Id))).Where(tb.State.Eq(2)).UpdateColumnSimple(
		tb.Symbol.Value(reqdata.Symbol),
		tb.Net.Value(reqdata.Net),
		tb.Txid.Value(reqdata.TxId),
		tb.State.Value(9),
		tb.Memo.Value(reqdata.Memo),
		tb.RealAmount.Value(amount.InexactFloat64()),
	)
	if result.RowsAffected != 1 {
		if err != nil {
			ctx.RespErrString(true, &errcode, "update 不对 "+err.Error())
			return
		}
		ctx.RespErrString(true, &errcode, "update 失败 "+abugo.GetStringFromInterface(result.RowsAffected))
		return
	}

	ctx.RespOK()
}

func (c *WalletController) symbols(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "钱包管理", "提币记录", "查", "币种查询")
	if token == nil {
		return
	}
	tb := server.DaoxHashGame().XFinanceSymbol
	finds, err := tb.WithContext(context.Background()).Where(tb.FType.Value(2)).Select(tb.Symbol.Distinct()).Find()
	if err != nil {
		logs.Info("symbols ", err)
	}
	ctx.RespOK(finds)
}

func (c *WalletController) binded_address(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		UserId    int
		Address   string
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "钱包管理", "绑定地址", "查", "绑定地址")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	total, presult := server.Db().Table("x_verified_address").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", presult)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 钱包张变记录的接口
func (c *WalletController) changelog(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   []int32
		ChannelId  []int
		UserId     int
		Reason     []int
		StartTime  int64
		EndTime    int64
		Export     int //0表示分页查询，1表示导出报表
		AmountType int // 1=真金，2=Bonus币，默认为1或0
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndTokenJudgeSellerId(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, reqdata.SellerId, "钱包管理", "账变记录", "查", "账变记录")
	if token == nil {
		return
	}
	// AmountType 前端没传时默认=1或0
	if reqdata.AmountType < 0 {
		reqdata.AmountType = 1
	}
	if reqdata.StartTime < 1 {
		reqdata.StartTime = carbon.Parse("2023-02-08 01:00:00").TimestampMilli()
	}
	if reqdata.EndTime < 1 {
		reqdata.EndTime = carbon.Now().TimestampMilli()
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}
	db := server.Db().GormDao()
	sub := db.Table("x_amount_change_log").Where("CreateTime >= ? and CreateTime < ?", carbon.CreateFromTimestampMilli(reqdata.StartTime).String(),
		carbon.CreateFromTimestampMilli(reqdata.EndTime).String())

	// 根据 AmountType 筛选
	//if reqdata.AmountType == 0 || reqdata.AmountType == 1 {
	//	// 前端没传参数或传入1时，查询 AmountType IN (0,1)
	//	sub = sub.Where("AmountType IN (0,1)")
	//} else if reqdata.AmountType == 2 {
	//	// 前端传了 2时，查询 BonusAmount 不为0且不为null
	//	sub = sub.Where("BonusAmount IS NOT NULL AND BonusAmount != 0")
	//}

	if reqdata.UserId > 0 {
		sub = sub.Where("UserId = ?", reqdata.UserId)
	}
	sub = sub.Clauses(hints.ForceIndex("idx_CreateTime_UserId"))
	where := abugo.AbuDbWhere{}
	sellerIdStr := utils.ToSellers(reqdata.SellerId)
	if len(sellerIdStr) > 0 {
		where.Add("and", "SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
	}
	channelIdStr := utils.ToChannels(reqdata.ChannelId)
	if len(channelIdStr) > 0 {
		where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelIdStr), 0)
	}
	strreason := "("
	for i := 0; i < len(reqdata.Reason); i++ {
		if i < len(reqdata.Reason)-1 {
			strreason += fmt.Sprintf("%d,", reqdata.Reason[i])
		} else {
			strreason += fmt.Sprintf("%d", reqdata.Reason[i])
		}
	}
	strreason += ")"
	if len(reqdata.Reason) > 0 {
		where.Add("and", "Reason", "in", strreason, nil)
	}
	sql, args := where.Sql()
	f := func() *gorm.DB {
		do := db.Table("(?) as t", sub)
		if len(sql) > 0 {
			do = do.Where(sql, args...)
		}
		return do
	}

	// 定义结果结构，增加Bonus相关字段
	var results []*model.XAmountChangeLog
	totalResult := struct {
		TotalAmount            float64
		TotalBeforeAmount      float64
		TotalAfterAmount       float64
		TotalBonusAmount       float64
		TotalBeforeBonusAmount float64
		TotalAfterBonusAmount  float64
		Total                  int64
	}{}

	if reqdata.Export != 1 {
		do := f()
		// 更新统计查询，增加Bonus相关字段
		do.Select("IFNULL(sum(Amount),0) as TotalAmount,IFNULL(sum(BeforeAmount),0) as TotalBeforeAmount,IFNULL(sum(AfterAmount),0) as TotalAfterAmount, " +
			"IFNULL(sum(BonusAmount),0) as TotalBonusAmount,IFNULL(sum(BeforeBonusAmount),0) as TotalBeforeBonusAmount,IFNULL(sum(AfterBonusAmount),0) as TotalAfterBonusAmount, " +
			"count(*) as Total").Scan(&totalResult)
		if totalResult.Total > 0 {
			do.Select("*").Order("CreateTime desc").Limit(reqdata.PageSize).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&results)
		}

		// 获取用户ID列表
		var userIds []int32
		for _, result := range results {
			userIds = append(userIds, result.UserID)
		}

		// 查询顶级代理ID
		topAgentMap := make(map[int32]int32)
		if len(userIds) > 0 {
			// 构建IN查询
			userIdsStr := make([]string, len(userIds))
			for i, id := range userIds {
				userIdsStr[i] = fmt.Sprintf("%d", id)
			}

			query := fmt.Sprintf("UserId IN (%s)", strings.Join(userIdsStr, ","))

			type UserTopAgent struct {
				UserId     int32
				TopAgentId int32
			}

			var userTopAgents []UserTopAgent
			err := db.Table("x_user").Where(query).
				Select("UserId, TopAgentId").
				Scan(&userTopAgents).Error

			if err == nil {
				for _, user := range userTopAgents {
					topAgentMap[user.UserId] = user.TopAgentId
				}
			}
		}

		// 构建返回数据，保持原始格式
		var data []map[string]string
		for _, result := range results {
			item := utils.StructToMap(*result)
			// 添加顶级代理ID
			item["TopAgentId"] = fmt.Sprintf("%v", topAgentMap[result.UserID])
			data = append(data, item)
		}

		ctx.Put("total", totalResult.Total)
		ctx.Put("data", data)
		ctx.Put("totalamount", totalResult)
	} else {
		reqdata.Page = 1
		reqdata.PageSize = 100000
		do := f()
		// 更新统计查询，增加Bonus相关字段
		do.Select("IFNULL(sum(Amount),0) as TotalAmount,IFNULL(sum(BeforeAmount),0) as TotalBeforeAmount,IFNULL(sum(AfterAmount),0) as TotalAfterAmount, " +
			"IFNULL(sum(BonusAmount),0) as TotalBonusAmount,IFNULL(sum(BeforeBonusAmount),0) as TotalBeforeBonusAmount,IFNULL(sum(AfterBonusAmount),0) as TotalAfterBonusAmount, " +
			"count(*) as Total").Scan(&totalResult)
		if totalResult.Total > 0 {
			do.Select("*").Order("CreateTime desc").Limit(reqdata.PageSize).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&results)
		}

		// 获取用户ID列表
		var userIds []int32
		for _, result := range results {
			userIds = append(userIds, result.UserID)
		}

		// 查询顶级代理ID
		topAgentMap := make(map[int32]int32)
		if len(userIds) > 0 {
			// 构建IN查询
			userIdsStr := make([]string, len(userIds))
			for i, id := range userIds {
				userIdsStr[i] = fmt.Sprintf("%d", id)
			}

			query := fmt.Sprintf("UserId IN (%s)", strings.Join(userIdsStr, ","))

			type UserTopAgent struct {
				UserId     int32
				TopAgentId int32
			}

			var userTopAgents []UserTopAgent
			err := db.Table("x_user").
				Where(query).
				Select("UserId, TopAgentId").
				Scan(&userTopAgents).Error

			if err == nil {
				for _, user := range userTopAgents {
					topAgentMap[user.UserId] = user.TopAgentId
				}
			}
		}

		xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_账变记录_%s", time.Now().Format("**************")))
		defer xlsx.Close()
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("SellerId", "运营商")
		xlsx.SetTitle("Id", "ID")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("TopAgentId", "顶级代理ID") // 添加顶级代理ID表头
		xlsx.SetTitle("Reason", "类型")
		xlsx.SetTitle("Amount", "变化值")
		xlsx.SetTitle("BeforeAmount", "变化前")
		xlsx.SetTitle("AfterAmount", "变化后")
		// 添加Bonus相关表头
		xlsx.SetTitle("BonusAmount", "Bonus变化值")
		xlsx.SetTitle("BeforeBonusAmount", "Bonus变化前")
		xlsx.SetTitle("AfterBonusAmount", "Bonus变化后")
		xlsx.SetTitle("Memo", "备注")
		xlsx.SetTitle("CreateTime", "时间")
		xlsx.SetTitleStyle()
		xlsx.SetColumnWidth("SellerId", 20)
		xlsx.SetColumnWidth("Id", 20)
		xlsx.SetColumnWidth("ChannelId", 20)
		xlsx.SetColumnWidth("UserId", 20)
		xlsx.SetColumnWidth("TopAgentId", 20) // 设置顶级代理ID列宽
		xlsx.SetColumnWidth("Reason", 20)
		xlsx.SetColumnWidth("Amount", 20)
		xlsx.SetColumnWidth("BeforeAmount", 20)
		xlsx.SetColumnWidth("AfterAmount", 20)
		// 设置Bonus相关列宽
		xlsx.SetColumnWidth("BonusAmount", 20)
		xlsx.SetColumnWidth("BeforeBonusAmount", 20)
		xlsx.SetColumnWidth("AfterBonusAmount", 20)
		xlsx.SetColumnWidth("Memo", 20)
		xlsx.SetColumnWidth("CreateTime", 20)
		// 从帐变类型中读取
		changetypeTb := server.DaoxHashGame().XDictChangetype
		changetypeDb := server.DaoxHashGame().XDictChangetype.WithContext(context.Background())
		changetypes, err := changetypeDb.Select(changetypeTb.ChangeType, changetypeTb.ChangeName).Find()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		changetypesMap := make(map[int64]string, len(changetypes))
		for _, types := range changetypes {
			changetypesMap[types.ChangeType] = types.ChangeName
		}
		sellerNameMap, err := InitSellerNameMap()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		for i := 0; i < len(results); i++ {
			stringMap := utils.StructToMap(*results[i])
			// logs.Info(i, *results[i], stringMap)
			for k, v := range stringMap {
				if k == "ChannelId" {
					xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
				} else if k == "SellerId" {
					xlsx.SetValue(k, SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(v))), int64(i+2))
				} else if k == "Reason" {
					//xlsx.SetValue(k, getAmountChangelogReason(abugo.GetInt64FromInterface(v)), int64(i+2))
					xlsx.SetValue(k, changetypesMap[abugo.GetInt64FromInterface(v)], int64(i+2))
				} else if k == "UserId" {
					xlsx.SetValue(k, abugo.GetInt64FromInterface(v), int64(i+2))
				} else if k == "Amount" || k == "BeforeAmount" || k == "AfterAmount" ||
					k == "BonusAmount" || k == "BeforeBonusAmount" || k == "AfterBonusAmount" {
					xlsx.SetValue(k, abugo.GetFloat64FromInterface(v), int64(i+2))
				} else {
					xlsx.SetValue(k, v, int64(i+2))
				}
			}
			// 添加顶级代理ID
			userId := results[i].UserID
			topAgentId := topAgentMap[userId]
			xlsx.SetValue("TopAgentId", topAgentId, int64(i+2))
		}
		totalSize := int64(len(results))
		xlsx.SetValue("Id", "合计", totalSize+2)
		xlsx.SetValue("Amount", totalResult.TotalAmount, totalSize+2)
		xlsx.SetValue("BeforeAmount", totalResult.TotalBeforeAmount, totalSize+2)
		xlsx.SetValue("AfterAmount", totalResult.TotalAfterAmount, totalSize+2)
		// 添加Bonus相关合计
		xlsx.SetValue("BonusAmount", totalResult.TotalBonusAmount, totalSize+2)
		xlsx.SetValue("BeforeBonusAmount", totalResult.TotalBeforeBonusAmount, totalSize+2)
		xlsx.SetValue("AfterBonusAmount", totalResult.TotalAfterBonusAmount, totalSize+2)
		xlsx.SetValueStyle(totalSize + 2)

		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
}

// report 充提报表查询接口
// 支持三种查询模式：
// 1. 按运营商查询：展示各渠道的充提数据和顶级代理数量
// 2. 按渠道查询：展示该渠道下各顶级代理的充提数据
// 3. 按顶级代理查询：展示指定顶级代理的充提数据
func (c *WalletController) report(ctx *abugo.AbuHttpContent) {
	defer recover()
	// 请求参数结构体
	type RequestData struct {
		Page         int     // 页码
		PageSize     int     // 每页数量
		SellerId     []int32 // 运营商ID列表
		ChannelId    []int   // 渠道ID列表
		TopAgentId   []int   // 顶级代理ID列表
		StartTime    int64   // 开始时间戳
		EndTime      int64   // 结束时间戳
		Export       int     // 导出标识：0-分页查询，1-导出报表
		SpecialAgent int     // 特殊代理标识
		HostTagId    int     // 标签ID
	}

	errcode := 0
	reqdata := RequestData{}
	// 权限验证
	token := server.GetRequestDataAndTokenJudgeSellerId(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, reqdata.SellerId, "钱包管理", "充提报表", "查", "查询充提统计")
	if token == nil {
		return
	}
	logs.Info("reqdata.SellerId length:", len(reqdata.SellerId))

	// 判断必须选择时间范围
	if reqdata.StartTime <= 0 {
		ctx.RespErrString(true, &errcode, "请选择开始时间")
		return
	}
	if reqdata.EndTime <= 0 {
		ctx.RespErrString(true, &errcode, "请选择结束时间")
		return
	}

	// 判断开始时间必须小于结束时间（转换为秒级时间戳比较）
	startTimeSeconds := reqdata.StartTime / 1000
	endTimeSeconds := reqdata.EndTime / 1000
	if startTimeSeconds > endTimeSeconds {
		ctx.RespErrString(true, &errcode, "开始时间必须小于结束时间")
		return
	}

	// 判断必须选择查询条件
	if len(reqdata.SellerId) == 0 && len(reqdata.ChannelId) == 0 && len(reqdata.TopAgentId) == 0 {
		// 返回空数据和默认汇总数据
		ctx.Put("data", []interface{}{})
		ctx.Put("total", 0)
		ctx.Put("totaldata", map[string]interface{}{
			"RechargeCount":        0,   // 充值笔数
			"RechargeAmount":       0.0, // 充值金额
			"WithdrawCount":        0,   // 提现笔数
			"WithdrawAmount":       0.0, // 提现金额
			"WithdrawFee":          0.0, // 提现手续费
			"WithdrawPayoutAmount": 0.0, // 提现到账金额
			"RechargeUserCount":    0,   // 充值人数
			"WithdrawUserCount":    0,   // 提现人数
			"FirstUserCount":       0,   // 首充人数
			"FirstRechargeAmount":  0.0, // 首充金额
		})
		ctx.RespOK()
		return
	}

	// 渠道后台只能查看自己的数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}

	// 构建基础SQL
	var baseSql string
	var params []interface{}
	startDate := time.Unix(reqdata.StartTime/1000, 0).Format("2006-01-02")
	endDate := time.Unix(reqdata.EndTime/1000, 0).Format("2006-01-02")

	// 根据传入参数判断使用哪种查询模式
	if len(reqdata.TopAgentId) > 0 {
		// 按顶级代理查询
		// 生成占位符和参数
		topAgentIdStr := ""
		for i, id := range reqdata.TopAgentId {
			if i > 0 {
				topAgentIdStr += ","
			}
			topAgentIdStr += "?"
			params = append(params, id) // 添加到参数列表
		}

		baseSql = `SELECT
			DATE_FORMAT(U.RecordDate, '%Y-%m-%d') as RecordDate,
			U.SellerId,
			U.ChannelId,
			U.TopAgentId AS TopAgentId,
			U.RechargeUserCount,
			U.RechargeCount,
			U.RechargeAmount,
			U.WithdrawUserCount,
			U.WithdrawCount,
			U.WithdrawAmount,
			U.WithdrawFee,
			E.TagName,
			(U.WithdrawAmount - U.WithdrawFee) as WithdrawPayoutAmount,
			IFNULL(T.FirstUserCount,0) AS FirstUserCount,
			IFNULL(T.FirstRechargeAmount,0) AS FirstRechargeAmount
		FROM (
			SELECT
				DATE_FORMAT(RecordDate, '%Y-%m-%d') as RecordDate,
				SellerId,
				IFNULL(NULLIF(TopAgentId,0),UserId) AS TopAgentId,
				ChannelId,
				COUNT(DISTINCT CASE WHEN RechargeCount>0 THEN UserId END) AS RechargeUserCount,
				IFNULL(SUM(RechargeCount),0) AS RechargeCount,
				IFNULL(SUM(RechargeAmount),0) AS RechargeAmount,
				COUNT(DISTINCT CASE WHEN WithdrawCount>0 THEN UserId END) AS WithdrawUserCount,
				IFNULL(SUM(WithdrawCount),0) AS WithdrawCount,
				IFNULL(SUM(WithdrawAmount),0) AS WithdrawAmount,
				IFNULL(SUM(WithdrawFee),0) AS WithdrawFee
			FROM x_user_recharge_withard_date
			WHERE IFNULL(NULLIF(TopAgentId,0),UserId) IN (` + topAgentIdStr + `)
			AND RecordDate>=? AND RecordDate<=?
			GROUP BY RecordDate,SellerId,IFNULL(NULLIF(TopAgentId,0),UserId),ChannelId
		) AS U
		LEFT JOIN (
			SELECT
				DATE_FORMAT(FirstRechargeTime,'%Y-%m-%d') AS RecordDate,
				SellerId,
				IFNULL(NULLIF(TopAgentId,0),UserId) AS TopAgentId,
				COUNT(DISTINCT UserId) AS FirstUserCount,
				IFNULL(SUM(FirstRechargeAmount),0) AS FirstRechargeAmount
			FROM x_user_recharge_withard
			WHERE IFNULL(NULLIF(TopAgentId,0),UserId) IN (` + topAgentIdStr + `)
			AND FirstRechargeTime>=? AND FirstRechargeTime<TIMESTAMPADD(DAY,1,?)
			GROUP BY DATE_FORMAT(FirstRechargeTime,'%Y-%m-%d'),SellerId,IFNULL(NULLIF(TopAgentId,0),UserId)
		) AS T
			ON U.RecordDate=T.RecordDate
			AND U.TopAgentId=T.TopAgentId
			AND U.SellerId=T.SellerId
		LEFT JOIN x_user AS D
			ON U.TopAgentId=D.UserId
		LEFT JOIN x_channel_host AS H
			ON D.RegUrl=H.Host
		LEFT JOIN x_host_tag AS E
			ON H.HostTagId=E.Id`

		// 添加HostTagId条件（如果不为0）
		if reqdata.HostTagId != 0 {
			baseSql += `
		WHERE E.Id = ?`
		}

		baseSql += `
		ORDER BY U.RecordDate DESC`

		// 添加时间参数
		params = append(params, startDate, endDate)
		// 再次添加 TopAgentId 参数（因为 IN 子句出现了两次）
		for _, id := range reqdata.TopAgentId {
			params = append(params, id)
		}
		// 添加最后的时间参数
		params = append(params, startDate, endDate)
		// 添加HostTagId参数（如果不为0）
		if reqdata.HostTagId != 0 {
			params = append(params, reqdata.HostTagId)
		}
	} else if len(reqdata.ChannelId) > 0 {
		// 按渠道查询
		// 生成占位符和参数
		channelIdStr := ""
		for i, id := range reqdata.ChannelId {
			if i > 0 {
				channelIdStr += ","
			}
			channelIdStr += "?"
			params = append(params, id) // 添加到参数列表
		}

		baseSql = `SELECT
			DATE_FORMAT(U.RecordDate, '%Y-%m-%d') as RecordDate,
			U.SellerId,
			U.ChannelId,
			U.TopAgentId,
			U.RechargeUserCount,
			U.RechargeCount,
			U.RechargeAmount,
			U.WithdrawUserCount,
			U.WithdrawCount,
			U.WithdrawAmount,
			U.WithdrawFee,
			E.TagName,
			(U.WithdrawAmount - U.WithdrawFee) as WithdrawPayoutAmount,
			IFNULL(T.FirstUserCount,0) AS FirstUserCount,
			IFNULL(T.FirstRechargeAmount,0) AS FirstRechargeAmount
		FROM (
			SELECT
				DATE_FORMAT(RecordDate, '%Y-%m-%d') as RecordDate,
				SellerId,
				IFNULL(NULLIF(TopAgentId,0),UserId) AS TopAgentId,
				ChannelId,
				COUNT(DISTINCT CASE WHEN RechargeCount>0 THEN UserId END) AS RechargeUserCount,
				IFNULL(SUM(RechargeCount),0) AS RechargeCount,
				IFNULL(SUM(RechargeAmount),0) AS RechargeAmount,
				COUNT(DISTINCT CASE WHEN WithdrawCount>0 THEN UserId END) AS WithdrawUserCount,
				IFNULL(SUM(WithdrawCount),0) AS WithdrawCount,
				IFNULL(SUM(WithdrawAmount),0) AS WithdrawAmount,
				IFNULL(SUM(WithdrawFee),0) AS WithdrawFee
			FROM x_user_recharge_withard_date
			WHERE ChannelId IN (` + channelIdStr + `)
			AND RecordDate >= ?
			AND RecordDate <= ?
			GROUP BY RecordDate,SellerId,IFNULL(NULLIF(TopAgentId,0),UserId),ChannelId
		) AS U
		LEFT JOIN (
			SELECT
				DATE_FORMAT(FirstRechargeTime,'%Y-%m-%d') AS RecordDate,
				SellerId,
					IFNULL(NULLIF(TopAgentId,0),UserId) AS TopAgentId,
				ChannelId,
				COUNT(DISTINCT UserId) AS FirstUserCount,
				IFNULL(SUM(FirstRechargeAmount),0) AS FirstRechargeAmount
			FROM x_user_recharge_withard
			WHERE ChannelId IN (` + channelIdStr + `)
			AND FirstRechargeTime >= ?
			AND FirstRechargeTime < TIMESTAMPADD(DAY,1,?)
			GROUP BY DATE_FORMAT(FirstRechargeTime,'%Y-%m-%d'),SellerId,IFNULL(NULLIF(TopAgentId,0),UserId),ChannelId
		) AS T
			ON U.RecordDate = T.RecordDate
			AND U.TopAgentId = T.TopAgentId
			AND U.ChannelId = T.ChannelId
			AND U.SellerId = T.SellerId
		LEFT JOIN x_user AS D
			ON U.TopAgentId=D.UserId
		LEFT JOIN x_channel_host AS H
			ON D.RegUrl=H.Host
		LEFT JOIN x_host_tag AS E
			ON H.HostTagId = E.Id`

		// 添加HostTagId条件（如果不为0）
		if reqdata.HostTagId != 0 {
			baseSql += `
		WHERE E.Id = ?`
		}

		baseSql += `
		ORDER BY U.RecordDate DESC`

		// 添加时间参数
		params = append(params, startDate, endDate)
		// 再次添加 ChannelId 参数（因为 IN 子句出现了两次）
		for _, id := range reqdata.ChannelId {
			params = append(params, id)
		}
		// 添加最后的时间参数
		params = append(params, startDate, endDate)
		// 添加HostTagId参数（如果不为0）
		if reqdata.HostTagId != 0 {
			params = append(params, reqdata.HostTagId)
		}
	} else if len(reqdata.SellerId) > 0 {
		// 按运营商查询
		// 生成占位符和参数
		sellerIdStr := ""
		for i, id := range reqdata.SellerId {
			if i > 0 {
				sellerIdStr += ","
			}
			sellerIdStr += "?"
			params = append(params, id) // 添加到参数列表
		}

		baseSql = `SELECT
			DATE_FORMAT(U.RecordDate, '%Y-%m-%d') as RecordDate,
			U.SellerId,
			U.ChannelId,
			C.TopAgentCount AS TopAgentCount,
			U.RechargeUserCount,
			U.RechargeCount,
			U.RechargeAmount,
			U.WithdrawUserCount,
			U.WithdrawCount,
			U.WithdrawAmount,
			U.WithdrawFee,
			(U.WithdrawAmount - U.WithdrawFee) as WithdrawPayoutAmount,
			IFNULL(T.FirstUserCount,0) AS FirstUserCount,
			IFNULL(T.FirstRechargeAmount,0) AS FirstRechargeAmount
		FROM (
			SELECT
				DATE_FORMAT(RecordDate, '%Y-%m-%d') as RecordDate,
				SellerId,
				ChannelId,
				COUNT(DISTINCT CASE WHEN RechargeCount>0 THEN UserId END) AS RechargeUserCount,
				IFNULL(SUM(RechargeCount),0) AS RechargeCount,
				IFNULL(SUM(RechargeAmount),0) AS RechargeAmount,
				COUNT(DISTINCT CASE WHEN WithdrawCount>0 THEN UserId END) AS WithdrawUserCount,
				IFNULL(SUM(WithdrawCount),0) AS WithdrawCount,
				IFNULL(SUM(WithdrawAmount),0) AS WithdrawAmount,
				IFNULL(SUM(WithdrawFee),0) AS WithdrawFee
			FROM x_user_recharge_withard_date
			WHERE SellerId IN (` + sellerIdStr + `)
			AND RecordDate>=? AND RecordDate<=?
			GROUP BY RecordDate,SellerId,ChannelId
		) AS U
		INNER JOIN (
			SELECT
				SellerId,
				ChannelId,
				COUNT(1) AS TopAgentCount
			FROM x_user
			WHERE SellerId IN (` + sellerIdStr + `)
			GROUP BY SellerId,ChannelId
		) AS C
			ON U.ChannelId=C.ChannelId
			AND U.SellerId=C.SellerId
		LEFT JOIN (
			SELECT
				DATE_FORMAT(FirstRechargeTime,'%Y-%m-%d') AS RecordDate,
				SellerId,
				ChannelId,
				COUNT(DISTINCT UserId) AS FirstUserCount,
				IFNULL(SUM(FirstRechargeAmount),0) AS FirstRechargeAmount
			FROM x_user_recharge_withard
			WHERE SellerId IN (` + sellerIdStr + `)
			AND FirstRechargeTime>=? AND FirstRechargeTime<TIMESTAMPADD(DAY,1,?)
			GROUP BY DATE_FORMAT(FirstRechargeTime,'%Y-%m-%d'),SellerId,ChannelId
		) AS T
			ON U.RecordDate=T.RecordDate
			AND U.ChannelId=T.ChannelId
			AND U.SellerId=T.SellerId`

		// 添加HostTagId条件（如果不为0）
		baseSql += `
		ORDER BY U.RecordDate DESC`

		// 添加时间参数
		params = append(params, startDate, endDate)
		// 再次添加 SellerId 参数（因为 IN 子句出现了三次）
		for _, id := range reqdata.SellerId {
			params = append(params, id)
		}
		for _, id := range reqdata.SellerId {
			params = append(params, id)
		}
		// 添加最后的时间参数
		params = append(params, startDate, endDate)
	}

	// 执行查询
	var totalSql string
	var queryParams []interface{}
	if len(reqdata.TopAgentId) > 0 || len(reqdata.ChannelId) > 0 || len(reqdata.SellerId) > 0 {
		totalSql = baseSql
		queryParams = make([]interface{}, len(params))
		copy(queryParams, params)

		// 如果不是导出，则添加分页
		if reqdata.Export != 1 {
			baseSql += ` LIMIT ?, ?`
			params = append(params, (reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
		}
	}

	// 先查询总数
	totalDbr, err := server.Db().Conn().Query(totalSql, queryParams...)
	if err != nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("查询失败: %v", err))
		return
	}
	totalResult := abugo.GetResult(totalDbr)
	total := 0
	if totalResult != nil {
		total = len(*totalResult)
	}

	// 检查导出数据量
	if reqdata.Export == 1 && total > 10000 {
		ctx.Put("data", []interface{}{})
		ctx.Put("total", total)
		ctx.Put("totaldata", map[string]interface{}{
			"RechargeCount":        0,   // 充值笔数
			"RechargeAmount":       0.0, // 充值金额
			"WithdrawCount":        0,   // 提现笔数
			"WithdrawAmount":       0.0, // 提现金额
			"WithdrawFee":          0.0, // 提现手续费
			"WithdrawPayoutAmount": 0.0, // 提现到账金额
			"RechargeUserCount":    0,   // 充值人数
			"WithdrawUserCount":    0,   // 提现人数
			"FirstUserCount":       0,   // 首充人数
			"FirstRechargeAmount":  0.0, // 首充金额
		})
		ctx.RespErrString(true, &errcode, fmt.Sprintf("导出数据超过10000条（当前%d条），请缩小时间范围重新筛选", total))
		return
	}

	var result *[]map[string]interface{}
	if reqdata.Export == 1 {
		// 导出时使用总数据
		result = totalResult
	} else {
		// 执行分页查询
		logs.Debug("执行分页查询: %s %+v", baseSql, params)
		dbr, err := server.Db().Conn().Query(baseSql, params...)
		if err != nil {
			ctx.RespErrString(true, &errcode, fmt.Sprintf("查询失败: %v", err))
			return
		}
		result = abugo.GetResult(dbr)
	}

	// 处理查询结果
	if result == nil || len(*result) == 0 {
		// 无数据时返回空结果和默认汇总数据
		ctx.Put("data", []interface{}{})
		ctx.Put("total", 0)
		ctx.Put("totaldata", map[string]interface{}{
			"RechargeCount":        0,   // 充值笔数
			"RechargeAmount":       0.0, // 充值金额
			"WithdrawCount":        0,   // 提现笔数
			"WithdrawAmount":       0.0, // 提现金额
			"WithdrawFee":          0.0, // 提现手续费
			"WithdrawPayoutAmount": 0.0, // 提现到账金额
			"RechargeUserCount":    0,   // 充值人数
			"WithdrawUserCount":    0,   // 提现人数
			"FirstUserCount":       0,   // 首充人数
			"FirstRechargeAmount":  0.0, // 首充金额
		})
	} else {
		// 计算汇总数据（使用总数据计算）
		totalData := map[string]interface{}{
			"RechargeCount":        0,   // 充值笔数
			"RechargeAmount":       0.0, // 充值金额
			"WithdrawCount":        0,   // 提现笔数
			"WithdrawAmount":       0.0, // 提现金额
			"WithdrawFee":          0.0, // 提现手续费
			"WithdrawPayoutAmount": 0.0, // 提现到账金额
			"RechargeUserCount":    0,   // 充值人数
			"WithdrawUserCount":    0,   // 提现人数
			"FirstUserCount":       0,   // 首充人数
			"FirstRechargeAmount":  0.0, // 首充金额
		}

		// 使用总查询结果计算汇总数据
		for _, row := range *totalResult {
			totalData["RechargeCount"] = totalData["RechargeCount"].(int) + int(abugo.GetInt64FromInterface(row["RechargeCount"]))
			totalData["RechargeAmount"] = totalData["RechargeAmount"].(float64) + abugo.GetFloat64FromInterface(row["RechargeAmount"])
			totalData["WithdrawCount"] = totalData["WithdrawCount"].(int) + int(abugo.GetInt64FromInterface(row["WithdrawCount"]))
			totalData["WithdrawAmount"] = totalData["WithdrawAmount"].(float64) + abugo.GetFloat64FromInterface(row["WithdrawAmount"])
			totalData["WithdrawFee"] = totalData["WithdrawFee"].(float64) + abugo.GetFloat64FromInterface(row["WithdrawFee"])
			totalData["WithdrawPayoutAmount"] = totalData["WithdrawPayoutAmount"].(float64) + abugo.GetFloat64FromInterface(row["WithdrawPayoutAmount"])
			totalData["RechargeUserCount"] = totalData["RechargeUserCount"].(int) + int(abugo.GetInt64FromInterface(row["RechargeUserCount"]))
			totalData["WithdrawUserCount"] = totalData["WithdrawUserCount"].(int) + int(abugo.GetInt64FromInterface(row["WithdrawUserCount"]))
			totalData["FirstUserCount"] = totalData["FirstUserCount"].(int) + int(abugo.GetInt64FromInterface(row["FirstUserCount"]))
			totalData["FirstRechargeAmount"] = totalData["FirstRechargeAmount"].(float64) + abugo.GetFloat64FromInterface(row["FirstRechargeAmount"])
		}

		// 设置返回数据
		if reqdata.Export == 1 {
			// 导出时使用所有数据
			// 创建Excel文件
			xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_充提报表_%s", time.Now().Format("**************")))
			defer xlsx.Close()
			xlsx.Open()

			// 设置通用表头
			xlsx.SetTitle("RecordDate", "统计日期")
			xlsx.SetTitle("SellerId", "运营商")
			xlsx.SetTitle("TopAgentId", "顶级代理ID")
			xlsx.SetTitle("ChannelId", "渠道")
			xlsx.SetTitle("TopAgentCount", "顶级代理个数")
			xlsx.SetTitle("FirstUserCount", "首充人数")
			xlsx.SetTitle("FirstRechargeAmount", "首充金额")
			xlsx.SetTitle("RechargeUserCount", "充值人数")
			xlsx.SetTitle("RechargeCount", "充值笔数")
			xlsx.SetTitle("RechargeAmount", "充值金额")
			xlsx.SetTitle("WithdrawUserCount", "提现人数")
			xlsx.SetTitle("WithdrawCount", "提现笔数")
			xlsx.SetTitle("WithdrawAmount", "提现金额")
			xlsx.SetTitle("WithdrawFee", "提现手续费")
			xlsx.SetTitle("WithdrawPayoutAmount", "提现到账金额")
			xlsx.SetTitleStyle()

			// 设置列宽
			xlsx.SetColumnWidth("RecordDate", 15)
			xlsx.SetColumnWidth("SellerId", 15)
			xlsx.SetColumnWidth("TopAgentId", 15)
			xlsx.SetColumnWidth("ChannelId", 15)
			xlsx.SetColumnWidth("TopAgentCount", 15)
			xlsx.SetColumnWidth("FirstUserCount", 15)
			xlsx.SetColumnWidth("FirstRechargeAmount", 15)
			xlsx.SetColumnWidth("RechargeUserCount", 15)
			xlsx.SetColumnWidth("RechargeCount", 15)
			xlsx.SetColumnWidth("RechargeAmount", 15)
			xlsx.SetColumnWidth("WithdrawUserCount", 15)
			xlsx.SetColumnWidth("WithdrawCount", 15)
			xlsx.SetColumnWidth("WithdrawAmount", 15)
			xlsx.SetColumnWidth("WithdrawFee", 15)
			xlsx.SetColumnWidth("WithdrawPayoutAmount", 15)

			// 写入数据
			sellerNameMap, err := InitSellerNameMap()
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
				return
			}

			for i, row := range *totalResult {
				for k, v := range row {
					if k == "ChannelId" {
						// 转换渠道ID为渠道名称
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "SellerId" {
						// 转换运营商ID为运营商名称
						xlsx.SetValue(k, SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
			}

			// 写入汇总行
			totalSize := int64(len(*totalResult))
			xlsx.SetValue("RecordDate", "合计", totalSize+2)
			for k, v := range totalData {
				xlsx.SetValue(k, v, totalSize+2)
			}
			xlsx.SetValueStyle(totalSize + 2)

			// 生成文件
			filePath, err := xlsx.ProduceFile()
			if ctx.RespErr(err, &errcode) {
				return
			}
			ctx.Put("filename", "/exports/"+path.Base(filePath))
		} else {
			// 分页查询时使用分页数据
			ctx.Put("data", *result)
		}
		ctx.Put("total", total)         // 总记录数
		ctx.Put("totaldata", totalData) // 汇总数据
	}

	ctx.RespOK()
}

func (c *WalletController) changetype_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		ChangeName string
		Status     *int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "账变类型", "查", "查看帐变类型列表")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "ChangeName", "=", reqdata.ChangeName, "")
	where.Add("and", "ParentType", "=", 2001, 0)
	if reqdata.Status != nil {
		where.Add("and", "Status", "=", *reqdata.Status, nil)
	}
	total, presult := server.Db().Table("x_dict_changetype").Where(where).OrderBy("Sort desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", presult)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *WalletController) changetype_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChangeName string `validate:"required"`
		Remark     string `validate:"required"`
		GoogleCode string
		Sort       int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "账变类型", "增", "添加帐变类型")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.ChangeName = strings.TrimSpace(reqdata.ChangeName)
	presult, err := server.Db().CallProcedure("ConfigManage_x_dict_changetype_Insert", reqdata.ChangeName, reqdata.Sort, reqdata.Remark, token.Account, token.UserId, 4, ctx.GetIp())
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureCodeErr(presult) {
		return
	}
	ctx.RespOK()
}

func (c *WalletController) changetype_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChangeType int    `validate:"required"`
		Remark     string `validate:"required"`
		Status     int
		GoogleCode string
		Sort       int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "账变类型", "改", "修改帐变类型")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "ChangeType", "=", reqdata.ChangeType, nil)
	tdata, err := server.Db().Table("x_dict_changetype").Where(where).GetOne()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if tdata == nil {
		ctx.RespErrString(true, &errcode, "数据不存在")
		return
	}
	presult, err := server.Db().CallProcedure("ConfigManage_x_dict_changetype_UpdateStatus", reqdata.ChangeType, reqdata.Sort, reqdata.Status, reqdata.Remark, token.Account, token.UserId, 4, ctx.GetIp())
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureCodeErr(presult) {
		return
	}
	ctx.RespOK()
}

func (c *WalletController) changetype_search(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		Type     int `validate:"required"` // 1增资 2全部 3搜索
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	changetypeTb := server.DaoxHashGame().XDictChangetype
	changetypeDb := server.DaoxHashGame().XDictChangetype.WithContext(context.Background())
	if reqdata.Type == 1 { // 增资
		changetypes, err := changetypeDb.Where(changetypeTb.ParentType.Eq(2001), changetypeTb.Status.Eq(1)).Order(changetypeTb.Sort.Desc()).Find()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		ctx.Put("data", changetypes)
		ctx.RespOK()
	} else if reqdata.Type == 2 { // 2全部
		changetypes, err := changetypeDb.Find()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		ctx.Put("data", changetypes)
		ctx.RespOK()
	} else {
		changeParentypeTb := server.DaoxHashGame().XDictChangeParentype
		changeParentypeDb := server.DaoxHashGame().XDictChangeParentype.WithContext(context.Background())
		changeParentypeList, err := changeParentypeDb.Where(changeParentypeTb.Status.Eq(1)).Find()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		type resultData struct {
			*model.XDictChangeParentype
			Children []*model.XDictChangetype
		}
		changetypeList, err := changetypeDb.Where(changetypeTb.Status.Eq(1)).Find()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		data := make([]resultData, 0, len(changeParentypeList))
		for _, parentype := range changeParentypeList {
			var result resultData
			result.XDictChangeParentype = parentype
			for _, changetype := range changetypeList {
				if changetype.ParentType == parentype.ParentType {
					result.Children = append(result.Children, changetype)
				}
			}
			data = append(data, result)
		}
		ctx.Put("data", data)
		ctx.RespOK()
	}
}
func (c *WalletController) get_chain(ctx *abugo.AbuHttpContent) {
	defer recover()
	data := utils.SymbolToChain
	list := make([]utilsmodel.GetChain, 0, len(data))
	for k, v := range data {
		list = append(list, utilsmodel.GetChain{
			Name:  v,
			Value: k,
		})
	}
	ctx.Put("data", list)
	ctx.RespOK()
}

func (c *WalletController) withdrawLock(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Ids        []int32 `validate:"required"`
		SellerId   int
		State      int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "钱包管理", "提币记录", "锁定", "锁定提币")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	withdrawTb := server.DaoxHashGame().XWithdraw
	withdrawDb := server.DaoxHashGame().XWithdraw.WithContext(context.Background())
	if reqdata.SellerId > 0 {
		withdrawDb = withdrawDb.Where(withdrawTb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	withdrawList, err := withdrawDb.Where(withdrawTb.ID.In(reqdata.Ids...)).Find()
	if err != nil {
		logs.Error("withdrawLock withdrawDb err", err)
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
	}
	for _, withdraw := range withdrawList {
		if withdraw.State != 0 {
			if err != nil {
				ctx.RespErrString(true, &errcode, "待审核状态才能锁定")
				return
			}
		}
		if reqdata.State == 1 && withdraw.State == 1 {
			ctx.RespErrString(true, &errcode, "该订单已被锁定")
			return
		}
		if reqdata.State == 2 {
			if withdraw.LockUserID != int32(token.UserId) {
				if err != nil {
					ctx.RespErrString(true, &errcode, "不能解锁他人锁定的")
					return
				}
			}
		}
	}
	data := make(map[string]any)
	data["LockState"] = reqdata.State
	data["LockUserAccount"] = token.Account
	data["LockUserId"] = token.UserId
	data["LockTime"] = time.Now()
	if reqdata.State == 2 {
		data["LockUserId"] = 0
		data["LockUserAccount"] = ""
		data["LockTime"] = nil
		withdrawDb = withdrawDb.Where(withdrawTb.LockState.Eq(1))
	} else {
		withdrawDb = withdrawDb.Where(withdrawTb.LockState.Eq(2))
	}

	_, err = withdrawDb.Where(withdrawTb.ID.In(reqdata.Ids...)).Updates(data)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("提币记录锁定", ctx, reqdata)
}

func (c *WalletController) withdrawOrder(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Ids        []int32 `validate:"required"`
		SellerId   int
		State      int `validate:"required"`
		GoogleCode string
		Memo       string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "钱包管理", "提币记录", "锁定", "锁定提币")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	//withdrawTb := server.DaoxHashGame().XWithdraw
	//withdrawDb := server.DaoxHashGame().XWithdraw.WithContext(context.Background())

	upData := map[string]interface{}{
		"State": 0,
		"Memo":  reqdata.Memo,
	}
	//_, err := withdrawDb.Where(withdrawTb.ID.In(reqdata.Ids...)).
	//	Where(withdrawTb.State.Eq(5)).Updates(upData)

	// 构建条件：State=5 且 (PayType != 1 或 (PayType=1 且 Amount>100))
	//	strQuery := `
	//UPDATE x_withdraw
	//SET
	//    state = 0,
	//    memo = ?
	//WHERE
	//    id IN (?)
	//    AND state = 5
	//    AND (
	//        pay_type <> 1
	//        OR (pay_type = 1 AND amount > 100)
	//    )
	//`

	tdb := server.Db().GormDao().Table("x_withdraw")
	tdb.Where("Id IN (?)", reqdata.Ids)
	tdb.Where("State = ?", 5)
	tdb.Where("PayType <> 1 OR (PayType = 1 AND Amount > ?)", 100)
	err := tdb.Updates(upData).Error
	if err != nil {
		logs.Error("withdrawOrder withdrawDb err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	server.WriteAdminLog("提币订单撤回", ctx, reqdata)
	ctx.RespOK()
}

func (c *WalletController) deductlist(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	xMad := server.DaoxHashGame().XManAmountDetail
	db := xMad.WithContext(ctx.Gin())
	where := func(db gen.Dao) gen.Dao {
		db = db.Where(xMad.UserID.Eq(int32(reqdata.UserId))).Where(xMad.Amount.Lt(0))
		return db
	}
	list, count, err := db.Scopes(where).Order(xMad.ID.Desc()).FindByPage(offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	amount := struct {
		Amount decimal.Decimal
	}{}
	err = db.Scopes(where).Select(xMad.Amount.Sum().As("Amount")).Scan(&amount)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.Put("amount", amount.Amount)
	ctx.Put("data", list)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *WalletController) totalProfitLoss(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		Page     int
		PageSize int
		OrderId  int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	xWg := server.DaoxHashGame().XWithdrawGame
	db := xWg.WithContext(ctx.Gin())
	where := func(db gen.Dao) gen.Dao {
		db = db.Where(xWg.OrderID.Eq(int32(reqdata.OrderId)))
		return db
	}
	list, count, err := db.Scopes(where).Order(xWg.EndDate.Desc()).FindByPage(offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	type Amount struct {
		Symbol     string
		BetCount   decimal.Decimal
		BetAmount  decimal.Decimal
		LiuSui     decimal.Decimal
		WinAmount  decimal.Decimal
		ProfitLoss decimal.Decimal
	}
	var amounts []Amount
	err = db.Scopes(where).Select(xWg.Symbol,
		xWg.BetCount.Sum().As("BetCount"),
		xWg.BetAmount.Sum().As("BetAmount"),
		xWg.LiuSui.Sum().As("LiuSui"),
		xWg.WinAmount.Sum().As("WinAmount")).
		Group(xWg.Symbol).Scan(&amounts)
	if ctx.RespErr(err, &errcode) {
		return
	}
	var udstAmount Amount
	var trxAmount Amount
	udstAmount.Symbol = "usdt"
	trxAmount.Symbol = "trx"
	if count > 0 {
		for index, v := range amounts {
			amounts[index].ProfitLoss = v.WinAmount.Sub(v.BetAmount)
			if v.Symbol == "usdt" {
				udstAmount = amounts[index]
			} else if v.Symbol == "trx" {
				trxAmount = amounts[index]
			}
		}
	}
	ctx.Put("usdtAmount", udstAmount)
	ctx.Put("trxAmount", trxAmount)
	ctx.Put("data", list)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *WalletController) ipRepeat(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	type Result struct {
		RowId      int
		MatchType  string
		UserId     int
		Ip         string
		DeviceId   string
		RecordTime time.Time
	}
	type TotalData struct {
		PageRows      int
		IpCount       int
		DeviceCount   int
		PasswordCount int
	}
	rows, err := server.Db().GormDao().Raw("call WithdrawManage_user_GetIpList(?,?,?)",
		reqdata.UserId, reqdata.Page, reqdata.PageSize).
		Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	var list []Result
	var totalData TotalData
	if rows.Next() {
		err = rows.Scan(&totalData.PageRows, &totalData.IpCount, &totalData.DeviceCount, &totalData.PasswordCount)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}
	if rows.NextResultSet() {
		list = make([]Result, 0)
		for rows.Next() {
			data := Result{}
			err = rows.Scan(&data.RowId, &data.MatchType, &data.UserId, &data.Ip, &data.DeviceId,
				&data.RecordTime)
			if ctx.RespErr(err, &errcode) {
				return
			}
			list = append(list, data)
		}
	}
	ctx.Put("count", totalData)
	ctx.Put("list", list)
	ctx.Put("total", totalData.PageRows)
	ctx.RespOK()
}

func (c *WalletController) activityBonus(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		UserId    int `validate:"required"`
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	xCjd := server.DaoxHashGame().XCaijingDetail
	db := xCjd.WithContext(ctx.Gin())
	where := func(db gen.Dao) gen.Dao {
		db = db.Where(xCjd.UserID.Eq(int32(reqdata.UserId)))
		if reqdata.StartTime > 0 {
			startTime := carbon.Parse(carbon.CreateFromTimestampMilli(reqdata.StartTime).String()).StdTime()
			db = db.Where(xCjd.CreateTime.Gte(startTime))
		}
		if reqdata.EndTime > 0 {
			endTime := carbon.Parse(carbon.CreateFromTimestampMilli(reqdata.EndTime).String()).StdTime()
			db = db.Where(xCjd.CreateTime.Lt(endTime))
		}
		return db
	}
	list, count, err := db.Scopes(where).Order(xCjd.ID.Desc()).FindByPage(offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	amount := struct {
		Amount decimal.Decimal
	}{}
	err = db.Scopes(where).Select(xCjd.Amount.Sum().As("Amount")).Scan(&amount)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.Put("amount", amount.Amount)
	ctx.Put("data", list)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *WalletController) withdraw_address_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	dao := server.DaoxHashGame().XWithdrawAddress
	list, count, err := dao.WithContext(ctx.Gin()).
		Where(dao.UserID.Eq(int32(reqdata.UserId))).
		Order(dao.CreateTime.Desc()).
		FindByPage(offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *WalletController) user_tron_address(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "钱包管理", "提币记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	dao := server.DaoxHashGame().XWithdrawAddress
	withdrawAddresses, err := dao.WithContext(ctx.Gin()).
		Where(dao.UserID.Eq(int32(reqdata.UserId))).Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	var result []utilsmodel.UserTronAddress
	var results []utilsmodel.UserTronAddress
	var total int
	var repeatTotal int
	if len(withdrawAddresses) > 0 {
		rediskey := fmt.Sprintf("%s:%s:user_tron_address:%d", server.Project(), server.Module(), reqdata.UserId)
		redisdata := server.Redis().Get(rediskey)
		if redisdata != nil {
			err := json.Unmarshal(redisdata.([]byte), &result)
			if err != nil {
				logs.Error("user_tron_address json.Unmarshal err: ", err)
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
				return
			}
		} else {
			address := make([]string, 0, len(withdrawAddresses))
			for _, withdrawAddress := range withdrawAddresses {
				address = append(address, withdrawAddress.Address)
			}
			addressList := strings.Join(address, ",")
			data := map[string]string{
				"addressList": addressList,
			}
			url := tronscangoapi + addressTransferUrl
			resBoby, err := httpPost(url, data)
			if err != nil {
				logs.Error("user_tron_address httpPost err: ", err)
				return
			}
			resData := resBoby["data"]
			if resData != nil {
				bytes, _ := json.Marshal(resData)
				json.Unmarshal(bytes, &result)
			}
			server.Redis().SetEx(rediskey, 300, result)
		}
		if len(result) > 0 {
			repeatTotal = len(result)
			whereAddress := make([]string, 0, len(result))
			for _, wa := range result {
				whereAddress = append(whereAddress, wa.Address)
			}
			userAddresses, dataCount, err := dao.WithContext(ctx.Gin()).Where(dao.Address.In(whereAddress...)).FindByPage(offset, limit)
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
				return
			}
			total = int(dataCount)
			if len(userAddresses) > 0 {
				for _, ua := range userAddresses {
					for index, wa := range result {
						if wa.Address == ua.Address {
							dt := result[index]
							dt.UserId = ua.UserID
							results = append(results, dt)
							break
						}
					}
				}
			}
		}
	}
	ctx.Put("total", total)
	ctx.Put("repeatTotal", repeatTotal)
	ctx.Put("data", results)
	ctx.RespOK()
}

func httpPost(url string, data map[string]string) (xgo.H, error) {
	reqbytes, _ := json.Marshal(data)
	header := req.Header{
		"Content-Type": "application/json",
	}
	resp, err := req.Post(url, header, string(reqbytes))
	if err != nil {
		logs.Error("httpPost req.Post err: ", err)
		return nil, err
	}
	defer resp.Response().Body.Close()
	body, err := io.ReadAll(resp.Response().Body)
	//databytes, _ := json.Marshal(data)
	//logs.Debug("httpPost:", url, "|", string(body))
	if err != nil {
		logs.Error("httpPost io.ReadAl err: ", err)
		return nil, err
	}
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("httpPost json.Unmarshal err: ", err)
		return nil, err
	}
	code := xgo.ToInt(jdata["code"])
	if code != 0 {
		return nil, errors.New(jdata["msg"].(string))
	}
	return jdata, nil
}
