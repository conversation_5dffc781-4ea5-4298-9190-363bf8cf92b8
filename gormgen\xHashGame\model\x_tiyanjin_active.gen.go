// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTiyanjinActive = "x_tiyanjin_active"

// XTiyanjinActive 体验金活动表
type XTiyanjinActive struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:体验金活动id" json:"Id"` // 体验金活动id
	SellerID     int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                       // 运营商
	CreateTime   time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime   time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
	Status       int32     `gorm:"column:Status;comment:状态：1启用 2禁用" json:"Status"`                       // 状态：1启用 2禁用
	Name         string    `gorm:"column:Name;comment:活动名称" json:"Name"`                                 // 活动名称
	HostTagIds   string    `gorm:"column:HostTagIds;comment:域名标签id（英文逗号隔开）" json:"HostTagIds"`           // 域名标签id（英文逗号隔开）
	CoolingDate  int32     `gorm:"column:CoolingDate;comment:参与频率限制（0：仅限一次；>0：x天一次）" json:"CoolingDate"` // 参与频率限制（0：仅限一次；>0：x天一次）
	RewardConfig string    `gorm:"column:RewardConfig;comment:奖励配置" json:"RewardConfig"`                 // 奖励配置
	Rule         string    `gorm:"column:Rule;comment:发放规则" json:"Rule"`                                 // 发放规则
	Creator      string    `gorm:"column:Creator;comment:创建人账号" json:"Creator"`                          // 创建人账号
	Operator     string    `gorm:"column:Operator;comment:修改人账号" json:"Operator"`                        // 修改人账号
}

// TableName XTiyanjinActive's table name
func (*XTiyanjinActive) TableName() string {
	return TableNameXTiyanjinActive
}
