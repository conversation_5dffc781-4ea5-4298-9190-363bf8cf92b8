package controller

import (
	"encoding/json"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type LangController struct {
}

type LangGameRequestData struct {
	Page     int
	PageSize int
	SellerId int
	Brand    string // pp,pg,gfg,xyx
	GameId   string
	GameName string
	GameType int // 1电子,2棋牌,3小游戏,4彩票
	Sort     struct {
		Field string
		Type  string
	}
	State   int
	LangId  int
	IsHot   int
	IsNew   int
	IsRecom int
}

type ModifyGameSortRequestData struct {
	Sort   int
	LangId int
	GameId string
	Brand  string
}

type CommonRequest struct {
	LangId int
	Id     int
}

type ModifyRequest struct {
	Id                  int
	State               int32
	GameSort            string
	GameSortEx          string
	SportTarget         string
	LoginRegisterType   string
	PwdVerificationType int32
}

type NoticeListRequest struct {
	Id        int
	SellerId  int
	ChannelId int
	Title     string
	Page      int
	PageSize  int
}

type ModifyNoticeRequest struct {
	Id     int32
	LangId int32
	Sort   int32
	State  int32
}

type CarouselListRequest struct {
	Id        int
	SellerId  int
	ChannelId int
	Name      string
	Page      int
	PageSize  int
}

type ModifyCarouselRequest struct {
	Id     int32
	LangId int32
	Sort   int32
	State  int32
}

type ActiveListRequest struct {
	Id        int
	SellerId  int
	ChannelId int
	Title     string
	Page      int
	PageSize  int
}

type EmptyActiveRequest struct {
	Id   int
	Type string
}

type ModifyActiveRequest struct {
	LangId int32
	Id     int32
	Sort   int32
	Type   string
}

func (c *LangController) Init() {
	group := server.Http().NewGroup("/api/lang")
	{
		group.PostNoAuth("/list", c.list)     // 语言列表
		group.PostNoAuth("/modify", c.modify) // 修改语言

		group.PostNoAuth("/game/list", c.gameList)     // 游戏列表
		group.PostNoAuth("/game/modify", c.modifyGame) // 更新排序
		group.PostNoAuth("/game/empty", c.emptyGame)   // 清空排序
		// group.PostNoAuth("/game/sync", c.syncLangGame) // 同步所有语言

		group.PostNoAuth("/gameSort/sync", c.syncLangGameSort)     // 大类同步所有语言
		group.PostNoAuth("/gameSortEx/sync", c.syncLangGameSortEx) // 小类同步所有语言

		group.PostNoAuth("/notice/list", c.noticeList)
		group.PostNoAuth("/notice/modify", c.modifyNotice)
		group.PostNoAuth("/notice/empty", c.emptyNotice)
		group.PostNoAuth("/notice/sync", c.syncLangNotice)

		group.PostNoAuth("/carousel/list", c.carouselList)
		group.PostNoAuth("/carousel/modify", c.modifyCarousel)
		group.PostNoAuth("/carousel/empty", c.emptyCarousel)
		group.PostNoAuth("/carousel/sync", c.syncLangCarousel)

		group.PostNoAuth("/active/list", c.activeList)
		group.PostNoAuth("/active/empty", c.emptyActive)
		group.PostNoAuth("/active/modify", c.modifyActive)
		group.PostNoAuth("/active/sync", c.syncLangActive)

	}
}

func (c *LangController) list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	dao := server.DaoxHashGame().XLangList
	db := dao.WithContext(nil)

	result, err := db.Find()

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK(result)
}

func (c *LangController) gameList(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := LangGameRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	gameListDao := server.DaoxHashGame().XGameList
	langGameListDao := server.DaoxHashGame().XLangGameList
	langListDao := server.DaoxHashGame().XLangList
	langListDb := langListDao.WithContext(nil)
	db := gameListDao.WithContext(nil)

	langInfos, err := langListDb.Where(langListDao.ID.Eq(int32(reqdata.LangId))).First()
	if langInfos == nil {
		ctx.RespErrString(true, &errcode, "为找到该语言信息")
		return
	}

	type ResultData struct {
		//model.XGameList
		Id       int
		Brand    string
		GameId   string
		Name     string
		EName    string
		Icon     string
		EIcon    string
		GameType int
		Sort     int
		IsHot    int
		IsNew    int
		IsRecom  int
		State    int
		LangId   int
		RowNum   int
	}

	var result []ResultData
	query := db.Select(
		gameListDao.ID.As("Id"),
		gameListDao.Brand,
		gameListDao.GameID.As("GameId"),
		gameListDao.Name,
		gameListDao.EName,
		gameListDao.Icon,
		gameListDao.EIcon,
		gameListDao.GameType,
		gameListDao.IsHot,
		gameListDao.IsNew,
		gameListDao.IsRecom,
		gameListDao.State,
		langGameListDao.Sort,
	)

	if reqdata.GameType != 0 {
		query.Where(gameListDao.GameType.Eq(int32(reqdata.GameType)))
	}

	if reqdata.State != 0 {
		query.Where(gameListDao.State.Eq(int32(reqdata.State)))
	}

	if reqdata.Brand != "" {
		query.Where(gameListDao.Brand.Eq(reqdata.Brand))
	}

	if reqdata.GameId != "" {
		query.Where(gameListDao.GameID.Eq(reqdata.GameId))
	}

	if reqdata.GameName != "" {
		query.Where(gameListDao.Name.Like("%" + reqdata.GameName + "%")).Or(gameListDao.EName.Like("%" + reqdata.GameName + "%"))
	}

	if reqdata.IsHot != 0 {
		query.Where(gameListDao.IsHot.Eq(int32(reqdata.IsHot)))
	}

	if reqdata.IsNew != 0 {
		query.Where(gameListDao.IsNew.Eq(int32(reqdata.IsNew)))
	}

	if reqdata.IsRecom != 0 {
		query.Where(gameListDao.IsRecom.Eq(int32(reqdata.IsRecom)))
	}

	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)

	switch reqdata.Sort.Field {
	case "UserCount":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.UserCount.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(gameListDao.UserCount.Desc()).Order(gameListDao.ID.Desc())
		}
		break
	case "BetAmount":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.BetAmount.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(gameListDao.BetAmount.Desc()).Order(gameListDao.ID.Desc())
		}
		break
	case "Rtp":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.Rtp.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(gameListDao.Rtp.Desc()).Order(gameListDao.ID.Desc())
		}
	case "Sort":
		if reqdata.Sort.Type == "asc" {
			query.Order(langGameListDao.Sort.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.ID.Desc())
		}
	case "Id":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.ID.Asc())
		} else {
			query.Order(gameListDao.ID.Desc())
		}
	default:
		{
			type SortTypeData struct {
				SortType       int32 `json:"sortType"`
				ArtificialType int32 `json:"artificialType"`
			}
			redisSortTypeData := server.Redis().HGet("CONFIG", "GAME_LIST_SORT_TYPE")
			sortTypeData := &SortTypeData{}
			if redisSortTypeData != nil {
				// redisData 是 []uint8 类型，将其转换为字符串
				dataBytes := redisSortTypeData.([]uint8)
				// 将字节数据转为字符串
				dataStr := string(dataBytes)
				json.Unmarshal([]byte(dataStr), &sortTypeData)
			}

			switch sortTypeData.SortType {
			case 1:
				if sortTypeData.ArtificialType == 1 {
					query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.UserCount.Desc()).Order(gameListDao.ID.Desc())
				} else {
					query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.UserCount.Desc()).Order(gameListDao.ID.Desc())
				}
			case 2:
				if sortTypeData.ArtificialType == 1 {
					query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.BetAmount.Desc()).Order(gameListDao.ID.Desc())
				} else {
					query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.BetAmount.Desc()).Order(gameListDao.ID.Desc())
				}
			case 3:
				if sortTypeData.ArtificialType == 1 {
					query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.Rtp.Desc()).Order(gameListDao.ID.Desc())
				} else {
					query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.Rtp.Desc()).Order(gameListDao.ID.Desc())
				}
			default:
				query.Order(langGameListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.ID.Desc())
			}
		}
	}

	count, err := query.LeftJoin(langGameListDao, langGameListDao.LangID.Eq(int32(reqdata.LangId)), gameListDao.GameID.EqCol(langGameListDao.GameID), gameListDao.Brand.EqCol(langGameListDao.Brand)).ScanByPage(&result, offset, limit)

	if result == nil {
		result = []ResultData{}
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	for i, _ := range result {
		result[i].LangId = reqdata.LangId
		result[i].RowNum = (reqdata.Page-1)*reqdata.PageSize + i + 1
	}

	ctx.Put("url", server.ImageUrl())
	ctx.Put("data", result)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *LangController) modifyGame(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := ModifyGameSortRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	langGameList := server.DaoxHashGame().XLangGameList
	db := langGameList.WithContext(nil)

	langListDao := server.DaoxHashGame().XLangList
	langListDb := langListDao.WithContext(nil)

	// 获取host
	langInfos, err := langListDb.Where(langListDao.ID.Eq(int32(reqdata.LangId))).First()
	if langInfos == nil {
		ctx.RespErrString(true, &errcode, "未找到该语言")
		return
	}

	// 判断列表中是否存在该游戏
	game, err := db.Where(langGameList.LangID.Eq(int32(reqdata.LangId)), langGameList.GameID.Eq(reqdata.GameId), langGameList.Brand.Eq(reqdata.Brand)).First()
	if game != nil {
		db.Where(langGameList.LangID.Eq(int32(reqdata.LangId)), langGameList.GameID.Eq(reqdata.GameId), langGameList.Brand.Eq(reqdata.Brand)).Update(langGameList.Sort, reqdata.Sort)
	} else {
		// 新增一条记录到 game_channel_game_list 表中
		gameChannelListData := &model.XLangGameList{
			LangID: int32(reqdata.LangId),
			GameID: reqdata.GameId,
			Sort:   int32(reqdata.Sort),
			Brand:  reqdata.Brand,
		}

		err = db.Create(gameChannelListData)
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
	}

	ctx.RespOK()
}

func (c *LangController) emptyGame(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	dao := server.DaoxHashGame().XLangGameList
	db := dao.WithContext(nil)

	_, err = db.Where(dao.LangID.Eq(int32(reqdata.LangId))).Delete()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) syncLangGame(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	langListDao := server.DaoxHashGame().XLangList
	langListDb := langListDao.WithContext(nil)

	var Ids []int32
	err = langListDb.Where(langListDao.ID.Neq(int32(reqdata.Id))).Pluck(langListDao.ID, &Ids)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	langGameListDao := server.DaoxHashGame().XLangGameList
	langGameLisDb := langGameListDao.WithContext(nil)
	_, err = langGameLisDb.Where(langGameListDao.LangID.Neq(int32(reqdata.Id))).Delete()

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 动态构建 SQL 查询
	query := `
	INSERT INTO x_lang_game_list (LangId, Brand, GameId, Sort)
	SELECT ?, B.Brand, B.GameId, B.Sort 
	FROM x_lang_game_list AS B
	WHERE B.LangId = ?
	`
	values := make([]interface{}, 0, len(Ids)*4)
	for i, Id := range Ids {
		if i > 0 {
			query += " UNION ALL SELECT ?, B.Brand, B.GameId, B.Sort FROM x_lang_game_list AS B WHERE B.LangId = ?"
		}
		values = append(values, Id, reqdata.Id)
	}

	// 执行批量插入操作
	err = server.Db().Gorm().Exec(query, values...).Error

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) modify(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := ModifyRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	langListDao := server.DaoxHashGame().XLangList
	langLisDb := langListDao.WithContext(nil)

	langInfos := &model.XLangList{}
	if reqdata.State != 0 {
		langInfos.State = reqdata.State
	}

	if reqdata.GameSort != "" {
		langInfos.GameSort = reqdata.GameSort
	}

	if reqdata.GameSortEx != "" {
		langInfos.GameSortEx = reqdata.GameSortEx
	}

	if reqdata.SportTarget != "" {
		langInfos.SportTarget = reqdata.SportTarget
	}

	if reqdata.LoginRegisterType != "" {
		langInfos.LoginRegisterType = reqdata.LoginRegisterType
	}
	if reqdata.PwdVerificationType == 0 {
		server.Db().Conn().Exec("update x_lang_list set PwdVerificationType = ? where Id = ?", reqdata.PwdVerificationType, reqdata.Id)
	}
	langInfos.PwdVerificationType = reqdata.PwdVerificationType

	_, err = langLisDb.Where(langListDao.ID.Eq(int32(reqdata.Id))).Updates(langInfos)
	if err != nil {
		ctx.RespErrString(true, &errcode, "更新失败")
		return
	}

	ctx.RespOK()
}

func (c *LangController) syncLangGameSort(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	langListDao := server.DaoxHashGame().XLangList
	langLisDb := langListDao.WithContext(nil)

	langInfos, err := langLisDb.Where(langListDao.ID.Eq(int32(reqdata.Id))).First()
	if langInfos == nil {
		ctx.RespErrString(true, &errcode, "未找到该语言")
		return
	}

	_, err = langLisDb.Where(langListDao.ID.Neq(int32(reqdata.Id))).Update(langListDao.GameSort, langInfos.GameSort)
	if err != nil {
		ctx.RespErrString(true, &errcode, "更新失败")
		return
	}

	ctx.RespOK()
}

func (c *LangController) syncLangGameSortEx(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	langListDao := server.DaoxHashGame().XLangList
	langLisDb := langListDao.WithContext(nil)

	langInfos, err := langLisDb.Where(langListDao.ID.Eq(int32(reqdata.Id))).First()
	if langInfos == nil {
		ctx.RespErrString(true, &errcode, "未找到该语言")
		return
	}

	_, err = langLisDb.Where(langListDao.ID.Neq(int32(reqdata.Id))).Update(langListDao.GameSortEx, langInfos.GameSortEx)
	if err != nil {
		ctx.RespErrString(true, &errcode, "更新失败")
		return
	}

	ctx.RespOK()
}

func (c *LangController) noticeList(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := NoticeListRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)

	noticeListDao := server.DaoxHashGame().XNoticeV2
	noticeListDb := noticeListDao.WithContext(nil)
	sellerDao := server.DaoxHashGame().XSeller
	channelDao := server.DaoxHashGame().XChannel

	query := noticeListDb.Select(noticeListDao.ID.As("Id"), noticeListDao.LangID.As("LangId"), noticeListDao.Type, noticeListDao.Title, noticeListDao.Sort, noticeListDao.State, sellerDao.SellerName, channelDao.ChannelName).
		LeftJoin(sellerDao, noticeListDao.SellerID.EqCol(sellerDao.SellerID)).
		LeftJoin(channelDao, noticeListDao.ChannelID.EqCol(channelDao.ChannelID))

	if reqdata.SellerId != 0 {
		query.Where(noticeListDao.SellerID.Eq(int32(reqdata.SellerId)))
	}

	if reqdata.ChannelId != 0 {
		query.Where(noticeListDao.ChannelID.Eq(int32(reqdata.ChannelId)))
	}

	if reqdata.Title != "" {
		query.Where(noticeListDao.Title.Like("%" + reqdata.Title + "%"))
	}

	type ResultData struct {
		Id          int
		LangId      int
		Type        int
		Title       string
		Sort        int
		State       int
		RowNum      int
		SellerName  string
		ChannelName string
	}

	var result []ResultData
	count, err := query.Where(noticeListDao.LangID.Eq(int32(reqdata.Id))).Where(noticeListDao.State.Eq(1)).Order(noticeListDao.Sort.Desc(), noticeListDao.ID.Desc()).ScanByPage(&result, offset, limit)

	if result == nil {
		result = []ResultData{}
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	for i, _ := range result {
		result[i].RowNum = (reqdata.Page-1)*reqdata.PageSize + i + 1
	}

	ctx.Put("data", result)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *LangController) modifyNotice(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := ModifyNoticeRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	noticeDao := server.DaoxHashGame().XNoticeV2
	noticeDb := noticeDao.WithContext(nil)

	noticeInfos := &model.XNoticeV2{}
	if reqdata.State != 0 {
		noticeInfos.State = reqdata.State
	}

	if reqdata.Sort != 0 {
		noticeInfos.Sort = reqdata.Sort
	}

	_, err = noticeDb.Where(noticeDao.LangID.Eq(reqdata.LangId)).Where(noticeDao.ID.Eq(reqdata.Id)).Updates(&noticeInfos)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) syncLangNotice(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	err = server.Db().Gorm().Exec(`
		UPDATE x_notice_v2 AS a
		INNER JOIN x_notice_v2 AS b ON b.LangId=? and b.Id=a.Id 
		SET a.Sort = b.Sort`, reqdata.Id).Error

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) emptyNotice(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	noticeDao := server.DaoxHashGame().XNoticeV2
	noticeDb := noticeDao.WithContext(nil)

	_, err = noticeDb.Where(noticeDao.LangID.Eq(int32(reqdata.Id))).Update(noticeDao.Sort, 0)

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) carouselList(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CarouselListRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)

	CarouselListDao := server.DaoxHashGame().XHomeCarouselV2
	CarouselListDb := CarouselListDao.WithContext(nil)
	sellerDao := server.DaoxHashGame().XSeller
	channelDao := server.DaoxHashGame().XChannel

	query := CarouselListDb.Select(CarouselListDao.ID.As("Id"), CarouselListDao.Lang.As("LangId"), CarouselListDao.Name, CarouselListDao.Sort, CarouselListDao.State, sellerDao.SellerName, channelDao.ChannelName).
		LeftJoin(sellerDao, CarouselListDao.SellerID.EqCol(sellerDao.SellerID)).
		LeftJoin(channelDao, CarouselListDao.ChannelID.EqCol(channelDao.ChannelID))

	if reqdata.SellerId != 0 {
		query.Where(CarouselListDao.SellerID.Eq(int32(reqdata.SellerId)))
	}

	if reqdata.ChannelId != 0 {
		query.Where(CarouselListDao.ChannelID.Eq(int32(reqdata.ChannelId)))
	}

	if reqdata.Name != "" {
		query.Where(CarouselListDao.Name.Like("%" + reqdata.Name + "%"))
	}

	type ResultData struct {
		Id          int
		LangId      int
		Name        string
		Sort        int
		State       int
		RowNum      int
		SellerName  string
		ChannelName string
	}

	var result []ResultData
	count, err := query.Where(CarouselListDao.Lang.Eq(int32(reqdata.Id))).Where(CarouselListDao.State.Eq(1)).Order(CarouselListDao.Sort.Desc(), CarouselListDao.ID.Desc()).ScanByPage(&result, offset, limit)

	if result == nil {
		result = []ResultData{}
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	for i, _ := range result {
		result[i].RowNum = (reqdata.Page-1)*reqdata.PageSize + i + 1
	}

	ctx.Put("data", result)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *LangController) modifyCarousel(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := ModifyCarouselRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	carouselDao := server.DaoxHashGame().XHomeCarouselV2
	carouselDb := carouselDao.WithContext(nil)

	carouselInfos := &model.XNoticeV2{}
	if reqdata.State != 0 {
		carouselInfos.State = reqdata.State
	}

	if reqdata.Sort != 0 {
		carouselInfos.Sort = reqdata.Sort
	}

	_, err = carouselDb.Where(carouselDao.ID.Eq(reqdata.Id)).Where(carouselDao.Lang.Eq(reqdata.LangId)).Updates(&carouselInfos)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) emptyCarousel(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	carouselDao := server.DaoxHashGame().XHomeCarouselV2
	carouselDb := carouselDao.WithContext(nil)

	_, err = carouselDb.Where(carouselDao.Lang.Eq(int32(reqdata.Id))).Update(carouselDao.Sort, 0)

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) syncLangCarousel(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	err = server.Db().Gorm().Exec(`
		UPDATE x_home_carousel_v2 AS a
		INNER JOIN x_home_carousel_v2 AS b ON b.Lang=? and b.Id=a.Id 
		SET a.Sort = b.Sort`, reqdata.Id).Error

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) activeList(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := ActiveListRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	activeDao := server.DaoxHashGame().XActiveDefine
	activeDb := activeDao.WithContext(nil)
	activeSortDao := server.DaoxHashGame().XActiveDefineSort
	//activeSortDb := activeSortDao.WithContext(nil)
	sellerDao := server.DaoxHashGame().XSeller
	channelDao := server.DaoxHashGame().XChannel

	query := activeDb.Select(activeDao.ID.As("Id"), activeDao.Title, sellerDao.SellerName, channelDao.ChannelName, activeSortDao.Lang.As("LangId"), activeSortDao.Sort, activeSortDao.TopSort).
		LeftJoin(activeSortDao, activeSortDao.ID.EqCol(activeDao.ID), activeSortDao.Lang.Eq(int32(reqdata.Id))).
		LeftJoin(sellerDao, activeDao.SellerID.EqCol(sellerDao.SellerID)).
		LeftJoin(channelDao, activeDao.ChannelID.EqCol(channelDao.ChannelID)).
		//Where(activeSortDao.Lang.Eq(int32(reqdata.Id))).
		Where(activeDao.State.Eq(1))

	if reqdata.SellerId != 0 {
		query.Where(activeDao.SellerID.Eq(int32(reqdata.SellerId)))
	}

	if reqdata.ChannelId != 0 {
		query.Where(activeDao.ChannelID.Eq(int32(reqdata.ChannelId)))
	}

	if reqdata.Title != "" {
		query.Where(activeDao.Title.Like("%" + reqdata.Title + "%"))
	}

	type ResultData struct {
		Id     int
		LangId int
		Title  string
		Sort   int
		//State       int
		RowNum      int
		SellerName  string
		ChannelName string
		TopSort     int
		//IsTop       int
	}

	var result []ResultData
	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)
	count, err := query.Order(activeSortDao.TopSort.Desc(), activeSortDao.Sort.Desc(), activeSortDao.ID.Desc(), activeDao.Sort.Desc()).ScanByPage(&result, offset, limit)

	if result == nil {
		result = []ResultData{}
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	for i, _ := range result {
		result[i].RowNum = (reqdata.Page-1)*reqdata.PageSize + i + 1
	}

	ctx.Put("data", result)
	ctx.Put("total", count)
	ctx.RespOK()

}

func (c *LangController) emptyActive(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := EmptyActiveRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	activeSortDao := server.DaoxHashGame().XActiveDefineSort
	activeSortDb := activeSortDao.WithContext(nil)

	if reqdata.Type == "Sort" {
		_, err = activeSortDb.Where(activeSortDao.Lang.Eq(int32(reqdata.Id))).Update(activeSortDao.Sort, 0)
	}

	if reqdata.Type == "TopSort" {
		_, err = activeSortDb.Where(activeSortDao.Lang.Eq(int32(reqdata.Id))).Update(activeSortDao.TopSort, 0)
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *LangController) modifyActive(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := ModifyActiveRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	activeSortDao := server.DaoxHashGame().XActiveDefineSort
	activeSortDb := activeSortDao.WithContext(nil)

	query := activeSortDb.Where(activeSortDao.Lang.Eq(reqdata.LangId)).Where(activeSortDao.ID.Eq(reqdata.Id))

	infos, _ := query.First()
	if infos != nil {
		if reqdata.Type == "Sort" {
			_, err := query.Update(activeSortDao.Sort, reqdata.Sort)
			if err != nil {
				ctx.RespErrString(true, &errcode, "更新失败")
				return
			}
		}

		if reqdata.Type == "TopSort" {
			_, err := query.Update(activeSortDao.TopSort, reqdata.Sort)
			if err != nil {
				ctx.RespErrString(true, &errcode, "更新失败")
				return
			}
		}
	} else {
		if reqdata.Type == "Sort" {
			err := activeSortDb.Create(&model.XActiveDefineSort{
				ID:   reqdata.Id,
				Lang: reqdata.LangId,
				Sort: reqdata.Sort,
			})
			if err != nil {
				ctx.RespErrString(true, &errcode, "更新失败")
				return
			}
		}

		if reqdata.Type == "TopSort" {
			err := activeSortDb.Create(&model.XActiveDefineSort{
				ID:      reqdata.Id,
				Lang:    reqdata.LangId,
				TopSort: reqdata.Sort,
			})
			if err != nil {
				ctx.RespErrString(true, &errcode, "更新失败")
				return
			}
		}

	}

	ctx.RespOK()
}

func (c *LangController) syncLangActive(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommonRequest{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	err = server.Db().Gorm().Exec(`
		UPDATE x_active_define_sort AS a
		INNER JOIN x_active_define_sort AS b ON b.Lang=? and b.Id=a.Id 
		SET a.Sort = b.Sort,
		    a.TopSort = b.TopSort
		`, reqdata.Id).Error

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}
