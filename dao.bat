SET tables=x_robot_report_import_flow_upload,x_robot_report_import_flow,x_robot_redbag_report_users,x_robot_post_user_info,x_robot_push_msg_config,x_robot_push_activity_record,x_robot_redbag_questions,x_robot_ad_start_record,x_ads_function_interaction_button_daily_stats,x_ads_function_interaction_tab_daily_stats,x_ads_game_daily_stats,x_ads_withdrawal_daily_stats,x_ads_recharge_daily_stats,x_ads_keyword_daily_stats,x_ads_active_daily_stats,x_ads_page_daily_stats,x_ads_dict_event,x_ads_event_tracking_record,x_robot_statistics_day,x_ads_user_profile,x_ads_user_label_config,x_user,x_channel,x_login_log,x_tiyanjing,x_recharge,x_seller,x_channel_host,x_withdraw,x_tg_account,x_tyj_broadcast_config,x_amount_change_log,x_user_dailly,x_vip_info,x_caijing_detail,x_man_amount_detail,x_withdraw_address
SET tables=%tables%,x_order,x_third_dianzhi,x_third_live,x_third_lottery,x_third_qipai,x_third_quwei,x_third_sport,x_game_list,x_third_texas,x_withdraw_game
SET tables=%tables%,x_agent,x_agent_child,x_agent_independence,x_agent_dailly,x_agent_independent_commission_detail,x_agent_code_t1,x_agent_commission_config,x_agent_independence_fencheng_history,x_agent_code
SET tables=%tables%,x_config,x_tg_robot_guide,x_kefu_bind_history,x_dict_changetype,x_dict_change_parentype,x_tb_rewardword,x_finance_symbol,x_active_define,x_active_seller_define
SET tables=%tables%,x_dict_gametype,x_tb_winlost_config,admin_user,x_tb_banker_config,x_tb_banker_user,x_game_chain
SET tables=%tables%,x_tg_robot_redpacket,x_tg_redpacket,x_tg_redpacket_log,x_tg_redpacket_users,x_tg_redpacket_invite_count,x_tg_redpacket_withdraw_log
SET tables=%tables%,x_black_block,x_white_block,x_game_period,x_agent_blacklist,x_game_brand,x_withdraw_limit_config,x_vip_define,x_game,x_channel_game_list
SET tables=%tables%,x_home_carousel_v2,x_lang_game_list,x_lang_list,x_notice_v2,x_active_define_sort,x_host_tag,x_tiyanjin_active
set tables=%tables%,x_bonus_task_template,x_bonus_task_user,x_finance_method
set tables=%tables%,x_agent_game_define,x_agent_game_dailly,x_agent_game_commission_config,x_vip_game_define,x_tg_user_robot,x_tg_messages,x_tg_chat
set tables=%tables%,x_chat_hongbao_condition,x_verify,x_robot_mission_templte,x_robot_config,x_robot_message_templte,x_active_redeemcode_record,x_agent_data_date,x_seller_input_data_date,x_operations_input_data_date
set tables=%tables%,x_agent_mode_log,x_agent_commission_team_define,x_agent_commission_level_define,x_agent_commission_scheme

gentool -dsn "userbu:aw9#gf*S7fT1P@tcp(*************:14578)/x_hash_game?charset=utf8mb4&parseTime=True&loc=Local" -tables %tables% -outPath "./gormgen/xHashGame/dao/"


@REM SET tables2=x_tg_robot_log_action,x_tg_robot_stat_hour,x_tg_robot_stat_date
@REM
@REM gentool -dsn "userbu:aw9#gf*S7fT1P@tcp(*************:14578)/x_hash_stat?charset=utf8mb4&parseTime=True&loc=Local" -tables %tables2% -outPath "./gormgen/xHashStat/dao/"


