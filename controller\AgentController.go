package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"path"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	gamedefine "xserver/share/gameDefine"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AgentController struct {
}

/*

	channelid := ""
	for i := 0; i < len(reqdata.ChannelId); i++ {
		channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	}
	if len(channelid) > 0 {
		channelid = channelid[0 : len(channelid)-1]
		where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	}


	topagentid := ""
	for i := 0; i < len(reqdata.TopAgentId); i++ {
		topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	}
	if len(topagentid) > 0 {
		topagentid = topagentid[0 : len(topagentid)-1]
		where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	}


*/

func (c *AgentController) Init() {
	group := server.Http().NewGroup("/api/agent")
	{

		group.Post("/topagent_list", c.topagent_list)
		group.Post("/set_fencheng", c.set_fencheng)
		group.Post("/fencheng_list", c.fencheng_list)
		group.Post("/fencheng_add", c.fencheng_add)
		group.Post("/fencheng_modify", c.fencheng_modify)
		group.Post("/fencheng_delete", c.fencheng_delete)
		group.Post("/list", c.list)
		group.Post("/dict_child_list", c.dict_child_list)
		group.Post("/all_child_list", c.all_child_list)
		group.Post("/agent_dailly", c.agent_dailly)
		group.Post("/agent_dailly_dict", c.agent_dailly_dict)
		group.Post("/agent_dailly_all", c.agent_dailly_all)
		group.Post("/commission_list", c.commission_list)
		group.Post("/commission_result", c.commission_result)
		group.Post("/commission_result_batch", c.commission_result_batch)
		group.Post("/liusui_list", c.liusui_list)
		group.Post("/liusui_kouchu", c.liusui_kouchu)
		group.Post("/commission_detail", c.commission_detail)

		group.Post("/agent_define_get", c.agent_define_get)
		group.Post("/agent_define_add", c.agent_define_add)
		group.Post("/agent_define_del", c.agent_define_del)
		group.Post("/agent_define_mod", c.agent_define_mod)

		// 香港彩返佣配置玩法查询
		group.Post("/agent_game_lottery_play", c.agent_game_lottery_play)

		group.Post("/agent_set_ignore_agent_get", c.agent_set_ignore_agent_get)
		group.Post("/agent_set_ignore_agent_set", c.agent_set_ignore_agent_set)

		group.Post("/agent_independence_add", c.agent_independence_add)
		group.Post("/agent_independence_delete", c.agent_independence_delete)
		group.Post("/agent_independence_list", c.agent_independence_list)
		group.Post("/agent_independence_list_all", c.agent_independence_list_all)
		group.Post("/agent_independence_list_all_detail", c.agent_independence_list_all_detail)
		group.Post("/agent_independence_list_all_fine", c.agent_independence_list_all_fine)
		group.Post("/agent_independence_list_all_team", c.agent_independence_list_all_team)
		group.Post("/agent_independence_list_all_histroy", c.agent_independence_list_all_histroy)
		group.Post("/agent_independence_daily", c.agent_independence_daily)
		group.Post("/agent_independence_modify", c.agent_independence_modify)
		group.Post("/agent_independence_report", c.agent_independence_report)
		group.Post("/agent_independence_report_detail", c.agent_independence_report_detail)
		group.Post("/agent_independence_update_logo_icon", c.agent_independence_update_logo_icon)

		group.Post("/agent_fangan_record", c.agent_fangan_record)

		group.Post("/get_config_t1", c.get_config_t1)
		group.Post("/add_config_t1", c.add_config_t1)
		group.Post("/modify_config_t1", c.modify_config_t1)
		group.Post("/delete_config_t1", c.delete_config_t1)

		group.Post("/get_blacklist_t1", c.get_blacklist_t1)
		group.Post("/add_blacklist_t1", c.add_blacklist_t1)
		group.Post("/delete_blacklist_t1", c.delete_blacklist_t1)

		group.Post("/commission_list_t1", c.commission_list_t1)
		group.Post("/commission_result_t1", c.commission_result_t1)
		group.Post("/commission_result_batch_t1", c.commission_result_batch_t1)

		group.Post("/check_agent_user", c.checkAgentUserId)
		group.Post("/check_agent_host", c.checkAgentHost)
		group.Post("/add_agent_user", c.addAgentUser)
		group.Post("/get_agent_admin_user", c.getAgentAdminUser)
		group.Post("/get_channel_host", c.getChannelHost)
	}
}

func (c *AgentController) topagent_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId      int
		Page          int
		PageSize      int
		UserId        int
		ChannelId     int
		Address       string
		AgentShortUrl string // 新增字段
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "顶级代理管理", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.SellerId == -1 {
		reqdata.SellerId = 0
	}

	// 调用数据库方法获取代理的分页数据
	total, data := db.Agent_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.UserId, 1, reqdata.ChannelId, reqdata.Address, reqdata.AgentShortUrl)

	// 将 data 赋值给上下文
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("顶级代理查询", ctx, reqdata)
}
func (c *AgentController) set_fencheng(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		FenCheng   float64
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理查询", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var dberrcode int
	var dberrmsg string
	server.Db().QueryScan("call x_admin_set_fencheng(?,?)", []interface{}{reqdata.UserId, reqdata.FenCheng}, &dberrcode, &dberrmsg)
	if ctx.RespErrString(dberrcode > 0, &errcode, dberrmsg) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置分成", ctx, reqdata)
}
func (c *AgentController) fencheng_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "顶级代理管理", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	type ReturnData struct {
		GameId       int
		RoomLevel    int
		FenChengRate float64
		State        int
	}
	sql := "select GameId,RoomLevel,FenChengRate,State from x_game_agent_fencheng where UserId = ? "
	dbresult, err := server.Db().Conn().Query(sql, &reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	data := []ReturnData{}
	for dbresult.Next() {
		element := ReturnData{}
		abugo.GetDbResult(dbresult, &element)
		data = append(data, element)
	}
	dbresult.Close()
	ctx.RespOK(data)
	server.WriteAdminLog("查询代理游戏分成", ctx, reqdata)
}
func (c *AgentController) fencheng_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId     int
		UserId       int     `validate:"required"`
		GameId       int     `validate:"required"`
		RoomLevel    int     `validate:"required"`
		FenChengRate float64 `validate:"required"`
		State        int     `validate:"required"`
		GoogleCode   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "顶级代理管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErrString(reqdata.FenChengRate < 0 || reqdata.FenChengRate > 0.045, &errcode, "分成比例超出范围:[0,0.045]") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "insert into x_game_agent_fencheng(UserId,GameId,RoomLevel,FenChengRate,State) values(?,?,?,?,?)"
	err = server.Db().QueryNoResult(sql, reqdata.UserId, reqdata.GameId, reqdata.RoomLevel, reqdata.FenChengRate, reqdata.State)
	if err != nil && strings.Index(err.Error(), "1062: Duplicate entry") > 0 {
		ctx.RespErrString(err != nil, &errcode, "添加失败,该游戏分成比例已存在")
		return
	}
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("添加代理游戏赔率", ctx, reqdata)
}
func (c *AgentController) fencheng_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId     int
		UserId       int     `validate:"required"`
		GameId       int     `validate:"required"`
		RoomLevel    int     `validate:"required"`
		FenChengRate float64 `validate:"required"`
		State        int     `validate:"required"`
		GoogleCode   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(reqdata.FenChengRate < 0 || reqdata.FenChengRate > 0.045, &errcode, "分成比例超出范围:[0,0.045]") {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "顶级代理管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "update x_game_agent_fencheng set FenChengRate = ?,State = ? where UserId = ? and GameId = ? and RoomLevel = ?"
	err = server.Db().QueryNoResult(sql, reqdata.FenChengRate, reqdata.State, reqdata.UserId, reqdata.GameId, reqdata.RoomLevel)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("修改代理分成比例", ctx, reqdata)
}
func (c *AgentController) fencheng_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		GameId     int `validate:"required"`
		RoomLevel  int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "顶级代理管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "delete from  x_game_agent_fencheng  where UserId = ? and GameId = ? and RoomLevel = ?"
	err = server.Db().QueryNoResult(sql, reqdata.UserId, reqdata.GameId, reqdata.RoomLevel)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("删除代理分成比例", ctx, reqdata)
}
func (c *AgentController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId      int    // 运营商ID
		Page          int    // 页码
		PageSize      int    // 每页数量
		UserId        int    // 用户ID
		ChannelId     int    // 渠道ID
		Address       string // 地址
		AgentShortUrl string // 短连接 - 新增字段
	}

	// 错误码初始化
	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 获取用户token并验证权限
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理查询", "查"), &errcode, "权限不足") {
		return
	}

	// 验证运营商权限
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	// 如果请求的SellerId为-1，则将其设置为0
	if reqdata.SellerId == -1 {
		reqdata.SellerId = 0
	}

	// 调用数据库方法获取代理的分页数据
	total, data := db.Agent_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.UserId, 0, reqdata.ChannelId, reqdata.Address, reqdata.AgentShortUrl)

	// 确保 data 不为 nil
	if data == nil {
		data = []db.AgentWithShortUrl{} // 初始化为一个空切片
	}

	// 将 data 赋值给上下文
	ctx.Put("data", data)   // 代理数据
	ctx.Put("total", total) // 总记录数
	ctx.RespOK()            // 返回成功响应

	// 记录管理日志
	server.WriteAdminLog("代理查询", ctx, reqdata)
}
func (c *AgentController) dict_child_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理查询", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	data := db.Agent_Child_Data(reqdata.UserId)
	ctx.RespOK(data)
	server.WriteAdminLog("直属代理查询", ctx, reqdata)
}
func (c *AgentController) all_child_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理查询", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.SellerId == -1 {
		reqdata.SellerId = 0
	}
	data := db.Agent_Child_Data_All(reqdata.UserId)
	ctx.RespOK(data)
	server.WriteAdminLog("查看所有下级", ctx, reqdata)
}
func (c *AgentController) agent_dailly(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		UserId    int
		StartTime int64
		ChannelId int
		Address   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理查询", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	total, data := db.AgentDailly_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.UserId, reqdata.StartTime, reqdata.ChannelId, reqdata.Address)
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("代理每日数据查询", ctx, reqdata)
}
func (c *AgentController) agent_dailly_dict(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		UserId    int
		StartTime int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理查询", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.StartTime == 0 {
		reqdata.StartTime = abugo.LocalDateToTimeStamp(abugo.GetLocalDate()) * 1000
	}
	timestr := abugo.TimeStampToLocalDate(reqdata.StartTime)
	data := db.AgentDailly_Child_Data_Dict(reqdata.UserId, timestr)
	ctx.Put("data", data)
	ctx.RespOK()
	server.WriteAdminLog("代理每日数据查询", ctx, reqdata)
}
func (c *AgentController) agent_dailly_all(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		UserId    int
		StartTime int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理查询", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.StartTime == 0 {
		reqdata.StartTime = abugo.LocalDateToTimeStamp(abugo.GetLocalDate()) * 1000
	}
	timestr := abugo.TimeStampToLocalDate(reqdata.StartTime)
	data := db.AgentDailly_Child_Data_All(reqdata.UserId, timestr)
	ctx.Put("data", data)
	ctx.RespOK()
	server.WriteAdminLog("代理每日数据查询", ctx, reqdata)
}
func (c *AgentController) commission_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		Id        int
		UserId    int
		Symbol    string
		State     int
		StartTime int64
		EndTime   int64
		ChannelId int
		Address   string
		Export    int //0表示分页查询，1表示导出报表
		AgentMode int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "佣金审核", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_佣金审核_%s", time.Now().Format("20060102150405")))
	//var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("Id", "订单号")
		xlsx.SetTitle("UserId", "代理ID")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("Address", "钱包地址")
		xlsx.SetTitle("AgentMode", "代理模式")
		xlsx.SetTitle("Symbol", "币种")
		xlsx.SetTitle("Amount", "佣金金额")
		xlsx.SetTitle("StartDate-EndDate", "佣金产生时段")
		xlsx.SetTitle("CreateTime", "申请时间")
		xlsx.SetTitle("Memo", "审核备注")
		xlsx.SetTitle("AuditAccount", "审核员")
		xlsx.SetTitle("AuditTime", "审核时间")
		xlsx.SetTitle("SendAccount", "发放员")
		xlsx.SetTitle("SendTime", "发放时间")
		xlsx.SetTitle("State", "状态")
		xlsx.SetTitleStyle()
	}

	total, data, totalamount := db.CommissionAudit_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.Id, reqdata.SellerId, reqdata.UserId, reqdata.Symbol, reqdata.State, reqdata.StartTime, reqdata.EndTime, reqdata.ChannelId, reqdata.Address, reqdata.AgentMode)
	if reqdata.Export != 1 {
		ctx.Put("data", data)
		ctx.Put("total", total)
		ctx.Put("totalamount", totalamount)
	} else {
		for i := 0; i < len(data); i++ {
			xlsx.SetValue("Id", data[i].Id, int64(i+2))
			xlsx.SetValue("UserId", data[i].UserId, int64(i+2))
			xlsx.SetValue("ChannelId", ChannelName(data[i].ChannelId), int64(i+2))
			xlsx.SetValue("Address", data[i].Address, int64(i+2))
			xlsx.SetValue("AgentMode", data[i].AgentMode, int64(i+2))
			xlsx.SetValue("Symbol", data[i].Symbol, int64(i+2))
			xlsx.SetValue("Amount", data[i].Amount, int64(i+2))
			xlsx.SetValue("StartDate-EndDate", fmt.Sprintf("%s-%s", data[i].StartDate, data[i].EndDate), int64(i+2))
			xlsx.SetValue("CreateTime", data[i].CreateTime, int64(i+2))
			xlsx.SetValue("Memo", data[i].Memo, int64(i+2))
			xlsx.SetValue("AuditAccount", data[i].AuditAccount, int64(i+2))
			xlsx.SetValue("AuditTime", data[i].AuditTime, int64(i+2))
			xlsx.SetValue("SendAccount", data[i].SendAccount, int64(i+2))
			xlsx.SetValue("SendTime", data[i].SendTime, int64(i+2))

			if data[i].State == 1 {
				xlsx.SetValue("State", "待审核", int64(i+2))
			} else if data[i].State == 2 {
				xlsx.SetValue("State", "审核拒绝", int64(i+2))
			} else if data[i].State == 3 {
				xlsx.SetValue("State", "审核通过", int64(i+2))
			} else if data[i].State == 4 {
				xlsx.SetValue("State", "已发放", int64(i+2))
			} else {
				xlsx.SetValue("State", "未定义", int64(i+2))
			}
		}
		xlsx.SetValue("Id", "合计", int64(total+2))
		xlsx.SetValue("Amount", totalamount, int64(total+2))
		xlsx.SetValueStyle(int64(total + 2))
	}

	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("佣金审核查询", ctx, reqdata)
}

func (c *AgentController) commission_result(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Id         int    `validate:"required"`
		State      int    `validate:"required"`
		Memo       string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "佣金审核", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var dberrcode int
	var dberrmsg string
	server.Db().QueryScan("call x_admin_commission_audit(?,?,?,?)", []interface{}{reqdata.Id, reqdata.State, token.Account, reqdata.Memo}, &dberrcode, &dberrmsg)
	if ctx.RespErrString(dberrcode > 0, &errcode, dberrmsg) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("佣金审核", ctx, reqdata)
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}

func (c *AgentController) commission_result_batch(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Ids        []int  `validate:"required"`
		State      int    `validate:"required"`
		Memo       string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "佣金审核", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	for _, id := range reqdata.Ids {
		var dberrcode int
		var dberrmsg string
		server.Db().QueryScan("call x_admin_commission_audit(?,?,?,?)", []interface{}{id, reqdata.State, token.Account, reqdata.Memo}, &dberrcode, &dberrmsg)
	}
	ctx.RespOK()
	server.WriteAdminLog("佣金审核", ctx, reqdata)
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}

func (c *AgentController) liusui_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		UserId    int
		Address   string
		StartTime int64
		EndTime   int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "业绩扣除", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	type ReturnData struct {
		UserId         int
		Address        string
		LiuSuiTrx      float64
		LiuSuiUsdt     float64
		FineLiuSuiTrx  float64
		FineLiuSuiUsdt float64
		FineAccount    string
		FineTime       string
		RecordDate     string
		FineMemo       string
	}
	StartTime := abugo.TimeStampToLocalTime(reqdata.StartTime)
	EndTime := abugo.TimeStampToLocalTime(reqdata.EndTime)
	if len(StartTime) == 0 {
		StartTime = time.Now().Format("2006-01-02")
	}
	if len(EndTime) == 0 {
		m, _ := time.ParseDuration("24h")
		now := time.Now()
		StartTime = now.Format("2006-01-02")
		EndTime = now.Add(m).Format("2006-01-02")
	}
	datas := []ReturnData{}
	if reqdata.UserId > 0 {
		sql := "select x_user_dailly.*,x_user.Address from x_user_dailly left join x_user on x_user.UserId = x_user_dailly.UserId where x_user_dailly.UserId = ? and RecordDate >= ? and RecordDate < ? order by RecordDate desc "
		dbresult, err := server.Db().Conn().Query(sql, reqdata.UserId, StartTime, EndTime)
		if ctx.RespErr(err, &errcode) {
			return
		}
		for dbresult.Next() {
			data := ReturnData{}
			abugo.GetDbResult(dbresult, &data)
			datas = append(datas, data)
		}
		dbresult.Close()
	} else if len(reqdata.Address) > 0 {
		{
			UserId := 0
			sql := "select UserId from x_user where Address = ?"
			server.Db().QueryScan(sql, []interface{}{reqdata.Address}, &UserId)
			if UserId > 0 {
				sql := "select * from x_user_dailly where UserId = ?  and RecordDate >= ? and RecordDate < ? order by RecordDate desc "
				dbresult, err := server.Db().Conn().Query(sql, UserId, StartTime, EndTime)
				if ctx.RespErr(err, &errcode) {
					return
				}
				for dbresult.Next() {
					data := ReturnData{}
					abugo.GetDbResult(dbresult, &data)
					data.Address = reqdata.Address
					datas = append(datas, data)
				}
				dbresult.Close()
			}
		}
		if len(datas) == 0 {
			sql := "select * from x_guest_dailly where Address = ? and RecordDate >= ? and RecordDate < ? order by RecordDate desc "
			dbresult, err := server.Db().Conn().Query(sql, reqdata.Address, StartTime, EndTime)
			if ctx.RespErr(err, &errcode) {
				return
			}
			for dbresult.Next() {
				data := ReturnData{}
				abugo.GetDbResult(dbresult, &data)
				datas = append(datas, data)
			}
			dbresult.Close()
		}
	}
	ctx.Put("data", datas)
	ctx.Put("total", len(datas))
	ctx.RespOK()
	server.WriteAdminLog("扣除业绩查询", ctx, reqdata)
}
func (c *AgentController) liusui_kouchu(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Ids        string
		Memo       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "业绩扣除", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var dberrcode int
	var dberrmsg string
	server.Db().QueryScan("call x_admin_liusui_kouchu(?,?,?)", []interface{}{reqdata.Ids, token.Account, reqdata.Memo},
		&dberrcode, &dberrmsg)
	if ctx.RespErrString(dberrcode > 0, &errcode, dberrmsg) {
		return
	}
	server.WriteAdminLog("扣除业绩", ctx, reqdata)
	ctx.RespOK()
}

func (c *AgentController) commission_detail(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		UserId    int `validate:"required"`
		StartDate string
		EndDate   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "佣金审核", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndDate == "" {
		reqdata.EndDate = carbon.Now().String()
	}
	if reqdata.StartDate == "" {
		reqdata.StartDate = carbon.Now().SubDay().String()
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "AgentId", "=", reqdata.UserId, 0)
	where.Add("and", "RecordDate", ">=", reqdata.StartDate, "")
	where.Add("and", "RecordDate", "<=", reqdata.EndDate, 0)
	data, err := server.Db().Table("x_commission_source").Where(where).GetList()
	ctx.RespOK(data)
}

type AgentDefine struct {
	SellerId              int     `gorm:"column:SellerId"`              //
	ChannelId             int     `gorm:"column:ChannelId"`             //
	AgentLevel            int     `gorm:"column:AgentLevel"`            //
	LiuSuiHaXiTrx         float64 `gorm:"column:LiuSuiHaXiTrx"`         //
	LiuSuiHaXi            float64 `gorm:"column:LiuSuiHaXi"`            //
	LiuSuiLottery         float64 `gorm:"column:LiuSuiLottery"`         //
	LiuSuiQiPai           float64 `gorm:"column:LiuSuiQiPai"`           //
	LiuSuiDianZhi         float64 `gorm:"column:LiuSuiDianZhi"`         //
	LiuSuiXiaoYouXi       float64 `gorm:"column:LiuSuiXiaoYouXi"`       //
	LiuSuiLive            float64 `gorm:"column:LiuSuiLive"`            //
	LiuSuiSport           float64 `gorm:"column:LiuSuiSport"`           //
	LiuSuiTexas           float64 `gorm:"column:LiuSuiTexas"`           //
	LiuSuiLowLottery      float64 `gorm:"column:LiuSuiLowLottery"`      //
	LiuSuiCryptoMarket    float64 `gorm:"column:LiuSuiCryptoMarket"`    //
	LiuSuiHaXiRouletteTrx float64 `gorm:"column:LiuSuiHaXiRouletteTrx"` //
	LiuSuiHaXiRoulette    float64 `gorm:"column:LiuSuiHaXiRoulette"`    //
	RewardHaXiTrx         float64 `gorm:"column:RewardHaXiTrx"`         //
	RewardHaXi            float64 `gorm:"column:RewardHaXi"`            //
	RewardLottery         float64 `gorm:"column:RewardLottery"`         //
	RewardQiPai           float64 `gorm:"column:RewardQiPai"`           //
	RewardDianZhi         float64 `gorm:"column:RewardDianZhi"`         //
	RewardXiaoYouXi       float64 `gorm:"column:RewardXiaoYouXi"`       //
	RewardLive            float64 `gorm:"column:RewardLive"`            //
	RewardSport           float64 `gorm:"column:RewardSport"`           //
	RewardTexas           float64 `gorm:"column:RewardTexas"`           //
	RewardLowLottery      float64 `gorm:"column:RewardLowLottery"`
	RewardCryptoMarket    float64 `gorm:"column:RewardCryptoMarket"`
	RewardHaXiRouletteTrx float64 `gorm:"column:RewardHaXiRouletteTrx"` //
	RewardHaXiRoulette    float64 `gorm:"column:RewardHaXiRoulette"`    //
}

func (c *AgentController) agent_define_get(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 香港六合彩佣金
	type AgentGameDefine struct {
		//SellerId   int32   `gorm:"column:SellerId"`
		//ChannelId  int32   `gorm:"column:ChannelId"`
		//AgentLevel int32   `gorm:"column:AgentLevel"`
		//Brand      string  `gorm:"column:Brand"`
		//GameId     string  `gorm:"column:GameId"`
		CatId  int32   `gorm:"column:CatId"`
		LiuSui float64 `gorm:"column:LiuSui"`
		Reward float64 `gorm:"column:Reward"`
		Memo   string  `gorm:"column:Memo"`
	}
	type AgentGameDefineArr []AgentGameDefine
	type ResponseData struct {
		SellerId              int     `gorm:"column:SellerId"`              //
		ChannelId             int     `gorm:"column:ChannelId"`             //
		AgentLevel            int     `gorm:"column:AgentLevel"`            //
		LiuSuiHaXiTrx         float64 `gorm:"column:LiuSuiHaXiTrx"`         //
		LiuSuiHaXi            float64 `gorm:"column:LiuSuiHaXi"`            //
		LiuSuiLottery         float64 `gorm:"column:LiuSuiLottery"`         //
		LiuSuiQiPai           float64 `gorm:"column:LiuSuiQiPai"`           //
		LiuSuiDianZhi         float64 `gorm:"column:LiuSuiDianZhi"`         //
		LiuSuiXiaoYouXi       float64 `gorm:"column:LiuSuiXiaoYouXi"`       //
		LiuSuiLive            float64 `gorm:"column:LiuSuiLive"`            //
		LiuSuiSport           float64 `gorm:"column:LiuSuiSport"`           //
		LiuSuiTexas           float64 `gorm:"column:LiuSuiTexas"`           //
		LiuSuiLowLottery      float64 `gorm:"column:LiuSuiLowLottery"`      //
		LiuSuiHaXiRouletteTrx float64 `gorm:"column:LiuSuiHaXiRouletteTrx"` //
		LiuSuiHaXiRoulette    float64 `gorm:"column:LiuSuiHaXiRoulette"`    //
		RewardHaXiTrx         float64 `gorm:"column:RewardHaXiTrx"`         //
		RewardHaXi            float64 `gorm:"column:RewardHaXi"`            //
		RewardLottery         float64 `gorm:"column:RewardLottery"`         //
		RewardQiPai           float64 `gorm:"column:RewardQiPai"`           //
		RewardDianZhi         float64 `gorm:"column:RewardDianZhi"`         //
		RewardXiaoYouXi       float64 `gorm:"column:RewardXiaoYouXi"`       //
		RewardLive            float64 `gorm:"column:RewardLive"`            //
		RewardSport           float64 `gorm:"column:RewardSport"`           //
		RewardTexas           float64 `gorm:"column:RewardTexas"`           //
		RewardLowLottery      float64 `gorm:"column:RewardLowLottery"`
		RewardHaXiRouletteTrx float64 `gorm:"column:RewardHaXiRouletteTrx"` //
		RewardHaXiRoulette    float64 `gorm:"column:RewardHaXiRoulette"`    //
		LiuSuiCryptoMarket    float64 `gorm:"column:LiuSuiCryptoMarket"`
		RewardCryptoMarket    float64 `gorm:"column:RewardCryptoMarket"`
		AgentGameDefineArr    AgentGameDefineArr
	}
	type ResponseDataArr []ResponseData
	responseDataArr := ResponseDataArr{}
	//responseData := ResponseData{}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "无限代", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.SellerId == -1 {
		reqdata.SellerId = 0
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	data, err := server.Db().Table("x_agent_define").Where(where).OrderBy("AgentLevel asc").GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 香港六合彩返佣配置数据
	rows, err := server.Db().Table("x_agent_game_define").Where(where).OrderBy("AgentLevel asc").GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 解析data,组装返回数据
	for i := 0; i < len(*data); i++ {
		// TODO (*data)[i]["AgentGameDefineArr"] = rows
		agentDefine := (*data)[i]
		var responseDataTemp ResponseData
		responseDataTemp.SellerId = ChangeInt(agentDefine, "SellerId")
		responseDataTemp.ChannelId = ChangeInt(agentDefine, "ChannelId")
		responseDataTemp.AgentLevel = ChangeInt(agentDefine, "AgentLevel")
		responseDataTemp.LiuSuiHaXiTrx = agentDefine["LiuSuiHaXiTrx"].(float64)
		responseDataTemp.LiuSuiHaXi = agentDefine["LiuSuiHaXiTrx"].(float64)
		responseDataTemp.LiuSuiDianZhi = agentDefine["LiuSuiDianZhi"].(float64)
		responseDataTemp.LiuSuiLive = agentDefine["LiuSuiLive"].(float64)
		responseDataTemp.LiuSuiLottery = agentDefine["LiuSuiLottery"].(float64)
		responseDataTemp.LiuSuiLowLottery = agentDefine["LiuSuiLowLottery"].(float64)
		responseDataTemp.LiuSuiHaXiRouletteTrx = agentDefine["LiuSuiHaXiRouletteTrx"].(float64)
		responseDataTemp.LiuSuiHaXiRoulette = agentDefine["LiuSuiHaXiRoulette"].(float64)
		responseDataTemp.LiuSuiQiPai = agentDefine["LiuSuiQiPai"].(float64)
		responseDataTemp.LiuSuiSport = agentDefine["LiuSuiSport"].(float64)
		responseDataTemp.LiuSuiTexas = agentDefine["LiuSuiTexas"].(float64)
		responseDataTemp.LiuSuiXiaoYouXi = agentDefine["LiuSuiXiaoYouXi"].(float64)

		responseDataTemp.RewardHaXiTrx = agentDefine["RewardHaXiTrx"].(float64)
		responseDataTemp.RewardHaXi = agentDefine["RewardHaXi"].(float64)
		responseDataTemp.RewardLive = agentDefine["RewardLive"].(float64)
		responseDataTemp.RewardDianZhi = agentDefine["RewardDianZhi"].(float64)
		responseDataTemp.RewardLottery = agentDefine["RewardLottery"].(float64)
		responseDataTemp.RewardQiPai = agentDefine["RewardQiPai"].(float64)
		responseDataTemp.RewardLowLottery = agentDefine["RewardLowLottery"].(float64)
		responseDataTemp.RewardSport = agentDefine["RewardSport"].(float64)
		responseDataTemp.RewardTexas = agentDefine["RewardTexas"].(float64)
		responseDataTemp.RewardXiaoYouXi = agentDefine["RewardXiaoYouXi"].(float64)
		responseDataTemp.RewardHaXiRouletteTrx = agentDefine["RewardHaXiRouletteTrx"].(float64)
		responseDataTemp.RewardHaXiRoulette = agentDefine["RewardHaXiRoulette"].(float64)

		responseDataTemp.LiuSuiCryptoMarket = agentDefine["LiuSuiCryptoMarket"].(float64)
		responseDataTemp.RewardCryptoMarket = agentDefine["RewardCryptoMarket"].(float64)

		for j := 0; j < len(*rows); j++ {
			gameDefine := (*rows)[j]
			var tempGameDefine AgentGameDefine
			agentLevel := ChangeInt(gameDefine, "AgentLevel")
			if agentLevel != responseDataTemp.AgentLevel {
				continue
			}
			tempGameDefine.CatId = int32(ChangeInt(gameDefine, "CatId"))
			tempGameDefine.LiuSui = gameDefine["LiuSui"].(float64)
			tempGameDefine.Reward = gameDefine["Reward"].(float64)
			responseDataTemp.AgentGameDefineArr = append(responseDataTemp.AgentGameDefineArr, tempGameDefine)
		}
		responseDataArr = append(responseDataArr, responseDataTemp)
	}

	ctx.Put("data", responseDataArr)
	ctx.RespOK()
	server.WriteAdminLog("佣金配置-查询", ctx, reqdata)
}

// 从map取值断言转int
func ChangeInt(agentDefine map[string]interface{}, key string) int {
	if tempValue, ok := agentDefine[key]; ok {
		if sellInt, ok := tempValue.(int); ok {
			return sellInt
		} else if sellInt64, ok := tempValue.(int64); ok {
			return int(sellInt64)
		} else if sellInt32, ok := tempValue.(int32); ok {
			return int(sellInt32)
		} else {
			fmt.Println("佣金配置查询-int值断言错误：Value is not of type int")
		}
	} else {
		fmt.Println("佣金配置查询-int值断言错误：Key 'age' not found")
	}
	return 0
}

// 获取香港彩玩法
func (c *AgentController) agent_game_lottery_play(ctx *abugo.AbuHttpContent) {
	defer recover()
	type LotteryPlay struct {
		Id    int64
		PName string
	}
	type ResponseData struct {
		lotteryPlay []LotteryPlay
	}
	responseData := ResponseData{}
	query := `select Id, PName from x_liuhecai_map GROUP BY id, PName`
	params := make([]interface{}, 0)
	rows, err := server.Db().Query(query, params)
	if err != nil {
		panic(err)
	}
	//var respData ResponseData
	for i := 0; i < len(*rows); i++ {
		play := (*rows)[i]
		var lotteryPlay LotteryPlay
		idStr := fmt.Sprintf("%v", play["Id"])
		lotteryPlay.Id, err = strconv.ParseInt(idStr, 10, 64)
		lotteryPlay.PName = fmt.Sprintf("%v", play["PName"])
		responseData.lotteryPlay = append(responseData.lotteryPlay, lotteryPlay)
		//server.Db().GormDao().Table("x_liuhecai_map").Select("Id, PName").Group("Id")
	}
	ctx.Put("", responseData.lotteryPlay)
	ctx.RespOK()
	server.WriteAdminLog("香港彩玩法-查询", ctx, responseData)
}

var agentUTC8 = time.FixedZone("utc+8", 8*3600)

func (c *AgentController) agent_define_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	// 香港六合彩佣金
	type AgentGameDefine struct {
		//SellerId   int32   `gorm:"column:SellerId"`
		//ChannelId  int32   `gorm:"column:ChannelId"`
		//AgentLevel int32   `gorm:"column:AgentLevel"`
		//Brand      string  `gorm:"column:Brand"`
		//GameId     string  `gorm:"column:GameId"`
		CatId  int32   `gorm:"column:CatId"`
		LiuSui float64 `gorm:"column:LiuSui"`
		Reward float64 `gorm:"column:Reward"`
		Memo   string  `gorm:"column:Memo"`
	}
	type AgentGameDefineArr []AgentGameDefine
	type RequestData struct {
		SellerId              int
		ChannelId             int     `validate:"required,gte=1"`
		AgentLevel            int     `validate:"required,gte=1"`
		LiuSuiHaXiTrx         float64 `validate:"min=0"`
		LiuSuiHaXi            float64 `validate:"min=0"`
		LiuSuiLottery         float64 `validate:"min=0"`
		LiuSuiQiPai           float64 `validate:"min=0"`
		LiuSuiDianZhi         float64 `validate:"min=0"`
		LiuSuiXiaoYouXi       float64 `validate:"min=0"`
		LiuSuiLive            float64 `validate:"min=0"`
		LiuSuiSport           float64 `validate:"min=0"`
		LiuSuiTexas           float64 `validate:"min=0"`
		LiuSuiLowLottery      float64 `validate:"min=0"`
		LiuSuiCryptoMarket    float64 `validate:"min=0"`
		LiuSuiHaXiRouletteTrx float64 `validate:"min=0"`
		LiuSuiHaXiRoulette    float64 `validate:"min=0"`
		RewardHaXiTrx         float64 `validate:"min=0,max=100"`
		RewardHaXi            float64 `validate:"min=0,max=100"`
		RewardLottery         float64 `validate:"min=0,max=100"`
		RewardQiPai           float64 `validate:"min=0,max=100"`
		RewardDianZhi         float64 `validate:"min=0,max=100"`
		RewardXiaoYouXi       float64 `validate:"min=0,max=100"`
		RewardLive            float64 `validate:"min=0,max=100"`
		RewardSport           float64 `validate:"min=0,max=100"`
		RewardTexas           float64 `validate:"min=0,max=100"`
		RewardLowLottery      float64 `validate:"min=0,max=100"`
		RewardCryptoMarket    float64 `validate:"min=0,max=100"`
		RewardHaXiRouletteTrx float64 `validate:"min=0,max=100"`
		RewardHaXiRoulette    float64 `validate:"min=0,max=100"`
		//GoogleCode         string
		// 香港六合彩佣金配置
		AgentGameDefineArr AgentGameDefineArr `json:"agentGameDefineArr"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "无限代", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	data := make([]AgentDefine, 0)
	err = server.Db().Gorm().Table("x_agent_define").
		Where("SellerId=? and ChannelId=?", reqdata.SellerId, reqdata.ChannelId).
		Order("AgentLevel desc").
		Find(&data).Error
	if ctx.RespErr(err, &errcode) {
		return
	}
	if len(data) > 0 { //只能从尾部添加代理等级
		lastAgentDefine := data[0]
		if ctx.RespErrString(lastAgentDefine.AgentLevel+1 != reqdata.AgentLevel, &errcode, "添加的AgentLevel不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXiTrx >= reqdata.LiuSuiHaXiTrx, &errcode, "添加的LiuSuiHaXiTrx不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXi >= reqdata.LiuSuiHaXi, &errcode, "添加的LiuSuiHaXi不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiLottery >= reqdata.LiuSuiLottery, &errcode, "添加的LiuSuiLottery不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiQiPai >= reqdata.LiuSuiQiPai, &errcode, "添加的LiuSuiQiPai不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiDianZhi >= reqdata.LiuSuiDianZhi, &errcode, "添加的LiuSuiDianZhi不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiXiaoYouXi >= reqdata.LiuSuiXiaoYouXi, &errcode, "添加的LiuSuiXiaoYouXi不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiLive >= reqdata.LiuSuiLive, &errcode, "添加的LiuSuiLive不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiSport >= reqdata.LiuSuiSport, &errcode, "添加的LiuSuiSport不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiTexas >= reqdata.LiuSuiTexas, &errcode, "添加的LiuSuiTexas不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiLowLottery >= reqdata.LiuSuiLowLottery, &errcode, "添加的LiuSuiLowLottery不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXiRouletteTrx >= reqdata.LiuSuiHaXiRouletteTrx, &errcode, "添加的LiuSuiHaXiRouletteTrx不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXiRoulette >= reqdata.LiuSuiHaXiRoulette, &errcode, "添加的LiuSuiHaXiRoulette不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiCryptoMarket >= reqdata.LiuSuiCryptoMarket, &errcode, "添加的LiuSuiCryptoMarket不合法") {
			return
		}
	} else { //必须从等级1开始添加代理等级
		if ctx.RespErrString(reqdata.AgentLevel != 1, &errcode, "添加的AgentLevel不合法") {
			return
		}
	}

	d := &AgentDefine{
		SellerId:              reqdata.SellerId,
		ChannelId:             reqdata.ChannelId,
		AgentLevel:            reqdata.AgentLevel,
		LiuSuiHaXiTrx:         reqdata.LiuSuiHaXiTrx,
		LiuSuiHaXi:            reqdata.LiuSuiHaXi,
		LiuSuiLottery:         reqdata.LiuSuiLottery,
		LiuSuiQiPai:           reqdata.LiuSuiQiPai,
		LiuSuiDianZhi:         reqdata.LiuSuiDianZhi,
		LiuSuiXiaoYouXi:       reqdata.LiuSuiXiaoYouXi,
		LiuSuiLive:            reqdata.LiuSuiLive,
		LiuSuiSport:           reqdata.LiuSuiSport,
		LiuSuiTexas:           reqdata.LiuSuiTexas,
		LiuSuiLowLottery:      reqdata.LiuSuiLowLottery,
		LiuSuiCryptoMarket:    reqdata.LiuSuiCryptoMarket,
		LiuSuiHaXiRouletteTrx: reqdata.LiuSuiHaXiRouletteTrx,
		LiuSuiHaXiRoulette:    reqdata.LiuSuiHaXiRoulette,
		RewardHaXiTrx:         reqdata.RewardHaXiTrx,
		RewardHaXi:            reqdata.RewardHaXi,
		RewardLottery:         reqdata.RewardLottery,
		RewardQiPai:           reqdata.RewardQiPai,
		RewardDianZhi:         reqdata.RewardDianZhi,
		RewardXiaoYouXi:       reqdata.RewardXiaoYouXi,
		RewardLive:            reqdata.RewardLive,
		RewardSport:           reqdata.RewardSport,
		RewardTexas:           reqdata.RewardTexas,
		RewardLowLottery:      reqdata.RewardLowLottery,
		RewardCryptoMarket:    reqdata.RewardCryptoMarket,
		RewardHaXiRouletteTrx: reqdata.RewardHaXiRouletteTrx,
		RewardHaXiRoulette:    reqdata.RewardHaXiRoulette,
	}

	// 新增香港六合彩
	errcode = -1
	rerr := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		// 新增佣金配置
		err = server.Db().Gorm().Table("x_agent_define").
			Create(d).Error
		if err != nil && strings.Index(err.Error(), "1062: Duplicate entry") > 0 {
			ctx.RespErrString(err != nil, &errcode, "添加失败,该佣金配置已存在")
			logs.Error("添加失败,该佣金配置已存在, err:", err)
			return err
		}
		// 香港六合彩大类集合(初始化佣金等级)
		type LotteryPlay struct {
			Id    int64
			PName string
		}
		type ResponseData struct {
			lotteryPlay []LotteryPlay
		}
		responseData := ResponseData{}
		query := `select Id, PName from x_liuhecai_map GROUP BY id, PName`
		params := make([]interface{}, 0)
		rows, err := server.Db().Query(query, params)
		if err != nil {
			logs.Error("查询相关六合彩玩法大类出错：err:", err)
			panic(err)
		}
		//var respData ResponseData
		for i := 0; i < len(*rows); i++ {
			play := (*rows)[i]
			var lotteryPlay LotteryPlay
			idStr := fmt.Sprintf("%v", play["Id"])
			lotteryPlay.Id, err = strconv.ParseInt(idStr, 10, 64)
			lotteryPlay.PName = fmt.Sprintf("%v", play["PName"])
			responseData.lotteryPlay = append(responseData.lotteryPlay, lotteryPlay)
			//server.Db().GormDao().Table("x_liuhecai_map").Select("Id, PName").Group("Id")
		}

		agentGameDb := tx.XAgentGameDefine.WithContext(context.Background())
		agentGameData := make([]*model.XAgentGameDefine, 0, len(reqdata.AgentGameDefineArr))
		// 获取佣金配置最大等级
		checkData := make([]AgentDefine, 0)
		err = server.Db().Gorm().Table("x_agent_define").
			Where("SellerId=? and ChannelId=?", reqdata.SellerId, reqdata.ChannelId).
			Order("AgentLevel desc").
			First(&checkData).Error
		if ctx.RespErr(err, &errcode) {
			logs.Error("查询原有佣金最大等级出错：err:", err)
			log.Println("查询原有佣金最大等级出错：err:", err)
			return err
		}
		checkDefine := checkData[0]
		maxLevel := checkDefine.AgentLevel
		logs.Info("当前最大佣金配置等级：", maxLevel)
		if maxLevel > 0 {
			for i := 1; i <= maxLevel; i++ {
				// 检查香港六合彩不存在的佣金等级，进行初始化
				if reqdata.AgentLevel == i {
					continue
				}
				xgDefine := model.XAgentGameDefine{}
				err = server.Db().Gorm().Table("x_agent_game_define").
					Where("SellerId=? and ChannelId=? and AgentLevel=?", reqdata.SellerId, reqdata.ChannelId, i).First(&xgDefine).Error
				if err == nil {
					continue
				}
				if xgDefine.AgentLevel == 0 && xgDefine.AgentLevel != int32(reqdata.AgentLevel) {
					// 佣金等级下所有的香港六合彩大类都初始化
					initGameData := make([]*model.XAgentGameDefine, 0, len(reqdata.AgentGameDefineArr))
					for _, v := range responseData.lotteryPlay {
						// 开始新增
						intGameDefine := &model.XAgentGameDefine{
							SellerID:   int32(reqdata.SellerId),
							ChannelID:  int32(reqdata.ChannelId),
							AgentLevel: int32(i),
							Brand:      "gfg",
							GameID:     "1",
							CatID:      int32(v.Id),
							LiuSui:     0,
							Reward:     0,
							Memo:       "",
							CreateTime: time.Now().In(agentUTC8),
							UpdateTime: time.Now().In(agentUTC8),
						}
						initGameData = append(initGameData, intGameDefine)
					}
					err = agentGameDb.CreateInBatches(initGameData, len(initGameData))
					if err != nil {
						logs.Error("初始化香港彩佣金配置错误，初始化佣金等级：", i, "\n err:", err)
						ctx.RespErr(err, &errcode)
						return err
					}
					logs.Info("初始化香港彩佣金配置成功，初始化佣金等级：", i)
				}
			}
		}
		for _, gameDefine := range reqdata.AgentGameDefineArr {

			gameDefineModel := &model.XAgentGameDefine{
				SellerID:   int32(reqdata.SellerId),
				ChannelID:  int32(reqdata.ChannelId),
				AgentLevel: int32(reqdata.AgentLevel),
				Brand:      "gfg", //使用香港六合彩的brand和gameId
				GameID:     "1",
				CatID:      gameDefine.CatId,
				LiuSui:     gameDefine.LiuSui,
				Reward:     gameDefine.Reward,
				Memo:       gameDefine.Memo,
				CreateTime: time.Now().In(agentUTC8),
				UpdateTime: time.Now().In(agentUTC8),
			}
			agentGameData = append(agentGameData, gameDefineModel)
		}
		err = agentGameDb.CreateInBatches(agentGameData, len(agentGameData))
		if err != nil {
			logs.Error("新增香港彩佣金配置错误：", err)
			ctx.RespErr(err, &errcode)
			return err
		}
		return nil
	})

	type ResponseData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	responseData := ResponseData{}
	if rerr != nil {
		responseData.Code = -1
		responseData.Msg = "批量新增香港彩返佣配置失败,err:" + rerr.Error()
		logs.Error(responseData.Msg)
		ctx.RespErr(rerr, &errcode)
		return
	}
	responseData.Code = http.StatusOK
	responseData.Msg = "success"
	ctx.Put("xgData", responseData)
	ctx.RespOK()
	server.WriteAdminLog("佣金配置-添加", ctx, reqdata)
}

func (c *AgentController) agent_define_del(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChannelId  int `validate:"required,gte=1"`
		AgentLevel int `validate:"required,gte=1"`
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	// 删除操作只能删除当前用户所在的厂商
	reqdata.SellerId = token.SellerId
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "无限代", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	data := make([]AgentDefine, 0)
	query := server.Db().Gorm().
		Table("x_agent_define").
		Where("ChannelId=?", reqdata.ChannelId)
	if reqdata.SellerId > 0 {
		query = query.Where("SellerId=?", reqdata.SellerId)
	}
	err = query.Order("AgentLevel desc").Find(&data).Error
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(len(data) <= 0, &errcode, "没有相关的佣金配置记录") {
		return
	}
	if ctx.RespErrString(data[0].AgentLevel != reqdata.AgentLevel, &errcode, "必须从尾部删除佣金配置") {
		return
	}

	d := &AgentDefine{
		SellerId:   reqdata.SellerId,
		ChannelId:  reqdata.ChannelId,
		AgentLevel: reqdata.AgentLevel,
	}
	uquery := server.Db().Gorm().Table("x_agent_define").
		Where("ChannelId=? and AgentLevel=?", reqdata.ChannelId, reqdata.AgentLevel)
	if reqdata.SellerId > 0 {
		query = uquery.Where("SellerId=?", reqdata.SellerId)
	}
	if ctx.RespErr(err, &errcode) {
		return
	}

	gameDel := &model.XAgentGameDefine{
		SellerID:   int32(reqdata.SellerId),
		ChannelID:  int32(reqdata.ChannelId),
		AgentLevel: int32(reqdata.AgentLevel),
	}
	gameQuery := server.Db().Gorm().Table("x_agent_define").
		Where("ChannelId=? and AgentLevel=?", reqdata.ChannelId, reqdata.AgentLevel)
	if reqdata.SellerId > 0 {
		gameQuery = gameQuery.Where("SellerId=?", reqdata.SellerId)
	}
	rerr := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		err = uquery.Delete(d).Error
		if err != nil {
			return err
		}
		err = gameQuery.Delete(gameDel).Error
		if err != nil {
			return err
		}
		return nil
	})
	if rerr != nil {
		logs.Error("删除佣金配置错误,err:", rerr)
		ctx.RespErr(err, &errcode)
		return
	}
	if ctx.RespErr(err, &errcode) {
		return
	}
	logs.Info("删除香港彩佣金配置成功，del:", gameDel)
	ctx.RespOK()
	server.WriteAdminLog("佣金配置-删除", ctx, reqdata)
}

func (c *AgentController) agent_define_mod(ctx *abugo.AbuHttpContent) {
	defer recover()
	// 香港六合彩佣金
	type AgentGameDefine struct {
		//SellerId   int32   `gorm:"column:SellerId"`
		//ChannelId  int32   `gorm:"column:ChannelId"`
		//AgentLevel int32   `gorm:"column:AgentLevel"`
		//Brand      string  `gorm:"column:Brand"`
		//GameId     string  `gorm:"column:GameId"`
		CatId  int32   `gorm:"column:CatId"`
		LiuSui float64 `gorm:"column:LiuSui"`
		Reward float64 `gorm:"column:Reward"`
		Memo   string  `gorm:"column:Memo"`
	}
	type AgentGameDefineArr []AgentGameDefine
	type RequestData struct {
		SellerId              int
		ChannelId             int     `validate:"required,gte=1"`
		AgentLevel            int     `validate:"required,gte=1"`
		LiuSuiHaXiTrx         float64 `validate:"min=0"`
		LiuSuiHaXi            float64 `validate:"min=0"`
		LiuSuiLottery         float64 `validate:"min=0"`
		LiuSuiQiPai           float64 `validate:"min=0"`
		LiuSuiDianZhi         float64 `validate:"min=0"`
		LiuSuiXiaoYouXi       float64 `validate:"min=0"`
		LiuSuiLive            float64 `validate:"min=0"`
		LiuSuiSport           float64 `validate:"min=0"`
		LiuSuiTexas           float64 `validate:"min=0"`
		LiuSuiLowLottery      float64 `validate:"min=0"`
		LiuSuiCryptoMarket    float64 `validate:"min=0"`
		LiuSuiHaXiRouletteTrx float64 `validate:"min=0"`
		LiuSuiHaXiRoulette    float64 `validate:"min=0"`
		RewardHaXiTrx         float64 `validate:"min=0,max=100"`
		RewardHaXi            float64 `validate:"min=0,max=100"`
		RewardLottery         float64 `validate:"min=0,max=100"`
		RewardQiPai           float64 `validate:"min=0,max=100"`
		RewardDianZhi         float64 `validate:"min=0,max=100"`
		RewardXiaoYouXi       float64 `validate:"min=0,max=100"`
		RewardLive            float64 `validate:"min=0,max=100"`
		RewardSport           float64 `validate:"min=0,max=100"`
		RewardTexas           float64 `validate:"min=0,max=100"`
		RewardLowLottery      float64 `validate:"min=0,max=100"`
		RewardCryptoMarket    float64 `validate:"min=0,max=100"`
		RewardHaXiRouletteTrx float64 `validate:"min=0,max=100"`
		RewardHaXiRoulette    float64 `validate:"min=0,max=100"`
		//GoogleCode         string
		// 香港六合彩佣金配置
		AgentGameDefineArr AgentGameDefineArr
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "无限代", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	data := make([]AgentDefine, 0)
	query := server.Db().Gorm().Table("x_agent_define").
		Where("ChannelId=?", reqdata.ChannelId)
	if reqdata.SellerId > 0 {
		query = query.Where("SellerId=?", reqdata.SellerId)
	}
	err = query.Order("AgentLevel asc").Find(&data).Error
	if ctx.RespErrString(len(data) <= 0, &errcode, "没有相关的佣金配置记录") {
		return
	}

	curAgentDefineIndex := reqdata.AgentLevel - 1
	lastAgentDefineIndex := curAgentDefineIndex - 1
	nextAgentDefineIndex := curAgentDefineIndex + 1
	if ctx.RespErrString(curAgentDefineIndex < 0 || curAgentDefineIndex >= len(data), &errcode, "修改的AgentLevel不合法") {
		return
	}
	if lastAgentDefineIndex >= 0 && lastAgentDefineIndex < len(data) {
		lastAgentDefine := data[lastAgentDefineIndex]
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXiTrx > reqdata.LiuSuiHaXiTrx, &errcode, "修改的LiuSuiHaXiTrx不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXi > reqdata.LiuSuiHaXi, &errcode, "修改的LiuSuiHaXi不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiLottery > reqdata.LiuSuiLottery, &errcode, "修改的LiuSuiLottery不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiQiPai > reqdata.LiuSuiQiPai, &errcode, "修改的LiuSuiQiPai不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiDianZhi > reqdata.LiuSuiDianZhi, &errcode, "修改的LiuSuiDianZhi不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiXiaoYouXi > reqdata.LiuSuiXiaoYouXi, &errcode, "修改的LiuSuiXiaoYouXi不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiLive > reqdata.LiuSuiLive, &errcode, "修改的LiuSuiLive不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiSport > reqdata.LiuSuiSport, &errcode, "修改的LiuSuiSport不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiTexas > reqdata.LiuSuiTexas, &errcode, "修改的LiuSuiTexas不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiLowLottery > reqdata.LiuSuiLowLottery, &errcode, "修改.LiuSuiLowLottery不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiCryptoMarket > reqdata.LiuSuiCryptoMarket, &errcode, "修改.LiuSuiCryptoMarket不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXiRouletteTrx > reqdata.LiuSuiHaXiRouletteTrx, &errcode, "修改的LiuSuiHaXiRouletteTrx不合法") {
			return
		}
		if ctx.RespErrString(lastAgentDefine.LiuSuiHaXiRoulette > reqdata.LiuSuiHaXiRoulette, &errcode, "修改的LiuSuiHaXiRoulette不合法") {
			return
		}
	}
	if nextAgentDefineIndex >= 0 && nextAgentDefineIndex < len(data) {
		nextAgentDefine := data[nextAgentDefineIndex]
		if ctx.RespErrString(nextAgentDefine.LiuSuiHaXiTrx < reqdata.LiuSuiHaXiTrx, &errcode, "修改的LiuSuiHaXiTrx不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiHaXi < reqdata.LiuSuiHaXi, &errcode, "修改的LiuSuiHaXi不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiLottery < reqdata.LiuSuiLottery, &errcode, "修改的LiuSuiLottery不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiQiPai < reqdata.LiuSuiQiPai, &errcode, "修改的LiuSuiQiPai不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiDianZhi < reqdata.LiuSuiDianZhi, &errcode, "修改的LiuSuiDianZhi不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiXiaoYouXi < reqdata.LiuSuiXiaoYouXi, &errcode, "修改的LiuSuiXiaoYouXi不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiLive < reqdata.LiuSuiLive, &errcode, "修改的LiuSuiLive不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiSport < reqdata.LiuSuiSport, &errcode, "修改的LiuSuiSport不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiTexas < reqdata.LiuSuiTexas, &errcode, "修改的LiuSuiTexas不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiLowLottery < reqdata.LiuSuiLowLottery, &errcode, "修改的LiuSuiLowLottery不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiCryptoMarket < reqdata.LiuSuiCryptoMarket, &errcode, "修改的LiuSuiCryptoMarket不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiHaXiRouletteTrx < reqdata.LiuSuiHaXiRouletteTrx, &errcode, "修改的LiuSuiHaXiRouletteTrx不合法") {
			return
		}
		if ctx.RespErrString(nextAgentDefine.LiuSuiHaXiRoulette < reqdata.LiuSuiHaXiRoulette, &errcode, "修改的LiuSuiHaXiRoulette不合法") {
			return
		}
	}

	// 更新香港六合彩
	errcode = -1
	rerr := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		// 修改佣金配置
		sql := "update x_agent_define set LiuSuiHaXiTrx=?, LiuSuiHaXi=?, LiuSuiLottery=?, LiuSuiQiPai=?, LiuSuiDianZhi=?, LiuSuiXiaoYouXi=?," +
			" LiuSuiLive=?, LiuSuiSport=?, LiuSuiTexas=?, RewardHaXiTrx=?, RewardHaXi=?, RewardLottery=?, RewardQiPai=?, RewardDianZhi=?, RewardXiaoYouXi=?," +
			" RewardLive=?, RewardSport=?, RewardTexas=? , LiuSuiLowLottery=?,  LiuSuiHaXiRouletteTrx=?, LiuSuiHaXiRoulette=?, RewardLowLottery=?, LiuSuiCryptoMarket=?, RewardCryptoMarket=?, RewardHaXiRouletteTrx=?, RewardHaXiRoulette=? where SellerId=? and ChannelId=? and AgentLevel=?"
		err = server.Db().QueryNoResult(sql, reqdata.LiuSuiHaXiTrx, reqdata.LiuSuiHaXi, reqdata.LiuSuiLottery, reqdata.LiuSuiQiPai,
			reqdata.LiuSuiDianZhi, reqdata.LiuSuiXiaoYouXi, reqdata.LiuSuiLive, reqdata.LiuSuiSport, reqdata.LiuSuiTexas, reqdata.RewardHaXiTrx,
			reqdata.RewardHaXi, reqdata.RewardLottery, reqdata.RewardQiPai, reqdata.RewardDianZhi, reqdata.RewardXiaoYouXi,
			reqdata.RewardLive, reqdata.RewardSport, reqdata.RewardTexas, reqdata.LiuSuiLowLottery, reqdata.LiuSuiHaXiRouletteTrx, reqdata.LiuSuiHaXiRoulette, reqdata.RewardLowLottery, reqdata.LiuSuiCryptoMarket, reqdata.RewardCryptoMarket, reqdata.RewardHaXiRouletteTrx, reqdata.RewardHaXiRoulette, reqdata.SellerId, reqdata.ChannelId, reqdata.AgentLevel)
		if ctx.RespErr(err, &errcode) {
			logs.Error("修改佣金配置错误, err:", err.Error())
			return err
		}
		// 修改香港彩佣金配置
		agentGameData := make([]*model.XAgentGameDefine, 0, len(reqdata.AgentGameDefineArr))
		for _, gameDefine := range reqdata.AgentGameDefineArr {
			gameDefineModel := &model.XAgentGameDefine{
				SellerID:   int32(reqdata.SellerId),
				ChannelID:  int32(reqdata.ChannelId),
				AgentLevel: int32(reqdata.AgentLevel),
				Brand:      "gfg",
				GameID:     "1",
				CatID:      gameDefine.CatId,
				LiuSui:     gameDefine.LiuSui,
				Reward:     gameDefine.Reward,
				Memo:       gameDefine.Memo,
				CreateTime: time.Now().In(agentUTC8),
				UpdateTime: time.Now().In(agentUTC8),
			}
			agentGameData = append(agentGameData, gameDefineModel)
		}
		// 开始批量更新
		for _, update := range agentGameData {
			uQuery := "UPDATE x_agent_game_define SET LiuSui = ?, Reward = ? WHERE SellerID = ? and ChannelID = ? and AgentLevel = ? and Brand = ? and GameID = ? and CatID = ?"
			uErr := server.Db().QueryNoResult(uQuery, update.LiuSui, update.Reward, update.SellerID, update.ChannelID, update.AgentLevel, update.Brand, update.GameID, update.CatID)
			if uErr != nil {
				fmt.Println("香港六合彩佣金配置更新错误:", err, "\n data:", update)
			} else {
				fmt.Printf("成功更新香港六合彩佣金配置，大类ID 为 %d 的记录\n", update.CatID)
			}
		}
		return nil
	})

	type ResponseData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	responseData := ResponseData{}
	if rerr != nil {
		responseData.Code = -1
		responseData.Msg = "批量新增香港彩返佣配置失败,err:" + rerr.Error()
		ctx.RespErr(rerr, &errcode)
		return
	}
	responseData.Code = http.StatusOK
	responseData.Msg = "success"
	ctx.Put("xgData", responseData)
	ctx.RespOK()
	server.WriteAdminLog("佣金配置-修改", ctx, reqdata)
}

type AgentIgnore struct {
	SellerId  int `gorm:"column:SellerId"`  //
	ChannelId int `gorm:"column:ChannelId"` //
	UserId    int `gorm:"column:UserId"`    //
}

func (c *AgentController) agent_set_ignore_agent_get(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理设置", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.SellerId == -1 {
		reqdata.SellerId = 0
	}

	presult := make([]AgentIgnore, 0)
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "x_agent_ignore.SellerId", "=", reqdata.SellerId, 0)
	err = server.Db().Gorm().Table("x_agent_ignore").Where(sql, params...).Order("UserId asc").Find(&presult).Error
	if ctx.RespErr(err, &errcode) {
		return
	}
	presult2 := make([]int, 0, len(presult))
	for i := 0; i < len(presult); i++ {
		presult2 = append(presult2, presult[i].UserId)
	}
	ctx.Put("UserIdList", presult2)

	ctx.RespOK()
	server.WriteAdminLog("代理设置-查询", ctx, reqdata)
}

func (c *AgentController) agent_set_ignore_agent_set(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserIdList []int
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "代理设置", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}
	if reqdata.SellerId == -1 {
		reqdata.SellerId = 0
	}

	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "x_agent_ignore.SellerId", "=", reqdata.SellerId, 0)
	err = server.Db().Gorm().Table("x_agent_ignore").Where(sql, params...).Delete(&AgentIgnore{SellerId: reqdata.SellerId}).Error
	if ctx.RespErr(err, &errcode) {
		return
	}
	if len(reqdata.UserIdList) > 0 {
		sql = "INSERT IGNORE INTO x_agent_ignore(`UserId`) VALUES"
		for i := 0; i < len(reqdata.UserIdList); i++ {
			sql += fmt.Sprintf("(%d),", reqdata.UserIdList[i])
		}
		sql = strings.TrimRight(sql, ",")
		server.Db().QueryNoResult(sql)
		server.Db().QueryNoResult("UPDATE x_agent_ignore SET x_agent_ignore.SellerId = (SELECT x_user.SellerId FROM x_user WHERE x_agent_ignore.UserId = x_user.UserId),x_agent_ignore.ChannelId =  (SELECT x_user.ChannelId FROM x_user WHERE x_agent_ignore.UserId = x_user.UserId)")
		server.Db().QueryNoResult("DELETE FROM x_agent_ignore WHERE SellerId is NULL")
	}
	ctx.RespOK()
	server.WriteAdminLog("代理设置-设置", ctx, reqdata)
}

// 独立代理列表 - 获取独立代理的基本信息列表
func (c *AgentController) agent_independence_list(ctx *abugo.AbuHttpContent) {
	// 请求参数结构体
	type RequestData struct {
		SellerId      int    // 运营商ID
		ChannelId     int    // 渠道ID
		UserId        int    // 用户ID
		Page          int    // 页码
		PageSize      int    // 每页数量
		Host          string // 域名
		AgentShortUrl string // 代理短链接 - 新增字段
	}

	// 错误码初始化
	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 获取用户token并验证权限
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "查"), &errcode, "权限不足") {
		return
	}

	// 验证运营商权限
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	// 如果是渠道后台用户,只能查看自己渠道的数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = token.ChannelId
	}

	// 构建SQL查询条件
	sql := ""
	params := []interface{}{}

	// 添加运营商筛选条件
	if reqdata.SellerId > 0 {
		server.Db().AddWhere(&sql, &params, "and", "x_agent_independence.SellerId", "=", reqdata.SellerId, 0)
	}

	// 添加渠道筛选条件
	if reqdata.ChannelId > 0 {
		server.Db().AddWhere(&sql, &params, "and", "x_agent_independence.ChannelId", "=", reqdata.ChannelId, 0)
	}

	// 添加用户ID筛选条件
	if reqdata.UserId > 0 {
		server.Db().AddWhere(&sql, &params, "and", "x_agent_independence.UserId", "=", reqdata.UserId, 0)
	}

	// 添加域名模糊查询条件
	if len(reqdata.Host) > 0 {
		server.Db().AddWhere(&sql, &params, "and", "x_agent_independence.Host", "like", "%"+reqdata.Host+"%", nil)
	}

	// 添加代理短链接精确查询条件 - 新增条件
	if len(reqdata.AgentShortUrl) > 0 {
		server.Db().AddWhere(&sql, &params, "and", "x_user.AgentShortUrl", "=", reqdata.AgentShortUrl, nil)
	}

	// 构建基础SQL查询语句,关联用户表获取短链接信息
	baseSql := `SELECT x_agent_independence.*, x_user.AgentShortUrl
            FROM x_agent_independence 
            LEFT JOIN x_user ON x_user.UserId = x_agent_independence.UserId`

	// 添加WHERE条件
	if sql != "" {
		baseSql += " WHERE " + sql
	}

	// 查询总记录数
	countResult, err := server.Db().Query("SELECT COUNT(*) as count FROM ("+baseSql+") as t", params)
	if ctx.RespErr(err, &errcode) {
		return
	}
	total := int64(0)
	if len(*countResult) > 0 {
		total = abugo.GetInt64FromInterface((*countResult)[0]["count"])
	}

	// 分页查询数据
	baseSql += fmt.Sprintf(" ORDER BY x_agent_independence.id DESC LIMIT %d,%d", (reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
	data, err := server.Db().Query(baseSql, params)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 设置返回数据
	ctx.Put("url", server.ImageUrl())
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()

	// 记录管理日志
	server.WriteAdminLog("独立代理-列表", ctx, reqdata)
}

func (c *AgentController) agent_independence_list_all(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Page       int
		PageSize   int
		UserId     int
		AgentId    int
		TopAgentId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "代理列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	total, data := db.Agent_Page_Data_independence(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.UserId, reqdata.AgentId, reqdata.TopAgentId, token.ChannelId)
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("独立代理-代理列表", ctx, reqdata)
}

func (c *AgentController) agent_independence_list_all_histroy(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Page     int
		PageSize int
		UserId   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "代理列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	xAgentIndependenceFenchengHistoryDo := server.DaoxHashGame().XAgentIndependenceFenchengHistory
	xAgentIndependenceFenchengHistoryDb := server.DaoxHashGame().XAgentIndependenceFenchengHistory.WithContext(context.Background())
	if reqdata.UserId > 0 {
		xAgentIndependenceFenchengHistoryDb = xAgentIndependenceFenchengHistoryDb.Where(xAgentIndependenceFenchengHistoryDo.UserID.Eq(int32(reqdata.UserId)))
	}
	data, count, _ := xAgentIndependenceFenchengHistoryDb.Order(xAgentIndependenceFenchengHistoryDo.ID.Desc()).FindByPage((reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
	ctx.Put("data", data)
	ctx.Put("count", count)
	ctx.RespOK()
	server.WriteAdminLog("独立代理-代理列表", ctx, reqdata)
}

func (c *AgentController) agent_independence_list_all_detail(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Amount    float64
		AgentId   int
		StartTime int64
		EndTime   int64
		GroupGame int // 1否 2 是
		Page      int
		PageSize  int

		CommissiType int    // 0 不限 1 自身 2 直属 3 团队
		OrderId      int64  // 订单ID
		Symbol       string // 币种类型 usdt trx
		Export       int    //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金审核", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	startTime := carbon.CreateFromTimestamp(reqdata.StartTime).StdTime()
	endTime := carbon.CreateFromTimestamp(reqdata.EndTime).StdTime()
	if reqdata.StartTime <= 1 {
		startTime = carbon.Now().StartOfDay().StdTime()
		endTime = carbon.Now().EndOfDay().StdTime()
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	detailtb := server.DaoxHashGame().XAgentIndependentCommissionDetail
	detaildb := server.DaoxHashGame().XAgentIndependentCommissionDetail.WithContext(context.Background())
	detaildb = detaildb.Where(detailtb.CreateTime.Gte(startTime)).Where(detailtb.CreateTime.Lte(endTime))
	detaildb = detaildb.Where(detailtb.AgentID.Eq(int32(reqdata.AgentId)))
	if reqdata.CommissiType > 0 {
		detaildb = detaildb.Where(detailtb.CommissionType.Eq(int32(reqdata.CommissiType)))
	}
	if reqdata.OrderId > 0 {
		detaildb = detaildb.Where(detailtb.OrderID.Eq(reqdata.OrderId))
	}
	if reqdata.Symbol == "usdt" || reqdata.Symbol == "trx" {
		detaildb = detaildb.Where(detailtb.Symbol.Eq(reqdata.Symbol))
	}
	if reqdata.GroupGame == 2 {
		data, count, err := detaildb.Group(detailtb.GameType, detailtb.UserID).Select(
			detailtb.UserID, detailtb.GameType, detailtb.Rate.Max().As("Rate"),
			detailtb.BetAmount.Sum().As("BetAmount"), detailtb.Commission.Sum().As("Commission")).FindByPage(
			(reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
		if err != nil {
			ctx.Put("total", 0)
		}
		ctx.Put("total", count)
		ctx.Put("data", data)
	} else {
		data, count, err := detaildb.FindByPage((reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
		if err != nil {
			ctx.Put("total", 0)
		}
		sum := model.XAgentIndependentCommissionDetail{}
		detaildb.Select(detailtb.Commission.Sum().As("Commission"), detailtb.BetAmount.Sum().As("BetAmount")).Limit(-1).Offset(-1).Scan(&sum)

		if reqdata.Export == 0 {
			ctx.Put("total", count)
			ctx.Put("data", data)
			ctx.Put("sum", sum)
		} else {
			xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_独代每日佣金返佣详情_%s", time.Now().Format("20060102150405")))
			xlsx.Open()
			//设置表头
			xlsx.SetTitle("UserID", "下级")
			xlsx.SetTitle("CommissionType", "返佣类型")
			xlsx.SetTitle("GameType", "游戏类型")
			xlsx.SetTitle("BetUserID", "玩家ID")
			xlsx.SetTitle("FromAddress", "钱包地址")
			xlsx.SetTitle("OrderID", "注单号")
			xlsx.SetTitle("BetAmount", "投注金额")
			xlsx.SetTitle("Rate", "返佣比例")
			xlsx.SetTitle("Commission", "返佣金额")
			xlsx.SetTitle("CreateTime", "下注时间")
			xlsx.SetTitle("SysTime", "算佣时间")
			xlsx.SetTitle("AgentFenCheng", "上级分成")
			xlsx.SetTitle("FenCheng", "分成")
			xlsx.SetTitleStyle()
			for i := 0; i < len(data); i++ {
				t := data[i]
				xlsx.SetValue("UserID", t.UserID, int64(i+2))
				xlsx.SetValue("CommissionType", utils.Commission98TypeNick(int(t.CommissionType)), int64(i+2))
				xlsx.SetValue("GameType", utils.GameBigTypeNick(t.GameType), int64(i+2))
				xlsx.SetValue("BetUserID", t.BetUserID, int64(i+2))
				xlsx.SetValue("FromAddress", t.FromAddress, int64(i+2))
				xlsx.SetValue("OrderID", t.OrderID, int64(i+2))
				xlsx.SetValue("BetAmount", t.BetAmount, int64(i+2))
				xlsx.SetValue("Rate", t.Rate, int64(i+2))
				xlsx.SetValue("Commission", t.Commission, int64(i+2))
				xlsx.SetValue("CreateTime", carbon.CreateFromStdTime(t.CreateTime).ToDateTimeString(), int64(i+2))
				xlsx.SetValue("SysTime", carbon.CreateFromStdTime(t.SysTime).ToDateTimeString(), int64(i+2))
				xlsx.SetValue("AgentFenCheng", t.AgentFenCheng, int64(i+2))
				xlsx.SetValue("FenCheng", t.FenCheng, int64(i+2))
			}
			xlsx.SetValue("UserID", "合计", int64(count+2))
			xlsx.SetValue("BetAmount", sum.BetAmount, int64(count+2))
			xlsx.SetValue("Commission", sum.Commission, int64(count+2))
			xlsx.SetValueStyle(int64(count + 2))

			filePath, err := xlsx.ProduceFile()
			if ctx.RespErr(err, &errcode) {
				return
			}
			ctx.Put("filename", "/exports/"+path.Base(filePath))
		}
	}
	ctx.Put("amount", reqdata.Amount)
	ctx.RespOK()
	server.WriteAdminLog("独立代理-代理列表", ctx, reqdata)
}

func (c *AgentController) agent_independence_list_all_fine(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		Symbol     string
		FineAmount float64
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "代理列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}
	if ctx.RespErrString(reqdata.FineAmount <= 0, &errcode, "罚没金额错误") {
		return
	}
	db := server.DaoxHashGame()
	rerr := db.Transaction(func(tx *dao.Query) error {
		agenttb := tx.XAgent
		agentdb := tx.XAgent.WithContext(context.Background())
		if reqdata.Symbol == "usdt" {
			agentData, err := agentdb.Clauses(clause.Locking{Strength: "UPDATE"}).Where(agenttb.UserID.Eq(int32(reqdata.UserId))).First()
			if err != nil {
				return err
			}
			remain := agentData.BackCommissionUsdtT1 - reqdata.FineAmount
			availableRemain := agentData.AvailableCommissionUsdtT1 + remain
			logs.Error(remain, availableRemain)
			if remain >= 0 {
				result, err := agentdb.Where(agenttb.UserID.Eq(int32(reqdata.UserId))).Update(agenttb.BackCommissionUsdtT1, remain)
				if err != nil {
					logs.Error(result, err)
					return err
				}
			} else if availableRemain >= 0 {
				result, err := agentdb.Where(agenttb.UserID.Eq(int32(reqdata.UserId))).UpdateSimple(
					agenttb.BackCommissionUsdtT1.Value(0),
					agenttb.AvailableCommissionUsdtT1.Value(availableRemain),
				)
				if err != nil {
					logs.Error(result, err)
					return err
				}
			} else {
				return errors.New("罚没金额错误")
			}
			agentdb.Where(agenttb.UserID.Eq(int32(reqdata.UserId))).UpdateSimple(agenttb.FineCommissionUsdtT1.Add(reqdata.FineAmount))
		} else if reqdata.Symbol == "trx" {
			agentData, err := agentdb.Clauses(clause.Locking{Strength: "UPDATE"}).Where(agenttb.UserID.Eq(int32(reqdata.UserId))).First()
			if err != nil {
				return err
			}
			remain := agentData.BackCommissionTrxT1 - reqdata.FineAmount
			availableRemain := agentData.AvailableCommissionTrxT1 + remain
			if remain >= 0 {
				result, err := agentdb.Where(agenttb.UserID.Eq(int32(reqdata.UserId))).Update(agenttb.BackCommissionTrxT1, remain)
				if err != nil {
					logs.Error(result, err)
					return err
				}
			} else if availableRemain >= 0 {
				result, err := agentdb.Where(agenttb.UserID.Eq(int32(reqdata.UserId))).UpdateSimple(
					agenttb.BackCommissionTrxT1.Value(0),
					agenttb.AvailableCommissionTrxT1.Value(availableRemain),
				)
				if err != nil {
					logs.Error(result, err)
					return err
				}
			} else {
				return errors.New("罚没金额错误")
			}
			agentdb.Where(agenttb.UserID.Eq(int32(reqdata.UserId))).UpdateSimple(agenttb.FineCommissionTrxT1.Add(reqdata.FineAmount))
		} else {
			return errors.New("usdt or trx")
		}
		agentdailytb := tx.XAgentDailly
		agentdailydb := tx.XAgentDailly.WithContext(context.Background())
		dateTime := carbon.Now().StartOfDay().StdTime()
		updatedb := agentdailydb.Where(agentdailytb.UserID.Eq(int32(reqdata.UserId)))
		updatedb = updatedb.Where(agentdailytb.RecordDate.Eq(dateTime))
		_, err := updatedb.First()
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			usertb := tx.XUser
			userdb := tx.XUser.WithContext(context.Background())
			userdata, _ := userdb.Where(usertb.UserID.Eq(int32(reqdata.UserId))).First()
			if userdata == nil {
				return errors.New("用户不存在")
			}
			var fineCommissionTrxT1, fineCommissionUsdtT1 float64
			if reqdata.Symbol == "trx" {
				fineCommissionTrxT1 = reqdata.FineAmount
			} else if reqdata.Symbol == "usdt" {
				fineCommissionUsdtT1 = reqdata.FineAmount
			}
			err := agentdailydb.Create(&model.XAgentDailly{
				SellerID:             userdata.SellerID,
				ChannelID:            userdata.ChannelID,
				UserID:               userdata.UserID,
				RecordDate:           dateTime,
				Address:              userdata.Address,
				LastUpdateTime:       dateTime,
				FineCommissionTrxT1:  fineCommissionTrxT1,
				FineCommissionUsdtT1: fineCommissionUsdtT1,
			})
			if err != nil {
				return errors.New("daily用户创建失败")
			}
		} else {
			var info gen.ResultInfo
			var infoerr error
			if reqdata.Symbol == "trx" {
				info, infoerr = updatedb.UpdateSimple(agentdailytb.FineCommissionTrxT1.Add(reqdata.FineAmount))
			} else if reqdata.Symbol == "usdt" {
				info, infoerr = updatedb.UpdateSimple(agentdailytb.FineCommissionUsdtT1.Add(reqdata.FineAmount))
			}
			if info.RowsAffected <= 0 {
				logs.Error(infoerr)
				return errors.New("daily用户跟新失败")
			}
		}
		return nil
	})
	if rerr != nil {
		ctx.RespErrString(true, &errcode, rerr.Error())
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("独立代理-代理列表-改", ctx, reqdata)
}

func (c *AgentController) agent_independence_list_all_team(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		AgentId   int
		StartTime int64
		EndTime   int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "代理列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	tb := server.DaoxHashGame().XAgentChild
	db := server.DaoxHashGame().XAgentChild.WithContext(context.Background())
	db = db.Where(tb.UserID.Eq(int32(reqdata.AgentId))).Order(tb.ID)
	if reqdata.SellerId != 0 {
		db = db.Where(tb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	children, count, err := db.FindByPage((reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
	if ctx.RespErrString(err != nil, &errcode, "db error XAgentChild") {
		return
	}
	if len(children) <= 0 {
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}

	type responseData struct {
		UserId     int32
		AgentId    int32
		ChildLevel int32
		IsAgent    bool
		BetHashU   float64
		BetHashT   float64
		BetDianzi  float64
		BetLive    float64
		BetQipai   float64
		BetSport   float64
		BetLottery float64
		BetQuwei   float64
		BetTexas   float64
	}

	datas := make(map[int32]*responseData)
	for i := 0; i < len(children); i++ {
		child := children[i]
		t := &responseData{}
		datas[child.Child] = t
		t.UserId = child.Child
		t.ChildLevel = child.ChildLevel
	}
	usertb := server.DaoxHashGame().XUser
	userdb := server.DaoxHashGame().XUser.WithContext(context.Background())
	var userIds []int32
	for _, v := range children {
		userIds = append(userIds, v.Child)
	}
	users, err := userdb.Select(usertb.UserID, usertb.IsAgent, usertb.AgentID).Where(usertb.UserID.In(userIds...)).Find()
	if ctx.RespErrString(err != nil, &errcode, "db error users") {
		return
	}
	for _, v := range users {
		datas[v.UserID].AgentId = v.AgentID
		datas[v.UserID].IsAgent = v.IsAgent == gamedefine.Game_DB_Yes
	}
	startTime := carbon.CreateFromTimestamp(reqdata.StartTime).StdTime()
	endTime := carbon.CreateFromTimestamp(reqdata.EndTime).EndOfDay().StdTime()
	ordertb := server.DaoxHashGame().XOrder
	orderdb := server.DaoxHashGame().XOrder.WithContext(context.Background())
	orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
	type orderResult struct {
		AmountSum float64
		UserId    int32
		Symbol    string
	}
	var orders []*orderResult
	err = orderdb.Select(ordertb.Amount.Sum().As("AmountSum"), ordertb.UserID, ordertb.Symbol).Group(ordertb.UserID, ordertb.Symbol).Scan(&orders)
	if ctx.RespErrString(err != nil, &errcode, "db error orderdb") {
		logs.Error(err)
		return
	}
	for _, v := range orders {
		if v.Symbol == "usdt" {
			datas[v.UserId].BetHashU = v.AmountSum
		} else if v.Symbol == "trx" {
			datas[v.UserId].BetHashT = v.AmountSum
		}
	}

	{
		ordertb := server.DaoxHashGame().XThirdDianzhi
		orderdb := server.DaoxHashGame().XThirdDianzhi.WithContext(context.Background())
		orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
		type orderResult struct {
			AmountSum float64
			UserId    int32
		}
		var orders []*orderResult
		err = orderdb.Select(ordertb.BetAmount.Sum().As("AmountSum"), ordertb.UserID).Group(ordertb.UserID).Scan(&orders)
		if ctx.RespErrString(err != nil, &errcode, "db error XThirdDianzhi") {
			logs.Error(err)
			return
		}
		for _, v := range orders {
			datas[v.UserId].BetDianzi = v.AmountSum
		}
	}
	{
		ordertb := server.DaoxHashGame().XThirdLive
		orderdb := server.DaoxHashGame().XThirdLive.WithContext(context.Background())
		orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
		type orderResult struct {
			AmountSum float64
			UserId    int32
		}
		var orders []*orderResult
		err = orderdb.Select(ordertb.BetAmount.Sum().As("AmountSum"), ordertb.UserID).Group(ordertb.UserID).Scan(&orders)
		if ctx.RespErrString(err != nil, &errcode, "db error XThirdDianzhi") {
			logs.Error(err)
			return
		}
		for _, v := range orders {
			datas[v.UserId].BetLive = v.AmountSum
		}
	}
	{
		ordertb := server.DaoxHashGame().XThirdLottery
		orderdb := server.DaoxHashGame().XThirdLottery.WithContext(context.Background())
		orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
		type orderResult struct {
			AmountSum float64
			UserId    int32
		}
		var orders []*orderResult
		err = orderdb.Select(ordertb.BetAmount.Sum().As("AmountSum"), ordertb.UserID).Group(ordertb.UserID).Scan(&orders)
		if ctx.RespErrString(err != nil, &errcode, "db error XThirdDianzhi") {
			logs.Error(err)
			return
		}
		for _, v := range orders {
			datas[v.UserId].BetLottery = v.AmountSum
		}
	}
	{
		ordertb := server.DaoxHashGame().XThirdQipai
		orderdb := server.DaoxHashGame().XThirdQipai.WithContext(context.Background())
		orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
		type orderResult struct {
			AmountSum float64
			UserId    int32
		}
		var orders []*orderResult
		err = orderdb.Select(ordertb.BetAmount.Sum().As("AmountSum"), ordertb.UserID).Group(ordertb.UserID).Scan(&orders)
		if ctx.RespErrString(err != nil, &errcode, "db error XThirdDianzhi") {
			logs.Error(err)
			return
		}
		for _, v := range orders {
			datas[v.UserId].BetQipai = v.AmountSum
		}
	}
	{
		ordertb := server.DaoxHashGame().XThirdQuwei
		orderdb := server.DaoxHashGame().XThirdQuwei.WithContext(context.Background())
		orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
		type orderResult struct {
			AmountSum float64
			UserId    int32
		}
		var orders []*orderResult
		err = orderdb.Select(ordertb.BetAmount.Sum().As("AmountSum"), ordertb.UserID).Group(ordertb.UserID).Scan(&orders)
		if ctx.RespErrString(err != nil, &errcode, "db error XThirdDianzhi") {
			logs.Error(err)
			return
		}
		for _, v := range orders {
			datas[v.UserId].BetQuwei = v.AmountSum
		}
	}
	{
		ordertb := server.DaoxHashGame().XThirdSport
		orderdb := server.DaoxHashGame().XThirdSport.WithContext(context.Background())
		orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
		type orderResult struct {
			AmountSum float64
			UserId    int32
		}
		var orders []*orderResult
		err = orderdb.Select(ordertb.BetAmount.Sum().As("AmountSum"), ordertb.UserID).Group(ordertb.UserID).Scan(&orders)
		if ctx.RespErrString(err != nil, &errcode, "db error XThirdDianzhi") {
			logs.Error(err)
			return
		}
		for _, v := range orders {
			datas[v.UserId].BetSport = v.AmountSum
		}
	}
	{
		ordertb := server.DaoxHashGame().XThirdTexa
		orderdb := server.DaoxHashGame().XThirdTexa.WithContext(context.Background())
		orderdb = orderdb.Where(ordertb.UserID.In(userIds...)).Where(ordertb.CreateTime.Between(startTime, endTime))
		type orderResult struct {
			AmountSum float64
			UserId    int32
		}
		var orders []*orderResult
		err = orderdb.Select(ordertb.BetAmount.Sum().As("AmountSum"), ordertb.UserID).Group(ordertb.UserID).Scan(&orders)
		if ctx.RespErrString(err != nil, &errcode, "db error XThirdTexa") {
			logs.Error(err)
			return
		}
		for _, v := range orders {
			datas[v.UserId].BetTexas = v.AmountSum
		}
	}

	var list []*responseData
	for i := 0; i < len(children); i++ {
		child := children[i]
		list = append(list, datas[child.Child])
	}
	ctx.Put("data", list)
	ctx.Put("total", count)
	ctx.RespOK()
	server.WriteAdminLog("独立代理-代理列表", ctx, reqdata)
}

func (c *AgentController) agent_independence_daily(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		UserId    int32
		StartTime int64
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "每日佣金", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_独代每日佣金_%s", time.Now().Format("20060102150405")))
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("UserId", "代理ID")
		xlsx.SetTitle("AgentNickName", "代理昵称")
		xlsx.SetTitle("TotalCommissionTrx_t1", "返佣总额Trx")
		xlsx.SetTitle("GetedCommissionTrx_t1", "已领佣金Trx")
		xlsx.SetTitle("AvailableCommissionTrx_t1", "待领金额Trx")
		xlsx.SetTitle("BackCommissionTrx_t1", "已退金额Trx")
		xlsx.SetTitle("FineCommissionTrx_t1", "已罚没金额Trx")
		xlsx.SetTitle("TotalCommissionUsdt_t1", "返佣总额Usdt")
		xlsx.SetTitle("GetedCommissionUsdt_t1", "已领佣金Usdt")
		xlsx.SetTitle("AvailableCommissionUsdt_t1", "待领金额Usdt")
		xlsx.SetTitle("BackCommissionUsdt_t1", "已退金额Usdt")
		xlsx.SetTitle("FineCommissionUsdt_t1", "已罚没金额Usdt")
		xlsx.SetTitleStyle()
	}

	agenttb := server.DaoxHashGame().XAgent
	agentdb := server.DaoxHashGame().XAgent.WithContext(context.Background())
	agentdailytb := server.DaoxHashGame().XAgentDailly
	agentdailydb := server.DaoxHashGame().XAgentDailly.WithContext(context.Background())
	XAgentIndependencetb := server.DaoxHashGame().XAgentIndependence
	XAgentIndependencedb := server.DaoxHashGame().XAgentIndependence.WithContext(context.Background())
	usertb := server.DaoxHashGame().XUser
	userdb := server.DaoxHashGame().XUser.WithContext(context.Background())
	type Response struct {
		UserId                     int32
		AgentNickName              string
		TotalCommissionTrx_t1      float64
		FineCommissionTrx_t1       float64
		AvailableCommissionTrx_t1  float64
		BackCommissionTrx_t1       float64
		GetedCommissionTrx_t1      float64
		TotalCommissionUsdt_t1     float64
		FineCommissionUsdt_t1      float64
		AvailableCommissionUsdt_t1 float64
		BackCommissionUsdt_t1      float64
		GetedCommissionUsdt_t1     float64
	}
	type ResponseSum struct {
		TotalCommissionTrxT1      float64
		GetedCommissionTrxT1      float64
		AvailableCommissionTrxT1  float64
		BackCommissionTrxT1       float64
		FineCommissionTrxT1       float64
		TotalCommissionUsdtT1     float64
		GetedCommissionUsdtT1     float64
		AvailableCommissionUsdtT1 float64
		BackCommissionUsdtT1      float64
		FineCommissionUsdtT1      float64
	}
	startTime := carbon.CreateFromTimestamp(reqdata.StartTime).StartOfDay().StdTime()

	db := agentdb.Join(userdb, usertb.UserID.EqCol(agenttb.UserID))
	if reqdata.SellerId > 0 {
		db = db.Where(agenttb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		db = db.Where(agenttb.ChannelID.Eq(int32(token.ChannelId)))
	}
	db = db.Where(db.Where(userdb.Columns(agenttb.UserID).In(XAgentIndependencedb.Select(XAgentIndependencetb.UserID))).Or(
		usertb.Columns(usertb.TopAgentID).In(XAgentIndependencedb.Select(XAgentIndependencetb.UserID))))
	db = db.Where(agentdailytb.RecordDate.Eq(startTime))
	if reqdata.UserId > 0 {
		db = db.Where(agenttb.UserID.Eq(reqdata.UserId))
	}
	sumdb := agentdailydb.Where(agentdailytb.RecordDate.Eq(startTime))
	sumdb = sumdb.Where(sumdb.Columns(agentdailytb.UserID).In(db.Select(agenttb.UserID)))
	responseSum := ResponseSum{}
	sumdb.Select(agentdailytb.TotalCommissionTrxT1.Sum().As("TotalCommissionTrxT1"),
		agentdailytb.FineCommissionTrxT1.Sum().As("FineCommissionTrxT1"),
		agentdailytb.AvailableCommissionTrxT1.Sum().As("AvailableCommissionTrxT1"),
		agentdailytb.BackCommissionTrxT1.Sum().As("BackCommissionTrxT1"),
		agentdailytb.GetedCommissionTrxT1.Sum().As("GetedCommissionTrxT1"),
		agentdailytb.TotalCommissionUsdtT1.Sum().As("TotalCommissionUsdtT1"),
		agentdailytb.FineCommissionUsdtT1.Sum().As("FineCommissionUsdtT1"),
		agentdailytb.AvailableCommissionUsdtT1.Sum().As("AvailableCommissionUsdtT1"),
		agentdailytb.BackCommissionUsdtT1.Sum().As("BackCommissionUsdtT1"),
		agentdailytb.GetedCommissionUsdtT1.Sum().As("GetedCommissionUsdtT1")).Scan(&responseSum)
	var results []*Response
	db = db.LeftJoin(agentdailydb, agentdailytb.UserID.EqCol(agenttb.UserID))
	configtb := server.DaoxHashGame().XConfig
	configdb := server.DaoxHashGame().XConfig.WithContext(context.Background())
	configdata, err := configdb.Where(configtb.ID.Eq(22)).First()
	rate := "0.13"
	if err == nil && configdata != nil {
		rate = configdata.ConfigValue
	}
	ordersql := fmt.Sprintf("(%s.TotalCommissionUsdt_t1 + %s.TotalCommissionTrx_t1 * %s)", agentdailytb.TableName(), agentdailytb.TableName(), rate)
	db = db.Order(field.NewRaw("", ordersql).Desc())
	count, err := db.Select(agenttb.UserID, usertb.AgentNickName,
		agentdailytb.TotalCommissionTrxT1,
		agentdailytb.FineCommissionTrxT1,
		agentdailytb.AvailableCommissionTrxT1,
		agentdailytb.BackCommissionTrxT1,
		agentdailytb.GetedCommissionTrxT1,
		agentdailytb.TotalCommissionUsdtT1,
		agentdailytb.FineCommissionUsdtT1,
		agentdailytb.AvailableCommissionUsdtT1,
		agentdailytb.BackCommissionUsdtT1,
		agentdailytb.GetedCommissionUsdtT1).ScanByPage(
		&results, (reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
	if ctx.RespErrString(err != nil, &errcode, "db error agent_independence_daily") {
		logs.Error(err)
		return
	}
	if reqdata.Export != 1 {
		ctx.Put("data", struct {
			List []*Response
			Sum  *ResponseSum
		}{List: results, Sum: &responseSum})
		ctx.Put("total", count)
	} else {
		for i := 0; i < len(results); i++ {
			xlsx.SetValue("UserId", results[i].UserId, int64(i+2))
			xlsx.SetValue("AgentNickName", results[i].AgentNickName, int64(i+2))
			xlsx.SetValue("TotalCommissionTrx_t1", results[i].TotalCommissionTrx_t1, int64(i+2))
			xlsx.SetValue("GetedCommissionTrx_t1", results[i].GetedCommissionTrx_t1, int64(i+2))
			xlsx.SetValue("AvailableCommissionTrx_t1", results[i].AvailableCommissionTrx_t1, int64(i+2))
			xlsx.SetValue("BackCommissionTrx_t1", results[i].BackCommissionTrx_t1, int64(i+2))
			xlsx.SetValue("FineCommissionTrx_t1", results[i].FineCommissionTrx_t1, int64(i+2))
			xlsx.SetValue("TotalCommissionUsdt_t1", results[i].TotalCommissionUsdt_t1, int64(i+2))
			xlsx.SetValue("GetedCommissionUsdt_t1", results[i].GetedCommissionUsdt_t1, int64(i+2))
			xlsx.SetValue("AvailableCommissionUsdt_t1", results[i].AvailableCommissionUsdt_t1, int64(i+2))
			xlsx.SetValue("BackCommissionUsdt_t1", results[i].BackCommissionUsdt_t1, int64(i+2))
			xlsx.SetValue("FineCommissionUsdt_t1", results[i].FineCommissionUsdt_t1, int64(i+2))
		}
		xlsx.SetValue("UserId", "合计", int64(count+2))
		xlsx.SetValue("TotalCommissionTrx_t1", responseSum.TotalCommissionTrxT1, int64(count+2))
		xlsx.SetValue("GetedCommissionTrx_t1", responseSum.GetedCommissionTrxT1, int64(count+2))
		xlsx.SetValue("AvailableCommissionTrx_t1", responseSum.AvailableCommissionTrxT1, int64(count+2))
		xlsx.SetValue("BackCommissionTrx_t1", responseSum.BackCommissionTrxT1, int64(count+2))
		xlsx.SetValue("FineCommissionTrx_t1", responseSum.FineCommissionTrxT1, int64(count+2))
		xlsx.SetValue("TotalCommissionUsdt_t1", responseSum.TotalCommissionUsdtT1, int64(count+2))
		xlsx.SetValue("GetedCommissionUsdt_t1", responseSum.GetedCommissionUsdtT1, int64(count+2))
		xlsx.SetValue("AvailableCommissionUsdt_t1", responseSum.AvailableCommissionUsdtT1, int64(count+2))
		xlsx.SetValue("BackCommissionUsdt_t1", responseSum.BackCommissionUsdtT1, int64(count+2))
		xlsx.SetValue("FineCommissionUsdt_t1", responseSum.FineCommissionUsdtT1, int64(count+2))
		xlsx.SetValueStyle(int64(count + 2))

		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("独立代理-每日佣金", ctx, reqdata)
}

// 新增独立代理
func (c *AgentController) agent_independence_add(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId       int
		UserId         int `validate:"required"`
		AgentHost      string
		CommissionRate float64
		Memo           string
		AgentName      string
		ShowName       string
		Icon           string
		Logo           string
		IsSuper        int
		AgentUseId     int `validate:"required"`
		//GoogleCode     string
		AddType       int
		PromotionHost string `validate:"required"`
		Password      string
		Logo2         string
		IosIcon       string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	if reqdata.AddType == 1 && reqdata.Password == "" {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}

	rediskey := fmt.Sprintf("%v:%v:agent_independence_add_ing", server.Project(), server.Module())
	lck := server.Redis().SetNxString(rediskey, "1", 60)

	if lck != nil {
		ctx.RespErrString(true, &errcode, "正在添加中,请稍后再试")
		return
	}

	defer func() {
		server.Redis().Del(rediskey)
	}()

	adata, _ := server.Db().Query("select id from x_agent_independence where UserId = ?", []interface{}{reqdata.UserId})
	if ctx.RespErrString(len(*adata) > 0, &errcode, "该用户已经是独立代理") {
		return
	}

	udata, _ := server.Db().Query("select * from x_user where UserId = ?", []interface{}{reqdata.UserId})
	if ctx.RespErrString(len(*udata) == 0, &errcode, "该用户不存在") {
		return
	}

	var channelHost *model.XChannelHost
	if len(reqdata.AgentHost) > 0 {
		channelHostTb := server.DaoxHashGame().XChannelHost
		channelHostDb := server.DaoxHashGame().XChannelHost.WithContext(context.Background())
		channelHost, err = channelHostDb.Where(channelHostTb.Host.Eq(reqdata.AgentHost)).Where(channelHostTb.State.Eq(1)).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
	}

	UserId := abugo.GetInt64FromInterface((*udata)[0]["UserId"])
	SellerId := abugo.GetInt64FromInterface((*udata)[0]["SellerId"])
	ChannelId := abugo.GetInt64FromInterface((*udata)[0]["ChannelId"])
	AgentId := abugo.GetInt64FromInterface((*udata)[0]["AgentId"])
	if reqdata.SellerId > 0 && int64(reqdata.SellerId) != SellerId {
		ctx.RespErrString(true, &errcode, "该用户不存在")
		return
	}
	if channelHost != nil && int64(channelHost.ChannelID) != ChannelId {
		ctx.RespErrString(true, &errcode, "域名渠道不正确")
		return
	}

	if ctx.RespErrString(AgentId != 0, &errcode, "该用户有上级代理,不可成为独立代理") {
		return
	}

	// 特殊处理
	addressSellerId := reqdata.SellerId
	if reqdata.SellerId != 34 {
		addressSellerId = 0
	}

	games, _ := server.Db().Query("select * from x_game where (GameId < 100 or ( GameId > 300 and GameId < 350)) and ChannelId = ? and TopAgentId = 0", []interface{}{2})
	needaddresscount := int64(len(*games))

	addresscnt, _ := server.Db().Query("select count(id) as count from x_game_address_pool where SellerId = ? and State = 1 and AddType = 1", []interface{}{addressSellerId})
	if ctx.RespErrString(abugo.GetInt64FromInterface((*addresscnt)[0]["count"]) < needaddresscount, &errcode, "地址池不足,请联系管理员") {
		return
	}

	bscAddresscnt, _ := server.Db().Query("select count(id) as count from x_game_address_pool where SellerId = ? and State = 1 and AddType = 2", []interface{}{addressSellerId})
	if ctx.RespErrString(abugo.GetInt64FromInterface((*bscAddresscnt)[0]["count"]) < needaddresscount, &errcode, "Bsc地址池不足,请联系管理员") {
		return
	}

	games, _ = server.Db().Query("select * from x_game where ChannelId = ? and TopAgentId = 0 ", []interface{}{2})

	acode, _ := server.Db().Query("select * from x_agent_code where UserId = ?", []interface{}{reqdata.UserId})
	AgentCode := ""

	if ctx.RespErrString(len(*acode) == 0, &errcode, "该玩家尚未生成邀请码,请先生成邀请码") {
		return
	}

	AgentCode = (*acode)[0]["AgentCode"].(string)
	for i := 0; i < len(*games); i++ {
		g := (*games)[i]
		GAddress := abugo.GetStringFromInterface(g["Address"])
		BscAddress := abugo.GetStringFromInterface(g["BscAddress"])
		gameId := abugo.GetInt64FromInterface(g["GameId"])
		if (gameId >= 1 && gameId <= 13) || (gameId >= 301 && gameId <= 333) {
			addressdata, _ := server.Db().Query("select * from x_game_address_pool where SellerId = ? and AddType = 1 and State = 1 order by id asc limit 1", []interface{}{addressSellerId})
			bscAddressdata, _ := server.Db().Query("select * from x_game_address_pool where SellerId = ? and AddType = 2 and State = 1 order by id asc limit 1", []interface{}{addressSellerId})
			gid := abugo.GetInt64FromInterface((*addressdata)[0]["Id"])
			bscGid := abugo.GetInt64FromInterface((*bscAddressdata)[0]["Id"])
			GameId := abugo.GetInt64FromInterface(g["GameId"])
			RoomLevel := abugo.GetInt64FromInterface(g["RoomLevel"])
			GAddress = abugo.GetStringFromInterface((*addressdata)[0]["Address"])
			BscAddress = abugo.GetStringFromInterface((*bscAddressdata)[0]["Address"])
			server.Db().Query("update x_game_address_pool set State = 2,GameId = ?,RoomLevel = ? ,AgentId = ?,ChannelId = ?,SellerId = ? where Id = ?", []interface{}{GameId, RoomLevel, UserId, ChannelId, SellerId, gid})
			server.Db().Query("update x_game_address_pool set State = 2,GameId = ?,RoomLevel = ? ,AgentId = ?,ChannelId = ?,SellerId = ? where Id = ?", []interface{}{GameId, RoomLevel, UserId, ChannelId, SellerId, bscGid})
		} else {
			GAddress = GAddress + fmt.Sprintf("_%v", UserId)
			BscAddress = BscAddress + fmt.Sprintf("_%v", UserId)
		}
		g["Address"] = GAddress
		g["BscAddress"] = BscAddress
		g["ChannelId"] = ChannelId
		g["SellerId"] = SellerId
		delete(g, "Id")
		g["TopAgentId"] = UserId
		_, e := server.Db().Table("x_game").Insert(g)
		if e != nil {
			logs.Error("新增独立代理失败,插入游戏失败", e)
		}
	}
	if reqdata.AgentUseId > 0 {
		afangan, _ := server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.AgentUseId).First()
		if afangan == nil {
			ctx.RespErrString(true, &errcode, "佣金方案不存在")
			return
		}
		bytes, _ := json.Marshal(afangan.RawData)
		server.XDb().Table("x_record").Insert(map[string]interface{}{
			"RType":         "佣金方案_" + fmt.Sprint(reqdata.UserId),
			"Data":          string(bytes),
			"CreateAccount": token.Account,
		})
		server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).Update(map[string]interface{}{
			"AgentUseId":   reqdata.AgentUseId,
			"SpecialAgent": 1,
		})
		server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.AgentUseId).Update(map[string]interface{}{
			"UseCount": 1,
		})
	}
	sql := `insert into x_agent_independence(UserId, Host, CommissionRate, Memo,SellerId,ChannelId,CustomeService,MajorGameOrder,MinorGameOrder,AgentCode,AgentName,ShowName,Icon,Logo,IsSuper,AgentUseId, Logo2, PromotionHost, IosIcon)
	values(?, ?, ?, ?,?,?,'{}','{}','{}',?,?,?,?,?,?,?,?,?,?)`
	server.Db().Query(sql, []interface{}{reqdata.UserId, reqdata.AgentHost, reqdata.CommissionRate, reqdata.Memo, (*udata)[0]["SellerId"], (*udata)[0]["ChannelId"], AgentCode, reqdata.AgentName, reqdata.ShowName, reqdata.Icon, reqdata.Logo, reqdata.IsSuper, reqdata.AgentUseId, reqdata.Logo2, utils.RemoveHTTPPrefix(reqdata.PromotionHost), reqdata.IosIcon})
	server.Db().Query("update x_game set AgentName = ? where TopAgentId = ?", []interface{}{reqdata.AgentName, UserId})
	server.XDb().Table("x_user").Where("x_user.TopAgentId = ?", reqdata.UserId).Update(map[string]interface{}{
		"FenCheng": `{"haxitrx":0,"haxiusdt":0,"dianzi":0,"live":0,"qipai":0,"sport":0,"lottery":0,"chain":0,"texas":0,"lowLottery":0,"cryptoMarket":0,"haxiroulettetrx":0,"haxirouletteusdt":0}`,
	})
	password := abugo.GetStringFromInterface2((*udata)[0]["Password"])
	if reqdata.AddType == 1 && xgo.Md5(reqdata.Password) != password {
		userTb := server.DaoxHashGame().XUser
		userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
		hashPwd := xgo.Md5(reqdata.Password)
		row, err := userDb.Where(userTb.UserID.Eq(int32(reqdata.UserId))).Updates(map[string]any{
			"Password": hashPwd,
		})
		logs.Info("agent_independence_add Update Password RowsAffected: ", row.RowsAffected)
		if err != nil || row.RowsAffected <= 0 {
			logs.Error("agent_independence_add Update Password err: ", err)
			ctx.RespErrString(true, &errcode, "修改密码失败")
			return
		}
	}
	if len(reqdata.AgentHost) > 0 && channelHost == nil {
		channelHostTb := server.DaoxHashGame().XChannelHost
		channelHostModel := &model.XChannelHost{
			ChannelID: int32(ChannelId),
			Host:      reqdata.AgentHost,
			State:     1,
		}
		channelHostDb := server.DaoxHashGame().XChannelHost.WithContext(context.Background())
		err = channelHostDb.Omit(channelHostTb.SocialLinks).Create(channelHostModel)
		if err != nil {
			logs.Error("agent_independence_add add channelHost err: ", err)
			ctx.RespErrString(true, &errcode, "同步专属域名失败")
			return
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("新增独立代理", ctx, reqdata)
}

func (c *AgentController) agent_independence_delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}
	sql := "UPDATE x_game_address_pool SET State = 1,GameId = 0,RoomLevel = 0,AgentId = 0,SellerId = 0,ChannelId = 0 WHERE AgentId = ?"
	server.Db().Query(sql, []interface{}{reqdata.UserId})
	server.Db().Query("DELETE FROM x_agent_independence WHERE UserId = ?", []interface{}{reqdata.UserId})
	server.Db().Query("DELETE FROM x_game where TopAgentId = ?", []interface{}{reqdata.UserId})
	server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).Update(map[string]interface{}{
		"SpecialAgent": 2,
	})
	ctx.RespOK()
}

func (c *AgentController) agent_independence_modify(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId                int
		UserId                  int     `validate:"required"`
		Memo                    string  //备注
		Host                    string  //域名
		CustomeService          string  //客服
		IsSelfHost              int     //是否自定义域名
		IsDuiHuan               int     //是否自定义兑换
		IsSelfBackOffice        int     //是否自定义后台
		IsSelfTgBot             int     //是否自定义tg机器人
		IsSelfActive            int     //是否自定义活动
		IsSelfMajorGameOrder    int     //是否自定义大游戏排序
		IsSelfMajorGameOrderNew int     //是否自定义大新游戏排序
		IsSelfMinorGameOrder    int     //是否自定义小游戏排序
		IsSelfAddress           int     //是否自定义地址
		IsSelfCustomService     int     //是否自定义客服
		MajorGameOrder          string  //大游戏排序
		MajorGameOrderNew       string  // 新大类排序
		MinorGameOrder          string  //小游戏排序
		CommissionRate          float64 //佣金比例
		TgInfo                  string
		ActiveInfo              string
		BackOffaceInfo          string
		AgentName               string
		ShowName                string
		Icon                    string
		Logo                    string
		Logo2                   string
		IosIcon                 string
		IsSuper                 int
		AgentUseId              int
		//GoogleCode              string
		IsBatch int // 是否批量修改所有独立代理的游戏排序和开关 1:是 2:否
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	reqdata.Host = strings.Trim(reqdata.Host, " ")
	reqdata.Host = strings.Trim(reqdata.Host, "\t")
	reqdata.Host = strings.Trim(reqdata.Host, "\r")
	reqdata.Host = strings.Trim(reqdata.Host, "\n")

	rdata := xgo.H{}
	if reqdata.Host != "" {
		rdata["Host"] = reqdata.Host
	}
	if reqdata.CommissionRate > 0 {
		rdata["CommissionRate"] = reqdata.CommissionRate
	}
	if reqdata.Memo != "" {
		rdata["Memo"] = reqdata.Memo
	}
	if reqdata.CustomeService != "" {
		rdata["CustomeService"] = reqdata.CustomeService
	}
	if reqdata.IsSelfHost > 0 {
		rdata["IsSelfHost"] = reqdata.IsSelfHost
	}
	if reqdata.IsDuiHuan > 0 {
		rdata["IsDuiHuan"] = reqdata.IsDuiHuan
	}
	if reqdata.IsSelfBackOffice > 0 {
		rdata["IsSelfBackOffice"] = reqdata.IsSelfBackOffice
	}
	if reqdata.IsSelfTgBot > 0 {
		rdata["IsSelfTgBot"] = reqdata.IsSelfTgBot
	}
	if reqdata.IsSelfActive > 0 {
		rdata["IsSelfActive"] = reqdata.IsSelfActive
	}
	if reqdata.IsSelfMajorGameOrder > 0 {
		rdata["IsSelfMajorGameOrder"] = reqdata.IsSelfMajorGameOrder
	}
	if reqdata.IsSelfMajorGameOrderNew > 0 {
		rdata["IsSelfMajorGameOrderNew"] = reqdata.IsSelfMajorGameOrderNew
	}
	if reqdata.IsSelfMinorGameOrder > 0 {
		rdata["IsSelfMinorGameOrder"] = reqdata.IsSelfMinorGameOrder
	}
	if reqdata.IsSelfAddress > 0 {
		rdata["IsSelfAddress"] = reqdata.IsSelfAddress
	}
	if reqdata.IsSelfCustomService > 0 {
		rdata["IsSelfCustomService"] = reqdata.IsSelfCustomService
	}
	if reqdata.MajorGameOrder != "" {
		rdata["MajorGameOrder"] = reqdata.MajorGameOrder
	}
	if reqdata.MajorGameOrderNew != "" {
		rdata["MajorGameOrderNew"] = reqdata.MajorGameOrderNew
	}
	if reqdata.MinorGameOrder != "" {
		rdata["MinorGameOrder"] = reqdata.MinorGameOrder
	}
	if reqdata.TgInfo != "" {
		rdata["TgInfo"] = reqdata.TgInfo
	}
	if reqdata.ActiveInfo != "" {
		rdata["ActiveInfo"] = reqdata.ActiveInfo
	}
	if reqdata.BackOffaceInfo != "" {
		rdata["BackOffaceInfo"] = reqdata.BackOffaceInfo
	}
	if reqdata.AgentName != "" {
		rdata["AgentName"] = reqdata.AgentName
		server.XDb().Table("x_game").Where("TopAgentId = ?", reqdata.UserId).Update(xgo.H{"AgentName": reqdata.AgentName})
	}
	if reqdata.ShowName != "" {
		rdata["ShowName"] = reqdata.ShowName
	}
	if reqdata.Icon != "" {
		rdata["Icon"] = reqdata.Icon
	}
	if reqdata.Logo != "" {
		rdata["Logo"] = reqdata.Logo
	}
	if reqdata.Logo2 != "" {
		rdata["Logo2"] = reqdata.Logo2
	}
	if reqdata.IosIcon != "" {
		rdata["IosIcon"] = reqdata.IosIcon
	}
	if reqdata.IsSuper > 0 {
		rdata["IsSuper"] = reqdata.IsSuper
	}
	if reqdata.AgentUseId > 0 {
		ad, _ := server.XDb().Table("x_agent_independence").Where("UserId = ?", reqdata.UserId).First()
		if ad.Int("AgentUseId") != reqdata.AgentUseId {
			afangan, _ := server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.AgentUseId).First()
			if afangan == nil {
				ctx.RespErrString(true, &errcode, "佣金方案不存在")
				return
			}
			bytes, _ := json.Marshal(afangan.RawData)
			server.XDb().Table("x_record").Insert(map[string]interface{}{
				"RType":         "佣金方案_" + fmt.Sprint(reqdata.UserId),
				"Data":          string(bytes),
				"CreateAccount": token.Account,
			})

			rdata["AgentUseId"] = reqdata.AgentUseId
			server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).Update(map[string]interface{}{
				"AgentUseId": reqdata.AgentUseId,
			})

			old, _ := server.XDb().Table("x_agent_commission_config").Where("Id = ?", ad.Int("AgentUseId")).First()
			needReset := false
			if old != nil {
				oldData := make(map[string]any)
				json.Unmarshal([]byte(old.String("Data")), &oldData)
				newData := make(map[string]any)
				json.Unmarshal([]byte(afangan.String("Data")), &newData)
				logs.Info("newData ", afangan.String("Data"))
				logs.Info("oldData ", old.String("Data"))

				newRate := newData["rate"].(map[string]interface{})
				oldRate := oldData["rate"].(map[string]interface{})

				getValue := func(data map[string]interface{}, key string) float64 {
					if value, ok := data[key]; ok {
						if v, ok := value.(float64); ok {
							return v
						}
					}
					return 0.0 // 默认返回0.0，或根据需求选择合适的默认值
				}

				if getValue(newRate, "chain") < getValue(oldRate, "chain") ||
					getValue(newRate, "dianzi") < getValue(oldRate, "dianzi") ||
					getValue(newRate, "haxitrx") < getValue(oldRate, "haxitrx") ||
					getValue(newRate, "haxiusdt") < getValue(oldRate, "haxiusdt") ||
					getValue(newRate, "live") < getValue(oldRate, "live") ||
					getValue(newRate, "lottery") < getValue(oldRate, "lottery") ||
					getValue(newRate, "qipai") < getValue(oldRate, "qipai") ||
					getValue(newRate, "sport") < getValue(oldRate, "sport") ||
					getValue(newRate, "texas") < getValue(oldRate, "texas") ||
					getValue(newRate, "lowLottery") < getValue(oldRate, "lowLottery") ||
					getValue(newRate, "cryptoMarket") < getValue(oldRate, "cryptoMarket") ||
					getValue(newRate, "haxiroulettetrx") < getValue(oldRate, "haxiroulettetrx") ||
					getValue(newRate, "haxiroulette") < getValue(oldRate, "haxiroulette") {

					needReset = true
				}
				//if newData["rate"].(map[string]any)["chain"].(float64) < oldData["rate"].(map[string]any)["chain"].(float64) ||
				//	newData["rate"].(map[string]any)["dianzi"].(float64) < oldData["rate"].(map[string]any)["dianzi"].(float64) ||
				//	newData["rate"].(map[string]any)["haxitrx"].(float64) < oldData["rate"].(map[string]any)["haxitrx"].(float64) ||
				//	newData["rate"].(map[string]any)["haxiusdt"].(float64) < oldData["rate"].(map[string]any)["haxiusdt"].(float64) ||
				//	newData["rate"].(map[string]any)["live"].(float64) < oldData["rate"].(map[string]any)["live"].(float64) ||
				//	newData["rate"].(map[string]any)["lottery"].(float64) < oldData["rate"].(map[string]any)["lottery"].(float64) ||
				//	newData["rate"].(map[string]any)["qipai"].(float64) < oldData["rate"].(map[string]any)["qipai"].(float64) ||
				//	newData["rate"].(map[string]any)["sport"].(float64) < oldData["rate"].(map[string]any)["sport"].(float64) ||
				//	newData["rate"].(map[string]any)["texas"].(float64) < oldData["rate"].(map[string]any)["texas"].(float64) ||
				//	newData["rate"].(map[string]any)["lowLottery"].(float64) < oldData["rate"].(map[string]any)["lowLottery"].(float64) {
				//
				//	needReset = true
				//}
			}
			if old == nil || needReset {
				var modifyData []*model.XAgentIndependenceFenchengHistory
				xUserDo := server.DaoxHashGame().XUser
				xUserDb := server.DaoxHashGame().XUser.WithContext(context.Background())
				xAgentIndependenceFenchengHistoryDo := server.DaoxHashGame().XAgentIndependenceFenchengHistory
				xAgentIndependenceFenchengHistoryDb := server.DaoxHashGame().XAgentIndependenceFenchengHistory.WithContext(context.Background())
				xUserDb.Select(
					xUserDo.UserID, xUserDo.FenCheng.As(xAgentIndependenceFenchengHistoryDo.ChangeBefore.ColumnName().String()),
					xUserDo.AgentID.As(xAgentIndependenceFenchengHistoryDo.AgentID.ColumnName().String()),
				).Where(xUserDo.TopAgentID.Eq(int32(reqdata.UserId))).Scan(&modifyData)
				for _, v := range modifyData {
					v.Reason = 3 // 方案变化
					v.ChangeAfter = `{"haxitrx":0,"haxiusdt":0,"dianzi":0,"live":0,"qipai":0,"sport":0,"lottery":0,"chain":0,"texas":0,"lowLottery":0,"cryptoMarket":0,"haxiroulettetrx":0,"haxirouletteusdt":0}`
				}
				xAgentIndependenceFenchengHistoryDb.CreateInBatches(modifyData, 10000)

				server.XDb().Table("x_user").Where("x_user.TopAgentId = ?", reqdata.UserId).Update(map[string]interface{}{
					"FenCheng": `{"haxitrx":0,"haxiusdt":0,"dianzi":0,"live":0,"qipai":0,"sport":0,"lottery":0,"chain":0,"texas":0,"lowLottery":0,"cryptoMarket":0,"haxiroulettetrx":0,"haxirouletteusdt":0}`,
				})
				server.XDb().Table("x_agent_code_t1").Where("x_agent_code_t1.UserId in (select userid from x_user where x_user.TopAgentId = ? or x_user.userid = ?)", reqdata.UserId, reqdata.UserId).Update(
					map[string]interface{}{
						"FenCheng": `{"haxitrx":0,"haxiusdt":0,"dianzi":0,"live":0,"qipai":0,"sport":0,"lottery":0,"chain":0,"texas":0,"lowLottery":0,"cryptoMarket":0,"haxiroulettetrx":0,"haxirouletteusdt":0}`,
					})

			}
			server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.AgentUseId).Update(map[string]interface{}{
				"UseCount": 1,
			})
		}
	}
	if reqdata.IsBatch == 1 {
		rdata2 := xgo.H{}
		rdata2["MajorGameOrder"] = reqdata.MajorGameOrder
		rdata2["MinorGameOrder"] = reqdata.MinorGameOrder
		rdata2["MajorGameOrderNew"] = reqdata.MajorGameOrderNew
		server.XDb().Table("x_agent_independence").Update(rdata2)
	} else {
		server.XDb().Table("x_agent_independence").Where("UserId = ?", reqdata.UserId).Update(rdata)
	}
	ctx.RespOK()
	server.WriteAdminLog("修改独立代理", ctx, reqdata)
}

/*
ALTER TABLE x_third_dianzhi ADD COLUMN TopAgentId INT DEFAULT(0);
ALTER TABLE x_third_live ADD COLUMN TopAgentId INT DEFAULT(0);
ALTER TABLE x_third_lottery ADD COLUMN TopAgentId INT DEFAULT(0);
ALTER TABLE x_third_qipai ADD COLUMN TopAgentId INT DEFAULT(0);
ALTER TABLE x_third_sport ADD COLUMN TopAgentId INT DEFAULT(0);
ALTER TABLE x_third_quwei ADD COLUMN TopAgentId INT DEFAULT(0);


ALTER TABLE x_third_dianzhi ADD KEY TopAgentId(TopAgentId);
ALTER TABLE x_third_live ADD KEY TopAgentId(TopAgentId);
ALTER TABLE x_third_lottery ADD KEY TopAgentId(TopAgentId);
ALTER TABLE x_third_qipai ADD KEY TopAgentId(TopAgentId);
ALTER TABLE x_third_sport ADD KEY TopAgentId(TopAgentId);
ALTER TABLE x_third_quwei ADD KEY TopAgentId(TopAgentId);

ALTER TABLE x_caijing_detail ADD COLUMN TopAgentId INT DEFAULT(0);
ALTER TABLE x_caijing_detail ADD KEY TopAgentId(TopAgentId);
*/

func (c *AgentController) agent_independence_report(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId []int
		UserId    []int
		StartTime int64
		EndTime   int64
		Page      int
		PageSize  int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "数据报表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	channelid := ""
	for i := 0; i < len(reqdata.ChannelId); i++ {
		channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	}
	if len(channelid) > 0 {
		channelid = channelid[0 : len(channelid)-1]
		where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	}
	userid := ""
	for i := 0; i < len(reqdata.UserId); i++ {
		userid = userid + fmt.Sprintf("%d,", reqdata.UserId[i])
	}
	if len(userid) > 0 {
		userid = userid[0 : len(userid)-1]
		where.Add("and", "UserId", "in", fmt.Sprintf("(%s)", userid), nil)
	}

	total, data := server.Db().Table("x_agent_independence").OrderBy("id desc").Where(where).Select("Id,SellerId,ChannelId,UserId,Host").PageData(reqdata.Page, reqdata.
		PageSize)

	rdata := make([]map[string]interface{}, 0)
	for i := 0; i < len(*data); i++ {
		item := (*data)[i]
		userdata, _ := server.Db().Query("select UserId,RegisterTime,LoginTime,Address from x_user where UserId = ?", []interface{}{item["UserId"]})
		if userdata == nil || len(*userdata) == 0 {

			continue
		}
		item["RegisterTime"] = (*userdata)[0]["RegisterTime"]
		item["LoginTime"] = (*userdata)[0]["LoginTime"]
		item["Address"] = (*userdata)[0]["Address"]
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "RegisterTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "RegisterTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_user").Select("count(id) as count,sum(Amount) as Amount").Where(where).GetOne()
			item["NewCount"] = (*data)["count"]
			item["Amount"] = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			data, _ := server.Db().Table("x_user").Select("count(id) as count").Where(where).GetOne()
			item["ChildCount"] = (*data)["count"]
		}
		{
			where := ""
			if abugo.TimeStampToLocalTime(reqdata.StartTime) != "" {
				if where != "" {
					where += " and "
				}
				where += "CreateTime >= '" + abugo.TimeStampToLocalTime(reqdata.StartTime) + "'"
			}
			if abugo.TimeStampToLocalTime(reqdata.EndTime) != "" {
				if where != "" {
					where += " and "
				}
				where += "CreateTime < '" + abugo.TimeStampToLocalTime(reqdata.EndTime) + "'"
			}

			sql := fmt.Sprintf("SELECT UserId FROM x_order WHERE TopAgentId = %d and UserId > 0", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			sql += fmt.Sprintf("SELECT FromAddress FROM x_order WHERE TopAgentId = %d and UserId = 0", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			where = strings.ReplaceAll(where, "CreateTime", "ThirdTime")

			sql += fmt.Sprintf("SELECT UserId FROM x_third_dianzhi WHERE TopAgentId = %d  ", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			sql += fmt.Sprintf("SELECT UserId FROM x_third_live WHERE TopAgentId = %d  ", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			sql += fmt.Sprintf("SELECT UserId FROM x_third_lottery WHERE TopAgentId = %d  ", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			sql += fmt.Sprintf("SELECT UserId FROM x_third_qipai WHERE TopAgentId = %d  ", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			sql += fmt.Sprintf("SELECT UserId FROM x_third_quwei WHERE TopAgentId = %d  ", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			sql += fmt.Sprintf("SELECT UserId FROM x_third_sport WHERE TopAgentId = %d  ", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			sql += " union "

			sql += fmt.Sprintf("SELECT UserId FROM x_third_texas WHERE TopAgentId = %d  ", item["UserId"])
			if where != "" {
				sql += " and " + where
			}
			data, _ := server.Db().Query(sql, []interface{}{})
			item["BetCount"] = len(*data)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_recharge").Select("count(DISTINCT UserId) as count,sum(RealAmount) as RealAmount").Where(where).GetOne()
			item["RechargeCount"] = (*data)["count"]
			item["RechargeAmount"] = abugo.GetFloat64FromInterface((*data)["RealAmount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "State", ">", 1, nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_withdraw").Select("count(DISTINCT UserId) as count,sum(Amount) as Amount").Where(where).GetOne()
			item["WithdrawCount"] = (*data)["count"]
			item["WithdrawAmount"] = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "usdt", nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_caijing_detail").Select("sum(Amount) as Amount").Where(where).GetOne()
			item["CaiJingAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "trx", nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_caijing_detail").Select("sum(Amount) as Amount").Where(where).GetOne()
			item["CaiJingAmountTrx"] = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("sum(GasFee) as GasFee").Where(where).GetOne()
			item["GasFee"] = abugo.GetFloat64FromInterface((*data)["GasFee"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "trx", nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("count(id) as  count,sum(Amount) as Amount,sum(RewardAmount) as RewardAmount").Where(where).GetOne()
			item["TransferCountTrx"] = (*data)["count"]
			item["TransferBetAmountTrx"] = abugo.GetFloat64FromInterface((*data)["Amount"])
			item["TransferWinAmountTrx"] = abugo.GetFloat64FromInterface((*data)["RewardAmount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "usdt", nil)
			where.Add("and", "GameId", "<", 100, nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("count(id) as  count,sum(Amount) as Amount,sum(RewardAmount) as RewardAmount").Where(where).GetOne()
			item["TransferCountUsdt"] = (*data)["count"]
			item["TransferBetAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["Amount"])
			item["TransferWinAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["RewardAmount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "TopAgentId", "=", item["UserId"], nil)
			where.Add("and", "GameId", ">", 100, nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("count(id) as  count,sum(Amount) as Amount,sum(RewardAmount) as RewardAmount").Where(where).GetOne()
			item["YueCountUsdt"] = (*data)["count"]
			item["YueBetAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["Amount"])
			item["YueWinAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["RewardAmount"])
		}
		ApiBetCount := 0
		ApiBetAmount := 0.0
		ApiWinAmount := 0.0
		tables := []string{"x_third_dianzhi", "x_third_live", "x_third_lottery", "x_third_qipai", "x_third_sport", "x_third_quwei", "x_third_texas"}
		for i := 0; i < len(tables); i++ {
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "TopAgentId", "=", item["UserId"], nil)
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
				where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
				data, _ := server.Db().Table(tables[i]).Select("count(id) as count,sum(BetAmount) as BetAmount,sum(WinAmount) as WinAmount").Where(where).GetOne()
				ApiBetCount += int(abugo.GetInt64FromInterface((*data)["count"]))
				ApiBetAmount += abugo.GetFloat64FromInterface((*data)["BetAmount"])
				ApiWinAmount += abugo.GetFloat64FromInterface((*data)["WinAmount"])
			}
		}
		item["ApiBetCount"] = ApiBetCount
		item["ApiBetAmount"] = ApiBetAmount
		item["ApiWinAmount"] = ApiWinAmount
		rdata = append(rdata, item)
	}
	ctx.Put("total", total)
	ctx.Put("data", rdata)
	ctx.RespOK()
}

func (c *AgentController) agent_independence_report_detail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId  int
		AgentId   int
		StartTime int64
		EndTime   int64
		Page      int
		PageSize  int
		QueryType int //1 下级人数,2 新增人数,3 老会员人数
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "数据报表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "TopAgentId", "=", reqdata.AgentId, 0)
	if reqdata.QueryType == 2 {
		where.Add("and", "RegisterTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("and", "RegisterTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	} else if reqdata.QueryType == 3 {
		where.Add("and", "RegisterTime", "<", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("or", "RegisterTime", ">=", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	}
	total, data := server.Db().Table("x_user").OrderBy("id desc").Where(where).Select("Id,SellerId,ChannelId,UserId,Address,RegisterTime,LoginTime,Amount,AgentId").PageData(reqdata.Page, reqdata.PageSize)

	for i := 0; i < len(*data); i++ {
		item := (*data)[i]
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_recharge").Select("sum(RealAmount) as RealAmount").Where(where).GetOne()
			item["RechargeAmount"] = abugo.GetFloat64FromInterface((*data)["RealAmount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "State", ">", 1, nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_withdraw").Select("sum(Amount) as Amount").Where(where).GetOne()
			item["WithdrawAmount"] = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "usdt", nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_caijing_detail").Select("sum(Amount) as Amount").Where(where).GetOne()
			item["CaiJingAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "trx", nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_caijing_detail").Select("sum(Amount) as Amount").Where(where).GetOne()
			item["CaiJingAmountTrx"] = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("sum(GasFee) as GasFee").Where(where).GetOne()
			item["GasFee"] = abugo.GetFloat64FromInterface((*data)["GasFee"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "trx", nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("count(id) as  count,sum(Amount) as Amount,sum(RewardAmount) as RewardAmount").Where(where).GetOne()
			item["TransferCountTrx"] = (*data)["count"]
			item["TransferBetAmountTrx"] = abugo.GetFloat64FromInterface((*data)["Amount"])
			item["TransferWinAmountTrx"] = abugo.GetFloat64FromInterface((*data)["RewardAmount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "Symbol", "=", "usdt", nil)
			where.Add("and", "GameId", "<", 100, nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("count(id) as  count,sum(Amount) as Amount,sum(RewardAmount) as RewardAmount").Where(where).GetOne()
			item["TransferCountUsdt"] = (*data)["count"]
			item["TransferBetAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["Amount"])
			item["TransferWinAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["RewardAmount"])
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", item["UserId"], nil)
			where.Add("and", "GameId", ">", 100, nil)
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
			data, _ := server.Db().Table("x_order").Select("count(id) as  count,sum(Amount) as Amount,sum(RewardAmount) as RewardAmount").Where(where).GetOne()
			item["YueCountUsdt"] = (*data)["count"]
			item["YueBetAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["Amount"])
			item["YueWinAmountUsdt"] = abugo.GetFloat64FromInterface((*data)["RewardAmount"])
		}
		ApiBetCount := 0
		ApiBetAmount := 0.0
		ApiWinAmount := 0.0
		tables := []string{"x_third_dianzhi", "x_third_live", "x_third_lottery", "x_third_qipai", "x_third_sport", "x_third_quwei", "x_third_texas"}
		for i := 0; i < len(tables); i++ {
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "=", item["UserId"], nil)
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
				where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
				data, _ := server.Db().Table(tables[i]).Select("count(id) as count,sum(BetAmount) as BetAmount,sum(WinAmount) as WinAmount").Where(where).GetOne()
				ApiBetCount += int(abugo.GetInt64FromInterface((*data)["count"]))
				ApiBetAmount += abugo.GetFloat64FromInterface((*data)["BetAmount"])
				ApiWinAmount += abugo.GetFloat64FromInterface((*data)["WinAmount"])
			}
		}
		item["ApiBetCount"] = ApiBetCount
		item["ApiBetAmount"] = ApiBetAmount
		item["ApiWinAmount"] = ApiWinAmount
	}
	ctx.Put("total", total)
	ctx.Put("data", data)
	ctx.RespOK()
}

// 批量上传或批量同步独立代理Logo/页签
func (c *AgentController) agent_independence_update_logo_icon(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Ids    []int `validate:"required"`
		Icon   string
		Logo   string
		Remark string
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "改"), &errcode, "权限不足") {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	for i := 0; i < len(reqdata.Ids); i++ {
		server.Db().Conn().Exec("update x_agent_independence set Icon = ?, Logo = ?  where UserId = ? ", reqdata.Icon, reqdata.Logo, reqdata.Ids[i])
	}
	ctx.RespOK()
	server.WriteAdminLog("批量修改独立代理Logo/Icon", ctx, reqdata)
}

func (c *AgentController) agent_fangan_record(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId int
		UserId   int `validate:"required"`
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	data, _ := server.Db().Query("select * from x_record where RType = ? order by id desc", []interface{}{"佣金方案_" + fmt.Sprint(reqdata.UserId)})
	ctx.RespOK(data)
}
