// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXHostTag(db *gorm.DB, opts ...gen.DOOption) xHostTag {
	_xHostTag := xHostTag{}

	_xHostTag.xHostTagDo.UseDB(db, opts...)
	_xHostTag.xHostTagDo.UseModel(&model.XHostTag{})

	tableName := _xHostTag.xHostTagDo.TableName()
	_xHostTag.ALL = field.NewAsterisk(tableName)
	_xHostTag.ID = field.NewInt32(tableName, "Id")
	_xHostTag.SellerID = field.NewInt32(tableName, "SellerId")
	_xHostTag.TagName = field.NewString(tableName, "TagName")

	_xHostTag.fillFieldMap()

	return _xHostTag
}

// xHostTag 域名标签
type xHostTag struct {
	xHostTagDo xHostTagDo

	ALL      field.Asterisk
	ID       field.Int32  // id
	SellerID field.Int32  // 运营商
	TagName  field.String // 标签名称

	fieldMap map[string]field.Expr
}

func (x xHostTag) Table(newTableName string) *xHostTag {
	x.xHostTagDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xHostTag) As(alias string) *xHostTag {
	x.xHostTagDo.DO = *(x.xHostTagDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xHostTag) updateTableName(table string) *xHostTag {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.TagName = field.NewString(table, "TagName")

	x.fillFieldMap()

	return x
}

func (x *xHostTag) WithContext(ctx context.Context) *xHostTagDo { return x.xHostTagDo.WithContext(ctx) }

func (x xHostTag) TableName() string { return x.xHostTagDo.TableName() }

func (x xHostTag) Alias() string { return x.xHostTagDo.Alias() }

func (x xHostTag) Columns(cols ...field.Expr) gen.Columns { return x.xHostTagDo.Columns(cols...) }

func (x *xHostTag) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xHostTag) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 3)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["TagName"] = x.TagName
}

func (x xHostTag) clone(db *gorm.DB) xHostTag {
	x.xHostTagDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xHostTag) replaceDB(db *gorm.DB) xHostTag {
	x.xHostTagDo.ReplaceDB(db)
	return x
}

type xHostTagDo struct{ gen.DO }

func (x xHostTagDo) Debug() *xHostTagDo {
	return x.withDO(x.DO.Debug())
}

func (x xHostTagDo) WithContext(ctx context.Context) *xHostTagDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xHostTagDo) ReadDB() *xHostTagDo {
	return x.Clauses(dbresolver.Read)
}

func (x xHostTagDo) WriteDB() *xHostTagDo {
	return x.Clauses(dbresolver.Write)
}

func (x xHostTagDo) Session(config *gorm.Session) *xHostTagDo {
	return x.withDO(x.DO.Session(config))
}

func (x xHostTagDo) Clauses(conds ...clause.Expression) *xHostTagDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xHostTagDo) Returning(value interface{}, columns ...string) *xHostTagDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xHostTagDo) Not(conds ...gen.Condition) *xHostTagDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xHostTagDo) Or(conds ...gen.Condition) *xHostTagDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xHostTagDo) Select(conds ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xHostTagDo) Where(conds ...gen.Condition) *xHostTagDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xHostTagDo) Order(conds ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xHostTagDo) Distinct(cols ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xHostTagDo) Omit(cols ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xHostTagDo) Join(table schema.Tabler, on ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xHostTagDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xHostTagDo) RightJoin(table schema.Tabler, on ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xHostTagDo) Group(cols ...field.Expr) *xHostTagDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xHostTagDo) Having(conds ...gen.Condition) *xHostTagDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xHostTagDo) Limit(limit int) *xHostTagDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xHostTagDo) Offset(offset int) *xHostTagDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xHostTagDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xHostTagDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xHostTagDo) Unscoped() *xHostTagDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xHostTagDo) Create(values ...*model.XHostTag) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xHostTagDo) CreateInBatches(values []*model.XHostTag, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xHostTagDo) Save(values ...*model.XHostTag) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xHostTagDo) First() (*model.XHostTag, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHostTag), nil
	}
}

func (x xHostTagDo) Take() (*model.XHostTag, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHostTag), nil
	}
}

func (x xHostTagDo) Last() (*model.XHostTag, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHostTag), nil
	}
}

func (x xHostTagDo) Find() ([]*model.XHostTag, error) {
	result, err := x.DO.Find()
	return result.([]*model.XHostTag), err
}

func (x xHostTagDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XHostTag, err error) {
	buf := make([]*model.XHostTag, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xHostTagDo) FindInBatches(result *[]*model.XHostTag, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xHostTagDo) Attrs(attrs ...field.AssignExpr) *xHostTagDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xHostTagDo) Assign(attrs ...field.AssignExpr) *xHostTagDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xHostTagDo) Joins(fields ...field.RelationField) *xHostTagDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xHostTagDo) Preload(fields ...field.RelationField) *xHostTagDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xHostTagDo) FirstOrInit() (*model.XHostTag, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHostTag), nil
	}
}

func (x xHostTagDo) FirstOrCreate() (*model.XHostTag, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHostTag), nil
	}
}

func (x xHostTagDo) FindByPage(offset int, limit int) (result []*model.XHostTag, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xHostTagDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xHostTagDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xHostTagDo) Delete(models ...*model.XHostTag) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xHostTagDo) withDO(do gen.Dao) *xHostTagDo {
	x.DO = *do.(*gen.DO)
	return x
}
