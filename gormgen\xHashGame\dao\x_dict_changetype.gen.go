// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXDictChangetype(db *gorm.DB, opts ...gen.DOOption) xDictChangetype {
	_xDictChangetype := xDictChangetype{}

	_xDictChangetype.xDictChangetypeDo.UseDB(db, opts...)
	_xDictChangetype.xDictChangetypeDo.UseModel(&model.XDictChangetype{})

	tableName := _xDictChangetype.xDictChangetypeDo.TableName()
	_xDictChangetype.ALL = field.NewAsterisk(tableName)
	_xDictChangetype.ChangeType = field.NewInt64(tableName, "ChangeType")
	_xDictChangetype.ChangeName = field.NewString(tableName, "ChangeName")
	_xDictChangetype.ParentType = field.NewInt32(tableName, "ParentType")
	_xDictChangetype.Sort = field.NewInt32(tableName, "Sort")
	_xDictChangetype.Memo = field.NewString(tableName, "Memo")
	_xDictChangetype.Status = field.NewInt32(tableName, "Status")
	_xDictChangetype.Operator = field.NewString(tableName, "Operator")
	_xDictChangetype.OperUserID = field.NewInt32(tableName, "OperUserID")
	_xDictChangetype.DeviceType = field.NewInt32(tableName, "DeviceType")
	_xDictChangetype.DeviceID = field.NewString(tableName, "DeviceID")
	_xDictChangetype.CreateTime = field.NewTime(tableName, "CreateTime")
	_xDictChangetype.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xDictChangetype.fillFieldMap()

	return _xDictChangetype
}

// xDictChangetype 资金变化类型
type xDictChangetype struct {
	xDictChangetypeDo xDictChangetypeDo

	ALL        field.Asterisk
	ChangeType field.Int64  // 变化类型
	ChangeName field.String // 变化名
	ParentType field.Int32  // 上级分类 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金
	Sort       field.Int32  // 排序
	Memo       field.String // 描述
	Status     field.Int32  // 0无效 1有效
	Operator   field.String // 操作员
	OperUserID field.Int32  // 操作员ID
	DeviceType field.Int32  // 设备类型
	DeviceID   field.String // 设备ID
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xDictChangetype) Table(newTableName string) *xDictChangetype {
	x.xDictChangetypeDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xDictChangetype) As(alias string) *xDictChangetype {
	x.xDictChangetypeDo.DO = *(x.xDictChangetypeDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xDictChangetype) updateTableName(table string) *xDictChangetype {
	x.ALL = field.NewAsterisk(table)
	x.ChangeType = field.NewInt64(table, "ChangeType")
	x.ChangeName = field.NewString(table, "ChangeName")
	x.ParentType = field.NewInt32(table, "ParentType")
	x.Sort = field.NewInt32(table, "Sort")
	x.Memo = field.NewString(table, "Memo")
	x.Status = field.NewInt32(table, "Status")
	x.Operator = field.NewString(table, "Operator")
	x.OperUserID = field.NewInt32(table, "OperUserID")
	x.DeviceType = field.NewInt32(table, "DeviceType")
	x.DeviceID = field.NewString(table, "DeviceID")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xDictChangetype) WithContext(ctx context.Context) *xDictChangetypeDo {
	return x.xDictChangetypeDo.WithContext(ctx)
}

func (x xDictChangetype) TableName() string { return x.xDictChangetypeDo.TableName() }

func (x xDictChangetype) Alias() string { return x.xDictChangetypeDo.Alias() }

func (x xDictChangetype) Columns(cols ...field.Expr) gen.Columns {
	return x.xDictChangetypeDo.Columns(cols...)
}

func (x *xDictChangetype) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xDictChangetype) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["ChangeType"] = x.ChangeType
	x.fieldMap["ChangeName"] = x.ChangeName
	x.fieldMap["ParentType"] = x.ParentType
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Status"] = x.Status
	x.fieldMap["Operator"] = x.Operator
	x.fieldMap["OperUserID"] = x.OperUserID
	x.fieldMap["DeviceType"] = x.DeviceType
	x.fieldMap["DeviceID"] = x.DeviceID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xDictChangetype) clone(db *gorm.DB) xDictChangetype {
	x.xDictChangetypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xDictChangetype) replaceDB(db *gorm.DB) xDictChangetype {
	x.xDictChangetypeDo.ReplaceDB(db)
	return x
}

type xDictChangetypeDo struct{ gen.DO }

func (x xDictChangetypeDo) Debug() *xDictChangetypeDo {
	return x.withDO(x.DO.Debug())
}

func (x xDictChangetypeDo) WithContext(ctx context.Context) *xDictChangetypeDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xDictChangetypeDo) ReadDB() *xDictChangetypeDo {
	return x.Clauses(dbresolver.Read)
}

func (x xDictChangetypeDo) WriteDB() *xDictChangetypeDo {
	return x.Clauses(dbresolver.Write)
}

func (x xDictChangetypeDo) Session(config *gorm.Session) *xDictChangetypeDo {
	return x.withDO(x.DO.Session(config))
}

func (x xDictChangetypeDo) Clauses(conds ...clause.Expression) *xDictChangetypeDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xDictChangetypeDo) Returning(value interface{}, columns ...string) *xDictChangetypeDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xDictChangetypeDo) Not(conds ...gen.Condition) *xDictChangetypeDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xDictChangetypeDo) Or(conds ...gen.Condition) *xDictChangetypeDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xDictChangetypeDo) Select(conds ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xDictChangetypeDo) Where(conds ...gen.Condition) *xDictChangetypeDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xDictChangetypeDo) Order(conds ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xDictChangetypeDo) Distinct(cols ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xDictChangetypeDo) Omit(cols ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xDictChangetypeDo) Join(table schema.Tabler, on ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xDictChangetypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xDictChangetypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xDictChangetypeDo) Group(cols ...field.Expr) *xDictChangetypeDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xDictChangetypeDo) Having(conds ...gen.Condition) *xDictChangetypeDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xDictChangetypeDo) Limit(limit int) *xDictChangetypeDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xDictChangetypeDo) Offset(offset int) *xDictChangetypeDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xDictChangetypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xDictChangetypeDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xDictChangetypeDo) Unscoped() *xDictChangetypeDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xDictChangetypeDo) Create(values ...*model.XDictChangetype) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xDictChangetypeDo) CreateInBatches(values []*model.XDictChangetype, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xDictChangetypeDo) Save(values ...*model.XDictChangetype) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xDictChangetypeDo) First() (*model.XDictChangetype, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangetype), nil
	}
}

func (x xDictChangetypeDo) Take() (*model.XDictChangetype, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangetype), nil
	}
}

func (x xDictChangetypeDo) Last() (*model.XDictChangetype, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangetype), nil
	}
}

func (x xDictChangetypeDo) Find() ([]*model.XDictChangetype, error) {
	result, err := x.DO.Find()
	return result.([]*model.XDictChangetype), err
}

func (x xDictChangetypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XDictChangetype, err error) {
	buf := make([]*model.XDictChangetype, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xDictChangetypeDo) FindInBatches(result *[]*model.XDictChangetype, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xDictChangetypeDo) Attrs(attrs ...field.AssignExpr) *xDictChangetypeDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xDictChangetypeDo) Assign(attrs ...field.AssignExpr) *xDictChangetypeDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xDictChangetypeDo) Joins(fields ...field.RelationField) *xDictChangetypeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xDictChangetypeDo) Preload(fields ...field.RelationField) *xDictChangetypeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xDictChangetypeDo) FirstOrInit() (*model.XDictChangetype, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangetype), nil
	}
}

func (x xDictChangetypeDo) FirstOrCreate() (*model.XDictChangetype, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangetype), nil
	}
}

func (x xDictChangetypeDo) FindByPage(offset int, limit int) (result []*model.XDictChangetype, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xDictChangetypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xDictChangetypeDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xDictChangetypeDo) Delete(models ...*model.XDictChangetype) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xDictChangetypeDo) withDO(do gen.Dao) *xDictChangetypeDo {
	x.DO = *do.(*gen.DO)
	return x
}
