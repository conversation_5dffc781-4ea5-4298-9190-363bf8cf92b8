// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentModeLog = "x_agent_mode_log"

// XAgentModeLog mapped from table <x_agent_mode_log>
type XAgentModeLog struct {
	ID                int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	ChannelID         int32     `gorm:"column:ChannelId;not null;comment:渠道ID" json:"ChannelId"`                  // 渠道ID
	OriginAgentMode   int32     `gorm:"column:OriginAgentMode;not null;comment:原代理模式" json:"OriginAgentMode"`     // 原代理模式
	NowAgentMode      int32     `gorm:"column:NowAgentMode;not null;comment:现代理模式" json:"NowAgentMode"`           // 现代理模式
	OrginState        int32     `gorm:"column:OrginState;not null;comment:原状态" json:"OrginState"`                 // 原状态
	NowState          int32     `gorm:"column:NowState;not null;comment:现状态" json:"NowState"`                     // 现状态
	Remark            string    `gorm:"column:Remark;comment:备注" json:"Remark"`                                   // 备注
	OriginAgentCaseID int32     `gorm:"column:OriginAgentCaseId;not null;comment:原代理方案" json:"OriginAgentCaseId"` // 原代理方案
	NowAgentCaseID    int32     `gorm:"column:NowAgentCaseId;not null;comment:现代理方案" json:"NowAgentCaseId"`       // 现代理方案
	Operator          string    `gorm:"column:Operator;comment:操作人" json:"Operator"`                              // 操作人
	CreateTime        time.Time `gorm:"column:CreateTime;not null;comment:创建时间" json:"CreateTime"`                // 创建时间
}

// TableName XAgentModeLog's table name
func (*XAgentModeLog) TableName() string {
	return TableNameXAgentModeLog
}
