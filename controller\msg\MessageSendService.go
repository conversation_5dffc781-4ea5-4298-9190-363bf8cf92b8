package msg

// MessageSendService 消息发送接口
type MessageSendService interface {
	// 根据模板ID发送消息给单个用户
	// @param templateId 模板ID
	// @param userId 用户ID
	// @param variables 消息变量，用于替换模板中的变量
	// @return error 发送失败时返回错误
	SendMessage(templateId int64, userId int, variables map[string]interface{}) error

	// 根据模板ID批量发送消息
	// @param templateId 模板ID
	// @param userFilter 用户筛选条件，用于确定消息接收者
	// @param variables 消息变量，用于替换模板中的变量
	// @return int 成功发送的消息数量
	// @return error 发送失败时返回错误
	SendBatchMessage(templateId int64, userFilter UserFilter, variables map[string]interface{}) (int, error)

	// 根据消息类型发送消息（内部查询模板ID）
	// @param messageType 消息类型，对应模板类型
	// @param userId 用户ID
	// @param variables 消息变量，用于替换模板中的变量
	// @return error 发送失败时返回错误
	SendMessageByType(messageType string, userId int, variables map[string]interface{}) error

	// 异步发送消息
	// @param templateId 模板ID
	// @param userFilter 用户筛选条件，用于确定消息接收者
	// @param variables 消息变量，用于替换模板中的变量
	// @return string 异步任务ID
	// @return error 创建异步任务失败时返回错误
	AsyncSendMessage(templateId int64, userFilter UserFilter, variables map[string]interface{}) (string, error)

	// 发送定时消息
	// @param templateId 模板ID
	// @return int 成功发送的消息数量
	// @return error 发送失败时返回错误
	SendTimedMessage(templateId int64) (int, error)
}

// NewMessageSendService 创建消息发送服务实例
func NewMessageSendService() MessageSendService {
	return NewMessageSendServiceImpl()
}
