package robot

import (
	"fmt"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	//"github.com/beego/beego/logs"
	//"github.com/go-resty/resty/v2"
	//"github.com/spf13/viper"
)

// pushPromoteReport 获取推送数据报表
func (c *Router) pushPromoteReport(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page       int      `json:"page"`
		PageSize   int      `json:"page_size"`
		StartTime  int64    `json:"start_time"`
		EndTime    int64    `json:"end_time"`
		SellerID   []int32  `json:"seller_id"`
		ChannelID  []int32  `json:"channel_id"`
		UserChatID []int64  `json:"user_chat_id"`
		BotName    []string `json:"bot_name"`
		UserID     []int64  `json:"user_id"`
		PushMsgID  []int32  `json:"push_msg_id"`
		IsExport   int      `json:"is_export"`
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "接待机器人推送活动数据统计", "查", "查询接待机器人推送活动数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	type Result struct {
		model.XRobotPushActivityRecord
		SellerName  string `json:"seller_name"`
		ChannelName string `json:"channel_name"`
	}
	var results []*Result

	dao := server.DaoxHashGame().XRobotPushActivityRecord
	query := dao.WithContext(nil).Select(dao.ALL)

	if len(req.SellerID) > 0 {
		query.Where(dao.SellerID.In(req.SellerID...))
	}
	if len(req.ChannelID) > 0 {
		query.Where(dao.ChannelID.In(req.ChannelID...))
	}
	if len(req.UserChatID) > 0 {
		query.Where(dao.UserChatID.In(req.UserChatID...))
	}
	if len(req.UserID) > 0 {
		query.Where(dao.UserID.In(req.UserID...))
	}
	if len(req.BotName) > 0 {
		query.Where(dao.Name.In(req.BotName...))
	}
	if len(req.PushMsgID) > 0 {
		query.Where(dao.PushMsgID.In(req.PushMsgID...))
	}
	tm1 := time.Unix(0, req.StartTime*int64(time.Millisecond))
	tm2 := time.Unix(0, req.EndTime*int64(time.Millisecond))
	if !tm1.IsZero() && !tm2.IsZero() && req.StartTime != 0 && req.EndTime != 0 {
		query.Where(dao.CreateTime.Between(tm1, tm2))
	}
	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	total, err := query.WithContext(nil).
		Select(dao.ALL, xSeller.SellerName, xChannel.ChannelName).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		LeftJoin(xChannel, xChannel.ChannelID.EqCol(dao.ChannelID)).
		Order(dao.ClickCnt.Desc()).
		ScanByPage(&results, offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	if 1 == req.IsExport {
		var fileMap = map[string]string{
			"日期":     "record_time",
			"运营商ID":  "seller_id",
			"运营商名称":  "seller_name",
			"渠道号ID":  "channel_id",
			"渠道号名称":  "channel_name",
			"机器人名称":  "name",
			"TG号ID":  "user_chat_id",
			"玩家用户名":  "user_name",
			"玩家ID":   "user_id",
			"推文ID":   "push_msg_id",
			"推文名":    "push_msg",
			"点击次数":   "click_cnt",
			"是否领取彩金": "is_gift",
			"充值次数":   "recharge_cnt",
			"历史首充金额": "first_time_recharge_amount",
			"历史1充金额": "recharge_amount_1",
			"历史2充金额": "recharge_amount_2",
			"历史3充金额": "recharge_amount_3",
			"历史累计金额": "recharge_amount_all",
		}

		// 设置表头
		var headerLine = []string{"日期", "运营商ID", "运营商名称", "渠道号ID", "渠道号名称", "机器人名称",
			"TG号ID", "玩家用户名", "玩家ID", "推文ID", "推文名", "点击次数", "是否领取彩金", "充值次数", "历史首充金额",
			"历史1充金额", "历史2充金额", "历史3充金额", "历史累计金额",
		}
		var mappedResults []map[string]interface{}
		for _, r := range results {
			mapped := map[string]interface{}{
				"record_time":                r.RecordTime,
				"seller_id":                  r.SellerID,
				"seller_name":                r.SellerName,
				"channel_id":                 r.ChannelID,
				"channel_name":               r.ChannelName,
				"name":                       r.Name,
				"user_chat_id":               r.UserChatID,
				"user_name":                  r.UserName,
				"user_id":                    r.UserID,
				"push_msg_id":                r.PushMsgID,
				"push_msg":                   r.PushMsg,
				"click_cnt":                  r.ClickCnt,
				"is_gift":                    r.IsGift,
				"recharge_cnt":               r.RechargeCnt,
				"first_time_recharge_amount": r.FirstTimeRechargeAmount,
				"recharge_amount_1":          r.RechargeAmount1,
				"recharge_amount_2":          r.RechargeAmount2,
				"recharge_amount_3":          r.RechargeAmount3,
				"recharge_amount_all":        r.RechargeAmountAll,
			}
			mappedResults = append(mappedResults, mapped)
		}

		filename, err := c.exportExcel(&mappedResults, headerLine, fileMap)
		if err != nil {
			ctx.RespErrString(true, &errCode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("total", total)
		ctx.Put("results", results)
		ctx.RespOK()
	}
}

func (c *Router) pushPromoteReportBySum(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page       int      `json:"page"`
		PageSize   int      `json:"page_size"`
		StartTime  int64    `json:"start_time"`
		EndTime    int64    `json:"end_time"`
		SellerID   []int    `json:"seller_id"`
		ChannelID  []int    `json:"channel_id"`
		UserChatID []int64  `json:"user_chat_id"`
		BotName    []string `json:"bot_name"`
		UserID     []int64  `json:"user_id"`
		PushMsgID  []int    `json:"push_msg_id"`
		IsExport   int      `json:"is_export"`
	}
	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "接待机器人推送活动数据统计", "查", "查询接待机器人推送活动数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	where := abugo.AbuDbWhere{}
	groupSQL := "GROUP BY   seller_id , channel_id, push_msg_id "
	where.Add("and", "1", "=", "1", nil)

	if len(req.SellerID) > 0 {
		where.Add("and", "seller_id", "in", SliceToSQLTuple(req.SellerID), nil)
	}
	if len(req.ChannelID) > 0 {
		where.Add("and", "channel_id", "in", SliceToSQLTuple(req.ChannelID), nil)
	}
	if len(req.PushMsgID) > 0 {
		where.Add("and", "push_msg_id", "in", SliceToSQLTuple(req.PushMsgID), nil)
	}
	tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "record_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "record_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		tm2 = abugo.TimeStampToLocalTime(req.EndTime)

	}
	whereSQL, whereData := where.Sql()
	sql := fmt.Sprintf(`
		SELECT  "%s" start_time , "%s" end_time ,  record_time , seller_id ,channel_id ,
		 SellerName seller_name, ChannelName channel_name, push_msg_id , push_msg,
		COUNT(DISTINCT user_id) click_sum ,
		SUM(click_cnt) click_cnt ,
		SUM(is_gift) is_gift , 
		SUM(DISTINCT (CASE WHEN  recharge_cnt >0 THEN 1  ELSE 0 END )) recharge_sum , 
		SUM(recharge_cnt)   recharge_cnt ,
		SUM(recharge_amount_1) recharge_amount_1 ,
		SUM(recharge_amount_2) recharge_amount_2,
		SUM(recharge_amount_3) recharge_amount_3,
		SUM(recharge_amount_all)  recharge_amount_all  
		FROM x_robot_push_activity_record  LEFT JOIN x_seller  ON x_seller.SellerId = x_robot_push_activity_record.seller_id
		LEFT JOIN x_channel ON x_channel.ChannelId=x_robot_push_activity_record.channel_id
		WHERE %s %s 
		ORDER BY  record_time DESC 
	    LIMIT %d OFFSET %d `, tm1, tm2, whereSQL, groupSQL, limit, offset)

	results, err := server.Db().Query(sql, whereData)
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT COUNT(1) AS total FROM (
	SELECT NAME FROM  x_hash_game.x_robot_push_activity_record  where %s  %s  ) tmp 
`, whereSQL, groupSQL), whereData)

	if results == nil || pTotal == nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	total := abugo.GetInt64FromInterface((*pTotal)[0]["total"])

	sql = fmt.Sprintf(`
		SELECT  record_time , seller_id ,channel_id ,
		 SellerName seller_name, ChannelName channel_name, push_msg_id , push_msg,
		COUNT(DISTINCT user_id) click_sum ,
		SUM(click_cnt) click_cnt ,
		SUM(is_gift) is_gift , 
		SUM(DISTINCT (CASE WHEN  recharge_cnt >0 THEN 1  ELSE 0 END )) recharge_sum , 
		SUM(recharge_cnt)   recharge_cnt ,
		SUM(recharge_amount_1) recharge_amount_1 ,
		SUM(recharge_amount_2) recharge_amount_2,
		SUM(recharge_amount_3) recharge_amount_3,
		SUM(recharge_amount_all)  recharge_amount_all  
		FROM x_robot_push_activity_record  LEFT JOIN x_seller  ON x_seller.SellerId = x_robot_push_activity_record.seller_id
		LEFT JOIN x_channel ON x_channel.ChannelId=x_robot_push_activity_record.channel_id
		WHERE %s 
		ORDER BY  record_time DESC 
	    LIMIT %d OFFSET %d `, whereSQL, limit, offset)

	pSum, err := server.Db().Query(sql, whereData)
	if pSum == nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	if 1 == req.IsExport {
		var fileMap = map[string]string{
			"日期":     "record_time",
			"运营商ID":  "seller_id",
			"运营商名称":  "seller_name",
			"渠道号ID":  "channel_id",
			"渠道号名称":  "channel_name",
			"推文ID":   "push_msg_id",
			"推文名":    "push_msg",
			"点击人数":   "click_sum",
			"点击次数":   "click_cnt",
			"是否领取彩金": "is_gift",
			"充值人数":   "recharge_sum",
			"充值次数":   "recharge_cnt",
			"历史首充金额": "first_time_recharge_amount",
			"历史1充金额": "recharge_amount_1",
			"历史2充金额": "recharge_amount_2",
			"历史3充金额": "recharge_amount_3",
			"历史累计金额": "recharge_amount_all",
		}

		// 设置表头
		var headerLine = []string{"日期", "运营商ID", "运营商名称", "渠道号ID", "渠道号名称",
			"推文ID", "推文名", "点击人数", "点击次数", "是否领取彩金", "充值人数", "充值次数", "历史首充金额",
			"历史1充金额", "历史2充金额", "历史3充金额", "历史累计金额",
		}

		filename, err := c.exportExcel(results, headerLine, fileMap)
		if err != nil {
			ctx.RespErrString(true, &errCode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("total", total)
		ctx.Put("results", results)
		ctx.Put("sum", pSum)
		ctx.RespOK()
	}
}

// 活动配置列表
func (c *Router) pushActivityList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page     int   `json:"page"`
		PageSize int   `json:"page_size"`
		ID       int64 `json:"id"`
	}
	reqData := RequestData{}
	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData,
		"机器人管理", "接待机器人推送活动数据统计", "查", "查询接待机器人推送活动配置名称")

	if token == nil {
		return
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}

	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize
	type Result struct {
		model.XRobotPushMsgConfig
	}
	var results []*Result
	dao := server.DaoxHashGame().XRobotPushMsgConfig
	query := dao.WithContext(nil).Select(dao.ALL)
	if reqData.ID != 0 {
		query.Where(dao.ID.Eq(reqData.ID))
	}
	total, err := query.WithContext(nil).
		Select(dao.ALL).
		Order(dao.ID.Desc()).
		ScanByPage(&results, offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.Put("total", total)
	ctx.Put("results", results)
	ctx.RespOK()

}

func (c *Router) pushActivityCreate(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := model.XRobotPushMsgConfig{}
	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人推送活动数据统计", "增", "新增接待机器人推送活动名称配置")
	if token == nil {
		return
	}
	err := server.DaoxHashGame().XRobotPushMsgConfig.WithContext(nil).Create(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

func (c *Router) pushActivityUpdate(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := model.XRobotPushMsgConfig{}
	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人推送活动数据统计", "改", "修改接待机器人推送活动名称配置")
	if token == nil {
		return
	}

	err := server.DaoxHashGame().XRobotPushMsgConfig.WithContext(nil).
		Where(server.DaoxHashGame().XRobotPushMsgConfig.ID.Eq(reqData.ID)).
		Save(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

func (c *Router) pushActivityDelete(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		ID int64 `json:"id"`
	}
	reqData := RequestData{}

	var token *server.TokenData
	token = server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData),
		reqData, "机器人管理", "接待机器人推送活动数据统计", "删", "删除接待机器人推送活动名称配置")

	if token == nil {
		return
	}

	// 软删除
	_, err := server.DaoxHashGame().XRobotPushMsgConfig.WithContext(nil).
		Where(server.DaoxHashGame().XRobotPushMsgConfig.ID.Eq(reqData.ID)).
		Delete()
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

func (c *Router) thirdPushDataReport(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page      int     `json:"page"`
		PageSize  int     `json:"page_size"`
		StartTime int64   `json:"start_time"`
		EndTime   int64   `json:"end_time"`
		Type      []int32 `json:"type"`
		IsExport  int     `json:"is_export"`
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "三方推送飞机号数据", "查", "查询三方推送飞机号数据")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	type Result struct {
		model.XRobotPostUserInfo
	}
	var results []*Result
	dao := server.DaoxHashGame().XRobotPostUserInfo
	query := dao.WithContext(nil).Select(dao.ALL)

	if len(req.Type) > 0 {
		query.Where(dao.Type.In(req.Type...))
	}

	tm1 := time.Unix(0, req.StartTime*int64(time.Millisecond))
	tm2 := time.Unix(0, req.EndTime*int64(time.Millisecond))
	if !tm1.IsZero() && !tm2.IsZero() && req.StartTime != 0 && req.EndTime != 0 {
		query.Where(dao.CreateTime.Between(tm1, tm2))
	}

	total, err := query.WithContext(nil).
		Select(dao.ALL).
		Where(dao.UserName.Neq("")).
		Order(dao.ID.Desc()).
		ScanByPage(&results, offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	if 1 == req.IsExport {
		var fileMap = map[string]string{
			"日期":      "create_time",
			"用户名/手机号": "user_name",
			"数据类型":    "type",
		}

		// 设置表头
		var headerLine = []string{"日期", "用户名/手机号", "数据类型"}
		var mappedResults []map[string]interface{}
		for _, r := range results {
			mapped := map[string]interface{}{
				"create_time": r.CreateTime,
				"user_name":   r.UserName,
				"type":        r.Type,
			}
			mappedResults = append(mappedResults, mapped)
		}

		filename, err := c.exportCSV(&mappedResults, headerLine, fileMap)
		if err != nil {
			ctx.RespErrString(true, &errCode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("total", total)
		ctx.Put("results", results)
		ctx.RespOK()
	}
}
