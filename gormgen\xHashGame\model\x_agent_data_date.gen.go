// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentDataDate = "x_agent_data_date"

// XAgentDataDate 顶级代理数据(按日期)
type XAgentDataDate struct {
	ID                           int32     `gorm:"column:Id;not null" json:"Id"`
	RecordDate                   time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	TopAgentID                   int32     `gorm:"column:TopAgentId;primaryKey" json:"TopAgentId"`
	SellerID                     int32     `gorm:"column:SellerId" json:"SellerId"`
	ChannelID                    int32     `gorm:"column:ChannelId" json:"ChannelId"`
	SpendAmount                  float64   `gorm:"column:SpendAmount;default:0.000000;comment:当日消耗金额" json:"SpendAmount"`                                                // 当日消耗金额
	RegUsers                     int32     `gorm:"column:RegUsers;comment:注册人数" json:"RegUsers"`                                                                         // 注册人数
	NewRechargeUsers             int32     `gorm:"column:NewRechargeUsers;comment:新增充值人数" json:"NewRechargeUsers"`                                                       // 新增充值人数
	NewRechargeAmount            int32     `gorm:"column:NewRechargeAmount;comment:新增充值金额(首充金额)" json:"NewRechargeAmount"`                                               // 新增充值金额(首充金额)
	NewRechargeUsers2            float64   `gorm:"column:NewRechargeUsers2;default:0.000000;comment:首充次日人数" json:"NewRechargeUsers2"`                                    // 首充次日人数
	NewRechargeUsers3            float64   `gorm:"column:NewRechargeUsers3;default:0.000000;comment:首充3日人数" json:"NewRechargeUsers3"`                                    // 首充3日人数
	NewRechargeUsers7            float64   `gorm:"column:NewRechargeUsers7;default:0.000000;comment:首充7日人数" json:"NewRechargeUsers7"`                                    // 首充7日人数
	NewRechargeUsers15           float64   `gorm:"column:NewRechargeUsers15;default:0.000000;comment:首充15日人数(运营总报表)" json:"NewRechargeUsers15"`                          // 首充15日人数(运营总报表)
	NewTransferBetUsers          int32     `gorm:"column:NewTransferBetUsers;comment:新增转账人数" json:"NewTransferBetUsers"`                                                 // 新增转账人数
	NewTransferBetCount          int32     `gorm:"column:NewTransferBetCount;comment:新增转账注单数" json:"NewTransferBetCount"`                                                // 新增转账注单数
	NewTransferBetAmount         float64   `gorm:"column:NewTransferBetAmount;default:0.000000;comment:新增转账注单金额" json:"NewTransferBetAmount"`                            // 新增转账注单金额
	NewTransferWinAmount         float64   `gorm:"column:NewTransferWinAmount;default:0.000000;comment:新增转账派彩金额" json:"NewTransferWinAmount"`                            // 新增转账派彩金额
	NewTransferBetUsers2         int32     `gorm:"column:NewTransferBetUsers2;comment:新增转账次日人数" json:"NewTransferBetUsers2"`                                             // 新增转账次日人数
	NewTransferBetUsers3         int32     `gorm:"column:NewTransferBetUsers3;comment:新增转账3日人数" json:"NewTransferBetUsers3"`                                             // 新增转账3日人数
	NewTransferBetUsers7         int32     `gorm:"column:NewTransferBetUsers7;comment:新增转账7日人数" json:"NewTransferBetUsers7"`                                             // 新增转账7日人数
	NewTransferUsdtBetUsers      int32     `gorm:"column:NewTransferUsdtBetUsers;comment:新增转账Usdt人数(运营总报表)" json:"NewTransferUsdtBetUsers"`                              // 新增转账Usdt人数(运营总报表)
	NewTransferUsdtBetCount      int32     `gorm:"column:NewTransferUsdtBetCount;comment:新增转账Usdt注单数(运营总报表)" json:"NewTransferUsdtBetCount"`                             // 新增转账Usdt注单数(运营总报表)
	NewTransferUsdtBetAmount     float64   `gorm:"column:NewTransferUsdtBetAmount;default:0.000000;comment:新增转账Usdt注单金额(运营总报表)" json:"NewTransferUsdtBetAmount"`         // 新增转账Usdt注单金额(运营总报表)
	NewTransferUsdtWinAmount     float64   `gorm:"column:NewTransferUsdtWinAmount;default:0.000000;comment:新增转账Usdt派彩金额(运营总报表)" json:"NewTransferUsdtWinAmount"`         // 新增转账Usdt派彩金额(运营总报表)
	NewTransferUsdtFeeAmount     float64   `gorm:"column:NewTransferUsdtFeeAmount;default:0.000000;comment:新增转账Usdt手续费(运营总报表)" json:"NewTransferUsdtFeeAmount"`          // 新增转账Usdt手续费(运营总报表)
	NewTransferUsdtLiuShuiAmount float64   `gorm:"column:NewTransferUsdtLiuShuiAmount;default:0.000000;comment:新增转账Usdt流水金额(运营总报表)" json:"NewTransferUsdtLiuShuiAmount"` // 新增转账Usdt流水金额(运营总报表)
	NewTransferUsdtBetUsers2     int32     `gorm:"column:NewTransferUsdtBetUsers2;comment:新增转账Usdt次日人数(运营总报表)" json:"NewTransferUsdtBetUsers2"`                          // 新增转账Usdt次日人数(运营总报表)
	NewTransferUsdtBetUsers3     int32     `gorm:"column:NewTransferUsdtBetUsers3;comment:新增转账Usdt3日人数(运营总报表)" json:"NewTransferUsdtBetUsers3"`                          // 新增转账Usdt3日人数(运营总报表)
	NewTransferUsdtBetUsers7     int32     `gorm:"column:NewTransferUsdtBetUsers7;comment:新增转账Usdt7日人数(运营总报表)" json:"NewTransferUsdtBetUsers7"`                          // 新增转账Usdt7日人数(运营总报表)
	NewTransferUsdtBetUsers15    int32     `gorm:"column:NewTransferUsdtBetUsers15;comment:新增转账Usdt15日人数(运营总报表)" json:"NewTransferUsdtBetUsers15"`                       // 新增转账Usdt15日人数(运营总报表)
	NewTransferTrxBetUsers       int32     `gorm:"column:NewTransferTrxBetUsers;comment:新增转账Trx人数(运营总报表)" json:"NewTransferTrxBetUsers"`                                 // 新增转账Trx人数(运营总报表)
	NewTransferTrxBetCount       int32     `gorm:"column:NewTransferTrxBetCount;comment:新增转账Trx注单数(运营总报表)" json:"NewTransferTrxBetCount"`                                // 新增转账Trx注单数(运营总报表)
	NewTransferTrxBetAmount      float64   `gorm:"column:NewTransferTrxBetAmount;default:0.000000;comment:新增转账Trx注单金额(运营总报表)" json:"NewTransferTrxBetAmount"`            // 新增转账Trx注单金额(运营总报表)
	NewTransferTrxWinAmount      float64   `gorm:"column:NewTransferTrxWinAmount;default:0.000000;comment:新增转账Trx派彩金额(运营总报表)" json:"NewTransferTrxWinAmount"`            // 新增转账Trx派彩金额(运营总报表)
	NewTransferTrxFeeAmount      float64   `gorm:"column:NewTransferTrxFeeAmount;default:0.000000;comment:新增转账Trx手续费(运营总报表)" json:"NewTransferTrxFeeAmount"`             // 新增转账Trx手续费(运营总报表)
	NewTransferTrxLiuShuiAmount  float64   `gorm:"column:NewTransferTrxLiuShuiAmount;default:0.000000;comment:新增转账Trx流水金额(运营总报表)" json:"NewTransferTrxLiuShuiAmount"`    // 新增转账Trx流水金额(运营总报表)
	NewTransferTrxBetUsers2      int32     `gorm:"column:NewTransferTrxBetUsers2;comment:新增转账Trx次日人数(运营总报表)" json:"NewTransferTrxBetUsers2"`                             // 新增转账Trx次日人数(运营总报表)
	NewTransferTrxBetUsers3      int32     `gorm:"column:NewTransferTrxBetUsers3;comment:新增转账Trx3日人数(运营总报表)" json:"NewTransferTrxBetUsers3"`                             // 新增转账Trx3日人数(运营总报表)
	NewTransferTrxBetUsers7      int32     `gorm:"column:NewTransferTrxBetUsers7;comment:新增转账Trx7日人数(运营总报表)" json:"NewTransferTrxBetUsers7"`                             // 新增转账Trx7日人数(运营总报表)
	NewTransferTrxBetUsers15     int32     `gorm:"column:NewTransferTrxBetUsers15;comment:新增转账Trx15日人数(运营总报表)" json:"NewTransferTrxBetUsers15"`                          // 新增转账Trx15日人数(运营总报表)
	NewBetUsers                  int32     `gorm:"column:NewBetUsers;comment:新增余额投注人数" json:"NewBetUsers"`                                                               // 新增余额投注人数
	NewBetCount                  int32     `gorm:"column:NewBetCount;comment:新增余额注单数" json:"NewBetCount"`                                                                // 新增余额注单数
	NewBetAmount                 float64   `gorm:"column:NewBetAmount;default:0.000000;comment:新增余额注单金额" json:"NewBetAmount"`                                            // 新增余额注单金额
	NewWinAmount                 float64   `gorm:"column:NewWinAmount;default:0.000000;comment:新增余额派彩金额" json:"NewWinAmount"`                                            // 新增余额派彩金额
	NewTotalBetUsers             int32     `gorm:"column:NewTotalBetUsers;comment:新增总投注人数(运营总报表)" json:"NewTotalBetUsers"`                                               // 新增总投注人数(运营总报表)
	TotalNewUsers                int32     `gorm:"column:TotalNewUsers;comment:总新增人数 新增充值+新增转账人数，同时人数需要去重" json:"TotalNewUsers"`                                         // 总新增人数 新增充值+新增转账人数，同时人数需要去重
	ValidRecharge10Users         int32     `gorm:"column:ValidRecharge10Users;comment:10元充值人数(市场报表)" json:"ValidRecharge10Users"`                                        // 10元充值人数(市场报表)
	ValidRecharge50Users         int32     `gorm:"column:ValidRecharge50Users;comment:50元充值人数(市场报表)" json:"ValidRecharge50Users"`                                        // 50元充值人数(市场报表)
	ValidRecharge100Users        int32     `gorm:"column:ValidRecharge100Users;comment:100元充值人数(市场报表)" json:"ValidRecharge100Users"`                                     // 100元充值人数(市场报表)
	RechargeUsers                int32     `gorm:"column:RechargeUsers;comment:充值人数" json:"RechargeUsers"`                                                               // 充值人数
	RechargeCount                int32     `gorm:"column:RechargeCount;comment:充值笔数" json:"RechargeCount"`                                                               // 充值笔数
	RechargeAmount               float64   `gorm:"column:RechargeAmount;default:0.000000;comment:充值金额" json:"RechargeAmount"`                                            // 充值金额
	TotalBetUsers                int32     `gorm:"column:TotalBetUsers;comment:总投注人数(市场报表)" json:"TotalBetUsers"`                                                        // 总投注人数(市场报表)
	TotalBetCount                int32     `gorm:"column:TotalBetCount;comment:总注单数(运营总报表)" json:"TotalBetCount"`                                                        // 总注单数(运营总报表)
	TotalBetAmount               float64   `gorm:"column:TotalBetAmount;default:0.000000;comment:总注单金额(运营总报表)" json:"TotalBetAmount"`                                    // 总注单金额(运营总报表)
	TotalWinAmount               float64   `gorm:"column:TotalWinAmount;default:0.000000;comment:总派彩金额(运营总报表)" json:"TotalWinAmount"`                                    // 总派彩金额(运营总报表)
	TotalFeeAmount               float64   `gorm:"column:TotalFeeAmount;default:0.000000;comment:总手续费(运营总报表)" json:"TotalFeeAmount"`                                     // 总手续费(运营总报表)
	TransferBetUsers             int32     `gorm:"column:TransferBetUsers;comment:转账人数" json:"TransferBetUsers"`                                                         // 转账人数
	TransferBetCount             int32     `gorm:"column:TransferBetCount;comment:转账注单数" json:"TransferBetCount"`                                                        // 转账注单数
	TransferBetAmount            float64   `gorm:"column:TransferBetAmount;default:0.000000;comment:转账注单金额" json:"TransferBetAmount"`                                    // 转账注单金额
	TransferWinAmount            float64   `gorm:"column:TransferWinAmount;default:0.000000;comment:转账派彩金额" json:"TransferWinAmount"`                                    // 转账派彩金额
	TransferUsdtBetUsers         int32     `gorm:"column:TransferUsdtBetUsers;comment:转账Usdt人数(市场报表)" json:"TransferUsdtBetUsers"`                                       // 转账Usdt人数(市场报表)
	TransferUsdtBetCount         int32     `gorm:"column:TransferUsdtBetCount;comment:转账Usdt注单数(市场报表)" json:"TransferUsdtBetCount"`                                      // 转账Usdt注单数(市场报表)
	TransferUsdtWinCount         int32     `gorm:"column:TransferUsdtWinCount;comment:转账Usdt中奖次数(运营总报表)" json:"TransferUsdtWinCount"`                                    // 转账Usdt中奖次数(运营总报表)
	TransferUsdtBetAmount        float64   `gorm:"column:TransferUsdtBetAmount;default:0.000000;comment:转账Usdt注单金额(市场报表)" json:"TransferUsdtBetAmount"`                  // 转账Usdt注单金额(市场报表)
	TransferUsdtWinAmount        float64   `gorm:"column:TransferUsdtWinAmount;default:0.000000;comment:转账Usdt派彩金额(市场报表)" json:"TransferUsdtWinAmount"`                  // 转账Usdt派彩金额(市场报表)
	TransferUsdtFeeAmount        float64   `gorm:"column:TransferUsdtFeeAmount;default:0.000000;comment:转账Usdt手续费(运营总报表)" json:"TransferUsdtFeeAmount"`                  // 转账Usdt手续费(运营总报表)
	TransferUsdtLiuShuiAmount    float64   `gorm:"column:TransferUsdtLiuShuiAmount;default:0.000000;comment:转账Usdt流水金额(市场报表)" json:"TransferUsdtLiuShuiAmount"`          // 转账Usdt流水金额(市场报表)
	TransferTrxBetUsers          int32     `gorm:"column:TransferTrxBetUsers;comment:转账Trx人数(市场报表)" json:"TransferTrxBetUsers"`                                          // 转账Trx人数(市场报表)
	TransferTrxBetCount          int32     `gorm:"column:TransferTrxBetCount;comment:转账Trx注单数(市场报表)" json:"TransferTrxBetCount"`                                         // 转账Trx注单数(市场报表)
	TransferTrxWinCount          int32     `gorm:"column:TransferTrxWinCount;comment:转账Trx中奖次数(运营总报表)" json:"TransferTrxWinCount"`                                       // 转账Trx中奖次数(运营总报表)
	TransferTrxBetAmount         float64   `gorm:"column:TransferTrxBetAmount;default:0.000000;comment:转账Trx注单金额(市场报表)" json:"TransferTrxBetAmount"`                     // 转账Trx注单金额(市场报表)
	TransferTrxWinAmount         float64   `gorm:"column:TransferTrxWinAmount;default:0.000000;comment:转账Trx派彩金额(市场报表)" json:"TransferTrxWinAmount"`                     // 转账Trx派彩金额(市场报表)
	TransferTrxFeeAmount         float64   `gorm:"column:TransferTrxFeeAmount;default:0.000000;comment:转账Trx手续费(运营总报表)" json:"TransferTrxFeeAmount"`                     // 转账Trx手续费(运营总报表)
	TransferTrxLiuShuiAmount     float64   `gorm:"column:TransferTrxLiuShuiAmount;default:0.000000;comment:转账Trx流水金额(市场报表)" json:"TransferTrxLiuShuiAmount"`             // 转账Trx流水金额(市场报表)
	BetUsers                     int32     `gorm:"column:BetUsers;comment:余额人数" json:"BetUsers"`                                                                         // 余额人数
	BetCount                     int32     `gorm:"column:BetCount;comment:余额注单数" json:"BetCount"`                                                                        // 余额注单数
	WinCount                     int32     `gorm:"column:WinCount;comment:余额中奖次数(运营总报表)" json:"WinCount"`                                                                // 余额中奖次数(运营总报表)
	BetAmount                    float64   `gorm:"column:BetAmount;default:0.000000;comment:余额注单金额" json:"BetAmount"`                                                    // 余额注单金额
	WinAmount                    float64   `gorm:"column:WinAmount;default:0.000000;comment:余额派彩金额" json:"WinAmount"`                                                    // 余额派彩金额
	FeeAmount                    float64   `gorm:"column:FeeAmount;default:0.000000;comment:余额手续费(运营总报表)" json:"FeeAmount"`                                              // 余额手续费(运营总报表)
	LiuShuiAmount                float64   `gorm:"column:LiuShuiAmount;default:0.000000;comment:余额流水金额(运营总报表)" json:"LiuShuiAmount"`                                     // 余额流水金额(运营总报表)
	TotalPayUsers                int32     `gorm:"column:TotalPayUsers;comment:总付费人数 充值人数+转账人数，同时去重" json:"TotalPayUsers"`                                               // 总付费人数 充值人数+转账人数，同时去重
	TotalPayUsers2               int32     `gorm:"column:TotalPayUsers2;comment:总付费次日人数 充值人数+转账人数，同时去重" json:"TotalPayUsers2"`                                           // 总付费次日人数 充值人数+转账人数，同时去重
	TotalPayUsers3               int32     `gorm:"column:TotalPayUsers3;comment:总付费3日人数 充值人数+转账人数，同时去重" json:"TotalPayUsers3"`                                           // 总付费3日人数 充值人数+转账人数，同时去重
	TotalPayUsers7               int32     `gorm:"column:TotalPayUsers7;comment:总付费7日人数 充值人数+转账人数，同时去重" json:"TotalPayUsers7"`                                           // 总付费7日人数 充值人数+转账人数，同时去重
	RewardUsers                  int32     `gorm:"column:RewardUsers;comment:活动彩金人数(运营总报表)" json:"RewardUsers"`                                                          // 活动彩金人数(运营总报表)
	RewardCount                  int32     `gorm:"column:RewardCount;comment:活动彩金笔数(运营总报表)" json:"RewardCount"`                                                          // 活动彩金笔数(运营总报表)
	RewardAmount                 float64   `gorm:"column:RewardAmount;default:0.000000;comment:活动彩金金额(运营总报表)" json:"RewardAmount"`                                       // 活动彩金金额(运营总报表)
	ManReduceRewardUsers         int32     `gorm:"column:ManReduceRewardUsers;comment:人工减少活动彩金人数(运营总报表)" json:"ManReduceRewardUsers"`                                    // 人工减少活动彩金人数(运营总报表)
	ManReduceRewardCount         int32     `gorm:"column:ManReduceRewardCount;comment:人工减少活动彩金笔数(运营总报表)" json:"ManReduceRewardCount"`                                    // 人工减少活动彩金笔数(运营总报表)
	ManReduceRewardAmount        float64   `gorm:"column:ManReduceRewardAmount;default:0.000000;comment:人工减少活动彩金金额(运营总报表)" json:"ManReduceRewardAmount"`                 // 人工减少活动彩金金额(运营总报表)
	VipRewardUsers               int32     `gorm:"column:VipRewardUsers;comment:Vip活动彩金人数(运营总报表)" json:"VipRewardUsers"`                                                 // Vip活动彩金人数(运营总报表)
	VipRewardCount               int32     `gorm:"column:VipRewardCount;comment:Vip活动彩金笔数(运营总报表)" json:"VipRewardCount"`                                                 // Vip活动彩金笔数(运营总报表)
	VipRewardAmount              float64   `gorm:"column:VipRewardAmount;default:0.000000;comment:Vip活动彩金金额(运营总报表)" json:"VipRewardAmount"`                              // Vip活动彩金金额(运营总报表)
	GetCommissionUsers           int32     `gorm:"column:GetCommissionUsers;comment:领取佣金人数(运营总报表)" json:"GetCommissionUsers"`                                            // 领取佣金人数(运营总报表)
	GetCommissionCount           int32     `gorm:"column:GetCommissionCount;comment:领取佣金笔数(运营总报表)" json:"GetCommissionCount"`                                            // 领取佣金笔数(运营总报表)
	GetCommissionAmount          float64   `gorm:"column:GetCommissionAmount;default:0.000000;comment:领取佣金金额(运营总报表)" json:"GetCommissionAmount"`                         // 领取佣金金额(运营总报表)
	WithdrawUsers                int32     `gorm:"column:WithdrawUsers;comment:提款人数" json:"WithdrawUsers"`                                                               // 提款人数
	WithdrawCount                int32     `gorm:"column:WithdrawCount;comment:提款笔数" json:"WithdrawCount"`                                                               // 提款笔数
	WithdrawAmount               float64   `gorm:"column:WithdrawAmount;default:0.000000;comment:提款金额" json:"WithdrawAmount"`                                            // 提款金额
	SumSpendAmount               float64   `gorm:"column:SumSpendAmount;default:0.000000;comment:累计消耗金额" json:"SumSpendAmount"`                                          // 累计消耗金额
	SumNewRechargeUsers          int32     `gorm:"column:SumNewRechargeUsers;comment:累计首充人数" json:"SumNewRechargeUsers"`                                                 // 累计首充人数
	SumTotalPayUsers             int32     `gorm:"column:SumTotalPayUsers;comment:累计付费人数" json:"SumTotalPayUsers"`                                                       // 累计付费人数
	SumRechargeAmount            float64   `gorm:"column:SumRechargeAmount;default:0.000000;comment:累计充值金额" json:"SumRechargeAmount"`                                    // 累计充值金额
	SumWithdrawAmount            float64   `gorm:"column:SumWithdrawAmount;default:0.000000;comment:累计提款金额" json:"SumWithdrawAmount"`                                    // 累计提款金额
	SumTransferBetAmount         float64   `gorm:"column:SumTransferBetAmount;default:0.000000;comment:累计转账注单金额" json:"SumTransferBetAmount"`                            // 累计转账注单金额
	SumTransferWinAmount         float64   `gorm:"column:SumTransferWinAmount;default:0.000000;comment:累计转账派彩金额" json:"SumTransferWinAmount"`                            // 累计转账派彩金额
	SumBetAmount                 float64   `gorm:"column:SumBetAmount;default:0.000000;comment:累计余额注单金额" json:"SumBetAmount"`                                            // 累计余额注单金额
	SumWinAmount                 float64   `gorm:"column:SumWinAmount;default:0.000000;comment:累计余额派彩金额" json:"SumWinAmount"`                                            // 累计余额派彩金额
	CreateTime                   time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`                                  // 创建时间
	UpdateTime                   time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`                                  // 更新时间
}

// TableName XAgentDataDate's table name
func (*XAgentDataDate) TableName() string {
	return TableNameXAgentDataDate
}
