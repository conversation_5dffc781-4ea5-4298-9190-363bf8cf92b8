package controller

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"io"
	"xserver/db"
	"xserver/gormgen/xHashGame/dao"
	"xserver/server"
	"xserver/utils"
)

type RedPacketAddCashController struct{}

type RedPacketAddCashReq struct {
	Amount   float64 `json:"amount"`
	UserId   int64   `json:"user_id"`
	Multiple float64 `json:"multiple"`
	Sign     string  `json:"sign"`
	ReasonId int32   `json:"reason_id" validate:"required"`
	Memo     string  `json:"memo" validate:"required"`
}

func (c *RedPacketAddCashController) Init() {
	server.Http().Gin().POST("/api/tgRobot/addCash", utils.CheckSign(), c.addCash)
}

func (c *RedPacketAddCashController) addCash(ctx *gin.Context) {
	// 获取json数据
	var data RedPacketAddCashReq
	body, err := io.ReadAll(ctx.Request.Body)

	if err != nil {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "参数错误",
		})
		return
	}

	json.Unmarshal(body, &data)

	cacheKey := fmt.Sprintf("redpacket:%v:unique", data.Sign)
	// 幂等性校验
	lck := server.Redis().SetNxString(cacheKey, data.Sign, 60)
	if lck != nil {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "重复请求",
		})
		return
	}

	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(int32(data.UserId))).First()

		activeAddUseBalancerInfo := struct {
			UserId            int32
			ActiveName        string
			RealAmount        float64
			WithdrawLiuSuiAdd float64
			BalanceCReason    int
		}{
			UserId:            int32(data.UserId),
			ActiveName:        data.Memo,
			RealAmount:        data.Amount,
			WithdrawLiuSuiAdd: data.Multiple * data.Amount,
			BalanceCReason:    int(data.ReasonId),
		}
		// 更新用户流水和账变记录
		err = db.ActiveAddUseBalancer(tx, activeAddUseBalancerInfo)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  err.Error(),
		})
		return
	}
	ctx.JSON(200, gin.H{
		"code": 1,
		"msg":  "success",
	})
}
