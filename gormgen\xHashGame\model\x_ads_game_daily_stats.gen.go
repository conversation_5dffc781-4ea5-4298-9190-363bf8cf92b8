// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsGameDailyStat = "x_ads_game_daily_stats"

// XAdsGameDailyStat 游戏偏好每日统计表
type XAdsGameDailyStat struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                        // 主键ID
	SellerID     int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                       // 运营商ID
	ChannelID    int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                      // 渠道ID
	TopAgentID   int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                // 顶级代理ID
	StatDate     time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                               // 统计日期
	GameTag      string    `gorm:"column:game_tag;not null;comment:游戏标签" json:"game_tag"`                                 // 游戏标签
	GameName     string    `gorm:"column:game_name;not null;comment:游戏名称" json:"game_name"`                               // 游戏名称
	GameCompany  string    `gorm:"column:game_company;not null;comment:游戏厂商" json:"game_company"`                         // 游戏厂商
	BetCountUPc  int32     `gorm:"column:bet_count_u_pc;not null;comment:下注次数U pc" json:"bet_count_u_pc"`                 // 下注次数U pc
	BetCountUH5  int32     `gorm:"column:bet_count_u_h5;not null;comment:下注次数U h5" json:"bet_count_u_h5"`                 // 下注次数U h5
	BetCountTPc  int32     `gorm:"column:bet_count_t_pc;not null;comment:下注次数T pc" json:"bet_count_t_pc"`                 // 下注次数T pc
	BetCountTH5  int32     `gorm:"column:bet_count_t_h5;not null;comment:下注次数T h5" json:"bet_count_t_h5"`                 // 下注次数T h5
	BetAmountUPc float32   `gorm:"column:bet_amount_u_pc;not null;comment:下注金额U pc" json:"bet_amount_u_pc"`               // 下注金额U pc
	BetAmountUH5 float32   `gorm:"column:bet_amount_u_h5;not null;comment:下注金额U h5" json:"bet_amount_u_h5"`               // 下注金额U h5
	BetAmountTPc float32   `gorm:"column:bet_amount_t_pc;not null;comment:下注金额T pc" json:"bet_amount_t_pc"`               // 下注金额T pc
	BetAmountTH5 float32   `gorm:"column:bet_amount_t_h5;not null;comment:下注金额T h5" json:"bet_amount_t_h5"`               // 下注金额T h5
	CreateTime   time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XAdsGameDailyStat's table name
func (*XAdsGameDailyStat) TableName() string {
	return TableNameXAdsGameDailyStat
}
