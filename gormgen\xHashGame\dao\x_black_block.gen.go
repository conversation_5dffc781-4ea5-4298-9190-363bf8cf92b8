// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXBlackBlock(db *gorm.DB, opts ...gen.DOOption) xBlackBlock {
	_xBlackBlock := xBlackBlock{}

	_xBlackBlock.xBlackBlockDo.UseDB(db, opts...)
	_xBlackBlock.xBlackBlockDo.UseModel(&model.XBlackBlock{})

	tableName := _xBlackBlock.xBlackBlockDo.TableName()
	_xBlackBlock.ALL = field.NewAsterisk(tableName)
	_xBlackBlock.ID = field.NewInt32(tableName, "Id")
	_xBlackBlock.BlockMaker = field.NewString(tableName, "BlockMaker")
	_xBlackBlock.Info = field.NewString(tableName, "Info")
	_xBlackBlock.Type = field.NewInt32(tableName, "Type")
	_xBlackBlock.CreateTime = field.NewTime(tableName, "CreateTime")
	_xBlackBlock.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xBlackBlock.fillFieldMap()

	return _xBlackBlock
}

// xBlackBlock 区块黑名单
type xBlackBlock struct {
	xBlackBlockDo xBlackBlockDo

	ALL        field.Asterisk
	ID         field.Int32
	BlockMaker field.String // 出块者
	Info       field.String // 拉黑信息
	Type       field.Int32  // 1-玩家 2-钱包地址 3-代理 4-运营商 5-渠道 6-区块
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xBlackBlock) Table(newTableName string) *xBlackBlock {
	x.xBlackBlockDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xBlackBlock) As(alias string) *xBlackBlock {
	x.xBlackBlockDo.DO = *(x.xBlackBlockDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xBlackBlock) updateTableName(table string) *xBlackBlock {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.BlockMaker = field.NewString(table, "BlockMaker")
	x.Info = field.NewString(table, "Info")
	x.Type = field.NewInt32(table, "Type")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xBlackBlock) WithContext(ctx context.Context) *xBlackBlockDo {
	return x.xBlackBlockDo.WithContext(ctx)
}

func (x xBlackBlock) TableName() string { return x.xBlackBlockDo.TableName() }

func (x xBlackBlock) Alias() string { return x.xBlackBlockDo.Alias() }

func (x xBlackBlock) Columns(cols ...field.Expr) gen.Columns { return x.xBlackBlockDo.Columns(cols...) }

func (x *xBlackBlock) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xBlackBlock) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["BlockMaker"] = x.BlockMaker
	x.fieldMap["Info"] = x.Info
	x.fieldMap["Type"] = x.Type
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xBlackBlock) clone(db *gorm.DB) xBlackBlock {
	x.xBlackBlockDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xBlackBlock) replaceDB(db *gorm.DB) xBlackBlock {
	x.xBlackBlockDo.ReplaceDB(db)
	return x
}

type xBlackBlockDo struct{ gen.DO }

func (x xBlackBlockDo) Debug() *xBlackBlockDo {
	return x.withDO(x.DO.Debug())
}

func (x xBlackBlockDo) WithContext(ctx context.Context) *xBlackBlockDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xBlackBlockDo) ReadDB() *xBlackBlockDo {
	return x.Clauses(dbresolver.Read)
}

func (x xBlackBlockDo) WriteDB() *xBlackBlockDo {
	return x.Clauses(dbresolver.Write)
}

func (x xBlackBlockDo) Session(config *gorm.Session) *xBlackBlockDo {
	return x.withDO(x.DO.Session(config))
}

func (x xBlackBlockDo) Clauses(conds ...clause.Expression) *xBlackBlockDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xBlackBlockDo) Returning(value interface{}, columns ...string) *xBlackBlockDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xBlackBlockDo) Not(conds ...gen.Condition) *xBlackBlockDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xBlackBlockDo) Or(conds ...gen.Condition) *xBlackBlockDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xBlackBlockDo) Select(conds ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xBlackBlockDo) Where(conds ...gen.Condition) *xBlackBlockDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xBlackBlockDo) Order(conds ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xBlackBlockDo) Distinct(cols ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xBlackBlockDo) Omit(cols ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xBlackBlockDo) Join(table schema.Tabler, on ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xBlackBlockDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xBlackBlockDo) RightJoin(table schema.Tabler, on ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xBlackBlockDo) Group(cols ...field.Expr) *xBlackBlockDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xBlackBlockDo) Having(conds ...gen.Condition) *xBlackBlockDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xBlackBlockDo) Limit(limit int) *xBlackBlockDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xBlackBlockDo) Offset(offset int) *xBlackBlockDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xBlackBlockDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xBlackBlockDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xBlackBlockDo) Unscoped() *xBlackBlockDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xBlackBlockDo) Create(values ...*model.XBlackBlock) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xBlackBlockDo) CreateInBatches(values []*model.XBlackBlock, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xBlackBlockDo) Save(values ...*model.XBlackBlock) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xBlackBlockDo) First() (*model.XBlackBlock, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBlackBlock), nil
	}
}

func (x xBlackBlockDo) Take() (*model.XBlackBlock, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBlackBlock), nil
	}
}

func (x xBlackBlockDo) Last() (*model.XBlackBlock, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBlackBlock), nil
	}
}

func (x xBlackBlockDo) Find() ([]*model.XBlackBlock, error) {
	result, err := x.DO.Find()
	return result.([]*model.XBlackBlock), err
}

func (x xBlackBlockDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XBlackBlock, err error) {
	buf := make([]*model.XBlackBlock, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xBlackBlockDo) FindInBatches(result *[]*model.XBlackBlock, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xBlackBlockDo) Attrs(attrs ...field.AssignExpr) *xBlackBlockDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xBlackBlockDo) Assign(attrs ...field.AssignExpr) *xBlackBlockDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xBlackBlockDo) Joins(fields ...field.RelationField) *xBlackBlockDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xBlackBlockDo) Preload(fields ...field.RelationField) *xBlackBlockDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xBlackBlockDo) FirstOrInit() (*model.XBlackBlock, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBlackBlock), nil
	}
}

func (x xBlackBlockDo) FirstOrCreate() (*model.XBlackBlock, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBlackBlock), nil
	}
}

func (x xBlackBlockDo) FindByPage(offset int, limit int) (result []*model.XBlackBlock, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xBlackBlockDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xBlackBlockDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xBlackBlockDo) Delete(models ...*model.XBlackBlock) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xBlackBlockDo) withDO(do gen.Dao) *xBlackBlockDo {
	x.DO = *do.(*gen.DO)
	return x
}
