package controller

import (
	"errors"
	"gorm.io/gorm"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type TgRedPacketController struct{}

func (c *TgRedPacketController) Init() {
	group := server.Http().NewGroup("/api/tgRedPacket")
	{
		group.Post("/list", c.list)
		group.Post("/add_count", c.add_count)
		//group.Post("/create", c.create)
		//group.Post("/update", c.update)
	}
}

func (c *TgRedPacketController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID   int64
		Page       int
		PageSize   int
		TgId       int64
		TgUserId   int64
		TgUserName string
		StartTime  string
		EndTime    string
	}
	type Result struct {
		model.XTgRedpacketUser
		RobotUserName      string
		SellerName         string
		TotalInviteCount   int64
		TotalGrabCount     int64
		TotalWithdrawCount int64
		Operator           string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人统计", "查", "红包机器人统计查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgRedpacketUser
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	var list []*Result
	//xChannel := server.DaoxHashGame().XChannel
	xSeller := server.DaoxHashGame().XSeller
	xAdmin := server.DaoxHashGame().AdminUser
	xRobot := server.DaoxHashGame().XTgRobotRedpacket
	xTgRedPacketInviteDao := server.DaoxHashGame().XTgRedpacketInviteCount
	xTgRedpacketInviteDB := xTgRedPacketInviteDao.WithContext(ctx.Gin())

	xTgGrabRedPacketDao := server.DaoxHashGame().XTgRedpacketLog
	xTgGrabRedPacketDB := xTgGrabRedPacketDao.WithContext(ctx.Gin())

	xTgWithdrawRedPacketDao := server.DaoxHashGame().XTgRedpacketWithdrawLog
	xTgWitdrawRedPacketDB := xTgWithdrawRedPacketDao.WithContext(ctx.Gin())

	if reqdata.TgUserId > 0 {
		db = db.Where(dao.TgUserID.Eq(reqdata.TgUserId))
	}

	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(int32(reqdata.SellerID)))
	}

	if reqdata.TgId > 0 {
		db = db.Where(dao.TgID.Eq(int32(reqdata.TgId)))
	}

	if reqdata.TgUserName != "" {
		db = db.Where(dao.TgName.Eq(reqdata.TgUserName))
	}

	if reqdata.StartTime != "" && reqdata.EndTime != "" {
		location, _ := time.LoadLocation("Asia/Shanghai")
		startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", reqdata.StartTime, location)
		endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", reqdata.EndTime, location)
		db = db.Where(dao.CreateTime.Between(startTime, endTime))
	}

	total, err := db.
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		LeftJoin(xAdmin, xAdmin.ID.EqCol(dao.OperatorID)).
		LeftJoin(xRobot, xRobot.ID.EqCol(dao.TgID)).
		Select(dao.ALL, xSeller.SellerName, xAdmin.Account.As("Operator"), xRobot.TgRobotUserName.As("RobotUserName")).
		Order(dao.CreateTime.Desc()).
		ScanByPage(&list, offset, limit)

	for _, v := range list {
		extraData := struct {
			TotalInviteCount   int64
			TotalGrabCount     int64
			TotalWithdrawCount int64
		}{}
		_ = xTgRedpacketInviteDB.Select(
			xTgRedPacketInviteDao.InviteCount.Sum().As("TotalInviteCount")).Where(
			xTgRedPacketInviteDao.TgID.Eq(v.TgID),
			xTgRedPacketInviteDao.TgUserID.Eq(v.TgUserID)).Scan(&extraData)

		_ = xTgGrabRedPacketDB.Select(xTgGrabRedPacketDao.ID.Count().As("TotalGrabCount")).Where(
			xTgGrabRedPacketDao.UserID.Eq(v.TgUserID),
			xTgGrabRedPacketDao.TgID.Eq(v.TgID)).Scan(&extraData)

		_ = xTgWitdrawRedPacketDB.Select(xTgWithdrawRedPacketDao.ID.Count().As("TotalWithdrawCount")).Where(
			xTgWithdrawRedPacketDao.TgUserID.Eq(v.TgUserID),
			xTgWithdrawRedPacketDao.TgID.Eq(v.TgID)).Scan(&extraData)

		v.TotalInviteCount = extraData.TotalInviteCount
		v.TotalGrabCount = extraData.TotalGrabCount
		v.TotalWithdrawCount = extraData.TotalWithdrawCount
	}

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TgRedPacketController) add_count(ctx *abugo.AbuHttpContent) {

	errcode := 0
	reqdata := struct {
		Id         int32
		Count      int64
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人统计", "改", "红包机器人统计修改")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgRedpacketUser
	db := dao.WithContext(ctx.Gin())

	if reqdata.Count == 0 {
		ctx.RespErr(errors.New("请输入大于0"), &errcode)
		return
	}
	operatorId := server.GetToken(ctx).UserId
	_, err := db.Where(dao.ID.Eq(reqdata.Id)).Updates(map[string]interface{}{
		"ArtificialGrabCount": gorm.Expr("ArtificialGrabCount + ?", reqdata.Count),
		"GrabCount":           gorm.Expr("GrabCount + ?", reqdata.Count),
		"OperatorId":          operatorId,
	})

	if err != nil {
		ctx.RespErr(errors.New("新增人工次数失败"), &errcode)
		return
	}

	ctx.RespOK()
}
