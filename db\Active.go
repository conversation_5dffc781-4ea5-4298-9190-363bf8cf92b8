package db

import (
	"fmt"
	"xserver/server"
)

type Active struct {
	Id         int    `gorm:"column:Id"`         //
	SellerId   int    `gorm:"column:SellerId"`   //
	State      int    `gorm:"column:State"`      //1启用,2禁用
	Sort       int    `gorm:"column:Sort"`       //排序数字越大,越靠前
	Title      string `gorm:"column:Title"`      //标题
	TitleImg   string `gorm:"column:TitleImg"`   //列表图片
	Content    string `gorm:"column:Content"`    //内容
	ContentImg string `gorm:"column:ContentImg"` //内容图片
	StartTime  string `gorm:"column:StartTime"`  //活动开始时间
	Endtime    string `gorm:"column:Endtime"`    //活动结束时间
	CreateTime string `gorm:"column:CreateTime"` //活动创建时间
	Memo       string `gorm:"column:Memo"`
}

func (*Active) TableName() string {
	return "x_active"
}

func Active_Page_Data(Page int, PageSize int, SellerId int) (int, []Active) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id asc"
	PageKey := "Id"
	data := Active{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []Active{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []Active{}
	dbtable.Where(fmt.Sprintf("%s >= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}
