// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotStatisticsDay = "x_robot_statistics_day"

// XRobotStatisticsDay mapped from table <x_robot_statistics_day>
type XRobotStatisticsDay struct {
	ID                int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`               // pk
	StatisticDate     time.Time `gorm:"column:statistic_date;not null;comment:统计日期" json:"statistic_date"`          // 统计日期
	SellerID          int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                            // 运营商ID
	ChannelID         int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                           // 渠道ID
	RobotType         int32     `gorm:"column:robot_type;comment:机器人类型" json:"robot_type"`                          // 机器人类型
	Name              string    `gorm:"column:name;not null;comment:机器人名称" json:"name"`                             // 机器人名称
	Token             string    `gorm:"column:token;comment:token" json:"token"`                                    // token
	UserID            int64     `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`                        // 用户id
	TgChatID          int64     `gorm:"column:tg_chat_id;comment:飞机ID" json:"tg_chat_id"`                           // 飞机ID
	TgUserName        string    `gorm:"column:tg_user_name;comment:飞机账号" json:"tg_user_name"`                       // 飞机账号
	LoginTime         time.Time `gorm:"column:login_time;comment:登录日期" json:"login_time"`                           // 登录日期
	RegisterTime      time.Time `gorm:"column:register_time;comment:注册时间" json:"register_time"`                     // 注册时间
	FirstRechargeTime time.Time `gorm:"column:first_recharge_time;comment:首充时间" json:"first_recharge_time"`         // 首充时间
	IsInResourceDb    int32     `gorm:"column:is_in_resource_db;comment:是否在库" json:"is_in_resource_db"`             // 是否在库
	BetCount          int32     `gorm:"column:bet_count;comment:投注次数" json:"bet_count"`                             // 投注次数
	TransferBetCount  int64     `gorm:"column:transfer_bet_count;comment:转账次数" json:"transfer_bet_count"`           // 转账次数
	GiftUsdtStatus    int32     `gorm:"column:gift_usdt_status;comment:usdt礼金状态" json:"gift_usdt_status"`           // usdt礼金状态
	GiftTrxStatus     int32     `gorm:"column:gift_trx_status;comment:trx领取状态" json:"gift_trx_status"`              // trx领取状态
	IsGiftUsdt        int32     `gorm:"column:is_gift_usdt;comment:是否是领取U玩家" json:"is_gift_usdt"`                   // 是否是领取U玩家
	IsGiftTrx         int32     `gorm:"column:is_gift_trx;comment:领取T" json:"is_gift_trx"`                          // 领取T
	MissionGiftUsdt   int32     `gorm:"column:mission_gift_usdt;comment:任务赠送U" json:"mission_gift_usdt"`            // 任务赠送U
	JoinGroupStatus   int32     `gorm:"column:join_group_status;comment:加入群组状态" json:"join_group_status"`           // 加入群组状态
	BandingTrxAddress string    `gorm:"column:banding_trx_address;comment:绑定trx地址" json:"banding_trx_address"`      // 绑定trx地址
	ShardLinkCnt      int32     `gorm:"column:shard_link_cnt;comment:点击分享次数" json:"shard_link_cnt"`                 // 点击分享次数
	AgentCount        int32     `gorm:"column:agent_count;comment:邀请人数2" json:"agent_count"`                        // 邀请人数2
	CreateTime        time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建" json:"create_time"` // 创建
	UpdateTime        time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新" json:"update_time"` // 更新
}

// TableName XRobotStatisticsDay's table name
func (*XRobotStatisticsDay) TableName() string {
	return TableNameXRobotStatisticsDay
}
