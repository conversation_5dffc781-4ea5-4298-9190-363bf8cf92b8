package db

import (
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

type AgentDailly struct {
	UserId              int     `gorm:"column:UserId"`              //
	RecordDate          string  `gorm:"column:RecordDate"`          //
	SellerId            int     `gorm:"column:SellerId"`            //
	BetTrx              float64 `gorm:"column:BetTrx"`              //Trx下注
	BetUsdt             float64 `gorm:"column:BetUsdt"`             //usdt下注
	RewardTrx           float64 `gorm:"column:RewardTrx"`           //trx返奖
	RewardUsdt          float64 `gorm:"column:RewardUsdt"`          //usdt返奖
	LiuSuiTrx           float64 `gorm:"column:LiuSuiTrx"`           //trx流水
	LiuSuiUsdt          float64 `gorm:"column:LiuSuiUsdt"`          //usdt流水
	DictBetTrx          float64 `gorm:"column:DictBetTrx"`          //Trx下注
	DictBetUsdt         float64 `gorm:"column:DictBetUsdt"`         //usdt下注
	DictLiuSuiUsdt      float64 `gorm:"column:DictLiuSuiUsdt"`      //usdt流水
	DictLiuSuiTrx       float64 `gorm:"column:DictLiuSuiTrx"`       //trx流水
	DictRewardTrx       float64 `gorm:"column:DictRewardTrx"`       //trx返奖
	DictRewardUsdt      float64 `gorm:"column:DictRewardUsdt"`      //usdt返奖
	TotalCommissionTrx  float64 `gorm:"column:TotalCommissionTrx"`  //当日产生佣金
	TotalCommissionUsdt float64 `gorm:"column:TotalCommissionUsdt"` //
	GetedCommissionTrx  float64 `gorm:"column:GetedCommissionTrx"`  //当日领取佣金
	GetedCommissionUsdt float64 `gorm:"column:GetedCommissionUsdt"` //
	FineCommissionTrx   float64 `gorm:"column:FineCommissionTrx"`   //当日罚没佣金
	FineCommissionUsdt  float64 `gorm:"column:FineCommissionUsdt"`  //
	AgentId             int     `gorm:"column:AgentId"`             //
	NewChildCount       int     `gorm:"column:NewChildCount"`
	SelfBetTrx          float64 `gorm:"column:SelfBetTrx"`     //Trx下注
	SelfBetUsdt         float64 `gorm:"column:SelfBetUsdt"`    //usdt下注
	SelfLiuSuiUsdt      float64 `gorm:"column:SelfLiuSuiUsdt"` //usdt流水
	SelfLiuSuiTrx       float64 `gorm:"column:SelfLiuSuiTrx"`  //trx流水
	SelfRewardTrx       float64 `gorm:"column:SelfRewardTrx"`  //trx返奖
	SelfRewardUsdt      float64 `gorm:"column:SelfRewardUsdt"` //usdt返奖
	Address             string  `gorm:"column:Address"`
	AgentLevel          int     `gorm:"column:AgentLevel"`
	DictNewChildCount   int     `gorm:"column:DictNewChildCount"`
	ChannelId           int     `gorm:"column:ChannelId"`
}

func (*AgentDailly) TableName() string {
	return "x_agent_dailly"
}

func AgentDailly_Page_Data(Page int, PageSize int, SellerId int, UserId int, StartTime int64, ChannelId int, Address string) (int, []AgentDailly) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := AgentDailly{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "x_agent_dailly.ChannelId", "=", ChannelId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_agent_dailly.SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_agent_dailly.UserId", "=", UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_agent_dailly.Address", "=", Address, "")
	server.Db().AddWhere(&sql, &params, "and", "x_agent_dailly.RecordDate", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	if StartTime == 0 {
		StartTime = abugo.LocalDateToTimeStamp(abugo.GetLocalDate()) * 1000
	}
	EndTime := StartTime + 86400000
	server.Db().AddWhere(&sql, &params, "and", "x_agent_dailly.RecordDate", "<", abugo.TimeStampToLocalTime(EndTime), "")
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(x_agent_dailly.%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []AgentDailly{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("x_agent_dailly.%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []AgentDailly{}
	dbtable.Select("x_agent_dailly.*,x_user.AgentId,x_user.Address,JSON_LENGTH(Agents) as AgentLevel").Where(fmt.Sprintf("x_agent_dailly.%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Joins("left join x_user on x_user.UserId = x_agent_dailly.UserId").Find(&result)
	return dt.Total, result
}

func AgentDailly_Child_Data_Dict(UserId int, timestr string) []AgentDailly {
	sql := "SELECT x_agent_child.Child,x_user.TopAgentId,x_user.FenCheng,x_user.AgentId,x_user.Address,JSON_LENGTH(x_user.Agents) AS AgentLevel,x_user.IsAgent,x_agent_dailly.* FROM x_agent_child INNER JOIN x_user ON x_user.UserId =  x_agent_child.Child INNER JOIN x_agent_dailly ON x_agent_dailly.UserId =  x_agent_child.Child AND x_agent_dailly.RecordDate = ?  WHERE x_agent_child.UserId = ? and x_agent_child.ChildLevel = 0 "
	dbresult, _ := server.Db().Conn().Query(sql, timestr, UserId)
	result := []AgentDailly{}
	for dbresult.Next() {
		d := AgentDailly{}
		abugo.GetDbResult(dbresult, &d)
		result = append(result, d)
	}
	return result
}

func AgentDailly_Child_Data_All(UserId int, timestr string) []AgentDailly {
	sql := "SELECT x_agent_child.Child,x_agent_child.ChildLevel,x_user.TopAgentId,x_user.FenCheng,x_user.AgentId,x_user.Address,JSON_LENGTH(x_user.Agents) AS AgentLevel,x_user.IsAgent,x_agent_dailly.* FROM x_agent_child INNER JOIN x_user ON x_user.UserId =  x_agent_child.Child INNER JOIN x_agent_dailly ON x_agent_dailly.UserId =  x_agent_child.Child AND x_agent_dailly.RecordDate = ?  WHERE x_agent_child.UserId = ? ORDER BY ChildLevel ASC"
	dbresult, _ := server.Db().Conn().Query(sql, timestr, UserId)
	result := []AgentDailly{}
	for dbresult.Next() {
		d := AgentDailly{}
		abugo.GetDbResult(dbresult, &d)
		result = append(result, d)
	}
	return result
}
