// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentGameDefine = "x_agent_game_define"

// XAgentGameDefine mapped from table <x_agent_game_define>
type XAgentGameDefine struct {
	SellerID   int32     `gorm:"column:SellerId;not null" json:"SellerId"`
	ChannelID  int32     `gorm:"column:ChannelId;not null" json:"ChannelId"`
	AgentLevel int32     `gorm:"column:AgentLevel;not null;comment:代理等级" json:"AgentLevel"` // 代理等级
	Brand      string    `gorm:"column:Brand;not null;comment:品牌" json:"Brand"`             // 品牌
	GameID     string    `gorm:"column:GameId;not null;comment:游戏Id" json:"GameId"`         // 游戏Id
	CatID      int32     `gorm:"column:CatId;not null;comment:彩票大类ID" json:"CatId"`         // 彩票大类ID
	LiuSui     float64   `gorm:"column:LiuSui;default:0.000000" json:"LiuSui"`
	Reward     float64   `gorm:"column:Reward;default:0.000000" json:"Reward"`
	Memo       string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XAgentGameDefine's table name
func (*XAgentGameDefine) TableName() string {
	return TableNameXAgentGameDefine
}
