// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotReportImportFlow = "x_robot_report_import_flow"

// XRobotReportImportFlow mapped from table <x_robot_report_import_flow>
type XRobotReportImportFlow struct {
	ID                        int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                                             // pk
	DateTime                  time.Time `gorm:"column:date_time;not null;comment:日期" json:"date_time"`                                                    // 日期
	SellerID                  int32     `gorm:"column:seller_id;not null;comment:经销商ID" json:"seller_id"`                                                 // 经销商ID
	ChannelID                 int32     `gorm:"column:channel_id;not null;comment:渠道ID" json:"channel_id"`                                                // 渠道ID
	UserID                    int64     `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`                                                      // 用户id
	TopAgentID                int64     `gorm:"column:top_agent_id;comment:顶级代理用户ID" json:"top_agent_id"`                                                 // 顶级代理用户ID
	BandingTrxAddress         string    `gorm:"column:banding_trx_address;comment:总投注人数" json:"banding_trx_address"`                                      // 总投注人数
	IsIndb                    int32     `gorm:"column:is_indb;comment:0不在库;1在库" json:"is_indb"`                                                           // 0不在库;1在库
	StartCnt                  int32     `gorm:"column:start_cnt;comment:启动机器人" json:"start_cnt"`                                                          // 启动机器人
	GiftUsdtStatus            int32     `gorm:"column:gift_usdt_status;comment:usdt体验金" json:"gift_usdt_status"`                                          // usdt体验金
	GiftTrxStatus             int32     `gorm:"column:gift_trx_status;comment:trx体验金" json:"gift_trx_status"`                                             // trx体验金
	GiftUsdtAmount            int32     `gorm:"column:gift_usdt_amount;comment:usdt体验金金额" json:"gift_usdt_amount"`                                        // usdt体验金金额
	GiftTrxAmount             int32     `gorm:"column:gift_trx_amount;comment:trx金额" json:"gift_trx_amount"`                                              // trx金额
	RechargeCnt               int32     `gorm:"column:recharge_cnt;comment:充值次数" json:"recharge_cnt"`                                                     // 充值次数
	RechargeAmount            float64   `gorm:"column:recharge_amount;default:0.00;comment:充值金额" json:"recharge_amount"`                                  // 充值金额
	WithdrawCnt               int32     `gorm:"column:withdraw_cnt;comment:提款次数" json:"withdraw_cnt"`                                                     // 提款次数
	WithdrawAmount            float64   `gorm:"column:withdraw_amount;default:0.00;comment:提款金额" json:"withdraw_amount"`                                  // 提款金额
	FirstRechargeAmount       float64   `gorm:"column:first_recharge_amount;default:0.00;comment:首次充值金额" json:"first_recharge_amount"`                    // 首次充值金额
	FirstRechargeTime         time.Time `gorm:"column:first_recharge_time;comment:首次充值时间" json:"first_recharge_time"`                                     // 首次充值时间
	HashBalanceBetAmount      float64   `gorm:"column:hash_balance_bet_amount;default:0.00;comment:余额投注额" json:"hash_balance_bet_amount"`                 // 余额投注额
	HashBalanceWinAmount      float64   `gorm:"column:hash_balance_win_amount;default:0.00;comment:余额赢" json:"hash_balance_win_amount"`                   // 余额赢
	HashBalanceBetCount       int32     `gorm:"column:hash_balance_bet_count;comment:投注次数" json:"hash_balance_bet_count"`                                 // 投注次数
	HashBalanceWinCount       int32     `gorm:"column:hash_balance_win_count;comment:赢次数" json:"hash_balance_win_count"`                                  // 赢次数
	HashBalanceFirstBet       float64   `gorm:"column:hash_balance_first_bet;default:0.00;comment:首次投注" json:"hash_balance_first_bet"`                    // 首次投注
	HashTransferTrxBetAmount  float64   `gorm:"column:hash_transfer_trx_bet_amount;default:0.00;comment:转账trx投注额" json:"hash_transfer_trx_bet_amount"`    // 转账trx投注额
	HashTransferTrxWinAmount  float64   `gorm:"column:hash_transfer_trx_win_amount;default:0.00;comment:转账trx赢" json:"hash_transfer_trx_win_amount"`      // 转账trx赢
	HashTransferTrxBetCount   int32     `gorm:"column:hash_transfer_trx_bet_count;comment:转账trx投注次数" json:"hash_transfer_trx_bet_count"`                  // 转账trx投注次数
	HashTransferTrxWinCount   int32     `gorm:"column:hash_transfer_trx_win_count;comment:转账trx赢次数" json:"hash_transfer_trx_win_count"`                   // 转账trx赢次数
	HashTransferTrxFirstBet   float64   `gorm:"column:hash_transfer_trx_first_bet;default:0.00;comment:首次投注" json:"hash_transfer_trx_first_bet"`          // 首次投注
	HashTransferUsdtBetAmount float64   `gorm:"column:hash_transfer_usdt_bet_amount;default:0.00;comment:转账usdt投注额" json:"hash_transfer_usdt_bet_amount"` // 转账usdt投注额
	HashTransferUsdtWinAmount float64   `gorm:"column:hash_transfer_usdt_win_amount;default:0.00;comment:转账usdt赢" json:"hash_transfer_usdt_win_amount"`   // 转账usdt赢
	HashTransferUsdtBetCount  int32     `gorm:"column:hash_transfer_usdt_bet_count;comment:转账usdt投注次数" json:"hash_transfer_usdt_bet_count"`               // 转账usdt投注次数
	HashTransferUsdtWinCount  int32     `gorm:"column:hash_transfer_usdt_win_count;comment:转账usdt赢次数" json:"hash_transfer_usdt_win_count"`                // 转账usdt赢次数
	HashTransferUsdtFirstBet  float64   `gorm:"column:hash_transfer_usdt_first_bet;default:0.00;comment:首次投注" json:"hash_transfer_usdt_first_bet"`        // 首次投注
	CreateTime                time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建" json:"create_time"`                               // 创建
	UpdateTime                time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新" json:"update_time"`                               // 更新
}

// TableName XRobotReportImportFlow's table name
func (*XRobotReportImportFlow) TableName() string {
	return TableNameXRobotReportImportFlow
}
