// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgRedpacketUser(db *gorm.DB, opts ...gen.DOOption) xTgRedpacketUser {
	_xTgRedpacketUser := xTgRedpacketUser{}

	_xTgRedpacketUser.xTgRedpacketUserDo.UseDB(db, opts...)
	_xTgRedpacketUser.xTgRedpacketUserDo.UseModel(&model.XTgRedpacketUser{})

	tableName := _xTgRedpacketUser.xTgRedpacketUserDo.TableName()
	_xTgRedpacketUser.ALL = field.NewAsterisk(tableName)
	_xTgRedpacketUser.ID = field.NewInt32(tableName, "id")
	_xTgRedpacketUser.TgID = field.NewInt32(tableName, "TgId")
	_xTgRedpacketUser.TgUserID = field.NewInt64(tableName, "TgUserId")
	_xTgRedpacketUser.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRedpacketUser.WithDrawCount = field.NewInt32(tableName, "WithDrawCount")
	_xTgRedpacketUser.GrabCount = field.NewInt32(tableName, "GrabCount")
	_xTgRedpacketUser.ArtificialGrabCount = field.NewInt32(tableName, "ArtificialGrabCount")
	_xTgRedpacketUser.TgName = field.NewString(tableName, "TgName")
	_xTgRedpacketUser.Balance = field.NewFloat64(tableName, "Balance")
	_xTgRedpacketUser.Withdraw = field.NewFloat64(tableName, "Withdraw")
	_xTgRedpacketUser.TgInviteUID = field.NewInt64(tableName, "TgInviteUid")
	_xTgRedpacketUser.OperatorID = field.NewInt32(tableName, "OperatorId")
	_xTgRedpacketUser.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgRedpacketUser.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTgRedpacketUser.fillFieldMap()

	return _xTgRedpacketUser
}

type xTgRedpacketUser struct {
	xTgRedpacketUserDo xTgRedpacketUserDo

	ALL                 field.Asterisk
	ID                  field.Int32
	TgID                field.Int32 // Tg后台ID
	TgUserID            field.Int64 // Tg用户ID
	SellerID            field.Int32 // 运营商ID
	WithDrawCount       field.Int32
	GrabCount           field.Int32
	ArtificialGrabCount field.Int32 // 人工次数
	TgName              field.String
	Balance             field.Float64
	Withdraw            field.Float64 // 提现金额
	TgInviteUID         field.Int64   // Tg邀请用户ID
	OperatorID          field.Int32   // 操作人id
	CreateTime          field.Time
	UpdateTime          field.Time

	fieldMap map[string]field.Expr
}

func (x xTgRedpacketUser) Table(newTableName string) *xTgRedpacketUser {
	x.xTgRedpacketUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRedpacketUser) As(alias string) *xTgRedpacketUser {
	x.xTgRedpacketUserDo.DO = *(x.xTgRedpacketUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRedpacketUser) updateTableName(table string) *xTgRedpacketUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "id")
	x.TgID = field.NewInt32(table, "TgId")
	x.TgUserID = field.NewInt64(table, "TgUserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.WithDrawCount = field.NewInt32(table, "WithDrawCount")
	x.GrabCount = field.NewInt32(table, "GrabCount")
	x.ArtificialGrabCount = field.NewInt32(table, "ArtificialGrabCount")
	x.TgName = field.NewString(table, "TgName")
	x.Balance = field.NewFloat64(table, "Balance")
	x.Withdraw = field.NewFloat64(table, "Withdraw")
	x.TgInviteUID = field.NewInt64(table, "TgInviteUid")
	x.OperatorID = field.NewInt32(table, "OperatorId")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTgRedpacketUser) WithContext(ctx context.Context) *xTgRedpacketUserDo {
	return x.xTgRedpacketUserDo.WithContext(ctx)
}

func (x xTgRedpacketUser) TableName() string { return x.xTgRedpacketUserDo.TableName() }

func (x xTgRedpacketUser) Alias() string { return x.xTgRedpacketUserDo.Alias() }

func (x xTgRedpacketUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRedpacketUserDo.Columns(cols...)
}

func (x *xTgRedpacketUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRedpacketUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["id"] = x.ID
	x.fieldMap["TgId"] = x.TgID
	x.fieldMap["TgUserId"] = x.TgUserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["WithDrawCount"] = x.WithDrawCount
	x.fieldMap["GrabCount"] = x.GrabCount
	x.fieldMap["ArtificialGrabCount"] = x.ArtificialGrabCount
	x.fieldMap["TgName"] = x.TgName
	x.fieldMap["Balance"] = x.Balance
	x.fieldMap["Withdraw"] = x.Withdraw
	x.fieldMap["TgInviteUid"] = x.TgInviteUID
	x.fieldMap["OperatorId"] = x.OperatorID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTgRedpacketUser) clone(db *gorm.DB) xTgRedpacketUser {
	x.xTgRedpacketUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRedpacketUser) replaceDB(db *gorm.DB) xTgRedpacketUser {
	x.xTgRedpacketUserDo.ReplaceDB(db)
	return x
}

type xTgRedpacketUserDo struct{ gen.DO }

func (x xTgRedpacketUserDo) Debug() *xTgRedpacketUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRedpacketUserDo) WithContext(ctx context.Context) *xTgRedpacketUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRedpacketUserDo) ReadDB() *xTgRedpacketUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRedpacketUserDo) WriteDB() *xTgRedpacketUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRedpacketUserDo) Session(config *gorm.Session) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRedpacketUserDo) Clauses(conds ...clause.Expression) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRedpacketUserDo) Returning(value interface{}, columns ...string) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRedpacketUserDo) Not(conds ...gen.Condition) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRedpacketUserDo) Or(conds ...gen.Condition) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRedpacketUserDo) Select(conds ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRedpacketUserDo) Where(conds ...gen.Condition) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRedpacketUserDo) Order(conds ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRedpacketUserDo) Distinct(cols ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRedpacketUserDo) Omit(cols ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRedpacketUserDo) Join(table schema.Tabler, on ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRedpacketUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRedpacketUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRedpacketUserDo) Group(cols ...field.Expr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRedpacketUserDo) Having(conds ...gen.Condition) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRedpacketUserDo) Limit(limit int) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRedpacketUserDo) Offset(offset int) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRedpacketUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRedpacketUserDo) Unscoped() *xTgRedpacketUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRedpacketUserDo) Create(values ...*model.XTgRedpacketUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRedpacketUserDo) CreateInBatches(values []*model.XTgRedpacketUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRedpacketUserDo) Save(values ...*model.XTgRedpacketUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRedpacketUserDo) First() (*model.XTgRedpacketUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketUser), nil
	}
}

func (x xTgRedpacketUserDo) Take() (*model.XTgRedpacketUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketUser), nil
	}
}

func (x xTgRedpacketUserDo) Last() (*model.XTgRedpacketUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketUser), nil
	}
}

func (x xTgRedpacketUserDo) Find() ([]*model.XTgRedpacketUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRedpacketUser), err
}

func (x xTgRedpacketUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRedpacketUser, err error) {
	buf := make([]*model.XTgRedpacketUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRedpacketUserDo) FindInBatches(result *[]*model.XTgRedpacketUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRedpacketUserDo) Attrs(attrs ...field.AssignExpr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRedpacketUserDo) Assign(attrs ...field.AssignExpr) *xTgRedpacketUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRedpacketUserDo) Joins(fields ...field.RelationField) *xTgRedpacketUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRedpacketUserDo) Preload(fields ...field.RelationField) *xTgRedpacketUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRedpacketUserDo) FirstOrInit() (*model.XTgRedpacketUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketUser), nil
	}
}

func (x xTgRedpacketUserDo) FirstOrCreate() (*model.XTgRedpacketUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketUser), nil
	}
}

func (x xTgRedpacketUserDo) FindByPage(offset int, limit int) (result []*model.XTgRedpacketUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRedpacketUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRedpacketUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRedpacketUserDo) Delete(models ...*model.XTgRedpacketUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRedpacketUserDo) withDO(do gen.Dao) *xTgRedpacketUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
