// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotAdStartRecord = "x_robot_ad_start_record"

// XRobotAdStartRecord mapped from table <x_robot_ad_start_record>
type XRobotAdStartRecord struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                 // pk
	SellerID       int32     `gorm:"column:seller_id;not null;comment:运营商ID" json:"seller_id"`                     // 运营商ID
	ChannelID      int32     `gorm:"column:channel_id;not null;comment:渠道ID" json:"channel_id"`                    // 渠道ID
	Name           string    `gorm:"column:name;not null;comment:机器人name" json:"name"`                             // 机器人name
	Token          string    `gorm:"column:token;not null;comment:机器人token" json:"token"`                          // 机器人token
	UserChatID     int64     `gorm:"column:user_chat_id;not null;comment:用户chatID" json:"user_chat_id"`            // 用户chatID
	UserName       string    `gorm:"column:user_name;comment:用户名" json:"user_name"`                                // 用户名
	UserFullName   string    `gorm:"column:user_full_name;comment:全名" json:"user_full_name"`                       // 全名
	LangCode       string    `gorm:"column:lang_code;comment:用户语言" json:"lang_code"`                               // 用户语言
	StartFirstTime time.Time `gorm:"column:start_first_time;comment:首次启动时间" json:"start_first_time"`               // 首次启动时间
	StartLastTime  time.Time `gorm:"column:start_last_time;comment:最后启动时间" json:"start_last_time"`                 // 最后启动时间
	StartCnt       int32     `gorm:"column:start_cnt;comment:启动次数" json:"start_cnt"`                               // 启动次数
	CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建日期" json:"create_time"` // 创建日期
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新日期" json:"update_time"` // 更新日期
}

// TableName XRobotAdStartRecord's table name
func (*XRobotAdStartRecord) TableName() string {
	return TableNameXRobotAdStartRecord
}
