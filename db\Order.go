package db

import (
	"fmt"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
)

type Order struct {
	Id                int     `gorm:"column:Id"`                //
	SellerId          int     `gorm:"column:SellerId"`          //运营商
	UserId            int     `gorm:"column:UserId"`            //玩家id
	GameId            int     `gorm:"column:GameId"`            //游戏id
	RoomLevel         int     `gorm:"column:RoomLevel"`         //房间等级
	ToAddress         string  `gorm:"column:ToAddress"`         //游戏地址
	FromAddress       string  `gorm:"column:FromAddress"`       //玩家地址
	TxId              string  `gorm:"column:TxId"`              //下注哈希
	BlockNum          int     `gorm:"column:BlockNum"`          //下注块编号
	BlockHash         string  `gorm:"column:BlockHash"`         //下注块哈希
	Symbol            string  `gorm:"column:Symbol"`            //币种
	Amount            float64 `gorm:"column:Amount"`            //下注金额
	State             int     `gorm:"column:State"`             //订单状态   1.未找到房间信息   2.未找到运营商
	Memo              string  `gorm:"column:Memo"`              //备忘录
	CreateTime        string  `gorm:"column:CreateTime"`        //订单创建时间
	RewardOrder       string  `gorm:"column:RewardOrder"`       //返奖订单号
	RewardAmount      float64 `gorm:"column:RewardAmount"`      //返奖励金额
	BonusAmount       float64 `gorm:"column:BonusAmount"`       //Bonus下注金额
	BonusRewardAmount float64 `gorm:"column:BonusRewardAmount"` //Bonus返奖励金额
	RealAmount        float64 `gorm:"column:RealAmount"`        //实际下注金额
	RealRewardAmount  float64 `gorm:"column:RealRewardAmount"`  //实际返奖金额
	RewardTxId        string  `gorm:"column:RewardTxId"`        //返奖哈希
	WinAmount         float64 `gorm:"column:WinAmount"`         //中奖金额
	BetArea           string  `gorm:"column:BetArea"`           //
	OpenArea          string  `gorm:"column:OpenArea"`          //
	RewardType        int     `gorm:"column:RewardType"`        //
	RewardRate        float64 `gorm:"column:RewardRate"`        //
	DataState         int     `gorm:"column:DataState"`         //数据状态
	AuditAccount      string  `gorm:"column:AuditAccount"`
	AuditTime         string  `gorm:"column:AuditTime"`
	IsWin             int     `gorm:"column:IsWin"`
	Fined             int     `gorm:"column:Fined"`
	LiuSui            float64 `gorm:"column:LiuSui"`
	TopAgentId        int     `gorm:"column:TopAgentId"`
	FineAccount       string  `gorm:"column:FineAccount"`
	FineTime          string  `gorm:"column:FineTime"`
	FineMemo          string  `gorm:"column:FineMemo"`
	NowTime           string
	ChannelId         int     `gorm:"column:ChannelId"`
	BetChannelId      int     `gorm:"column:BetChannelId"`
	UserAmount        float64 `gorm:"column:UserAmount"`
	UserBonusAmount   float64 `gorm:"column:UserBonusAmount"`
	IsTest            int     `gorm:"column:IsTest"`
	HeFeeRate         float64 `gorm:"column:HeFeeRate"`

	Period        string `gorm:"column:Period"`
	BlockMaker    string `gorm:"column:BlockMaker"`
	NextBlockHash string `gorm:"column:NextBlockHash"`
	RewardTime    string `gorm:"column:RewardTime"`

	ExceedLimit  int `gorm:"column:ExceedLimit"`
	SpecialAgent int `gorm:"column:SpecialAgent"`

	RegisterIP string `gorm:"column:RegisterIP"`
	RegLang    string `gorm:"column:RegLang"`
	Ip         string `gorm:"column:Ip"`
	Lang       string `gorm:"column:Lang"`
	UtType     int    `gorm:"column:UtType"`
	ChainType  int    `gorm:"column:ChainType"`
}

type AuditOrder struct {
	Id                    int     `gorm:"column:Id"`           //
	UserId                int     `gorm:"column:UserId"`       //玩家id
	Symbol                string  `gorm:"column:Symbol"`       //币种
	Amount                float64 `gorm:"column:Amount"`       //下注金额
	RewardAmount          float64 `gorm:"column:RewardAmount"` //返奖励金额
	CreateTime            string  `gorm:"column:CreateTime"`   //订单创建时间
	State                 int     `gorm:"column:State"`        //订单状态   1.未找到房间信息   2.未找到运营商
	FromAddress           string  `gorm:"column:FromAddress"`  //玩家地址
	GameId                int     `gorm:"column:GameId"`       //游戏id
	RoomLevel             int     `gorm:"column:RoomLevel"`
	AuditAccount          string  `gorm:"column:AuditAccount"`
	AuditTime             string  `gorm:"column:AuditTime"`
	ToAddress             string  `gorm:"column:ToAddress"`
	TxId                  string  `gorm:"column:TxId"`
	BlockNum              int     `gorm:"column:BlockNum"`
	RewardRate            float64 `gorm:"column:RewardRate"`
	BlockHash             string  `gorm:"column:BlockHash"`
	IsWin                 int     `gorm:"column:IsWin"`
	BetArea               string  `gorm:"column:BetArea"`
	OpenArea              string  `gorm:"column:OpenArea"`
	Memo                  string  `gorm:"column:Memo"`
	ChannelId             int     `gorm:"column:ChannelId"`
	NextBlockHash         string  `gorm:"column:NextBlockHash"`
	RiskEarlyWarningFirst int     `gorm:"column:RiskEarlyWarningFirst"`

	Period string `gorm:"column:Period"`

	BetTrx     float64 `gorm:"column:BetTrx"`
	BetUsdt    float64 `gorm:"column:BetUsdt"`
	LiuSuiTrx  float64 `gorm:"column:LiuSuiTrx"`
	LiuSuiUsdt float64 `gorm:"column:LiuSuiUsdt"`
	RewardTrx  float64 `gorm:"column:RewardTrx"`
	RewardUsdt float64 `gorm:"column:RewardUsdt"`

	TodayBetTrx     float64 `gorm:"column:TodayBetTrx"`
	TodayBetUsdt    float64 `gorm:"column:TodayBetUsdt"`
	TodayLiuSuiTrx  float64 `gorm:"column:TodayLiuSuiTrx"`
	TodayLiuSuiUsdt float64 `gorm:"column:TodayLiuSuiUsdt"`
	TodayRewardTrx  float64 `gorm:"column:TodayRewardTrx"`
	TodayRewardUsdt float64 `gorm:"column:TodayRewardUsdt"`
	SpecialAgent    int     `gorm:"column:SpecialAgent"`
	IsTest          int     `gorm:"column:IsTest"`
}

func (*AuditOrder) TableName() string {
	return "x_order"
}

func (*Order) TableName() string {
	return "x_order"
}

func Order_Page_Data(Page int, PageSize int, SellerId []int32, Id int, UserId int, FromAddress string,
	TxId string, RewardType int, State int, Symbol string, StartTime int64,
	EndTime int64, GameId []int, RoomLevel int, TopAgentId int, ToAddress string,
	KouChu int, ChannelId []int, Period string, BlockMaker string, IsTest int, SpecialAgent int, GameType int, ChainType int) (int, []Order) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "x_order.CreateTime desc, x_order.Id desc"
	PageKey := "x_order.Id"
	data := Order{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "x_order.CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	server.Db().AddWhere(&sql, &params, "and", "x_order.CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	//server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_order.Id", "=", Id, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_order.TopAgentId", "=", TopAgentId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_order.UserId", "=", UserId, 0)
	if IsTest == 3 {
		server.Db().AddWhere(&sql, &params, "and", "x_order.IsPanda", "=", 1, 0)
	} else {
		server.Db().AddWhere(&sql, &params, "and", "x_order.IsTest", "=", IsTest, 0)
	}

	if len(GameId) > 0 {
		GameType = 0
		in := ""
		for _, v := range GameId {
			if v == 0 {
				continue
			}
			in += fmt.Sprintf("%d,", v)
		}
		if len(in) > 0 {
			in = in[0 : len(in)-1]
			sql += fmt.Sprintf(" and x_order.GameId in (%s)", in)
		}
	}

	if GameType == -1 {
		sql += " and x_order.GameId in (1, 2, 3, 4, 5, 6, 7, 11, 12, 13, 301, 302, 303, 313, 323, 331, 332, 333)"
	} else if GameType == -2 {
		sql += " and x_order.GameId in (101,102,103,104,105,106,116,126,131,132,133,134,135,136,201,202,203,204,205,206)"
	}
	// 渠道多选
	channelIdStr := utils.ToChannels(ChannelId)
	if len(channelIdStr) > 0 {
		sql += fmt.Sprintf(" and x_order.ChannelId in (%s)", channelIdStr)
	}
	// 运营商多选
	sellerIdStr := utils.ToSellers(SellerId)
	if len(sellerIdStr) > 0 {
		sql += fmt.Sprintf(" and x_order.SellerId in (%s)", sellerIdStr)
	}

	//server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_order.RoomLevel", "=", RoomLevel, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_order.FromAddress", "=", FromAddress, "")
	server.Db().AddWhere(&sql, &params, "and", "x_order.ToAddress", "=", ToAddress, "")
	server.Db().AddWhere(&sql, &params, "and", "x_order.TxId", "=", TxId, "")
	server.Db().AddWhere(&sql, &params, "and", "x_order.Symbol", "=", Symbol, "")
	server.Db().AddWhere(&sql, &params, "and", "x_order.RewardType", "=", RewardType, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_order.State", "=", State, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_order.Period", "=", Period, "")
	server.Db().AddWhere(&sql, &params, "and", "x_order.BlockMaker", "=", BlockMaker, "")
	server.Db().AddWhere(&sql, &params, "and", "x_order.SpecialAgent", "=", SpecialAgent, 0)
	if KouChu == 1 {
		server.Db().AddWhere(&sql, &params, "and", "x_order.LiuSui", ">", 0, "")
	}
	server.Db().AddWhere(&sql, &params, "and", "x_order.ChainType", "=", ChainType, 0)
	fmt.Println(sql)

	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}

	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []Order{}
	}

	Offset := (Page - 1) * PageSize
	result := []Order{}
	dberr := dbtable.Where(sql, params...).
		Joins("left join x_user on x_order.userid = x_user.userid").
		Select("x_order.*, ROUND(x_order.Amount - x_order.BonusAmount, 2) as RealAmount, ROUND(x_order.RewardAmount - x_order.BonusRewardAmount, 2) as RealRewardAmount, x_user.RegisterIP as RegisterIP, x_user.RegLang as RegLang").
		Limit(PageSize).Offset(Offset).Order(OrderBy).Find(&result).Error
	if dberr != nil {
		logs.Error(dberr)
	}
	nowTime := time.Now().Format(abugo.TimeLayout)
	userIds := make([]int32, 0, dt.Total)
	var userId int32
	for i := 0; i < len(result); i++ {
		result[i].NowTime = nowTime
		userId = int32(result[i].UserId)
		if userId > 0 {
			userIds = append(userIds, userId)
		}
	}

	return dt.Total, result
}

func Order_Audit_Page_Data(Page int, PageSize int, SellerId int, UserId int, Symbol string, State int, StartTime int64, EndTime int64, Id int, GameId int, ChannelId int) (int, []AuditOrder) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := AuditOrder{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".UserId", "=", UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".Symbol", "=", Symbol, "")
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".Id", ">", Id, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".RewardAmount", ">", 0, -1)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".State", "=", State, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".GameId", "=", GameId, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".ChannelId", "=", ChannelId, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	if EndTime > 0 {
		EndTime += 86400000
	}
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []AuditOrder{}
	}
	result := []AuditOrder{}
	dbtable.Where(fmt.Sprintf("%s.%s <= ?", data.TableName(), PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	for i := 0; i < len(result); i++ {
		if result[i].UserId > 0 {
			{
				sql := "select BetTrx,BetUsdt,LiuSuiTrx,LiuSuiUsdt,RewardTrx,RewardUsdt from x_user where UserId = ?"
				dbresult, err := server.Db().Conn().Query(sql, result[i].UserId)
				if err == nil && dbresult.Next() {
					var BetTrx float64
					var BetUsdt float64
					var LiuSuiTrx float64
					var LiuSuiUsdt float64
					var RewardTrx float64
					var RewardUsdt float64
					dbresult.Scan(&BetTrx, &BetUsdt, &LiuSuiTrx, &LiuSuiUsdt, &RewardTrx, &RewardUsdt)
					result[i].BetTrx = BetTrx
					result[i].BetUsdt = BetUsdt
					result[i].LiuSuiTrx = LiuSuiTrx
					result[i].LiuSuiUsdt = LiuSuiUsdt
					result[i].RewardTrx = RewardTrx
					result[i].RewardUsdt = RewardUsdt
				}
				dbresult.Close()
			}
			{
				sql := fmt.Sprintf("select BetTrx,BetUsdt,LiuSuiTrx,LiuSuiUsdt,RewardTrx,RewardUsdt from x_user_dailly where UserId = ? and RecordDate = '%s'", strings.Split(result[i].CreateTime, " ")[0])
				dbresult, err := server.Db().Conn().Query(sql, result[i].UserId)
				if err == nil && dbresult.Next() {
					var BetTrx float64
					var BetUsdt float64
					var LiuSuiTrx float64
					var LiuSuiUsdt float64
					var RewardTrx float64
					var RewardUsdt float64
					dbresult.Scan(&BetTrx, &BetUsdt, &LiuSuiTrx, &LiuSuiUsdt, &RewardTrx, &RewardUsdt)
					result[i].TodayBetTrx = BetTrx
					result[i].TodayBetUsdt = BetUsdt
					result[i].TodayLiuSuiTrx = LiuSuiTrx
					result[i].TodayLiuSuiUsdt = LiuSuiUsdt
					result[i].TodayRewardTrx = RewardTrx
					result[i].TodayRewardUsdt = RewardUsdt
				}
				dbresult.Close()
			}
		} else {
			{
				sql := "select BetTrx,BetUsdt,LiuSuiTrx,LiuSuiUsdt,RewardTrx,RewardUsdt from x_guest where Address = ?"
				dbresult, err := server.Db().Conn().Query(sql, result[i].FromAddress)
				if err == nil && dbresult.Next() {
					var BetTrx float64
					var BetUsdt float64
					var LiuSuiTrx float64
					var LiuSuiUsdt float64
					var RewardTrx float64
					var RewardUsdt float64
					dbresult.Scan(&BetTrx, &BetUsdt, &LiuSuiTrx, &LiuSuiUsdt, &RewardTrx, &RewardUsdt)
					result[i].BetTrx = BetTrx
					result[i].BetUsdt = BetUsdt
					result[i].LiuSuiTrx = LiuSuiTrx
					result[i].LiuSuiUsdt = LiuSuiUsdt
					result[i].RewardTrx = RewardTrx
					result[i].RewardUsdt = RewardUsdt
				}
				dbresult.Close()
			}
			{
				sql := fmt.Sprintf("select BetTrx,BetUsdt,LiuSuiTrx,LiuSuiUsdt,RewardTrx,RewardUsdt from x_guest_dailly where Address = ? and RecordDate = '%s'", strings.Split(result[i].CreateTime, " ")[0])
				dbresult, err := server.Db().Conn().Query(sql, result[i].FromAddress)
				if err == nil && dbresult.Next() {
					var BetTrx float64
					var BetUsdt float64
					var LiuSuiTrx float64
					var LiuSuiUsdt float64
					var RewardTrx float64
					var RewardUsdt float64
					dbresult.Scan(&BetTrx, &BetUsdt, &LiuSuiTrx, &LiuSuiUsdt, &RewardTrx, &RewardUsdt)
					result[i].TodayBetTrx = BetTrx
					result[i].TodayBetUsdt = BetUsdt
					result[i].TodayLiuSuiTrx = LiuSuiTrx
					result[i].TodayLiuSuiUsdt = LiuSuiUsdt
					result[i].TodayRewardTrx = RewardTrx
					result[i].TodayRewardUsdt = RewardUsdt
				}
				dbresult.Close()
			}
		}
	}
	return dt.Total, result
}
