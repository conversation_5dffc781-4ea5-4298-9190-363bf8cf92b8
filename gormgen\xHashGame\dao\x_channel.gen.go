// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXChannel(db *gorm.DB, opts ...gen.DOOption) xChannel {
	_xChannel := xChannel{}

	_xChannel.xChannelDo.UseDB(db, opts...)
	_xChannel.xChannelDo.UseModel(&model.XChannel{})

	tableName := _xChannel.xChannelDo.TableName()
	_xChannel.ALL = field.NewAsterisk(tableName)
	_xChannel.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xChannel.SellerID = field.NewInt32(tableName, "SellerId")
	_xChannel.ChannelName = field.NewString(tableName, "ChannelName")
	_xChannel.ShowName = field.NewString(tableName, "ShowName")
	_xChannel.State = field.NewInt32(tableName, "State")
	_xChannel.Remark = field.NewString(tableName, "Remark")
	_xChannel.Icon = field.NewString(tableName, "Icon")
	_xChannel.Logo = field.NewString(tableName, "Logo")
	_xChannel.CreateTime = field.NewTime(tableName, "CreateTime")
	_xChannel.Sort = field.NewInt32(tableName, "Sort")
	_xChannel.ChatOpen = field.NewInt32(tableName, "ChatOpen")
	_xChannel.ChatCompanyID = field.NewString(tableName, "ChatCompanyId")
	_xChannel.AgentMode = field.NewInt32(tableName, "AgentMode")
	_xChannel.AgentCaseID = field.NewInt32(tableName, "AgentCaseId")
	_xChannel.PaymentMethod = field.NewInt32(tableName, "PaymentMethod")

	_xChannel.fillFieldMap()

	return _xChannel
}

type xChannel struct {
	xChannelDo xChannelDo

	ALL           field.Asterisk
	ChannelID     field.Int32
	SellerID      field.Int32  // 运营商
	ChannelName   field.String // 渠道名称
	ShowName      field.String
	State         field.Int32  // 1启用 2禁用
	Remark        field.String // 备注
	Icon          field.String
	Logo          field.String
	CreateTime    field.Time // 创建时间
	Sort          field.Int32
	ChatOpen      field.Int32 // 聊天室是否开启 1开启,2关闭
	ChatCompanyID field.String
	AgentMode     field.Int32 // 代理模式 1-无限代 2-三级代
	AgentCaseID   field.Int32 // 三级代理方案ID
	PaymentMethod field.Int32 // 支付方式 1=虚拟币，2=法币

	fieldMap map[string]field.Expr
}

func (x xChannel) Table(newTableName string) *xChannel {
	x.xChannelDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xChannel) As(alias string) *xChannel {
	x.xChannelDo.DO = *(x.xChannelDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xChannel) updateTableName(table string) *xChannel {
	x.ALL = field.NewAsterisk(table)
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelName = field.NewString(table, "ChannelName")
	x.ShowName = field.NewString(table, "ShowName")
	x.State = field.NewInt32(table, "State")
	x.Remark = field.NewString(table, "Remark")
	x.Icon = field.NewString(table, "Icon")
	x.Logo = field.NewString(table, "Logo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Sort = field.NewInt32(table, "Sort")
	x.ChatOpen = field.NewInt32(table, "ChatOpen")
	x.ChatCompanyID = field.NewString(table, "ChatCompanyId")
	x.AgentMode = field.NewInt32(table, "AgentMode")
	x.AgentCaseID = field.NewInt32(table, "AgentCaseId")
	x.PaymentMethod = field.NewInt32(table, "PaymentMethod")

	x.fillFieldMap()

	return x
}

func (x *xChannel) WithContext(ctx context.Context) *xChannelDo { return x.xChannelDo.WithContext(ctx) }

func (x xChannel) TableName() string { return x.xChannelDo.TableName() }

func (x xChannel) Alias() string { return x.xChannelDo.Alias() }

func (x xChannel) Columns(cols ...field.Expr) gen.Columns { return x.xChannelDo.Columns(cols...) }

func (x *xChannel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xChannel) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 15)
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelName"] = x.ChannelName
	x.fieldMap["ShowName"] = x.ShowName
	x.fieldMap["State"] = x.State
	x.fieldMap["Remark"] = x.Remark
	x.fieldMap["Icon"] = x.Icon
	x.fieldMap["Logo"] = x.Logo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["ChatOpen"] = x.ChatOpen
	x.fieldMap["ChatCompanyId"] = x.ChatCompanyID
	x.fieldMap["AgentMode"] = x.AgentMode
	x.fieldMap["AgentCaseId"] = x.AgentCaseID
	x.fieldMap["PaymentMethod"] = x.PaymentMethod
}

func (x xChannel) clone(db *gorm.DB) xChannel {
	x.xChannelDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xChannel) replaceDB(db *gorm.DB) xChannel {
	x.xChannelDo.ReplaceDB(db)
	return x
}

type xChannelDo struct{ gen.DO }

func (x xChannelDo) Debug() *xChannelDo {
	return x.withDO(x.DO.Debug())
}

func (x xChannelDo) WithContext(ctx context.Context) *xChannelDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xChannelDo) ReadDB() *xChannelDo {
	return x.Clauses(dbresolver.Read)
}

func (x xChannelDo) WriteDB() *xChannelDo {
	return x.Clauses(dbresolver.Write)
}

func (x xChannelDo) Session(config *gorm.Session) *xChannelDo {
	return x.withDO(x.DO.Session(config))
}

func (x xChannelDo) Clauses(conds ...clause.Expression) *xChannelDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xChannelDo) Returning(value interface{}, columns ...string) *xChannelDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xChannelDo) Not(conds ...gen.Condition) *xChannelDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xChannelDo) Or(conds ...gen.Condition) *xChannelDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xChannelDo) Select(conds ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xChannelDo) Where(conds ...gen.Condition) *xChannelDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xChannelDo) Order(conds ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xChannelDo) Distinct(cols ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xChannelDo) Omit(cols ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xChannelDo) Join(table schema.Tabler, on ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xChannelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xChannelDo) RightJoin(table schema.Tabler, on ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xChannelDo) Group(cols ...field.Expr) *xChannelDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xChannelDo) Having(conds ...gen.Condition) *xChannelDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xChannelDo) Limit(limit int) *xChannelDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xChannelDo) Offset(offset int) *xChannelDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xChannelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xChannelDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xChannelDo) Unscoped() *xChannelDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xChannelDo) Create(values ...*model.XChannel) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xChannelDo) CreateInBatches(values []*model.XChannel, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xChannelDo) Save(values ...*model.XChannel) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xChannelDo) First() (*model.XChannel, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannel), nil
	}
}

func (x xChannelDo) Take() (*model.XChannel, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannel), nil
	}
}

func (x xChannelDo) Last() (*model.XChannel, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannel), nil
	}
}

func (x xChannelDo) Find() ([]*model.XChannel, error) {
	result, err := x.DO.Find()
	return result.([]*model.XChannel), err
}

func (x xChannelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XChannel, err error) {
	buf := make([]*model.XChannel, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xChannelDo) FindInBatches(result *[]*model.XChannel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xChannelDo) Attrs(attrs ...field.AssignExpr) *xChannelDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xChannelDo) Assign(attrs ...field.AssignExpr) *xChannelDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xChannelDo) Joins(fields ...field.RelationField) *xChannelDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xChannelDo) Preload(fields ...field.RelationField) *xChannelDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xChannelDo) FirstOrInit() (*model.XChannel, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannel), nil
	}
}

func (x xChannelDo) FirstOrCreate() (*model.XChannel, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannel), nil
	}
}

func (x xChannelDo) FindByPage(offset int, limit int) (result []*model.XChannel, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xChannelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xChannelDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xChannelDo) Delete(models ...*model.XChannel) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xChannelDo) withDO(do gen.Dao) *xChannelDo {
	x.DO = *do.(*gen.DO)
	return x
}
