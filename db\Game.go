package db

import (
	"fmt"
	"xserver/server"
)

type Game struct {
	Id                 int     `gorm:"column:Id"`                 //
	SellerId           int     `gorm:"column:SellerId"`           //运营商
	GameId             int     `gorm:"column:GameId"`             //游戏id
	RoomLevel          int     `gorm:"column:RoomLevel"`          //房间等级
	GameName           string  `gorm:"column:GameName"`           //游戏名称
	RoomName           string  `gorm:"column:RoomName"`           //房间名称
	Address            string  `gorm:"column:Address"`            //投注地址
	RewardRateEx       string  `gorm:"column:RewardRateEx"`       //特殊游戏的赔率,哈希牛牛,幸运哈希等
	UsdtLimitMin       int     `gorm:"column:UsdtLimitMin"`       //最小下注usdt
	UsdtLimitMax       int     `gorm:"column:UsdtLimitMax"`       //最大下注usdt
	UsdtBscLimitMin    int     `gorm:"column:UsdtBscLimitMin"`    //最小下注usdtbsc
	UsdtBscLimitMax    int     `gorm:"column:UsdtBscLimitMax"`    //最大下注usdtbsc
	UsdtEthLimitMin    int     `gorm:"column:UsdtEthLimitMin"`    //最小下注usdteth
	UsdtEthLimitMax    int     `gorm:"column:UsdtEthLimitMax"`    //最大下注usdteth
	TrxLimitMin        int     `gorm:"column:TrxLimitMin"`        //最少下注trx
	TrxLimitMax        int     `gorm:"column:TrxLimitMax"`        //最大下注trx
	BackFeeRate        float64 `gorm:"column:BackFeeRate"`        //返还费率
	RewardDownRole     string  `gorm:"column:RewardDownRole"`     //降赔规则
	StopRewardDownRole string  `gorm:"column:StopRewardDownRole"` //停止降赔规则
	State              int     `gorm:"column:State"`              //状态 1启用 2禁用
	LiuSuiType         int     `gorm:"column:LiuSuiType"`         //流水算法 1单边 2双边 3输赢绝对值
	FenChengRate       float64 `gorm:"column:FenChengRate"`
	FeeRate            float64 `gorm:"column:FeeRate"`
	AmountTrx          float64 `gorm:"column:AmountTrx"`
	AmountUsdt         float64 `gorm:"column:AmountUsdt"`
}

func (*Game) TableName() string {
	return "x_game"
}

func Game_Page_Data(Page int, PageSize int, SellerId int, GameId int, State int) (int, []Game) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := Game{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "State", "=", State, 0)
	server.Db().AddWhere(&sql, &params, "and", "GameId", "=", GameId, 0)
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []Game{}
	}
	result := []Game{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}

func Game_All_Address() []string {
	data := Game{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	result := []Game{}
	dbtable.Select("Address").Where("state = 1").Find(&result)
	returndata := []string{}
	for _, v := range result {
		returndata = append(returndata, v.Address)
	}
	return returndata
}
