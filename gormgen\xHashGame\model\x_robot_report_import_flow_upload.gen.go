// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotReportImportFlowUpload = "x_robot_report_import_flow_upload"

// XRobotReportImportFlowUpload mapped from table <x_robot_report_import_flow_upload>
type XRobotReportImportFlowUpload struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`               // pk
	DateTime       time.Time `gorm:"column:date_time;not null;comment:日期" json:"date_time"`                      // 日期
	SellerID       int32     `gorm:"column:seller_id;not null;comment:经销商ID" json:"seller_id"`                   // 经销商ID
	ChannelID      int32     `gorm:"column:channel_id;not null;comment:渠道ID" json:"channel_id"`                  // 渠道ID
	SendCnt        int32     `gorm:"column:send_cnt;comment:发送数量" json:"send_cnt"`                               // 发送数量
	SmallAccontCnt int32     `gorm:"column:small_accont_cnt;comment:使用小号数量" json:"small_accont_cnt"`             // 使用小号数量
	AccountPrice   float64   `gorm:"column:account_price;default:0.00;comment:号单价" json:"account_price"`         // 号单价
	SendCostPrice  float64   `gorm:"column:send_cost_price;default:0.00;comment:发送总费用" json:"send_cost_price"`   // 发送总费用
	SendAvgCnt     float64   `gorm:"column:send_avg_cnt;default:0.00;comment:平均发送数量" json:"send_avg_cnt"`        // 平均发送数量
	CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建" json:"create_time"` // 创建
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新" json:"update_time"` // 更新
}

// TableName XRobotReportImportFlowUpload's table name
func (*XRobotReportImportFlowUpload) TableName() string {
	return TableNameXRobotReportImportFlowUpload
}
