package controller

import (
	"errors"
	"fmt"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type TgAccountController struct{}

func (c *TgAccountController) Init() {
	group := server.Http().NewGroup("/api/tgAccount")
	{
		group.Post("/creat", c.creat)
		group.Post("/list", c.list)
		group.Post("/kefu_list", c.kefu_list)
		group.Post("/info", c.info)
		group.Post("/update", c.update)
		group.Post("/update_status", c.update_status)
		group.Post("/delete", c.delete)
	}
}

func (c *TgAccountController) creat(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgAccount
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG账号管理", "增", "TG账号创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	reqdata.OperUserAccount = token.Account

	dao := server.DaoxHashGame().XTgAccount
	db := dao.WithContext(ctx.Gin())
	err := db.Create(&reqdata.XTgAccount)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgAccountController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		model.XTgAccount
	}
	type Result struct {
		model.XTgAccount
		SellerName string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG账号管理", "查", "TG账号查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgAccount
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}
	if reqdata.KefuAccount != "" {
		db = db.Where(dao.KefuAccount.Eq(reqdata.KefuAccount))
	}
	if reqdata.Phone != "" {
		db = db.Where(dao.Phone.Eq(reqdata.Phone))
	}
	if reqdata.TgUsername != "" {
		db = db.Where(dao.TgUsername.Eq(reqdata.TgUsername))
	}
	if reqdata.Status != 0 {
		db = db.Where(dao.Status.Eq(reqdata.Status))
	}
	var list []Result
	xSeller := server.DaoxHashGame().XSeller
	total, err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Select(dao.ALL, xSeller.SellerName).Order(dao.ID.Desc()).
		ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("list", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TgAccountController) kefu_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID int32
	}
	type Result struct {
		KefuAccount string             `gorm:"column:KefuAccount"`
		CSGroup     string             `gorm:"column:CSGroup"`
		TgAccount   []model.XTgAccount `gorm:"foreignkey:KefuAccount;references:KefuAccount"`
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG账号管理", "查", "TG账号查询")
	if token == nil {
		return
	}

	tableName := server.DaoxHashGame().XTgAccount.TableName()
	admUserTbName := server.DaoxHashGame().AdminUser.TableName()
	db := server.Db().GormDao().Table(tableName)
	db = db.Joins(fmt.Sprintf("left join %s on %s.Account=%s.KefuAccount", admUserTbName, admUserTbName, tableName))

	if reqdata.SellerID > 0 {
		db = db.Where(fmt.Sprintf("%s.SellerID = ?", tableName), reqdata.SellerID)
	}
	var list []Result
	err := db.Where(fmt.Sprintf("%s.Status = 1", tableName)).Distinct("KefuAccount", "CSGroup").Preload("TgAccount").Find(&list).Error
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("list", list)
	ctx.RespOK()
}

func (c *TgAccountController) info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id       int32
		SellerID int32
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG账号管理", "查", "TG账号查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgAccount
	db := dao.WithContext(ctx.Gin())
	xSeller := server.DaoxHashGame().XSeller
	var data struct {
		model.XTgAccount
		SellerName string
	}
	err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Where(dao.ID.Eq(reqdata.Id)).Select(dao.ALL, xSeller.SellerName).
		Scan(&data)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *TgAccountController) update(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgAccount
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG账号管理", "改", "修改TG账号")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	reqdata.OperUserAccount = token.Account

	dao := server.DaoxHashGame().XTgAccount
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).
		Select(dao.Phone, dao.TgUsername, dao.Status, dao.KefuAccount, dao.OperUserAccount).
		Updates(&reqdata.XTgAccount)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	daoBot := server.DaoxHashGame().XTgRobotGuide
	_, err = daoBot.WithContext(ctx.Gin()).
		Where(daoBot.TgAccountID.Eq(reqdata.ID)).
		Or(daoBot.KefuTgUserName.Eq(reqdata.TgUsername)).
		Update(daoBot.KefuTgUserName, reqdata.TgUsername)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgAccountController) update_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgAccount
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG账号管理", "改", "修改TG账号状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.OperUserAccount = token.Account

	dao := server.DaoxHashGame().XTgAccount
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Select(dao.Status, dao.OperUserAccount).Update(dao.Status, reqdata.Status)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgAccountController) delete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgAccount
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG账号管理", "删", "删除TG账号")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTgAccount
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Delete()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}
