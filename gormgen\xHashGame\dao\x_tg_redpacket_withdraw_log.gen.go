// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgRedpacketWithdrawLog(db *gorm.DB, opts ...gen.DOOption) xTgRedpacketWithdrawLog {
	_xTgRedpacketWithdrawLog := xTgRedpacketWithdrawLog{}

	_xTgRedpacketWithdrawLog.xTgRedpacketWithdrawLogDo.UseDB(db, opts...)
	_xTgRedpacketWithdrawLog.xTgRedpacketWithdrawLogDo.UseModel(&model.XTgRedpacketWithdrawLog{})

	tableName := _xTgRedpacketWithdrawLog.xTgRedpacketWithdrawLogDo.TableName()
	_xTgRedpacketWithdrawLog.ALL = field.NewAsterisk(tableName)
	_xTgRedpacketWithdrawLog.ID = field.NewInt32(tableName, "Id")
	_xTgRedpacketWithdrawLog.TgUserID = field.NewInt64(tableName, "TgUserId")
	_xTgRedpacketWithdrawLog.TgUsername = field.NewString(tableName, "TgUsername")
	_xTgRedpacketWithdrawLog.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRedpacketWithdrawLog.TgID = field.NewInt32(tableName, "TgId")
	_xTgRedpacketWithdrawLog.WithdrawAmount = field.NewFloat64(tableName, "WithdrawAmount")
	_xTgRedpacketWithdrawLog.OrderNumber = field.NewString(tableName, "OrderNumber")
	_xTgRedpacketWithdrawLog.PaltformUserID = field.NewInt64(tableName, "PaltformUserId")
	_xTgRedpacketWithdrawLog.State = field.NewInt32(tableName, "State")
	_xTgRedpacketWithdrawLog.OperatorID = field.NewInt32(tableName, "OperatorId")
	_xTgRedpacketWithdrawLog.AuditAt = field.NewTime(tableName, "AuditAt")
	_xTgRedpacketWithdrawLog.CreatedAt = field.NewTime(tableName, "CreatedAt")
	_xTgRedpacketWithdrawLog.UpdatedAt = field.NewTime(tableName, "UpdatedAt")

	_xTgRedpacketWithdrawLog.fillFieldMap()

	return _xTgRedpacketWithdrawLog
}

type xTgRedpacketWithdrawLog struct {
	xTgRedpacketWithdrawLogDo xTgRedpacketWithdrawLogDo

	ALL            field.Asterisk
	ID             field.Int32
	TgUserID       field.Int64
	TgUsername     field.String
	SellerID       field.Int32   // 运营商id
	TgID           field.Int32   // 后台机器人id
	WithdrawAmount field.Float64 // 提现金额
	OrderNumber    field.String  // 订单号
	PaltformUserID field.Int64   // 平台用户ID
	State          field.Int32   // 1:未审核 2:通过 3:拒绝 4:没收
	OperatorID     field.Int32   // 操作人
	AuditAt        field.Time    // 审核时间
	CreatedAt      field.Time
	UpdatedAt      field.Time

	fieldMap map[string]field.Expr
}

func (x xTgRedpacketWithdrawLog) Table(newTableName string) *xTgRedpacketWithdrawLog {
	x.xTgRedpacketWithdrawLogDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRedpacketWithdrawLog) As(alias string) *xTgRedpacketWithdrawLog {
	x.xTgRedpacketWithdrawLogDo.DO = *(x.xTgRedpacketWithdrawLogDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRedpacketWithdrawLog) updateTableName(table string) *xTgRedpacketWithdrawLog {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.TgUserID = field.NewInt64(table, "TgUserId")
	x.TgUsername = field.NewString(table, "TgUsername")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.TgID = field.NewInt32(table, "TgId")
	x.WithdrawAmount = field.NewFloat64(table, "WithdrawAmount")
	x.OrderNumber = field.NewString(table, "OrderNumber")
	x.PaltformUserID = field.NewInt64(table, "PaltformUserId")
	x.State = field.NewInt32(table, "State")
	x.OperatorID = field.NewInt32(table, "OperatorId")
	x.AuditAt = field.NewTime(table, "AuditAt")
	x.CreatedAt = field.NewTime(table, "CreatedAt")
	x.UpdatedAt = field.NewTime(table, "UpdatedAt")

	x.fillFieldMap()

	return x
}

func (x *xTgRedpacketWithdrawLog) WithContext(ctx context.Context) *xTgRedpacketWithdrawLogDo {
	return x.xTgRedpacketWithdrawLogDo.WithContext(ctx)
}

func (x xTgRedpacketWithdrawLog) TableName() string { return x.xTgRedpacketWithdrawLogDo.TableName() }

func (x xTgRedpacketWithdrawLog) Alias() string { return x.xTgRedpacketWithdrawLogDo.Alias() }

func (x xTgRedpacketWithdrawLog) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRedpacketWithdrawLogDo.Columns(cols...)
}

func (x *xTgRedpacketWithdrawLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRedpacketWithdrawLog) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["TgUserId"] = x.TgUserID
	x.fieldMap["TgUsername"] = x.TgUsername
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["TgId"] = x.TgID
	x.fieldMap["WithdrawAmount"] = x.WithdrawAmount
	x.fieldMap["OrderNumber"] = x.OrderNumber
	x.fieldMap["PaltformUserId"] = x.PaltformUserID
	x.fieldMap["State"] = x.State
	x.fieldMap["OperatorId"] = x.OperatorID
	x.fieldMap["AuditAt"] = x.AuditAt
	x.fieldMap["CreatedAt"] = x.CreatedAt
	x.fieldMap["UpdatedAt"] = x.UpdatedAt
}

func (x xTgRedpacketWithdrawLog) clone(db *gorm.DB) xTgRedpacketWithdrawLog {
	x.xTgRedpacketWithdrawLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRedpacketWithdrawLog) replaceDB(db *gorm.DB) xTgRedpacketWithdrawLog {
	x.xTgRedpacketWithdrawLogDo.ReplaceDB(db)
	return x
}

type xTgRedpacketWithdrawLogDo struct{ gen.DO }

func (x xTgRedpacketWithdrawLogDo) Debug() *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRedpacketWithdrawLogDo) WithContext(ctx context.Context) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRedpacketWithdrawLogDo) ReadDB() *xTgRedpacketWithdrawLogDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRedpacketWithdrawLogDo) WriteDB() *xTgRedpacketWithdrawLogDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRedpacketWithdrawLogDo) Session(config *gorm.Session) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRedpacketWithdrawLogDo) Clauses(conds ...clause.Expression) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRedpacketWithdrawLogDo) Returning(value interface{}, columns ...string) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRedpacketWithdrawLogDo) Not(conds ...gen.Condition) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRedpacketWithdrawLogDo) Or(conds ...gen.Condition) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRedpacketWithdrawLogDo) Select(conds ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRedpacketWithdrawLogDo) Where(conds ...gen.Condition) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRedpacketWithdrawLogDo) Order(conds ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRedpacketWithdrawLogDo) Distinct(cols ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRedpacketWithdrawLogDo) Omit(cols ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRedpacketWithdrawLogDo) Join(table schema.Tabler, on ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRedpacketWithdrawLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRedpacketWithdrawLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRedpacketWithdrawLogDo) Group(cols ...field.Expr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRedpacketWithdrawLogDo) Having(conds ...gen.Condition) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRedpacketWithdrawLogDo) Limit(limit int) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRedpacketWithdrawLogDo) Offset(offset int) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRedpacketWithdrawLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRedpacketWithdrawLogDo) Unscoped() *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRedpacketWithdrawLogDo) Create(values ...*model.XTgRedpacketWithdrawLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRedpacketWithdrawLogDo) CreateInBatches(values []*model.XTgRedpacketWithdrawLog, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRedpacketWithdrawLogDo) Save(values ...*model.XTgRedpacketWithdrawLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRedpacketWithdrawLogDo) First() (*model.XTgRedpacketWithdrawLog, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketWithdrawLog), nil
	}
}

func (x xTgRedpacketWithdrawLogDo) Take() (*model.XTgRedpacketWithdrawLog, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketWithdrawLog), nil
	}
}

func (x xTgRedpacketWithdrawLogDo) Last() (*model.XTgRedpacketWithdrawLog, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketWithdrawLog), nil
	}
}

func (x xTgRedpacketWithdrawLogDo) Find() ([]*model.XTgRedpacketWithdrawLog, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRedpacketWithdrawLog), err
}

func (x xTgRedpacketWithdrawLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRedpacketWithdrawLog, err error) {
	buf := make([]*model.XTgRedpacketWithdrawLog, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRedpacketWithdrawLogDo) FindInBatches(result *[]*model.XTgRedpacketWithdrawLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRedpacketWithdrawLogDo) Attrs(attrs ...field.AssignExpr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRedpacketWithdrawLogDo) Assign(attrs ...field.AssignExpr) *xTgRedpacketWithdrawLogDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRedpacketWithdrawLogDo) Joins(fields ...field.RelationField) *xTgRedpacketWithdrawLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRedpacketWithdrawLogDo) Preload(fields ...field.RelationField) *xTgRedpacketWithdrawLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRedpacketWithdrawLogDo) FirstOrInit() (*model.XTgRedpacketWithdrawLog, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketWithdrawLog), nil
	}
}

func (x xTgRedpacketWithdrawLogDo) FirstOrCreate() (*model.XTgRedpacketWithdrawLog, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketWithdrawLog), nil
	}
}

func (x xTgRedpacketWithdrawLogDo) FindByPage(offset int, limit int) (result []*model.XTgRedpacketWithdrawLog, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRedpacketWithdrawLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRedpacketWithdrawLogDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRedpacketWithdrawLogDo) Delete(models ...*model.XTgRedpacketWithdrawLog) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRedpacketWithdrawLogDo) withDO(do gen.Dao) *xTgRedpacketWithdrawLogDo {
	x.DO = *do.(*gen.DO)
	return x
}
