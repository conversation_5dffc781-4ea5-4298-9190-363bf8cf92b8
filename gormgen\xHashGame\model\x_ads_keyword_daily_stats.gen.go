// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsKeywordDailyStat = "x_ads_keyword_daily_stats"

// XAdsKeywordDailyStat 关键词搜索每日统计表
type XAdsKeywordDailyStat struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                        // 主键ID
	SellerID   int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                       // 运营商ID
	ChannelID  int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                      // 渠道ID
	TopAgentID int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                // 顶级代理ID
	KeyWord    string    `gorm:"column:key_word;not null;comment:搜索关键词" json:"key_word"`                                // 搜索关键词
	StatDate   time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                               // 统计日期
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XAdsKeywordDailyStat's table name
func (*XAdsKeywordDailyStat) TableName() string {
	return TableNameXAdsKeywordDailyStat
}
