package balance

import (
	"github.com/shopspring/decimal"
	"xserver/utils"
)

type AmountChargeLog struct {
	Id           int             `json:"Id" gorm:"column:Id"`                     //id
	UserId       int             `json:"UserId" gorm:"column:UserId"`             //id
	BeforeAmount decimal.Decimal `json:"BeforeAmount" gorm:"column:BeforeAmount"` //变化前余额
	Amount       decimal.Decimal `json:"Amount" gorm:"column:Amount"`             //变化值
	AfterAmount  decimal.Decimal `json:"AfterAmount" gorm:"column:AfterAmount"`   //变化后余额
	Reason       int             `json:"Reason" gorm:"column:Reason"`             //变化原因
	CreateTime   utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`     //变化时间
	Memo         string          `json:"Memo" gorm:"column:Memo"`                 //备注
	SellerId     int             `json:"SellerId" gorm:"column:SellerId"`         //
	ChannelId    int             `json:"ChannelId" gorm:"column:ChannelId"`       //
}

type CaiJinDetail struct {
	Id         int             `json:"Id" gorm:"column:Id"`                 //id
	UserId     int             `json:"UserId" gorm:"column:UserId"`         //UserId
	SType      string          `json:"SType" gorm:"column:SType"`           //SType
	Symbol     string          `json:"Symbol" gorm:"column:Symbol"`         //Symbol
	Amount     decimal.Decimal `json:"Amount" gorm:"column:Amount"`         //Amount
	CSGroup    string          `json:"CSGroup" gorm:"column:CSGroup"`       //CSGroup
	CSId       string          `json:"CSId" gorm:"column:CSId"`             //CSId
	CreateTime utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"` //
	TopAgentId int             `json:"TopAgentId" gorm:"column:TopAgentId"` //
}
