// Package controller 实现了活动相关的控制器功能
package controller

// 注意：在 active_define_mod 函数中，当处理特殊活动类型时，如果已经调用了特定的处理函数并且该函数内部已经发送了响应，
// 则应该在调用后立即返回，不要继续执行后续代码，否则会导致发送两次响应。

import (
	"context"
	"encoding/json"
	"fmt"
	"path"
	"sort"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/active"
	activedb "xserver/db/active"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"gorm.io/gen"
	daoGrom "gorm.io/gorm"
)

// ActiveController 活动管理控制器
type ActiveController struct {
}

// Init 初始化活动管理相关的路由
func (c *ActiveController) Init() {
	group := server.Http().NewGroup("/api/active")
	{
		//活动插图管理
		group.Post("/list", c.list)                         // 活动列表
		group.Post("/delete", c.delete)                     // 删除活动
		group.Post("/modify", c.modify)                     // 修改活动
		group.Post("/add", c.add)                           // 添加活动
		group.Post("/queuedmsg_list", c.queuedmsg_list)     // 跑马灯消息列表
		group.Post("/queuedmsg_add", c.queuedmsg_add)       // 添加跑马灯消息
		group.Post("/queuedmsg_modify", c.queuedmsg_modify) // 修改跑马灯消息
		group.Post("/queuedmsg_delete", c.queuedmsg_delete) // 删除跑马灯消息
		group.Post("/reward", c.reward)                     // 活动奖励列表
		group.Post("/reward_audit", c.reward_audit)         // 审核活动奖励
		group.Post("/report", c.report)                     // 活动报表

		//活动列表-新
		group.Post("/active_define_list", c.active_define_list) // 获取活动定义列表
		group.Post("/active_define_mod", c.active_define_mod)   // 修改活动定义
		// group.Post("/active_define_add", c.active_define_add)   // 添加活动定义
		group.Post("/active_define_info", c.active_define_info) // 获取活动定义详情

		//活动详情
		group.Post("/active_info", c.active_info)                   // 获取活动信息
		group.Post("/active_info_add", c.active_info_add)           // 添加活动信息
		group.Post("/active_info_del", c.active_info_del)           // 删除活动信息
		group.Post("/active_info_mod", c.active_info_mod)           // 修改活动信息
		group.Post("/generate_redeem_code", c.generate_redeem_code) // 生成兑换码
		group.Post("/redeem_code_record", c.redeem_code_record)     // 查询兑换码领取记录
		//新活动审核
		group.Post("/reward_audit_list", c.reward_audit_list)     // 活动奖励审核列表
		group.Post("/reward_audit_audit", c.reward_audit_audit)   // 审核活动奖励
		group.Post("/reward_audit_report", c.reward_audit_report) // 活动奖励审核报表
		//首页轮播图
		group.Post("/home_carousel_list", c.home_carousel_list)  // 首页轮播图列表
		group.Post("/home_carousel_add", c.home_carousel_add)    // 添加首页轮播图
		group.Post("/home_carousel_del", c.home_carousel_del)    // 删除首页轮播图
		group.Post("/home_carousel_mod", c.home_carousel_mod)    // 修改首页轮播图
		group.Post("/get_active_name_list", c.GetActiveNameList) // 获取活动名称列表

		group.Post("/home_carousel_list_v2", c.home_carousel_list_v2) // 首页轮播图列表V2
		group.Post("/home_carousel_add_v2", c.home_carousel_add_v2)   // 添加首页轮播图V2
		group.Post("/home_carousel_del_v2", c.home_carousel_del_v2)   // 删除首页轮播图V2
		group.Post("/home_carousel_open_v2", c.home_carousel_open_v2) // 开启/关闭首页轮播图V2
	}
}

// list 获取活动列表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int // 商户ID
		Page      int // 页码
		PageSize  int // 每页数量
		ChannelId int // 渠道ID
		State     int // 状态
		LangId    int // 语言ID
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动配置", "查", "查看活动")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "State", "=", reqdata.State, 0)
	where.Add("and", "LangId", "=", reqdata.LangId, 0)
	total, data := server.Db().Table("x_active").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("url", server.ImageUrl())
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()
}

// modify 修改活动信息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int     // 商户ID
		Id         int     `validate:"required"` // 活动ID
		State      int     // 状态 1启用,2禁用
		Sort       int     // 排序数字越大,越靠前
		Title      string  // 标题
		TitleImg   string  // 列表图片
		Content    string  // 内容
		ContentImg string  // 内容图片
		StartTime  int64   // 活动开始时间
		EndTime    int64   // 活动结束时间
		Memo       string  // 备注
		Amount     float64 // 金额
		BtnState   int     // 按钮状态
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "活动管理", "活动配置", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	sql := "update x_active set State = ?,Sort = ?,Title = ?,TitleImg = ?,Content = ?,ContentImg = ?,StartTime = ?,Endtime = ?,memo=?,Amount = ?,BtnState = ? where Id = ?"
	StartTime := abugo.TimeStampToLocalTime(reqdata.StartTime)
	EndTime := abugo.TimeStampToLocalTime(reqdata.EndTime)
	if len(StartTime) == 0 {
		StartTime = time.Now().Format("2006-01-02 15:04:05")
	}
	if len(EndTime) == 0 {
		EndTime = time.Now().Format("2006-01-02 15:04:05")
	}
	server.Db().QueryNoResult(sql, reqdata.State, reqdata.Sort, reqdata.Title, reqdata.TitleImg, reqdata.Content, reqdata.ContentImg, StartTime, EndTime, reqdata.Memo, reqdata.Amount, reqdata.BtnState, reqdata.Id)
	ctx.RespOK()
	server.WriteAdminLog("修改活动", ctx, reqdata)
}

// add 添加新活动
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int     // 商户ID
		State      int     `validate:"required"` // 状态 1启用,2禁用
		Sort       int     `validate:"required"` // 排序数字越大,越靠前
		Title      string  `validate:"required"` // 标题
		TitleImg   string  // 列表图片
		Content    string  // 内容
		ContentImg string  // 内容图片
		StartTime  int64   `validate:"required"` // 活动开始时间
		EndTime    int64   `validate:"required"` // 活动结束时间
		Memo       string  // 备注
		ChannelId  int     `validate:"required"` // 渠道ID
		LangId     int     `validate:"required"` // 语言ID
		Amount     float64 // 金额
		BtnState   int     // 按钮状态
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动配置", "增", "添加活动")
	if token == nil {
		return
	}
	StartTime := abugo.TimeStampToLocalTime(reqdata.StartTime)
	EndTime := abugo.TimeStampToLocalTime(reqdata.EndTime)
	if len(StartTime) == 0 {
		StartTime = time.Now().Format("2006-01-02 15:04:05")
	}
	if len(EndTime) == 0 {
		EndTime = time.Now().Format("2006-01-02 15:04:05")
	}
	sql := "insert into x_active(SellerId,State,Sort,Title,TitleImg,Content,ContentImg,StartTime,Endtime,Memo,ChannelId,LangId,Amount,BtnState)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	server.Db().QueryNoResult(sql, reqdata.SellerId, reqdata.State, reqdata.Sort, reqdata.Title, reqdata.TitleImg, reqdata.Content, reqdata.ContentImg, StartTime, EndTime, reqdata.Memo, reqdata.ChannelId, reqdata.LangId, reqdata.Amount, reqdata.BtnState)
	ctx.RespOK()
}

// delete 删除活动
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int // 商户ID
		Id       int `validate:"required"` // 活动ID
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "活动管理", "活动配置", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	sql := "delete from x_active where id = ?"
	server.Db().QueryNoResult(sql, reqdata.Id)
	ctx.RespOK()
	server.WriteAdminLog("删除活动", ctx, reqdata)
}

// queuedmsg_list 获取跑马灯消息列表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) queuedmsg_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int // 商户ID
		ChannelId int // 渠道ID
		LangId    int // 语言ID
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "跑马灯", "查", "查看跑马灯")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "LangId", "=", reqdata.LangId, 0)
	pdata, _ := server.Db().Table("x_queuedmsg").Where(where).OrderBy("sort desc").GetList()
	ctx.RespOK(*pdata)
}

// queuedmsg_add 添加跑马灯消息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) queuedmsg_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int    // 商户ID
		ChannelId int    `validate:"required"` // 渠道ID
		Content   string `validate:"required"` // 消息内容
		Sort      int    `validate:"required"` // 排序
		State     int    `validate:"required"` // 状态
		LangId    int    `validate:"required"` // 语言ID
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "跑马灯", "增", "添加跑马灯")
	if token == nil {
		return
	}
	sql := "insert into x_queuedmsg(SellerId,ChannelId,Content,Sort,State,LangId)values(?,?,?,?,?,?)"
	server.Db().Query(sql, []interface{}{reqdata.SellerId, reqdata.ChannelId, reqdata.Content, reqdata.Sort, reqdata.State, reqdata.LangId})
	ctx.RespOK()
}

// queuedmsg_modify 修改跑马灯消息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) queuedmsg_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id        int    `validate:"required"` // 消息ID
		SellerId  int    // 商户ID
		ChannelId int    `validate:"required"` // 渠道ID
		Content   string `validate:"required"` // 消息内容
		Sort      int    `validate:"required"` // 排序
		State     int    `validate:"required"` // 状态
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "跑马灯", "改", "编辑跑马灯")
	if token == nil {
		return
	}
	sql := "update x_queuedmsg set Content = ?,Sort = ?,State = ? where id = ?"
	server.Db().Query(sql, []interface{}{reqdata.Content, reqdata.Sort, reqdata.State, reqdata.Id})
	ctx.RespOK()
}

// queuedmsg_delete 删除跑马灯消息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) queuedmsg_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int // 商户ID
		Id       int `validate:"required"` // 消息ID
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "跑马灯", "删", "删除跑马灯")
	if token == nil {
		return
	}
	sql := "delete from x_queuedmsg where id = ?"
	server.Db().Query(sql, []interface{}{reqdata.Id})
	ctx.RespOK()
}

// reward 获取活动奖励列表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) reward(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int    // 页码
		PageSize   int    // 每页数量
		SellerId   int    // 商户ID
		ChannelId  int    // 渠道ID
		UserId     int    // 用户ID
		StartTime  int64  // 开始时间
		EndTime    int64  // 结束时间
		ActiveName string // 活动名称
		State      int    // 状态
		Export     int    // 0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动领取", "查", "查看活动领取")
	if token == nil {
		return
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_活动领取_%s", time.Now().Format("20060102150405")))
	//var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 5000000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("Id", "订单号")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("ActiveName", "活动名称")
		xlsx.SetTitle("Amount", "申领金额")
		xlsx.SetTitle("State", "申领状态")
		xlsx.SetTitle("CreateTime", "申请时间")
		xlsx.SetTitle("AuditTime", "审核时间")
		xlsx.SetTitle("AuditMemo", "备注")
		xlsx.SetTitleStyle()
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "ActiveName", "=", reqdata.ActiveName, "")
	where.Add("and", "State", "=", reqdata.State, 0)
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	total, presult := server.Db().Table("x_active_reward").OrderBy("id desc").Where(where).PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("data", presult)
		ctx.Put("total", total)
	} else {
		for i := 0; i < len(*presult); i++ {
			for k, v := range (*presult)[i] {
				switch k {
				case "ChannelId":
					xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
				case "State":
					switch abugo.GetInt64FromInterface(v) {
					case 1:
						xlsx.SetValue(k, "待审核", int64(i+2))
					case 2:
						xlsx.SetValue(k, "审核拒绝", int64(i+2))
					case 3:
						xlsx.SetValue(k, "审核通过", int64(i+2))
					default:
						xlsx.SetValue(k, "未定义", int64(i+2))
					}
				default:
					xlsx.SetValue(k, v, int64(i+2))
				}
			}
		}
	}

	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("查看活动领取", ctx, reqdata)
}

// reward_audit 审核活动奖励
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) reward_audit(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int     // 商户ID
		Id         int     `validate:"required"` // 奖励ID
		State      int     `validate:"required"` // 审核状态
		Amount     float64 // 奖励金额
		Memo       string  // 备注
		GoogleCode string  // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动领取", "改", "审核活动领取")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	pdata := []interface{}{reqdata.Id, reqdata.Amount, reqdata.State, reqdata.Memo, token.Account}
	presult, err := server.Db().CallProcedure("x_admin_active_reward_audit", pdata...)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(presult) {
		return
	}
	ctx.RespOK()
}

// report 获取活动奖励报表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) report(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int    // 页码
		PageSize   int    // 每页数量
		SellerId   int    // 商户ID
		ChannelId  int    // 渠道ID
		TopAgentId int    // 顶级代理ID
		StartTime  int64  // 开始时间
		ActiveName string // 活动名称
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动领取报表", "查", "查询活动领取报表")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "TopAgentId", "=", reqdata.TopAgentId, -1)
	where.Add("and", "ActiveName", "=", reqdata.ActiveName, "")
	where.Add("and", "RecordDate", "=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	sql, data := where.Sql()
	sqlstr := fmt.Sprint("select sum(GetCount) as GetCount,sum(GetAmount) as GetAmount from x_report_active where ", sql)
	dbr, _ := server.Db().Conn().Query(sqlstr, data...)
	totalresult := abugo.GetResult(dbr)
	if totalresult != nil && len(*totalresult) > 0 {
		ctx.Put("totaldata", (*totalresult)[0])
	}
	total, presult := server.Db().Table("x_report_active").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", presult)
	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("审核活动领取", ctx, reqdata)
}

// active_define_list 获取活动定义列表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) active_define_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int // 商户ID
		ChannelId int `validate:"required"` // 渠道ID
		State     int // 状态
		ActiveId  int // 活动ID
		Page      int // 页码
		PageSize  int // 每页数量
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动列表", "查", "查看活动列表-好")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "State", "=", reqdata.State, 0)
	where.Add("and", "ActiveId", "=", reqdata.ActiveId, 0)

	// 确保分页参数有效
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 20
	}

	logs.Debug("active_define_list 请求参数: %+v", reqdata)

	// 1. 先简单获取总数
	countSql, countParams := where.Sql()
	var totalCount int64
	if len(countSql) > 0 {
		countFullSql := fmt.Sprintf("SELECT COUNT(*) AS Total FROM x_active_define where %s", countSql)
		countResult, err := server.Db().Conn().Query(countFullSql, countParams...)
		if err == nil && countResult.Next() {
			countResult.Scan(&totalCount)
			countResult.Close()
			logs.Debug("active_define_list 总数查询: %d", totalCount)
		}
	}

	// 2. 尝试使用PageDataEx查询
	presult, total := server.Db().Table("x_active_define").Where(where).PageDataEx(reqdata.Page, reqdata.PageSize, "Sort", "ASC")

	logs.Debug("active_define_list 查询结果: 数据条数=%d, 总数=%d", len(*presult), total)

	// 3. 如果PageDataEx查询结果为空但总数大于0，使用简单SQL查询作为备选方案
	if total > 0 && len(*presult) == 0 {
		logs.Warning("active_define_list 分页查询异常: 总数=%d, 但数据为空, 使用简单SQL查询", total)

		// 直接构建SQL查询带分页
		offset := (reqdata.Page - 1) * reqdata.PageSize
		simpleSql := ""
		if len(countSql) > 0 {
			simpleSql = fmt.Sprintf("SELECT * FROM x_active_define WHERE %s ORDER BY Sort ASC LIMIT %d OFFSET %d",
				countSql, reqdata.PageSize, offset)
		} else {
			simpleSql = fmt.Sprintf("SELECT * FROM x_active_define ORDER BY Sort ASC LIMIT %d OFFSET %d",
				reqdata.PageSize, offset)
		}

		logs.Debug("active_define_list 执行简单SQL: %s", simpleSql)
		simpleData, err := server.Db().Query(simpleSql, countParams)
		if err == nil && simpleData != nil {
			presult = simpleData
			total = totalCount // 使用之前查到的总数
			logs.Debug("active_define_list 使用简单查询获取到数据: %d 条", len(*presult))
		} else if err != nil {
			logs.Error("active_define_list 简单查询错误: %v", err)
		}
	}

	if presult != nil {
		for i := 0; i < len(*presult); i++ {
			(*presult)[i]["Url"] = server.ImageUrl()
		}
	}
	ctx.Put("data", presult)
	ctx.Put("total", total) // 添加 total 到响应中
	ctx.RespOK()
	server.WriteAdminLog("查看活动列表-好", ctx, reqdata)
}

// active_define_mod 修改活动定义
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) active_define_mod(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := active.DefineModReq{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动列表", "改", "修改活动列表-好")
	if token == nil {
		return
	}

	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }
	if reqdata.IsUseDetail == 0 {
		reqdata.IsUseDetail = 2
	}
	if reqdata.ActiveId == active.KActiveIdHaXiBreakout || reqdata.ActiveId == active.KActiveIdQipaiBreakout || reqdata.ActiveId == active.KActiveIdDianziBreakout {
		if reqdata.ExtReward < 0 || reqdata.ExtReward > 20000 {
			ctx.RespErrString(true, &errcode, "闯关活动额外奖励金额不正确[0-20000]")
			return
		}
	}
	if reqdata.ActiveId == utils.KActiveIdCiRiSong {
		if reqdata.CiRiConfig != nil {
			if reqdata.CiRiConfig.MinAwardLiuShui < 0 {
				ctx.RespErrString(true, &errcode, "次日送最小领取流水必须大于等于0")
				return
			}
		}
	}
	// 根据活动ID进行不同的参数检查和处理
	switch reqdata.ActiveId {
	case utils.KActiveIdCumulativeWeeklyRecharge:
		err := active.CumulativeWeeklyRechargeCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdSignReward:
		err := active.SignRewardCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdRecommendFriendReward:
		err := active.RecommendFriendRewardCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdDianziMeiZhouSong,
		utils.KActiveIdZhenrenMeiZhouSong,
		utils.KActiveIdQiPaiMeiZhouSong:
		err := active.WeekendBreakThroughConfigParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdHaXiBreakout,
		utils.KActiveIdQipaiBreakout,
		utils.KActiveIdDianziBreakout,
		utils.KActiveIdZhenrenBreakout,
		utils.KActiveIdMidAutumnZhenrenBreakout,
		utils.KActiveIdMidAutumnBreakout,
		utils.KActiveIdCryptoGamesBreakout:
		err := active.BreakThroughConfigParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdEnergyWhitelist:
		err := active.EnergyWhitelistParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdNewFirstDeposit:
		err := active.NewFirstDepositCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdDailyRechargeRebate:
		err := active.DailyRechargeRebateCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdRecommendNewMemberGift:
		err := active.RecommendNewMemberGiftCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdLuckyDice:
		err := active.LuckyDiceBaseCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdBoomingReward:
		err := active.BoomingRewardCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdPointGift:
		err := active.PointGiftCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.KActiveIdWeeklySignActiveReward:
		err := active.WeeklySignActiveRewardCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.RegisterGift:
		// 注册赠送活动
		err := active.RegisterGiftCheckParameter(reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}

		err = active.ProcessRegisterGiftWagerMultiple(&reqdata)
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	case utils.RedeemCodeGift_1003, utils.RedeemCodeGift_1015, utils.RedeemCodeGift_1016, utils.RedeemCodeGift_1017:
		// 将请求数据转换为模型
		defineData := &model.XActiveDefine{
			ID:              int32(reqdata.Id),
			SellerID:        int32(reqdata.SellerId),
			ChannelID:       int32(reqdata.ChannelId),
			ActiveID:        int32(reqdata.ActiveId),
			Memo:            reqdata.Memo,
			AuditType:       int32(reqdata.AuditType),
			State:           int32(reqdata.State),
			Sort:            int32(reqdata.Sort),
			EffectStartTime: reqdata.EffectStartTime,
			EffectEndTime:   reqdata.EffectEndTime,
			Title:           reqdata.Title,
			TitleLang:       reqdata.TitleLang,
			TitleImg:        reqdata.TitleImg,
			TitleImgLang:    reqdata.TitleImgLang,
			TopImg:          reqdata.TopImg,
			TopImgLang:      reqdata.TopImgLang,
			GameType:        reqdata.GameType,
			BaseConfig:      reqdata.BaseConfig,
			Config:          reqdata.Config,
			AwardType:       reqdata.AwardType,
			AwardTab:        reqdata.AwardTab,
			GiftWallet:      reqdata.GiftWallet,
			AwardData:       reqdata.AwardData,
		}

		// 调用兑换码活动的修改函数
		err := active.HandleRedeemCodeMod(ctx, defineData, &errcode)
		if err != nil {
			return
		}
		return
	case utils.SocialMediaFollowGift:
		// 社交媒体关注活动特殊处理
		defineData := &model.XActiveDefine{
			ID:              int32(reqdata.Id),
			SellerID:        int32(reqdata.SellerId),
			ChannelID:       int32(reqdata.ChannelId),
			ActiveID:        int32(reqdata.ActiveId),
			Memo:            reqdata.Memo,
			AuditType:       int32(reqdata.AuditType),
			State:           int32(reqdata.State),
			Sort:            int32(reqdata.Sort),
			EffectStartTime: reqdata.EffectStartTime,
			EffectEndTime:   reqdata.EffectEndTime,
			Title:           reqdata.Title,
			TitleImg:        reqdata.TitleImg,
			TitleImgLang:    reqdata.TitleImgLang,
			TopImg:          reqdata.TopImg,
			TopImgLang:      reqdata.TopImgLang,
			GameType:        reqdata.GameType,
			BaseConfig:      reqdata.BaseConfig,
			Config:          reqdata.Config,
		}

		// 调用社交媒体关注活动的修改函数
		err := active.HandleSocialMediaFollowMod(ctx, defineData, &errcode)
		if err != nil {
			return
		}
	case utils.ChatRoomBonusGift:
		// 聊天室红包活动特殊处理
		defineData := &model.XActiveDefine{
			ID:              int32(reqdata.Id),
			SellerID:        int32(reqdata.SellerId),
			ChannelID:       int32(reqdata.ChannelId),
			ActiveID:        int32(reqdata.ActiveId),
			Memo:            reqdata.Memo,
			AuditType:       int32(reqdata.AuditType),
			State:           int32(reqdata.State),
			Sort:            int32(reqdata.Sort),
			EffectStartTime: reqdata.EffectStartTime,
			EffectEndTime:   reqdata.EffectEndTime,
			Title:           reqdata.Title,
			TitleImg:        reqdata.TitleImg,
			TitleImgLang:    reqdata.TitleImgLang,
			TopImg:          reqdata.TopImg,
			TopImgLang:      reqdata.TopImgLang,
			GameType:        reqdata.GameType,
			BaseConfig:      reqdata.BaseConfig,
			Config:          reqdata.Config,
		}

		// 调用聊天室红包活动的修改函数
		err := active.HandleChatRoomActivityMod(ctx, defineData, &errcode)
		if err != nil {
			return
		}
	case utils.FirstDepositGift, utils.MultipleDepositGift, utils.MagicFirstDepositGift,
		utils.X9FirstDepositGift, utils.X9SecondDepositGift, utils.X9ThirdDepositGift,
		utils.X9FourthDepositGift, utils.X9FifthDepositGift:
		err := active.RechargeCheck(reqdata)
		if ctx.RespErr(err, &errcode) {
			return
		}

		// 使用 UpdateNormalActiveById 函数更新活动，只会更新一条记录
		var tb model.XActiveDefine
		err = utils.Copy(reqdata, &tb)
		if ctx.RespErr(err, &errcode) {
			return
		}
		err = active.UpdateNormalActiveById(&tb)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.RespOK()
		server.WriteAdminLog("修改活动列表-好", ctx, reqdata)
		return
	}

	// 对于只做参数验证的活动，执行通用更新逻辑
	err := active.UpdateActiveDefine(reqdata, time.Now())
	if err != nil {
		errcode := utils.ErrInternal
		ctx.RespErr(err, &errcode)
	} else {
		ctx.RespOK()
		server.WriteAdminLog("修改活动列表-好", ctx, reqdata)
	}
}

// active_define_info 获取活动定义详情
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) active_define_info(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		Id int32 // 活动定义ID
	}{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动列表", "查", "查看活动详情")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XActiveDefine
	tb, err := dao.WithContext(ctx.Gin()).Where(dao.ID.Eq(reqdata.Id)).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK(tb)
}

// active_info 获取活动信息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) active_info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int // 商户ID
		ChannelId int `validate:"required"` // 渠道ID
		ActiveId  int `validate:"required"` // 活动ID
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动列表", "查", "查看活动列表-好")
	if token == nil {
		return
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "ActiveId", "=", reqdata.ActiveId, 0)
	presult, err := server.Db().Table("x_active_info").Where(where).OrderBy("Level ASC").GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.Put("data", presult)

	activeInfo, err := server.Db().Table("x_active_define").Where(where).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if activeInfo != nil {
		(*activeInfo)["Url"] = server.ImageUrl()
	}
	if reqdata.ActiveId == utils.KActiveIdCiRiSong {
		cStr := (*activeInfo)["Config"]

		if cStr != nil && cStr != "" {
			str, ok := cStr.(string)
			if ok {
				ciriConf := activedb.ACiRiConfig{}
				err = json.Unmarshal([]byte(str), &ciriConf)
				if err == nil {
					(*activeInfo)["ACiRiConfig"] = ciriConf
				}
			}
		}
	}
	//获取完成之后删除无用字段
	delete(*activeInfo, "Config")
	ctx.Put("active", activeInfo)
	ctx.RespOK()
	server.WriteAdminLog("查看活动列表-好", ctx, reqdata)
}

// active_info_add 添加活动信息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) active_info_add(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId    int     `validate:"required"`       // 商户ID
		ChannelId   int     `validate:"required"`       // 渠道ID
		ActiveId    int     `validate:"required,min=1"` // 活动ID
		Level       int     // 等级
		LimitValue  float64 `validate:"min=0"` // 限制值
		RewardValue float64 `validate:"min=0"` // 奖励值
		RewardJson  string  // 奖励JSON配置
		GoogleCode  string  // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动列表", "增", "增加活动列表-好")
	if token == nil {
		return
	}

	// 验证 SellerId
	if token.SellerId > 0 && reqdata.SellerId != token.SellerId {
		ctx.RespErrString(true, &errcode, "运营商不正确")
		return
	}

	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	// 1. 先验证活动奖励配置
	isValidStr := active.CheckActiveInfoValid(reqdata.ActiveId, reqdata.LimitValue, reqdata.RewardValue, reqdata.RewardJson)
	if ctx.RespErrString(isValidStr != "", &errcode, isValidStr) {
		return
	}

	// 2. 查询并计算正确的等级
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "ActiveId", "=", reqdata.ActiveId, 0)

	// 添加日志输出查询条件
	logs.Debug("Query params: SellerId=%d, ChannelId=%d, ActiveId=%d",
		reqdata.SellerId, reqdata.ChannelId, reqdata.ActiveId)

	data, err := server.Db().Table("x_active_info").Select("Level").Where(where).OrderBy("Level desc").GetOne()
	if ctx.RespErr(err, &errcode) {
		logs.Error("Database error: %v", err)
		return
	}

	// 计算新的等级
	if data == nil {
		reqdata.Level = 1 // 如果没有记录，从1开始
		logs.Debug("No existing records, setting Level=1")
	} else {
		currentMaxLevel := abugo.GetInt64FromInterface((*data)["Level"])
		reqdata.Level = int(currentMaxLevel + 1)
		logs.Debug("Found existing record, current max Level=%d, new Level=%d",
			currentMaxLevel, reqdata.Level)
	}

	// 3. 使用计算出的等级进行验证（这个验证应该总是通过，因为我们已经确保了等级的连续性）
	err = active.ValidateAddSerial(reqdata.Level, "x_active_info", "Level", where)
	if ctx.RespErr(err, &errcode) {
		logs.Error("Unexpected error in ValidateAddSerial: Level=%d, err=%v", reqdata.Level, err)
		return
	}

	// 4. 插入数据
	_, err = server.Db().Table("x_active_info").Select("*").Insert(gin.H{
		"SellerId":    reqdata.SellerId,
		"ChannelId":   reqdata.ChannelId,
		"ActiveId":    reqdata.ActiveId,
		"Level":       reqdata.Level,
		"LimitValue":  reqdata.LimitValue,
		"RewardValue": reqdata.RewardValue,
		"RewardJson":  reqdata.RewardJson,
	})
	if ctx.RespErr(err, &errcode) {
		return
	}

	ctx.RespOK()
	server.WriteAdminLog("增加活动列表-好", ctx, reqdata)
}

// active_info_del 删除活动信息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) active_info_del(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id         int    `validate:"required"` // 活动信息ID
		SellerId   int    // 商户ID
		ChannelId  int    `validate:"required"`       // 渠道ID
		ActiveId   int    `validate:"required,min=1"` // 活动ID
		Level      int    `validate:"required,min=1"` // 等级
		GoogleCode string // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动列表", "删", "删除活动列表-好")
	if token == nil {
		return
	}
	reqdata.SellerId = token.SellerId
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.ActiveId != active.KActiveIdVipFanShui {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "ActiveId", "=", reqdata.ActiveId, 0)
		err := active.ValidateDelSerial(reqdata.Level, "x_active_info", "Level", where)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	server.Db().Query("delete from x_active_info where SellerId = ? and ChannelId = ? and ActiveId=? and Id = ?", []interface{}{reqdata.SellerId, reqdata.ChannelId, reqdata.ActiveId, reqdata.Id})
	ctx.RespOK()
	server.WriteAdminLog("删除活动列表-好", ctx, reqdata)
}

// active_info_mod 修改活动信息
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) active_info_mod(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id          int     `validate:"required"` // 活动信息ID
		SellerId    int     // 商户ID
		ChannelId   int     `validate:"required"`       // 渠道ID
		ActiveId    int     `validate:"required,min=1"` // 活动ID
		Level       int     `validate:"required,min=1"` // 等级
		LimitValue  float64 `validate:"min=0"`          // 限制值
		RewardValue float64 `validate:"min=0"`          // 奖励值
		RewardJson  string  // 奖励JSON配置
		GoogleCode  string  // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动列表", "改", "修改活动列表-好")
	if token == nil {
		return
	}
	reqdata.SellerId = token.SellerId
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	isValidStr := active.CheckActiveInfoValid(reqdata.ActiveId, reqdata.LimitValue, reqdata.RewardValue, reqdata.RewardJson)
	if ctx.RespErrString(isValidStr != "", &errcode, isValidStr) {
		return
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.Id, 0)
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "ActiveId", "=", reqdata.ActiveId, 0)
	where.Add("and", "Level", "=", reqdata.Level, 0)
	server.Db().Table("x_active_info").Select("*").Where(where).Update(gin.H{
		"LimitValue":  reqdata.LimitValue,
		"RewardValue": reqdata.RewardValue,
		"RewardJson":  reqdata.RewardJson,
	})
	ctx.RespOK()
	server.WriteAdminLog("修改活动列表-好", ctx, reqdata)
}

// reward_audit_list 获取活动奖励审核列表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) reward_audit_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int   // 页码
		PageSize  int   // 每页数量
		SellerId  int   // 商户ID
		ChannelId int   // 渠道ID
		UserId    int   // 用户ID
		StartTime int64 // 开始时间
		EndTime   int64 // 结束时间
		ActiveId  []int // 活动ID列表
		State     int   // 状态
		Export    int   // 0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动领取", "查", "查看活动领取-新")
	if token == nil {
		return
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_活动领取-新_%s", time.Now().Format("20060102150405")))
	//var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("Id", "订单号")
		xlsx.SetTitle("SellerId", "运营商")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("ActiveId", "活动类型")
		xlsx.SetTitle("ActiveLevel", "活动等级")
		xlsx.SetTitle("ActiveMemo", "说明")
		xlsx.SetTitle("RecordDate", "申领日期")
		xlsx.SetTitle("Amount", "活动金")
		xlsx.SetTitle("AmountTrx", "能量补给TRX")
		xlsx.SetTitle("GasFee", "gas费")
		xlsx.SetTitle("FirstRecharge", "首充金额(申请时)")
		xlsx.SetTitle("TotalRecharge", "充值金额(申请时)")
		xlsx.SetTitle("LiuShui", "有效投注(申请时)")
		xlsx.SetTitle("NetWinLoss", "当日净损失")
		xlsx.SetTitle("TxId", "交易哈希")
		xlsx.SetTitle("Address", "地址")
		xlsx.SetTitle("AuditState", "审核状态")
		xlsx.SetTitle("AuditTime", "审核时间")
		xlsx.SetTitle("AuditAccount", "审核账号")
		xlsx.SetTitle("AuditMemo", "审核备注")
		xlsx.SetTitle("CreateTime", "申请时间")
		xlsx.SetTitle("FirstSignTime", "首次签到时间")
		xlsx.SetTitle("TopAgentId", "顶级代理ID")
		xlsx.SetTitle("VipLevel", "VIP等级")
		xlsx.SetTitle("TotalWinLoss", "当日盈亏")
		xlsx.SetTitle("LastSignTime", "最后签到时间")
		xlsx.SetTitle("InviteRewardUsers", "领取额外奖励推荐人数(申请时)")
		xlsx.SetTitleStyle()
	}

	ids := ""
	for i := 0; i < len(reqdata.ActiveId); i++ {
		if len(ids) == 0 {
			ids += "("
		}
		ids += fmt.Sprintf("%v,", reqdata.ActiveId[i])
	}
	if len(ids) > 0 {
		ids = ids[:len(ids)-1]
		ids += ")"
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "ActiveId", "in", ids, "")
	where.Add("and", "AuditState", "=", reqdata.State, 0)
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	// 先查询奖励审核数据
	presult, total := server.Db().Table("x_active_reward_audit").Where(where).PageDataEx(reqdata.Page, reqdata.PageSize, "id", "desc")

	// 为每条记录添加用户的顶级代理ID和VIP等级
	if presult != nil {
		for i := range *presult {
			userId := int(abugo.GetInt64FromInterface((*presult)[i]["UserId"]))
			if userId > 0 {
				// 查询用户的顶级代理ID
				userWhere := abugo.AbuDbWhere{}
				userWhere.Add("and", "UserId", "=", userId, 0)
				userData, err := server.Db().Table("x_user").Select("TopAgentId").Where(userWhere).GetOne()
				if err == nil && userData != nil {
					(*presult)[i]["TopAgentId"] = (*userData)["TopAgentId"]
				} else {
					(*presult)[i]["TopAgentId"] = 0
				}

				// 查询用户的VIP等级
				vipWhere := abugo.AbuDbWhere{}
				vipWhere.Add("and", "UserId", "=", userId, 0)
				vipData, err := server.Db().Table("x_vip_info").Select("VipLevel").Where(vipWhere).GetOne()
				if err == nil && vipData != nil {
					(*presult)[i]["VipLevel"] = (*vipData)["VipLevel"]
				} else {
					(*presult)[i]["VipLevel"] = 0
				}

				// 查询用户的盈亏（使用GORM方式）
				var totalWinLoss float64 = 0

				// 查询第三方游戏数据
				type GameResult struct {
					BetAmount float64
					WinAmount float64
				}
				var thirdResult GameResult
				err = server.Db().Gorm().Table("x_custom_third").
					Select("IFNULL(SUM(BetAmount), 0) AS bet_amount, IFNULL(SUM(WinAmount), 0) AS win_amount").
					Where("UserId = ?", userId).
					Scan(&thirdResult).Error

				// 查询自营游戏数据
				var daillyResult GameResult
				if err == nil {
					err = server.Db().Gorm().Table("x_custom_dailly").
						Select("IFNULL(SUM(BetAmount), 0) AS bet_amount, IFNULL(SUM(RewardAmount), 0) AS win_amount").
						Where("UserId = ? AND IsTest = ? AND IsGameAddress = ?", userId, 2, 2).
						Scan(&daillyResult).Error
				}

				// 计算总盈亏
				if err == nil {
					totalWinLoss = (thirdResult.BetAmount + daillyResult.BetAmount) - (thirdResult.WinAmount + daillyResult.WinAmount)
				}
				(*presult)[i]["TotalWinLoss"] = totalWinLoss
			} else {
				(*presult)[i]["TopAgentId"] = 0
				(*presult)[i]["VipLevel"] = 0
				(*presult)[i]["TotalWinLoss"] = 0
			}
		}
	}

	// 计算所有符合条件数据的总盈亏（不分页）
	var sumWinLoss float64 = 0
	allUsersResult, _ := server.Db().Table("x_active_reward_audit").Select("DISTINCT UserId").Where(where).GetList()
	if allUsersResult != nil && len(*allUsersResult) > 0 {
		for _, userRecord := range *allUsersResult {
			userId := int(abugo.GetInt64FromInterface(userRecord["UserId"]))
			if userId > 0 {
				// 查询用户的盈亏
				type GameResult struct {
					BetAmount float64
					WinAmount float64
				}
				var thirdResult GameResult
				err := server.Db().Gorm().Table("x_custom_third").
					Select("IFNULL(SUM(BetAmount), 0) AS bet_amount, IFNULL(SUM(WinAmount), 0) AS win_amount").
					Where("UserId = ?", userId).
					Scan(&thirdResult).Error

				var daillyResult GameResult
				if err == nil {
					err = server.Db().Gorm().Table("x_custom_dailly").
						Select("IFNULL(SUM(BetAmount), 0) AS bet_amount, IFNULL(SUM(RewardAmount), 0) AS win_amount").
						Where("UserId = ? AND IsTest = ? AND IsGameAddress = ?", userId, 2, 2).
						Scan(&daillyResult).Error
				}

				if err == nil {
					userWinLoss := (thirdResult.BetAmount + daillyResult.BetAmount) - (thirdResult.WinAmount + daillyResult.WinAmount)
					sumWinLoss += userWinLoss
				}
			}
		}
	}

	where2 := abugo.AbuDbWhere{}
	where2.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where2.Add("and", "UserId", "=", reqdata.UserId, 0)
	where2.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where2.Add("and", "ActiveId", "in", ids, "")
	where2.Add("and", "AuditState", "=", reqdata.State, 0)
	where2.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	where2.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	sql, data := where2.Sql()
	if sql != "" {
		sql = " WHERE " + sql
	}
	sqlstr := fmt.Sprint("SELECT SUM(Amount) AS TotalAmount, SUM(AmountTrx) AS TotalAmountTrx, SUM(GasFee) AS TotalGasFee FROM x_active_reward_audit ", sql)
	dbr, _ := server.Db().Conn().Query(sqlstr, data...)
	totalresult := abugo.GetResult(dbr)
	sqlstr2 := fmt.Sprint("SELECT COUNT(DISTINCT UserId) AS TotalUsers FROM x_active_reward_audit ", sql)
	dbr2, _ := server.Db().Conn().Query(sqlstr2, data...)
	totalresult2 := abugo.GetResult(dbr2)
	if reqdata.Export != 1 {
		ctx.Put("data", presult)
		ctx.Put("total", total)
		ctx.Put("SumWinLoss", sumWinLoss) // 添加总盈亏统计
		if totalresult != nil && len(*totalresult) > 0 {
			ctx.Put("TotalAmount", abugo.GetFloat64FromInterface((*totalresult)[0]["TotalAmount"]))
			ctx.Put("TotalAmountTrx", abugo.GetFloat64FromInterface((*totalresult)[0]["TotalAmountTrx"]))
			ctx.Put("TotalGasFee", abugo.GetFloat64FromInterface((*totalresult)[0]["TotalGasFee"]))
		}
		if totalresult2 != nil && len(*totalresult2) > 0 {
			ctx.Put("TotalUserId", abugo.GetFloat64FromInterface((*totalresult2)[0]["TotalUsers"]))
		}
	} else {
		sellerNameMap, err := InitSellerNameMap()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		for i := 0; i < len(*presult); i++ {
			for k, v := range (*presult)[i] {
				switch k {
				case "SellerId":
					xlsx.SetValue(k, SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(v))), int64(i+2))
				case "ChannelId":
					xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
				case "AuditState":
					switch abugo.GetInt64FromInterface(v) {
					case 1:
						xlsx.SetValue(k, "待审核", int64(i+2))
					case 2:
						xlsx.SetValue(k, "审核拒绝", int64(i+2))
					case 3:
						xlsx.SetValue(k, "审核通过", int64(i+2))
					case 4:
						xlsx.SetValue(k, "自动通过", int64(i+2))
					default:
						xlsx.SetValue(k, "未定义", int64(i+2))
					}
				case "ActiveId":
					switch abugo.GetInt64FromInterface(v) {
					case int64(active.KActiveIdEachBetFanShui):
						xlsx.SetValue(k, "OK哈希能量补给站", int64(i+2))
					case int64(active.KActiveIdRechange):
						xlsx.SetValue(k, "充值任务奖励", int64(i+2))
					case int64(active.KActiveIdHaXiBreakout):
						xlsx.SetValue(k, "哈希闯关完成任务领取奖金", int64(i+2))
					case int64(active.KActiveIdQipaiBreakout):
						xlsx.SetValue(k, "棋牌闯关完成任务领取奖金", int64(i+2))
					case int64(active.KActiveIdDianziBreakout):
						xlsx.SetValue(k, "电子闯关完成任务领取奖金", int64(i+2))
					case int64(active.KActiveIdRescue):
						xlsx.SetValue(k, "OK Hash救援金", int64(i+2))
					case int64(active.KActiveIdInvite):
						xlsx.SetValue(k, "邀请好友，双重奖励，永久收益", int64(i+2))
					case int64(active.KActiveIdVipFanShui):
						xlsx.SetValue(k, "VIP返水", int64(i+2))
					case int64(active.KActiveIdXianglongfuhu):
						xlsx.SetValue(k, "降龙伏虎", int64(i+2))
					case int64(utils.KActiveIdCumulativeWeeklyRecharge):
						xlsx.SetValue(k, "累计周充值，豪礼享不停", int64(i+2))
					case int64(utils.KActiveIdSignReward):
						xlsx.SetValue(k, "签到奖励", int64(i+2))
					case int64(utils.KActiveIdRecommendFriendReward):
						xlsx.SetValue(k, "推荐好友多重奖励", int64(i+2))
					case int64(utils.KActiveIdZhenrenBreakout):
						xlsx.SetValue(k, "真人视讯闯关", int64(i+2))
					case int64(utils.KActiveIdDianziMeiZhouSong):
						xlsx.SetValue(k, "电子周末狂欢送", int64(i+2))
					case int64(utils.KActiveIdZhenrenMeiZhouSong):
						xlsx.SetValue(k, "真人视讯周末狂欢送", int64(i+2))
					case int64(utils.KActiveIdQiPaiMeiZhouSong):
						xlsx.SetValue(k, "棋牌游戏周末狂欢送", int64(i+2))
					case int64(utils.KActiveIdNewFirstDeposit):
						xlsx.SetValue(k, "新首存活动180", int64(i+2))
					case int64(utils.KActiveIdMidAutumnZhenrenBreakout):
						xlsx.SetValue(k, "中秋每日真人闯关", int64(i+2))
					case int64(utils.KActiveIdMidAutumnBreakout):
						xlsx.SetValue(k, "中秋每日流水闯关", int64(i+2))
					case int64(utils.KActiveIdCryptoGamesBreakout):
						xlsx.SetValue(k, "加密游戏闯关", int64(i+2))
					case int64(utils.SocialMediaFollowGift):
						xlsx.SetValue(k, "关注社媒，福利领不停", int64(i+2))
					case int64(utils.ChatRoomBonusGift):
						xlsx.SetValue(k, "聊天室红包活动", int64(i+2))
					case int64(utils.RegisterGift):
						xlsx.SetValue(k, "注册赠送活动", int64(i+2))
					case int64(utils.NewUserDepositGift):
						xlsx.SetValue(k, "新用户充值赠送活动", int64(i+2))
					case int64(utils.SpecialBonusGift):
						xlsx.SetValue(k, "特殊礼金活动", int64(i+2))
					case int64(utils.RedeemCodeGift_1003):
						xlsx.SetValue(k, "兑换码活动", int64(i+2))
					case int64(utils.FirstDepositGift):
						xlsx.SetValue(k, "首充赠送活动", int64(i+2))
					case int64(utils.MultipleDepositGift):
						xlsx.SetValue(k, "复充赠送活动", int64(i+2))
					case int64(utils.MagicFirstDepositGift):
						xlsx.SetValue(k, "Magic首充赠送活动", int64(i+2))
					case int64(utils.X9FirstDepositGift):
						xlsx.SetValue(k, "X9首充赠送活动", int64(i+2))
					case int64(utils.X9SecondDepositGift):
						xlsx.SetValue(k, "X9第二次充值活动", int64(i+2))
					case int64(utils.X9ThirdDepositGift):
						xlsx.SetValue(k, "X9第三次充值活动", int64(i+2))
					case int64(utils.X9FourthDepositGift):
						xlsx.SetValue(k, "X9第四次充值活动", int64(i+2))
					case int64(utils.X9FifthDepositGift):
						xlsx.SetValue(k, "X9第五次充值活动", int64(i+2))
					case 15:
						xlsx.SetValue(k, "哈希电子闯关", int64(i+2))
					case 16:
						xlsx.SetValue(k, "真人闯关", int64(i+2))
					case 17:
						xlsx.SetValue(k, "体育闯关", int64(i+2))
					default:
						xlsx.SetValue(k, "未定义", int64(i+2))
					}
				case "ActiveLevel":
					if abugo.GetInt64FromInterface((*presult)[i]["ActiveId"]) == int64(active.KActiveIdVipFanShui) {
						switch abugo.GetInt64FromInterface(v) {
						case 1:
							xlsx.SetValue(k, "哈希", int64(i+2))
						case 2:
							xlsx.SetValue(k, "原创", int64(i+2))
						case 3:
							xlsx.SetValue(k, "电子", int64(i+2))
						case 4:
							xlsx.SetValue(k, "棋牌", int64(i+2))
						case 5:
							xlsx.SetValue(k, "真人", int64(i+2))
						case 6:
							xlsx.SetValue(k, "运动", int64(i+2))
						default:
							xlsx.SetValue(k, "未定义", int64(i+2))
						}
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				default:
					xlsx.SetValue(k, v, int64(i+2))
				}
			}
		}

		xlsx.SetValue("Id", "合计", total+2)
		if totalresult != nil && len(*totalresult) > 0 {
			xlsx.SetValue("Amount", abugo.GetFloat64FromInterface((*totalresult)[0]["TotalAmount"]), total+2)
			xlsx.SetValue("AmountTrx", abugo.GetFloat64FromInterface((*totalresult)[0]["TotalAmountTrx"]), total+2)
			xlsx.SetValue("GasFee", abugo.GetFloat64FromInterface((*totalresult)[0]["TotalGasFee"]), total+2)
		}
		if totalresult2 != nil && len(*totalresult2) > 0 {
			xlsx.SetValue("UserId", abugo.GetFloat64FromInterface((*totalresult2)[0]["TotalUsers"]), total+2)
		}
	}

	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.Put("Url", server.ImageUrl())
	ctx.RespOK()
	server.WriteAdminLog("查看活动领取-新", ctx, reqdata)
}

// reward_audit_audit 审核活动奖励
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) reward_audit_audit(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int    // 商户ID
		ChannelId  int    // 渠道ID
		Ids        []int  `validate:"required"` // 奖励ID列表
		State      int    `validate:"required"` // 审核状态
		AuditMemo  string // 审核备注
		GoogleCode string // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "活动领取", "改", "修改活动领取-新")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	for _, id := range reqdata.Ids {
		pdata := []interface{}{id, token.Account, reqdata.AuditMemo, reqdata.State}
		presult, err := server.Db().CallProcedure("x_admin_active_reward_audit_by_id_n", pdata...)
		if err != nil {
			logs.Debug("x_admin_active_reward_audit_by_id err=", err.Error())
		}
		if ctx.RespErr(err, &errcode) {
			return
		}
		if len(reqdata.Ids) == 1 || (presult != nil && abugo.GetInt64FromInterface((*presult)["errcode"]) != 200) {
			if ctx.RespProcedureErr(presult) {
				return
			}
		}
	}

	ctx.RespOK()
	server.WriteAdminLog("修改活动领取-新", ctx, reqdata)
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}

// reward_audit_report 获取活动奖励审核报表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) reward_audit_report(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId    int   // 商户ID
		ChannelIds  []int // 渠道ID列表
		ActiveIds   []int // 活动ID列表
		StartTime   int64 // 开始时间
		EndTime     int64 // 结束时间
		VsStartTime int64 // 对比开始时间
		VsEndTime   int64 // 对比结束时间
		Export      int   // 0不导出 1导出
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "报表统计", "活动报表", "查", "查看活动报表")
	if token == nil {
		return
	}
	// 检查时间参数
	nowTime := time.Now()
	if reqdata.StartTime == 0 || reqdata.EndTime == 0 {
		// 如果不填起始日期，默认统计一周
		reqdata.StartTime = abugo.LocalTimeToTimeStamp(nowTime.Add(-24*7*time.Hour).Format(abugo.DateLayout)+" 00:00:00") * 1000
		reqdata.EndTime = abugo.LocalTimeToTimeStamp(nowTime.Format(abugo.DateLayout)+" 00:00:00") * 1000
	}

	// 准备存储过程参数
	startDate := abugo.TimeStampToLocalTime(reqdata.StartTime)
	endDate := abugo.TimeStampToLocalTime(reqdata.EndTime)
	var compareStartDate, compareEndDate string

	// 设置对比时间参数
	if reqdata.VsStartTime > 0 && reqdata.VsEndTime > 0 {
		compareStartDate = abugo.TimeStampToLocalTime(reqdata.VsStartTime)
		compareEndDate = abugo.TimeStampToLocalTime(reqdata.VsEndTime)
	} else {
		// 如果没有对比时间，使用相同的时间范围
		compareStartDate = startDate
		compareEndDate = endDate
	}

	// 准备商户ID和渠道ID参数
	sellerIdStr := ""
	channelIdStr := ""
	if reqdata.SellerId > 0 {
		sellerIdStr = fmt.Sprintf("%d", reqdata.SellerId)
	}
	if len(reqdata.ChannelIds) > 0 {
		channelIdStrs := make([]string, len(reqdata.ChannelIds))
		for i, id := range reqdata.ChannelIds {
			channelIdStrs[i] = fmt.Sprintf("%d", id)
		}
		channelIdStr = strings.Join(channelIdStrs, ",")
	}

	// 调用存储过程 - 使用Query方法获取多行数据
	sql := "CALL ReportManage_x_user_active_stat_date_GetList(?, ?, ?, ?, ?, ?)"
	pdata := []interface{}{startDate, endDate, compareStartDate, compareEndDate, sellerIdStr, channelIdStr}
	presult, err := server.DbReport().Query(sql, pdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 处理存储过程返回的数据
	var data []map[string]interface{}
	if presult != nil {
		data = *presult
	} else {
		data = make([]map[string]interface{}, 0)
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_活动领取报表-新_%s", time.Now().Format("20060102150405")))
	defer xlsx.Close()
	if reqdata.Export == 1 {
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("ActiveName", "活动名称")
		xlsx.SetTitle("Person", "领取人数")
		xlsx.SetTitle("Count", "领取次数")
		xlsx.SetTitle("Amount", "记录金额")
		xlsx.SetTitle("PersonRate", "活动参与率占比")
		xlsx.SetTitle("AmountRate", "活动金额占比")
		if reqdata.VsStartTime > 0 && reqdata.VsEndTime > 0 {
			xlsx.SetTitle("ComparePerson", "对比期间人数")
			xlsx.SetTitle("CompareCount", "对比期间次数")
			xlsx.SetTitle("CompareAmount", "对比期间金额")
			xlsx.SetTitle("PersonGrowth", "人数增长率")
			xlsx.SetTitle("CountGrowth", "次数增长率")
			xlsx.SetTitle("AmountGrowth", "金额增长率")
		}
		xlsx.SetTitleStyle()
	}

	// 如果指定了活动ID列表，则进行过滤
	if len(reqdata.ActiveIds) > 0 {
		filteredData := make([]map[string]interface{}, 0)
		for _, item := range data {
			activeId := int(abugo.GetInt64FromInterface(item["ActiveId"]))
			found := false
			for _, id := range reqdata.ActiveIds {
				if activeId == id {
					found = true
					break
				}
			}
			if found {
				filteredData = append(filteredData, item)
			}
		}
		data = filteredData
	}

	// 按活动ID排序
	sort.Slice(data, func(i, j int) bool {
		return int(abugo.GetInt64FromInterface(data[i]["ActiveId"])) < int(abugo.GetInt64FromInterface(data[j]["ActiveId"]))
	})

	if reqdata.Export == 1 {
		for i := 0; i < len(data); i++ {
			item := data[i]
			xlsx.SetValue("ActiveName", abugo.GetStringFromInterface(item["ActiveName"]), int64(i+2))
			xlsx.SetValue("Person", int(abugo.GetInt64FromInterface(item["RewardUsers"])), int64(i+2))
			xlsx.SetValue("Count", int(abugo.GetInt64FromInterface(item["RewardUsers"])), int64(i+2))
			xlsx.SetValue("Amount", abugo.GetFloat64FromInterface(item["RewardAmount"]), int64(i+2))
			xlsx.SetValue("PersonRate", fmt.Sprintf("%.2f%%", abugo.GetFloat64FromInterface(item["RewardUsersRate"])), int64(i+2))
			xlsx.SetValue("AmountRate", fmt.Sprintf("%.2f%%", abugo.GetFloat64FromInterface(item["RewardAmountRate"])), int64(i+2))
			if reqdata.VsStartTime > 0 && reqdata.VsEndTime > 0 {
				xlsx.SetValue("ComparePerson", int(abugo.GetInt64FromInterface(item["CompareRewardUsers"])), int64(i+2))
				xlsx.SetValue("CompareCount", int(abugo.GetInt64FromInterface(item["CompareRewardUsers"])), int64(i+2))
				xlsx.SetValue("CompareAmount", abugo.GetFloat64FromInterface(item["CompareRewardAmount"]), int64(i+2))
				xlsx.SetValue("PersonGrowth", fmt.Sprintf("%.2f%%", abugo.GetFloat64FromInterface(item["CompareRewardUsersRate"])), int64(i+2))
				xlsx.SetValue("CountGrowth", fmt.Sprintf("%.2f%%", abugo.GetFloat64FromInterface(item["CompareRewardUsersRate"])), int64(i+2))
				xlsx.SetValue("AmountGrowth", fmt.Sprintf("%.2f%%", abugo.GetFloat64FromInterface(item["CompareRewardAmountRate"])), int64(i+2))
			}
		}

		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK(data)
	server.WriteAdminLog("查看活动领取报表-新", ctx, reqdata)
}

// home_carousel_list 获取首页轮播图列表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int    // 商户ID
		ChannelId int    // 渠道ID
		Lang      int    // 语言ID
		State     int    // 状态
		RealName  string // 真实名称
		Name      string // 名称
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "查", "查看首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "State", "=", reqdata.State, 0)
	where.Add("and", "Lang", "=", reqdata.Lang, 0)
	if len(reqdata.RealName) > 1 {
		where.Add("and", "Name", "=", reqdata.RealName, "")
	} else {
		where.Add("and", "Name", "like", fmt.Sprintf("%%%s%%", reqdata.Name), "")
	}

	data, err := server.Db().Table("x_home_carousel").OrderBy("Sort desc").Where(where).GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.Put("data", data)
	ctx.Put("baseurl", server.ImageUrl())
	ctx.RespOK()
	server.WriteAdminLog("查看首页轮播图", ctx, reqdata)
}

type LangConfig struct {
	Lang             int
	Url              string
	PcUrl            string
	ExpiredTime      int64
	ELink            string
	PcELink          string
	Sort             int32
	RewardPoolConfig string
}

// home_carousel_list_v2 获取首页轮播图列表V2版本
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_list_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int    // 商户ID
		ChannelId int    // 渠道ID
		Lang      int    // 语言ID
		State     int    // 状态
		Id        int    // 轮播图ID
		Name      string // 名称
		Page      int    // 页码
		PageSize  int    // 每页数量
		Type      *int   // 类型 1-活动轮播 2-游戏轮播 3-奖池轮播
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "查", "查看首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	// 确保分页参数有效
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 20
	}

	logs.Debug("home_carousel_list_v2 请求参数: %+v", reqdata)

	homeCarouselDao := server.DaoxHashGame().XHomeCarouselV2
	sellerDao := server.DaoxHashGame().XSeller
	channelDao := server.DaoxHashGame().XChannel

	query := homeCarouselDao.WithContext(context.TODO())

	if reqdata.SellerId != 0 {
		query = query.Where(homeCarouselDao.SellerID.Eq(int32(reqdata.SellerId)))
	}

	if reqdata.ChannelId != 0 {
		query = query.Where(homeCarouselDao.ChannelID.Eq(int32(reqdata.ChannelId)))
	}

	if reqdata.State != 0 {
		query = query.Where(homeCarouselDao.State.Eq(int32(reqdata.State)))
	}

	if reqdata.Lang != 0 {
		query = query.Where(homeCarouselDao.Lang.Eq(int32(reqdata.Lang)))
	}

	if reqdata.Id != 0 {
		query = query.Where(homeCarouselDao.ID.Eq(int32(reqdata.Id)))
	}

	if reqdata.Name != "" {
		query = query.Where(homeCarouselDao.Name.Like("%" + reqdata.Name + "%"))
	}

	if reqdata.Type != nil {
		query = query.Where(homeCarouselDao.Type.Eq(int32(*reqdata.Type)))
	}

	type ResultData struct {
		ChannelId    int
		ChannelName  string
		SellerId     int
		SellerName   string
		Id           int
		Name         string
		Sort         int
		ExpiredTime  int64
		State        int
		ELink        string
		PcELink      string
		Type         int
		GameTypeJump string
		LangConfig   []LangConfig `gorm:"-"`
	}

	var result []ResultData

	count, err := query.Select(homeCarouselDao.ALL, sellerDao.SellerName, channelDao.ChannelName).
		LeftJoin(sellerDao, sellerDao.SellerID.EqCol(homeCarouselDao.SellerID)).
		LeftJoin(channelDao, channelDao.ChannelID.EqCol(homeCarouselDao.ChannelID)).
		Group(homeCarouselDao.SellerID, homeCarouselDao.ChannelID, homeCarouselDao.ID).
		Order(homeCarouselDao.ID.Desc()).
		ScanByPage(&result, (reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)

	// 把相同sellerId, channelId, id的数据合并到LangConfig字段里

	if result == nil {
		result = []ResultData{}
	}

	for i := range result {
		result[i].LangConfig = []LangConfig{}
		// 查询相同sellerId, channelId, id的数据
		langData, err := homeCarouselDao.WithContext(context.TODO()).Select(homeCarouselDao.Lang, homeCarouselDao.URL, homeCarouselDao.PcURL, homeCarouselDao.ExpiredTime, homeCarouselDao.ELink, homeCarouselDao.PcELink, homeCarouselDao.Sort, homeCarouselDao.RewardPoolConfig).
			Where(homeCarouselDao.SellerID.Eq(int32(result[i].SellerId))).
			Where(homeCarouselDao.ChannelID.Eq(int32(result[i].ChannelId))).
			Where(homeCarouselDao.ID.Eq(int32(result[i].Id))).
			Find()

		if err == nil {
			for _, v := range langData {
				result[i].LangConfig = append(result[i].LangConfig, LangConfig{
					Lang:             int(v.Lang),
					Url:              v.URL,
					PcUrl:            v.PcURL,
					ExpiredTime:      v.ExpiredTime,
					ELink:            v.ELink,
					PcELink:          v.PcELink,
					Sort:             v.Sort,
					RewardPoolConfig: v.RewardPoolConfig,
				})
			}
		}
	}

	if err != nil {
		logs.Error("home_carousel_list_v2 error ", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.Put("data", result)
	ctx.Put("total", count) // 添加 total 到响应中
	ctx.Put("baseurl", server.ImageUrl())
	ctx.RespOK()
	server.WriteAdminLog("查看首页轮播图", ctx, reqdata)
}

// home_carousel_add 添加首页轮播图
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId    int    `validate:"required"` // 商户ID
		ChannelId   int    `validate:"required"` // 渠道ID
		Name        string `validate:"required"` // 名称
		Sort        int    // 排序
		ExpiredTime int64  // 过期时间
		State       int    // 状态
		Url         string // 图片URL
		Lang        int    `validate:"required"` // 语言ID
		ELink       string // 外链地址
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "增", "增加首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	_, err := server.Db().Table("x_home_carousel").Select("*").Insert(gin.H{
		"SellerId":    reqdata.SellerId,
		"ChannelId":   reqdata.ChannelId,
		"Name":        reqdata.Name,
		"Sort":        reqdata.Sort,
		"ExpiredTime": reqdata.ExpiredTime,
		"State":       reqdata.State,
		"Url":         reqdata.Url,
		"Lang":        reqdata.Lang,
		"ELink":       reqdata.ELink,
	})
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("增加首页轮播图", ctx, reqdata)
}

// home_carousel_add_v2 添加首页轮播图V2版本
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_add_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type Item struct {
		Sort             int    // 排序
		ExpiredTime      int64  // 过期时间
		Url              string // 图片URL
		PcUrl            string // PC端图片URL
		ELink            string // 外链地址
		PcELink          string // PC端外链地址
		RewardPoolConfig string // 奖池配置
	}
	type RequestData struct {
		SellerId     int    `validate:"required"` // 商户ID
		ChannelId    int    `validate:"required"` // 渠道ID
		Name         string `validate:"required"` // 名称
		Id           int    // 轮播图ID
		State        int    // 状态
		Type         int    // 类型 1普通 2游戏分类跳转 3奖池轮播
		GameTypeJump string // 游戏分类跳转地址
		// RewardPoolConfig string       // 奖池配置
		Lang map[int]Item `validate:"required"` // 多语言配置
	}
	type RRequestData struct {
		Item
		SellerId     int    // 商户ID
		ChannelId    int    // 渠道ID
		Name         string // 名称
		Id           int    // 轮播图ID
		Lang         int    // 语言ID
		State        int    // 状态
		Type         int    // 类型 1普通 2游戏分类跳转 3奖池轮播
		GameTypeJump string // 游戏分类跳转地址
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "增", "增加首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	var rdatas []*RRequestData

	for k, v := range reqdata.Lang {
		t := &RRequestData{
			SellerId:     reqdata.SellerId,
			ChannelId:    reqdata.ChannelId,
			Name:         reqdata.Name,
			Id:           reqdata.Id,
			Lang:         k,
			State:        reqdata.State,
			Type:         reqdata.Type,
			GameTypeJump: reqdata.GameTypeJump,
		}
		t.RewardPoolConfig = v.RewardPoolConfig
		t.Sort = v.Sort
		t.ExpiredTime = v.ExpiredTime
		t.Url = v.Url
		t.PcUrl = v.PcUrl
		t.ELink = v.ELink
		t.PcELink = v.PcELink
		rdatas = append(rdatas, t)
	}

	for _, rdata := range rdatas {
		err := server.Db().GormDao().Transaction(func(tx *daoGrom.DB) error {
			do := tx.Table(model.TableNameXHomeCarouselV2).Where("ChannelId = ? and Lang = ? and SellerId = ?", rdata.ChannelId, rdata.Lang, rdata.SellerId)
			if rdata.Id > 0 {
				do = do.Where("Id = ?", rdata.Id)
			} else {
				do = do.Where("Name = ?", rdata.Name)
			}
			reModel := model.XHomeCarouselV2{}
			result := do.First(&reModel)
			if result.Error != nil && result.Error != daoGrom.ErrRecordNotFound {
				return result.Error
			}
			if result.RowsAffected > 0 {
				result2 := tx.Table(model.TableNameXHomeCarouselV2).Where("SellerId= ? and ChannelId = ? and Lang = ? and Id = ?", reModel.SellerID, reModel.ChannelID, reModel.Lang, reModel.ID).
					Updates(model.XHomeCarouselV2{
						Name:             rdata.Name,
						Sort:             int32(rdata.Sort),
						ExpiredTime:      rdata.ExpiredTime,
						State:            int32(rdata.State),
						URL:              rdata.Url,
						PcURL:            rdata.PcUrl,
						ELink:            rdata.ELink,
						PcELink:          rdata.PcELink,
						Type:             int32(rdata.Type),
						GameTypeJump:     rdata.GameTypeJump,
						RewardPoolConfig: rdata.RewardPoolConfig,
					})
				if result2.Error != nil {
					return result2.Error
				}
				if result2.RowsAffected < 1 {
					// return fmt.Errorf("update RowsAffected 0")
				}
			} else {
				do := tx.Table(model.TableNameXHomeCarouselV2).Where("ChannelId = ?", rdata.ChannelId)
				if rdata.Id > 0 {
					do = do.Where("Id = ?", rdata.Id)
				} else {
					do = do.Where("Name = ?", rdata.Name)
				}
				reModel := model.XHomeCarouselV2{}
				result := do.First(&reModel)
				if result.Error != nil && result.Error != daoGrom.ErrRecordNotFound {
					return result.Error
				}
				var id int32
				if result.RowsAffected > 0 {
					id = reModel.ID
				} else {
					sql := `
					WITH t AS (
					SELECT channelid, id FROM x_home_carousel_v2 GROUP BY channelid, id
					)
					SELECT COUNT(*) as id FROM t
				`
					result := tx.Raw(sql).Scan(&id)
					if result.Error != nil {
						return result.Error
					}
					id = id + 1
				}
				result = tx.Table(model.TableNameXHomeCarouselV2).Create(&model.XHomeCarouselV2{
					SellerID:         int32(rdata.SellerId),
					ChannelID:        int32(rdata.ChannelId),
					Lang:             int32(rdata.Lang),
					ID:               id,
					Name:             rdata.Name,
					Sort:             int32(rdata.Sort),
					ExpiredTime:      rdata.ExpiredTime,
					State:            int32(rdata.State),
					URL:              rdata.Url,
					PcURL:            rdata.PcUrl,
					ELink:            rdata.ELink,
					PcELink:          rdata.PcELink,
					Type:             int32(rdata.Type),
					GameTypeJump:     rdata.GameTypeJump,
					RewardPoolConfig: rdata.RewardPoolConfig,
				})
				if result.Error != nil {
					return result.Error
				}
			}
			return nil
		})
		if err != nil {
			logs.Error("home_carousel_add_v2 error ", err)
			ctx.RespErrString(true, &errcode, "数据库错误 "+err.Error())
			return
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("增加首页轮播图", ctx, reqdata)
}

// home_carousel_del 删除首页轮播图
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_del(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id        int `validate:"required"` // 轮播图ID
		SellerId  int // 商户ID
		ChannelId int `validate:"required"` // 渠道ID
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "删", "删除首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	var err error
	if reqdata.SellerId > 0 {
		_, err = server.Db().Query("delete from x_home_carousel where SellerId = ? and ChannelId = ? and Id = ?", []interface{}{reqdata.SellerId, reqdata.ChannelId, reqdata.Id})
	} else {
		_, err = server.Db().Query("delete from x_home_carousel where ChannelId = ? and Id = ?", []interface{}{reqdata.ChannelId, reqdata.Id})
	}
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("删除首页轮播图", ctx, reqdata)
}

// home_carousel_del_v2 删除首页轮播图V2版本
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_del_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` // 商户ID
		ChannelId int `validate:"required"` // 渠道ID
		Id        int `validate:"required"` // 轮播图ID
		// Lang      int `validate:"required"` // 语言ID
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "删", "删除首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	tb := server.DaoxHashGame().XHomeCarouselV2
	db := tb.WithContext(context.Background())
	result, err := db.Where(tb.ChannelID.Eq(int32(reqdata.ChannelId))).Where(tb.ID.Eq(int32(reqdata.Id))).Delete()
	if err != nil {
		logs.Error("home_carousel_del_v2 del ", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if result.RowsAffected < 1 {
		ctx.RespErrString(true, &errcode, "RowsAffected 0")
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("删除首页轮播图", ctx, reqdata)
}

// home_carousel_open_v2 开启/关闭首页轮播图V2版本
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_open_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` // 商户ID
		ChannelId int `validate:"required"` // 渠道ID
		Id        int `validate:"required"` // 轮播图ID
		Lang      int // 语言ID
		State     int `validate:"required"` // 状态
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "改", "修改首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	tb := server.DaoxHashGame().XHomeCarouselV2
	db := tb.WithContext(context.Background())
	db = db.Where(tb.ChannelID.Eq(int32(reqdata.ChannelId))).Where(tb.ID.Eq(int32(reqdata.Id)))
	if reqdata.Lang > 0 {
		db = db.Where(tb.Lang.Eq(int32(reqdata.Lang)))
	}
	result, err := db.UpdateSimple(tb.State.Value(int32(reqdata.State)))
	if err != nil {
		logs.Error("home_carousel_open_v2 update ", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if result.RowsAffected < 1 {
		// ctx.RespErrString(true, &errcode, "RowsAffected 0")
		// return
	}
	ctx.RespOK()
	server.WriteAdminLog("禁用首页轮播图", ctx, reqdata)
}

// home_carousel_mod 修改首页轮播图
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) home_carousel_mod(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id          int    `validate:"required"` // 轮播图ID
		SellerId    int    `validate:"required"` // 商户ID
		ChannelId   int    `validate:"required"` // 渠道ID
		Name        string // 名称
		Sort        int    // 排序
		ExpiredTime int64  // 过期时间
		State       int    // 状态
		Url         string // 图片URL
		ELink       string // 外链地址
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "首页轮播图", "改", "修改首页轮播图")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.Id, 0)
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	_, err := server.Db().Table("x_home_carousel").Select("*").Where(where).Update(gin.H{
		"Name":        reqdata.Name,
		"Sort":        reqdata.Sort,
		"ExpiredTime": reqdata.ExpiredTime,
		"State":       reqdata.State,
		"Url":         reqdata.Url,
		"ELink":       reqdata.ELink,
	})
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("修改首页轮播图", ctx, reqdata)
}

// GetActiveNameList 获取活动名称列表
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) GetActiveNameList(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int // 商户ID
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	activeTb := server.DaoxHashGame().XActiveSellerDefine
	activeDb := server.DaoxHashGame().XActiveSellerDefine.WithContext(context.Background())
	if reqdata.SellerId > 0 {
		activeDb = activeDb.Where(activeTb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	list, err := activeDb.Select(activeTb.ActiveID.Distinct().As(activeTb.ActiveID.ColumnName().String()), activeTb.Title).Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("list", list)
	server.WriteAdminLog("获取活动名称列表", ctx, reqdata)
	ctx.RespOK()
}

// generate_redeem_code 生成兑换码
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) generate_redeem_code(ctx *abugo.AbuHttpContent) {
	active.HandleGenerateRedeemCode(ctx)
}

// redeem_code_record 查询兑换码领取记录
// @param ctx *abugo.AbuHttpContent HTTP上下文
func (c *ActiveController) redeem_code_record(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int    `json:"SellerId" form:"SellerId"`     // 运营商ID
		ChannelId  int    `json:"ChannelId" form:"ChannelId"`   // 渠道ID
		Page       int    `json:"Page" form:"Page"`             // 页码，从1开始
		PageSize   int    `json:"PageSize" form:"PageSize"`     // 每页记录数
		RedeemCode string `json:"RedeemCode" form:"RedeemCode"` // 兑换码
		UserId     int32  `json:"UserId" form:"UserId"`         // 玩家ID
		StartTime  int64  `json:"StartTime" form:"StartTime"`   // 开始时间（毫秒时间戳）
		EndTime    int64  `json:"EndTime" form:"EndTime"`       // 结束时间（毫秒时间戳）
		ActiveId   int32  `json:"ActiveId" form:"ActiveId"`     // 活动ID
		ActiveName string `json:"ActiveName" form:"ActiveName"` // 活动名称
		Export     int    `json:"Export" form:"Export"`         // 0表示分页查询，1表示导出报表
	}

	errcode := 0
	reqdata := RequestData{}
	if err := ctx.RequestData(&reqdata); ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "活动管理", "活动列表", "查"), &errcode, "权限不足") {
		return
	}

	// 创建Excel导出对象
	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_兑换码领取记录_%s", time.Now().Format("20060102150405")))
	defer xlsx.Close()

	// 如果是导出模式，设置Excel相关参数
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 10000 // 导出时使用大页面大小
		xlsx.Open()
		// 设置表头
		xlsx.SetTitle("Id", "记录ID")
		xlsx.SetTitle("SellerId", "运营商")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("RedeemCode", "兑换码")
		xlsx.SetTitle("UseCount", "此码可兑次数")
		xlsx.SetTitle("RewardAmount", "获得奖励金额")
		xlsx.SetTitle("ActiveId", "对应活动ID")
		xlsx.SetTitle("ActiveName", "对应活动名称")
		xlsx.SetTitle("RedeemTime", "兑换时间")
		xlsx.SetTitle("CreateTime", "创建时间")
		xlsx.SetTitleStyle()
	} else {
		// 设置默认值
		if reqdata.Page <= 0 {
			reqdata.Page = 1
		}
		if reqdata.PageSize <= 0 {
			reqdata.PageSize = 15 // 默认每页显示15条记录
		}
		if reqdata.PageSize > 500 {
			reqdata.PageSize = 500 // 限制最大每页记录数
		}
	}

	redeemRecordTb := server.DaoxHashGame().XActiveRedeemcodeRecord
	redeemRecordDb := server.DaoxHashGame().XActiveRedeemcodeRecord.WithContext(context.Background())

	// 构建查询条件
	conditions := []gen.Condition{}

	// 添加基本条件
	conditions = append(conditions, redeemRecordTb.SellerID.Eq(int32(reqdata.SellerId)))
	conditions = append(conditions, redeemRecordTb.ChannelID.Eq(int32(reqdata.ChannelId)))

	// 添加可选条件
	if reqdata.RedeemCode != "" {
		conditions = append(conditions, redeemRecordTb.RedeemCode.Eq(reqdata.RedeemCode))
	}
	if reqdata.UserId > 0 {
		conditions = append(conditions, redeemRecordTb.UserID.Eq(reqdata.UserId))
	}
	if reqdata.ActiveId > 0 {
		conditions = append(conditions, redeemRecordTb.ActiveID.Eq(reqdata.ActiveId))
	}
	if reqdata.ActiveName != "" {
		conditions = append(conditions, redeemRecordTb.ActiveName.Eq(reqdata.ActiveName))
	}

	// 添加时间范围条件
	if reqdata.StartTime > 0 {
		startTime := time.Unix(reqdata.StartTime/1000, 0)
		conditions = append(conditions, redeemRecordTb.RedeemTime.Gte(startTime))
	}
	if reqdata.EndTime > 0 {
		endTime := time.Unix(reqdata.EndTime/1000, 0)
		conditions = append(conditions, redeemRecordTb.RedeemTime.Lte(endTime))
	}

	// 计算分页参数
	offset := (reqdata.Page - 1) * reqdata.PageSize

	// 执行查询
	var records []*model.XActiveRedeemcodeRecord
	var total int64

	// 查询总数
	var queryErr error
	total, queryErr = redeemRecordDb.Where(conditions...).Count()
	if queryErr != nil {
		logs.Error("redeem_code_record 查询总数错误: %v", queryErr)
		ctx.RespErrString(true, &errcode, "查询记录失败")
		return
	}

	// 查询数据
	records, queryErr = redeemRecordDb.Where(conditions...).
		Order(redeemRecordTb.RedeemTime.Desc()).
		Offset(offset).
		Limit(reqdata.PageSize).
		Find()

	if queryErr != nil {
		logs.Error("redeem_code_record 查询数据错误: %v", queryErr)
		ctx.RespErrString(true, &errcode, "查询记录失败")
		return
	}

	logs.Debug("redeem_code_record 查询结果: 数据条数=%d, 总数=%d", len(records), total)

	// 将查询结果转换为map格式，以便与原有代码兼容
	presult := &[]map[string]interface{}{}
	for _, record := range records {
		item := map[string]interface{}{
			"Id":           record.ID,
			"SellerId":     record.SellerID,
			"ChannelId":    record.ChannelID,
			"UserId":       record.UserID,
			"RedeemCode":   record.RedeemCode,
			"UseCount":     record.UseCount,
			"RewardAmount": record.RewardAmount,
			"ActiveId":     record.ActiveID,
			"ActiveName":   record.ActiveName,
			"RedeemTime":   record.RedeemTime,
			"CreateTime":   record.CreateTime,
			"UpdateTime":   record.UpdateTime,
		}
		*presult = append(*presult, item)
	}

	// 处理查询结果
	if reqdata.Export != 1 {
		// 普通查询模式，直接返回数据
		ctx.Put("data", presult)
		ctx.Put("total", total) // 添加 total 到响应中
	} else {
		// 导出模式，将数据写入Excel
		// 计算总奖励金额
		var totalRewardAmount float64
		totalSize := int64(len(*presult))

		// 写入数据行
		for i := 0; i < len(*presult); i++ {
			for k, v := range (*presult)[i] {
				switch k {
				case "ChannelId":
					xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
				case "ActiveId":
					activeId := abugo.GetInt64FromInterface(v)
					xlsx.SetValue(k, activeId, int64(i+2))
				case "RewardAmount":
					amount := abugo.GetFloat64FromInterface(v)
					totalRewardAmount += amount
					xlsx.SetValue(k, amount, int64(i+2))
				default:
					xlsx.SetValue(k, v, int64(i+2))
				}
			}
		}

		// 添加合计行
		xlsx.SetValue("RedeemCode", "合计", totalSize+2)
		xlsx.SetValue("RewardAmount", totalRewardAmount, totalSize+2)

		// 生成Excel文件
		filePath, err := xlsx.ProduceFile()
		if err != nil {
			logs.Error("生成Excel文件失败: %v", err)
			ctx.RespErrString(true, &errcode, "生成Excel文件失败")
			return
		}

		// 返回文件路径
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}

	ctx.RespOK()
	server.WriteAdminLog("查询兑换码领取记录", ctx, reqdata)
}
