// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXWithdrawGame = "x_withdraw_game"

// XWithdrawGame 提款订单游戏统计
type XWithdrawGame struct {
	OrderID    int32     `gorm:"column:OrderId;primaryKey;comment:订单id" json:"OrderId"` // 订单id
	Brand      string    `gorm:"column:Brand;primaryKey;comment:三方品牌" json:"Brand"`     // 三方品牌
	GameID     string    `gorm:"column:GameId;primaryKey;comment:游戏Id" json:"GameId"`   // 游戏Id
	Symbol     string    `gorm:"column:Symbol;primaryKey" json:"Symbol"`
	GameName   string    `gorm:"column:GameName;comment:游戏名" json:"GameName"`                                // 游戏名
	GameType   int32     `gorm:"column:GameType;not null;comment:游戏分类" json:"GameType"`                      // 游戏分类
	StartDate  time.Time `gorm:"column:StartDate;comment:统计开始日期" json:"StartDate"`                           // 统计开始日期
	EndDate    time.Time `gorm:"column:EndDate;comment:统计结束日期" json:"EndDate"`                               // 统计结束日期
	UserID     int32     `gorm:"column:UserId;comment:玩家id" json:"UserId"`                                   // 玩家id
	TopAgentID int32     `gorm:"column:TopAgentId;comment:顶级id" json:"TopAgentId"`                           // 顶级id
	SellerID   int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                // 运营商
	ChannelID  int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                               // 渠道
	BetCount   int32     `gorm:"column:BetCount;comment:投注次数" json:"BetCount"`                               // 投注次数
	WinCount   int32     `gorm:"column:WinCount;comment:赢次数" json:"WinCount"`                                // 赢次数
	BetAmount  float64   `gorm:"column:BetAmount;default:0.000000;comment:投注金额" json:"BetAmount"`            // 投注金额
	WinAmount  float64   `gorm:"column:WinAmount;default:0.000000;comment:返奖金额" json:"WinAmount"`            // 返奖金额
	LiuSui     float64   `gorm:"column:LiuSui;default:0.000000;comment:有效投注" json:"LiuSui"`                  // 有效投注
	Fee        float64   `gorm:"column:Fee;default:0.000000;comment:手续费" json:"Fee"`                         // 手续费
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
}

// TableName XWithdrawGame's table name
func (*XWithdrawGame) TableName() string {
	return TableNameXWithdrawGame
}
