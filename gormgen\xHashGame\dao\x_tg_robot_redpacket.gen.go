// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgRobotRedpacket(db *gorm.DB, opts ...gen.DOOption) xTgRobotRedpacket {
	_xTgRobotRedpacket := xTgRobotRedpacket{}

	_xTgRobotRedpacket.xTgRobotRedpacketDo.UseDB(db, opts...)
	_xTgRobotRedpacket.xTgRobotRedpacketDo.UseModel(&model.XTgRobotRedpacket{})

	tableName := _xTgRobotRedpacket.xTgRobotRedpacketDo.TableName()
	_xTgRobotRedpacket.ALL = field.NewAsterisk(tableName)
	_xTgRobotRedpacket.ID = field.NewInt64(tableName, "Id")
	_xTgRobotRedpacket.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRobotRedpacket.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTgRobotRedpacket.TgRobotUserName = field.NewString(tableName, "TgRobotUserName")
	_xTgRobotRedpacket.GuideRobotID = field.NewInt32(tableName, "GuideRobotId")
	_xTgRobotRedpacket.TgRobotToken = field.NewString(tableName, "TgRobotToken")
	_xTgRobotRedpacket.KefuTgUserName = field.NewString(tableName, "KefuTgUserName")
	_xTgRobotRedpacket.TgGroupID = field.NewInt64(tableName, "TgGroupId")
	_xTgRobotRedpacket.TgGroupName = field.NewString(tableName, "TgGroupName")
	_xTgRobotRedpacket.TgAdminID = field.NewInt64(tableName, "TgAdminId")
	_xTgRobotRedpacket.GameURL = field.NewString(tableName, "GameUrl")
	_xTgRobotRedpacket.GrabContent = field.NewString(tableName, "GrabContent")
	_xTgRobotRedpacket.StartContent = field.NewString(tableName, "StartContent")
	_xTgRobotRedpacket.IsEnable = field.NewInt32(tableName, "IsEnable")
	_xTgRobotRedpacket.LimitWithdrawAmount = field.NewFloat64(tableName, "LimitWithdrawAmount")
	_xTgRobotRedpacket.FreeWithdrawCount = field.NewInt32(tableName, "FreeWithdrawCount")
	_xTgRobotRedpacket.FreeGrabCount = field.NewInt32(tableName, "FreeGrabCount")
	_xTgRobotRedpacket.InviteCount = field.NewInt32(tableName, "InviteCount")
	_xTgRobotRedpacket.InviteGetChance = field.NewInt32(tableName, "InviteGetChance")
	_xTgRobotRedpacket.InviteGrabCount = field.NewInt32(tableName, "inviteGrabCount")
	_xTgRobotRedpacket.InviteGetGrabChance = field.NewInt32(tableName, "inviteGetGrabChance")
	_xTgRobotRedpacket.WithdrawFlowMultiple = field.NewInt32(tableName, "WithdrawFlowMultiple")
	_xTgRobotRedpacket.ResetWithdrawCount = field.NewInt32(tableName, "ResetWithdrawCount")
	_xTgRobotRedpacket.ResetGrabCount = field.NewInt32(tableName, "ResetGrabCount")
	_xTgRobotRedpacket.CreatedAt = field.NewTime(tableName, "CreatedAt")
	_xTgRobotRedpacket.UpdatedAt = field.NewTime(tableName, "UpdatedAt")

	_xTgRobotRedpacket.fillFieldMap()

	return _xTgRobotRedpacket
}

type xTgRobotRedpacket struct {
	xTgRobotRedpacketDo xTgRobotRedpacketDo

	ALL             field.Asterisk
	ID              field.Int64
	SellerID        field.Int32  // 运营商id
	ChannelID       field.Int32  // 渠道id
	TgRobotUserName field.String // 机器人Username
	GuideRobotID    field.Int32  // 关联接待机器人表id
	TgRobotToken    field.String // 机器人token
	KefuTgUserName  field.String // 客服tg号
	TgGroupID       field.Int64  // 特定群组ID
	TgGroupName     field.String // tg群名称
	TgAdminID       field.Int64  // 指定管理员ID
	GameURL         field.String // 游戏链接
	GrabContent     field.String // 抢红包文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	/*
		红包机器人开始文案
		{"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	*/
	StartContent         field.String
	IsEnable             field.Int32   // 是否启用 (1:是 2:否)
	LimitWithdrawAmount  field.Float64 // 提现最低金额
	FreeWithdrawCount    field.Int32   // 免费提现次数
	FreeGrabCount        field.Int32   // 免费抢红包次数
	InviteCount          field.Int32   // 需要邀请xx人获得提现次数
	InviteGetChance      field.Int32   // 邀请满足多少人后获得xxx提现次数
	InviteGrabCount      field.Int32   // 需要邀请xx人获得抢红包机会
	InviteGetGrabChance  field.Int32   // 满足邀请人数后获得xx次抢红包次数
	WithdrawFlowMultiple field.Int32   // 提现需要的流水倍数
	ResetWithdrawCount   field.Int32   // 提现每日重置次数
	ResetGrabCount       field.Int32   // 抢红包每日重置次数
	CreatedAt            field.Time    // 创建时间
	UpdatedAt            field.Time    // 修改时间

	fieldMap map[string]field.Expr
}

func (x xTgRobotRedpacket) Table(newTableName string) *xTgRobotRedpacket {
	x.xTgRobotRedpacketDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRobotRedpacket) As(alias string) *xTgRobotRedpacket {
	x.xTgRobotRedpacketDo.DO = *(x.xTgRobotRedpacketDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRobotRedpacket) updateTableName(table string) *xTgRobotRedpacket {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.TgRobotUserName = field.NewString(table, "TgRobotUserName")
	x.GuideRobotID = field.NewInt32(table, "GuideRobotId")
	x.TgRobotToken = field.NewString(table, "TgRobotToken")
	x.KefuTgUserName = field.NewString(table, "KefuTgUserName")
	x.TgGroupID = field.NewInt64(table, "TgGroupId")
	x.TgGroupName = field.NewString(table, "TgGroupName")
	x.TgAdminID = field.NewInt64(table, "TgAdminId")
	x.GameURL = field.NewString(table, "GameUrl")
	x.GrabContent = field.NewString(table, "GrabContent")
	x.StartContent = field.NewString(table, "StartContent")
	x.IsEnable = field.NewInt32(table, "IsEnable")
	x.LimitWithdrawAmount = field.NewFloat64(table, "LimitWithdrawAmount")
	x.FreeWithdrawCount = field.NewInt32(table, "FreeWithdrawCount")
	x.FreeGrabCount = field.NewInt32(table, "FreeGrabCount")
	x.InviteCount = field.NewInt32(table, "InviteCount")
	x.InviteGetChance = field.NewInt32(table, "InviteGetChance")
	x.InviteGrabCount = field.NewInt32(table, "inviteGrabCount")
	x.InviteGetGrabChance = field.NewInt32(table, "inviteGetGrabChance")
	x.WithdrawFlowMultiple = field.NewInt32(table, "WithdrawFlowMultiple")
	x.ResetWithdrawCount = field.NewInt32(table, "ResetWithdrawCount")
	x.ResetGrabCount = field.NewInt32(table, "ResetGrabCount")
	x.CreatedAt = field.NewTime(table, "CreatedAt")
	x.UpdatedAt = field.NewTime(table, "UpdatedAt")

	x.fillFieldMap()

	return x
}

func (x *xTgRobotRedpacket) WithContext(ctx context.Context) *xTgRobotRedpacketDo {
	return x.xTgRobotRedpacketDo.WithContext(ctx)
}

func (x xTgRobotRedpacket) TableName() string { return x.xTgRobotRedpacketDo.TableName() }

func (x xTgRobotRedpacket) Alias() string { return x.xTgRobotRedpacketDo.Alias() }

func (x xTgRobotRedpacket) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRobotRedpacketDo.Columns(cols...)
}

func (x *xTgRobotRedpacket) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRobotRedpacket) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 26)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["TgRobotUserName"] = x.TgRobotUserName
	x.fieldMap["GuideRobotId"] = x.GuideRobotID
	x.fieldMap["TgRobotToken"] = x.TgRobotToken
	x.fieldMap["KefuTgUserName"] = x.KefuTgUserName
	x.fieldMap["TgGroupId"] = x.TgGroupID
	x.fieldMap["TgGroupName"] = x.TgGroupName
	x.fieldMap["TgAdminId"] = x.TgAdminID
	x.fieldMap["GameUrl"] = x.GameURL
	x.fieldMap["GrabContent"] = x.GrabContent
	x.fieldMap["StartContent"] = x.StartContent
	x.fieldMap["IsEnable"] = x.IsEnable
	x.fieldMap["LimitWithdrawAmount"] = x.LimitWithdrawAmount
	x.fieldMap["FreeWithdrawCount"] = x.FreeWithdrawCount
	x.fieldMap["FreeGrabCount"] = x.FreeGrabCount
	x.fieldMap["InviteCount"] = x.InviteCount
	x.fieldMap["InviteGetChance"] = x.InviteGetChance
	x.fieldMap["inviteGrabCount"] = x.InviteGrabCount
	x.fieldMap["inviteGetGrabChance"] = x.InviteGetGrabChance
	x.fieldMap["WithdrawFlowMultiple"] = x.WithdrawFlowMultiple
	x.fieldMap["ResetWithdrawCount"] = x.ResetWithdrawCount
	x.fieldMap["ResetGrabCount"] = x.ResetGrabCount
	x.fieldMap["CreatedAt"] = x.CreatedAt
	x.fieldMap["UpdatedAt"] = x.UpdatedAt
}

func (x xTgRobotRedpacket) clone(db *gorm.DB) xTgRobotRedpacket {
	x.xTgRobotRedpacketDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRobotRedpacket) replaceDB(db *gorm.DB) xTgRobotRedpacket {
	x.xTgRobotRedpacketDo.ReplaceDB(db)
	return x
}

type xTgRobotRedpacketDo struct{ gen.DO }

func (x xTgRobotRedpacketDo) Debug() *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRobotRedpacketDo) WithContext(ctx context.Context) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRobotRedpacketDo) ReadDB() *xTgRobotRedpacketDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRobotRedpacketDo) WriteDB() *xTgRobotRedpacketDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRobotRedpacketDo) Session(config *gorm.Session) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRobotRedpacketDo) Clauses(conds ...clause.Expression) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRobotRedpacketDo) Returning(value interface{}, columns ...string) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRobotRedpacketDo) Not(conds ...gen.Condition) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRobotRedpacketDo) Or(conds ...gen.Condition) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRobotRedpacketDo) Select(conds ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRobotRedpacketDo) Where(conds ...gen.Condition) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRobotRedpacketDo) Order(conds ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRobotRedpacketDo) Distinct(cols ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRobotRedpacketDo) Omit(cols ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRobotRedpacketDo) Join(table schema.Tabler, on ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRobotRedpacketDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRobotRedpacketDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRobotRedpacketDo) Group(cols ...field.Expr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRobotRedpacketDo) Having(conds ...gen.Condition) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRobotRedpacketDo) Limit(limit int) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRobotRedpacketDo) Offset(offset int) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRobotRedpacketDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRobotRedpacketDo) Unscoped() *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRobotRedpacketDo) Create(values ...*model.XTgRobotRedpacket) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRobotRedpacketDo) CreateInBatches(values []*model.XTgRobotRedpacket, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRobotRedpacketDo) Save(values ...*model.XTgRobotRedpacket) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRobotRedpacketDo) First() (*model.XTgRobotRedpacket, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotRedpacket), nil
	}
}

func (x xTgRobotRedpacketDo) Take() (*model.XTgRobotRedpacket, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotRedpacket), nil
	}
}

func (x xTgRobotRedpacketDo) Last() (*model.XTgRobotRedpacket, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotRedpacket), nil
	}
}

func (x xTgRobotRedpacketDo) Find() ([]*model.XTgRobotRedpacket, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRobotRedpacket), err
}

func (x xTgRobotRedpacketDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRobotRedpacket, err error) {
	buf := make([]*model.XTgRobotRedpacket, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRobotRedpacketDo) FindInBatches(result *[]*model.XTgRobotRedpacket, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRobotRedpacketDo) Attrs(attrs ...field.AssignExpr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRobotRedpacketDo) Assign(attrs ...field.AssignExpr) *xTgRobotRedpacketDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRobotRedpacketDo) Joins(fields ...field.RelationField) *xTgRobotRedpacketDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRobotRedpacketDo) Preload(fields ...field.RelationField) *xTgRobotRedpacketDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRobotRedpacketDo) FirstOrInit() (*model.XTgRobotRedpacket, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotRedpacket), nil
	}
}

func (x xTgRobotRedpacketDo) FirstOrCreate() (*model.XTgRobotRedpacket, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotRedpacket), nil
	}
}

func (x xTgRobotRedpacketDo) FindByPage(offset int, limit int) (result []*model.XTgRobotRedpacket, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRobotRedpacketDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRobotRedpacketDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRobotRedpacketDo) Delete(models ...*model.XTgRobotRedpacket) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRobotRedpacketDo) withDO(do gen.Dao) *xTgRobotRedpacketDo {
	x.DO = *do.(*gen.DO)
	return x
}
