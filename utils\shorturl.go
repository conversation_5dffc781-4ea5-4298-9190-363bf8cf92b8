// Package utils 提供了 Bitly API 的 Go 语言客户端实现
package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

// 定义常量
const (
	// BaseURL 是 Bitly API 的基础 URL
	BaseURL = "https://api-ssl.bitly.com/v4"
	// Token 是访问 Bitly API 的认证令牌
	Token = "****************************************"
)

// Client 表示 Bitly API 客户端
// 包含了进行 HTTP 请求所需的客户端实例
type Client struct {
	HTTPClient *http.Client
}

// CreateBitlinkRequest 表示创建短链接的请求参数
type CreateBitlinkRequest struct {
	LongURL   string     `json:"long_url"`             // 需要缩短的原始长链接
	Domain    string     `json:"domain,omitempty"`     // 可选：自定义域名
	GroupGUID string     `json:"group_guid,omitempty"` // 可选：组 ID
	Title     string     `json:"title,omitempty"`      // 可选：链接标题
	Tags      []string   `json:"tags,omitempty"`       // 可选：标签列表
	DeepLinks []DeepLink `json:"deeplinks,omitempty"`  // 可选：深度链接配置
}

// DeepLink 表示深度链接配置
type DeepLink struct {
	AppID       string `json:"app_id"`       // 应用 ID
	AppURI      string `json:"app_uri_path"` // 应用 URI 路径
	InstallType string `json:"install_type"` // 安装类型
	InstallURL  string `json:"install_url"`  // 安装 URL
}

// BitlinkResponse 表示创建短链接的 API 响应
type BitlinkResponse struct {
	CreatedAt      string     `json:"created_at"`      // 创建时间
	ID             string     `json:"id"`              // 短链接 ID
	Link           string     `json:"link"`            // 生成的短链接
	CustomBitlinks []string   `json:"custom_bitlinks"` // 自定义短链接列表
	LongURL        string     `json:"long_url"`        // 原始长链接
	Title          string     `json:"title"`           // 链接标题
	Archived       bool       `json:"archived"`        // 是否已归档
	Tags           []string   `json:"tags"`            // 标签列表
	DeepLinks      []DeepLink `json:"deeplinks"`       // 深度链接配置
	References     struct {
		Group string `json:"group"` // 组引用
	} `json:"references"`
}

// BatchProgress 表示批量处理的进度信息
type BatchProgress struct {
	Total     int     // 总任务数
	Completed int     // 已完成数量
	Progress  float64 // 完成百分比
	Current   string  // 当前处理的URL
}

// BatchCreateBitlinkResult 表示批量创建短链接的单个结果
type BatchCreateBitlinkResult struct {
	LongURL string           // 原始长链接
	Result  *BitlinkResponse // 创建成功时的响应
	Error   error            // 创建失败时的错误信息
}

// BatchOptions 表示批量创建的选项
type BatchOptions struct {
	Concurrency      int                 // 并发数量
	RetryCount       int                 // 失败重试次数
	RetryDelay       time.Duration       // 重试间隔时间
	ProgressCallback func(BatchProgress) // 进度回调函数
}

// DefaultBatchOptions 返回默认的批量处理选项
func DefaultBatchOptions() BatchOptions {
	return BatchOptions{
		Concurrency: 5,
		RetryCount:  3,
		RetryDelay:  time.Second * 2,
	}
}

// NewClient 创建一个新的 Bitly API 客户端实例
func NewClient() *Client {
	return &Client{
		HTTPClient: &http.Client{},
	}
}

// APIError 表示 API 返回的错误信息
type APIError struct {
	Message string `json:"message"`
	Errors  []struct {
		Field   string `json:"field"`
		Message string `json:"message"`
	} `json:"errors"`
}

// CreateBitlink 创建一个新的短链接
// 参数 req 包含了创建短链接所需的所有信息
// 返回创建的短链接信息和可能的错误
func (c *Client) CreateBitlink(req CreateBitlinkRequest) (*BitlinkResponse, error) {
	// 将请求参数转换为 JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建 HTTP 请求
	request, err := http.NewRequest("POST", BaseURL+"/bitlinks", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 设置请求头
	request.Header.Set("Authorization", "Bearer "+Token)
	request.Header.Set("Content-Type", "application/json")

	// 发送请求
	response, err := c.HTTPClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer response.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应数据失败: %w", err)
	}

	// 检查响应状态码
	if response.StatusCode != http.StatusOK && response.StatusCode != http.StatusCreated {
		// 尝试解析错误信息
		var apiErr APIError
		if err := json.Unmarshal(body, &apiErr); err == nil && apiErr.Message != "" {
			return nil, fmt.Errorf("API错误: %s", apiErr.Message)
		}
		return nil, fmt.Errorf("API响应状态码异常(%d): %s", response.StatusCode, string(body))
	}

	// 解析响应数据
	var result BitlinkResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	return &result, nil
}

// BatchCreateBitlinks 批量创建短链接
// 参数 requests 包含多个创建短链接的请求
// 参数 opts 指定批量处理的选项
// 返回创建结果的切片，保证结果顺序与输入顺序一致
func (c *Client) BatchCreateBitlinks(requests []CreateBitlinkRequest, opts BatchOptions) []BatchCreateBitlinkResult {
	if opts.Concurrency <= 0 {
		opts.Concurrency = DefaultBatchOptions().Concurrency
	}

	// 创建工作通道
	jobs := make(chan struct {
		index int
		req   CreateBitlinkRequest
	}, len(requests))

	results := make(chan struct {
		index  int
		result BatchCreateBitlinkResult
	}, len(requests))

	// 创建进度追踪器
	progress := &struct {
		sync.Mutex
		completed int
	}{}

	// 启动工作协程池
	var wg sync.WaitGroup
	for i := 0; i < opts.Concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for job := range jobs {
				var result BatchCreateBitlinkResult
				result.LongURL = job.req.LongURL

				// 带重试的创建请求
				for retry := 0; retry <= opts.RetryCount; retry++ {
					if retry > 0 {
						time.Sleep(opts.RetryDelay)
					}

					resp, err := c.CreateBitlink(job.req)
					if err == nil {
						result.Result = resp
						break
					}

					if retry == opts.RetryCount {
						result.Error = fmt.Errorf("重试 %d 次后失败: %w", retry, err)
					}
				}

				// 更新进度
				progress.Lock()
				progress.completed++
				if opts.ProgressCallback != nil {
					opts.ProgressCallback(BatchProgress{
						Total:     len(requests),
						Completed: progress.completed,
						Progress:  float64(progress.completed) / float64(len(requests)) * 100,
						Current:   job.req.LongURL,
					})
				}
				progress.Unlock()

				results <- struct {
					index  int
					result BatchCreateBitlinkResult
				}{job.index, result}
			}
		}()
	}

	// 发送任务到工作通道
	for i, req := range requests {
		jobs <- struct {
			index int
			req   CreateBitlinkRequest
		}{i, req}
	}
	close(jobs)

	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果并按原始顺序排序
	allResults := make([]BatchCreateBitlinkResult, len(requests))
	for result := range results {
		allResults[result.index] = result.result
	}

	return allResults
}
