// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgMessage = "x_tg_messages"

// XTgMessage Tg机器人消息推送
type XTgMessage struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID   int32     `gorm:"column:SellerId;not null;comment:运营商" json:"SellerId"`                                  // 运营商
	Message    string    `gorm:"column:Message;not null;comment:文案消息" json:"Message"`                                   // 文案消息
	State      int32     `gorm:"column:State;not null;default:2;comment:发送状态 1:已发送 2:等待发送 3:已撤销 4:正在发送" json:"State"`   // 发送状态 1:已发送 2:等待发送 3:已撤销 4:正在发送
	Robots     string    `gorm:"column:Robots;comment:需要发送的机器人ID，逗号隔开" json:"Robots"`                                   // 需要发送的机器人ID，逗号隔开
	PushType   int32     `gorm:"column:PushType;not null;default:1;comment:推送类型 1:群 2:用户 3:指定用户 4:指定群" json:"PushType"` // 推送类型 1:群 2:用户 3:指定用户 4:指定群
	Users      string    `gorm:"column:Users;comment:推送名单，逗号隔开" json:"Users"`                                           // 推送名单，逗号隔开
	RobotType  int32     `gorm:"column:RobotType;not null;default:1;comment:机器人类型 1:中文机器人 2:英文机器人" json:"RobotType"`    // 机器人类型 1:中文机器人 2:英文机器人
	PushTime   time.Time `gorm:"column:PushTime;not null;default:CURRENT_TIMESTAMP;comment:推送时间" json:"PushTime"`       // 推送时间
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`   // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`   // 更新时间
	Operator   string    `gorm:"column:Operator;not null;comment:操作人" json:"Operator"`                                  // 操作人
}

// TableName XTgMessage's table name
func (*XTgMessage) TableName() string {
	return TableNameXTgMessage
}
