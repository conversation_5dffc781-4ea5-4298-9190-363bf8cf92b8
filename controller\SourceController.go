package controller

import (
	"xserver/abugo"
	"xserver/server"
)

type SourceController struct {
}

func (c *SourceController) Init() {
	group := server.Http().NewGroup("/api/source")
	{
		group.Post("/list", c.list)
		group.Post("/add", c.add)
		group.Post("/modify", c.modify)
		group.Post("/del", c.del)
	}
}

func (c *SourceController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page        int
		PageSize    int
		SellerId    int
		SourceName  string
		Country     string
		SourceLevel int
		PhoneNum    string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "资源管理", "拼多多分享名单", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "SourceName", "=", reqdata.SourceName, "")
	where.Add("and", "Country", "=", reqdata.Country, "")
	where.Add("and", "SourceLevel", "=", reqdata.SourceLevel, 0)
	where.Add("and", "PhoneNum", "=", reqdata.PhoneNum, "")
	total, data := server.Db().Table("x_spin_share_account").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *SourceController) add(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Data     []struct {
			SourceName  string
			Country     string
			SourceLevel int
			PhoneNum    string
			Memo        string
		}
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "资源管理", "拼多多分享名单", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	for i := 0; i < len(reqdata.Data); i++ {
		server.XDb().Table("x_spin_share_account").Insert(map[string]interface{}{
			"SellerId":      reqdata.SellerId,
			"SourceName":    reqdata.Data[i].SourceName,
			"Country":       reqdata.Data[i].Country,
			"SourceLevel":   reqdata.Data[i].SourceLevel,
			"PhoneNum":      reqdata.Data[i].PhoneNum,
			"Memo":          reqdata.Data[i].Memo,
			"CreateAccount": token.Account,
		})
	}
	ctx.RespOK()
}

func (c *SourceController) modify(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId    int
		Id          int
		SourceName  string
		Country     string
		SourceLevel int
		PhoneNum    string
		Memo        string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "资源管理", "拼多多分享名单", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	server.XDb().Table("x_spin_share_account").Where("SellerId = ? and Id = ?", reqdata.SellerId, reqdata.Id).Update(map[string]interface{}{
		"SourceName":    reqdata.SourceName,
		"Country":       reqdata.Country,
		"SourceLevel":   reqdata.SourceLevel,
		"PhoneNum":      reqdata.PhoneNum,
		"Memo":          reqdata.Memo,
		"CreateAccount": token.Account,
	})
	ctx.RespOK()
}

func (c *SourceController) del(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Id       []int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "资源管理", "拼多多分享名单", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	for i := 0; i < len(reqdata.Id); i++ {
		server.XDb().Table("x_spin_share_account").Where("SellerId = ? and Id = ?", reqdata.SellerId, reqdata.Id[i]).Delete()
	}
	ctx.RespOK()
}
