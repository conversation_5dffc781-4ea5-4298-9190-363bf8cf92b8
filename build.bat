

@echo off
chcp 65001
REM 设置Go的环境变量（可选，确保go已加入环境变量路径）
SET GOOS=linux
SET GOARCH=amd64

REM 编译目标路径
SET INPUT_PATH=adminapi.go
SET OUTPUT_NAME=adminapi

REM 检查文件是否存在，如果存在则删除
if exist %OUTPUT_NAME% (
    echo 发现已存在的输出文件，正在删除...
    del /f %OUTPUT_NAME%
    if errorlevel 1 (
        echo 删除文件失败！
        exit /b 1
    )
    echo 旧文件已删除
)

REM 编译
echo 正在编译为 Linux 版本...
go build -o %OUTPUT_NAME% %INPUT_PATH%

IF %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    exit /b %ERRORLEVEL%
) ELSE (
    echo 编译成功，生成文件：%OUTPUT_NAME%
)
pause






