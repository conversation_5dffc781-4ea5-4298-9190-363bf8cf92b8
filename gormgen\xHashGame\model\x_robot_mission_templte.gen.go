// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotMissionTemplte = "x_robot_mission_templte"

// XRobotMissionTemplte 活动任务模板配置
type XRobotMissionTemplte struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                       // pk
	Name       string    `gorm:"column:name;not null;comment:模板名称" json:"name"`                                      // 模板名称
	FullName   string    `gorm:"column:full_name;comment:全部名称" json:"full_name"`                                     // 全部名称
	Types      int32     `gorm:"column:types;comment:模板类型：0：未知，1：邀请任务，2：充值任务，3：流水任务，4：对局任务" json:"types"`            // 模板类型：0：未知，1：邀请任务，2：充值任务，3：流水任务，4：对局任务
	UserTypes  int32     `gorm:"column:user_types;comment:推送用户类型：0： 全部 1：在库，2：不在库，3；领U用户，4领Trx用户" json:"user_types"` // 推送用户类型：0： 全部 1：在库，2：不在库，3；领U用户，4领Trx用户
	DataText   string    `gorm:"column:data_text;comment:Json文本配置信息" json:"data_text"`                               // Json文本配置信息
	Remark     string    `gorm:"column:remark;comment:备注" json:"remark"`                                             // 备注
	IsTemplate int32     `gorm:"column:is_template;comment:是否为模板0:不是 1是" json:"is_template"`                         // 是否为模板0:不是 1是
	IsDel      int32     `gorm:"column:is_del;comment:是否删除" json:"is_del"`                                           // 是否删除
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP" json:"update_time"`
}

// TableName XRobotMissionTemplte's table name
func (*XRobotMissionTemplte) TableName() string {
	return TableNameXRobotMissionTemplte
}
