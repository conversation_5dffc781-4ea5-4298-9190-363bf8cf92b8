// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXChatHongbaoCondition = "x_chat_hongbao_condition"

// XChatHongbaoCondition mapped from table <x_chat_hongbao_condition>
type XChatHongbaoCondition struct {
	ID                int32   `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID          int32   `gorm:"column:SellerId;not null" json:"SellerId"`
	DayMax            int32   `gorm:"column:DayMax;not null" json:"DayMax"`
	OnlineTime        int32   `gorm:"column:OnlineTime;not null;comment:minute" json:"OnlineTime"` // minute
	RechargeNum       float64 `gorm:"column:RechargeNum;not null" json:"RechargeNum"`
	MemberRechargeNum float64 `gorm:"column:MemberRechargeNum;not null" json:"MemberRechargeNum"`
	MemberSum         int32   `gorm:"column:MemberSum;not null" json:"MemberSum"`
}

// TableName XChatHongbaoCondition's table name
func (*XChatHongbaoCondition) TableName() string {
	return TableNameXChatHongbaoCondition
}
