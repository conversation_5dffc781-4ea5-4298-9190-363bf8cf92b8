package msg

import (
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

// SellerChannelService 运营商和渠道服务接口
type SellerChannelService interface {
	// GetSellers 获取所有运营商列表
	GetSellers() ([]map[string]interface{}, error)
	// GetChannelsBySellerId 根据运营商ID获取渠道列表
	GetChannelsBySellerId(sellerId int64) ([]map[string]interface{}, error)
	// ValidateTopAgentIds 验证顶级代理ID是否存在
	ValidateTopAgentIds(topAgentIds string) ([]map[string]interface{}, error)
	// ValidateUserIds 验证用户ID是否存在
	ValidateUserIds(userIds string) ([]map[string]interface{}, error)
}

// sellerChannelServiceImpl 运营商和渠道服务实现
type sellerChannelServiceImpl struct {
	db *abugo.AbuDb
}

// GetSellers 获取所有运营商列表
func (s *sellerChannelServiceImpl) GetSellers() ([]map[string]interface{}, error) {
	// 查询所有状态为启用的运营商
	where := abugo.AbuDbWhere{}
	where.Add("and", "State", "=", "1", nil)

	sellers, err := server.Db().Table("x_seller").
		Select("SellerId, SellerName").
		Where(where).
		OrderBy("SellerId ASC").
		GetList()

	if err != nil {
		return nil, fmt.Errorf("获取运营商列表失败: %w", err)
	}

	return *sellers, nil
}

// GetChannelsBySellerId 根据运营商ID获取渠道列表
func (s *sellerChannelServiceImpl) GetChannelsBySellerId(sellerId int64) ([]map[string]interface{}, error) {
	// 查询指定运营商下的所有状态为启用的渠道
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", sellerId, nil)
	where.Add("and", "State", "=", "1", nil)

	channels, err := server.Db().Table("x_channel").
		Select("ChannelId, ChannelName, SellerId").
		Where(where).
		OrderBy("ChannelId ASC").
		GetList()

	if err != nil {
		return nil, fmt.Errorf("获取渠道列表失败: %w", err)
	}

	return *channels, nil
}

// ValidateTopAgentIds 验证顶级代理ID是否存在
func (s *sellerChannelServiceImpl) ValidateTopAgentIds(topAgentIds string) ([]map[string]interface{}, error) {
	if topAgentIds == "" {
		return []map[string]interface{}{}, nil
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}
	where.Add("and", "TopAgentId", "in", "("+topAgentIds+")", nil)

	// 查询顶级代理信息
	agents, err := server.Db().Table("x_user").
		Select("TopAgentId, NickName, RealName,Account").
		Where(where).
		OrderBy("TopAgentId ASC").
		GetList()

	if err != nil {
		return nil, fmt.Errorf("验证顶级代理ID失败: %w", err)
	}

	// 如果没有找到任何代理，返回空数组
	if agents == nil {
		return []map[string]interface{}{}, nil
	}

	return *agents, nil
}

// ValidateUserIds 验证用户ID是否存在
func (s *sellerChannelServiceImpl) ValidateUserIds(userIds string) ([]map[string]interface{}, error) {
	if userIds == "" {
		return []map[string]interface{}{}, nil
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "in", "("+userIds+")", nil)

	// 查询用户信息
	users, err := server.Db().Table("x_user").
		Select("UserId, NickName, RealName, Account").
		Where(where).
		OrderBy("UserId ASC").
		GetList()

	if err != nil {
		return nil, fmt.Errorf("验证用户ID失败: %w", err)
	}

	// 如果没有找到任何用户，返回空数组
	if users == nil {
		return []map[string]interface{}{}, nil
	}

	return *users, nil
}

// NewSellerChannelService 创建运营商和渠道服务实例
func NewSellerChannelService() SellerChannelService {
	return &sellerChannelServiceImpl{
		db: server.Db(),
	}
}
