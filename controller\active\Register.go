package active

import (
	"encoding/json"
	"errors"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

// RegisterGiftBaseConfig 注册赠送活动基础配置
type RegisterGiftBaseConfig struct {
	RequireDuringRegistration int32    `json:"RequireDuringRegistration"` // 是否要求在注册时参与，0=否，1=是
	MaxIPAttempts             int32    `json:"MaxIPAttempts"`             // 同一IP最大领取次数，0表示不限制
	BlockedIPList             string   `json:"BlockedIPList"`             // 黑名单IP列表，多个IP用逗号分隔
	ApplicableGames           int32    `json:"ApplicableGames"`           // 适用游戏范围，0=全站通用，1=指定游戏
	RewardAmount              float32  `json:"RewardAmount"`              // 奖励金额
	RewardWalletType          int32    `json:"RewardWalletType"`          // 奖励账户类型，0=真金账户，1=彩金账户
	UnlockType                int32    `json:"UnlockType"`                // 解锁类型，0=无要求，1=充值
	UnlockAmount              int32    `json:"UnlockAmount"`              // 解锁所需金额
	CanPlayAfterBonus         int32    `json:"CanPlayAfterBonus"`         // 领取奖励后是否可玩游戏，0=否，1=是
	WagerRequirement          int32    `json:"WagerRequirement"`          // 提取奖励流水要求，0=倍数，1=金额
	WagerMultiple             []int32  `json:"WagerMultiple"`             // 提取奖励流水倍数[充值流水倍数,奖励流水倍数]
	WagerAmount               int32    `json:"WagerAmount"`               // 提取奖励固定流水金额
	RewardAmountType          int32    `json:"RewardAmountType"`          // 奖励金额类型，0=奖励金额，1=充值金额，2=充值与奖励金额
	RechargeTurnover          int32    `json:"RechargeTurnover"`          // 提现所需充值流水
	RewardTurnover            int32    `json:"RewardTurnover"`            // 提现所需奖励流水
	RechargeRewardTurnover    [2]int32 `json:"RechargeRewardTurnover"`    // 提现所需充值与奖励流水组合
}

// RegisterGiftCheckParameter注册赠送活动参数校验
func RegisterGiftCheckParameter(reqdata DefineModReq) error {
	// 检查基础配置
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig RegisterGiftBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}

		// 检查MaxIPAttempts
		if baseConfig.MaxIPAttempts < 0 {
			return errors.New("同IP最大领取次数必须大于等于0")
		}

		// 检查ApplicableGames
		if baseConfig.ApplicableGames < 0 || baseConfig.ApplicableGames > 1 {
			return errors.New("适用游戏范围必须为0或1")
		}

		// 检查GameType
		if baseConfig.ApplicableGames == 1 && reqdata.GameType == "" {
			return errors.New("当适用游戏范围为指定游戏时，必须选择游戏类型")
		}

		// 检查UnlockType
		if baseConfig.UnlockType < 0 || baseConfig.UnlockType > 1 {
			return errors.New("解锁类型必须为0或1")
		}

		// 检查RewardWalletType
		if baseConfig.RewardWalletType < 0 || baseConfig.RewardWalletType > 1 {
			return errors.New("奖励账户类型必须为0或1")
		}

		// 检查UnlockAmount
		if baseConfig.UnlockAmount < 0 {
			return errors.New("解锁奖励要求值不能小于0")
		}

		// 检查RequireDuringRegistration
		if baseConfig.RequireDuringRegistration < 0 || baseConfig.RequireDuringRegistration > 1 {
			return errors.New("是否要求在注册时参与值必须为0或1")
		}

		// 检查奖励金额
		if baseConfig.RewardAmount < 0 {
			return errors.New("奖励金额不能小于0")
		}

		// 检查CanPlayAfterBonus
		if baseConfig.CanPlayAfterBonus < 0 || baseConfig.CanPlayAfterBonus > 1 {
			return errors.New("领取奖励后是否立即可玩游戏值必须为0或1")
		}

		// 检查WagerRequirement
		if baseConfig.WagerRequirement < 0 || baseConfig.WagerRequirement > 1 {
			return errors.New("提取奖励需打流水要求值必须为0或1")
		}

		// 根据WagerRequirement检查相关字段
		if baseConfig.WagerRequirement == 0 { // 倍数
			if len(baseConfig.WagerMultiple) == 0 {
				return errors.New("提取奖励需打流水倍数不能小于0")
			}
			// 检查RewardAmountType
			if baseConfig.RewardAmountType < 0 || baseConfig.RewardAmountType > 2 {
				return errors.New("奖励金额类型必须为0(奖励金额)、1(充值金额)或2(充值与奖励金额)")
			}
			// 使用switch结构处理不同的RewardAmountType值
			switch baseConfig.RewardAmountType {
			case 0: // 表示选择的是奖励金额
				if baseConfig.RewardAmount < 0 {
					return errors.New("奖励金额不能小于0")
				}
			case 1: // 表示选择的是充值金额
				if baseConfig.UnlockAmount < 0 {
					return errors.New("解锁金额不能小于0")
				}
			case 2: // 表示选择的是充值与奖励金额
				if baseConfig.UnlockAmount < 0 {
					return errors.New("充值金额不能小于0")
				}
				if baseConfig.RewardAmount < 0 {
					return errors.New("奖励金额不能小于0")
				}
			}
		} else { // 金额
			if baseConfig.WagerAmount < 0 {
				return errors.New("提取奖励需打固定金额不能小于0")
			}
		}
	}
	return nil
}

// ProcessRegisterGiftWagerMultiple 处理注册赠送活动的流水倍数计算
func ProcessRegisterGiftWagerMultiple(reqdata *DefineModReq) error {
	var baseConfig RegisterGiftBaseConfig
	err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
	if err != nil {
		return errors.New("基础参数格式错误")
	}

	// 验证额外参数
	if baseConfig.ApplicableGames < 0 || baseConfig.ApplicableGames > 1 {
		return errors.New("适用游戏范围必须为0或1")
	}
	if baseConfig.RewardWalletType < 0 || baseConfig.RewardWalletType > 1 {
		return errors.New("奖励账户类型必须为0或1")
	}
	if baseConfig.RequireDuringRegistration < 0 || baseConfig.RequireDuringRegistration > 1 {
		return errors.New("是否要求在注册时参与值必须为0或1")
	}
	if baseConfig.CanPlayAfterBonus < 0 || baseConfig.CanPlayAfterBonus > 1 {
		return errors.New("领取奖励后是否立即可玩游戏值必须为0或1")
	}
	if baseConfig.UnlockAmount < 0 {
		return errors.New("解锁奖励要求必须大于等于0")
	}

	// 验证流水相关字段
	if baseConfig.WagerRequirement == 0 {
		if baseConfig.RewardAmountType < 0 || baseConfig.RewardAmountType > 2 {
			return errors.New("奖励金额类型必须为0(奖励金额)、1(充值金额)或2(充值与奖励金额)")
		}

		// 计算按照倍数计算的奖励金额
		switch baseConfig.RewardAmountType {
		case 0:
			if len(baseConfig.WagerMultiple) > 0 {
				baseConfig.RechargeTurnover = int32(baseConfig.RewardAmount * float32(baseConfig.WagerMultiple[0]))
			}
		case 1:
			if len(baseConfig.WagerMultiple) > 0 {
				baseConfig.RechargeTurnover = int32(baseConfig.RewardAmount * float32(baseConfig.WagerMultiple[0]))
			}
		case 2:
			if len(baseConfig.WagerMultiple) > 1 {
				baseConfig.RechargeRewardTurnover[0] = int32(float32(baseConfig.RechargeTurnover) * float32(baseConfig.WagerMultiple[0]))
				baseConfig.RechargeRewardTurnover[1] = int32(float32(baseConfig.RewardTurnover) * float32(baseConfig.WagerMultiple[1]))
			}
		}

		// 重新序列化BaseConfig
		baseConfigBytes, err := json.Marshal(baseConfig)
		if err != nil {
			return errors.New("基础参数序列化失败")
		}
		reqdata.BaseConfig = string(baseConfigBytes)
	}

	return nil
}

// HandleRegisterGiftAdd 处理注册赠送活动的添加
func HandleRegisterGiftAdd(ctx *abugo.AbuHttpContent, reqdata struct {
	model.XActiveDefine
	GoogleCode string
}, errcode *int) {
	// 检查注册赠送活动参数
	reqDataForCheck := DefineModReq{
		BaseConfig: reqdata.BaseConfig,
		Config:     reqdata.Config,
		ActiveId:   int(reqdata.ActiveID),
	}
	err := RegisterGiftCheckParameter(reqDataForCheck)
	if err != nil {
		ctx.RespErrString(true, errcode, err.Error())
		return
	}

	// 解析BaseConfig以确保新参数正确
	var baseConfig RegisterGiftBaseConfig
	err = json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
	if err != nil {
		ctx.RespErrString(true, errcode, "基础参数格式错误")
		return
	}

	// 验证额外参数
	if baseConfig.ApplicableGames < 0 || baseConfig.ApplicableGames > 1 {
		ctx.RespErrString(true, errcode, "适用游戏范围必须为0或1")
		return
	}
	if baseConfig.RewardWalletType < 0 || baseConfig.RewardWalletType > 1 {
		ctx.RespErrString(true, errcode, "奖励账户类型必须为0或1")
		return
	}
	if baseConfig.RequireDuringRegistration < 0 || baseConfig.RequireDuringRegistration > 1 {
		ctx.RespErrString(true, errcode, "是否要求在注册时参与值必须为0或1")
		return
	}
	if baseConfig.CanPlayAfterBonus < 0 || baseConfig.CanPlayAfterBonus > 1 {
		ctx.RespErrString(true, errcode, "领取奖励后是否立即可玩游戏值必须为0或1")
		return
	}
	if baseConfig.UnlockType < 0 || baseConfig.UnlockType > 1 {
		ctx.RespErrString(true, errcode, "解锁类型必须为0或1")
		return
	}
	// 验证流水相关字段
	if baseConfig.WagerRequirement == 0 {
		if baseConfig.RewardAmountType < 0 || baseConfig.RewardAmountType > 2 {
			ctx.RespErrString(true, errcode, "奖励金额类型必须为0(奖励金额)、1(充值金额)或2(充值与奖励金额)")
			return
		}
		// 计算按照倍数计算的奖励金额
		switch baseConfig.RewardAmountType {
		case 0: // 奖励金额提现所需流水
			if len(baseConfig.WagerMultiple) > 0 {
				baseConfig.RewardTurnover = int32(baseConfig.RewardAmount * float32(baseConfig.WagerMultiple[0]))
			}
		case 1: // 充值金额提现所需流水
			if len(baseConfig.WagerMultiple) > 0 {
				baseConfig.RewardTurnover = int32(baseConfig.RewardAmount * float32(baseConfig.WagerMultiple[0]))
			}
		case 2: // 充值与奖励金额提现所需流水
			if len(baseConfig.WagerMultiple) > 1 {
				baseConfig.RechargeRewardTurnover[0] = int32(float32(baseConfig.RechargeTurnover) * float32(baseConfig.WagerMultiple[0]))
				baseConfig.RechargeRewardTurnover[1] = int32(float32(baseConfig.RewardTurnover) * float32(baseConfig.WagerMultiple[1]))
			}
		}
	}
	reqdata.AuditType = 2 // 设置为自动审核/自动发放
	// 创建活动记录
	tb := &reqdata.XActiveDefine
	// 如果是指定游戏范围，确保 GameType 字段已设置
	if baseConfig.ApplicableGames == 1 {
		if tb.GameType == "" {
			ctx.RespErrString(true, errcode, "当适用游戏范围为指定游戏时，必须选择游戏类型")
			return
		} else {
			tb.GameType = reqdata.GameType
		}
	}
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	err = xActiveDefine.WithContext(ctx.Gin()).Create(tb)
	if ctx.RespErr(err, errcode) {
		return
	}
	err = SaveActiveSort(tb.ID, tb.Sort, tb.TopSort)
	if ctx.RespErr(err, errcode) {
		return
	}
	ctx.RespOK()
}
