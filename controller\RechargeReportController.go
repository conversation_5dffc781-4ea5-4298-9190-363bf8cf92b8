package controller

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/shopspring/decimal"
	"strings"
	"xserver/abugo"
	"xserver/server"
)

type RechargeReportController struct{}

func (c *RechargeReportController) Init() {
	group := server.Http().NewGroup("/api/recharge")
	{
		group.Post("/report", c.report)                    // 支付成功率
		group.Post("/recommend", c.recommend)              // 推荐配置
		group.Post("/recommend_update", c.recommendUpdate) // 推荐配置
	}
}

func (c *RechargeReportController) report(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		StartTime string
		EndTime   string
		Symbol    string
	}
	req := RequestData{}
	errCode := 0
	if err := ctx.RequestData(&req); ctx.RespErr(err, &errCode) {
		return
	}

	startTime := carbon.Now().StartOfDay().StdTime()
	endTime := carbon.Now().EndOfDay().StdTime()
	if req.StartTime != "" && req.EndTime != "" {
		startTime = carbon.Parse(req.StartTime).StdTime()
		endTime = carbon.Parse(req.EndTime).StdTime()
	}

	logs.Info(startTime)
	logs.Info(endTime)
	logs.Info(req.Symbol)

	type ResultData struct {
		Brand               string  `json:"Brand" gorm:"column:Brand"`
		Symbol              string  `json:"Symbol" gorm:"column:Symbol"`
		RechargeCnt         int64   `json:"RechargeCnt" gorm:"column:RechargeCnt"`
		RechargeSuccessCnt  int64   `json:"RechargeSuccessCnt" gorm:"column:RechargeSuccessCnt"`
		RechargeTotalAmount float64 `json:"RechargeTotalAmount" gorm:"column:RechargeTotalAmount"`
		RechargeRate        float64 `json:"RechargeRate" gorm:"column:RechargeRate"`
		WithdrawCnt         int64   `json:"WithdrawCnt" gorm:"column:WithdrawCnt"`
		WithdrawTotalAmount float64 `json:"WithdrawTotalAmount" gorm:"column:WithdrawTotalAmount"`
		WithdrawSuccessCnt  int64   `json:"WithdrawSuccessCnt" gorm:"column:WithdrawSuccessCnt"`
		WithdrawRate        float64 `json:"WithdrawRate" gorm:"column:WithdrawRate"`
	}

	var results []ResultData
	xFinance := server.DaoxHashGame().XFinanceMethod
	xRecharge := server.DaoxHashGame().XRecharge
	xWithdraw := server.DaoxHashGame().XWithdraw

	query := xFinance.WithContext(ctx.Gin()).Select(xFinance.Brand, xFinance.Symbol).
		Where(xFinance.Ftype.Eq(1)).
		Where(xFinance.Rtype.In(3, 4, 5)).
		Group(xFinance.Brand, xFinance.Symbol).
		Order(xFinance.Brand)

	if req.Symbol != "" {
		query.Where(xFinance.Symbol.Eq(req.Symbol))
	}

	err := query.Scan(&results)

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	type RechargePaidResult struct {
		Cnt         int64   `json:"Cnt" gorm:"column:Cnt"`
		TotalAmount float64 `json:"TotalAmount" gorm:"column:TotalAmount"`
		PaidCnt     int64   `json:"PaidCnt" gorm:"column:PaidCnt"`
		PaidRate    float64 `json:"PaidRate" gorm:"column:PaidRate"`
	}

	type WithdrawPaidResult struct {
		Cnt         int64   `json:"Cnt" gorm:"column:Cnt"`
		TotalAmount float64 `json:"TotalAmount" gorm:"column:TotalAmount"`
		PaidCnt     int64   `json:"PaidCnt" gorm:"column:PaidCnt"`
		PaidRate    float64 `json:"PaidRate" gorm:"column:PaidRate"`
	}

	for i, _ := range results {
		var rechargeResult RechargePaidResult
		var withdrawResult WithdrawPaidResult

		count, _ := xRecharge.WithContext(nil).LeftJoin(xFinance, xFinance.ID.EqCol(xRecharge.PayID)).
			Where(xFinance.Brand.Eq(results[i].Brand)).
			Where(xFinance.Symbol.Eq(results[i].Symbol)).
			Where(xRecharge.CreateTime.Gte(startTime)).
			Where(xRecharge.CreateTime.Lte(endTime)).
			Count()

		paidCount, _ := xRecharge.WithContext(nil).LeftJoin(xFinance, xFinance.ID.EqCol(xRecharge.PayID)).
			Where(xFinance.Brand.Eq(results[i].Brand)).
			Where(xFinance.Symbol.Eq(results[i].Symbol)).
			Where(xRecharge.CreateTime.Gte(startTime)).
			Where(xRecharge.CreateTime.Lte(endTime)).
			Where(xRecharge.State.Eq(5)).
			Count()

		xRecharge.WithContext(nil).LeftJoin(xFinance, xFinance.ID.EqCol(xRecharge.PayID)).
			Select(xRecharge.Amount.Sum().As("TotalAmount")).
			Where(xFinance.Brand.Eq(results[i].Brand)).
			Where(xFinance.Symbol.Eq(results[i].Symbol)).
			Where(xRecharge.CreateTime.Gte(startTime)).
			Where(xRecharge.CreateTime.Lte(endTime)).
			Where(xRecharge.State.Eq(5)).
			Scan(&rechargeResult)

		wCount, _ := xWithdraw.WithContext(nil).LeftJoin(xFinance, xFinance.ID.EqCol(xWithdraw.PayID)).
			Where(xFinance.Brand.Eq(results[i].Brand)).
			Where(xFinance.Symbol.Eq(results[i].Symbol)).
			Where(xWithdraw.CreateTime.Gte(startTime)).
			Where(xWithdraw.CreateTime.Lte(endTime)).
			Count()

		wPaidCount, _ := xWithdraw.WithContext(nil).LeftJoin(xFinance, xFinance.ID.EqCol(xWithdraw.PayID)).
			Where(xFinance.Brand.Eq(results[i].Brand)).
			Where(xFinance.Symbol.Eq(results[i].Symbol)).
			Where(xWithdraw.CreateTime.Gte(startTime)).
			Where(xWithdraw.CreateTime.Lte(endTime)).
			Where(xWithdraw.State.Eq(5)).
			Count()

		xWithdraw.WithContext(nil).LeftJoin(xFinance, xFinance.ID.EqCol(xWithdraw.PayID)).
			Select(xWithdraw.Amount.Sum().As("TotalAmount")).
			Where(xFinance.Brand.Eq(results[i].Brand)).
			Where(xFinance.Symbol.Eq(results[i].Symbol)).
			Where(xWithdraw.CreateTime.Gte(startTime)).
			Where(xWithdraw.CreateTime.Lte(endTime)).
			Where(xWithdraw.State.Eq(6)).
			Scan(&withdrawResult)

		//server.Db().Gorm().Table("x_recharge").Select(`
		//	COUNT(x_recharge.Id) AS Cnt,
		//	COUNT( CASE WHEN x_recharge.State = 5 THEN x_recharge.Id END) AS PaidCnt,
		//	SUM( CASE WHEN x_recharge.State = 5 THEN Amount END) AS TotalAmount,
		//	ROUND(COUNT(CASE WHEN x_recharge.State = 5 THEN x_recharge.Id END) / COUNT(x_recharge.Id), 2) AS PaidRate
		//`).
		//	Joins("LEFT JOIN x_finance_method ON x_recharge.PayId = x_finance_method.Id").
		//	Where("x_finance_method.Brand = ? AND x_finance_method.Symbol = ?", results[i].Brand, results[i].Symbol).
		//	Where("x_recharge.CreateTime >= ? and x_recharge.CreateTime <= ?", startTime, endTime).
		//	First(&rechargeResult)

		//server.Db().Gorm().Table("x_withdraw").Select(`
		//	COUNT(x_withdraw.Id) AS Cnt,
		//	COUNT( CASE WHEN x_withdraw.State = 6 THEN x_withdraw.Id END) AS PaidCnt,
		//	SUM( CASE WHEN x_withdraw.State = 6 THEN Amount END) AS TotalAmount,
		//	ROUND(COUNT(CASE WHEN x_withdraw.State = 6 THEN x_withdraw.Id END) / COUNT(x_withdraw.Id), 2) AS PaidRate
		//`).
		//	Joins("LEFT JOIN x_finance_method ON x_withdraw.PayId = x_finance_method.Id").
		//	Where("x_finance_method.Brand = ? AND x_finance_method.Symbol = ?", results[i].Brand, results[i].Symbol).
		//	Where("x_withdraw.CreateTime >= ? and x_withdraw.CreateTime <= ?", startTime, endTime).
		//	First(&withdrawResult)

		results[i].Symbol = strings.ToUpper(results[i].Symbol)
		results[i].RechargeCnt = count
		results[i].RechargeSuccessCnt = paidCount
		results[i].RechargeTotalAmount = rechargeResult.TotalAmount

		if count > 0 {
			rate, _ := decimal.NewFromFloat(float64(paidCount)).
				Div(decimal.NewFromFloat(float64(count))).
				Float64()

			results[i].RechargeRate, _ = decimal.NewFromFloat(rate).Mul(decimal.NewFromInt(100)).Round(2).Float64()
		} else {
			results[i].RechargeRate = 0
		}

		results[i].WithdrawCnt = wCount
		results[i].WithdrawSuccessCnt = wPaidCount
		results[i].WithdrawTotalAmount = withdrawResult.TotalAmount
		if wCount > 0 {
			rate, _ := decimal.NewFromFloat(float64(wPaidCount)).
				Div(decimal.NewFromFloat(float64(wCount))).
				Float64()
			results[i].WithdrawRate, _ = decimal.NewFromFloat(rate).Mul(decimal.NewFromInt(100)).Round(2).Float64()
		} else {
			results[i].WithdrawRate = 0
		}

	}

	ctx.RespOK(results)
}

func (c *RechargeReportController) recommend(ctx *abugo.AbuHttpContent) {
	type RecommendConfigData struct {
		Way  int
		Hour int
	}
	var recommendConfig RecommendConfigData
	redisData := server.Redis().HGet("CONFIG", "SET_RECHARGE_RECOMMEND")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)

		// 反序列化为 setOnlineCntData
		if err := json.Unmarshal([]byte(dataStr), &recommendConfig); err != nil {
			fmt.Println("JSON 反序列化失败:", err)
			return
		}
	}

	ctx.RespOK(recommendConfig)

}

func (c *RechargeReportController) recommendUpdate(ctx *abugo.AbuHttpContent) {
	type RecommendConfigData struct {
		Way  int
		Hour int
	}
	req := RecommendConfigData{}
	errCode := 0
	if err := ctx.RequestData(&req); ctx.RespErr(err, &errCode) {
		return
	}

	var recommendConfig RecommendConfigData

	recommendConfig.Way = req.Way
	recommendConfig.Hour = req.Hour

	server.Redis().HSet("CONFIG", "SET_RECHARGE_RECOMMEND", recommendConfig)

	ctx.RespOK()
}
