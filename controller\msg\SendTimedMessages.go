package msg

import (
	"github.com/beego/beego/logs"
	"time"
	"xserver/controller/msg/model"
	"xserver/server"
)

// SendTimedMessages 定时任务：发送所有定时消息
func SendTimedMessages() {
	logs.Info("-开始执行定时消息发送任务")

	// 获取当前时间
	now := time.Now()

	// 查询所有已到发送时间的定时消息模板
	var templates []model.StationMessageTemplate
	result := server.Db().GormDao().Where("IsTimed = 1 AND Status = 1 AND TimedAt <= ?", now).Find(&templates)
	if result.Error != nil {
		logs.Error("查询定时消息模板失败: %v", result.Error)
		return
	}

	if len(templates) == 0 {
		logs.Info("没有需要发送的定时消息")
		return
	}

	// 创建消息发送服务
	messageSendService := NewMessageSendService()

	// 逐个发送定时消息
	for _, template := range templates {
		logs.Info("-开始发送定时消息，模板ID: %d, 模板别名: %s", template.ID, template.Alias)
		if template.Status == 0 {
			logs.Error("消息模板已经停用，跳过，模板ID: ", template.ID)
			continue
		}
		// 发送消息
		count, err := messageSendService.SendTimedMessage(template.ID)
		if err != nil {
			logs.Error("发送定时消息失败，模板ID: %d, 错误: %v", template.ID, err)
			continue
		}

		logs.Info("定时消息发送成功，模板ID: %d, 发送数量: %d", template.ID, count)

		// 更新模板状态，将定时发送标志设为0（已发送）
		server.Db().GormDao().Model(&model.StationMessageTemplate{}).Where("Id = ?", template.ID).Updates(map[string]interface{}{
			"IsTimed": 0,
		})
	}

	logs.Info("定时消息发送任务完成")
}
