package db

import (
	"context"
	"errors"
	"fmt"
	"time"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/golang-module/carbon/v2"
	"github.com/shopspring/decimal"

	"xserver/gormgen/xHashGame/dao"

	"github.com/beego/beego/logs"
	"gorm.io/gorm"
)

type User struct {
	Id           int     `gorm:"column:Id"`           //id
	UserId       int     `gorm:"column:UserId"`       //玩家
	SellerId     int     `gorm:"column:SellerId"`     //运营商
	Account      string  `gorm:"column:Account"`      //账号
	Password     string  `gorm:"column:Password"`     //登录密码
	NickName     string  `gorm:"column:NickName"`     //昵称
	Token        string  `gorm:"column:Token"`        //登录token
	Agents       string  `gorm:"column:Agents"`       //json数组,所有上级id,下标0是直属代理,越往后,代理等级越高
	TopAgentId   int     `gorm:"column:TopAgentId"`   //顶级代理id
	AgentId      int     `gorm:"column:AgentId"`      //直属代理
	RegisterIp   string  `gorm:"column:RegisterIp"`   //注册ip
	RegisterTime string  `gorm:"column:RegisterTime"` //注册时间
	Address      string  `gorm:"column:Address"`      //地址
	Email        string  `gorm:"column:Email"`        //
	PhoneNum     string  `gorm:"column:PhoneNum"`     //
	IsTopAgent   int     `gorm:"column:IsTopAgent"`   //是否是顶级代理 1是 2不是
	BetTrx       float64 `gorm:"column:BetTrx"`       //Trx下注
	RewardTrx    float64 `gorm:"column:RewardTrx"`    //trx返奖
	LiuSuiTrx    float64 `gorm:"column:LiuSuiTrx"`    //trx流水
	BetUsdt      float64 `gorm:"column:BetUsdt"`      //usdt下注
	RewardUsdt   float64 `gorm:"column:RewardUsdt"`   //usdt返奖
	LiuSuiUsdt   float64 `gorm:"column:LiuSuiUsdt"`   //usdt流水
	IsAgent      int     `gorm:"column:IsAgent"`      //是否是代理 1是,2不是
	LoginTime    string  `gorm:"column:LoginTime"`    //登录时间
	LoginIp      string  `gorm:"column:LoginIp"`      //登录ip
	State        int     `gorm:"column:State"`        //状态 1启用 2禁用
	TgChatId     int     `gorm:"column:TgChatId"`
	TgUserName   string  `gorm:"column:TgUserName"`
}

type UserInfo struct {
	Id                       int             `json:"Id" gorm:"column:Id"`                                             //id
	UserId                   int             `json:"UserId" gorm:"column:UserId"`                                     //用户id
	SellerId                 int             `json:"SellerId" gorm:"column:SellerId"`                                 //代理id
	ChannelId                int             `json:"ChannelId" gorm:"column:ChannelId"`                               //活动渠道id
	Account                  string          `json:"Account" gorm:"column:Account"`                                   //账号
	Password                 string          `json:"Password" gorm:"column:Password"`                                 //密码
	NickName                 string          `json:"NickName" gorm:"column:NickName"`                                 //昵称
	Token                    string          `json:"Token" gorm:"column:Token"`                                       //登陆token
	Agents                   string          `json:"Agents" gorm:"column:Agents"`                                     //json数组,所有上级id,下标0是直属代理,越往后,代理等级越高
	TopAgentId               int             `json:"TopAgentId" gorm:"column:TopAgentId"`                             //顶级代理id
	AgentId                  int             `json:"AgentId" gorm:"column:AgentId"`                                   //直属代理
	RegisterIp               string          `json:"RegisterIp" gorm:"column:RegisterIp"`                             //注册ip
	RegisterTime             utils.MyString  `json:"RegisterTime" gorm:"column:RegisterTime"`                         //注册时间
	Address                  string          `json:"Address" gorm:"column:Address"`                                   //地址
	Email                    string          `json:"Email" gorm:"column:Email"`                                       //邮件地址
	PhoneNum                 string          `json:"PhoneNum" gorm:"column:PhoneNum"`                                 //电话号码
	IsTopAgent               int             `json:"IsTopAgent" gorm:"column:IsTopAgent"`                             //是否顶级代理1 是 2不是
	BetTrx                   decimal.Decimal `json:"BetTrx" gorm:"column:BetTrx"`                                     //trx下注
	RewardTrx                decimal.Decimal `json:"RewardTrx" gorm:"column:RewardTrx"`                               //trx反奖
	LiuSuiTrx                decimal.Decimal `json:"LiuSuiTrx" gorm:"column:LiuSuiTrx"`                               //trx流水
	BetUsdt                  decimal.Decimal `json:"BetUsdt" gorm:"column:BetUsdt"`                                   //usdt下注
	RewardUsdt               decimal.Decimal `json:"RewardUsdt" gorm:"column:RewardUsdt"`                             //usdt反奖
	LiuSuiUsdt               decimal.Decimal `json:"LiuSuiUsdt" gorm:"column:LiuSuiUsdt"`                             //usdt流水
	IsAgent                  int             `json:"IsAgent" gorm:"column:IsAgent"`                                   //是否代理 1 是 2 不是
	LoginTime                utils.MyString  `json:"LoginTime" gorm:"column:LoginTime"`                               //登陆时间
	LoginIp                  string          `json:"LoginIp" gorm:"column:LoginIp"`                                   //登陆ip
	State                    int             `json:"State" gorm:"column:State"`                                       //状态 1启用 2禁用
	FineLiuSuiTrx            decimal.Decimal `json:"FineLiuSuiTrx" gorm:"column:FineLiuSuiTrx"`                       //扣除流水trx
	FineLiuSuiUsdt           decimal.Decimal `json:"FineLiuSuiUsdt" gorm:"column:FineLiuSuiUsdt"`                     //扣除流水usdt
	LastGameInfo             string          `json:"LastGameInfo" gorm:"column:LastGameInfo"`                         //最后玩的游戏
	FenCheng                 decimal.Decimal `json:"FenCheng" gorm:"column:FenCheng"`                                 //分成比例
	AgentCode                string          `json:"AgentCode" gorm:"column:AgentCode"`                               //注册邀请码
	TgChatId                 int64           `json:"TgChatId" gorm:"column:TgChatId"`                                 //电报机器人chat id
	TgUserName               string          `json:"TgUserName" gorm:"column:TgUserName"`                             //电报机器人用户名
	VerifyState              int             `json:"VerifyState" gorm:"column:VerifyState"`                           //地址是否已经被验证
	VerifyAmount             int             `json:"VerifyAmount" gorm:"column:VerifyAmount"`                         //地址验证转账金额
	VerifyTime               utils.MyString  `json:"VerifyTime" gorm:"column:VerifyTime"`                             //地址验证时间
	Amount                   decimal.Decimal `json:"Amount" gorm:"column:Amount"`                                     //账户余额usdt
	LockedAmount             decimal.Decimal `json:"LockedAmount" gorm:"column:LockedAmount"`                         //锁定账户余额
	LastGameInfo6            string          `json:"LastGameInfo6" gorm:"column:LastGameInfo6"`                       //最后一局快3
	LastGameInfo7            string          `json:"LastGameInfo7" gorm:"column:LastGameInfo7"`                       //最后一局pk10
	GameFee                  string          `json:"GameFee" gorm:"column:GameFee"`                                   //个人游戏费率
	VipAmount                decimal.Decimal `json:"VipAmount" gorm:"column:VipAmount"`                               //vip累计金额
	WinAudit                 string          `json:"WinAudit" gorm:"column:WinAudit"`                                 //连赢审核记录
	MaxBet                   string          `json:"MaxBet" gorm:"column:MaxBet"`                                     //单笔最大下注记录
	RechargeAddressTron      string          `json:"RechargeAddressTron" gorm:"column:RechargeAddressTron"`           //充值地址
	WalletPassword           string          `json:"WalletPassword" gorm:"column:WalletPassword"`                     //提现密码
	MaxBetTime               utils.MyString  `json:"MaxBetTime" gorm:"column:MaxBetTime"`                             //降赔重置时间
	JpType                   int             `json:"JpType" gorm:"column:JpType"`                                     //降赔类型
	LastGameInfoEx           string          `json:"LastGameInfoEx" gorm:"column:LastGameInfoEx"`                     //最后玩的游戏
	WithdrawLiuSui           decimal.Decimal `json:"WithdrawLiuSui" gorm:"column:WithdrawLiuSui"`                     //提现流水
	TotalLiuSui              decimal.Decimal `json:"TotalLiuSui" gorm:"column:TotalLiuSui"`                           //当前流水
	RechargeAddressEth       string          `json:"RechargeAddressEth" gorm:"column:RechargeAddressEth"`             //充值地址
	HeadId                   string          `json:"HeadId" gorm:"column:HeadId"`                                     //头像id
	Gender                   string          `json:"Gender" gorm:"column:Gender"`                                     //性别
	DeliveryAddress          string          `json:"DeliveryAddress" gorm:"column:DeliveryAddress"`                   //收货地址
	Birthday                 time.Time       `json:"Birthday" gorm:"column:Birthday"`                                 //生日
	RealName                 string          `json:"RealName" gorm:"column:RealName"`                                 //真实姓名
	IsTest                   int             `json:"IsTest" gorm:"column:IsTest"`                                     //是否测试账号 1是 2 不是
	UpdatePasswordTime       utils.MyString  `json:"UpdatePasswordTime" gorm:"column:UpdatePasswordTime"`             //最后一次修改密码时间
	UpdateWalletPasswordTime utils.MyString  `json:"UpdateWalletPasswordTime" gorm:"column:UpdateWalletPasswordTime"` //最后一次修改资金密码时间
	AuditAmount              decimal.Decimal `json:"AuditAmount" gorm:"column:AuditAmount"`                           //审核金额，反奖超过此金额需要审核
	WinJiangPeiMax           string          `json:"WinJiangPeiMax" gorm:"column:WinJiangPeiMax"`                     //降赔最大金额
	IsPanda                  int             `json:"IsPanda" gorm:"column:IsPanda"`                                   //是否量化用户 1是 2 不是
	BlackMaker               string          `json:"BlackMaker" gorm:"column:BlackMaker"`                             //区块黑名单
	TgName                   string          `json:"TgName" gorm:"column:TgName"`                                     //注册tg
	CsGroup                  string          `json:"CsGroup" gorm:"column:CsGroup"`                                   //客服团队
	CsId                     string          `json:"CsId" gorm:"column:CsId"`                                         //客服工号
	RegGift                  int             `json:"RegGift" gorm:"column:RegGift"`                                   //体验金状态 1无体验金,2可以领取体验金,3已经领取trx体验金,4可以领取u体验金,5已经领取u体验金
	IgnoreWinJiangPei        int             `json:"IgnoreWinJiangPei" gorm:"column:IgnoreWinJiangPei"`               //忽略盈利降赔,1是,2否
	BetCount                 int             `json:"BetCount" gorm:"column:BetCount"`                                 //投注次数
	FirstBetTime             utils.MyString  `json:"FirstBetTime" gorm:"column:FirstBetTime"`                         //首次投注时间
	LastBetTime              utils.MyString  `json:"LastBetTime" gorm:"column:LastBetTime"`                           //最后投注时间
	FirstRechargeTime        utils.MyString  `json:"FirstRechargeTime" gorm:"column:FirstRechargeTime"`               //首次充值时间
	LastRechargeTime         utils.MyString  `json:"LastRechargeTime" gorm:"column:LastRechargeTime"`                 //最后充值时间
	RegUrl                   string          `json:"RegUrl" gorm:"column:RegUrl"`                                     //注册域名
	Memo                     string          `json:"Memo" gorm:"column:Memo"`                                         //备注
	KeFuTgName               string          `json:"KeFuTgName" gorm:"column:KeFuTgName"`                             //客服tg
	Tag                      string          `json:"Tag" gorm:"column:Tag"`                                           //标签
	CaiJingTrx               decimal.Decimal `json:"CaiJingTrx" gorm:"column:CaiJingTrx"`                             //累计彩金trx
	CaiJingUsdt              decimal.Decimal `json:"CaiJingUsdt" gorm:"column:CaiJingUsdt"`                           //累计彩金usdt
	RechargeAmount           decimal.Decimal `json:"RechargeAmount" gorm:"column:RechargeAmount"`                     //累计充值
	WithdrawAmount           decimal.Decimal `json:"WithdrawAmount" gorm:"column:WithdrawAmount"`                     //累计提款
	CSBindTime               utils.MyString  `json:"CSBindTime" gorm:"column:CSBindTime"`                             //客服绑定时间
	ThirdId                  float64         `json:"ThirdId" gorm:"column:ThirdId"`                                   //三方id
	SpecialAgent             float64         `json:"SpecialAgent" gorm:"column:SpecialAgent"`                         //是否是独立代理 1 是,2不是
	LastAddAmount            decimal.Decimal `json:"LastAddAmount" gorm:"column:LastAddAmount"`                       //最后一次后台增值金额
	AgentShortUrl            string          `json:"AgentShortUrl" gorm:"column:AgentShortUrl"`                       //代理推广链接
}

func (*User) TableName() string {
	return "x_user"
}

func User_Get(SellerId int, UserId int) *Hbc {
	data_out := Hbc{}
	dbresult := server.Db().Gorm().Table(data_out.TableName()).Where("SellerId = ?", SellerId).First(&data_out)
	err := dbresult.Error
	if dbresult.RowsAffected <= 0 {
		return nil
	}
	if err != nil {
		logs.Error(err)
		return nil
	}
	return &data_out
}

func User_Set_State(UserId int, State int) {
	data_out := User{}
	dbresult := server.Db().Gorm().Table(data_out.TableName()).Where("UserId = ?", UserId).Update("State", State)
	err := dbresult.Error
	if err != nil {
		logs.Error(err)
	}
}

func User_Set_PwdVerificationType(UserId int, val int) {
	data_out := User{}
	dbresult := server.Db().Gorm().Table(data_out.TableName()).Where("UserId = ?", UserId).Update("PwdVerificationType", val)
	err := dbresult.Error
	if err != nil {
		logs.Error(err)
	}
}

func User_Set_Password(UserId int, Password string) {
	data_out := User{}
	dbresult := server.Db().Gorm().Table(data_out.TableName()).Where("UserId = ?", UserId).Update("Password", Password)
	err := dbresult.Error
	if err != nil {
		logs.Error(err)
	}
}

func User_Set_WalletPassword(UserId int, WalletPassword string) {
	data_out := User{}
	dbresult := server.Db().Gorm().Table(data_out.TableName()).Where("UserId = ?", UserId).Update("WalletPassword", WalletPassword)
	err := dbresult.Error
	if err != nil {
		logs.Error(err)
	}
}

func User_Set_Address(UserId int, Address string) {
	data_out := User{}
	dbresult := server.Db().Gorm().Table(data_out.TableName()).Where("UserId = ?", UserId).Update("Address", Address)
	err := dbresult.Error
	if err != nil {
		logs.Error(err)
	}
}

func User_Page_Data(Page int, PageSize int, SellerId int, UserId int, Account string, Address string) (int, []User) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := User{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "UserId", "=", UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", "Account", "=", Account, "")
	server.Db().AddWhere(&sql, &params, "and", "Address", "=", Address, "")
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []User{}
	}
	result := []User{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}

type ActiveAddUseBalancerInfo struct {
	UserId            int32
	ActiveName        string  // 活动备注
	RealAmount        float64 // 金额
	WithdrawLiuSuiAdd float64 // 流水
	BalanceCReason    int     // 帐变类型
}

func ActiveAddUseBalancer(tx *dao.Query, dataInfo ActiveAddUseBalancerInfo) error {
	defer recover()
	if tx == nil {
		tx = server.DaoxHashGame()
	}
	date := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	rerr := tx.Transaction(func(tx *dao.Query) error {
		userTb := server.DaoxHashGame().XUser
		userFindDb := server.DaoxHashGame().XUser.WithContext(context.Background())
		user, err := userFindDb.Where(userTb.UserID.Eq(dataInfo.UserId)).First()
		if err != nil {
			logs.Error("ActiveAddUseBalancer find user err", err)
			return err
		}
		// 玩家日统计
		userDaillyTb := tx.XUserDailly
		userDaillyDb := tx.XUserDailly.WithContext(context.Background())
		_, err = userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).First()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				userDailly := &model.XUserDailly{
					SellerID:   user.SellerID,
					ChannelID:  user.ChannelID,
					UserID:     user.UserID,
					RecordDate: date,
				}
				err = userDaillyDb.Select(userDaillyTb.SellerID, userDaillyTb.ChannelID, userDaillyTb.UserID, userDaillyTb.RecordDate).Create(userDailly)
				if err != nil {
					logs.Error("ActiveAddUseBalancer Create userDailly err", err)
					return err
				}
			} else {
				logs.Error(err)
				return err
			}
		}
		userDb := tx.XUser.WithContext(context.Background())
		if user.WithdrawLiuSui > user.TotalLiuSui {
			_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
				"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
				"WithdrawLiuSui": gorm.Expr("WithdrawLiuSui + ?", dataInfo.WithdrawLiuSuiAdd),
			})
		} else {
			_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
				"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
				"WithdrawLiuSui": dataInfo.WithdrawLiuSuiAdd,
				"TotalLiuSui":    0,
			})
		}
		if err != nil {
			logs.Error("ActiveAddUseBalancer Updates user err", err)
			return err
		}

		amountChangeLog := &model.XAmountChangeLog{
			UserID:       user.UserID,
			BeforeAmount: user.Amount,
			Amount:       dataInfo.RealAmount,
			AfterAmount:  user.Amount + dataInfo.RealAmount,
			Reason:       int32(dataInfo.BalanceCReason),
			Memo:         dataInfo.ActiveName,
			SellerID:     user.SellerID,
			ChannelID:    user.ChannelID,
		}
		amountChangeLogDB := tx.XAmountChangeLog.WithContext(context.Background())
		err = amountChangeLogDB.Create(amountChangeLog)
		if err != nil {
			logs.Error("ActiveAddUseBalancer Create amountChangeLog err", err)
			return err
		}

		_, err = userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).Updates(map[string]any{
			"TotalCaiJin": gorm.Expr("TotalCaiJin + ?", dataInfo.RealAmount),
		})
		if err != nil {
			logs.Error("ActiveAddUseBalancer Updates userDailly err", err)
			return err
		}
		vipInfoTb := tx.XVipInfo
		vipInfoDb := tx.XVipInfo.WithContext(context.Background())
		_, err = vipInfoDb.Where(vipInfoTb.UserID.Eq(user.UserID)).Updates(map[string]any{
			"CaiJin": gorm.Expr("CaiJin + ?", dataInfo.RealAmount),
		})
		if err != nil {
			logs.Error("ActiveAddUseBalancer Updates vipInfo err", err)
			return err
		}
		if dataInfo.RealAmount > 0 {
			caijingDetailDb := tx.XCaijingDetail.WithContext(context.Background())
			caijingDetail := &model.XCaijingDetail{
				UserID:     user.UserID,
				SType:      dataInfo.ActiveName,
				Symbol:     "usdt",
				Amount:     dataInfo.RealAmount,
				CSGroup:    user.CSGroup,
				CSID:       user.CSID,
				TopAgentID: user.TopAgentID,
			}
			err = caijingDetailDb.Create(caijingDetail)
			if err != nil {
				logs.Error("ActiveAddUseBalancer Create caijingDetail err", err)
				return err
			}
		}
		return nil
	})
	return rerr
}

// User_Set_AgentUrl 设置用户代理推广链接
func User_Set_AgentUrl(userid int, agentUrl string) error {
	return server.Db().QueryNoResult("update x_user set AgentUrl = ? where UserId = ?", agentUrl, userid)
}

// User_Set_AgentShortUrl 设置用户代理短链接
func User_Set_AgentShortUrl(userid int, shortUrl string) error {
	return server.Db().QueryNoResult("update x_user set AgentShortUrl = ? where UserId = ?", shortUrl, userid)
}
