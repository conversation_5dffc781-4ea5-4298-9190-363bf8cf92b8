package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/warning"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

const RISK_LOGIN_CHECK_OPTIONS = `[
{"field":"Id","name":"序号"},
{"field":"UserId","name":"玩家ID"},
{"field":"SellerId","name":"运营商"},
{"field":"ChannelId","name":"渠道"},
{"field":"Account","name":"玩家账号"},
{"field":"Address","name":"玩家地址"},
{"field":"LoginLang","name":"登录语言"},
{"field":"LoginTime","name":"登录时间"},
{"field":"LoginIp","name":"登录IP"},
{"field":"LoginIPName","name":"登录地区"},
{"field":"LoginDeviceId","name":"登录设备ID"},
{"field":"LoginDeviceType","name":"登录设备类型"},
{"field":"RegisterIp","name":"注册IP"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"RegLang","name":"注册语言"},
{"field":"BetIp","name":"下注IP"}
]`

/*
const RISK_LOGIN_CHECK_DETAIL_OPTIONS = `[
{"field":"UserId","name":"玩家ID"},
{"field":"LoginIp","name":"登录IP"},
{"field":"RegisterIp","name":"注册IP"},
{"field":"Password","name":"登录密码"},
{"field":"WalletPassword","name":"提现密码"},
{"field":"LoginDeviceId","name":"登录设备ID"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"LoginTime","name":"登录时间"}
]`
*/
const RISK_LOGIN_CHECK_DETAIL_OPTIONS = `[
{"field":"UserId","name":"玩家ID"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"LoginTime","name":"登录时间"}
]`
const RISK_LOGIN_CHECK_DETAIL_OPTIONS_RegisterIp = `[
{"field":"UserId","name":"玩家ID"},
{"field":"RegisterIp","name":"注册IP"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"LoginTime","name":"登录时间"}
]`
const RISK_LOGIN_CHECK_DETAIL_OPTIONS_LoginIp = `[
{"field":"UserId","name":"玩家ID"},
{"field":"LoginIp","name":"登录IP"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"LoginTime","name":"登录时间"}
]`
const RISK_LOGIN_CHECK_DETAIL_OPTIONS_LoginDeviceId = `[
{"field":"UserId","name":"玩家ID"},
{"field":"LoginDeviceId","name":"登录设备ID"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"LoginTime","name":"登录时间"}
]`
const RISK_LOGIN_CHECK_DETAIL_OPTIONS_BetIp = `[
{"field":"UserId","name":"玩家ID"},
{"field":"BetIp","name":"下注IP"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"LoginTime","name":"登录时间"}
]`

type RiskController struct{}

// formatDateTimeFields 格式化日期时间字段
func (c *RiskController) formatDateTimeFields(m map[string]interface{}) {
	// 需要格式化的日期时间字段列表
	dateFields := []string{"RegisterTime", "LoginTime", "CreateTime", "UpdateTime"}

	for _, field := range dateFields {
		if dateValue, ok := m[field]; ok {
			if dateStr, ok := dateValue.(string); ok && dateStr != "" {
				// 解析带时区的时间格式
				if parsedTime, err := time.Parse(time.RFC3339, dateStr); err == nil {
					// 转换为本地时间并格式化为 YYYY-MM-DD HH:MM:SS
					m[field] = parsedTime.Local().Format("2006-01-02 15:04:05")
				} else if parsedTime, err := time.Parse("2006-01-02T15:04:05Z07:00", dateStr); err == nil {
					// 尝试另一种时间格式
					m[field] = parsedTime.Local().Format("2006-01-02 15:04:05")
				} else if _, err := time.Parse("2006-01-02 15:04:05", dateStr); err == nil {
					// 如果已经是标准格式，保持不变
					m[field] = dateStr
				}
			}
		}
	}
}

func (c *RiskController) Init() {
	server.Http().Post("/api/risk/get_config", c.get_config)
	server.Http().Post("/api/risk/set_config", c.set_config)
	server.Http().Post("/api/risk/login_check", c.login_check)
	server.Http().Post("/api/risk/login_check_detail", c.login_check_detail)
	server.Http().Post("/api/risk/order_rate_audit", c.order_rate_audit)

	server.Http().Post("/api/risk_early_warning", c.risk_early_warning)
	server.Http().Post("/api/risk_early_warning_save", c.risk_early_warning_save)
	server.Http().Post("/api/risk/win_score_warning_config", c.win_score_warning_config)
	server.Http().Post("/api/risk/win_score_warning_config_save", c.win_score_warning_config_save)
	server.Http().Post("/api/risk/win_score_warning_list", c.win_score_warning_list)

}

func (c *RiskController) get_config(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "风控参数", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	ctx.RespOK(xgo.H{
		"OrderAuditTrxLimit":  server.GetConfigString(reqdata.SellerId, 0, "OrderAuditTrxLimit"),
		"OrderAuditUsdtLimit": server.GetConfigString(reqdata.SellerId, 0, "OrderAuditUsdtLimit"),
		"BlackMaker":          server.GetConfigString(reqdata.SellerId, 0, "BlackMaker"),
	})
}

func (c *RiskController) set_config(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId            int
		OrderAuditTrxLimit  string //订单审核trx限制
		OrderAuditUsdtLimit string //订单审核usdt限制
		BlackMaker          string //出块者黑名单
		GoogleCode          string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "风控参数", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.SetConfig(reqdata.SellerId, 0, "OrderAuditTrxLimit", reqdata.OrderAuditTrxLimit)
	server.SetConfig(reqdata.SellerId, 0, "OrderAuditUsdtLimit", reqdata.OrderAuditUsdtLimit)
	server.SetConfig(reqdata.SellerId, 0, "BlackMaker", reqdata.BlackMaker)
	ctx.RespOK()
	server.WriteAdminLog("设置风控参数", ctx, reqdata)
}

func (c *RiskController) login_check(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		ChannelId  int
		UserId     int
		RegisterIp string
		LoginIp    string
		BetIp      string
		Address    string
		StartTime  int64
		EndTime    int64
		Export     int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "登录查询", "查", "分控登录查询")
	if token == nil {
		return
	}
	tb := server.DaoxHashGame().XUser
	do := tb.WithContext(context.Background())
	if reqdata.SellerId > 0 {
		do = do.Where(tb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if reqdata.ChannelId > 0 {
		do = do.Where(tb.ChannelID.Eq(int32(reqdata.ChannelId)))
	}
	if reqdata.UserId > 0 {
		do = do.Where(tb.UserID.Eq(int32(reqdata.UserId)))
	}
	if reqdata.StartTime > 0 {
		startTime := time.UnixMilli(reqdata.StartTime).Local()
		do = do.Where(tb.LoginTime.Gte(startTime))
	}
	if reqdata.EndTime > 0 {
		endTime := time.UnixMilli(reqdata.EndTime).Local()
		do = do.Where(tb.LoginTime.Lte(endTime))
	}
	if len(reqdata.RegisterIp) > 1 {
		do = do.Where(tb.RegisterIP.Eq(reqdata.RegisterIp))
	}
	if len(reqdata.LoginIp) > 1 {
		do = do.Where(tb.LoginIP.Eq(reqdata.LoginIp))
	}
	if len(reqdata.Address) > 1 {
		do = do.Where(tb.Address.Eq(reqdata.Address))
	}

	// 处理 BetIp 查询条件
	if len(reqdata.BetIp) > 1 {
		// 先从 x_user_more 表查询使用指定 BetIp 的用户ID列表
		var userIds []int32
		err := server.Db().GormDao().Table("x_user_more").
			Where("BetIp = ? AND BetIp IS NOT NULL", reqdata.BetIp).
			Select("UserId").
			Scan(&userIds).Error
		if err != nil || len(userIds) == 0 {
			// 如果没有找到用户或出错，返回空结果
			ctx.Put("total", 0)
			ctx.Put("data", []*struct {
				model.XUser
				ChannelName       string
				LoginIPName       string
				PasswordNum       int
				WalletPasswordNum int
				RegisterIpNum     int
				LoginIpNum        int
				LoginDeviceIDNum  int
				BetIpNum          int
			}{})
			ctx.RespOK()
			return
		}
		// 添加用户ID过滤条件
		do = do.Where(tb.UserID.In(userIds...))
	}

	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	var users []*struct {
		model.XUser
		ChannelName       string
		LoginIPName       string
		PasswordNum       int
		WalletPasswordNum int
		RegisterIpNum     int
		LoginIpNum        int
		LoginDeviceIDNum  int
		BetIpNum          int
		BetIp             string
	}
	count, err := do.Select(tb.UserID, tb.SellerID, tb.ChannelID, tb.Account, tb.LoginIP, tb.RegisterIP,
		tb.Password, tb.WalletPassword, tb.LoginDeviceID, tb.LoginDeviceType, tb.RegLang, tb.Address, tb.RegisterTime, tb.LoginTime).
		Order(tb.LoginTime.Desc(), tb.ID.Desc()).ScanByPage(&users, (reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
	if err != nil || len(users) < 1 {
		logs.Error(err)
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	userIds := make([]string, 0)
	for _, v := range users {
		userIds = append(userIds, abugo.GetStringFromInterface(v.UserID))
	}
	userIdsStr := strings.Join(userIds, ",")
	sql := `
		WITH t1 AS (
		SELECT * FROM x_user WHERE userid IN (%s)
		), t11 AS (
		SELECT * FROM x_user WHERE PASSWORD IN (SELECT PASSWORD FROM t1)
		), t12 AS (
		SELECT t11.PASSWORD, COUNT(*) AS num FROM t11 GROUP BY PASSWORD
		),t21 AS (
		SELECT * FROM x_user WHERE WalletPassword IN (SELECT WalletPassword FROM t1)
		), t22 AS (
		SELECT t21.WalletPassword, COUNT(*) AS num FROM t21 GROUP BY WalletPassword
		),t31 AS (
		SELECT * FROM x_user WHERE RegisterIp IN (SELECT RegisterIp FROM t1)
		), t32 AS (
		SELECT t31.RegisterIp, COUNT(*) AS num FROM t31 GROUP BY RegisterIp
		),t41 AS (
		SELECT * FROM x_user WHERE LoginIp IN (SELECT LoginIp FROM t1)
		), t42 AS (
		SELECT t41.LoginIp, COUNT(*) AS num FROM t41 GROUP BY LoginIp
		),t51 AS (
		SELECT * FROM x_user WHERE LoginDeviceID IN (SELECT LoginDeviceID FROM t1)
		), t52 AS (
		SELECT t51.LoginDeviceID, COUNT(*) AS num FROM t51 GROUP BY LoginDeviceID
		),t61 AS (
		SELECT x_user.UserId FROM x_user
		INNER JOIN x_user_more ON x_user.UserId = x_user_more.UserId
		WHERE x_user_more.BetIp IN (SELECT DISTINCT x_user_more.BetIp FROM x_user_more WHERE x_user_more.UserId IN (%s) AND x_user_more.BetIp != '')
		), t62 AS (
		SELECT x_user_more.BetIp, COUNT(*) AS num FROM x_user_more
		WHERE x_user_more.BetIp IN (SELECT DISTINCT x_user_more.BetIp FROM x_user_more WHERE x_user_more.UserId IN (%s) AND x_user_more.BetIp != '')
		GROUP BY x_user_more.BetIp
		)
		, r1 AS (
			SELECT t1.userid, t1.PASSWORD, t12.num FROM t1 LEFT JOIN t12 ON t1.PASSWORD = t12.PASSWORD
		), r2 AS (
			SELECT t1.userid, t1.WalletPassword, t22.num FROM t1 LEFT JOIN t22 ON t1.WalletPassword = t22.WalletPassword
		), r3 AS (
			SELECT t1.userid, t1.RegisterIp, t32.num FROM t1 LEFT JOIN t32 ON t1.RegisterIp = t32.RegisterIp
		), r4 AS (
			SELECT t1.userid, t1.LoginIp, t42.num FROM t1 LEFT JOIN t42 ON t1.LoginIp = t42.LoginIp
		), r5 AS (
			SELECT t1.userid, t1.LoginDeviceID, t52.num FROM t1 LEFT JOIN t52 ON t1.LoginDeviceID = t52.LoginDeviceID
		), r6 AS (
			SELECT t1.userid, x_user_more.BetIp, COALESCE(t62.num, 0) as num
			FROM t1 LEFT JOIN x_user_more ON t1.userid = x_user_more.UserId
			LEFT JOIN t62 ON x_user_more.BetIp = t62.BetIp
		)
		SELECT r1.userid as UserId, r1.PASSWORD as Password, r1.num as PasswordNum,
			r2.WalletPassword as WalletPassword, r2.num as WalletPasswordNum,
			r3.RegisterIp as RegisterIp, r3.num as RegisterIpNum,
			r4.LoginIp as LoginIp, r4.num as LoginIpNum,
			r5.LoginDeviceID as LoginDeviceID, r5.num as LoginDeviceIDNum,
			r6.BetIp as BetIp, r6.num as BetIpNum
		FROM r1 JOIN r2 ON r1.userid = r2.userid JOIN r3 ON r1.userid = r3.userid JOIN r4 ON r1.userid = r4.userid
		join r5 on r1.userid = r5.userid JOIN r6 ON r1.userid = r6.userid
	`
	type Stat struct {
		UserId            int
		Password          string
		PasswordNum       int
		WalletPassword    string
		WalletPasswordNum int
		RegisterIp        string
		RegisterIpNum     int
		LoginIp           string
		LoginIpNum        int
		LoginDeviceID     string
		LoginDeviceIDNum  int
		BetIp             string
		BetIpNum          int
	}
	var statisticData []*Stat
	server.Db().GormDao().Raw(fmt.Sprintf(sql, userIdsStr, userIdsStr, userIdsStr)).Scan(&statisticData)
	maps := make(map[int]*Stat)
	for _, v := range statisticData {
		maps[v.UserId] = v
	}

	for _, v := range users {
		t := maps[int(v.UserID)]
		v.Password = utils.ReplaceMiddleWithStars(v.Password)
		v.WalletPassword = utils.ReplaceMiddleWithStars(v.WalletPassword)
		v.LoginIPName = utils.IpToLocation(v.LoginIP)
		v.ChannelName = ChannelName(int(v.ChannelID))
		v.PasswordNum = t.PasswordNum
		v.WalletPasswordNum = t.WalletPasswordNum
		v.RegisterIpNum = t.RegisterIpNum
		v.LoginIpNum = t.LoginIpNum
		v.LoginDeviceIDNum = t.LoginDeviceIDNum
		v.BetIp = t.BetIp
		v.BetIpNum = t.BetIpNum
	}

	if reqdata.Export == 1 {
		xdata := &xgo.XMaps{}
		xdata.RawData = []xgo.XMap{}
		for _, v := range users {
			data, _ := json.Marshal(*v)
			m := make(map[string]interface{})
			_ = json.Unmarshal(data, &m)
			// 将用户ID转换为字符串，避免Excel中显示为科学计数法
			// 在数字前加上单引号强制Excel将其识别为文本
			if userID, ok := m["UserId"]; ok {
				switch v := userID.(type) {
				case int32:
					m["UserId"] = " " + fmt.Sprintf("%d", v)
				case int64:
					m["UserId"] = " " + fmt.Sprintf("%d", v)
				case int:
					m["UserId"] = " " + fmt.Sprintf("%d", v)
				case float64:
					m["UserId"] = " " + fmt.Sprintf("%.0f", v)
				default:
					m["UserId"] = " " + fmt.Sprintf("%v", v)
				}
			}
			// 同样处理其他可能的大数字字段
			if sellerID, ok := m["SellerId"]; ok {
				switch v := sellerID.(type) {
				case int32:
					m["SellerId"] = " " + fmt.Sprintf("%d", v)
				case int64:
					m["SellerId"] = " " + fmt.Sprintf("%d", v)
				case int:
					m["SellerId"] = " " + fmt.Sprintf("%d", v)
				case float64:
					m["SellerId"] = " " + fmt.Sprintf("%.0f", v)
				default:
					m["SellerId"] = " " + fmt.Sprintf("%v", v)
				}
			}
			if channelID, ok := m["ChannelId"]; ok {
				switch v := channelID.(type) {
				case int32:
					m["ChannelId"] = " " + fmt.Sprintf("%d", v)
				case int64:
					m["ChannelId"] = " " + fmt.Sprintf("%d", v)
				case int:
					m["ChannelId"] = " " + fmt.Sprintf("%d", v)
				case float64:
					m["ChannelId"] = " " + fmt.Sprintf("%.0f", v)
				default:
					m["ChannelId"] = " " + fmt.Sprintf("%v", v)
				}
			}

			// 格式化日期时间字段
			c.formatDateTimeFields(m)

			xdata.RawData = append(xdata.RawData, xgo.XMap{RawData: m})
		}
		filename := "export_risk_login_check_" + time.Now().Format("20060102150405")
		xgo.Export(server.ExportDir()+"/"+filename, xdata, RISK_LOGIN_CHECK_OPTIONS)
		ctx.Put("filename", "/exports/"+filename+".xlsx")
	} else {
		ctx.Put("data", users)
		ctx.Put("total", count)
	}
	ctx.RespOK()

}

func (c *RiskController) login_check_detail(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		UserId    int
		CheckType string
		CheckData string
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
		Export    int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "登录查询", "查", "分控登录查询")
	if token == nil {
		return
	}

	OPTIONS := RISK_LOGIN_CHECK_DETAIL_OPTIONS
	tb := server.DaoxHashGame().XUser
	do := tb.WithContext(context.Background())
	tUser, err := do.Where(tb.UserID.Eq(int32(reqdata.UserId))).First()
	if err != nil {
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}

	// 查询 BetIp 需要特殊处理，因为它在 x_user_more 表中
	if reqdata.CheckType == "BetIp" {
		OPTIONS = RISK_LOGIN_CHECK_DETAIL_OPTIONS_BetIp
		// 先从 x_user_more 表获取当前用户的 BetIp
		var userMore struct {
			BetIp string
		}
		err := server.Db().GormDao().Table("x_user_more").
			Where("UserId = ?", reqdata.UserId).
			Select("BetIp").
			Scan(&userMore).Error
		if err != nil || userMore.BetIp == "" {
			ctx.Put("total", 0)
			ctx.Put("data", []*model.XUser{})
			ctx.RespOK()
			return
		}

		// 查询所有使用相同 BetIp 的用户
		var userIds []int32
		err = server.Db().GormDao().Table("x_user_more").
			Where("BetIp = ? AND BetIp IS NOT NULL", userMore.BetIp).
			Select("UserId").
			Scan(&userIds).Error
		if err != nil || len(userIds) == 0 {
			ctx.Put("total", 0)
			ctx.Put("data", []*model.XUser{})
			ctx.RespOK()
			return
		}

		do = tb.WithContext(context.Background()).Where(tb.UserID.In(userIds...))
	} else {
		do = tb.WithContext(context.Background())
		if reqdata.CheckType == "Password" {
			do = do.Where(tb.Password.Eq(tUser.Password))
		} else if reqdata.CheckType == "WalletPassword" {
			do = do.Where(tb.WalletPassword.Eq(tUser.WalletPassword))
		} else if reqdata.CheckType == "RegisterIp" {
			OPTIONS = RISK_LOGIN_CHECK_DETAIL_OPTIONS_RegisterIp
			do = do.Where(tb.RegisterIP.Eq(reqdata.CheckData))
		} else if reqdata.CheckType == "LoginIp" {
			OPTIONS = RISK_LOGIN_CHECK_DETAIL_OPTIONS_LoginIp
			do = do.Where(tb.LoginIP.Eq(reqdata.CheckData))
		} else if reqdata.CheckType == "LoginDeviceID" {
			OPTIONS = RISK_LOGIN_CHECK_DETAIL_OPTIONS_LoginDeviceId
			do = do.Where(tb.LoginDeviceID.Eq(reqdata.CheckData))
		}
	}

	if reqdata.StartTime > 0 {
		startTime := time.UnixMilli(reqdata.StartTime).Local()
		do = do.Where(tb.LoginTime.Gte(startTime))
	}
	if reqdata.EndTime > 0 {
		endTime := time.UnixMilli(reqdata.EndTime).Local()
		do = do.Where(tb.LoginTime.Lte(endTime))
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}

	finds, count, err := do.Select(tb.UserID, tb.Password, tb.WalletPassword, tb.RegisterIP, tb.LoginIP,
		tb.LoginDeviceID, tb.RegisterTime, tb.LoginTime).Order(tb.RegisterTime.Desc(), tb.ID.Desc()).
		FindByPage((reqdata.Page-1)*reqdata.PageSize, reqdata.PageSize)
	if err != nil {
		logs.Error(err)
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}

	// 如果是 BetIp 查询，需要添加 BetIp 字段到结果中
	var result []map[string]interface{}
	if reqdata.CheckType == "BetIp" {
		// 获取所有用户的 BetIp 信息
		userIds := make([]int32, len(finds))
		for i, v := range finds {
			userIds[i] = v.UserID
		}

		var betIpData []struct {
			UserId int32
			BetIp  string
		}
		server.Db().GormDao().Table("x_user_more").
			Where("UserId IN ?", userIds).
			Select("UserId, BetIp").
			Scan(&betIpData)

		betIpMap := make(map[int32]string)
		for _, v := range betIpData {
			betIpMap[v.UserId] = v.BetIp
		}

		// 构建包含 BetIp 的结果
		for _, v := range finds {
			v.Password = utils.ReplaceMiddleWithStars(v.Password)
			v.WalletPassword = utils.ReplaceMiddleWithStars(v.WalletPassword)

			data, _ := json.Marshal(*v)
			m := make(map[string]interface{})
			_ = json.Unmarshal(data, &m)
			m["BetIp"] = betIpMap[v.UserID]
			result = append(result, m)
		}
	} else {
		// 其他查询类型的正常处理
		for _, v := range finds {
			v.Password = utils.ReplaceMiddleWithStars(v.Password)
			v.WalletPassword = utils.ReplaceMiddleWithStars(v.WalletPassword)

			data, _ := json.Marshal(*v)
			m := make(map[string]interface{})
			_ = json.Unmarshal(data, &m)
			result = append(result, m)
		}
	}

	if reqdata.Export == 1 {
		xdata := &xgo.XMaps{}
		xdata.RawData = []xgo.XMap{}
		for _, v := range result {
			// 将用户ID转换为字符串，避免Excel中显示为科学计数法
			// 在数字前加上单引号强制Excel将其识别为文本
			if userID, ok := v["UserId"]; ok {
				switch val := userID.(type) {
				case int32:
					v["UserId"] = " " + fmt.Sprintf("%d", val)
				case int64:
					v["UserId"] = " " + fmt.Sprintf("%d", val)
				case int:
					v["UserId"] = " " + fmt.Sprintf("%d", val)
				case float64:
					v["UserId"] = " " + fmt.Sprintf("%.0f", val)
				default:
					v["UserId"] = " " + fmt.Sprintf("%v", val)
				}
			}
			// 同样处理其他可能的大数字字段
			if sellerID, ok := v["SellerId"]; ok {
				switch val := sellerID.(type) {
				case int32:
					v["SellerId"] = " " + fmt.Sprintf("%d", val)
				case int64:
					v["SellerId"] = " " + fmt.Sprintf("%d", val)
				case int:
					v["SellerId"] = " " + fmt.Sprintf("%d", val)
				case float64:
					v["SellerId"] = " " + fmt.Sprintf("%.0f", val)
				default:
					v["SellerId"] = " " + fmt.Sprintf("%v", val)
				}
			}
			if channelID, ok := v["ChannelId"]; ok {
				switch val := channelID.(type) {
				case int32:
					v["ChannelId"] = " " + fmt.Sprintf("%d", val)
				case int64:
					v["ChannelId"] = " " + fmt.Sprintf("%d", val)
				case int:
					v["ChannelId"] = " " + fmt.Sprintf("%d", val)
				case float64:
					v["ChannelId"] = " " + fmt.Sprintf("%.0f", val)
				default:
					v["ChannelId"] = " " + fmt.Sprintf("%v", val)
				}
			}

			// 格式化日期时间字段
			c.formatDateTimeFields(v)

			xdata.RawData = append(xdata.RawData, xgo.XMap{RawData: v})
		}
		filename := "export_risk_login_check_detail_" + time.Now().Format("20060102150405")
		xgo.Export(server.ExportDir()+"/"+filename, xdata, OPTIONS)
		ctx.Put("filename", "/exports/"+filename+".xlsx")
	} else {
		ctx.Put("data", result)
		ctx.Put("total", count)
	}
	ctx.RespOK()
}

func (c *RiskController) order_rate_audit(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		OrderId    int
		Rate       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "注单赔率", "改", "分控注单赔率修改")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tb := server.DaoxHashGame().XOrder
	do := tb.WithContext(context.Background())
	tOrder, err := do.Where(tb.ID.Eq(int32(reqdata.OrderId))).Where(
		tb.State.Eq(12)).First() // 待审核的订单
	if ctx.RespErrString(err != nil, &errcode, "订单不存在") {
		logs.Error("order_rate_audit ", err)
		return
	}
	rate := abugo.GetFloat64FromInterface(reqdata.Rate)
	if rate < 0 || rate > tOrder.RewardRate {
		ctx.RespErrString(true, &errcode, "返奖率错误")
		return
	}
	do = tb.WithContext(context.Background())
	rewardAmount := rate * tOrder.Amount
	logs.Info("order_rate_audit rewardAmount %s %d %f %f %f %f", token.Account, token.UserId, tOrder.RewardRate, rate, tOrder.Amount, rewardAmount)
	result, err := do.Where(tb.ID.Eq(tOrder.ID)).Where(tb.State.Eq(12)).UpdateSimple(tb.RewardRate.Value(rate), tb.RewardAmount.Value(rewardAmount))
	if result.RowsAffected != 1 || err != nil {
		ctx.RespErrString(true, &errcode, "修改数据错误")
		return
	}
	ctx.RespOK()
}

func (c *RiskController) risk_early_warning(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "风控预警", "查", "查风控预警")
	if token == nil {
		return
	}
	ctx.RespOK(xgo.H{
		"RiskNewerFirstWinUsdt": server.GetConfigString(reqdata.SellerId, 0, "RiskNewerFirstWinUsdt"),
		"RiskNewerFirstWinTrx":  server.GetConfigString(reqdata.SellerId, 0, "RiskNewerFirstWinTrx"),
		"RiskNewerFirstWinType": server.GetConfigString(reqdata.SellerId, 0, "RiskNewerFirstWinType"),
	})
}

func (c *RiskController) risk_early_warning_save(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId              int
		RiskNewerFirstWinUsdt string
		RiskNewerFirstWinTrx  string
		RiskNewerFirstWinType string // 1 累计 2 不累计
		GoogleCode            string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "风控预警", "改", "改风控预警")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.SetConfig(reqdata.SellerId, 0, "RiskNewerFirstWinUsdt", reqdata.RiskNewerFirstWinUsdt)
	server.SetConfig(reqdata.SellerId, 0, "RiskNewerFirstWinTrx", reqdata.RiskNewerFirstWinTrx)
	server.SetConfig(reqdata.SellerId, 0, "RiskNewerFirstWinType", reqdata.RiskNewerFirstWinType)
	ctx.RespOK()
}

// 赢分预警配置 - 获取配置
func (c *RiskController) win_score_warning_config(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "赢分预警", "查", "查看赢分预警配置")
	if token == nil {
		return
	}
	ctx.RespOK(xgo.H{
		"WinScoreWarningUsdt": server.GetConfigString(reqdata.SellerId, 0, warning.ConfigKeyWinScoreWarningUsdt),
		"WinScoreWarningTrx":  server.GetConfigString(reqdata.SellerId, 0, warning.ConfigKeyWinScoreWarningTrx),
	})
}

// 赢分预警配置 - 保存配置
func (c *RiskController) win_score_warning_config_save(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId            int
		WinScoreWarningUsdt string // USDT单次赢分
		WinScoreWarningTrx  string // TRX单次赢分
		IsEnabled           int    // 是否启用
		GoogleCode          string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "赢分预警", "改", "保存赢分预警配置")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	// 添加调试日志
	logs.Info("保存赢分预警配置",
		"sellerId", reqdata.SellerId,
		"usdtValue", reqdata.WinScoreWarningUsdt,
		"trxValue", reqdata.WinScoreWarningTrx)

	// 保存配置
	server.SetConfig(reqdata.SellerId, 0, warning.ConfigKeyWinScoreWarningUsdt, reqdata.WinScoreWarningUsdt)
	server.SetConfig(reqdata.SellerId, 0, warning.ConfigKeyWinScoreWarningTrx, reqdata.WinScoreWarningTrx)
	logs.Info("配置保存完成", "sellerId", reqdata.SellerId)

	// 等待一下再验证，确保数据库操作完成
	time.Sleep(100 * time.Millisecond)

	// 验证保存结果
	savedUsdt := server.GetConfigString(reqdata.SellerId, 0, warning.ConfigKeyWinScoreWarningUsdt)
	savedTrx := server.GetConfigString(reqdata.SellerId, 0, warning.ConfigKeyWinScoreWarningTrx)
	logs.Info("配置保存验证",
		"sellerId", reqdata.SellerId,
		"savedUsdt", savedUsdt,
		"savedTrx", savedTrx)

	// 如果保存失败，尝试直接数据库操作
	if savedUsdt == "" || savedTrx == "" {
		logs.Warn("配置保存可能失败，尝试直接数据库操作")

		// 使用GormDao操作数据库
		db := server.Db().GormDao()

		// 保存USDT配置
		usdtConfig := map[string]interface{}{
			"SellerId":    reqdata.SellerId,
			"ChannelId":   0,
			"ConfigName":  warning.ConfigKeyWinScoreWarningUsdt,
			"ConfigValue": reqdata.WinScoreWarningUsdt,
			"Remark":      "单次赢分USDT",
		}
		usdtErr := db.Table("x_config").
			Where("SellerId = ? AND ChannelId = 0 AND ConfigName = ?", reqdata.SellerId, warning.ConfigKeyWinScoreWarningUsdt).
			Assign(usdtConfig).
			FirstOrCreate(&map[string]interface{}{}).Error

		// 保存TRX配置
		trxConfig := map[string]interface{}{
			"SellerId":    reqdata.SellerId,
			"ChannelId":   0,
			"ConfigName":  warning.ConfigKeyWinScoreWarningTrx,
			"ConfigValue": reqdata.WinScoreWarningTrx,
			"Remark":      "单次赢分TRX",
		}
		trxErr := db.Table("x_config").
			Where("SellerId = ? AND ChannelId = 0 AND ConfigName = ?", reqdata.SellerId, warning.ConfigKeyWinScoreWarningTrx).
			Assign(trxConfig).
			FirstOrCreate(&map[string]interface{}{}).Error

		logs.Info("直接数据库保存结果",
			"usdtErr", usdtErr,
			"trxErr", trxErr)
	}

	server.WriteAdminLog("保存赢分预警配置", ctx, reqdata)
	ctx.RespOK()
}

// 赢分预警列表
func (c *RiskController) win_score_warning_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		ChannelId int
		UserId    int
		StartTime int64
		EndTime   int64
		Symbol    string // usdt 或 trx
		Export    int    // 0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "赢分预警", "查", "查看赢分预警列表")
	if token == nil {
		return
	}

	//// 验证时间间隔不能超过3个月（90天）
	//if reqdata.StartTime > 0 && reqdata.EndTime > 0 {
	//	timeDiff := reqdata.EndTime - reqdata.StartTime
	//	maxDiff := int64(90 * 24 * 60 * 60 * 1000) // 90天的毫秒数
	//	if timeDiff > maxDiff {
	//		ctx.Put("msg", "查询时间间隔不能超过3个月")
	//		ctx.RespErr(nil, nil)
	//		return
	//	}
	//}

	// 直接查询预警数据表
	var totalCount int32
	var allResults []map[string]interface{}

	// 构建查询条件
	var conditions []string
	var params []interface{}

	// 运营商条件
	if reqdata.SellerId > 0 {
		conditions = append(conditions, "SellerId = ?")
		params = append(params, reqdata.SellerId)
	}

	// 用户条件
	if reqdata.UserId > 0 {
		conditions = append(conditions, "UserId = ?")
		params = append(params, reqdata.UserId)
	}

	// 时间条件
	if reqdata.StartTime > 0 {
		conditions = append(conditions, "ThirdTime >= ?")
		params = append(params, abugo.TimeStampToLocalTime(reqdata.StartTime))
	}
	if reqdata.EndTime > 0 {
		conditions = append(conditions, "ThirdTime <= ?")
		params = append(params, abugo.TimeStampToLocalTime(reqdata.EndTime))
	}

	// 构建WHERE子句
	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 统计总数
	countSql := fmt.Sprintf("SELECT COUNT(*) as count FROM x_win_score_warning %s", whereClause)
	countResult, err := server.Db().Query(countSql, params)
	if err != nil {
		logs.Error("统计预警数据失败", "error", err)
		ctx.RespErrString(true, &errcode, "查询失败")
		return
	}

	if len(*countResult) > 0 {
		totalCount = int32(abugo.GetInt64FromInterface((*countResult)[0]["count"]))
	}

	logs.Info("预警数据统计完成", "totalCount", totalCount)

	// 如果没有数据，直接返回
	if totalCount == 0 {
		logs.Warn("未找到符合条件的预警数据", "sellerId", reqdata.SellerId)
		ctx.Put("data", []map[string]interface{}{})
		ctx.Put("total", totalCount)
		ctx.Put("page", reqdata.Page)
		ctx.Put("page_size", reqdata.PageSize)
		ctx.RespOK()
		return
	}

	// 分页查询数据
	offset := (reqdata.Page - 1) * reqdata.PageSize
	dataSql := fmt.Sprintf(`
		SELECT Id, SellerId, ChannelId, UserId, GameType, GameId, GameName, Brand, ThirdId,
		       BetAmount, WinAmount, WinScore, Symbol, ThirdTime, CreateTime
		FROM x_win_score_warning
		%s
		ORDER BY ThirdTime DESC
		LIMIT %d OFFSET %d
	`, whereClause, reqdata.PageSize, offset)

	result, err := server.Db().Query(dataSql, params)
	if err != nil {
		logs.Error("查询预警数据失败", "error", err)
		ctx.RespErrString(true, &errcode, "查询失败")
		return
	}

	if result != nil {
		allResults = *result
	}

	// 获取运营商名称映射
	sellerNameMap, err := InitSellerNameMap()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 批量查询用户信息
	c.batchQueryUserInfoForWarning(allResults)

	// 游戏类型名称映射
	gameTypeNameMap := map[int]string{
		-2: "余额哈希",
		-1: "哈希游戏",
		1:  "电子游戏",
		2:  "棋牌游戏",
		3:  "小游戏",
		4:  "彩票游戏",
		5:  "真人游戏",
		6:  "体育游戏",
		7:  "德州扑克",
	}

	// 处理结果数据，添加运营商名称、游戏类型名称、派彩金额显示等
	for i := range allResults {
		sellerId := int(abugo.GetInt64FromInterface(allResults[i]["SellerId"]))
		gameType := int(abugo.GetInt64FromInterface(allResults[i]["GameType"]))
		symbol := abugo.GetStringFromInterface(allResults[i]["Symbol"])
		winAmount := abugo.GetFloat64FromInterface(allResults[i]["WinAmount"])

		// 添加运营商名称
		allResults[i]["SellerName"] = SellerIDName(sellerNameMap, sellerId)

		// 添加游戏类型名称
		if gameTypeName, exists := gameTypeNameMap[gameType]; exists {
			allResults[i]["GameTypeName"] = gameTypeName
		} else {
			allResults[i]["GameTypeName"] = "未知游戏"
		}

		// 根据币种分别显示派彩金额
		if symbol == "trx" {
			allResults[i]["WinAmountTrx"] = winAmount
			allResults[i]["WinAmountUsdt"] = "-"
		} else if symbol == "usdt" {
			allResults[i]["WinAmountTrx"] = "-"
			allResults[i]["WinAmountUsdt"] = winAmount
		} else {
			allResults[i]["WinAmountTrx"] = "-"
			allResults[i]["WinAmountUsdt"] = "-"
		}
	}

	if reqdata.Export != 1 {
		// 清空Redis中的预警计数
		if reqdata.SellerId > 0 {
			// 清空指定运营商的预警计数
			warning.ClearWarningCount(reqdata.SellerId)
		} else {
			// 清空所有预警计数
			warning.ClearAllWarningCount()
		}

		ctx.Put("data", allResults)
		ctx.Put("total", totalCount)
		ctx.Put("page", reqdata.Page)
		ctx.Put("page_size", reqdata.PageSize)
		server.WriteAdminLog("查看赢分预警列表", ctx, reqdata)
		ctx.RespOK()
	} else {
		// 导出Excel逻辑（这里需要实现导出功能）
		logs.Info("导出功能暂未实现")
		ctx.RespErrString(true, &errcode, "导出功能暂未实现")
	}
}

// batchQueryUserInfoForWarning 批量查询用户信息（用于预警列表）
func (c *RiskController) batchQueryUserInfoForWarning(results []map[string]interface{}) {
	if len(results) == 0 {
		return
	}

	// 获取用户ID列表（去重）
	var userIds []int32
	userIdMap := make(map[int32]bool)
	for _, result := range results {
		userId := int32(abugo.GetInt64FromInterface(result["UserId"]))
		if !userIdMap[userId] {
			userIds = append(userIds, userId)
			userIdMap[userId] = true
		}
	}

	// 查询用户基本信息（注册时间）
	userInfoMap := make(map[int32]map[string]interface{})
	if len(userIds) > 0 {
		// 构建IN查询
		userIdsStr := make([]string, len(userIds))
		for i, id := range userIds {
			userIdsStr[i] = fmt.Sprintf("%d", id)
		}

		query := fmt.Sprintf("UserId IN (%s)", strings.Join(userIdsStr, ","))

		type UserInfo struct {
			UserId       int32  `gorm:"column:UserId"`
			RegisterTime string `gorm:"column:RegisterTime"`
		}

		var userInfos []UserInfo
		err := server.Db().GormDao().Table("x_user").Where(query).
			Select("UserId, RegisterTime").
			Scan(&userInfos).Error

		if err == nil {
			for _, user := range userInfos {
				userInfoMap[user.UserId] = map[string]interface{}{
					"RegisterTime": user.RegisterTime,
				}
			}
		}
	}

	// 批量查询用户充值总额
	rechargeMap := c.batchQueryUserRechargeForWarning(userIds)

	// 将用户信息添加到结果中
	for i := range results {
		userId := int32(abugo.GetInt64FromInterface(results[i]["UserId"]))

		// 设置注册时间
		if userInfo, exists := userInfoMap[userId]; exists {
			results[i]["RegisterTime"] = userInfo["RegisterTime"]
		} else {
			results[i]["RegisterTime"] = ""
		}

		// 设置充值总额
		if totalRecharge, exists := rechargeMap[userId]; exists {
			results[i]["TotalRecharge"] = totalRecharge
		} else {
			results[i]["TotalRecharge"] = 0
		}
	}
}

// batchQueryUserRechargeForWarning 批量查询用户充值总额（用于预警列表）
// userIds: 用户ID列表
// 返回: map[userId]totalRecharge
func (c *RiskController) batchQueryUserRechargeForWarning(userIds []int32) map[int32]float64 {
	rechargeMap := make(map[int32]float64)

	if len(userIds) == 0 {
		return rechargeMap
	}

	// 构建IN查询
	userIdsStr := make([]string, len(userIds))
	for i, id := range userIds {
		userIdsStr[i] = fmt.Sprintf("%d", id)
	}

	query := fmt.Sprintf("UserId IN (%s) AND State = 5", strings.Join(userIdsStr, ","))

	type RechargeSum struct {
		UserId        int32   `gorm:"column:UserId"`
		TotalRecharge float64 `gorm:"column:TotalRecharge"`
	}

	var rechargeSums []RechargeSum
	err := server.Db().GormDao().Table("x_recharge").
		Select("UserId, COALESCE(SUM(RealAmount), 0) as TotalRecharge").
		Where(query).
		Group("UserId").
		Scan(&rechargeSums).Error

	if err == nil {
		for _, recharge := range rechargeSums {
			rechargeMap[recharge.UserId] = recharge.TotalRecharge
		}
	}

	return rechargeMap
}
