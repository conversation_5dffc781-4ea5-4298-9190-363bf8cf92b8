package controller

import (
	"context"
	"errors"
	"fmt"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ChatController struct {
}

func (c *ChatController) Init() {
	group := server.Http().NewGroup("/api/chat")
	{
		group.Post("/getconfig", c.getconfig)
		group.Post("/setconfig", c.setconfig)
		group.Post("/blacklist", c.blacklist)
		group.Post("/blackadd", c.blackadd)
		group.Post("/blackmodify", c.blackmodify)
		group.Post("/blackdelete", c.blackdelete)
		group.Post("/hongbaodetail", c.hongbaodetail)
		group.Post("/hong_bao_conditions", c.hong_bao_conditions)
		group.Post("/hong_bao_condition_add", c.hong_bao_condition_add)
	}
}

func (c *ChatController) getconfig(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "聊天室管理", "聊天室设置", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	data := gin.H{
		"HongBaoBeiShu": server.GetConfigString(token.SellerId, 0, "HongBaoBeiShu"),
	}
	ctx.RespOK(data)
}

func (c *ChatController) setconfig(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId      int
		HongBaoBeiShu float32
		GoogleCode    string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.HongBaoBeiShu < 0 {
		ctx.RespErrString(true, &errcode, "参数不正确")
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "聊天室管理", "聊天室设置", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.SetConfig(reqdata.SellerId, 0, "HongBaoBeiShu", fmt.Sprint(reqdata.HongBaoBeiShu))
	ctx.RespOK()
}

func (c *ChatController) blacklist(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		UserId    int
		Page      int
		PageSize  int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "聊天室管理", "聊天室设置", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	total, data := server.Db().Table("x_chat_black").OrderBy("id desc").Where(where).PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("total", total)
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *ChatController) blackadd(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserIds    []int
		Enter      int
		HongBao    int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "聊天室管理", "聊天室设置", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	for i := 0; i < len(reqdata.UserIds); i++ {
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", reqdata.UserIds[i], nil)
		data, _ := server.Db().Table("x_user").Select("SellerId,ChannelId").Where(where).GetOne()
		if data != nil {
			server.Db().Conn().Exec("insert into x_chat_black(SellerId,ChannelId,UserId,Enter,HongBao)values(?,?,?,?,?)",
				(*data)["SellerId"], (*data)["ChannelId"], reqdata.UserIds[i], reqdata.Enter, reqdata.HongBao)
		}
	}
	ctx.RespOK()
}

func (c *ChatController) blackmodify(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int
		Enter      int
		HongBao    int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "聊天室管理", "聊天室设置", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.Db().Conn().Exec("update x_chat_black set Enter = ?  , HongBao = ? where UserId = ?", reqdata.Enter, reqdata.HongBao, reqdata.UserId)
	ctx.RespOK()
}

func (c *ChatController) blackdelete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserIds    []int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "聊天室管理", "聊天室设置", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	for i := 0; i < len(reqdata.UserIds); i++ {
		server.Db().Conn().Exec("delete from x_chat_black where UserId = ?", reqdata.UserIds[i])
	}
	ctx.RespOK()
}

func (c *ChatController) hongbaodetail(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		UserId    int
		Page      int
		PageSize  int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "聊天室管理", "红包明细", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	total, data := server.Db().Table("x_chat_hongbao_detail").OrderBy("id desc").Where(where).PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("total", total)
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *ChatController) hong_bao_condition_add(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int

		Id                int
		DayMax            int
		OnlineTime        int
		RechargeNum       float64
		MemberRechargeNum float64
		MemberSum         int

		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "聊天室管理", "抢红包条件", "改", "改抢红包条件")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tb := server.DaoxHashGame().XChatHongbaoCondition
	if reqdata.Id > 0 {
		err := tb.WithContext(context.Background()).Save(&model.XChatHongbaoCondition{
			ID:                int32(reqdata.Id),
			SellerID:          int32(reqdata.SellerId),
			DayMax:            int32(reqdata.DayMax),
			OnlineTime:        int32(reqdata.OnlineTime),
			RechargeNum:       decimal.NewFromFloat(reqdata.RechargeNum).InexactFloat64(),
			MemberRechargeNum: decimal.NewFromFloat(reqdata.MemberRechargeNum).InexactFloat64(),
			MemberSum:         int32(reqdata.MemberSum),
		})
		if err != nil {
			logs.Error("hong_bao_conditions Save ", err)
			ctx.RespErrString(true, &errcode, "db创建失败")
			return
		}
	} else {
		err := tb.WithContext(context.Background()).Create(&model.XChatHongbaoCondition{
			SellerID:          int32(reqdata.SellerId),
			DayMax:            int32(reqdata.DayMax),
			OnlineTime:        int32(reqdata.OnlineTime),
			RechargeNum:       decimal.NewFromFloat(reqdata.RechargeNum).InexactFloat64(),
			MemberRechargeNum: decimal.NewFromFloat(reqdata.MemberRechargeNum).InexactFloat64(),
			MemberSum:         int32(reqdata.MemberSum),
		})
		if err != nil {
			logs.Error("hong_bao_conditions Save ", err)
			ctx.RespErrString(true, &errcode, "db创建失败")
			return
		}
	}

	ctx.RespOK()
}

func (c *ChatController) hong_bao_conditions(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "聊天室管理", "抢红包条件", "查", "查抢红包条件")
	if token == nil {
		return
	}
	tb := server.DaoxHashGame().XChatHongbaoCondition
	db := tb.WithContext(context.Background())
	if reqdata.SellerId > 0 {
		db = db.Where(tb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	infos, err := db.First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("hong_bao_conditions find ", err)
		ctx.RespErrString(true, &errcode, "db查询失败 "+err.Error())
		return
	}
	if infos == nil {
		infos = &model.XChatHongbaoCondition{}
	}
	ctx.RespOK(infos)
}
