package active

import "xserver/db/active"

type DefineModReq struct {
	Id        int
	SellerId  int
	ChannelId int `validate:"required"`
	ActiveId  int `validate:"required"`

	Title           string
	Memo            string
	AuditType       int `validate:"oneof=1 2"`
	State           int `validate:"oneof=1 2"`
	Sort            int
	EffectStartTime int64
	EffectEndTime   int64
	TitleImg        string
	ExtReward       float64
	MinLiuShui      float64
	MinDeposit      float64
	MaxReward       float64
	ValidRecharge   float64
	ValidLiuShui    float64
	//TrxPrice        float64  //TrxPrice按后台配置VIP流水价格计算，所以不可更改
	GoogleCode string

	CiRiConfig      *active.ACiRiConfig `json:"CiRiConfig" form:"CiRiConfig"`
	Config          string
	BaseConfig      string
	TopImg          string // 置顶图片
	IsTop           uint8  `validate:"oneof=1 2"` // 是否置顶 1是 2否
	TopSort         int    // 置顶顺序
	TitleImgCn      string
	TitleImgEn      string
	TopImgEn        string // 置顶图片英文
	GameType        string // 游戏分类
	TitleImgLang    string // 图片其他语言
	TopImgLang      string // 置顶图片其他语言
	DetailImgLang   string // 详情图片其他语言
	PcDetailImgLang string
	IsUseDetail     int
	Cats            string
	TitleLang       string
	CustomerUrl     string

	// 新增字段，对应 x_active_define 表的新字段
	AwardType  int32  // 奖金类别：1:返水奖励，2:升级奖励，3:每月奖励，4:充值奖励，5:签到奖励，6:闯关奖励，7:特别奖励
	AwardTab   int32  // 奖金页面分类：1:一般奖金，2:欢迎奖金，3:VIP奖金，4:特殊奖金，1000:不显示
	GiftWallet int32  // 赠送钱包类型：1:真金钱包，2:彩金钱包
	AwardData  string // 彩金钱包下的活动励配置数据

}
