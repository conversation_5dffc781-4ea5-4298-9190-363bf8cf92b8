package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"path"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/server"
	"xserver/utils"
	"xserver/utilsmodel"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
)

type ReportController struct {
	querying bool
}

func (c *ReportController) Init() {
	group := server.Http().NewGroup("/api/report")
	{
		group.Post("/list", c.list)
		group.Post("/custom", c.custom)
		group.Post("/wallet_snapshot", c.wallet_snapshot)
		group.Post("/same_blockinfo", c.same_blockinfo)
		group.Post("/report_dailly", c.report_dailly)
		group.Post("/report_dailly_good", c.report_dailly_good)
		group.Post("/report_keep", c.report_keep)
		group.Post("/report_rank", c.report_rank)
		group.Post("/report_maker", c.report_maker)
		group.Post("/report_comprehensive", c.report_comprehensive)
		group.Post("/report_synthesize", c.report_synthesize)
		group.Post("/report_game", c.report_game)
		group.Post("/custom_third", c.custom_third)
		group.Post("/report_recharge_range", c.report_recharge_range)
		group.Post("/report_bet_range", c.report_bet_range)
		group.Post("/report_profit_loss_range", c.report_profit_loss_range)
		group.Post("/report_lang_game_type", c.report_lang_game_type)
	}
	server.Http().GetNoAuth("/api/report_comprehensive", c.report_comprehensiveex)
	c.querying = false
}

func (c *ReportController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		StartTime int64
		EndTime   int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "综合简报", "查") && !server.Auth2(token, "报表统计", "游戏业绩", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.SellerId == -1 {
		reqdata.SellerId = 0
	}
	total, data := db.SellerDailly_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.StartTime, reqdata.EndTime)
	ctx.Put("data", data)

	ctx.Put("total", total)
	server.WriteAdminLog("查看报表", ctx, reqdata)
	ctx.RespOK()
}

func specialAgentName(specialAgent int) string {
	if specialAgent == 1 {
		return "独立代理"
	}
	return "公司官网"
}

func (c *ReportController) custom(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId      []int32
		Page          int
		PageSize      int
		StartTime     int64
		EndTime       int64
		GameId        []int
		RoomLevel     int
		IsGameAddress int //1官方地址 2客户地址
		Address       string
		IsFirst       int //1首次 2非首次
		Symbol        string
		ToAddress     string
		ChannelId     []int
		Export        int
		OrderBy       int //排序 1降序,2升序
		TopAgentId    []int
		SpecialAgent  int
		IsTest        int
		CSGroup       string
		GameType      int
		HostTagId     int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "客户统计", "查"), &errcode, "权限不足") {
		return
	}
	if token.SellerId > 0 {
		if len(reqdata.SellerId) == 0 || reqdata.SellerId[0] != int32(token.SellerId) {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}
	//if reqdata.SellerId == -1 {
	//	reqdata.SellerId = 0
	//}
	if reqdata.Symbol != "trx" && reqdata.Symbol != "usdt" {
		reqdata.Symbol = ""
	}
	where := abugo.AbuDbWhere{}
	//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	// 运营商多选
	sellerIdStr := utils.ToSellers(reqdata.SellerId)
	if len(sellerIdStr) > 0 {
		where.Add("and", "x_custom_dailly.SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
	}
	where.Add("and", "x_custom_dailly.Symbol", "=", reqdata.Symbol, "")
	where.Add("and", "x_custom_dailly.IsTest", "=", reqdata.IsTest, 0)
	topagentid := ""
	for i := 0; i < len(reqdata.TopAgentId); i++ {
		topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	}
	if len(topagentid) > 0 {
		topagentid = topagentid[0 : len(topagentid)-1]
		where.Add("and", "x_custom_dailly.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	}
	where.Add("and", "x_custom_dailly.SpecialAgent", "=", reqdata.SpecialAgent, 0)
	where.Add("and", "x_custom_dailly.RecordDate", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "x_custom_dailly.RecordDate", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	where.Add("and", "x_custom_dailly.CSGroup", "=", reqdata.CSGroup, "")

	if reqdata.GameType == -1 { //转账游戏
		where.Add("and", "x_custom_dailly.GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12, 13,301,302,303,313,323,331,332,333)", 0)
	} else if reqdata.GameType == -2 { //余额游戏
		where.Add("and", "x_custom_dailly.GameId", "in", "(101,102,103,104,105,106,116,126,136,131,132,133,134,135,201,202,203,204,205,206)", 0)
	}
	if len(reqdata.GameId) > 0 {
		in := ""
		for _, v := range reqdata.GameId {
			if v == 0 {
				continue
			}
			in += fmt.Sprintf("%d,", v)
		}
		if len(in) > 0 {
			in = in[0 : len(in)-1]
			where.Add("and", "x_custom_dailly.GameId", "in", fmt.Sprintf("(%s)", in), 0)
		}
	}
	where.Add("and", "x_custom_dailly.RoomLevel", "=", reqdata.RoomLevel, 0)
	where.Add("and", "x_custom_dailly.NewGuys", "=", reqdata.IsFirst, 0)
	where.Add("and", "x_custom_dailly.Address", "=", reqdata.Address, "")
	where.Add("and", "x_custom_dailly.ToAddress", "=", reqdata.ToAddress, "")
	where.Add("and", "x_host_tag.Id", "=", reqdata.HostTagId, 0)
	channelid := ""
	for i := 0; i < len(reqdata.ChannelId); i++ {
		channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	}
	if len(channelid) > 0 {
		channelid = channelid[0 : len(channelid)-1]
		where.Add("and", "x_custom_dailly.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	}

	if reqdata.ToAddress != "" {
		where.Add("and", "x_custom_dailly.IsGameAddress", "=", 2, 0)
	} else {
		where.Add("and", "x_custom_dailly.IsGameAddress", "=", reqdata.IsGameAddress, 0)
	}
	wheresql, wheredata := where.Sql()
	joinsql := "left join x_user on x_custom_dailly.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id"
	sql := fmt.Sprintf(`select
	sum(x_custom_dailly.BetCount) as BetCount,
	sum(x_custom_dailly.WinCount) as WinCount,
	sum(x_custom_dailly.BetAmount) as BetAmount,
	sum(x_custom_dailly.RewardAmount) as RewardAmount,
	sum(x_custom_dailly.GasFee) as GasFee,
	sum(x_custom_dailly.Fee) as Fee,
	sum(x_custom_dailly.LiuSui) as LiuSui,
	sum(x_custom_dailly.BetAmount - x_custom_dailly.RewardAmount) as Profit,
	sum(x_custom_dailly.ValidBetAmount) as ValidBetAmount from x_custom_dailly %s where %s`, joinsql, wheresql)
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	orderby := "desc"
	if reqdata.OrderBy == 2 {
		orderby = "asc"
	}

	pcount, _ := server.DbReport().Query(sql, wheredata)
	ptotal, _ := server.DbReport().Query(fmt.Sprintf("select count(x_custom_dailly.id) as total from x_custom_dailly %s where %s", joinsql, wheresql), wheredata)
	total := int32(abugo.GetInt64FromInterface((*ptotal)[0]["total"]))
	presult, _ := server.DbReport().Query(fmt.Sprintf("select x_custom_dailly.*,x_host_tag.TagName from x_custom_dailly %s where %s order by BetAmount - RewardAmount %s limit %d offset %d", joinsql, wheresql, orderby, reqdata.PageSize, (reqdata.Page-1)*reqdata.PageSize), wheredata)
	paddresscount, _ := server.DbReport().Query(fmt.Sprintf("SELECT DISTINCT count( DISTINCT x_custom_dailly.Address) AS Count FROM x_custom_dailly %s where %s", joinsql, wheresql), wheredata)

	if reqdata.Export != 1 {
		ctx.Put("addr", *paddresscount)
		ctx.Put("count", *pcount)
		ctx.Put("data", *presult)
		ctx.Put("total", total)
		server.WriteAdminLog("查看客户统计", ctx, reqdata)
		ctx.RespOK()
	} else {
		data := *presult
		sellerNameMap, err := InitSellerNameMap()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"运营商", "玩家id", "注册渠道", "投注渠道", "顶级代理", "游戏类型", "游戏名称", "钱包地址", "官方地址", "币种", "投注次数", "中奖次数", "转账金额", "有效投注", "充值有效流水", "平台赔付", "手续费", "平台收益", "系统磨损", "中奖率", "日期", "首次参与", "客服组", "标签"})
		for i, d := range data {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(d["SellerId"]))),
				d["UserId"],
				ChannelName(int(abugo.GetInt64FromInterface(d["ChannelId"]))),
				ChannelName(int(abugo.GetInt64FromInterface(d["BetChannelId"]))),
				d["TopAgentId"],
				getGameTypeByGameId(int(abugo.GetInt64FromInterface(d["GameId"]))),
				fmt.Sprintf("%s(%s)", GameName[int(abugo.GetInt64FromInterface(d["GameId"]))], RoomName[int(abugo.GetInt64FromInterface(d["RoomLevel"]))]),
				d["Address"],
				d["ToAddress"],
				d["Symbol"],
				d["BetCount"],
				d["WinCount"],
				d["BetAmount"],
				d["LiuSui"],
				d["ValidBetAmount"],
				d["RewardAmount"],
				d["Fee"],
				abugo.GetFloat64FromInterface(d["BetAmount"]) - abugo.GetFloat64FromInterface(d["RewardAmount"]),
				d["GasFee"],
				fmt.Sprintf("%.2f%%", (float64(abugo.GetInt64FromInterface(d["WinCount"]))/float64(abugo.GetInt64FromInterface(d["BetCount"])))*100),
				d["RecordDate"],
				d["NewGuys"],
				d["CSGroup"],
				d["TagName"],
			})
		}
		filename := "export_custom_" + time.Now().Format("20060102150405") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出订单", ctx, reqdata)
	}
}

func (c *ReportController) custom_third(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page         int     //页码
		PageSize     int     //每页数量
		SellerId     []int32 //	运营商id
		UserId       int     //玩家id
		ChannelId    []int   //渠道id
		ThirdType    int     //三方类型
		Brand        string  //游戏类型
		IsFirst      int     //1首次 2非首次
		OrderBy      int     //排序 1降序,2升序
		SpecialAgent int     // 玩家来源 1独立代理,2公司官网
		TopAgentId   []int   //顶级代理id
		GameName     string  //游戏名称

		StartTime int64 //开始时间
		EndTime   int64 //结束时间

		Export int //是否导出 1导出

		CSGroup   string //客服组
		GameId    string // 游戏id
		HostTagId int    // 标签id
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "三方客户统计", "查"), &errcode, "权限不足") {
		return
	}
	if token.SellerId > 0 {
		if len(reqdata.SellerId) == 0 || reqdata.SellerId[0] != int32(token.SellerId) {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}
	//if reqdata.SellerId == -1 {
	//	reqdata.SellerId = 0
	//}

	where := abugo.AbuDbWhere{}
	//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	// 运营商多选
	sellerIdStr := utils.ToSellers(reqdata.SellerId)
	if len(sellerIdStr) > 0 {
		where.Add("and", "x_custom_third.SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
	}
	channelid := ""
	for i := 0; i < len(reqdata.ChannelId); i++ {
		channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	}
	if len(channelid) > 0 {
		channelid = channelid[0 : len(channelid)-1]
		where.Add("and", "x_custom_third.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	}
	where.Add("and", "x_custom_third.UserId", "=", reqdata.UserId, 0)
	topagentid := ""
	for i := 0; i < len(reqdata.TopAgentId); i++ {
		topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	}
	if len(topagentid) > 0 {
		topagentid = topagentid[0 : len(topagentid)-1]
		where.Add("and", "x_custom_third.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	}
	where.Add("and", "x_custom_third.SpecialAgent", "=", reqdata.SpecialAgent, 0)
	where.Add("and", "x_custom_third.RecordDate", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "x_custom_third.RecordDate", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	where.Add("and", "x_custom_third.IsFirst", "=", reqdata.IsFirst, 0)
	where.Add("and", "x_custom_third.ThirdType", "=", reqdata.ThirdType, 0)
	where.Add("and", "x_custom_third.Brand", "=", reqdata.Brand, "")
	where.Add("and", "x_custom_third.CSGroup", "=", reqdata.CSGroup, "")
	where.Add("and", "x_custom_third.GameName", "=", reqdata.GameName, "")
	where.Add("and", "x_custom_third.GameId", "=", reqdata.GameId, "")
	where.Add("and", "x_host_tag.Id", "=", reqdata.HostTagId, 0)

	wheresql, wheredata := where.Sql()
	joinsql := "left join x_user on x_custom_third.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id"
	sql := fmt.Sprintf(`select
	sum(x_custom_third.BetCount) as BetCount,
	sum(x_custom_third.WinCount) as WinCount,
	sum(x_custom_third.BetAmount) as BetAmount,
	sum(x_custom_third.WinAmount) as WinAmount,
	sum(x_custom_third.LiuSui) as LiuSui,
	sum(x_custom_third.Fee) as Fee,
	sum(x_custom_third.BetAmount - x_custom_third.WinAmount) as Profit,
	sum(x_custom_third.ValidBetAmount) as ValidBetAmount from x_custom_third %s where %s`, joinsql, wheresql)
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 5000000
	}
	orderby := "desc"
	if reqdata.OrderBy == 2 {
		orderby = "asc"
	}

	pcount, _ := server.DbReport().Query(sql, wheredata)
	ptotal, _ := server.DbReport().Query(fmt.Sprintf("select count(x_custom_third.id) as total from x_custom_third %s where %s", joinsql, wheresql), wheredata)
	total := int32(abugo.GetInt64FromInterface((*ptotal)[0]["total"]))

	presult, _ := server.DbReport().Query(fmt.Sprintf("select x_custom_third.*,x_host_tag.TagName from x_custom_third %s where %s order by BetAmount - WinAmount %s limit %d offset %d", joinsql, wheresql, orderby, reqdata.PageSize, (reqdata.Page-1)*reqdata.PageSize), wheredata)
	usercount, _ := server.DbReport().Query(fmt.Sprintf("SELECT DISTINCT count( DISTINCT x_custom_third.UserId) AS Count FROM x_custom_third %s where %s", joinsql, wheresql), wheredata)
	if reqdata.Export != 1 {
		ctx.Put("usercount", *usercount)
		ctx.Put("count", *pcount)
		ctx.Put("data", *presult)
		ctx.Put("total", total)
		server.WriteAdminLog("查看三方客户统计", ctx, reqdata)
		ctx.RespOK()
	} else {
		xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_三方客户统计_%s", time.Now().Format("20060102150405")))
		defer xlsx.Close()
		xlsx.Open()
		xlsx.SetTitle("SellerId", "运营商")
		xlsx.SetTitle("UserId", "玩家Id")
		xlsx.SetTitle("BetChannelId", "投注渠道")
		xlsx.SetTitle("TopAgentId", "顶级代理")
		xlsx.SetTitle("ChannelId", "注册渠道")
		xlsx.SetTitleEx("ThirdType", "游戏类型", map[string]interface{}{"1": "哈希彩票", "2": "电子游戏", "3": "棋牌游戏", "4": "哈希电子", "5": "真人视讯", "6": "体育竞技", "7": "德州扑克"})
		xlsx.SetTitle("Brand", "游戏子类型")
		xlsx.SetTitle("GameName", "游戏名称")
		xlsx.SetTitle("Symbol", "币种")
		xlsx.SetTitle("BetCount", "投注次数")
		xlsx.SetTitle("WinCount", "赢次数")
		xlsx.SetTitle("LiuSui", "有效投注")
		xlsx.SetTitle("ValidBetAmount", "充值有效流水")
		xlsx.SetTitle("BetAmount", "投注金额")
		xlsx.SetTitle("WinAmount", "平台赔付")
		xlsx.SetTitle("Fee", "手续费")
		xlsx.SetTitle("Profit", "平台收益")
		xlsx.SetTitle("WinRate", "中奖率")
		xlsx.SetTitleEx("IsFirst", "首次参与", map[string]interface{}{"1": "是", "2": "否"})
		xlsx.SetTitle("RecordDate", "日期")
		xlsx.SetTitle("CSGroup", "客服组")

		xlsx.SetTitleStyle()
		sellerNameMap, err := InitSellerNameMap()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		for i := 0; i < len(*presult); i++ {
			for k, v := range (*presult)[i] {
				if k == "ChannelId" {
					xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
				} else if k == "BetChannelId" {
					xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
				} else if k == "SellerId" {
					xlsx.SetValue(k, SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(v))), int64(i+2))
				} else {
					xlsx.SetValue(k, v, int64(i+2))
				}
			}
			betamount := abugo.GetFloat64FromInterface((*presult)[i]["BetAmount"])
			winamount := abugo.GetFloat64FromInterface((*presult)[i]["WinAmount"])
			v := math.Floor((betamount-winamount)*100) / 100
			xlsx.SetValue("Profit", v, int64(i+2))

			wincount := abugo.GetFloat64FromInterface((*presult)[i]["WinCount"])
			betcount := abugo.GetFloat64FromInterface((*presult)[i]["BetCount"])
			if betcount == 0 {
				xlsx.SetValue("WinRate", 0, int64(i+2))
			} else {
				v := math.Floor(wincount/betcount*100) / 100
				xlsx.SetValue("WinRate", v, int64(i+2))
			}
		}
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
		ctx.RespOK()
		server.WriteAdminLog("导出三方客户统计", ctx, reqdata)
	}
}

func (c *ReportController) wallet_snapshot(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		StartTime int64
		EndTime   int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "钱包快照", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where := abugo.AbuDbWhere{}
	//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "RecordDate", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	where.Add("and", "RecordDate", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	total, presult := server.Db().Table("x_wallet_snapshot").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *presult)

	ctx.Put("total", total)
	server.WriteAdminLog("查看钱包快照", ctx, reqdata)
	ctx.RespOK()
}

func (c *ReportController) same_blockinfo(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		BlockHash string
		Symbol    string
		MinAmount float64
		MaxAmount float64
		StartTime int64
		EndTime   int64
		Address   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Page == 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize == 0 {
		reqdata.PageSize = 15
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "代理系统", "区块监控", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.MaxAmount > 0 {
		reqdata.MaxAmount += 0.01
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where1 := abugo.AbuDbWhere{}
	where1.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where1.Add("and", "BetCount", ">", 1, 0)
	where1.Add("and", "BlockHash", "=", reqdata.BlockHash, "")
	where1.Add("and", "Symbol", "=", reqdata.Symbol, "")
	where1.Add("and", "OrderTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	where1.Add("and", "OrderTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	where1.Add("and", "Amount", ">=", reqdata.MinAmount, float64(0))
	where1.Add("and", "Amount", "<", reqdata.MaxAmount, float64(0))
	where1.Add("and", "FromAddress", "=", reqdata.Address, "")
	total, presult := server.Db().Table("x_block_monitor").Select("OrderId,TrxPrice").OrderBy("BlockNum desc").Where(where1).PageData(reqdata.Page, reqdata.PageSize)
	data := []map[string]interface{}{}
	for i := 0; i < len(*presult); i++ {
		d := map[string]interface{}{}
		d["TrxPrice"] = (*presult)[i]["TrxPrice"]
		where2 := abugo.AbuDbWhere{}
		where2.Add("and", "Id", "=", (*presult)[i]["OrderId"], 0)
		porderdata, _ := server.Db().Table("x_order").Where(where2).GetOne()
		if porderdata == nil {
			continue
		}
		for k, v := range *porderdata {
			d[k] = v
		}
		where3 := abugo.AbuDbWhere{}
		where3.Add("and", "UserId", "=", (*porderdata)["UserId"], 0)
		puserdata, _ := server.Db().Table("x_user").Select("TopAgentId,AgentId").Where(where3).GetOne()
		if puserdata != nil {
			if (*puserdata)["TopAgentId"] != nil {
				d["TopAgentId"] = (*puserdata)["TopAgentId"]
			} else {
				d["TopAgentId"] = 0
			}
			if (*puserdata)["AgentId"] != nil {
				d["AgentId"] = (*puserdata)["AgentId"]
			} else {
				d["AgentId"] = 0
			}
		} else {
			d["TopAgentId"] = 0
			d["AgentId"] = 0
		}
		data = append(data, d)
	}
	ctx.Put("data", data)

	ctx.Put("total", total)
	ctx.RespOK()
}
func (c *ReportController) report_dailly(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		Symbol     string
		TopAgentId int
		StartTime  int64
		EndTime    int64
		ChannelId  int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "每日简报", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.TopAgentId == 0 {
		reqdata.TopAgentId = -1
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	where.Add("and", "TopAgentId", "=", reqdata.TopAgentId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, "")
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where.Add("and", "RecordDate", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "RecordDate", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	selectstr := `sum(NewUser) as NewUser,
	sum(NewUserValid) as NewUserValid,
	sum(BetUser) as BetUser,
	sum(BetUserValid) as BetUserValid,
	sum(BetCount) as BetCount,
	sum(WinCount) as WinCount,
	sum(BetAmount) as BetAmount,
	sum(RewardAmount) as RewardAmount,
	sum(GasFee) as GasFee,
	sum(GetedCommission) as GetedCommission`
	pdata, _ := server.Db().Table("x_report_dailly").Select(selectstr).Where(where).GetList()
	if pdata != nil {
		ctx.Put("count", *pdata)
	}
	total, presult := server.Db().Table("x_report_dailly").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *presult)

	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *ReportController) report_dailly_good(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		Symbol     string
		UserId     int
		TopAgentId int
		StartTime  int64
		EndTime    int64
		ChannelId  int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "每日简报", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	startTime := carbon.CreateFromTimestampMilli(reqdata.StartTime)
	if reqdata.StartTime == 0 {
		startTime = carbon.Parse("2023-01-01 00:00:00")
	}
	endTime := carbon.CreateFromTimestampMilli(reqdata.EndTime)
	if reqdata.EndTime == 0 {
		endTime = carbon.Now()
	}
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = token.ChannelId
	}
	sellerId := ""
	if reqdata.SellerId != 0 {
		sellerId = abugo.GetStringFromInterface2(reqdata.SellerId)
	}
	channelId := ""
	if reqdata.ChannelId != 0 {
		channelId = abugo.GetStringFromInterface2(reqdata.ChannelId)
	}
	userId := -1
	if reqdata.UserId != 0 {
		userId = reqdata.UserId
	}
	topAgentId := -1
	if reqdata.TopAgentId != 0 {
		topAgentId = reqdata.TopAgentId
	}
	result, err := server.DbReport().GormDao().Raw("CALL ReportManage_x_custom_dailly_GetListByDaily(?,?,?,?,?,?,?,?,?)", startTime.ToDateString(), endTime.ToDateString(),
		sellerId, channelId, reqdata.Symbol, userId, topAgentId, reqdata.Page, reqdata.PageSize).Rows()
	defer func() {
		result.Close()
	}()
	if err != nil {
		logs.Error("report_dailly_good raw %+v %+v", reqdata, err)
		ctx.RespErrString(true, &errcode, "查询失败")
		return
	}
	count := 0
	server.DbReport().GormDao().ScanRows(result, &count)
	ctx.Put("count", count)
	if count > 0 {
		if result.NextResultSet() {
			data := []map[string]any{}
			server.DbReport().GormDao().ScanRows(result, &data)
			ctx.Put("data", data[1:])
		}
		if result.NextResultSet() {
			data := []map[string]any{}
			server.DbReport().GormDao().ScanRows(result, &data)
			ctx.Put("total", data[1:])
		}
	}
	ctx.RespOK()
}

func (c *ReportController) report_keep(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		Symbol     string
		TopAgentId []int
		StartTime  int64
		EndTime    int64
		ChannelId  []int
		KeepType   int `validate:"required"` //1下注 2 有效下注  3新增 4有效新增
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if len(reqdata.TopAgentId) == 0 {
		reqdata.TopAgentId = []int{0}
	}
	if len(reqdata.ChannelId) == 0 {
		reqdata.ChannelId = []int{0}
	}

	tablename := ""
	if reqdata.KeepType == 1 {
		if ctx.RespErrString(!server.Auth2(token, "报表统计", "投注留存", "查"), &errcode, "权限不足") {
			return
		}
		tablename = "x_report_keep_bet"
	} else if reqdata.KeepType == 2 {
		if ctx.RespErrString(!server.Auth2(token, "报表统计", "有效投注留存", "查"), &errcode, "权限不足") {
			return
		}
		tablename = "x_report_keep_bet_valid"
	} else if reqdata.KeepType == 3 {
		if ctx.RespErrString(!server.Auth2(token, "报表统计", "新增留存", "查"), &errcode, "权限不足") {
			return
		}
		tablename = "x_report_keep_new"
	} else if reqdata.KeepType == 4 {
		if ctx.RespErrString(!server.Auth2(token, "报表统计", "有效新增留存", "查"), &errcode, "权限不足") {
			return
		}
		tablename = "x_report_keep_new_valid"
	}
	if ctx.RespErrString(tablename == "", &errcode, "KeepType不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	topagentid := ""
	for i := 0; i < len(reqdata.TopAgentId); i++ {
		topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	}
	if len(topagentid) > 0 {
		topagentid = topagentid[0 : len(topagentid)-1]
		where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	}
	channelid := ""
	for i := 0; i < len(reqdata.ChannelId); i++ {
		channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	}
	if len(channelid) > 0 {
		channelid = channelid[0 : len(channelid)-1]
		where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where.Add("and", "RecordDate", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "RecordDate", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	total, presult := server.Db().Table(tablename).Select("SellerId,ChannelId,RecordDate,Symbol,TopAgentId,day0,day1,day3,day7,day15").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *presult)

	ctx.Put("total", total)
	ctx.RespOK()
}
func (c *ReportController) report_rank(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page         int
		PageSize     int
		SellerId     int
		Symbol       string `validate:"required"`
		TopAgentId   []int
		StartTime    int64
		EndTime      int64
		RankCount    int
		GameId       int
		RoomLevel    int
		RankType     int `validate:"required"` //1赢,2输
		ChannelId    []int
		SpecialAgent int
		Export       int
		HostTagId    int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "排行统计", "查"), &errcode, "权限不足") {
		return
	}
	StartTime := abugo.TimeStampToLocalDate(reqdata.StartTime)
	if len(StartTime) == 0 {
		StartTime = abugo.GetLocalDate()
	}
	if reqdata.RankCount == 0 {
		reqdata.RankCount = 10
	}
	var pdata *[]map[string]interface{}
	if reqdata.RankType == 1 {
		where := abugo.AbuDbWhere{}
		where.Add("and", "x_report_rank_ex.RecordDate", "=", StartTime, 0)
		where.Add("and", "x_report_rank_ex.SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "x_report_rank_ex.GameId", "=", reqdata.GameId, 0)
		where.Add("and", "x_report_rank_ex.RoomLevel", "=", reqdata.RoomLevel, 0)
		where.Add("and", "x_host_tag.Id", "=", reqdata.HostTagId, 0)
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "x_report_rank_ex.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "x_report_rank_ex.Symbol", "=", reqdata.Symbol, 0)
		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "x_report_rank_ex.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "x_report_rank_ex.SpecialAgent", "=", reqdata.SpecialAgent, 0)
		wheresql, whereparam := where.Sql()
		whereparam = append(whereparam, reqdata.RankCount)
		joinsql := "left join x_user on x_report_rank_ex.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id"
		sql := fmt.Sprintf(`SELECT x_host_tag.TagName,x_report_rank_ex.*,x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount AS WinLost FROM x_report_rank_ex %s WHERE x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount > 0 and %s  ORDER BY x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount  DESC LIMIT ?`, joinsql, wheresql)
		presult, _ := server.Db().Query(sql, whereparam)
		for i := 0; i < len(*presult); i++ {
			channelid := int(abugo.GetInt64FromInterface((*presult)[i]["ChannelId"]))
			var pwinlost *[]map[string]interface{}
			if reqdata.SellerId > 0 {
				sql = "select sum(x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount) as TotalWinLost from x_report_rank_ex left join x_user on x_report_rank_ex.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id where x_report_rank_ex.SellerId = ? and x_report_rank_ex.RecordDate = ? and x_report_rank_ex.Address = ? and x_report_rank_ex.ChannelId = ? and x_report_rank_ex.Symbol = ?"
				params := []interface{}{reqdata.SellerId, StartTime, abugo.GetStringFromInterface((*presult)[i]["Address"]), channelid, reqdata.Symbol}
				if reqdata.HostTagId > 0 {
					sql += " and x_host_tag.Id = ?"
					params = append(params, reqdata.HostTagId)
				}
				pwinlost, _ = server.Db().Query(sql, params)
			} else {
				sql = "select sum(x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount) as TotalWinLost from x_report_rank_ex left join x_user on x_report_rank_ex.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id where x_report_rank_ex.RecordDate = ? and x_report_rank_ex.Address = ? and x_report_rank_ex.ChannelId = ? and x_report_rank_ex.Symbol = ?"
				params := []interface{}{StartTime, abugo.GetStringFromInterface((*presult)[i]["Address"]), channelid, reqdata.Symbol}
				if reqdata.HostTagId > 0 {
					sql += " and x_host_tag.Id = ?"
					params = append(params, reqdata.HostTagId)
				}
				pwinlost, _ = server.Db().Query(sql, params)
			}
			(*presult)[i]["TotalWinLost"] = (*pwinlost)[0]["TotalWinLost"]
		}
		pdata = presult
	}
	if reqdata.RankType == 2 {
		where := abugo.AbuDbWhere{}
		where.Add("and", "x_report_rank_ex.RecordDate", "=", StartTime, 0)
		where.Add("and", "x_report_rank_ex.SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "x_report_rank_ex.GameId", "=", reqdata.GameId, 0)
		where.Add("and", "x_report_rank_ex.RoomLevel", "=", reqdata.RoomLevel, 0)
		where.Add("and", "x_host_tag.Id", "=", reqdata.HostTagId, 0)
		channelid := ""
		for i := 0; i < len(reqdata.ChannelId); i++ {
			channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
		}
		if len(channelid) > 0 {
			channelid = channelid[0 : len(channelid)-1]
			where.Add("and", "x_report_rank_ex.ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
		}
		where.Add("and", "x_report_rank_ex.Symbol", "=", reqdata.Symbol, 0)
		topagentid := ""
		for i := 0; i < len(reqdata.TopAgentId); i++ {
			topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
		}
		if len(topagentid) > 0 {
			topagentid = topagentid[0 : len(topagentid)-1]
			where.Add("and", "x_report_rank_ex.TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
		}
		where.Add("and", "x_report_rank_ex.SpecialAgent", "=", reqdata.SpecialAgent, 0)
		wheresql, whereparam := where.Sql()
		joinsql := "left join x_user on x_report_rank_ex.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id"
		whereparam = append(whereparam, reqdata.RankCount)
		sql := fmt.Sprintf(`SELECT x_host_tag.TagName,x_report_rank_ex.*,x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount AS WinLost FROM x_report_rank_ex %sWHERE x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount < 0 and %s  ORDER BY x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount  asc LIMIT ?`, joinsql, wheresql)
		presult, _ := server.Db().Query(sql, whereparam)
		for i := 0; i < len(*presult); i++ {
			channelid := int(abugo.GetInt64FromInterface((*presult)[i]["ChannelId"]))
			var pwinlost *[]map[string]interface{}
			if reqdata.SellerId > 0 {
				sql = "select sum(x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount) as TotalWinLost from x_report_rank_ex left join x_user on x_report_rank_ex.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id where x_report_rank_ex.SellerId = ? and x_report_rank_ex.RecordDate = ? and x_report_rank_ex.Address = ? and x_report_rank_ex.ChannelId = ? and x_report_rank_ex.Symbol = ?"
				params := []interface{}{reqdata.SellerId, StartTime, abugo.GetStringFromInterface((*presult)[i]["Address"]), channelid, reqdata.Symbol}
				if reqdata.HostTagId > 0 {
					sql += " and x_host_tag.Id = ?"
					params = append(params, reqdata.HostTagId)
				}
				pwinlost, _ = server.Db().Query(sql, params)
			} else {
				sql = "select sum(x_report_rank_ex.BetAmount - x_report_rank_ex.RewardAmount) as TotalWinLost from x_report_rank_ex left join x_user on x_report_rank_ex.UserId = x_user.UserId left join x_channel_host on x_channel_host.host = x_user.regUrl left join x_host_tag on x_channel_host.hostTagId = x_host_tag.id where x_report_rank_ex.RecordDate = ? and x_report_rank_ex.Address = ? and x_report_rank_ex.ChannelId = ? and x_report_rank_ex.Symbol = ?"
				params := []interface{}{StartTime, abugo.GetStringFromInterface((*presult)[i]["Address"]), channelid, reqdata.Symbol}
				if reqdata.HostTagId > 0 {
					sql += " and x_host_tag.Id = ?"
					params = append(params, reqdata.HostTagId)
				}
				pwinlost, _ = server.Db().Query(sql, params)
			}
			(*presult)[i]["TotalWinLost"] = (*pwinlost)[0]["TotalWinLost"]
		}
		pdata = presult
	}
	if reqdata.Export == 0 {
		ctx.RespOK(*pdata)
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"名次", "玩家Id", "投注地址", "玩法", "币种", "下注次数", "中奖次数", "下注金额", "中奖金额", "手续费", "平台收益", "总输赢", "系统磨损", "中奖率", "标签"})
		for i, d := range *pdata {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				i + 1,
				d["UserId"],
				d["Address"],
				fmt.Sprintf("%s(%s)", GameName[int(abugo.GetInt64FromInterface(d["GameId"]))], RoomName[int(abugo.GetInt64FromInterface(d["RoomLevel"]))]),
				d["Symbol"],
				d["BetCount"],
				d["WinCount"],
				d["BetAmount"],
				d["RewardAmount"],
				d["Fee"],
				math.Round((abugo.GetFloat64FromInterface(d["BetAmount"])-abugo.GetFloat64FromInterface(d["RewardAmount"]))*100) / 100,
				d["TotalWinLost"],
				d["GasFee"],
				fmt.Sprintf("%.2f%%", (float64(abugo.GetInt64FromInterface(d["WinCount"]))/float64(abugo.GetInt64FromInterface(d["BetCount"])))*100),
				d["TagName"],
			})
		}
		filename := "export_rank_" + time.Now().Format("20060102150405") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出排行统计", ctx, reqdata)
	}
}

// func (c *ReportController) test(ctx *abugo.AbuHttpContent) {
// 	server.Db().QueryNoResult("UPDATE x_user_address SET CreateTime = DATE_ADD(CreateTime,INTERVAL -1 DAY);")
// 	server.Db().QueryNoResult("UPDATE x_report_keep_bet SET RecordDate = DATE_ADD(RecordDate,INTERVAL -1 DAY);")
// 	server.Db().QueryNoResult("UPDATE x_report_keep_bet_valid SET RecordDate = DATE_ADD(RecordDate,INTERVAL -1 DAY);")
// 	server.Db().QueryNoResult("UPDATE x_report_keep_new SET RecordDate = DATE_ADD(RecordDate,INTERVAL -1 DAY);")
// 	server.Db().QueryNoResult("UPDATE x_report_keep_new_valid SET RecordDate = DATE_ADD(RecordDate,INTERVAL -1 DAY);")
// 	server.Db().QueryNoResult("UPDATE x_user_address_dailly SET RecordDate = DATE_ADD(RecordDate,INTERVAL -1 DAY);")
// 	ctx.RespOK()
// }

func (c *ReportController) report_maker(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		StartTime int64
		EndTime   int64
		GameType  int
		Address   string
		UserId    int
		GameId    int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "出块监控", "查"), &errcode, "权限不足") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "FromAddress", "=", reqdata.Address, "")
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	if reqdata.GameType == 1 {
		where.Add("and", "GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12, 301,302,331,332)", 0)
	}
	if reqdata.GameType == 2 {
		where.Add("and", "GameId", "in", "(101,102,103,104,105,131,132,133,134,135,201,202,203,204,205)", 0)
	}

	where.Add("and", "GameId", "=", reqdata.GameId, 0)

	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	blockmakers, _ := server.Db().Table("x_order").Select("DISTINCT BlockMaker").Where(where).GetList()
	retdata := []map[string]interface{}{}
	for _, v := range *blockmakers {
		maker := abugo.GetStringFromInterface(v["BlockMaker"])
		itemdata := map[string]interface{}{}
		itemdata["BlockMaker"] = maker
		if maker != "" {
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "Symbol", "=", "trx", "")
			where1.Add("and", "BlockMaker", "=", maker, "")
			where1.Add("and", "FromAddress", "=", reqdata.Address, "")
			where1.Add("and", "UserId", "=", reqdata.UserId, 0)
			if reqdata.GameType == 1 {
				where.Add("and", "GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12, 301,302,331,332)", 0)
			}
			if reqdata.GameType == 2 {
				where.Add("and", "GameId", "in", "(101,102,103,104,105,131,132,133,134,135,201,202,203,204,205)", 0)
			}
			where1.Add("and", "GameId", "=", reqdata.GameId, 0)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			d1, _ := server.Db().Table("x_order").Select("count(Id) as BetCountTrx").Where(where1).GetOne()
			d3, _ := server.Db().Table("x_order").Select("sum(Amount) as BetAmountTrx").Where(where1).GetOne()
			d4, _ := server.Db().Table("x_order").Select("sum(RewardAmount) as WinAmountTrx").Where(where1).GetOne()
			where1.Add("and", "IsWin", "=", 1, nil)
			d2, _ := server.Db().Table("x_order").Select("count(Id) as WinCountTrx").Where(where1).GetOne()
			itemdata["BetCountTrx"] = abugo.GetFloat64FromInterface((*d1)["BetCountTrx"])
			itemdata["WinCountTrx"] = abugo.GetFloat64FromInterface((*d2)["WinCountTrx"])
			itemdata["BetAmountTrx"] = abugo.GetFloat64FromInterface((*d3)["BetAmountTrx"])
			itemdata["WinAmountTrx"] = abugo.GetFloat64FromInterface((*d4)["WinAmountTrx"])
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "Symbol", "=", "usdt", "")
			where1.Add("and", "BlockMaker", "=", maker, "")
			where1.Add("and", "FromAddress", "=", reqdata.Address, "")
			where1.Add("and", "UserId", "=", reqdata.UserId, 0)
			if reqdata.GameType == 1 {
				where.Add("and", "GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12, 301,302,331,332)", 0)
			}
			if reqdata.GameType == 2 {
				where.Add("and", "GameId", "in", "(101,102,103,104,105,131,132,133,134,135,201,202,203,204,205)", 0)
			}
			where1.Add("and", "GameId", "=", reqdata.GameId, 0)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			d1, _ := server.Db().Table("x_order").Select("count(Id) as BetCountUsdt").Where(where1).GetOne()
			d3, _ := server.Db().Table("x_order").Select("sum(Amount) as BetAmountUsdt").Where(where1).GetOne()
			d4, _ := server.Db().Table("x_order").Select("sum(RewardAmount) as WinAmountUsdt").Where(where1).GetOne()
			where1.Add("and", "IsWin", "=", 1, nil)
			d2, _ := server.Db().Table("x_order").Select("count(Id) as WinCountUsdt").Where(where1).GetOne()
			itemdata["BetCountUsdt"] = abugo.GetFloat64FromInterface((*d1)["BetCountUsdt"])
			itemdata["WinCountUsdt"] = abugo.GetFloat64FromInterface((*d2)["WinCountUsdt"])
			itemdata["BetAmountUsdt"] = abugo.GetFloat64FromInterface((*d3)["BetAmountUsdt"])
			itemdata["WinAmountUsdt"] = abugo.GetFloat64FromInterface((*d4)["WinAmountUsdt"])
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "Symbol", "=", "trx", "")
			where1.Add("and", "BlockMaker", "=", maker, "")
			where1.Add("and", "FromAddress", "=", reqdata.Address, "")
			where1.Add("and", "UserId", "=", reqdata.UserId, 0)
			if reqdata.GameType == 1 {
				where.Add("and", "GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12, 301,302,331,332)", 0)
			}
			if reqdata.GameType == 2 {
				where.Add("and", "GameId", "in", "(101,102,103,104,105,131,132,133,134,135,201,202,203,204,205)", 0)
			}
			where1.Add("and", "GameId", "=", reqdata.GameId, 0)
			where1.Add("and", "IsFanBei", "=", 1, nil)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			//d1, _ := server.Db().Table("x_order").Select("count(Id) as BetCountTrx").Where(where1).GetOne()
			//d3, _ := server.Db().Table("x_order").Select("sum(Amount) as BetAmountTrx").Where(where1).GetOne()
			//d4, _ := server.Db().Table("x_order").Select("sum(RewardAmount) as WinAmountTrx").Where(where1).GetOne()
			where1.Add("and", "IsWin", "=", 1, nil)
			d2, _ := server.Db().Table("x_order").Select("count(Id) as WinCountTrx").Where(where1).GetOne()
			//itemdata["FanBeiBetCountTrx"] = abugo.GetInt64FromInterface((*d1)["BetCountTrx"])
			itemdata["FanBeiWinCountTrx"] = abugo.GetFloat64FromInterface((*d2)["WinCountTrx"])
			//itemdata["FanBeiBetAmountTrx"] = abugo.GetInt64FromInterface((*d3)["BetAmountTrx"])
			//itemdata["FanBeiWinAmountTrx"] = abugo.GetInt64FromInterface((*d4)["WinAmountTrx"])
		}
		{
			where1 := abugo.AbuDbWhere{}
			where1.Add("and", "Symbol", "=", "usdt", "")
			where1.Add("and", "BlockMaker", "=", maker, "")
			where1.Add("and", "FromAddress", "=", reqdata.Address, "")
			where1.Add("and", "UserId", "=", reqdata.UserId, 0)
			if reqdata.GameType == 1 {
				where.Add("and", "GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12, 301,302,331,332)", 0)
			}
			if reqdata.GameType == 2 {
				where.Add("and", "GameId", "in", "(101,102,103,104,105,131,132,133,134,135,201,202,203,204,205)", 0)
			}
			where1.Add("and", "GameId", "=", reqdata.GameId, 0)
			where1.Add("and", "IsFanBei", "=", 1, nil)
			where1.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
			where1.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
			// d1, _ := server.Db().Table("x_order").Select("count(Id) as BetCountUsdt").Where(where1).GetOne()
			// d3, _ := server.Db().Table("x_order").Select("sum(Amount) as BetAmountUsdt").Where(where1).GetOne()
			//d4, _ := server.Db().Table("x_order").Select("sum(RewardAmount) as WinAmountUsdt").Where(where1).GetOne()
			where1.Add("and", "IsWin", "=", 1, nil)
			d2, _ := server.Db().Table("x_order").Select("count(Id) as WinCountUsdt").Where(where1).GetOne()
			//itemdata["FanBeiBetCountUsdt"] = abugo.GetInt64FromInterface((*d1)["BetCountUsdt"])
			itemdata["FanBeiWinCountUsdt"] = abugo.GetFloat64FromInterface((*d2)["WinCountUsdt"])
			//itemdata["FanBeiBetAmountUsdt"] = abugo.GetInt64FromInterface((*d3)["BetAmountUsdt"])
			//["FanBeiWinAmountUsdt"] = abugo.GetInt64FromInterface((*d4)["WinAmountUsdt"])
		}
		retdata = append(retdata, itemdata)
	}
	ctx.RespOK(retdata)
}

func (c *ReportController) report_comprehensiveex(ctx *abugo.AbuHttpContent) {
	conn, err := upGrader.Upgrade(ctx.Gin().Writer, ctx.Gin().Request, nil)
	if err != nil {
		return
	}
	if c.querying {
		r := gin.H{"code": 1, "msg": "另外一个查询正在进行"}
		b, _ := json.Marshal(r)
		conn.WriteMessage(websocket.TextMessage, b)
		conn.Close()
		return
	}
	c.querying = true
	defer func() {
		c.querying = false
	}()
	_, msgdata, err := conn.ReadMessage()
	if err != nil {
		r := gin.H{"code": 1, "msg": err.Error()}
		b, _ := json.Marshal(r)
		conn.WriteMessage(websocket.TextMessage, b)
		conn.Close()
		return
	}
	type RequestData struct {
		Token string
	}
	reqdata := RequestData{}
	err = json.Unmarshal(msgdata, &reqdata)
	if err != nil {
		r := gin.H{"code": 1, "msg": err.Error()}
		b, _ := json.Marshal(r)
		conn.WriteMessage(websocket.TextMessage, b)
		conn.Close()
		return
	}
	strurl := "http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/report/report_comprehensive"

	requestBody := bytes.NewBuffer(msgdata)
	client := &http.Client{}
	req, err := http.NewRequest("POST", strurl, requestBody)
	if err != nil {
		r := gin.H{"code": 1, "msg": err.Error()}
		b, _ := json.Marshal(r)
		conn.WriteMessage(websocket.TextMessage, b)
		conn.Close()
		return
	}
	req.Header.Set("x-token", reqdata.Token)
	resp, err := client.Do(req)
	if err != nil {
		r := gin.H{"code": 1, "msg": err.Error()}
		b, _ := json.Marshal(r)
		conn.WriteMessage(websocket.TextMessage, b)
		conn.Close()
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		r := gin.H{"code": 1, "msg": fmt.Sprintf("状态码:%d", resp.StatusCode)}
		b, _ := json.Marshal(r)
		conn.WriteMessage(websocket.TextMessage, b)
		conn.Close()
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		r := gin.H{"code": 1, "msg": err.Error()}
		b, _ := json.Marshal(r)
		conn.WriteMessage(websocket.TextMessage, b)
		conn.Close()
		return
	}
	r := gin.H{"code": 1, "msg": string(body)}
	b, _ := json.Marshal(r)
	conn.WriteMessage(websocket.TextMessage, b)
	conn.Close()
}

func (c *ReportController) report_comprehensive(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId     []int32
		ChannelId    []int
		StartTime    int64
		EndTime      int64
		Symbol       string
		UserId       int
		TopAgentId   int
		SpecialAgent int
		VsStartTime  int64
		VsEndTime    int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = []int{token.ChannelId}
	}
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "综合简报", "查"), &errcode, "权限不足") {
		return
	}
	if token.SellerId > 0 {
		if len(reqdata.SellerId) == 0 || reqdata.SellerId[0] != int32(token.SellerId) {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime

	VsStartTime := reqdata.VsStartTime
	VsEndTime := reqdata.VsEndTime

	var channelIdStr string
	if len(reqdata.ChannelId) > 0 {
		channelIdStr = utils.ToChannels(reqdata.ChannelId)
	}
	var sellerIdStr string
	if len(reqdata.SellerId) > 0 {
		sellerIdStr = utils.ToSellers(reqdata.SellerId)
	}
	StartTimeStr := abugo.TimeStampToLocalTime(StartTime)
	EndTimeStr := abugo.TimeStampToLocalTime(EndTime)
	VsStartTimeStr := abugo.TimeStampToLocalTime(VsStartTime)
	VsEndTimeStr := abugo.TimeStampToLocalTime(VsEndTime)

	var result []utilsmodel.Comprehensive
	result = []utilsmodel.Comprehensive{}

	raw := server.DbReport().Gorm().Raw("CALL ReportManage_x_custom_dailly_GetList(?,?,?,?,?,?,?,?)", StartTimeStr, EndTimeStr,
		sellerIdStr, channelIdStr, reqdata.Symbol, reqdata.UserId, reqdata.SpecialAgent, reqdata.TopAgentId).Scan(&result)
	err = raw.Error
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	var vsResult []utilsmodel.Comprehensive
	vsResult = []utilsmodel.Comprehensive{}
	if VsStartTime > 0 && VsEndTime > 0 {
		vsraw := server.DbReport().Gorm().Raw("CALL ReportManage_x_custom_dailly_GetList(?,?,?,?,?,?,?,?)", VsStartTimeStr, VsEndTimeStr,
			sellerIdStr, channelIdStr, reqdata.Symbol, reqdata.UserId, reqdata.SpecialAgent, reqdata.TopAgentId).Scan(&vsResult)
		err = vsraw.Error
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
	}

	var BetUserCount int32
	if len(result) > 0 {
		// 从后往前遍历，避免索引问题
		for i := len(result) - 1; i >= 0; i-- {
			if result[i].TypeId == 0 {
				BetUserCount = result[i].TotalBetUser
				result = append(result[:i], result[i+1:]...)
				break // 找到后退出循环
			}
		}
	}

	var VsBetUserCount int32
	if len(vsResult) > 0 {
		// 从后往前遍历，避免索引问题
		for i := len(vsResult) - 1; i >= 0; i-- {
			if vsResult[i].TypeId == 0 {
				VsBetUserCount = vsResult[i].TotalBetUser
				vsResult = append(vsResult[:i], vsResult[i+1:]...)
				break // 找到后退出循环
			}
		}
	}

	// 取两个结果的并集，并计算增长率
	var comprehensiveUpRateResult []utilsmodel.ComprehensiveUpRateResult
	comprehensiveUpRateResult = []utilsmodel.ComprehensiveUpRateResult{}

	// 调试信息：输出结果集大小
	logs.Info("report_comprehensive debug - result length:", len(result))
	logs.Info("report_comprehensive debug - vsResult length:", len(vsResult))

	// 输出result中的TypeId
	for i, r := range result {
		logs.Info("report_comprehensive debug - result[%d] TypeId: %d, Type: %s", i, r.TypeId, r.Type)
	}

	// 输出vsResult中的TypeId
	for i, r := range vsResult {
		logs.Info("report_comprehensive debug - vsResult[%d] TypeId: %d, Type: %s", i, r.TypeId, r.Type)
	}

	// 如果没有对比数据，直接转换result为ComprehensiveUpRateResult
	if len(vsResult) == 0 {
		logs.Info("report_comprehensive debug - no vsResult data, converting result directly")
		for _, value := range result {
			item := utilsmodel.ComprehensiveUpRateResult{
				Type:           value.Type,
				TypeId:         value.TypeId,
				Amount:         value.Amount,
				Fee:            value.Fee,
				LiuSui:         value.LiuSui,
				RewardAmount:   value.RewardAmount,
				TotalBetCount:  value.TotalBetCount,
				TotalBetUser:   value.TotalBetUser,
				WinCount:       value.WinCount,
				ValidBetAmount: value.ValidBetAmount,
				TotalWin: func() decimal.Decimal {
					if value.Type == "qipai" || value.Type == "dianzhi" || value.Type == "trading" || value.Type == "updown" {
						return value.Amount.Sub(value.RewardAmount).Add(value.Fee).Round(2)
					} else {
						return value.Amount.Sub(value.RewardAmount).Round(2)
					}
				}(),
				// TotalWin:       value.Amount.Sub(value.RewardAmount).Add(value.Fee).Round(2),
				// 对比数据为空
				VsAmount:         decimal.Zero,
				VsFee:            decimal.Zero,
				VsLiuSui:         decimal.Zero,
				VsRewardAmount:   decimal.Zero,
				VsTotalBetCount:  0,
				VsTotalBetUser:   0,
				VsWinCount:       0,
				VsValidBetAmount: decimal.Zero,
				VsTotalWin:       decimal.Zero,
				// 增长率为100%（相对于0），保留两位小数
				AmountUpRate:        calculateUpRate(value.Amount, decimal.Zero),
				FeeUpRate:           calculateUpRate(value.Fee, decimal.Zero),
				LiuSuiUpRate:        calculateUpRate(value.LiuSui, decimal.Zero),
				RewardUpRate:        calculateUpRate(value.RewardAmount, decimal.Zero),
				TotalBetCountUpRate: calculateUpRateInt32(value.TotalBetCount, 0),
				TotalBetUserUpRate:  calculateUpRateInt32(value.TotalBetUser, 0),
				WinCountUpRate:      calculateUpRateInt32(value.WinCount, 0),
				ValidBetUpRate:      calculateUpRate(value.ValidBetAmount, decimal.Zero),
				TotalWinUpRate: func() decimal.Decimal {
					if value.Type == "qipai" || value.Type == "dianzhi" || value.Type == "trading" || value.Type == "updown" {
						return calculateUpRate(value.Amount.Sub(value.RewardAmount).Add(value.Fee), decimal.Zero)
					} else {
						return calculateUpRate(value.Amount.Sub(value.RewardAmount), decimal.Zero)
					}
				}(),
			}
			comprehensiveUpRateResult = append(comprehensiveUpRateResult, item)
		}
	} else {
		// 创建vsResult的映射，以TypeId为key，方便查找
		vsResultMap := make(map[int]utilsmodel.Comprehensive)
		for _, vsValue := range vsResult {
			vsResultMap[vsValue.TypeId] = vsValue
		}

		// 创建result的映射，用于检查vsResult中的项目是否已处理
		resultMap := make(map[int]utilsmodel.Comprehensive)
		for _, value := range result {
			resultMap[value.TypeId] = value
		}

		// 第一步：遍历result，为每个项目创建ComprehensiveUpRateResult
		for _, value := range result {
			var item utilsmodel.ComprehensiveUpRateResult

			if vsValue, exists := vsResultMap[value.TypeId]; exists {
				logs.Info("report_comprehensive debug - found intersection for TypeId: %d", value.TypeId)
				// 有对比数据的情况
				item = utilsmodel.ComprehensiveUpRateResult{
					Type:           value.Type,
					TypeId:         value.TypeId,
					Amount:         value.Amount,
					Fee:            value.Fee,
					LiuSui:         value.LiuSui,
					RewardAmount:   value.RewardAmount,
					TotalBetCount:  value.TotalBetCount,
					TotalBetUser:   value.TotalBetUser,
					WinCount:       value.WinCount,
					ValidBetAmount: value.ValidBetAmount,
					TotalWin: func() decimal.Decimal {
						if value.Type == "qipai" || value.Type == "dianzhi" || value.Type == "trading" || value.Type == "updown" {
							return value.Amount.Sub(value.RewardAmount).Add(value.Fee).Round(2)
						} else {
							return value.Amount.Sub(value.RewardAmount).Round(2)
						}
					}(),
					VsAmount:         vsValue.Amount,
					VsFee:            vsValue.Fee,
					VsLiuSui:         vsValue.LiuSui,
					VsRewardAmount:   vsValue.RewardAmount,
					VsTotalBetCount:  vsValue.TotalBetCount,
					VsTotalBetUser:   vsValue.TotalBetUser,
					VsWinCount:       vsValue.WinCount,
					VsValidBetAmount: vsValue.ValidBetAmount,
					VsTotalWin: func() decimal.Decimal {
						if value.Type == "qipai" || value.Type == "dianzhi" || value.Type == "trading" || value.Type == "updown" {
							return vsValue.Amount.Sub(vsValue.RewardAmount).Sub(vsValue.Fee).Round(2)
						} else {
							return vsValue.Amount.Sub(vsValue.RewardAmount).Round(2)
						}
					}(),
					// 计算增长率
					AmountUpRate:        calculateUpRate(value.Amount, vsValue.Amount),
					FeeUpRate:           calculateUpRate(value.Fee, vsValue.Fee),
					LiuSuiUpRate:        calculateUpRate(value.LiuSui, vsValue.LiuSui),
					RewardUpRate:        calculateUpRate(value.RewardAmount, vsValue.RewardAmount),
					TotalBetCountUpRate: calculateUpRateInt32(value.TotalBetCount, vsValue.TotalBetCount),
					TotalBetUserUpRate:  calculateUpRateInt32(value.TotalBetUser, vsValue.TotalBetUser),
					WinCountUpRate:      calculateUpRateInt32(value.WinCount, vsValue.WinCount),
					ValidBetUpRate:      calculateUpRate(value.ValidBetAmount, vsValue.ValidBetAmount),
					TotalWinUpRate: func() decimal.Decimal {
						if value.Type == "qipai" || value.Type == "dianzhi" || value.Type == "trading" || value.Type == "updown" {
							return calculateUpRate(value.Amount.Sub(value.RewardAmount).Add(value.Fee), vsValue.Amount.Sub(vsValue.RewardAmount).Sub(vsValue.Fee))
						} else {
							return calculateUpRate(value.Amount.Sub(value.RewardAmount), vsValue.Amount.Sub(vsValue.RewardAmount))
						}
					}(),
				}
			} else {
				logs.Info("report_comprehensive debug - no comparison data for TypeId: %d, using zero values", value.TypeId)
				// 没有对比数据的情况
				item = utilsmodel.ComprehensiveUpRateResult{
					Type:           value.Type,
					TypeId:         value.TypeId,
					Amount:         value.Amount,
					Fee:            value.Fee,
					LiuSui:         value.LiuSui,
					RewardAmount:   value.RewardAmount,
					TotalBetCount:  value.TotalBetCount,
					TotalBetUser:   value.TotalBetUser,
					WinCount:       value.WinCount,
					ValidBetAmount: value.ValidBetAmount,
					TotalWin: func() decimal.Decimal {
						if value.Type == "qipai" || value.Type == "dianzhi" || value.Type == "trading" || value.Type == "updown" {
							return value.Amount.Sub(value.RewardAmount).Add(value.Fee).Round(2)
						} else {
							return value.Amount.Sub(value.RewardAmount).Round(2)
						}
					}(),
					// 对比数据为空
					VsAmount:         decimal.Zero,
					VsFee:            decimal.Zero,
					VsLiuSui:         decimal.Zero,
					VsRewardAmount:   decimal.Zero,
					VsTotalBetCount:  0,
					VsTotalBetUser:   0,
					VsWinCount:       0,
					VsValidBetAmount: decimal.Zero,
					VsTotalWin:       decimal.Zero,
					// 增长率为100%（相对于0）或0%（如果当前值也为0）
					AmountUpRate:        calculateUpRate(value.Amount, decimal.Zero),
					FeeUpRate:           calculateUpRate(value.Fee, decimal.Zero),
					LiuSuiUpRate:        calculateUpRate(value.LiuSui, decimal.Zero),
					RewardUpRate:        calculateUpRate(value.RewardAmount, decimal.Zero),
					TotalBetCountUpRate: calculateUpRateInt32(value.TotalBetCount, 0),
					TotalBetUserUpRate:  calculateUpRateInt32(value.TotalBetUser, 0),
					WinCountUpRate:      calculateUpRateInt32(value.WinCount, 0),
					ValidBetUpRate:      calculateUpRate(value.ValidBetAmount, decimal.Zero),
				}
			}

			comprehensiveUpRateResult = append(comprehensiveUpRateResult, item)
		}

		// 第二步：遍历vsResult，添加在result中不存在的项目
		for _, vsValue := range vsResult {
			if _, exists := resultMap[vsValue.TypeId]; !exists {
				logs.Info("report_comprehensive debug - adding vsResult-only item for TypeId: %d", vsValue.TypeId)
				// 只在对比时间段存在的数据
				item := utilsmodel.ComprehensiveUpRateResult{
					Type:   vsValue.Type,
					TypeId: vsValue.TypeId,
					// 当前数据为空
					Amount:         decimal.Zero,
					Fee:            decimal.Zero,
					LiuSui:         decimal.Zero,
					RewardAmount:   decimal.Zero,
					TotalBetCount:  0,
					TotalBetUser:   0,
					WinCount:       0,
					ValidBetAmount: decimal.Zero,
					TotalWin:       decimal.Zero,
					// 对比数据
					VsAmount:         vsValue.Amount,
					VsFee:            vsValue.Fee,
					VsLiuSui:         vsValue.LiuSui,
					VsRewardAmount:   vsValue.RewardAmount,
					VsTotalBetCount:  vsValue.TotalBetCount,
					VsTotalBetUser:   vsValue.TotalBetUser,
					VsWinCount:       vsValue.WinCount,
					VsValidBetAmount: vsValue.ValidBetAmount,
					VsTotalWin: func() decimal.Decimal {
						if vsValue.Type == "qipai" || vsValue.Type == "dianzhi" || vsValue.Type == "trading" || vsValue.Type == "updown" {
							return vsValue.Amount.Sub(vsValue.RewardAmount).Add(vsValue.Fee).Round(2)
						} else {
							return vsValue.Amount.Sub(vsValue.RewardAmount).Round(2)
						}
					}(),
					// 增长率为-100%（从有值变为0）
					AmountUpRate:        calculateUpRate(decimal.Zero, vsValue.Amount),
					FeeUpRate:           calculateUpRate(decimal.Zero, vsValue.Fee),
					LiuSuiUpRate:        calculateUpRate(decimal.Zero, vsValue.LiuSui),
					RewardUpRate:        calculateUpRate(decimal.Zero, vsValue.RewardAmount),
					TotalBetCountUpRate: calculateUpRateInt32(0, vsValue.TotalBetCount),
					TotalBetUserUpRate:  calculateUpRateInt32(0, vsValue.TotalBetUser),
					WinCountUpRate:      calculateUpRateInt32(0, vsValue.WinCount),
					ValidBetUpRate:      calculateUpRate(decimal.Zero, vsValue.ValidBetAmount),
					TotalWinUpRate:      calculateUpRate(decimal.Zero, vsValue.Amount.Sub(vsValue.RewardAmount).Add(vsValue.Fee)),
				}
				comprehensiveUpRateResult = append(comprehensiveUpRateResult, item)
			}
		}
	}

	// 调试信息：输出最终结果大小
	logs.Info("report_comprehensive debug - final comprehensiveUpRateResult length:", len(comprehensiveUpRateResult))

	ctx.Put("BetUserCount", BetUserCount)
	ctx.Put("VsBetUserCount", VsBetUserCount)
	ctx.Put("BetUserCountUpRate", calculateUpRateInt32(BetUserCount, VsBetUserCount))
	ctx.Put("list", comprehensiveUpRateResult)
	ctx.RespOK()
}

// 计算增长率的辅助函数，保留两位小数
func calculateUpRate(current, vs decimal.Decimal) decimal.Decimal {
	if vs.IsZero() {
		if current.IsZero() {
			return decimal.Zero
		}

		if current.LessThan(decimal.Zero) {
			return decimal.NewFromInt(-100)
		}

		return decimal.NewFromInt(100) // 如果对比值为0，当前值不为0，则增长100%
	}

	if current.IsZero() {
		if vs.LessThan(decimal.Zero) {
			return decimal.NewFromInt(100)
		}

		return decimal.NewFromInt(-100) // 如果当前值为0，对比值不为0，则减少100%
	}

	result := current.Sub(vs).Div(vs).Mul(decimal.NewFromInt(100))
	return result.Round(2) // 保留两位小数
}

func calculateUpRateInt32(current, vs int32) decimal.Decimal {
	if vs == 0 {
		if current == 0 {
			return decimal.Zero
		}
		if current < 0 {
			return decimal.NewFromInt(-100)
		}

		return decimal.NewFromInt(100)
	}
	if current == 0 {
		if vs < 0 {
			return decimal.NewFromInt(100)
		}

		return decimal.NewFromInt(-100)
	}
	result := decimal.NewFromInt32(current - vs).Div(decimal.NewFromInt32(vs)).Mul(decimal.NewFromInt(100))
	return result.Round(2) // 保留两位小数
}

func (c *ReportController) report_synthesize(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId     int
		ChannelId    int
		StartTime    int64
		EndTime      int64
		Symbol       string
		UserId       int
		IncludeChild int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "综合简报", "查"), &errcode, "权限不足") {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}
	children := []interface{}{}
	if reqdata.UserId != 0 {
		children = append(children, reqdata.UserId)
		if reqdata.IncludeChild == 1 {
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", reqdata.UserId, 0)
			data, _ := server.Db().Table("x_agent_child").Select("Child").Where(where).GetList()
			for i := 0; i < len(*data); i++ {
				children = append(children, (*data)[i]["Child"])
			}
		}
	}
	childrensql := abugo.GetInSql(&children)
	//注册人数
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "in", childrensql, "")
		where.Add("and", "RegisterTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "RegisterTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		data, _ := server.Db().Table("x_user").Select("count(DISTINCT UserId) as RegisterCount").Where(where).GetOne()
		if data != nil {
			ctx.Put("RegisterCount", (*data)["RegisterCount"])
		}
	}
	// //转账玩法投注人数
	BetCount := 0
	{
		if reqdata.UserId == 0 {
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", 0, "")
			where.Add("and", "Symbol", "=", reqdata.Symbol, "")
			where.Add("and", "GameId", "<", 100, "")
			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			data, _ := server.Db().Table("x_order").Select("count(DISTINCT FromAddress) as BetCount").Where(where).GetOne()
			if data != nil {
				BetCount += int(abugo.GetInt64FromInterface((*data)["BetCount"]))
			}
		}
	}
	{
		where := abugo.AbuDbWhere{}
		if len(children) > 0 {
			where.Add("and", "UserId", "in", childrensql, "")
		} else {
			where.Add("and", "UserId", ">", 0, "")
		}
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "GameId", "<", 100, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		data, _ := server.Db().Table("x_order").Select("count(DISTINCT UserId) as BetCount").Where(where).GetOne()
		if data != nil {
			BetCount += int(abugo.GetInt64FromInterface((*data)["BetCount"]))
		}
	}
	ctx.Put("BetCountWallet", BetCount)
	BetCount = 0
	{
		where := abugo.AbuDbWhere{}
		if len(children) > 0 {
			where.Add("and", "UserId", "in", childrensql, "")
		} else {
			where.Add("and", "UserId", ">", 0, "")
		}
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		where.Add("and", "GameId", ">", 100, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		data, _ := server.Db().Table("x_order").Select("count(DISTINCT UserId) as BetCount").Where(where).GetOne()
		if data != nil {
			BetCount += int(abugo.GetInt64FromInterface((*data)["BetCount"]))
		}
	}
	ctx.Put("BetCountAmount", BetCount)
	ctx.RespOK()
}

func (c *ReportController) report_game(ctx *abugo.AbuHttpContent) {
	defer recover()
	reqdata := struct {
		SellerId     int
		ChannelId    []int
		StartTime    int64
		EndTime      int64
		Symbol       string
		GameType     int // 1 哈希游戏,2余额游戏,3一分哈希,4电子游戏,5真人视讯,6哈希电子,7哈希彩票,8体育竞技,9棋牌游戏,10德州游戏
		TopAgentId   []int
		SpecialAgent int
	}{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "综合简报", "查"), &errcode, "权限不足") {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 86400000
	}
	result := []interface{}{}
	if reqdata.GameType == 1 || reqdata.GameType == 0 {
		gameid := []int{1, 2, 3, 4, 5, 6, 7, 11, 12, 301, 302, 331, 332}
		for i := 0; i < len(gameid); i++ {
			item := make(map[string]interface{})
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
				where.Add("and", "Symbol", "=", reqdata.Symbol, "")
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "IsTest", "=", 2, nil)
				topagentid := ""
				for i := 0; i < len(reqdata.TopAgentId); i++ {
					topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
				}
				if len(topagentid) > 0 {
					topagentid = topagentid[0 : len(topagentid)-1]
					where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
				}
				where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
				r, _ := server.Db().Table("x_order").Where(where).Select("count(*) as BetCount,sum(Amount) as BetAmount,sum(RewardAmount) WinAmount,sum(GasFee) as GasFee").GetOne()
				item["BetCount"] = (*r)["BetCount"]
				item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
				item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
				item["GasFee"] = abugo.GetFloat64FromInterface((*r)["GasFee"])
				item["Symbol"] = reqdata.Symbol
				item["ChannelId"] = reqdata.ChannelId
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
				where.Add("and", "Symbol", "=", reqdata.Symbol, "")
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "IsWin", "=", 1, nil)
				where.Add("and", "IsTest", "=", 2, nil)
				topagentid := ""
				for i := 0; i < len(reqdata.TopAgentId); i++ {
					topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
				}
				if len(topagentid) > 0 {
					topagentid = topagentid[0 : len(topagentid)-1]
					where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
				}
				where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
				r, _ := server.Db().Table("x_order").Where(where).Select("count(*) as WinCount").GetOne()
				item["WinCount"] = (*r)["WinCount"]
			}
			usercount := 0
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
				where.Add("and", "Symbol", "=", reqdata.Symbol, "")
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "UserId", "=", 0, nil)
				where.Add("and", "IsTest", "=", 2, nil)
				topagentid := ""
				for i := 0; i < len(reqdata.TopAgentId); i++ {
					topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
				}
				if len(topagentid) > 0 {
					topagentid = topagentid[0 : len(topagentid)-1]
					where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
				}
				where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
				r, _ := server.Db().Table("x_order").Where(where).Select("count(DISTINCT FromAddress) as UserCount").GetOne()
				usercount += int(abugo.GetInt64FromInterface((*r)["UserCount"]))
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
				where.Add("and", "Symbol", "=", reqdata.Symbol, "")
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "UserId", ">", 0, nil)
				where.Add("and", "IsTest", "=", 2, nil)
				topagentid := ""
				for i := 0; i < len(reqdata.TopAgentId); i++ {
					topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
				}
				if len(topagentid) > 0 {
					topagentid = topagentid[0 : len(topagentid)-1]
					where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
				}
				where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
				r, _ := server.Db().Table("x_order").Where(where).Select("count(DISTINCT UserId) as UserCount").GetOne()
				usercount += int(abugo.GetInt64FromInterface((*r)["UserCount"]))
			}
			item["UserCount"] = usercount
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "State", "=", 1, nil)
				r, _ := server.Db().Table("x_game").Where(where).Select("RoomLevel,FeeRate,RewardRate,RewardRateEx").GetList()
				item["GameConfig"] = (*r)
			}
			item["GameType"] = 1
			item["GameId"] = gameid[i]
			item["Symbol"] = reqdata.Symbol
			item["ChannelId"] = reqdata.ChannelId
			result = append(result, item)
		}
	}
	if reqdata.GameType == 2 || reqdata.GameType == 0 {
		gameid := []int{101, 102, 103, 104, 105, 131, 132, 133, 134, 135, 201, 202, 203, 204, 205}
		for i := 0; i < len(gameid); i++ {
			item := make(map[string]interface{})
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
				where.Add("and", "Symbol", "=", reqdata.Symbol, "")
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "IsTest", "=", 2, nil)
				topagentid := ""
				for i := 0; i < len(reqdata.TopAgentId); i++ {
					topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
				}
				if len(topagentid) > 0 {
					topagentid = topagentid[0 : len(topagentid)-1]
					where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
				}
				where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
				r, _ := server.Db().Table("x_order").Where(where).Select("count(*) as BetCount,sum(Amount) as BetAmount,sum(RewardAmount) WinAmount,sum(GasFee) as GasFee").GetOne()
				item["BetCount"] = (*r)["BetCount"]
				item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
				item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
				item["GasFee"] = abugo.GetFloat64FromInterface((*r)["GasFee"])
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
				where.Add("and", "Symbol", "=", reqdata.Symbol, "")
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "IsWin", "=", 1, nil)
				where.Add("and", "IsTest", "=", 2, nil)
				topagentid := ""
				for i := 0; i < len(reqdata.TopAgentId); i++ {
					topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
				}
				if len(topagentid) > 0 {
					topagentid = topagentid[0 : len(topagentid)-1]
					where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
				}
				where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
				r, _ := server.Db().Table("x_order").Where(where).Select("count(*) as WinCount").GetOne()
				item["WinCount"] = (*r)["WinCount"]
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
				where.Add("and", "Symbol", "=", reqdata.Symbol, "")
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "UserId", ">", 0, nil)
				where.Add("and", "IsTest", "=", 2, nil)
				topagentid := ""
				for i := 0; i < len(reqdata.TopAgentId); i++ {
					topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
				}
				if len(topagentid) > 0 {
					topagentid = topagentid[0 : len(topagentid)-1]
					where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
				}
				where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
				r, _ := server.Db().Table("x_order").Where(where).Select("count(DISTINCT UserId) as UserCount").GetOne()
				item["UserCount"] = int(abugo.GetInt64FromInterface((*r)["UserCount"]))
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
				channelid := ""
				for i := 0; i < len(reqdata.ChannelId); i++ {
					channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
				}
				if len(channelid) > 0 {
					channelid = channelid[0 : len(channelid)-1]
					where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
				}
				where.Add("and", "GameId", "=", gameid[i], nil)
				where.Add("and", "State", "=", 1, nil)
				r, _ := server.Db().Table("x_game").Where(where).Select("RoomLevel,FeeRate,RewardRate,RewardRateEx").GetList()
				item["GameConfig"] = (*r)
			}
			item["GameType"] = 2
			item["GameId"] = gameid[i]
			item["Symbol"] = reqdata.Symbol
			item["ChannelId"] = reqdata.ChannelId
			result = append(result, item)
		}
	}
	// if reqdata.GameType == 3 || reqdata.GameType == 0 {
	// 	gameid := []int{101, 102, 103, 104, 105}
	// 	for i := 0; i < len(gameid); i++ {
	// 		item := make(map[string]interface{})
	// 		{
	// 			where := abugo.AbuDbWhere{}
	// 			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	// 			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	// 			where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	// 			where.Add("and", "SellerId", "=", reqdata.SellerId, nil)
	// 			channelid := ""
	// 			for i := 0; i < len(reqdata.ChannelId); i++ {
	// 				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	// 			}
	// 			if len(channelid) > 0 {
	// 				channelid = channelid[0 : len(channelid)-1]
	// 				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	// 			}
	// 			where.Add("and", "GameId", "=", gameid[i], nil)
	// 			where.Add("and", "IsTest", "=", 2, nil)
	// 			topagentid := ""
	// 			for i := 0; i < len(reqdata.TopAgentId); i++ {
	// 				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	// 			}
	// 			if len(topagentid) > 0 {
	// 				topagentid = topagentid[0 : len(topagentid)-1]
	// 				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	// 			}
	// 			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
	// 			r, _ := server.Db().Table("x_order").Where(where).Select("count(*) as BetCount,sum(Amount) as BetAmount,sum(RewardAmount) WinAmount,sum(GasFee) as GasFee").GetOne()
	// 			item["BetCount"] = (*r)["BetCount"]
	// 			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
	// 			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
	// 			item["GasFee"] = (*r)["GasFee"]
	// 		}
	// 		{
	// 			where := abugo.AbuDbWhere{}
	// 			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	// 			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	// 			where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	// 			where.Add("and", "SellerId", "=", reqdata.SellerId, nil)
	// 			channelid := ""
	// 			for i := 0; i < len(reqdata.ChannelId); i++ {
	// 				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	// 			}
	// 			if len(channelid) > 0 {
	// 				channelid = channelid[0 : len(channelid)-1]
	// 				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	// 			}
	// 			where.Add("and", "GameId", "=", gameid[i], nil)
	// 			where.Add("and", "IsWin", "=", 1, nil)
	// 			where.Add("and", "IsTest", "=", 2, nil)
	// 			topagentid := ""
	// 			for i := 0; i < len(reqdata.TopAgentId); i++ {
	// 				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	// 			}
	// 			if len(topagentid) > 0 {
	// 				topagentid = topagentid[0 : len(topagentid)-1]
	// 				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	// 			}
	// 			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
	// 			r, _ := server.Db().Table("x_order").Where(where).Select("count(*) as WinCount").GetOne()
	// 			item["WinCount"] = (*r)["WinCount"]
	// 		}
	// 		{
	// 			where := abugo.AbuDbWhere{}
	// 			where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	// 			where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	// 			where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	// 			where.Add("and", "SellerId", "=", reqdata.SellerId, nil)
	// 			channelid := ""
	// 			for i := 0; i < len(reqdata.ChannelId); i++ {
	// 				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	// 			}
	// 			if len(channelid) > 0 {
	// 				channelid = channelid[0 : len(channelid)-1]
	// 				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	// 			}
	// 			where.Add("and", "GameId", "=", gameid[i], nil)
	// 			where.Add("and", "UserId", ">", 0, nil)
	// 			where.Add("and", "IsTest", "=", 2, nil)
	// 			topagentid := ""
	// 			for i := 0; i < len(reqdata.TopAgentId); i++ {
	// 				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	// 			}
	// 			if len(topagentid) > 0 {
	// 				topagentid = topagentid[0 : len(topagentid)-1]
	// 				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	// 			}
	// 			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
	// 			r, _ := server.Db().Table("x_order").Where(where).Select("count(DISTINCT UserId) as UserCount").GetOne()
	// 			item["UserCount"] = int(abugo.GetInt64FromInterface((*r)["UserCount"]))
	// 		}

	// 		{
	// 			where := abugo.AbuDbWhere{}
	// 			where.Add("and", "SellerId", "=", reqdata.SellerId, nil)
	// 			channelid := ""
	// 			for i := 0; i < len(reqdata.ChannelId); i++ {
	// 				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	// 			}
	// 			if len(channelid) > 0 {
	// 				channelid = channelid[0 : len(channelid)-1]
	// 				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	// 			}
	// 			where.Add("and", "GameId", "=", gameid[i], nil)
	// 			where.Add("and", "State", "=", 1, nil)
	// 			r, _ := server.Db().Table("x_game").Where(where).Select("RoomLevel,FeeRate,RewardRate,RewardRateEx").GetList()
	// 			item["GameConfig"] = (*r)
	// 		}
	// 		item["GameType"] = 3
	// 		item["Symbol"] = reqdata.Symbol
	// 		item["ChannelId"] = reqdata.ChannelId
	// 		item["GameId"] = gameid[i]
	// 		result = append(result, item)
	// 	}
	// }
	if reqdata.GameType == 4 || reqdata.GameType == 0 {
		item := make(map[string]interface{})
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			r, _ := server.Db().Table("x_third_dianzhi").Where(where).Select("count(DISTINCT UserId) as UserCount,count(*) as BetCount,sum(BetAmount) as BetAmount,sum(WinAmount) WinAmount,0 as GasFee").GetOne()
			item["BetCount"] = (*r)["BetCount"]
			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
			item["GasFee"] = abugo.GetFloat64FromInterface((*r)["GasFee"])
			item["UserCount"] = (*r)["UserCount"]
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			where.End("and WinAmount > BetAmount")
			r, _ := server.Db().Table("x_third_dianzhi").Where(where).Select("count(*) as WinCount").GetOne()
			item["WinCount"] = (*r)["WinCount"]
		}
		item["GameType"] = 4
		item["Symbol"] = reqdata.Symbol
		item["ChannelId"] = reqdata.ChannelId
		result = append(result, item)
	}
	if reqdata.GameType == 5 || reqdata.GameType == 0 {
		item := make(map[string]interface{})
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			r, _ := server.Db().Table("x_third_live").Where(where).Select("count(DISTINCT UserId) as UserCount,count(*) as BetCount,sum(BetAmount) as BetAmount,sum(WinAmount) WinAmount,0 as GasFee").GetOne()
			item["BetCount"] = (*r)["BetCount"]
			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
			item["GasFee"] = (*r)["GasFee"]
			item["UserCount"] = (*r)["UserCount"]
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			where.End("and WinAmount > BetAmount")
			r, _ := server.Db().Table("x_third_live").Where(where).Select("count(*) as WinCount").GetOne()
			item["WinCount"] = (*r)["WinCount"]
		}
		item["GameType"] = 5
		item["Symbol"] = reqdata.Symbol
		item["ChannelId"] = reqdata.ChannelId
		result = append(result, item)
	}
	if reqdata.GameType == 6 || reqdata.GameType == 0 {
		item := make(map[string]interface{})
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			r, _ := server.Db().Table("x_third_quwei").Where(where).Select("count(DISTINCT UserId) as UserCount,count(*) as BetCount,sum(BetAmount) as BetAmount,sum(WinAmount) WinAmount,0 as GasFee").GetOne()
			item["BetCount"] = (*r)["BetCount"]
			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
			item["GasFee"] = (*r)["GasFee"]
			item["UserCount"] = (*r)["UserCount"]
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			where.End("and WinAmount > BetAmount")
			r, _ := server.Db().Table("x_third_quwei").Where(where).Select("count(*) as WinCount").GetOne()
			item["WinCount"] = (*r)["WinCount"]
		}
		item["GameType"] = 6
		item["Symbol"] = reqdata.Symbol
		item["ChannelId"] = reqdata.ChannelId
		result = append(result, item)
	}
	if reqdata.GameType == 7 || reqdata.GameType == 0 {
		item := make(map[string]interface{})
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			r, _ := server.Db().Table("x_third_lottery").Where(where).Select("count(DISTINCT UserId) as UserCount,count(*) as BetCount,sum(BetAmount) as BetAmount,sum(WinAmount) WinAmount,0 as GasFee").GetOne()
			item["BetCount"] = (*r)["BetCount"]
			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
			item["GasFee"] = (*r)["GasFee"]
			item["UserCount"] = (*r)["UserCount"]
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			where.End("and WinAmount > BetAmount")
			r, _ := server.Db().Table("x_third_lottery").Where(where).Select("count(*) as WinCount").GetOne()
			item["WinCount"] = (*r)["WinCount"]
		}
		item["GameType"] = 7
		item["Symbol"] = reqdata.Symbol
		item["ChannelId"] = reqdata.ChannelId
		result = append(result, item)
	}
	if reqdata.GameType == 8 || reqdata.GameType == 0 {
		item := make(map[string]interface{})
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			r, _ := server.Db().Table("x_third_sport").Where(where).Select("count(DISTINCT UserId) as UserCount,count(*) as BetCount,sum(BetAmount) as BetAmount,sum(WinAmount) WinAmount,0 as GasFee").GetOne()
			item["BetCount"] = (*r)["BetCount"]
			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
			item["GasFee"] = (*r)["GasFee"]
			item["UserCount"] = (*r)["UserCount"]
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			where.End("and WinAmount > BetAmount")
			r, _ := server.Db().Table("x_third_sport").Where(where).Select("count(*) as WinCount").GetOne()
			item["WinCount"] = (*r)["WinCount"]
		}
		item["GameType"] = 8
		item["Symbol"] = reqdata.Symbol
		item["ChannelId"] = reqdata.ChannelId
		result = append(result, item)
	}
	if reqdata.GameType == 9 || reqdata.GameType == 0 {
		item := make(map[string]interface{})
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			r, _ := server.Db().Table("x_third_qipai").Where(where).Select("count(DISTINCT UserId) as UserCount,count(*) as BetCount,sum(BetAmount) as BetAmount,sum(WinAmount) WinAmount,0 as GasFee").GetOne()
			item["BetCount"] = (*r)["BetCount"]
			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
			item["GasFee"] = (*r)["GasFee"]
			item["UserCount"] = (*r)["UserCount"]
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			where.End("and WinAmount > BetAmount")
			r, _ := server.Db().Table("x_third_qipai").Where(where).Select("count(*) as WinCount").GetOne()
			item["WinCount"] = (*r)["WinCount"]
		}
		item["GameType"] = 9
		item["Symbol"] = reqdata.Symbol
		item["ChannelId"] = reqdata.ChannelId
		result = append(result, item)
	}
	if reqdata.GameType == 10 || reqdata.GameType == 0 {
		item := make(map[string]interface{})
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			r, _ := server.Db().Table("x_third_texas").Where(where).Select("count(DISTINCT UserId) as UserCount,count(*) as BetCount,sum(BetAmount) as BetAmount,sum(WinAmount) WinAmount,0 as GasFee").GetOne()
			item["BetCount"] = (*r)["BetCount"]
			item["BetAmount"] = abugo.GetFloat64FromInterface((*r)["BetAmount"])
			item["WinAmount"] = abugo.GetFloat64FromInterface((*r)["WinAmount"])
			item["GasFee"] = (*r)["GasFee"]
			item["UserCount"] = (*r)["UserCount"]
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
			where.Add("and", "ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
			where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
			channelid := ""
			for i := 0; i < len(reqdata.ChannelId); i++ {
				channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
			}
			if len(channelid) > 0 {
				channelid = channelid[0 : len(channelid)-1]
				where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
			}
			topagentid := ""
			for i := 0; i < len(reqdata.TopAgentId); i++ {
				topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
			}
			if len(topagentid) > 0 {
				topagentid = topagentid[0 : len(topagentid)-1]
				where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
			}
			where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
			where.End("and WinAmount > BetAmount")
			r, _ := server.Db().Table("x_third_texas").Where(where).Select("count(*) as WinCount").GetOne()
			item["WinCount"] = (*r)["WinCount"]
		}
		item["GameType"] = 10
		item["Symbol"] = reqdata.Symbol
		item["ChannelId"] = reqdata.ChannelId
		result = append(result, item)
	}
	ctx.RespOK(result)
}

// report_recharge_range 充值区间分布统计
// 功能：按不同充值金额区间统计用户分布情况，包括各区间的充值人数、人数占比、充值金额、金额占比
func (c *ReportController) report_recharge_range(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 请求参数结构体
	type RequestData struct {
		SellerId   int   // 运营商ID，0表示全部
		ChannelIds []int `json:"ChannelIds"` // 渠道ID列表，支持多选
		StartTime  int64 // 开始时间戳（毫秒）
		EndTime    int64 // 结束时间戳（毫秒）
		TopAgentId []int // 顶级代理ID列表，支持多选
		Export     int   // 是否导出Excel，1表示导出
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 定义充值金额区间
	// 根据业务需求预定义11个充值金额区间，覆盖从小额到大额的充值范围
	ranges := []struct {
		Name     string  // 区间名称，用于显示
		MinValue float64 // 区间最小值（包含）
		MaxValue float64 // 区间最大值（不包含），0表示无上限
	}{
		{"0-10", 0, 10},                     // 小额充值区间
		{"10-50", 10, 50},                   //
		{"50-100", 50, 100},                 //
		{"100-500", 100, 500},               // 中等充值区间
		{"500-1000", 500, 1000},             //
		{"1000-5000", 1000, 5000},           //
		{"5000-10000", 5000, 10000},         // 大额充值区间
		{"10000-50000", 10000, 50000},       //
		{"50000-100000", 50000, 100000},     // 超大额充值区间
		{"100000-500000", 100000, 500000},   //
		{"500000-1000000", 500000, 1000000}, // 顶级充值区间
	}

	// 获取 GORM gen DAO 查询对象
	dao := server.DaoxHashGame()
	recharge := dao.XRecharge

	// 修改为按单笔充值金额分布统计
	// 查询所有符合条件的充值记录
	type RechargeRecord struct {
		UserID     int32   `gorm:"column:user_id"`
		RealAmount float64 `gorm:"column:real_amount"`
	}

	var rechargeRecords []RechargeRecord
	recordQuery := recharge.WithContext(ctx.Gin().Request.Context()).Where(recharge.State.Eq(5))

	// 添加基础筛选条件
	if reqdata.SellerId > 0 {
		recordQuery = recordQuery.Where(recharge.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if len(reqdata.ChannelIds) > 0 {
		channelIds := make([]int32, len(reqdata.ChannelIds))
		for i, id := range reqdata.ChannelIds {
			channelIds[i] = int32(id)
		}
		recordQuery = recordQuery.Where(recharge.ChannelID.In(channelIds...))
	}
	if len(reqdata.TopAgentId) > 0 {
		topAgentIds := make([]int32, len(reqdata.TopAgentId))
		for i, id := range reqdata.TopAgentId {
			topAgentIds[i] = int32(id)
		}
		recordQuery = recordQuery.Where(recharge.TopAgentID.In(topAgentIds...))
	}
	if reqdata.StartTime > 0 {
		startTimeStr := abugo.TimeStampToLocalTime(reqdata.StartTime)
		// 修复时区问题：使用 ParseInLocation 明确指定本地时区
		startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
		recordQuery = recordQuery.Where(recharge.CreateTime.Gte(startTime))
	}
	if reqdata.EndTime > 0 {
		endTimeStr := abugo.TimeStampToLocalTime(reqdata.EndTime)
		// 修复时区问题：使用 ParseInLocation 明确指定本地时区
		endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
		recordQuery = recordQuery.Where(recharge.CreateTime.Lte(endTime))
	}
	// 查询所有充值记录
	err = recordQuery.Select(
		recharge.UserID.As("user_id"),
		recharge.RealAmount.As("real_amount"),
	).Scan(&rechargeRecords)

	if err != nil {
		ctx.RespErrString(true, &errcode, "查询充值记录失败")
		return
	}

	// 按用户统计最大单笔充值金额和总充值金额
	type UserMaxAmount struct {
		UserID      int32
		MaxAmount   float64 // 最大单笔充值金额
		TotalAmount float64 // 用户总充值金额
	}

	userAmountMap := make(map[int32]*UserMaxAmount) // 用户充值统计
	totalAmount := float64(0)                       // 总充值金额

	for _, record := range rechargeRecords {
		if userAmount, exists := userAmountMap[record.UserID]; exists {
			// 用户已存在，更新最大金额和总金额
			if record.RealAmount > userAmount.MaxAmount {
				userAmount.MaxAmount = record.RealAmount
			}
			userAmount.TotalAmount += record.RealAmount
		} else {
			// 新用户，创建记录
			userAmountMap[record.UserID] = &UserMaxAmount{
				UserID:      record.UserID,
				MaxAmount:   record.RealAmount,
				TotalAmount: record.RealAmount,
			}
		}
		totalAmount += record.RealAmount // 累加总充值金额
	}

	totalUsers := float64(len(userAmountMap)) // 去重后的用户数

	// 按用户最大单笔充值金额分配到对应区间
	var resultData []map[string]interface{}

	for _, rangeItem := range ranges {
		users := 0           // 当前区间的用户数
		amount := float64(0) // 当前区间的充值金额

		// 遍历所有用户，按最大单笔充值金额分配到区间
		for _, userAmount := range userAmountMap {
			// 判断用户的最大单笔充值金额是否在当前区间范围内
			if userAmount.MaxAmount >= rangeItem.MinValue {
				// 检查是否小于最大值（如果最大值为0表示无上限）
				if rangeItem.MaxValue == 0 || userAmount.MaxAmount < rangeItem.MaxValue {
					users++                          // 用户数加1
					amount += userAmount.TotalAmount // 累加该用户的总充值金额
				}
			}
		}

		// 计算当前区间在总体中的占比
		userPercent := float64(0)   // 用户数占比
		amountPercent := float64(0) // 金额占比

		// 避免除零错误，只有总数大于0时才计算占比
		if totalUsers > 0 {
			userPercent = (float64(users) / totalUsers) * 100
		}
		if totalAmount > 0 {
			amountPercent = (amount / totalAmount) * 100
		}

		// 将当前区间的统计结果添加到结果集中
		resultData = append(resultData, map[string]interface{}{
			"Range":         rangeItem.Name,                       // 区间名称
			"Users":         users,                                // 充值人数（整数）
			"UserPercent":   fmt.Sprintf("%.2f%%", userPercent),   // 用户数占比（格式化为百分比）
			"Amount":        amount,                               // 充值金额（浮点数）
			"AmountPercent": fmt.Sprintf("%.2f%%", amountPercent), // 金额占比（格式化为百分比）
		})
	}

	// 在结果集最后添加总计行，汇总所有区间的数据
	resultData = append(resultData, map[string]interface{}{
		"Range":         "总计",            // 标识为总计行
		"Users":         int(totalUsers), // 总充值人数
		"UserPercent":   "100%",          // 人数占比固定为100%
		"Amount":        totalAmount,     // 总充值金额
		"AmountPercent": "100%",          // 金额占比固定为100%
	})

	// 根据Export参数判断是返回数据还是导出Excel
	if reqdata.Export != 1 {
		// 正常返回JSON数据给前端展示
		ctx.Put("data", resultData)
		server.WriteAdminLog("查看充值区间分布", ctx, reqdata)
		ctx.RespOK()
	} else {
		// 导出Excel文件
		excel := excelize.NewFile()

		// 设置Excel表头，定义各列的标题
		excel.SetSheetRow("Sheet1", "A1", &[]string{
			"充值区间", // A列：显示金额区间范围
			"充值人数", // B列：该区间的充值用户数量
			"人数占比", // C列：该区间人数占总人数的百分比
			"充值金额", // D列：该区间的总充值金额
			"金额占比", // E列：该区间金额占总金额的百分比
		})

		// 遍历统计结果，填充Excel数据行
		for i, data := range resultData {
			// 从第2行开始填充数据（第1行是表头）
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				data["Range"],         // 充值区间名称
				data["Users"],         // 充值人数
				data["UserPercent"],   // 人数占比（已格式化为百分比字符串）
				data["Amount"],        // 充值金额
				data["AmountPercent"], // 金额占比（已格式化为百分比字符串）
			})
		}

		// 生成唯一的文件名，包含时间戳避免文件名冲突
		filename := "export_recharge_range_" + time.Now().Format("20060102150405") + ".xlsx"

		// 保存Excel文件到服务器的导出目录
		err = excel.SaveAs(server.ExportDir() + "/" + filename)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出文件失败")
			return
		}

		// 返回文件下载链接给前端
		ctx.Put("filename", "/exports/"+filename)
		server.WriteAdminLog("导出充值区间分布", ctx, reqdata)
		ctx.RespOK()
	}
}

// 游戏投注区间分布
func (c *ReportController) report_bet_range(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 请求参数结构体
	type RequestData struct {
		SellerId   int     // 运营商ID，单选，0表示全部
		ChannelIds []int   // 渠道ID列表，支持多选
		StartTime  int64   // 开始时间戳（毫秒）
		EndTime    int64   // 结束时间戳（毫秒）
		TopAgentId []int   // 顶级代理ID列表，支持多选
		GameTypes  []int32 // 游戏类型ID列表，支持多选：-1=哈希游戏, -2=余额哈希, 1=电子, 2=棋牌, 3=小游戏, 4=彩票, 5=真人, 6=体育, 7=德州扑克
		Export     int     // 是否导出Excel，1表示导出
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 权限验证
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "区间分布", "查"), &errcode, "权限不足") {
		return
	}

	// 运营商权限验证
	if token.SellerId > 0 {
		if reqdata.SellerId != token.SellerId {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelIds = []int{token.ChannelId}
	}

	// 定义投注金额区间 - 根据用户提供的图片中的区间
	ranges := []struct {
		Name     string  // 区间名称，用于显示
		MinValue float64 // 区间最小值（包含）
		MaxValue float64 // 区间最大值（不包含），0表示无上限
	}{
		{"0-100", 0, 100},
		{"100-500", 100, 500},
		{"500-1,000", 500, 1000},
		{"1,000-5,000", 1000, 5000},
		{"5,000-10,000", 5000, 10000},
		{"10,000-50,000", 10000, 50000},
		{"50,000+", 50000, 0},
	}

	// 数据库实际值映射表
	// x_custom_dailly表GameId: -1=哈希游戏, -2=余额哈希
	// x_custom_third表ThirdType: 1=彩票, 2=电子, 3=棋牌, 4=小游戏, 5=真人, 6=体育, 7=德州扑克(假设)
	standardToDbThirdType := map[int32]int32{
		1: 2, // 标准"电子"(1) -> 数据库ThirdType(2)
		2: 3, // 标准"棋牌"(2) -> 数据库ThirdType(3)
		3: 4, // 标准"小游戏"(3) -> 数据库ThirdType(4)
		4: 1, // 标准"彩票"(4) -> 数据库ThirdType(1)
		5: 5, // 标准"真人"(5) -> 数据库ThirdType(5)
		6: 6, // 标准"体育"(6) -> 数据库ThirdType(6)
		7: 7, // 标准"德州扑克"(7) -> 数据库ThirdType(7)
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}

	// 运营商条件
	if reqdata.SellerId > 0 {
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	}

	// 时间条件
	if reqdata.StartTime > 0 {
		where.Add("and", "RecordDate", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	}
	if reqdata.EndTime > 0 {
		where.Add("and", "RecordDate", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	}

	// 渠道条件
	if len(reqdata.ChannelIds) > 0 {
		channelIdStr := ""
		for i, id := range reqdata.ChannelIds {
			if i > 0 {
				channelIdStr += ","
			}
			channelIdStr += fmt.Sprintf("%d", id)
		}
		where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelIdStr), nil)
	}

	// 顶级代理条件
	if len(reqdata.TopAgentId) > 0 {
		topAgentIdStr := ""
		for i, id := range reqdata.TopAgentId {
			if i > 0 {
				topAgentIdStr += ","
			}
			topAgentIdStr += fmt.Sprintf("%d", id)
		}
		where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topAgentIdStr), nil)
	}

	wheresql, wheredata := where.Sql()

	// 存储所有投注记录
	type BetRecord struct {
		UserID    int32   `json:"user_id"`
		BetAmount float64 `json:"bet_amount"`
	}

	var allBetRecords []BetRecord

	// 根据游戏类型构建查询条件
	var hashGameIds []int32  // x_custom_dailly表的GameId
	var thirdTypeIds []int32 // x_custom_third表的ThirdType（数据库实际值）

	if len(reqdata.GameTypes) == 0 {
		// 如果没有指定游戏类型，查询所有
		hashGameIds = []int32{-1, -2}               // 哈希游戏、余额哈希
		thirdTypeIds = []int32{1, 2, 3, 4, 5, 6, 7} // 数据库中的ThirdType值：1=彩票,2=电子,3=棋牌,4=小游戏,5=真人,6=体育,7=德州扑克
	} else {
		// 根据选择的游戏类型构建ID列表
		for _, standardGameTypeId := range reqdata.GameTypes {
			if standardGameTypeId == -1 || standardGameTypeId == -2 {
				// 哈希游戏类型，直接使用标准ID查询x_custom_dailly表
				hashGameIds = append(hashGameIds, standardGameTypeId)
			} else {
				// 其他游戏类型，转换为数据库ThirdType值查询x_custom_third表
				if dbThirdType, exists := standardToDbThirdType[standardGameTypeId]; exists {
					thirdTypeIds = append(thirdTypeIds, dbThirdType)
				}
			}
		}
	}

	// 查询x_custom_dailly表（哈希游戏相关）
	if len(hashGameIds) > 0 {
		hashWhere := wheresql

		// 根据哈希游戏类型构建正确的GameId过滤条件
		var gameIdFilter string
		for i, gameId := range hashGameIds {
			if i > 0 {
				gameIdFilter += " or "
			}

			if gameId == -1 { // 转账游戏
				gameIdFilter += "GameId in (1, 2, 3, 4, 5, 6, 7, 11, 12, 13, 301, 302, 303, 313, 323, 331, 332, 333)"
			} else if gameId == -2 { // 余额游戏
				gameIdFilter += "GameId in (101, 102, 103, 104, 105, 106, 116, 126, 136, 131, 132, 133, 134, 135, 201, 202, 203, 204, 205, 206)"
			}
		}

		if hashWhere != "" {
			hashWhere = hashWhere + fmt.Sprintf(" and (%s) and BetAmount > 0", gameIdFilter)
		} else {
			hashWhere = fmt.Sprintf("(%s) and BetAmount > 0", gameIdFilter)
		}

		hashSql := fmt.Sprintf("SELECT UserId as user_id, BetAmount as bet_amount FROM x_custom_dailly WHERE %s", hashWhere)
		hashResult, err := server.DbReport().Query(hashSql, wheredata)
		if err == nil && hashResult != nil {
			for _, record := range *hashResult {
				allBetRecords = append(allBetRecords, BetRecord{
					UserID:    int32(abugo.GetInt64FromInterface(record["user_id"])),
					BetAmount: abugo.GetFloat64FromInterface(record["bet_amount"]),
				})
			}
		}
	}

	// 查询x_custom_third表（第三方游戏）
	if len(thirdTypeIds) > 0 {
		thirdWhere := wheresql

		// 添加ThirdType过滤条件（使用ThirdType字段而不是GameId）
		thirdTypeFilter := ""
		for i, thirdTypeId := range thirdTypeIds {
			if i > 0 {
				thirdTypeFilter += ","
			}
			thirdTypeFilter += fmt.Sprintf("%d", thirdTypeId)
		}

		if thirdWhere != "" {
			thirdWhere = thirdWhere + fmt.Sprintf(" and ThirdType in (%s) and BetAmount > 0", thirdTypeFilter)
		} else {
			thirdWhere = fmt.Sprintf("ThirdType in (%s) and BetAmount > 0", thirdTypeFilter)
		}

		thirdSql := fmt.Sprintf("SELECT UserId as user_id, BetAmount as bet_amount FROM x_custom_third WHERE %s", thirdWhere)
		thirdResult, err := server.DbReport().Query(thirdSql, wheredata)
		if err == nil && thirdResult != nil {
			for _, record := range *thirdResult {
				allBetRecords = append(allBetRecords, BetRecord{
					UserID:    int32(abugo.GetInt64FromInterface(record["user_id"])),
					BetAmount: abugo.GetFloat64FromInterface(record["bet_amount"]),
				})
			}
		}
	}

	// 按用户统计投注数据
	type UserBetData struct {
		UserID         int32
		MaxBetAmount   float64 // 最大单笔投注金额
		TotalBetAmount float64 // 用户总投注金额
	}

	userBetMap := make(map[int32]*UserBetData)
	totalBetAmount := float64(0)

	for _, record := range allBetRecords {
		if userBet, exists := userBetMap[record.UserID]; exists {
			// 用户已存在，更新最大投注金额和总投注金额
			if record.BetAmount > userBet.MaxBetAmount {
				userBet.MaxBetAmount = record.BetAmount
			}
			userBet.TotalBetAmount += record.BetAmount
		} else {
			// 新用户，创建记录
			userBetMap[record.UserID] = &UserBetData{
				UserID:         record.UserID,
				MaxBetAmount:   record.BetAmount,
				TotalBetAmount: record.BetAmount,
			}
		}
		totalBetAmount += record.BetAmount
	}

	totalUsers := float64(len(userBetMap))

	// 按用户最大单笔投注金额分配到对应区间
	var resultData []map[string]interface{}

	for _, rangeItem := range ranges {
		users := 0
		amount := float64(0)

		// 遍历所有用户，按最大单笔投注金额分配到区间
		for _, userBet := range userBetMap {
			if userBet.MaxBetAmount >= rangeItem.MinValue {
				if rangeItem.MaxValue == 0 || userBet.MaxBetAmount < rangeItem.MaxValue {
					users++
					amount += userBet.TotalBetAmount
				}
			}
		}

		// 计算占比
		userPercent := float64(0)
		amountPercent := float64(0)

		if totalUsers > 0 {
			userPercent = (float64(users) / totalUsers) * 100
		}
		if totalBetAmount > 0 {
			amountPercent = (amount / totalBetAmount) * 100
		}

		// 格式化数据，添加千分位和保留小数
		formattedAmount := decimal.NewFromFloat(amount).Round(2)

		resultData = append(resultData, map[string]interface{}{
			"BetRange":       rangeItem.Name,
			"BetUsers":       users,
			"UserPercentage": fmt.Sprintf("%.2f%%", userPercent),
			"TotalBet":       formattedAmount.String(),
			"BetPercentage":  fmt.Sprintf("%.2f%%", amountPercent),
		})
	}

	// 添加总计行
	formattedTotalAmount := decimal.NewFromFloat(totalBetAmount).Round(2)
	resultData = append(resultData, map[string]interface{}{
		"BetRange":       "总计",
		"BetUsers":       int(totalUsers),
		"UserPercentage": "100.00%",
		"TotalBet":       formattedTotalAmount.String(),
		"BetPercentage":  "100.00%",
	})

	// 根据Export参数判断是返回数据还是导出Excel
	if reqdata.Export != 1 {
		// 正常返回JSON数据给前端展示
		ctx.Put("data", resultData)
		server.WriteAdminLog("查看游戏投注区间分布", ctx, reqdata)
		ctx.RespOK()
	} else {
		// 导出Excel文件
		excel := excelize.NewFile()

		// 设置Excel表头
		excel.SetSheetRow("Sheet1", "A1", &[]string{
			"投注区间",
			"投注人数",
			"人数占比",
			"总投注",
			"投注占比",
		})

		// 填充Excel数据行
		for i, data := range resultData {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				data["BetRange"],
				data["BetUsers"],
				data["UserPercentage"],
				data["TotalBet"],
				data["BetPercentage"],
			})
		}

		// 生成唯一的文件名
		filename := "export_bet_range_" + time.Now().Format("20060102150405") + ".xlsx"

		// 保存Excel文件
		err = excel.SaveAs(server.ExportDir() + "/" + filename)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出文件失败")
			return
		}

		// 返回文件下载链接
		ctx.Put("filename", "/exports/"+filename)
		server.WriteAdminLog("导出游戏投注区间分布", ctx, reqdata)
		ctx.RespOK()
	}
}

func (c *ReportController) report_profit_loss_range(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 请求参数结构体
	type RequestData struct {
		SellerId       int     // 运营商ID，单选，0表示全部
		ChannelIds     []int   // 渠道ID列表，支持多选
		StartTime      int64   // 开始时间戳（毫秒）
		EndTime        int64   // 结束时间戳（毫秒）
		TopAgentId     []int   // 顶级代理ID列表，支持多选
		GameTypes      []int32 // 游戏类型ID列表，支持多选：-1=哈希游戏, -2=余额哈希, 1=电子, 2=棋牌, 3=小游戏, 4=彩票, 5=真人, 6=体育, 7=德州扑克
		ProfitLossType int     // 盈亏类型：1=查询平台盈利，2=查询平台亏损，默认为1
		Export         int     // 是否导出Excel，1表示导出
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 设置默认值：如果未指定盈亏类型，默认查询平台盈利
	if reqdata.ProfitLossType == 0 {
		reqdata.ProfitLossType = 1
	}

	// 权限验证
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "区间分布", "查"), &errcode, "权限不足") {
		return
	}

	// 运营商权限验证
	if token.SellerId > 0 {
		if reqdata.SellerId != token.SellerId {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelIds = []int{token.ChannelId}
	}

	// 根据盈亏类型定义不同的区间名称
	var ranges []struct {
		Name     string  // 区间名称，用于显示
		MinValue float64 // 区间最小值（包含）
		MaxValue float64 // 区间最大值（不包含），0表示无上限
	}

	var reportTitle string
	if reqdata.ProfitLossType == 1 {
		// 平台盈利金额区间
		reportTitle = "平台盈利金额"
		ranges = []struct {
			Name     string
			MinValue float64
			MaxValue float64
		}{
			{"0-100", 0, 100},
			{"100-500", 100, 500},
			{"500-1,000", 500, 1000},
			{"1,000-5,000", 1000, 5000},
			{"5,000-10,000", 5000, 10000},
			{"10,000-50,000", 10000, 50000},
			{"50,000+", 50000, 0},
		}
	} else {
		// 平台亏损金额区间（使用绝对值）
		reportTitle = "平台亏损金额"
		ranges = []struct {
			Name     string
			MinValue float64
			MaxValue float64
		}{
			{"0-100", 0, 100},
			{"100-500", 100, 500},
			{"500-1,000", 500, 1000},
			{"1,000-5,000", 1000, 5000},
			{"5,000-10,000", 5000, 10000},
			{"10,000-50,000", 10000, 50000},
			{"50,000+", 50000, 0},
		}
	}

	// 数据库实际值映射表
	// x_custom_dailly表GameId: -1=哈希游戏, -2=余额哈希
	// x_custom_third表ThirdType: 1=彩票, 2=电子, 3=棋牌, 4=小游戏, 5=真人, 6=体育, 7=德州扑克(假设)
	standardToDbThirdType := map[int32]int32{
		1: 2, // 标准"电子"(1) -> 数据库ThirdType(2)
		2: 3, // 标准"棋牌"(2) -> 数据库ThirdType(3)
		3: 4, // 标准"小游戏"(3) -> 数据库ThirdType(4)
		4: 1, // 标准"彩票"(4) -> 数据库ThirdType(1)
		5: 5, // 标准"真人"(5) -> 数据库ThirdType(5)
		6: 6, // 标准"体育"(6) -> 数据库ThirdType(6)
		7: 7, // 标准"德州扑克"(7) -> 数据库ThirdType(7)
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}

	// 运营商条件
	if reqdata.SellerId > 0 {
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	}

	// 时间条件
	if reqdata.StartTime > 0 {
		where.Add("and", "RecordDate", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	}
	if reqdata.EndTime > 0 {
		where.Add("and", "RecordDate", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	}

	// 渠道条件
	if len(reqdata.ChannelIds) > 0 {
		channelIdStr := ""
		for i, id := range reqdata.ChannelIds {
			if i > 0 {
				channelIdStr += ","
			}
			channelIdStr += fmt.Sprintf("%d", id)
		}
		where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelIdStr), nil)
	}

	// 顶级代理条件
	if len(reqdata.TopAgentId) > 0 {
		topAgentIdStr := ""
		for i, id := range reqdata.TopAgentId {
			if i > 0 {
				topAgentIdStr += ","
			}
			topAgentIdStr += fmt.Sprintf("%d", id)
		}
		where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topAgentIdStr), nil)
	}

	wheresql, wheredata := where.Sql()

	// 存储所有投注记录
	type ProfitLossRecord struct {
		UserID       int32   `json:"user_id"`
		BetAmount    float64 `json:"bet_amount"`
		RewardAmount float64 `json:"reward_amount"`
		ProfitLoss   float64 `json:"profit_loss"` // 盈亏金额：BetAmount - RewardAmount，正数表示亏损，负数表示盈利
	}

	var allProfitLossRecords []ProfitLossRecord

	// 根据游戏类型构建查询条件
	var hashGameIds []int32  // x_custom_dailly表的GameId
	var thirdTypeIds []int32 // x_custom_third表的ThirdType（数据库实际值）

	if len(reqdata.GameTypes) == 0 {
		// 如果没有指定游戏类型，查询所有
		hashGameIds = []int32{-1, -2}               // 哈希游戏、余额哈希
		thirdTypeIds = []int32{1, 2, 3, 4, 5, 6, 7} // 数据库中的ThirdType值：1=彩票,2=电子,3=棋牌,4=小游戏,5=真人,6=体育,7=德州扑克
	} else {
		// 根据选择的游戏类型构建ID列表
		for _, standardGameTypeId := range reqdata.GameTypes {
			if standardGameTypeId == -1 || standardGameTypeId == -2 {
				// 哈希游戏类型，直接使用标准ID查询x_custom_dailly表
				hashGameIds = append(hashGameIds, standardGameTypeId)
			} else {
				// 其他游戏类型，转换为数据库ThirdType值查询x_custom_third表
				if dbThirdType, exists := standardToDbThirdType[standardGameTypeId]; exists {
					thirdTypeIds = append(thirdTypeIds, dbThirdType)
				}
			}
		}
	}

	// 查询x_custom_dailly表（哈希游戏相关）
	if len(hashGameIds) > 0 {
		hashWhere := wheresql

		// 根据哈希游戏类型构建正确的GameId过滤条件
		var gameIdFilter string
		for i, gameId := range hashGameIds {
			if i > 0 {
				gameIdFilter += " or "
			}

			if gameId == -1 { // 转账游戏
				gameIdFilter += "GameId in (1, 2, 3, 4, 5, 6, 7, 11, 12, 13, 301, 302, 303, 313, 323, 331, 332, 333)"
			} else if gameId == -2 { // 余额游戏
				gameIdFilter += "GameId in (101, 102, 103, 104, 105, 106, 116, 126, 136, 131, 132, 133, 134, 135, 201, 202, 203, 204, 205, 206)"
			}
		}

		if hashWhere != "" {
			hashWhere = hashWhere + fmt.Sprintf(" and (%s)", gameIdFilter)
		} else {
			hashWhere = fmt.Sprintf("(%s)", gameIdFilter)
		}

		hashSql := fmt.Sprintf("SELECT UserId as user_id, BetAmount as bet_amount, RewardAmount as reward_amount FROM x_custom_dailly WHERE %s", hashWhere)
		hashResult, err := server.DbReport().Query(hashSql, wheredata)
		if err == nil && hashResult != nil {
			for _, record := range *hashResult {
				betAmount := abugo.GetFloat64FromInterface(record["bet_amount"])
				rewardAmount := abugo.GetFloat64FromInterface(record["reward_amount"])
				profitLoss := betAmount - rewardAmount // 正数表示亏损， 负数表示盈利

				allProfitLossRecords = append(allProfitLossRecords, ProfitLossRecord{
					UserID:       int32(abugo.GetInt64FromInterface(record["user_id"])),
					BetAmount:    betAmount,
					RewardAmount: rewardAmount,
					ProfitLoss:   profitLoss,
				})
			}
		}
	}

	// 查询x_custom_third表（第三方游戏）
	if len(thirdTypeIds) > 0 {
		thirdWhere := wheresql

		// 添加ThirdType过滤条件（使用ThirdType字段而不是GameId）
		thirdTypeFilter := ""
		for i, thirdTypeId := range thirdTypeIds {
			if i > 0 {
				thirdTypeFilter += ","
			}
			thirdTypeFilter += fmt.Sprintf("%d", thirdTypeId)
		}

		if thirdWhere != "" {
			thirdWhere = thirdWhere + fmt.Sprintf(" and ThirdType in (%s)", thirdTypeFilter)
		} else {
			thirdWhere = fmt.Sprintf("ThirdType in (%s)", thirdTypeFilter)
		}

		thirdSql := fmt.Sprintf("SELECT UserId as user_id, BetAmount as bet_amount, WinAmount as reward_amount FROM x_custom_third WHERE %s", thirdWhere)
		thirdResult, err := server.DbReport().Query(thirdSql, wheredata)
		if err == nil && thirdResult != nil {
			for _, record := range *thirdResult {
				betAmount := abugo.GetFloat64FromInterface(record["bet_amount"])
				rewardAmount := abugo.GetFloat64FromInterface(record["reward_amount"])
				profitLoss := betAmount - rewardAmount // 正数表示亏损，负数表示盈利

				allProfitLossRecords = append(allProfitLossRecords, ProfitLossRecord{
					UserID:       int32(abugo.GetInt64FromInterface(record["user_id"])),
					BetAmount:    betAmount,
					RewardAmount: rewardAmount,
					ProfitLoss:   profitLoss,
				})
			}
		}
	}

	// 按用户统计投注数据
	type UserProfitLossData struct {
		UserID          int32
		TotalProfitLoss float64 // 用户总盈亏金额（正数表示亏损，负数表示盈利）
		TotalBetAmount  float64 // 用户总投注金额
	}

	userProfitLossMap := make(map[int32]*UserProfitLossData)
	totalBetAmount := float64(0)
	totalProfitLoss := float64(0)

	for _, record := range allProfitLossRecords {
		if userProfitLoss, exists := userProfitLossMap[record.UserID]; exists {
			// 用户已存在，累加盈亏金额和投注金额
			userProfitLoss.TotalProfitLoss += record.ProfitLoss
			userProfitLoss.TotalBetAmount += record.BetAmount
		} else {
			// 新用户，创建记录
			userProfitLossMap[record.UserID] = &UserProfitLossData{
				UserID:          record.UserID,
				TotalProfitLoss: record.ProfitLoss,
				TotalBetAmount:  record.BetAmount,
			}
		}
		totalBetAmount += record.BetAmount
		totalProfitLoss += record.ProfitLoss
	}

	// 计算符合条件的用户总数和总投注金额
	var filteredUsers int32 = 0
	var filteredBetAmount float64 = 0

	for _, userProfitLoss := range userProfitLossMap {
		platformProfit := userProfitLoss.TotalProfitLoss

		var shouldInclude bool
		if reqdata.ProfitLossType == 1 {
			// 查询平台盈利：只统计平台盈利的用户（平台盈利金额大于0）
			shouldInclude = platformProfit > 0
		} else {
			// 查询平台亏损：只统计平台亏损的用户（平台盈利金额小于0）
			shouldInclude = platformProfit < 0
		}

		if shouldInclude {
			filteredUsers++
			filteredBetAmount += userProfitLoss.TotalBetAmount
		}
	}

	// 按用户总盈亏金额分配到对应区间
	var resultData []map[string]interface{}
	totalBetUsers := 0
	for _, rangeItem := range ranges {
		users := 0
		amount := float64(0)

		// 遍历所有用户，按平台盈亏金额分配到区间
		for _, userProfitLoss := range userProfitLossMap {
			// 平台盈利金额（正数表示平台盈利，负数表示平台亏损）
			platformProfit := userProfitLoss.TotalProfitLoss

			// 根据盈亏类型进行不同的筛选
			var shouldInclude bool
			var compareValue float64

			if reqdata.ProfitLossType == 1 {
				// 查询平台盈利：只统计平台盈利的用户（平台盈利金额大于0）
				shouldInclude = platformProfit > 0
				compareValue = platformProfit
			} else {
				// 查询平台亏损：只统计平台亏损的用户（平台盈利金额小于0），使用绝对值进行区间比较
				shouldInclude = platformProfit < 0
				compareValue = -platformProfit // 使用绝对值
			}

			if shouldInclude {
				if rangeItem.MaxValue == 0 {
					// 无上限区间（如50,000+）
					if compareValue >= rangeItem.MinValue {
						users++
						amount += userProfitLoss.TotalBetAmount
					}
				} else {
					// 标准区间
					if compareValue >= rangeItem.MinValue && compareValue < rangeItem.MaxValue {
						users++
						amount += userProfitLoss.TotalBetAmount
					}
				}
			}
		}

		// 计算占比 - 使用筛选后的用户数和投注金额作为分母
		userPercent := float64(0)
		amountPercent := float64(0)

		if filteredUsers > 0 {
			userPercent = (float64(users) / float64(filteredUsers)) * 100
		}
		if filteredBetAmount > 0 {
			amountPercent = (amount / filteredBetAmount) * 100
		}

		// 格式化数据，添加千分位和保留小数
		formattedAmount := decimal.NewFromFloat(amount).Round(2)

		resultData = append(resultData, map[string]interface{}{
			"BetRange":       rangeItem.Name,
			"BetUsers":       users,
			"UserPercentage": fmt.Sprintf("%.2f%%", userPercent),
			"TotalBet":       formattedAmount.String(),
			"BetPercentage":  fmt.Sprintf("%.2f%%", amountPercent),
		})
		totalBetUsers = totalBetUsers + users
	}

	// 添加总计行 - 使用实际的筛选后数据
	formattedFilteredAmount := decimal.NewFromFloat(filteredBetAmount).Round(2)
	resultData = append(resultData, map[string]interface{}{
		"BetRange":       "总计",
		"BetUsers":       totalBetUsers,                    // 应该等于filteredUsers
		"UserPercentage": "100.00%",                        // 所有区间的百分比之和
		"TotalBet":       formattedFilteredAmount.String(), // 使用筛选后的投注金额
		"BetPercentage":  "100.00%",                        // 所有区间的投注占比之和
	})

	// 根据Export参数判断是返回数据还是导出Excel
	if reqdata.Export != 1 {
		// 正常返回JSON数据给前端展示
		ctx.Put("data", resultData)
		if reqdata.ProfitLossType == 1 {
			server.WriteAdminLog("查看平台盈利金额区间分布", ctx, reqdata)
		} else {
			server.WriteAdminLog("查看平台亏损金额区间分布", ctx, reqdata)
		}
		ctx.RespOK()
	} else {
		// 导出Excel文件
		excel := excelize.NewFile()

		// 设置Excel表头
		excel.SetSheetRow("Sheet1", "A1", &[]string{
			reportTitle,
			"投注人数",
			"人数占比",
			"总投注",
			"投注占比",
		})

		// 填充Excel数据行
		for i, data := range resultData {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				data["BetRange"],
				data["BetUsers"],
				data["UserPercentage"],
				data["TotalBet"],
				data["BetPercentage"],
			})
		}

		// 生成唯一的文件名
		var filename string
		if reqdata.ProfitLossType == 1 {
			filename = "export_profit_range_" + time.Now().Format("20060102150405") + ".xlsx"
		} else {
			filename = "export_loss_range_" + time.Now().Format("20060102150405") + ".xlsx"
		}

		// 保存Excel文件
		err = excel.SaveAs(server.ExportDir() + "/" + filename)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出文件失败")
			return
		}

		// 返回文件下载链接
		ctx.Put("filename", "/exports/"+filename)
		if reqdata.ProfitLossType == 1 {
			server.WriteAdminLog("导出平台盈利金额区间分布", ctx, reqdata)
		} else {
			server.WriteAdminLog("导出平台亏损金额区间分布", ctx, reqdata)
		}
		ctx.RespOK()
	}
}

func (c *ReportController) report_lang_game_type(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 请求参数结构体
	type RequestData struct {
		Page       int     // 页码，从1开始
		PageSize   int     // 每页条数
		SellerId   int     // 运营商ID，单选，0表示全部
		ChannelIds []int   // 渠道ID列表，支持多选
		StartTime  int64   // 开始时间戳（毫秒）
		EndTime    int64   // 结束时间戳（毫秒）
		TopAgentId []int   // 顶级代理ID列表，支持多选
		GameTypes  []int32 // 游戏类型ID列表，支持多选：-1=哈希游戏, -2=余额哈希, 1=电子, 2=棋牌, 3=小游戏, 4=彩票, 5=真人, 6=体育, 7=德州扑克
		Lang       int     // 投注语言，单选，0表示全部
		Export     int     // 是否导出Excel，1表示导出
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 权限验证
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "区间分布", "查"), &errcode, "权限不足") {
		return
	}

	// 运营商权限验证
	if token.SellerId > 0 {
		if reqdata.SellerId != token.SellerId {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelIds = []int{token.ChannelId}
	}

	// 设置默认分页参数
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 20
	}
	if reqdata.PageSize > 1000 {
		reqdata.PageSize = 1000
	}

	// 语言ID映射
	langNames := map[int]string{
		1:  "中文",
		2:  "英语",
		3:  "泰语",
		4:  "韩语",
		5:  "土耳其语",
		6:  "越南语",
		7:  "葡萄牙语",
		8:  "印地语",
		9:  "日语",
		10: "俄语",
		11: "柬埔寨语",
		12: "乌克兰语",
		13: "中文繁体",
		14: "西班牙语",
	}

	// 游戏类型表映射
	gameTableTypes := map[int32]string{
		-1: "x_order",         // 哈希游戏
		-2: "x_order",         // 余额哈希
		1:  "x_third_dianzhi", // 电子
		2:  "x_third_qipai",   // 棋牌
		3:  "x_third_quwei",   // 趣味游戏
		4:  "x_third_lottery", // 彩票
		5:  "x_third_live",    // 真人
		6:  "x_third_sport",   // 体育
		7:  "x_third_texas",   // 德州扑克
	}

	// 游戏类型名称映射
	gameTypeNames := map[int32]string{
		-1: "哈希游戏",
		-2: "余额哈希",
		1:  "电子",
		2:  "棋牌",
		3:  "趣味游戏",
		4:  "彩票",
		5:  "真人",
		6:  "体育",
		7:  "德州扑克",
	}

	// GameId名称映射（用于x_order表）
	gameIdNames := map[int]string{
		1:   "哈希大小",
		2:   "哈希单双",
		3:   "幸运哈希",
		4:   "幸运庄闲",
		5:   "哈希牛牛",
		6:   "哈希快3",
		7:   "哈希pk10",
		11:  "和值大小",
		12:  "和值单双",
		13:  "转账轮盘",
		101: "一分大小",
		102: "一分单双",
		103: "一分幸运",
		104: "一分庄闲",
		105: "一分牛牛",
		106: "一分余额轮盘",
		116: "一分半余额轮盘",
		126: "二分余额轮盘",
		131: "三分大小",
		132: "三分单双",
		133: "三分幸运",
		134: "三分庄闲",
		135: "三分牛牛",
		136: "三分余额轮盘",
		201: "余额大小",
		202: "余额单双",
		203: "幸运余额",
		204: "余额庄闲",
		205: "余额牛牛",
		206: "余额轮盘",
		301: "一分哈希大小",
		302: "一分哈希单双",
		303: "一分转账轮盘",
		313: "一分半转账轮盘",
		323: "二分转账轮盘",
		331: "三分哈希大小",
		332: "三分哈希单双",
		333: "三分转账轮盘",
	}

	// RoomLevel名称映射（用于x_order表）
	roomLevelNames := map[int]string{
		1: "初级场",
		2: "中级场",
		3: "高级场",
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}

	// 运营商条件
	if reqdata.SellerId > 0 {
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	}

	// 时间条件
	if reqdata.StartTime > 0 {
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	}
	if reqdata.EndTime > 0 {
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	}

	// 渠道条件
	if len(reqdata.ChannelIds) > 0 {
		channelIdStr := ""
		for i, id := range reqdata.ChannelIds {
			if i > 0 {
				channelIdStr += ","
			}
			channelIdStr += fmt.Sprintf("%d", id)
		}
		where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelIdStr), nil)
	}

	// 顶级代理条件
	if len(reqdata.TopAgentId) > 0 {
		topAgentIdStr := ""
		for i, id := range reqdata.TopAgentId {
			if i > 0 {
				topAgentIdStr += ","
			}
			topAgentIdStr += fmt.Sprintf("%d", id)
		}
		where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topAgentIdStr), nil)
	}

	// 语言条件
	if reqdata.Lang > 0 {
		where.Add("and", "Lang", "LIKE", langNames[reqdata.Lang]+"%", "")
	}

	wheresql, wheredata := where.Sql()

	// 游戏类型范围
	var queryGameTypes []int32
	if len(reqdata.GameTypes) == 0 {
		// 如果没有指定游戏类型，查询所有
		queryGameTypes = []int32{-1, -2, 1, 2, 3, 4, 5, 6, 7}
	} else {
		queryGameTypes = reqdata.GameTypes
	}

	// 存储统计结果
	type GameStat struct {
		SellerId   int32   // 运营商ID
		SellerName string  // 运营商名称
		GameType   int32   // 游戏类型
		GameName   string  // 游戏名称
		Lang       string  // 投注语言
		BetUsers   int32   // 投注人数
		BetCount   int32   // 投注笔数
		WinCount   int32   // 中奖笔数
		BetAmount  float64 // 投注金额
		ValidBet   float64 // 有效投注
		WinAmount  float64 // 派奖金额
		ProfitLoss float64 // 盈亏
	}

	var allStats []GameStat
	var totalCount int32 = 0

	// 查询运营商信息映射
	sellerMap := make(map[int32]string)
	sellerSql := "SELECT SellerId, SellerName FROM x_seller"
	sellerResult, err := server.DbReport().Query(sellerSql, nil)
	if err == nil && sellerResult != nil {
		for _, record := range *sellerResult {
			sellerId := int32(abugo.GetInt64FromInterface(record["SellerId"]))
			sellerName := abugo.GetStringFromInterface(record["SellerName"])
			sellerMap[sellerId] = sellerName
		}
	}

	// 构建统一的子查询SQL来获取所有数据
	var unionSQLs []string
	var allWhereData []interface{}

	// 遍历每个游戏类型构建UNION查询
	for _, gameType := range queryGameTypes {
		tableName, exists := gameTableTypes[gameType]
		if !exists {
			continue
		}

		var selectFields string
		var groupFields string
		var finalWhere string

		// 根据表类型确定字段映射
		if tableName == "x_order" {
			// 哈希游戏表
			selectFields = "SellerId, %d as game_type, GameId, RoomLevel, Lang, COUNT(DISTINCT UserId) as bet_users, COUNT(*) as bet_count, SUM(CASE WHEN RewardAmount > Amount THEN 1 ELSE 0 END) as win_count, SUM(Amount) as bet_amount, SUM(ValidBetAmount) as valid_bet, SUM(RewardAmount) as win_amount, '' as GameName"
			selectFields = fmt.Sprintf(selectFields, gameType)
			groupFields = "SellerId, GameId, RoomLevel, Lang"

			// 添加GameId过滤条件
			var gameIdList string
			if gameType == -1 { // 转账游戏
				gameIdList = "(1, 2, 3, 4, 5, 6, 7, 11, 12, 13, 301, 302, 303, 313, 323, 331, 332, 333)"
			} else if gameType == -2 { // 余额游戏
				gameIdList = "(101, 102, 103, 104, 105, 106, 116, 126, 136, 131, 132, 133, 134, 135, 201, 202, 203, 204, 205, 206)"
			}

			finalWhere = wheresql
			if finalWhere != "" {
				finalWhere = finalWhere + fmt.Sprintf(" and GameId in %s", gameIdList)
			} else {
				finalWhere = fmt.Sprintf("GameId in %s", gameIdList)
			}
		} else {
			// 第三方游戏表
			selectFields = "SellerId, %d as game_type, 0 as GameId, 0 as RoomLevel, Lang, COUNT(DISTINCT UserId) as bet_users, COUNT(*) as bet_count, SUM(CASE WHEN WinAmount > BetAmount THEN 1 ELSE 0 END) as win_count, SUM(BetAmount) as bet_amount, SUM(ValidBetAmount) as valid_bet, SUM(WinAmount) as win_amount, GameName"
			selectFields = fmt.Sprintf(selectFields, gameType)
			groupFields = "SellerId, GameName, Lang"

			finalWhere = wheresql
			if finalWhere == "" {
				finalWhere = "1=1"
			}
		}

		subSQL := fmt.Sprintf("SELECT %s FROM %s WHERE %s GROUP BY %s", selectFields, tableName, finalWhere, groupFields)
		unionSQLs = append(unionSQLs, subSQL)
		allWhereData = append(allWhereData, wheredata...)
	}

	// 构建最终的UNION查询
	if len(unionSQLs) == 0 {
		ctx.Put("data", []map[string]interface{}{})
		ctx.Put("total", 0)
		ctx.Put("page", reqdata.Page)
		ctx.Put("page_size", reqdata.PageSize)
		ctx.RespOK()
		return
	}

	unionSQL := strings.Join(unionSQLs, " UNION ALL ")

	// 查询总记录数
	countSQL := fmt.Sprintf("SELECT COUNT(*) as total FROM (%s) as temp", unionSQL)
	countResult, err := server.DbReport().Query(countSQL, allWhereData)
	if err != nil || countResult == nil || len(*countResult) == 0 {
		totalCount = 0
	} else {
		totalCount = int32(abugo.GetInt64FromInterface((*countResult)[0]["total"]))
	}

	// 添加分页到UNION查询
	offset := (reqdata.Page - 1) * reqdata.PageSize
	finalSQL := fmt.Sprintf("SELECT * FROM (%s) as temp ORDER BY SellerId, game_type, Lang LIMIT %d OFFSET %d", unionSQL, reqdata.PageSize, offset)

	// 执行分页查询
	result, err := server.DbReport().Query(finalSQL, allWhereData)
	if err == nil && result != nil {
		for _, record := range *result {
			sellerId := int32(abugo.GetInt64FromInterface(record["SellerId"]))
			gameTypeValue := int32(abugo.GetInt64FromInterface(record["game_type"]))
			lang := abugo.GetStringFromInterface(record["Lang"])
			betUsers := int32(abugo.GetInt64FromInterface(record["bet_users"]))
			betCount := int32(abugo.GetInt64FromInterface(record["bet_count"]))
			winCount := int32(abugo.GetInt64FromInterface(record["win_count"]))
			betAmount := abugo.GetFloat64FromInterface(record["bet_amount"])
			validBet := abugo.GetFloat64FromInterface(record["valid_bet"])
			winAmount := abugo.GetFloat64FromInterface(record["win_amount"])

			// 构造游戏名称
			var gameName string
			if gameTypeValue == -1 || gameTypeValue == -2 {
				// x_order表：根据GameId和RoomLevel构造游戏名称
				gameId := int(abugo.GetInt64FromInterface(record["GameId"]))
				roomLevel := int(abugo.GetInt64FromInterface(record["RoomLevel"]))

				gameIdName := gameIdNames[gameId]
				if gameIdName == "" {
					gameIdName = fmt.Sprintf("游戏%d", gameId)
				}

				roomLevelName := roomLevelNames[roomLevel]
				if roomLevelName == "" {
					roomLevelName = fmt.Sprintf("房间%d", roomLevel)
				}

				gameName = fmt.Sprintf("%s(%s)", gameIdName, roomLevelName)
			} else {
				// 第三方表：直接使用GameName字段
				gameName = abugo.GetStringFromInterface(record["GameName"])
			}

			// 计算盈亏：投注金额 - 派奖金额
			profitLoss := betAmount - winAmount

			sellerName := sellerMap[sellerId]
			if sellerName == "" {
				sellerName = fmt.Sprintf("运营商%d", sellerId)
			}

			allStats = append(allStats, GameStat{
				SellerId:   sellerId,
				SellerName: sellerName,
				GameType:   gameTypeValue,
				GameName:   gameName,
				Lang:       lang,
				BetUsers:   betUsers,
				BetCount:   betCount,
				WinCount:   winCount,
				BetAmount:  betAmount,
				ValidBet:   validBet,
				WinAmount:  winAmount,
				ProfitLoss: profitLoss,
			})
		}
	}

	// 计算去重的总用户数
	var uniqueUserSQL string
	var uniqueUserSQLs []string

	// 为每个游戏类型构建用户去重查询
	for _, gameType := range queryGameTypes {
		tableName, exists := gameTableTypes[gameType]
		if !exists {
			continue
		}

		var finalWhere string

		// 根据表类型确定字段映射
		if tableName == "x_order" {
			// 添加GameId过滤条件
			var gameIdList string
			if gameType == -1 { // 转账游戏
				gameIdList = "(1, 2, 3, 4, 5, 6, 7, 11, 12, 13, 301, 302, 303, 313, 323, 331, 332, 333)"
			} else if gameType == -2 { // 余额游戏
				gameIdList = "(101, 102, 103, 104, 105, 106, 116, 126, 136, 131, 132, 133, 134, 135, 201, 202, 203, 204, 205, 206)"
			}

			finalWhere = wheresql
			if finalWhere != "" {
				finalWhere = finalWhere + fmt.Sprintf(" and GameId in %s", gameIdList)
			} else {
				finalWhere = fmt.Sprintf("GameId in %s", gameIdList)
			}
		} else {
			finalWhere = wheresql
			if finalWhere == "" {
				finalWhere = "1=1"
			}
		}

		userSQL := fmt.Sprintf("SELECT DISTINCT UserId FROM %s WHERE %s", tableName, finalWhere)
		uniqueUserSQLs = append(uniqueUserSQLs, userSQL)
	}

	// 获取去重后的总用户数
	totalBetUsers := int32(0)
	if len(uniqueUserSQLs) > 0 {
		uniqueUserSQL = fmt.Sprintf("SELECT COUNT(DISTINCT UserId) as unique_users FROM (%s) as temp", strings.Join(uniqueUserSQLs, " UNION "))
		uniqueUserResult, err := server.DbReport().Query(uniqueUserSQL, allWhereData)
		if err == nil && uniqueUserResult != nil && len(*uniqueUserResult) > 0 {
			totalBetUsers = int32(abugo.GetInt64FromInterface((*uniqueUserResult)[0]["unique_users"]))
		}
	}

	// 构建结果数据
	var resultData []map[string]interface{}
	totalBetCount := int32(0)
	totalWinCount := int32(0)
	totalBetAmount := float64(0)
	totalValidBet := float64(0)
	totalWinAmount := float64(0)
	totalProfitLoss := float64(0)

	for _, stat := range allStats {
		gameTypeName := gameTypeNames[stat.GameType]
		if gameTypeName == "" {
			gameTypeName = fmt.Sprintf("游戏类型%d", stat.GameType)
		}

		// 格式化数字
		formattedBetAmount := decimal.NewFromFloat(stat.BetAmount).Round(2)
		formattedValidBet := decimal.NewFromFloat(stat.ValidBet).Round(2)
		formattedWinAmount := decimal.NewFromFloat(stat.WinAmount).Round(2)
		formattedProfitLoss := decimal.NewFromFloat(stat.ProfitLoss).Round(2)

		resultData = append(resultData, map[string]interface{}{
			"SellerName": stat.SellerName,
			"GameType":   gameTypeName,
			"GameName":   stat.GameName,
			"Lang":       stat.Lang,
			"BetUsers":   stat.BetUsers,
			"BetCount":   stat.BetCount,
			"WinCount":   stat.WinCount,
			"BetAmount":  formattedBetAmount.String(),
			"ValidBet":   formattedValidBet.String(),
			"WinAmount":  formattedWinAmount.String(),
			"ProfitLoss": formattedProfitLoss.String(),
		})

		// 累加总计（除了用户数，其他可以直接累加）
		totalBetCount += stat.BetCount
		totalWinCount += stat.WinCount
		totalBetAmount += stat.BetAmount
		totalValidBet += stat.ValidBet
		totalWinAmount += stat.WinAmount
		totalProfitLoss += stat.ProfitLoss
	}

	// 添加总计行
	formattedTotalBetAmount := decimal.NewFromFloat(totalBetAmount).Round(2)
	formattedTotalValidBet := decimal.NewFromFloat(totalValidBet).Round(2)
	formattedTotalWinAmount := decimal.NewFromFloat(totalWinAmount).Round(2)
	formattedTotalProfitLoss := decimal.NewFromFloat(totalProfitLoss).Round(2)

	resultData = append(resultData, map[string]interface{}{
		"SellerName": "总计",
		"GameType":   "-",
		"GameName":   "-",
		"Lang":       "-",
		"BetUsers":   totalBetUsers,
		"BetCount":   totalBetCount,
		"WinCount":   totalWinCount,
		"BetAmount":  formattedTotalBetAmount.String(),
		"ValidBet":   formattedTotalValidBet.String(),
		"WinAmount":  formattedTotalWinAmount.String(),
		"ProfitLoss": formattedTotalProfitLoss.String(),
	})

	// 根据Export参数判断是返回数据还是导出Excel
	if reqdata.Export != 1 {
		// 正常返回JSON数据给前端展示，包含分页信息
		response := map[string]interface{}{
			"data":      resultData,
			"total":     totalCount,
			"page":      reqdata.Page,
			"page_size": reqdata.PageSize,
		}
		ctx.Put("result", response)
		server.WriteAdminLog("查看语言投注游戏类型报表", ctx, reqdata)
		ctx.RespOK()
	} else {
		// 导出Excel文件
		excel := excelize.NewFile()

		// 设置Excel表头
		excel.SetSheetRow("Sheet1", "A1", &[]string{
			"运营商",
			"游戏类型",
			"游戏名称",
			"投注语言",
			"投注人数",
			"投注笔数",
			"中奖笔数",
			"投注金额",
			"有效投注",
			"派奖",
			"盈亏",
		})

		// 填充Excel数据行
		for i, data := range resultData {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				data["SellerName"],
				data["GameType"],
				data["GameName"],
				data["Lang"],
				data["BetUsers"],
				data["BetCount"],
				data["WinCount"],
				data["BetAmount"],
				data["ValidBet"],
				data["WinAmount"],
				data["ProfitLoss"],
			})
		}

		// 生成唯一的文件名
		filename := "export_lang_game_type_" + time.Now().Format("20060102150405") + ".xlsx"

		// 保存Excel文件
		err = excel.SaveAs(server.ExportDir() + "/" + filename)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出文件失败")
			return
		}

		// 返回文件下载链接
		ctx.Put("filename", "/exports/"+filename)
		server.WriteAdminLog("导出语言投注游戏类型报表", ctx, reqdata)
		ctx.RespOK()
	}
}
