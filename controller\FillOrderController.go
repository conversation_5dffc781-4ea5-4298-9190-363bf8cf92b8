package controller

import (
	"github.com/spf13/viper"
	"time"
	"xserver/abugo"
	"xserver/server"
)

type FillOrderController struct{}

func (f *FillOrderController) Init() {
	server.Http().Post("/api/fillOrder", f.FillOrder)
}

func (f *FillOrderController) FillOrder(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		StartTime int64
		EndTime   int64
		Brand     string
	}
	errCode := 0
	reqData := RequestData{}
	err := ctx.RequestData(&reqData)
	if ctx.RespErr(err, &errCode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Brand", "=", reqData.Brand, 0)
	res, _ := server.Db().Table("x_third_config").Where(where).GetOne()
	if res == nil {
		ctx.RespErrString(true, &errCode, "品牌不存在")
		return
	}
	lck := server.Redis().SetNxString("FillOrderGapLock", "1", 60)
	if lck != nil {
		ctx.RespErrString(true, &errCode, "操作频繁,请一分钟后再试")
		return
	}
	beginTime := time.Unix(reqData.StartTime/1000, 0)
	endTime := time.Unix(reqData.EndTime/1000, 0)
	if beginTime.Unix() > endTime.Unix() {
		ctx.RespErrString(true, &errCode, "开始时间不能大于结束时间")
		return
	}
	if endTime.Unix()-beginTime.Unix() > 24*3600 {
		ctx.RespErrString(true, &errCode, "时间跨度不能超过一天")
		return
	}
	if reqData.Brand != "gfg" {
		// 按10分钟划分
		diff := (endTime.Unix() - beginTime.Unix()) / 600
		var i int64
		for i = 0; i <= diff; i++ {
			begin := beginTime.Unix() + i*600
			after := begin + 600
			err = server.XRedis().RPush("sync_order_"+reqData.Brand, time.Unix(begin, 0).Format("2006-01-02 15:04:05")+
				","+time.Unix(after, 0).Format("2006-01-02 15:04:05"), viper.GetString("server.project"))
		}
	} else {
		err = server.XRedis().RPush("sync_order_"+reqData.Brand, beginTime.Format("2006-01-02 15:04:05")+","+endTime.Format("2006-01-02 15:04:05"), viper.GetString("server.project"))
	}
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.RespOK()
}
