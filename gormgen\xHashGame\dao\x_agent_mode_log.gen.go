// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentModeLog(db *gorm.DB, opts ...gen.DOOption) xAgentModeLog {
	_xAgentModeLog := xAgentModeLog{}

	_xAgentModeLog.xAgentModeLogDo.UseDB(db, opts...)
	_xAgentModeLog.xAgentModeLogDo.UseModel(&model.XAgentModeLog{})

	tableName := _xAgentModeLog.xAgentModeLogDo.TableName()
	_xAgentModeLog.ALL = field.NewAsterisk(tableName)
	_xAgentModeLog.ID = field.NewInt32(tableName, "Id")
	_xAgentModeLog.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentModeLog.OriginAgentMode = field.NewInt32(tableName, "OriginAgentMode")
	_xAgentModeLog.NowAgentMode = field.NewInt32(tableName, "NowAgentMode")
	_xAgentModeLog.OrginState = field.NewInt32(tableName, "OrginState")
	_xAgentModeLog.NowState = field.NewInt32(tableName, "NowState")
	_xAgentModeLog.Remark = field.NewString(tableName, "Remark")
	_xAgentModeLog.OriginAgentCaseID = field.NewInt32(tableName, "OriginAgentCaseId")
	_xAgentModeLog.NowAgentCaseID = field.NewInt32(tableName, "NowAgentCaseId")
	_xAgentModeLog.Operator = field.NewString(tableName, "Operator")
	_xAgentModeLog.CreateTime = field.NewTime(tableName, "CreateTime")

	_xAgentModeLog.fillFieldMap()

	return _xAgentModeLog
}

type xAgentModeLog struct {
	xAgentModeLogDo xAgentModeLogDo

	ALL               field.Asterisk
	ID                field.Int32
	ChannelID         field.Int32  // 渠道ID
	OriginAgentMode   field.Int32  // 原代理模式
	NowAgentMode      field.Int32  // 现代理模式
	OrginState        field.Int32  // 原状态
	NowState          field.Int32  // 现状态
	Remark            field.String // 备注
	OriginAgentCaseID field.Int32  // 原代理方案
	NowAgentCaseID    field.Int32  // 现代理方案
	Operator          field.String // 操作人
	CreateTime        field.Time   // 创建时间

	fieldMap map[string]field.Expr
}

func (x xAgentModeLog) Table(newTableName string) *xAgentModeLog {
	x.xAgentModeLogDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentModeLog) As(alias string) *xAgentModeLog {
	x.xAgentModeLogDo.DO = *(x.xAgentModeLogDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentModeLog) updateTableName(table string) *xAgentModeLog {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.OriginAgentMode = field.NewInt32(table, "OriginAgentMode")
	x.NowAgentMode = field.NewInt32(table, "NowAgentMode")
	x.OrginState = field.NewInt32(table, "OrginState")
	x.NowState = field.NewInt32(table, "NowState")
	x.Remark = field.NewString(table, "Remark")
	x.OriginAgentCaseID = field.NewInt32(table, "OriginAgentCaseId")
	x.NowAgentCaseID = field.NewInt32(table, "NowAgentCaseId")
	x.Operator = field.NewString(table, "Operator")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentModeLog) WithContext(ctx context.Context) *xAgentModeLogDo {
	return x.xAgentModeLogDo.WithContext(ctx)
}

func (x xAgentModeLog) TableName() string { return x.xAgentModeLogDo.TableName() }

func (x xAgentModeLog) Alias() string { return x.xAgentModeLogDo.Alias() }

func (x xAgentModeLog) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentModeLogDo.Columns(cols...)
}

func (x *xAgentModeLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentModeLog) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["OriginAgentMode"] = x.OriginAgentMode
	x.fieldMap["NowAgentMode"] = x.NowAgentMode
	x.fieldMap["OrginState"] = x.OrginState
	x.fieldMap["NowState"] = x.NowState
	x.fieldMap["Remark"] = x.Remark
	x.fieldMap["OriginAgentCaseId"] = x.OriginAgentCaseID
	x.fieldMap["NowAgentCaseId"] = x.NowAgentCaseID
	x.fieldMap["Operator"] = x.Operator
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xAgentModeLog) clone(db *gorm.DB) xAgentModeLog {
	x.xAgentModeLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentModeLog) replaceDB(db *gorm.DB) xAgentModeLog {
	x.xAgentModeLogDo.ReplaceDB(db)
	return x
}

type xAgentModeLogDo struct{ gen.DO }

func (x xAgentModeLogDo) Debug() *xAgentModeLogDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentModeLogDo) WithContext(ctx context.Context) *xAgentModeLogDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentModeLogDo) ReadDB() *xAgentModeLogDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentModeLogDo) WriteDB() *xAgentModeLogDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentModeLogDo) Session(config *gorm.Session) *xAgentModeLogDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentModeLogDo) Clauses(conds ...clause.Expression) *xAgentModeLogDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentModeLogDo) Returning(value interface{}, columns ...string) *xAgentModeLogDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentModeLogDo) Not(conds ...gen.Condition) *xAgentModeLogDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentModeLogDo) Or(conds ...gen.Condition) *xAgentModeLogDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentModeLogDo) Select(conds ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentModeLogDo) Where(conds ...gen.Condition) *xAgentModeLogDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentModeLogDo) Order(conds ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentModeLogDo) Distinct(cols ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentModeLogDo) Omit(cols ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentModeLogDo) Join(table schema.Tabler, on ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentModeLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentModeLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentModeLogDo) Group(cols ...field.Expr) *xAgentModeLogDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentModeLogDo) Having(conds ...gen.Condition) *xAgentModeLogDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentModeLogDo) Limit(limit int) *xAgentModeLogDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentModeLogDo) Offset(offset int) *xAgentModeLogDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentModeLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentModeLogDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentModeLogDo) Unscoped() *xAgentModeLogDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentModeLogDo) Create(values ...*model.XAgentModeLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentModeLogDo) CreateInBatches(values []*model.XAgentModeLog, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentModeLogDo) Save(values ...*model.XAgentModeLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentModeLogDo) First() (*model.XAgentModeLog, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentModeLog), nil
	}
}

func (x xAgentModeLogDo) Take() (*model.XAgentModeLog, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentModeLog), nil
	}
}

func (x xAgentModeLogDo) Last() (*model.XAgentModeLog, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentModeLog), nil
	}
}

func (x xAgentModeLogDo) Find() ([]*model.XAgentModeLog, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentModeLog), err
}

func (x xAgentModeLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentModeLog, err error) {
	buf := make([]*model.XAgentModeLog, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentModeLogDo) FindInBatches(result *[]*model.XAgentModeLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentModeLogDo) Attrs(attrs ...field.AssignExpr) *xAgentModeLogDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentModeLogDo) Assign(attrs ...field.AssignExpr) *xAgentModeLogDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentModeLogDo) Joins(fields ...field.RelationField) *xAgentModeLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentModeLogDo) Preload(fields ...field.RelationField) *xAgentModeLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentModeLogDo) FirstOrInit() (*model.XAgentModeLog, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentModeLog), nil
	}
}

func (x xAgentModeLogDo) FirstOrCreate() (*model.XAgentModeLog, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentModeLog), nil
	}
}

func (x xAgentModeLogDo) FindByPage(offset int, limit int) (result []*model.XAgentModeLog, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentModeLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentModeLogDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentModeLogDo) Delete(models ...*model.XAgentModeLog) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentModeLogDo) withDO(do gen.Dao) *xAgentModeLogDo {
	x.DO = *do.(*gen.DO)
	return x
}
