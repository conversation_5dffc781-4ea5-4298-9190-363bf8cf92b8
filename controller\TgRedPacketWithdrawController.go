package controller

import (
	"errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type TgRedPacketWithdrawController struct{}

func (c *TgRedPacketWithdrawController) Init() {
	group := server.Http().NewGroup("/api/tgRedPacketWithdraw")
	{
		group.Post("/list", c.list)
		group.Post("/audit", c.audit)
		//group.Post("/create", c.create)
		//group.Post("/update", c.update)
	}
}

func (c *TgRedPacketWithdrawController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID   int64
		Page       int
		PageSize   int
		TgId       int64
		TgUserId   int64
		TgUserName string
		StartTime  string
		EndTime    string
		OrderNum   string
	}

	type Result struct {
		model.XTgRedpacketWithdrawLog
		SellerName    string
		Operator      string
		RobotUserName string
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人提现", "查", "红包机器人提现")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgRedpacketWithdrawLog
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	var list []*Result
	//xChannel := server.DaoxHashGame().XChannel
	xSeller := server.DaoxHashGame().XSeller
	xAdmin := server.DaoxHashGame().AdminUser
	xRobot := server.DaoxHashGame().XTgRobotRedpacket
	if reqdata.TgUserId > 0 {
		db = db.Where(dao.TgUserID.Eq(reqdata.TgUserId))
	}

	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(int32(reqdata.SellerID)))
	}

	if reqdata.TgId > 0 {
		db = db.Where(dao.TgID.Eq(int32(reqdata.TgId)))
	}

	if reqdata.TgUserName != "" {
		db = db.Where(dao.TgUsername.Eq(reqdata.TgUserName))
	}

	if reqdata.OrderNum != "" {
		db = db.Where(dao.OrderNumber.Eq(reqdata.OrderNum))
	}

	if reqdata.StartTime != "" && reqdata.EndTime != "" {
		location, _ := time.LoadLocation("Asia/Shanghai")
		startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", reqdata.StartTime, location)
		endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", reqdata.EndTime, location)
		db = db.Where(dao.CreatedAt.Between(startTime, endTime))
	}

	total, err := db.
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		LeftJoin(xAdmin, xAdmin.ID.EqCol(dao.OperatorID)).
		LeftJoin(xRobot, xRobot.ID.EqCol(dao.TgID)).
		Select(dao.ALL, xSeller.SellerName, xAdmin.Account.As("Operator"), xRobot.TgRobotUserName.As("RobotUserName")).
		Order(dao.CreatedAt.Desc()).
		ScanByPage(&list, offset, limit)

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TgRedPacketWithdrawController) audit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id             int32
		State          int32
		GoogleCode     string
		PaltformUserId int64
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人提现", "改", "红包机器人提现修改")
	if token == nil {
		return
	}

	if reqdata.State != 2 && reqdata.PaltformUserId < 0 {
		ctx.RespErr(errors.New("请输入平台用户ID"), &errcode)
	}

	operatorId := server.GetToken(ctx).UserId

	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		orderInfos, err := tx.XTgRedpacketWithdrawLog.WithContext(ctx.Gin()).Clauses(clause.Locking{Strength: "UPDATE"}).
			Where(tx.XTgRedpacketWithdrawLog.ID.Eq(reqdata.Id)).
			Where(tx.XTgRedpacketWithdrawLog.State.Eq(1)).
			First()
		if err != nil {
			return err
		}
		// 更新订单状态
		// 通过
		if reqdata.State == 2 {
			// 判断用户是否存在
			_, err = tx.XUser.WithContext(ctx.Gin()).Where(tx.XUser.UserID.Eq(int32(reqdata.PaltformUserId))).First()
			if err != nil {
				return errors.New("您输入的平台用户不存在")
			}

			tx.XTgRedpacketWithdrawLog.WithContext(ctx.Gin()).Where(tx.XTgRedpacketWithdrawLog.ID.Eq(reqdata.Id)).Where(tx.XTgRedpacketWithdrawLog.State.Eq(1)).Updates(map[string]interface{}{
				"State":          2,
				"PaltformUserID": reqdata.PaltformUserId,
				"OperatorID":     operatorId,
				"AuditAt":        time.Now(),
			})
			// 获取配置中的流水限制
			config, err := tx.XTgRobotRedpacket.WithContext(ctx.Gin()).Where(tx.XTgRobotRedpacket.ID.Eq(int64(orderInfos.TgID))).First()
			if err != nil {
				return err
			}

			activeAddUseBalancerInfo := struct {
				UserId            int32
				ActiveName        string
				RealAmount        float64
				WithdrawLiuSuiAdd float64
				BalanceCReason    int
			}{
				UserId:            int32(reqdata.PaltformUserId),
				ActiveName:        "TG抢红包",
				RealAmount:        orderInfos.WithdrawAmount,
				WithdrawLiuSuiAdd: float64(config.WithdrawFlowMultiple) * orderInfos.WithdrawAmount,
				BalanceCReason:    62,
			}
			// 更新用户流水和账变记录
			err = db.ActiveAddUseBalancer(tx, activeAddUseBalancerInfo)
			if err != nil {
				return err
			}
		}

		// 拒绝
		if reqdata.State == 3 {
			_, err := tx.XTgRedpacketWithdrawLog.WithContext(ctx.Gin()).Where(tx.XTgRedpacketWithdrawLog.ID.Eq(reqdata.Id)).Where(tx.XTgRedpacketWithdrawLog.State.Eq(1)).Updates(map[string]interface{}{
				"State":      3,
				"OperatorID": operatorId,
				"AuditAt":    time.Now(),
			})
			if err != nil {
				return err
			}
			// 退还
			_, err = tx.XTgRedpacketUser.WithContext(ctx.Gin()).
				Where(tx.XTgRedpacketUser.TgUserID.Eq(orderInfos.TgUserID)).
				Where(tx.XTgRedpacketUser.TgID.Eq(orderInfos.TgID)).
				Updates(map[string]interface{}{
					"Balance":  gorm.Expr("Balance + ?", orderInfos.WithdrawAmount),
					"Withdraw": gorm.Expr("Withdraw - ?", orderInfos.WithdrawAmount),
				})
			if err != nil {
				return err
			}
		}

		// 没收
		if reqdata.State == 4 {
			tx.XTgRedpacketWithdrawLog.WithContext(ctx.Gin()).Where(tx.XTgRedpacketWithdrawLog.ID.Eq(reqdata.Id)).Where(tx.XTgRedpacketWithdrawLog.State.Eq(1)).Updates(map[string]interface{}{
				"State":      4,
				"OperatorID": operatorId,
				"AuditAt":    time.Now(),
			})
		}

		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}
