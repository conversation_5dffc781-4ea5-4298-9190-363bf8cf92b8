// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameAdminUser = "admin_user"

// AdminUser mapped from table <admin_user>
type AdminUser struct {
	ID              int32     `gorm:"column:Id;not null" json:"Id"`
	Account         string    `gorm:"column:Account;primaryKey;comment:账号" json:"Account"`                                 // 账号
	Password        string    `gorm:"column:Password;not null;comment:密码" json:"Password"`                                 // 密码
	SellerID        int32     `gorm:"column:SellerId;not null;comment:运营商" json:"SellerId"`                                // 运营商
	RoleName        string    `gorm:"column:RoleName;not null;comment:角色名" json:"RoleName"`                                // 角色名
	State           int32     `gorm:"column:State;default:1;comment:状态 1启用 2禁用" json:"State"`                              // 状态 1启用 2禁用
	Token           string    `gorm:"column:Token;comment:token" json:"Token"`                                             // token
	GoogleSecret    string    `gorm:"column:GoogleSecret;comment:谷歌验证码" json:"GoogleSecret"`                               // 谷歌验证码
	Remark          string    `gorm:"column:Remark;comment:备注" json:"Remark"`                                              // 备注
	LoginCount      int32     `gorm:"column:LoginCount;comment:登录次数" json:"LoginCount"`                                    // 登录次数
	LoginTime       time.Time `gorm:"column:LoginTime;comment:最后登录时间" json:"LoginTime"`                                    // 最后登录时间
	LoginIP         string    `gorm:"column:LoginIp;comment:最后登录Ip" json:"LoginIp"`                                        // 最后登录Ip
	CreateTime      time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	IPWhite         string    `gorm:"column:IpWhite;comment:ip白名单,空表无白名单,所有ip均可登录" json:"IpWhite"`                         // ip白名单,空表无白名单,所有ip均可登录
	OptGoogleSecret string    `gorm:"column:OptGoogleSecret;comment:操作谷歌验证码" json:"OptGoogleSecret"`                       // 操作谷歌验证码
	RecvNotice      int32     `gorm:"column:RecvNotice;default:1;comment:是否接受通知 1是,2否" json:"RecvNotice"`                  // 是否接受通知 1是,2否
	ChannelID       int32     `gorm:"column:ChannelId;default:1;comment:渠道" json:"ChannelId"`                              // 渠道
	RoleType        int32     `gorm:"column:RoleType;default:1;comment:1运营角色 2渠道角色" json:"RoleType"`                       // 1运营角色 2渠道角色
	MaxAddMoney     int32     `gorm:"column:MaxAddMoney;comment:后台增资最大金额" json:"MaxAddMoney"`                              // 后台增资最大金额
	CSGroup         string    `gorm:"column:CSGroup;comment:客服组" json:"CSGroup"`                                           // 客服组
	CSID            string    `gorm:"column:CSId;comment:客服Id" json:"CSId"`                                                // 客服Id
	IsIPWhite       int32     `gorm:"column:IsIpWhite;default:1;comment:1-开启 2-关闭" json:"IsIpWhite"`                       // 1-开启 2-关闭
}

// TableName AdminUser's table name
func (*AdminUser) TableName() string {
	return TableNameAdminUser
}
