// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRobotRedpacket = "x_tg_robot_redpacket"

// XTgRobotRedpacket mapped from table <x_tg_robot_redpacket>
type XTgRobotRedpacket struct {
	ID              int64  `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID        int32  `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                                                          // 运营商id
	ChannelID       int32  `gorm:"column:ChannelId;comment:渠道id" json:"ChannelId"`                                                                         // 渠道id
	TgRobotUserName string `gorm:"column:TgRobotUserName;not null;comment:机器人Username" json:"TgRobotUserName"`                                             // 机器人Username
	GuideRobotID    int32  `gorm:"column:GuideRobotId;not null;comment:关联接待机器人表id" json:"GuideRobotId"`                                                    // 关联接待机器人表id
	TgRobotToken    string `gorm:"column:TgRobotToken;not null;comment:机器人token" json:"TgRobotToken"`                                                      // 机器人token
	KefuTgUserName  string `gorm:"column:KefuTgUserName;not null;comment:客服tg号" json:"KefuTgUserName"`                                                     // 客服tg号
	TgGroupID       int64  `gorm:"column:TgGroupId;comment:特定群组ID" json:"TgGroupId"`                                                                       // 特定群组ID
	TgGroupName     string `gorm:"column:TgGroupName;comment:tg群名称" json:"TgGroupName"`                                                                    // tg群名称
	TgAdminID       int64  `gorm:"column:TgAdminId;comment:指定管理员ID" json:"TgAdminId"`                                                                      // 指定管理员ID
	GameURL         string `gorm:"column:GameUrl;comment:游戏链接" json:"GameUrl"`                                                                             // 游戏链接
	GrabContent     string `gorm:"column:GrabContent;comment:抢红包文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"GrabContent"` // 抢红包文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	/*
		红包机器人开始文案
		{"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	*/
	StartContent         string    `gorm:"column:StartContent;comment:红包机器人开始文案\n{"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"StartContent"`
	IsEnable             int32     `gorm:"column:IsEnable;comment:是否启用 (1:是 2:否)" json:"IsEnable"`                                     // 是否启用 (1:是 2:否)
	LimitWithdrawAmount  float64   `gorm:"column:LimitWithdrawAmount;not null;default:0.00;comment:提现最低金额" json:"LimitWithdrawAmount"` // 提现最低金额
	FreeWithdrawCount    int32     `gorm:"column:FreeWithdrawCount;not null;comment:免费提现次数" json:"FreeWithdrawCount"`                  // 免费提现次数
	FreeGrabCount        int32     `gorm:"column:FreeGrabCount;not null;comment:免费抢红包次数" json:"FreeGrabCount"`                         // 免费抢红包次数
	InviteCount          int32     `gorm:"column:InviteCount;not null;comment:需要邀请xx人获得提现次数" json:"InviteCount"`                       // 需要邀请xx人获得提现次数
	InviteGetChance      int32     `gorm:"column:InviteGetChance;not null;comment:邀请满足多少人后获得xxx提现次数" json:"InviteGetChance"`           // 邀请满足多少人后获得xxx提现次数
	InviteGrabCount      int32     `gorm:"column:inviteGrabCount;not null;comment:需要邀请xx人获得抢红包机会" json:"inviteGrabCount"`              // 需要邀请xx人获得抢红包机会
	InviteGetGrabChance  int32     `gorm:"column:inviteGetGrabChance;not null;comment:满足邀请人数后获得xx次抢红包次数" json:"inviteGetGrabChance"`   // 满足邀请人数后获得xx次抢红包次数
	WithdrawFlowMultiple int32     `gorm:"column:WithdrawFlowMultiple;not null;comment:提现需要的流水倍数" json:"WithdrawFlowMultiple"`         // 提现需要的流水倍数
	ResetWithdrawCount   int32     `gorm:"column:ResetWithdrawCount;comment:提现每日重置次数" json:"ResetWithdrawCount"`                       // 提现每日重置次数
	ResetGrabCount       int32     `gorm:"column:ResetGrabCount;comment:抢红包每日重置次数" json:"ResetGrabCount"`                              // 抢红包每日重置次数
	CreatedAt            time.Time `gorm:"column:CreatedAt;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreatedAt"`                   // 创建时间
	UpdatedAt            time.Time `gorm:"column:UpdatedAt;default:CURRENT_TIMESTAMP;comment:修改时间" json:"UpdatedAt"`                   // 修改时间
}

// TableName XTgRobotRedpacket's table name
func (*XTgRobotRedpacket) TableName() string {
	return TableNameXTgRobotRedpacket
}
