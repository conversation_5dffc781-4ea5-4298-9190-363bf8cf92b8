// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentDataDate(db *gorm.DB, opts ...gen.DOOption) xAgentDataDate {
	_xAgentDataDate := xAgentDataDate{}

	_xAgentDataDate.xAgentDataDateDo.UseDB(db, opts...)
	_xAgentDataDate.xAgentDataDateDo.UseModel(&model.XAgentDataDate{})

	tableName := _xAgentDataDate.xAgentDataDateDo.TableName()
	_xAgentDataDate.ALL = field.NewAsterisk(tableName)
	_xAgentDataDate.ID = field.NewInt32(tableName, "Id")
	_xAgentDataDate.RecordDate = field.NewTime(tableName, "RecordDate")
	_xAgentDataDate.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xAgentDataDate.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentDataDate.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentDataDate.SpendAmount = field.NewFloat64(tableName, "SpendAmount")
	_xAgentDataDate.RegUsers = field.NewInt32(tableName, "RegUsers")
	_xAgentDataDate.NewRechargeUsers = field.NewInt32(tableName, "NewRechargeUsers")
	_xAgentDataDate.NewRechargeAmount = field.NewInt32(tableName, "NewRechargeAmount")
	_xAgentDataDate.NewRechargeUsers2 = field.NewFloat64(tableName, "NewRechargeUsers2")
	_xAgentDataDate.NewRechargeUsers3 = field.NewFloat64(tableName, "NewRechargeUsers3")
	_xAgentDataDate.NewRechargeUsers7 = field.NewFloat64(tableName, "NewRechargeUsers7")
	_xAgentDataDate.NewRechargeUsers15 = field.NewFloat64(tableName, "NewRechargeUsers15")
	_xAgentDataDate.NewTransferBetUsers = field.NewInt32(tableName, "NewTransferBetUsers")
	_xAgentDataDate.NewTransferBetCount = field.NewInt32(tableName, "NewTransferBetCount")
	_xAgentDataDate.NewTransferBetAmount = field.NewFloat64(tableName, "NewTransferBetAmount")
	_xAgentDataDate.NewTransferWinAmount = field.NewFloat64(tableName, "NewTransferWinAmount")
	_xAgentDataDate.NewTransferBetUsers2 = field.NewInt32(tableName, "NewTransferBetUsers2")
	_xAgentDataDate.NewTransferBetUsers3 = field.NewInt32(tableName, "NewTransferBetUsers3")
	_xAgentDataDate.NewTransferBetUsers7 = field.NewInt32(tableName, "NewTransferBetUsers7")
	_xAgentDataDate.NewTransferUsdtBetUsers = field.NewInt32(tableName, "NewTransferUsdtBetUsers")
	_xAgentDataDate.NewTransferUsdtBetCount = field.NewInt32(tableName, "NewTransferUsdtBetCount")
	_xAgentDataDate.NewTransferUsdtBetAmount = field.NewFloat64(tableName, "NewTransferUsdtBetAmount")
	_xAgentDataDate.NewTransferUsdtWinAmount = field.NewFloat64(tableName, "NewTransferUsdtWinAmount")
	_xAgentDataDate.NewTransferUsdtFeeAmount = field.NewFloat64(tableName, "NewTransferUsdtFeeAmount")
	_xAgentDataDate.NewTransferUsdtLiuShuiAmount = field.NewFloat64(tableName, "NewTransferUsdtLiuShuiAmount")
	_xAgentDataDate.NewTransferUsdtBetUsers2 = field.NewInt32(tableName, "NewTransferUsdtBetUsers2")
	_xAgentDataDate.NewTransferUsdtBetUsers3 = field.NewInt32(tableName, "NewTransferUsdtBetUsers3")
	_xAgentDataDate.NewTransferUsdtBetUsers7 = field.NewInt32(tableName, "NewTransferUsdtBetUsers7")
	_xAgentDataDate.NewTransferUsdtBetUsers15 = field.NewInt32(tableName, "NewTransferUsdtBetUsers15")
	_xAgentDataDate.NewTransferTrxBetUsers = field.NewInt32(tableName, "NewTransferTrxBetUsers")
	_xAgentDataDate.NewTransferTrxBetCount = field.NewInt32(tableName, "NewTransferTrxBetCount")
	_xAgentDataDate.NewTransferTrxBetAmount = field.NewFloat64(tableName, "NewTransferTrxBetAmount")
	_xAgentDataDate.NewTransferTrxWinAmount = field.NewFloat64(tableName, "NewTransferTrxWinAmount")
	_xAgentDataDate.NewTransferTrxFeeAmount = field.NewFloat64(tableName, "NewTransferTrxFeeAmount")
	_xAgentDataDate.NewTransferTrxLiuShuiAmount = field.NewFloat64(tableName, "NewTransferTrxLiuShuiAmount")
	_xAgentDataDate.NewTransferTrxBetUsers2 = field.NewInt32(tableName, "NewTransferTrxBetUsers2")
	_xAgentDataDate.NewTransferTrxBetUsers3 = field.NewInt32(tableName, "NewTransferTrxBetUsers3")
	_xAgentDataDate.NewTransferTrxBetUsers7 = field.NewInt32(tableName, "NewTransferTrxBetUsers7")
	_xAgentDataDate.NewTransferTrxBetUsers15 = field.NewInt32(tableName, "NewTransferTrxBetUsers15")
	_xAgentDataDate.NewBetUsers = field.NewInt32(tableName, "NewBetUsers")
	_xAgentDataDate.NewBetCount = field.NewInt32(tableName, "NewBetCount")
	_xAgentDataDate.NewBetAmount = field.NewFloat64(tableName, "NewBetAmount")
	_xAgentDataDate.NewWinAmount = field.NewFloat64(tableName, "NewWinAmount")
	_xAgentDataDate.NewTotalBetUsers = field.NewInt32(tableName, "NewTotalBetUsers")
	_xAgentDataDate.TotalNewUsers = field.NewInt32(tableName, "TotalNewUsers")
	_xAgentDataDate.ValidRecharge10Users = field.NewInt32(tableName, "ValidRecharge10Users")
	_xAgentDataDate.ValidRecharge50Users = field.NewInt32(tableName, "ValidRecharge50Users")
	_xAgentDataDate.ValidRecharge100Users = field.NewInt32(tableName, "ValidRecharge100Users")
	_xAgentDataDate.RechargeUsers = field.NewInt32(tableName, "RechargeUsers")
	_xAgentDataDate.RechargeCount = field.NewInt32(tableName, "RechargeCount")
	_xAgentDataDate.RechargeAmount = field.NewFloat64(tableName, "RechargeAmount")
	_xAgentDataDate.TotalBetUsers = field.NewInt32(tableName, "TotalBetUsers")
	_xAgentDataDate.TotalBetCount = field.NewInt32(tableName, "TotalBetCount")
	_xAgentDataDate.TotalBetAmount = field.NewFloat64(tableName, "TotalBetAmount")
	_xAgentDataDate.TotalWinAmount = field.NewFloat64(tableName, "TotalWinAmount")
	_xAgentDataDate.TotalFeeAmount = field.NewFloat64(tableName, "TotalFeeAmount")
	_xAgentDataDate.TransferBetUsers = field.NewInt32(tableName, "TransferBetUsers")
	_xAgentDataDate.TransferBetCount = field.NewInt32(tableName, "TransferBetCount")
	_xAgentDataDate.TransferBetAmount = field.NewFloat64(tableName, "TransferBetAmount")
	_xAgentDataDate.TransferWinAmount = field.NewFloat64(tableName, "TransferWinAmount")
	_xAgentDataDate.TransferUsdtBetUsers = field.NewInt32(tableName, "TransferUsdtBetUsers")
	_xAgentDataDate.TransferUsdtBetCount = field.NewInt32(tableName, "TransferUsdtBetCount")
	_xAgentDataDate.TransferUsdtWinCount = field.NewInt32(tableName, "TransferUsdtWinCount")
	_xAgentDataDate.TransferUsdtBetAmount = field.NewFloat64(tableName, "TransferUsdtBetAmount")
	_xAgentDataDate.TransferUsdtWinAmount = field.NewFloat64(tableName, "TransferUsdtWinAmount")
	_xAgentDataDate.TransferUsdtFeeAmount = field.NewFloat64(tableName, "TransferUsdtFeeAmount")
	_xAgentDataDate.TransferUsdtLiuShuiAmount = field.NewFloat64(tableName, "TransferUsdtLiuShuiAmount")
	_xAgentDataDate.TransferTrxBetUsers = field.NewInt32(tableName, "TransferTrxBetUsers")
	_xAgentDataDate.TransferTrxBetCount = field.NewInt32(tableName, "TransferTrxBetCount")
	_xAgentDataDate.TransferTrxWinCount = field.NewInt32(tableName, "TransferTrxWinCount")
	_xAgentDataDate.TransferTrxBetAmount = field.NewFloat64(tableName, "TransferTrxBetAmount")
	_xAgentDataDate.TransferTrxWinAmount = field.NewFloat64(tableName, "TransferTrxWinAmount")
	_xAgentDataDate.TransferTrxFeeAmount = field.NewFloat64(tableName, "TransferTrxFeeAmount")
	_xAgentDataDate.TransferTrxLiuShuiAmount = field.NewFloat64(tableName, "TransferTrxLiuShuiAmount")
	_xAgentDataDate.BetUsers = field.NewInt32(tableName, "BetUsers")
	_xAgentDataDate.BetCount = field.NewInt32(tableName, "BetCount")
	_xAgentDataDate.WinCount = field.NewInt32(tableName, "WinCount")
	_xAgentDataDate.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xAgentDataDate.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xAgentDataDate.FeeAmount = field.NewFloat64(tableName, "FeeAmount")
	_xAgentDataDate.LiuShuiAmount = field.NewFloat64(tableName, "LiuShuiAmount")
	_xAgentDataDate.TotalPayUsers = field.NewInt32(tableName, "TotalPayUsers")
	_xAgentDataDate.TotalPayUsers2 = field.NewInt32(tableName, "TotalPayUsers2")
	_xAgentDataDate.TotalPayUsers3 = field.NewInt32(tableName, "TotalPayUsers3")
	_xAgentDataDate.TotalPayUsers7 = field.NewInt32(tableName, "TotalPayUsers7")
	_xAgentDataDate.RewardUsers = field.NewInt32(tableName, "RewardUsers")
	_xAgentDataDate.RewardCount = field.NewInt32(tableName, "RewardCount")
	_xAgentDataDate.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xAgentDataDate.ManReduceRewardUsers = field.NewInt32(tableName, "ManReduceRewardUsers")
	_xAgentDataDate.ManReduceRewardCount = field.NewInt32(tableName, "ManReduceRewardCount")
	_xAgentDataDate.ManReduceRewardAmount = field.NewFloat64(tableName, "ManReduceRewardAmount")
	_xAgentDataDate.VipRewardUsers = field.NewInt32(tableName, "VipRewardUsers")
	_xAgentDataDate.VipRewardCount = field.NewInt32(tableName, "VipRewardCount")
	_xAgentDataDate.VipRewardAmount = field.NewFloat64(tableName, "VipRewardAmount")
	_xAgentDataDate.GetCommissionUsers = field.NewInt32(tableName, "GetCommissionUsers")
	_xAgentDataDate.GetCommissionCount = field.NewInt32(tableName, "GetCommissionCount")
	_xAgentDataDate.GetCommissionAmount = field.NewFloat64(tableName, "GetCommissionAmount")
	_xAgentDataDate.WithdrawUsers = field.NewInt32(tableName, "WithdrawUsers")
	_xAgentDataDate.WithdrawCount = field.NewInt32(tableName, "WithdrawCount")
	_xAgentDataDate.WithdrawAmount = field.NewFloat64(tableName, "WithdrawAmount")
	_xAgentDataDate.SumSpendAmount = field.NewFloat64(tableName, "SumSpendAmount")
	_xAgentDataDate.SumNewRechargeUsers = field.NewInt32(tableName, "SumNewRechargeUsers")
	_xAgentDataDate.SumTotalPayUsers = field.NewInt32(tableName, "SumTotalPayUsers")
	_xAgentDataDate.SumRechargeAmount = field.NewFloat64(tableName, "SumRechargeAmount")
	_xAgentDataDate.SumWithdrawAmount = field.NewFloat64(tableName, "SumWithdrawAmount")
	_xAgentDataDate.SumTransferBetAmount = field.NewFloat64(tableName, "SumTransferBetAmount")
	_xAgentDataDate.SumTransferWinAmount = field.NewFloat64(tableName, "SumTransferWinAmount")
	_xAgentDataDate.SumBetAmount = field.NewFloat64(tableName, "SumBetAmount")
	_xAgentDataDate.SumWinAmount = field.NewFloat64(tableName, "SumWinAmount")
	_xAgentDataDate.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentDataDate.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentDataDate.fillFieldMap()

	return _xAgentDataDate
}

// xAgentDataDate 顶级代理数据(按日期)
type xAgentDataDate struct {
	xAgentDataDateDo xAgentDataDateDo

	ALL                          field.Asterisk
	ID                           field.Int32
	RecordDate                   field.Time
	TopAgentID                   field.Int32
	SellerID                     field.Int32
	ChannelID                    field.Int32
	SpendAmount                  field.Float64 // 当日消耗金额
	RegUsers                     field.Int32   // 注册人数
	NewRechargeUsers             field.Int32   // 新增充值人数
	NewRechargeAmount            field.Int32   // 新增充值金额(首充金额)
	NewRechargeUsers2            field.Float64 // 首充次日人数
	NewRechargeUsers3            field.Float64 // 首充3日人数
	NewRechargeUsers7            field.Float64 // 首充7日人数
	NewRechargeUsers15           field.Float64 // 首充15日人数(运营总报表)
	NewTransferBetUsers          field.Int32   // 新增转账人数
	NewTransferBetCount          field.Int32   // 新增转账注单数
	NewTransferBetAmount         field.Float64 // 新增转账注单金额
	NewTransferWinAmount         field.Float64 // 新增转账派彩金额
	NewTransferBetUsers2         field.Int32   // 新增转账次日人数
	NewTransferBetUsers3         field.Int32   // 新增转账3日人数
	NewTransferBetUsers7         field.Int32   // 新增转账7日人数
	NewTransferUsdtBetUsers      field.Int32   // 新增转账Usdt人数(运营总报表)
	NewTransferUsdtBetCount      field.Int32   // 新增转账Usdt注单数(运营总报表)
	NewTransferUsdtBetAmount     field.Float64 // 新增转账Usdt注单金额(运营总报表)
	NewTransferUsdtWinAmount     field.Float64 // 新增转账Usdt派彩金额(运营总报表)
	NewTransferUsdtFeeAmount     field.Float64 // 新增转账Usdt手续费(运营总报表)
	NewTransferUsdtLiuShuiAmount field.Float64 // 新增转账Usdt流水金额(运营总报表)
	NewTransferUsdtBetUsers2     field.Int32   // 新增转账Usdt次日人数(运营总报表)
	NewTransferUsdtBetUsers3     field.Int32   // 新增转账Usdt3日人数(运营总报表)
	NewTransferUsdtBetUsers7     field.Int32   // 新增转账Usdt7日人数(运营总报表)
	NewTransferUsdtBetUsers15    field.Int32   // 新增转账Usdt15日人数(运营总报表)
	NewTransferTrxBetUsers       field.Int32   // 新增转账Trx人数(运营总报表)
	NewTransferTrxBetCount       field.Int32   // 新增转账Trx注单数(运营总报表)
	NewTransferTrxBetAmount      field.Float64 // 新增转账Trx注单金额(运营总报表)
	NewTransferTrxWinAmount      field.Float64 // 新增转账Trx派彩金额(运营总报表)
	NewTransferTrxFeeAmount      field.Float64 // 新增转账Trx手续费(运营总报表)
	NewTransferTrxLiuShuiAmount  field.Float64 // 新增转账Trx流水金额(运营总报表)
	NewTransferTrxBetUsers2      field.Int32   // 新增转账Trx次日人数(运营总报表)
	NewTransferTrxBetUsers3      field.Int32   // 新增转账Trx3日人数(运营总报表)
	NewTransferTrxBetUsers7      field.Int32   // 新增转账Trx7日人数(运营总报表)
	NewTransferTrxBetUsers15     field.Int32   // 新增转账Trx15日人数(运营总报表)
	NewBetUsers                  field.Int32   // 新增余额投注人数
	NewBetCount                  field.Int32   // 新增余额注单数
	NewBetAmount                 field.Float64 // 新增余额注单金额
	NewWinAmount                 field.Float64 // 新增余额派彩金额
	NewTotalBetUsers             field.Int32   // 新增总投注人数(运营总报表)
	TotalNewUsers                field.Int32   // 总新增人数 新增充值+新增转账人数，同时人数需要去重
	ValidRecharge10Users         field.Int32   // 10元充值人数(市场报表)
	ValidRecharge50Users         field.Int32   // 50元充值人数(市场报表)
	ValidRecharge100Users        field.Int32   // 100元充值人数(市场报表)
	RechargeUsers                field.Int32   // 充值人数
	RechargeCount                field.Int32   // 充值笔数
	RechargeAmount               field.Float64 // 充值金额
	TotalBetUsers                field.Int32   // 总投注人数(市场报表)
	TotalBetCount                field.Int32   // 总注单数(运营总报表)
	TotalBetAmount               field.Float64 // 总注单金额(运营总报表)
	TotalWinAmount               field.Float64 // 总派彩金额(运营总报表)
	TotalFeeAmount               field.Float64 // 总手续费(运营总报表)
	TransferBetUsers             field.Int32   // 转账人数
	TransferBetCount             field.Int32   // 转账注单数
	TransferBetAmount            field.Float64 // 转账注单金额
	TransferWinAmount            field.Float64 // 转账派彩金额
	TransferUsdtBetUsers         field.Int32   // 转账Usdt人数(市场报表)
	TransferUsdtBetCount         field.Int32   // 转账Usdt注单数(市场报表)
	TransferUsdtWinCount         field.Int32   // 转账Usdt中奖次数(运营总报表)
	TransferUsdtBetAmount        field.Float64 // 转账Usdt注单金额(市场报表)
	TransferUsdtWinAmount        field.Float64 // 转账Usdt派彩金额(市场报表)
	TransferUsdtFeeAmount        field.Float64 // 转账Usdt手续费(运营总报表)
	TransferUsdtLiuShuiAmount    field.Float64 // 转账Usdt流水金额(市场报表)
	TransferTrxBetUsers          field.Int32   // 转账Trx人数(市场报表)
	TransferTrxBetCount          field.Int32   // 转账Trx注单数(市场报表)
	TransferTrxWinCount          field.Int32   // 转账Trx中奖次数(运营总报表)
	TransferTrxBetAmount         field.Float64 // 转账Trx注单金额(市场报表)
	TransferTrxWinAmount         field.Float64 // 转账Trx派彩金额(市场报表)
	TransferTrxFeeAmount         field.Float64 // 转账Trx手续费(运营总报表)
	TransferTrxLiuShuiAmount     field.Float64 // 转账Trx流水金额(市场报表)
	BetUsers                     field.Int32   // 余额人数
	BetCount                     field.Int32   // 余额注单数
	WinCount                     field.Int32   // 余额中奖次数(运营总报表)
	BetAmount                    field.Float64 // 余额注单金额
	WinAmount                    field.Float64 // 余额派彩金额
	FeeAmount                    field.Float64 // 余额手续费(运营总报表)
	LiuShuiAmount                field.Float64 // 余额流水金额(运营总报表)
	TotalPayUsers                field.Int32   // 总付费人数 充值人数+转账人数，同时去重
	TotalPayUsers2               field.Int32   // 总付费次日人数 充值人数+转账人数，同时去重
	TotalPayUsers3               field.Int32   // 总付费3日人数 充值人数+转账人数，同时去重
	TotalPayUsers7               field.Int32   // 总付费7日人数 充值人数+转账人数，同时去重
	RewardUsers                  field.Int32   // 活动彩金人数(运营总报表)
	RewardCount                  field.Int32   // 活动彩金笔数(运营总报表)
	RewardAmount                 field.Float64 // 活动彩金金额(运营总报表)
	ManReduceRewardUsers         field.Int32   // 人工减少活动彩金人数(运营总报表)
	ManReduceRewardCount         field.Int32   // 人工减少活动彩金笔数(运营总报表)
	ManReduceRewardAmount        field.Float64 // 人工减少活动彩金金额(运营总报表)
	VipRewardUsers               field.Int32   // Vip活动彩金人数(运营总报表)
	VipRewardCount               field.Int32   // Vip活动彩金笔数(运营总报表)
	VipRewardAmount              field.Float64 // Vip活动彩金金额(运营总报表)
	GetCommissionUsers           field.Int32   // 领取佣金人数(运营总报表)
	GetCommissionCount           field.Int32   // 领取佣金笔数(运营总报表)
	GetCommissionAmount          field.Float64 // 领取佣金金额(运营总报表)
	WithdrawUsers                field.Int32   // 提款人数
	WithdrawCount                field.Int32   // 提款笔数
	WithdrawAmount               field.Float64 // 提款金额
	SumSpendAmount               field.Float64 // 累计消耗金额
	SumNewRechargeUsers          field.Int32   // 累计首充人数
	SumTotalPayUsers             field.Int32   // 累计付费人数
	SumRechargeAmount            field.Float64 // 累计充值金额
	SumWithdrawAmount            field.Float64 // 累计提款金额
	SumTransferBetAmount         field.Float64 // 累计转账注单金额
	SumTransferWinAmount         field.Float64 // 累计转账派彩金额
	SumBetAmount                 field.Float64 // 累计余额注单金额
	SumWinAmount                 field.Float64 // 累计余额派彩金额
	CreateTime                   field.Time    // 创建时间
	UpdateTime                   field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentDataDate) Table(newTableName string) *xAgentDataDate {
	x.xAgentDataDateDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentDataDate) As(alias string) *xAgentDataDate {
	x.xAgentDataDateDo.DO = *(x.xAgentDataDateDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentDataDate) updateTableName(table string) *xAgentDataDate {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.SpendAmount = field.NewFloat64(table, "SpendAmount")
	x.RegUsers = field.NewInt32(table, "RegUsers")
	x.NewRechargeUsers = field.NewInt32(table, "NewRechargeUsers")
	x.NewRechargeAmount = field.NewInt32(table, "NewRechargeAmount")
	x.NewRechargeUsers2 = field.NewFloat64(table, "NewRechargeUsers2")
	x.NewRechargeUsers3 = field.NewFloat64(table, "NewRechargeUsers3")
	x.NewRechargeUsers7 = field.NewFloat64(table, "NewRechargeUsers7")
	x.NewRechargeUsers15 = field.NewFloat64(table, "NewRechargeUsers15")
	x.NewTransferBetUsers = field.NewInt32(table, "NewTransferBetUsers")
	x.NewTransferBetCount = field.NewInt32(table, "NewTransferBetCount")
	x.NewTransferBetAmount = field.NewFloat64(table, "NewTransferBetAmount")
	x.NewTransferWinAmount = field.NewFloat64(table, "NewTransferWinAmount")
	x.NewTransferBetUsers2 = field.NewInt32(table, "NewTransferBetUsers2")
	x.NewTransferBetUsers3 = field.NewInt32(table, "NewTransferBetUsers3")
	x.NewTransferBetUsers7 = field.NewInt32(table, "NewTransferBetUsers7")
	x.NewTransferUsdtBetUsers = field.NewInt32(table, "NewTransferUsdtBetUsers")
	x.NewTransferUsdtBetCount = field.NewInt32(table, "NewTransferUsdtBetCount")
	x.NewTransferUsdtBetAmount = field.NewFloat64(table, "NewTransferUsdtBetAmount")
	x.NewTransferUsdtWinAmount = field.NewFloat64(table, "NewTransferUsdtWinAmount")
	x.NewTransferUsdtFeeAmount = field.NewFloat64(table, "NewTransferUsdtFeeAmount")
	x.NewTransferUsdtLiuShuiAmount = field.NewFloat64(table, "NewTransferUsdtLiuShuiAmount")
	x.NewTransferUsdtBetUsers2 = field.NewInt32(table, "NewTransferUsdtBetUsers2")
	x.NewTransferUsdtBetUsers3 = field.NewInt32(table, "NewTransferUsdtBetUsers3")
	x.NewTransferUsdtBetUsers7 = field.NewInt32(table, "NewTransferUsdtBetUsers7")
	x.NewTransferUsdtBetUsers15 = field.NewInt32(table, "NewTransferUsdtBetUsers15")
	x.NewTransferTrxBetUsers = field.NewInt32(table, "NewTransferTrxBetUsers")
	x.NewTransferTrxBetCount = field.NewInt32(table, "NewTransferTrxBetCount")
	x.NewTransferTrxBetAmount = field.NewFloat64(table, "NewTransferTrxBetAmount")
	x.NewTransferTrxWinAmount = field.NewFloat64(table, "NewTransferTrxWinAmount")
	x.NewTransferTrxFeeAmount = field.NewFloat64(table, "NewTransferTrxFeeAmount")
	x.NewTransferTrxLiuShuiAmount = field.NewFloat64(table, "NewTransferTrxLiuShuiAmount")
	x.NewTransferTrxBetUsers2 = field.NewInt32(table, "NewTransferTrxBetUsers2")
	x.NewTransferTrxBetUsers3 = field.NewInt32(table, "NewTransferTrxBetUsers3")
	x.NewTransferTrxBetUsers7 = field.NewInt32(table, "NewTransferTrxBetUsers7")
	x.NewTransferTrxBetUsers15 = field.NewInt32(table, "NewTransferTrxBetUsers15")
	x.NewBetUsers = field.NewInt32(table, "NewBetUsers")
	x.NewBetCount = field.NewInt32(table, "NewBetCount")
	x.NewBetAmount = field.NewFloat64(table, "NewBetAmount")
	x.NewWinAmount = field.NewFloat64(table, "NewWinAmount")
	x.NewTotalBetUsers = field.NewInt32(table, "NewTotalBetUsers")
	x.TotalNewUsers = field.NewInt32(table, "TotalNewUsers")
	x.ValidRecharge10Users = field.NewInt32(table, "ValidRecharge10Users")
	x.ValidRecharge50Users = field.NewInt32(table, "ValidRecharge50Users")
	x.ValidRecharge100Users = field.NewInt32(table, "ValidRecharge100Users")
	x.RechargeUsers = field.NewInt32(table, "RechargeUsers")
	x.RechargeCount = field.NewInt32(table, "RechargeCount")
	x.RechargeAmount = field.NewFloat64(table, "RechargeAmount")
	x.TotalBetUsers = field.NewInt32(table, "TotalBetUsers")
	x.TotalBetCount = field.NewInt32(table, "TotalBetCount")
	x.TotalBetAmount = field.NewFloat64(table, "TotalBetAmount")
	x.TotalWinAmount = field.NewFloat64(table, "TotalWinAmount")
	x.TotalFeeAmount = field.NewFloat64(table, "TotalFeeAmount")
	x.TransferBetUsers = field.NewInt32(table, "TransferBetUsers")
	x.TransferBetCount = field.NewInt32(table, "TransferBetCount")
	x.TransferBetAmount = field.NewFloat64(table, "TransferBetAmount")
	x.TransferWinAmount = field.NewFloat64(table, "TransferWinAmount")
	x.TransferUsdtBetUsers = field.NewInt32(table, "TransferUsdtBetUsers")
	x.TransferUsdtBetCount = field.NewInt32(table, "TransferUsdtBetCount")
	x.TransferUsdtWinCount = field.NewInt32(table, "TransferUsdtWinCount")
	x.TransferUsdtBetAmount = field.NewFloat64(table, "TransferUsdtBetAmount")
	x.TransferUsdtWinAmount = field.NewFloat64(table, "TransferUsdtWinAmount")
	x.TransferUsdtFeeAmount = field.NewFloat64(table, "TransferUsdtFeeAmount")
	x.TransferUsdtLiuShuiAmount = field.NewFloat64(table, "TransferUsdtLiuShuiAmount")
	x.TransferTrxBetUsers = field.NewInt32(table, "TransferTrxBetUsers")
	x.TransferTrxBetCount = field.NewInt32(table, "TransferTrxBetCount")
	x.TransferTrxWinCount = field.NewInt32(table, "TransferTrxWinCount")
	x.TransferTrxBetAmount = field.NewFloat64(table, "TransferTrxBetAmount")
	x.TransferTrxWinAmount = field.NewFloat64(table, "TransferTrxWinAmount")
	x.TransferTrxFeeAmount = field.NewFloat64(table, "TransferTrxFeeAmount")
	x.TransferTrxLiuShuiAmount = field.NewFloat64(table, "TransferTrxLiuShuiAmount")
	x.BetUsers = field.NewInt32(table, "BetUsers")
	x.BetCount = field.NewInt32(table, "BetCount")
	x.WinCount = field.NewInt32(table, "WinCount")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.FeeAmount = field.NewFloat64(table, "FeeAmount")
	x.LiuShuiAmount = field.NewFloat64(table, "LiuShuiAmount")
	x.TotalPayUsers = field.NewInt32(table, "TotalPayUsers")
	x.TotalPayUsers2 = field.NewInt32(table, "TotalPayUsers2")
	x.TotalPayUsers3 = field.NewInt32(table, "TotalPayUsers3")
	x.TotalPayUsers7 = field.NewInt32(table, "TotalPayUsers7")
	x.RewardUsers = field.NewInt32(table, "RewardUsers")
	x.RewardCount = field.NewInt32(table, "RewardCount")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.ManReduceRewardUsers = field.NewInt32(table, "ManReduceRewardUsers")
	x.ManReduceRewardCount = field.NewInt32(table, "ManReduceRewardCount")
	x.ManReduceRewardAmount = field.NewFloat64(table, "ManReduceRewardAmount")
	x.VipRewardUsers = field.NewInt32(table, "VipRewardUsers")
	x.VipRewardCount = field.NewInt32(table, "VipRewardCount")
	x.VipRewardAmount = field.NewFloat64(table, "VipRewardAmount")
	x.GetCommissionUsers = field.NewInt32(table, "GetCommissionUsers")
	x.GetCommissionCount = field.NewInt32(table, "GetCommissionCount")
	x.GetCommissionAmount = field.NewFloat64(table, "GetCommissionAmount")
	x.WithdrawUsers = field.NewInt32(table, "WithdrawUsers")
	x.WithdrawCount = field.NewInt32(table, "WithdrawCount")
	x.WithdrawAmount = field.NewFloat64(table, "WithdrawAmount")
	x.SumSpendAmount = field.NewFloat64(table, "SumSpendAmount")
	x.SumNewRechargeUsers = field.NewInt32(table, "SumNewRechargeUsers")
	x.SumTotalPayUsers = field.NewInt32(table, "SumTotalPayUsers")
	x.SumRechargeAmount = field.NewFloat64(table, "SumRechargeAmount")
	x.SumWithdrawAmount = field.NewFloat64(table, "SumWithdrawAmount")
	x.SumTransferBetAmount = field.NewFloat64(table, "SumTransferBetAmount")
	x.SumTransferWinAmount = field.NewFloat64(table, "SumTransferWinAmount")
	x.SumBetAmount = field.NewFloat64(table, "SumBetAmount")
	x.SumWinAmount = field.NewFloat64(table, "SumWinAmount")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentDataDate) WithContext(ctx context.Context) *xAgentDataDateDo {
	return x.xAgentDataDateDo.WithContext(ctx)
}

func (x xAgentDataDate) TableName() string { return x.xAgentDataDateDo.TableName() }

func (x xAgentDataDate) Alias() string { return x.xAgentDataDateDo.Alias() }

func (x xAgentDataDate) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentDataDateDo.Columns(cols...)
}

func (x *xAgentDataDate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentDataDate) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 112)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["SpendAmount"] = x.SpendAmount
	x.fieldMap["RegUsers"] = x.RegUsers
	x.fieldMap["NewRechargeUsers"] = x.NewRechargeUsers
	x.fieldMap["NewRechargeAmount"] = x.NewRechargeAmount
	x.fieldMap["NewRechargeUsers2"] = x.NewRechargeUsers2
	x.fieldMap["NewRechargeUsers3"] = x.NewRechargeUsers3
	x.fieldMap["NewRechargeUsers7"] = x.NewRechargeUsers7
	x.fieldMap["NewRechargeUsers15"] = x.NewRechargeUsers15
	x.fieldMap["NewTransferBetUsers"] = x.NewTransferBetUsers
	x.fieldMap["NewTransferBetCount"] = x.NewTransferBetCount
	x.fieldMap["NewTransferBetAmount"] = x.NewTransferBetAmount
	x.fieldMap["NewTransferWinAmount"] = x.NewTransferWinAmount
	x.fieldMap["NewTransferBetUsers2"] = x.NewTransferBetUsers2
	x.fieldMap["NewTransferBetUsers3"] = x.NewTransferBetUsers3
	x.fieldMap["NewTransferBetUsers7"] = x.NewTransferBetUsers7
	x.fieldMap["NewTransferUsdtBetUsers"] = x.NewTransferUsdtBetUsers
	x.fieldMap["NewTransferUsdtBetCount"] = x.NewTransferUsdtBetCount
	x.fieldMap["NewTransferUsdtBetAmount"] = x.NewTransferUsdtBetAmount
	x.fieldMap["NewTransferUsdtWinAmount"] = x.NewTransferUsdtWinAmount
	x.fieldMap["NewTransferUsdtFeeAmount"] = x.NewTransferUsdtFeeAmount
	x.fieldMap["NewTransferUsdtLiuShuiAmount"] = x.NewTransferUsdtLiuShuiAmount
	x.fieldMap["NewTransferUsdtBetUsers2"] = x.NewTransferUsdtBetUsers2
	x.fieldMap["NewTransferUsdtBetUsers3"] = x.NewTransferUsdtBetUsers3
	x.fieldMap["NewTransferUsdtBetUsers7"] = x.NewTransferUsdtBetUsers7
	x.fieldMap["NewTransferUsdtBetUsers15"] = x.NewTransferUsdtBetUsers15
	x.fieldMap["NewTransferTrxBetUsers"] = x.NewTransferTrxBetUsers
	x.fieldMap["NewTransferTrxBetCount"] = x.NewTransferTrxBetCount
	x.fieldMap["NewTransferTrxBetAmount"] = x.NewTransferTrxBetAmount
	x.fieldMap["NewTransferTrxWinAmount"] = x.NewTransferTrxWinAmount
	x.fieldMap["NewTransferTrxFeeAmount"] = x.NewTransferTrxFeeAmount
	x.fieldMap["NewTransferTrxLiuShuiAmount"] = x.NewTransferTrxLiuShuiAmount
	x.fieldMap["NewTransferTrxBetUsers2"] = x.NewTransferTrxBetUsers2
	x.fieldMap["NewTransferTrxBetUsers3"] = x.NewTransferTrxBetUsers3
	x.fieldMap["NewTransferTrxBetUsers7"] = x.NewTransferTrxBetUsers7
	x.fieldMap["NewTransferTrxBetUsers15"] = x.NewTransferTrxBetUsers15
	x.fieldMap["NewBetUsers"] = x.NewBetUsers
	x.fieldMap["NewBetCount"] = x.NewBetCount
	x.fieldMap["NewBetAmount"] = x.NewBetAmount
	x.fieldMap["NewWinAmount"] = x.NewWinAmount
	x.fieldMap["NewTotalBetUsers"] = x.NewTotalBetUsers
	x.fieldMap["TotalNewUsers"] = x.TotalNewUsers
	x.fieldMap["ValidRecharge10Users"] = x.ValidRecharge10Users
	x.fieldMap["ValidRecharge50Users"] = x.ValidRecharge50Users
	x.fieldMap["ValidRecharge100Users"] = x.ValidRecharge100Users
	x.fieldMap["RechargeUsers"] = x.RechargeUsers
	x.fieldMap["RechargeCount"] = x.RechargeCount
	x.fieldMap["RechargeAmount"] = x.RechargeAmount
	x.fieldMap["TotalBetUsers"] = x.TotalBetUsers
	x.fieldMap["TotalBetCount"] = x.TotalBetCount
	x.fieldMap["TotalBetAmount"] = x.TotalBetAmount
	x.fieldMap["TotalWinAmount"] = x.TotalWinAmount
	x.fieldMap["TotalFeeAmount"] = x.TotalFeeAmount
	x.fieldMap["TransferBetUsers"] = x.TransferBetUsers
	x.fieldMap["TransferBetCount"] = x.TransferBetCount
	x.fieldMap["TransferBetAmount"] = x.TransferBetAmount
	x.fieldMap["TransferWinAmount"] = x.TransferWinAmount
	x.fieldMap["TransferUsdtBetUsers"] = x.TransferUsdtBetUsers
	x.fieldMap["TransferUsdtBetCount"] = x.TransferUsdtBetCount
	x.fieldMap["TransferUsdtWinCount"] = x.TransferUsdtWinCount
	x.fieldMap["TransferUsdtBetAmount"] = x.TransferUsdtBetAmount
	x.fieldMap["TransferUsdtWinAmount"] = x.TransferUsdtWinAmount
	x.fieldMap["TransferUsdtFeeAmount"] = x.TransferUsdtFeeAmount
	x.fieldMap["TransferUsdtLiuShuiAmount"] = x.TransferUsdtLiuShuiAmount
	x.fieldMap["TransferTrxBetUsers"] = x.TransferTrxBetUsers
	x.fieldMap["TransferTrxBetCount"] = x.TransferTrxBetCount
	x.fieldMap["TransferTrxWinCount"] = x.TransferTrxWinCount
	x.fieldMap["TransferTrxBetAmount"] = x.TransferTrxBetAmount
	x.fieldMap["TransferTrxWinAmount"] = x.TransferTrxWinAmount
	x.fieldMap["TransferTrxFeeAmount"] = x.TransferTrxFeeAmount
	x.fieldMap["TransferTrxLiuShuiAmount"] = x.TransferTrxLiuShuiAmount
	x.fieldMap["BetUsers"] = x.BetUsers
	x.fieldMap["BetCount"] = x.BetCount
	x.fieldMap["WinCount"] = x.WinCount
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["FeeAmount"] = x.FeeAmount
	x.fieldMap["LiuShuiAmount"] = x.LiuShuiAmount
	x.fieldMap["TotalPayUsers"] = x.TotalPayUsers
	x.fieldMap["TotalPayUsers2"] = x.TotalPayUsers2
	x.fieldMap["TotalPayUsers3"] = x.TotalPayUsers3
	x.fieldMap["TotalPayUsers7"] = x.TotalPayUsers7
	x.fieldMap["RewardUsers"] = x.RewardUsers
	x.fieldMap["RewardCount"] = x.RewardCount
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["ManReduceRewardUsers"] = x.ManReduceRewardUsers
	x.fieldMap["ManReduceRewardCount"] = x.ManReduceRewardCount
	x.fieldMap["ManReduceRewardAmount"] = x.ManReduceRewardAmount
	x.fieldMap["VipRewardUsers"] = x.VipRewardUsers
	x.fieldMap["VipRewardCount"] = x.VipRewardCount
	x.fieldMap["VipRewardAmount"] = x.VipRewardAmount
	x.fieldMap["GetCommissionUsers"] = x.GetCommissionUsers
	x.fieldMap["GetCommissionCount"] = x.GetCommissionCount
	x.fieldMap["GetCommissionAmount"] = x.GetCommissionAmount
	x.fieldMap["WithdrawUsers"] = x.WithdrawUsers
	x.fieldMap["WithdrawCount"] = x.WithdrawCount
	x.fieldMap["WithdrawAmount"] = x.WithdrawAmount
	x.fieldMap["SumSpendAmount"] = x.SumSpendAmount
	x.fieldMap["SumNewRechargeUsers"] = x.SumNewRechargeUsers
	x.fieldMap["SumTotalPayUsers"] = x.SumTotalPayUsers
	x.fieldMap["SumRechargeAmount"] = x.SumRechargeAmount
	x.fieldMap["SumWithdrawAmount"] = x.SumWithdrawAmount
	x.fieldMap["SumTransferBetAmount"] = x.SumTransferBetAmount
	x.fieldMap["SumTransferWinAmount"] = x.SumTransferWinAmount
	x.fieldMap["SumBetAmount"] = x.SumBetAmount
	x.fieldMap["SumWinAmount"] = x.SumWinAmount
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentDataDate) clone(db *gorm.DB) xAgentDataDate {
	x.xAgentDataDateDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentDataDate) replaceDB(db *gorm.DB) xAgentDataDate {
	x.xAgentDataDateDo.ReplaceDB(db)
	return x
}

type xAgentDataDateDo struct{ gen.DO }

func (x xAgentDataDateDo) Debug() *xAgentDataDateDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentDataDateDo) WithContext(ctx context.Context) *xAgentDataDateDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentDataDateDo) ReadDB() *xAgentDataDateDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentDataDateDo) WriteDB() *xAgentDataDateDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentDataDateDo) Session(config *gorm.Session) *xAgentDataDateDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentDataDateDo) Clauses(conds ...clause.Expression) *xAgentDataDateDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentDataDateDo) Returning(value interface{}, columns ...string) *xAgentDataDateDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentDataDateDo) Not(conds ...gen.Condition) *xAgentDataDateDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentDataDateDo) Or(conds ...gen.Condition) *xAgentDataDateDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentDataDateDo) Select(conds ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentDataDateDo) Where(conds ...gen.Condition) *xAgentDataDateDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentDataDateDo) Order(conds ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentDataDateDo) Distinct(cols ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentDataDateDo) Omit(cols ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentDataDateDo) Join(table schema.Tabler, on ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentDataDateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentDataDateDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentDataDateDo) Group(cols ...field.Expr) *xAgentDataDateDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentDataDateDo) Having(conds ...gen.Condition) *xAgentDataDateDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentDataDateDo) Limit(limit int) *xAgentDataDateDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentDataDateDo) Offset(offset int) *xAgentDataDateDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentDataDateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentDataDateDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentDataDateDo) Unscoped() *xAgentDataDateDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentDataDateDo) Create(values ...*model.XAgentDataDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentDataDateDo) CreateInBatches(values []*model.XAgentDataDate, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentDataDateDo) Save(values ...*model.XAgentDataDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentDataDateDo) First() (*model.XAgentDataDate, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDataDate), nil
	}
}

func (x xAgentDataDateDo) Take() (*model.XAgentDataDate, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDataDate), nil
	}
}

func (x xAgentDataDateDo) Last() (*model.XAgentDataDate, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDataDate), nil
	}
}

func (x xAgentDataDateDo) Find() ([]*model.XAgentDataDate, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentDataDate), err
}

func (x xAgentDataDateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentDataDate, err error) {
	buf := make([]*model.XAgentDataDate, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentDataDateDo) FindInBatches(result *[]*model.XAgentDataDate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentDataDateDo) Attrs(attrs ...field.AssignExpr) *xAgentDataDateDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentDataDateDo) Assign(attrs ...field.AssignExpr) *xAgentDataDateDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentDataDateDo) Joins(fields ...field.RelationField) *xAgentDataDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentDataDateDo) Preload(fields ...field.RelationField) *xAgentDataDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentDataDateDo) FirstOrInit() (*model.XAgentDataDate, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDataDate), nil
	}
}

func (x xAgentDataDateDo) FirstOrCreate() (*model.XAgentDataDate, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDataDate), nil
	}
}

func (x xAgentDataDateDo) FindByPage(offset int, limit int) (result []*model.XAgentDataDate, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentDataDateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentDataDateDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentDataDateDo) Delete(models ...*model.XAgentDataDate) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentDataDateDo) withDO(do gen.Dao) *xAgentDataDateDo {
	x.DO = *do.(*gen.DO)
	return x
}
