// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentCode = "x_agent_code"

// XAgentCode mapped from table <x_agent_code>
type XAgentCode struct {
	ID            int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"`               // id
	UserID        int32     `gorm:"column:UserId;not null;comment:玩家id" json:"UserId"`                          // 玩家id
	AgentCode     string    `gorm:"column:AgentCode;not null;comment:推广码" json:"AgentCode"`                     // 推广码
	FenCheng      float64   `gorm:"column:FenCheng;comment:分成比例" json:"FenCheng"`                               // 分成比例
	RegisterCount int32     `gorm:"column:RegisterCount;comment:注册人数" json:"RegisterCount"`                     // 注册人数
	BetCount      int32     `gorm:"column:BetCount;comment:投注人数" json:"BetCount"`                               // 投注人数
	CreateTime    time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
}

// TableName XAgentCode's table name
func (*XAgentCode) TableName() string {
	return TableNameXAgentCode
}
