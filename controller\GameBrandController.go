package controller

import (
	"github.com/go-sql-driver/mysql"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type GameBrandController struct{}

func (c *GameBrandController) Init() {
	group := server.Http().NewGroup("/api/GameBrand")
	{
		group.Post("/creat", c.creat)
		group.Post("/list", c.list)
		group.Post("/info", c.info)
		group.Post("/update", c.update)
		group.Post("/update_status", c.update_status)
		group.Post("/delete", c.delete)
	}
}

func (c *GameBrandController) creat(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XGameBrand
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方厂商设置", "增", "厂商创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XGameBrand
	db := dao.WithContext(ctx.Gin())
	err := db.Create(&reqdata.XGameBrand)
	if err != nil {
		if e, ok := err.(*mysql.MySQLError); ok && e.Number == 1062 {
			ctx.RespErrString(true, &errcode, "配置已存在，请勿重复添加！")
			return
		}
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *GameBrandController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		model.XGameBrand
	}
	type Result struct {
		model.XGameBrand
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方厂商设置", "查", "厂商查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XGameBrand
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	if reqdata.Status != 0 {
		db = db.Where(dao.Status.Eq(reqdata.Status))
	}
	if reqdata.GameType > 0 {
		db = db.Where(dao.GameType.Eq(reqdata.GameType))
	}
	if reqdata.Brand != "" {
		db = db.Where(dao.Brand.Eq(reqdata.Brand))
	}
	if reqdata.BrandName != "" {
		db = db.Where(dao.BrandName.Like("%" + reqdata.BrandName + "%"))
	}
	var list []Result
	total, err := db.ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("list", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *GameBrandController) info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameType int32  `validate:"required"`
		Brand    string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方厂商设置", "查", "厂商查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XGameBrand
	db := dao.WithContext(ctx.Gin())
	var data struct {
		model.XGameBrand
	}
	err := db.Where(dao.GameType.Eq(reqdata.GameType)).Where(dao.Brand.Eq(reqdata.Brand)).Scan(&data)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *GameBrandController) update(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XGameBrand
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方厂商设置", "改", "修改厂商")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XGameBrand
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.Brand.Eq(reqdata.Brand)).
		Omit(dao.GameType, dao.Brand, dao.ClientGameType, dao.GameSortExID).
		Updates(&reqdata.XGameBrand)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *GameBrandController) update_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XGameBrand
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方厂商设置", "改", "修改厂商状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XGameBrand
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.Brand.Eq(reqdata.Brand)).Update(dao.Status, reqdata.Status)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *GameBrandController) delete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XGameBrand
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方厂商设置", "删", "删除厂商")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XGameBrand
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.Brand.Eq(reqdata.Brand)).Delete()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}
