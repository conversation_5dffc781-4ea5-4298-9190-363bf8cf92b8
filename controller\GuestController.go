package controller

import (
	"encoding/json"
	"fmt"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
)

type GuestController struct {
}

func (c *GuestController) Init() {
	group := server.Http().NewGroup("/api/guest")
	{
		group.Post("/list", c.list)
		group.Post("/set_gamefee", c.set_gamefee)
		group.Post("/sync_gamefee", c.sync_gamefee)
		group.Post("/set_jptype", c.set_jptype)
		group.Post("/set_audit_amount", c.set_audit_amount)
		group.Post("/reset_max_bet", c.reset_max_bet)
		group.Post("/set_blockmaker", c.set_blockmaker)
		group.Post("/ingore_winjiangpei", c.ingore_winjiangpei)
	}
}

func (c *GuestController) reset_max_bet(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChannelId  int
		Address    string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "游客查询", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.Db().Query(`update x_guest set MaxBet = '{"usdt":0,"trx":0}', MaxBetTime = now() where SellerId = ? and ChannelId = ? and Address = ?`, []interface{}{reqdata.SellerId, reqdata.ChannelId, reqdata.Address})
	ctx.RespOK()
	server.WriteAdminLog("重置游客MaxBet", ctx, reqdata)
}

// 游客查询
func (c *GuestController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		Address    string
		IsBinded   int //是否绑定上级, 1 已绑定,2 未绑定
		ChannelId  int
		StartTime  int64
		EndTime    int64
		TopAgentId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "游客查询", "查", "游客查询")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "IsBinded", "=", reqdata.IsBinded, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "TopAgentId", "=", reqdata.TopAgentId, 0)
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	total, data := server.Db().Table("x_guest").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *GuestController) set_gamefee(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChannelId  int    `validate:"required"`
		Address    string `validate:"required"`
		GameFee    string `validate:"required"`
		GoogleCode string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "游客查询", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	isGameFeeValidStr := check_user_game_fee_valid(reqdata.GameFee)
	if ctx.RespErrString(isGameFeeValidStr != "", &errcode, isGameFeeValidStr) {
		return
	}
	_, err = server.Db().Conn().Exec("update x_guest set GameFee = ? where SellerId = ? and Address = ?", reqdata.GameFee, reqdata.SellerId, reqdata.Address)
	if err != nil {
		logs.Error(err)
	}
	_, err = server.Db().Conn().Exec("update x_user set GameFee = ? where SellerId = ? and Address = ?", reqdata.GameFee, reqdata.SellerId, reqdata.Address)
	if err != nil {
		logs.Error(err)
	}
	ctx.RespOK()
	server.WriteAdminLog("设置游客游戏赔率", ctx, reqdata)
}

func (c *GuestController) sync_gamefee(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		ChannelId  int
		Address    string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "游客查询", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	go func() {
		fmt.Println("guest 开始同步")
		var addrmap map[string]int = make(map[string]int)
		var gamefee string
		server.Db().QueryScan("select GameFee from x_guest where SellerId = ? and ChannelId = ? and  Address = ?", []interface{}{reqdata.SellerId, reqdata.ChannelId, reqdata.Address}, &gamefee)
		server.Db().Conn().Query("update x_guest set GameFee = ? where address = ?", gamefee, reqdata.Address)
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "Address", "=", reqdata.Address, 0)
			users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
			for i := 0; i < len(*users); i++ {
				server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", (*users)[i]["UserId"], gamefee)
			}
		}
		start := 0
		for {
			url := fmt.Sprintf("https://apilist.tronscanapi.com/api/transfer?limit=20&sort=timestamp&address=%s&start=%d", reqdata.Address, start)
			getresult, err := req.Get(url)
			if err != nil {
				continue
			}
			var data map[string]interface{} = make(map[string]interface{})
			bytedata, _ := getresult.ToBytes()
			json.Unmarshal(bytedata, &data)
			if data["data"] == nil {
				break
			}
			dataarr := data["data"].([]interface{})
			if len(dataarr) == 0 {
				break
			}
			for i := 0; i < len(dataarr); i++ {
				data = dataarr[i].(map[string]interface{})
				FromAddress := data["transferFromAddress"].(string)
				ToAddress := data["transferToAddress"].(string)
				exists := addrmap[FromAddress]
				if FromAddress != reqdata.Address && exists == 0 {
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, FromAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", FromAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
				exists = addrmap[ToAddress]
				if ToAddress != reqdata.Address && exists == 0 {
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, ToAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", ToAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
			}
			start += 20
		}
		start = 0
		for {
			url := fmt.Sprintf("https://apilist.tronscanapi.com/api/new/token_trc20/transfers?limit=20&start=%d&filterTokenValue=1&relatedAddress=%s", start, reqdata.Address)
			getresult, err := req.Get(url)
			if err != nil {
				continue
			}
			var data map[string]interface{} = make(map[string]interface{})
			bytedata, _ := getresult.ToBytes()
			json.Unmarshal(bytedata, &data)
			if data["token_transfers"] == nil {
				break
			}
			dataarr := data["token_transfers"].([]interface{})
			if len(dataarr) == 0 {
				break
			}
			for i := 0; i < len(dataarr); i++ {
				data = dataarr[i].(map[string]interface{})
				FromAddress := data["from_address"].(string)
				ToAddress := data["to_address"].(string)
				exists := addrmap[ToAddress]
				if FromAddress != reqdata.Address && exists == 0 {
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, FromAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", FromAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
				exists = addrmap[ToAddress]
				if ToAddress != reqdata.Address && exists == 0 {
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, ToAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", ToAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
			}
			start += 20
		}
		fmt.Println("guest 结束同步")
	}()
	ctx.RespOK()
}

func (c *GuestController) set_jptype(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Address  string
		JpType   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	server.Db().Conn().Exec("update x_guest set JpType = ? where SellerId = ? and Address = ?", reqdata.JpType, reqdata.SellerId, reqdata.Address)
	ctx.RespOK()
}

func (c *GuestController) set_audit_amount(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId        int
		Address         string `validate:"required"`
		AuditAmountUsdt float64
		AuditAmountTrx  float64
		GoogleCode      string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "游客查询", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_guest set AuditAmountUsdt = ?,AuditAmountTrx = ? where address = ?", reqdata.AuditAmountUsdt, reqdata.AuditAmountTrx, reqdata.Address)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置游客审核金额", ctx, reqdata)
}

func (c *GuestController) set_blockmaker(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Address    string `validate:"required"`
		BlackMaker string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "游客查询", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_guest set BlackMaker = ? where Address = ?", reqdata.BlackMaker, reqdata.Address)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("游客出块黑名单", ctx, reqdata)
}

func (c *GuestController) ingore_winjiangpei(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId          int
		Address           string `validate:"required"`
		IgnoreWinJiangPei int    //忽略盈利降赔 1是,2否
		GoogleCode        string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "游客查询", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_guest set IgnoreWinJiangPei = ? where SellerId = ? and  Address = ?", reqdata.IgnoreWinJiangPei, reqdata.SellerId, reqdata.Address)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置游客是否参与盈利降赔", ctx, reqdata)
}
