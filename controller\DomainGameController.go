package controller

import (
	"encoding/json"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type DomainGameController struct {
}

type RequestData struct {
	Page     int
	PageSize int
	SellerId int
	Brand    string // pp,pg,gfg,xyx
	GameId   string
	GameName string
	GameType int // 1电子,2棋牌,3小游戏,4彩票
	Sort     struct {
		Field string
		Type  string
	}
	State   int
	HostId  int
	IsHot   int
	IsNew   int
	IsRecom int
}

type ModifyRequestData struct {
	Sort   int
	HostId int
	GameId string
	Brand  string
}

type SyncRequestData struct {
	HostId int
}
type SyncChannelRequestData struct {
	ChannelId int
}

type RestRequestData struct {
	HostId int
	LangId int
}

func (c *DomainGameController) Init() {
	gropu := server.Http().NewGroup("/api/domain/game")
	{
		gropu.PostNoAuth("/list", c.list)
		gropu.PostNoAuth("/modify", c.modify)
		gropu.PostNoAuth("/syncCurr", c.syncCurr)
		gropu.PostNoAuth("/syncAll", c.syncAll)
		gropu.PostNoAuth("/reset", c.reset)
		gropu.PostNoAuth("/empty", c.empty)

	}
}

func (c *DomainGameController) list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	gameListDao := server.DaoxHashGame().XGameList
	gameChannelListDao := server.DaoxHashGame().XChannelGameList
	db := gameListDao.WithContext(nil)
	channelHostDao := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostDao.WithContext(nil)

	channelHost, err := channelHostDb.Where(channelHostDao.ID.Eq(int32(reqdata.HostId))).First()
	if channelHost == nil {
		ctx.RespErrString(true, &errcode, "未找到该域名数据")
		return
	}

	type ResultData struct {
		Id        int
		Brand     string
		GameId    string
		Name      string
		EName     string
		Icon      string
		EIcon     string
		GameType  int
		Sort      int
		IsHot     int
		IsNew     int
		IsRecom   int
		State     int
		RowNum    int
		HostId    int
		ChannelId int
	}

	var result []ResultData

	query := db.Select(
		gameListDao.ID.As("Id"),
		gameListDao.Brand,
		gameListDao.GameID.As("GameId"),
		gameListDao.Name,
		gameListDao.EName,
		gameListDao.Icon,
		gameListDao.EIcon,
		gameListDao.GameType,
		gameListDao.IsHot,
		gameListDao.IsNew,
		gameListDao.IsRecom,
		gameListDao.State,
		gameChannelListDao.Sort,
	)

	if reqdata.GameType != 0 {
		query.Where(gameListDao.GameType.Eq(int32(reqdata.GameType)))
	}

	if reqdata.State != 0 {
		query.Where(gameListDao.State.Eq(int32(reqdata.State)))
	}

	if reqdata.Brand != "" {
		query.Where(gameListDao.Brand.Eq(reqdata.Brand))
	}

	if reqdata.GameId != "" {
		query.Where(gameListDao.GameID.Eq(reqdata.GameId))
	}

	if reqdata.GameName != "" {
		query.Where(db.Where(gameListDao.Name.Like("%" + reqdata.GameName + "%")).Or(gameListDao.EName.Like("%" + reqdata.GameName + "%")))
	}

	if reqdata.IsHot != 0 {
		query.Where(gameListDao.IsHot.Eq(int32(reqdata.IsHot)))
	}

	if reqdata.IsNew != 0 {
		query.Where(gameListDao.IsNew.Eq(int32(reqdata.IsNew)))
	}

	if reqdata.IsRecom != 0 {
		query.Where(gameListDao.IsRecom.Eq(int32(reqdata.IsRecom)))
	}

	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)

	switch reqdata.Sort.Field {
	case "UserCount":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.UserCount.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(gameListDao.UserCount.Desc()).Order(gameListDao.ID.Desc())
		}
		break
	case "BetAmount":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.BetAmount.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(gameListDao.BetAmount.Desc()).Order(gameListDao.ID.Desc())
		}
		break
	case "Rtp":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.Rtp.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(gameListDao.Rtp.Desc()).Order(gameListDao.ID.Desc())
		}
	case "Sort":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameChannelListDao.Sort.Asc()).Order(gameListDao.ID.Desc())
		} else {
			query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.ID.Desc())
		}
	case "Id":
		if reqdata.Sort.Type == "asc" {
			query.Order(gameListDao.ID.Asc())
		} else {
			query.Order(gameListDao.ID.Desc())
		}
	default:
		{
			type SortTypeData struct {
				SortType       int32 `json:"sortType"`
				ArtificialType int32 `json:"artificialType"`
			}
			redisSortTypeData := server.Redis().HGet("CONFIG", "GAME_LIST_SORT_TYPE")
			sortTypeData := &SortTypeData{}
			if redisSortTypeData != nil {
				// redisData 是 []uint8 类型，将其转换为字符串
				dataBytes := redisSortTypeData.([]uint8)
				// 将字节数据转为字符串
				dataStr := string(dataBytes)
				json.Unmarshal([]byte(dataStr), &sortTypeData)
			}

			switch sortTypeData.SortType {
			case 1:
				if sortTypeData.ArtificialType == 1 {
					query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.UserCount.Desc()).Order(gameListDao.ID.Desc())
				} else {
					//query.OrderBy("Sort desc, UserCount desc")
					query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.UserCount.Desc()).Order(gameListDao.ID.Desc())
				}
			case 2:
				if sortTypeData.ArtificialType == 1 {
					query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.BetAmount.Desc()).Order(gameListDao.ID.Desc())
				} else {
					query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.BetAmount.Desc()).Order(gameListDao.ID.Desc())
				}
			case 3:
				if sortTypeData.ArtificialType == 1 {
					query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.Rtp.Desc()).Order(gameListDao.ID.Desc())
				} else {
					query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.Rtp.Desc()).Order(gameListDao.ID.Desc())
				}
			default:
				query.Order(gameChannelListDao.Sort.Desc()).Order(gameListDao.Sort.Desc()).Order(gameListDao.ID.Desc())
			}
		}
	}

	count, err := query.LeftJoin(gameChannelListDao, gameChannelListDao.HostID.Eq(int32(reqdata.HostId)), gameListDao.GameID.EqCol(gameChannelListDao.GameID), gameListDao.Brand.EqCol(gameChannelListDao.Brand)).ScanByPage(&result, offset, limit)

	if result == nil {
		result = []ResultData{}
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	for i, _ := range result {
		result[i].HostId = reqdata.HostId
		result[i].ChannelId = int(channelHost.ChannelID)
		result[i].RowNum = (reqdata.Page-1)*reqdata.PageSize + i + 1
	}

	ctx.Put("url", server.ImageUrl())
	ctx.Put("data", result)
	ctx.Put("total", count)
	ctx.RespOK()

}

func (c *DomainGameController) modify(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := ModifyRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	gameChannelListDao := server.DaoxHashGame().XChannelGameList
	db := gameChannelListDao.WithContext(nil)

	channelHostDao := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostDao.WithContext(nil)

	// 获取host
	hostInfos, err := channelHostDb.Where(channelHostDao.ID.Eq(int32(reqdata.HostId))).First()
	if hostInfos == nil {
		ctx.RespErrString(true, &errcode, "未找到该渠道域名")
		return
	}

	// 判断列表中是否存在该游戏
	game, err := db.Where(gameChannelListDao.HostID.Eq(int32(reqdata.HostId)), gameChannelListDao.GameID.Eq(reqdata.GameId), gameChannelListDao.Brand.Eq(reqdata.Brand)).First()
	if game != nil {
		db.Where(gameChannelListDao.HostID.Eq(int32(reqdata.HostId)), gameChannelListDao.GameID.Eq(reqdata.GameId), gameChannelListDao.Brand.Eq(reqdata.Brand)).Update(gameChannelListDao.Sort, reqdata.Sort)
	} else {
		// 新增一条记录到 game_channel_game_list 表中
		gameChannelListData := &model.XChannelGameList{
			HostID:    int32(reqdata.HostId),
			GameID:    reqdata.GameId,
			Sort:      int32(reqdata.Sort),
			Brand:     reqdata.Brand,
			ChannelID: hostInfos.ChannelID,
			Host:      hostInfos.Host,
		}

		err = db.Create(gameChannelListData)
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
	}

	ctx.RespOK()
}

// 同步当前渠道所有域名的游戏列表
func (c *DomainGameController) syncCurr(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := SyncRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 获取当前域名id所在渠道
	channelHostDao := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostDao.WithContext(nil)
	channelHost, err := channelHostDb.Where(channelHostDao.ID.Eq(int32(reqdata.HostId))).First()
	if channelHost == nil {
		ctx.RespErrString(true, &errcode, "未找到该域名数据")
		return
	}

	// 获取渠道为chanelHost.ChannelId的所有域名
	var hostIds []int32
	err = channelHostDb.Where(channelHostDao.ChannelID.Eq(channelHost.ChannelID)).Where(channelHostDao.ID.Neq(int32(reqdata.HostId))).Pluck(channelHostDao.ID, &hostIds)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	if len(hostIds) == 0 {
		ctx.RespOK()
		return
	}

	// 先删除已存在的数据
	channelGameListDao := server.DaoxHashGame().XChannelGameList
	channelGameListDb := channelGameListDao.WithContext(nil)
	_, err = channelGameListDb.Where(channelGameListDao.HostID.In(hostIds...)).Delete()

	query := `
	INSERT INTO x_channel_game_list (HostId, Brand, ChannelId, GameId, Host, Sort)
	SELECT ?, B.Brand, B.ChannelId, B.GameId, B.Host, B.Sort
	FROM x_channel_game_list AS B
	WHERE B.HostId = ?
`

	for i := 1; i < len(hostIds); i++ {
		query += " UNION ALL SELECT ?, B.Brand, B.ChannelId, B.GameId, B.Host, B.Sort FROM x_channel_game_list AS B WHERE B.HostId = ?"
	}

	values := make([]interface{}, 0, len(hostIds)*2)
	for _, hostId := range hostIds {
		values = append(values, hostId, reqdata.HostId)
	}

	err = server.Db().Gorm().Exec(query, values...).Error

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

// 同步当前渠道所有域名的游戏列表
func (c *DomainGameController) syncAll(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := SyncRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 获取所有hostid
	channelHostDao := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostDao.WithContext(nil)
	var hostIds []int32
	err = channelHostDb.Where(channelHostDao.ID.Neq(int32(reqdata.HostId))).Pluck(channelHostDao.ID, &hostIds)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 先删除已存在的数据
	channelGameListDao := server.DaoxHashGame().XChannelGameList
	channelGameListDb := channelGameListDao.WithContext(nil)
	_, err = channelGameListDb.Where(channelGameListDao.HostID.Neq(int32(reqdata.HostId))).Delete()

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 一次性获取所有的 channelHostInfos
	channelHostInfos, err := channelHostDb.Where(channelHostDao.ID.In(hostIds...)).Select(channelHostDao.ID, channelHostDao.ChannelID, channelHostDao.Host).Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 动态构建 SQL 查询
	query := `
	INSERT INTO x_channel_game_list (HostId, Brand, ChannelId, GameId, Host, Sort)
	SELECT ?, B.Brand, ? AS ChannelId, B.GameId, ? AS Host, B.Sort
	FROM x_channel_game_list AS B
	WHERE B.HostId = ?
	`

	// 构建 UNION ALL 部分
	values := make([]interface{}, 0, len(channelHostInfos)*4)
	for i, info := range channelHostInfos {
		if i > 0 {
			query += " UNION ALL SELECT ?, B.Brand, ? AS ChannelId, B.GameId, ? AS Host, B.Sort FROM x_channel_game_list AS B WHERE B.HostId = ?"
		}
		values = append(values, info.ID, info.ChannelID, info.Host, reqdata.HostId)
	}

	// 执行批量插入操作
	err = server.Db().Gorm().Exec(query, values...).Error

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *DomainGameController) reset(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := RestRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	dao := server.DaoxHashGame().XChannelGameList
	db := dao.WithContext(nil)

	_, err = db.Where(dao.HostID.Eq(int32(reqdata.HostId))).Delete()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
	}

	channelHostDao := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostDao.WithContext(nil)
	hostInfos, err := channelHostDb.Where(channelHostDao.ID.Eq(int32(reqdata.HostId))).First()
	if hostInfos != nil {
		server.Db().Gorm().Exec(`INSERT INTO x_channel_game_list(HostId, Brand, ChannelId, GameId, Host,Sort)
				SELECT ? AS HostId, B.Brand,? AS ChannelId, B.GameId, ? AS Host, B.Sort 
				FROM x_lang_game_list AS B
				WHERE B.LangId = ?
				`, hostInfos.ID, hostInfos.ChannelID, hostInfos.Host, reqdata.LangId)
	} else {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *DomainGameController) empty(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := RestRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	dao := server.DaoxHashGame().XChannelGameList
	db := dao.WithContext(nil)

	_, err = db.Where(dao.HostID.Eq(int32(reqdata.HostId))).Delete()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()
}
