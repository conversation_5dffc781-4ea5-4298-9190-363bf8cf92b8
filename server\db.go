package server

import "fmt"

type DBSeller struct {
	SellerId   int    `gorm:"column:SellerId"`   //运营商
	SellerName string `gorm:"column:SellerName"` //运营名称
	State      int    `gorm:"column:State"`      //状态 1启用 2禁用
	Remark     string `gorm:"column:Remark"`     //备注
	CreateTime string `gorm:"column:CreateTime"` //创建时间
}

func (*DBSeller) TableName() string {
	return "x_seller"
}

func DbSeller_Page_Data(Page int, PageSize int) (int, []DBSeller) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "SellerId asc"
	PageKey := "SellerId"
	data := DBSeller{}
	dbtable := Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	Db().AddWhere(&sql, &params, "and", "State", "=", 1, 0)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []DBSeller{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []DBSeller{}
	dbtable.Where(fmt.Sprintf("%s >= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}

type DBAdminLoginLog struct {
	Id         int    `gorm:"column:Id"`         //
	UserId     int    `gorm:"column:UserId"`     //管理员id
	SellerId   int    `gorm:"column:SellerId"`   //运营商
	Account    string `gorm:"column:Account"`    //管理员账号
	Token      string `gorm:"column:Token"`      //当次登录token
	LoginIp    string `gorm:"column:LoginIp"`    //登录ip
	CreateTime string `gorm:"column:CreateTime"` //登录时间
}

func (*DBAdminLoginLog) TableName() string {
	return "admin_login_log"
}

func DBAdminLoginLog_Page_Data(Page int, PageSize int, SellerId int, Account string) (int, []DBAdminLoginLog) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := DBAdminLoginLog{}
	dbtable := Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	Db().AddWhere(&sql, &params, "and", "Account", "=", Account, "")
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []DBAdminLoginLog{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []DBAdminLoginLog{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}

type DBRole struct {
	Id         int    `gorm:"column:Id"`         //
	RoleName   string `gorm:"column:RoleName"`   //
	SellerId   int    `gorm:"column:SellerId"`   //
	Parent     string `gorm:"column:Parent"`     //上级角色
	RoleData   string `gorm:"column:RoleData"`   //角色数据
	CreateTime string `gorm:"column:CreateTime"` //创建时间
}

func (*DBRole) TableName() string {
	return "admin_role"
}

func DBRole_Page_Data(Page int, PageSize int, SellerId int) (int, []DBRole) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := DBRole{}
	dbtable := Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []DBRole{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []DBRole{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}

type DbAdminLog struct {
	Id         int    `gorm:"column:Id"`         //
	Account    string `gorm:"column:Account"`    //操作账号
	SellerId   int    `gorm:"column:SellerId"`   //账号所属运营商
	Opt        string `gorm:"column:Opt"`        //操作类型
	Ip         string `gorm:"column:Ip"`         //操作ip
	Token      string `gorm:"column:Token"`      //请求token
	Data       string `gorm:"column:Data"`       //请求数据
	CreateTime string `gorm:"column:CreateTime"` //创建时间
}

func (*DbAdminLog) TableName() string {
	return "admin_opt_log"
}

func DBAdminLog_Page_Data(Page int, PageSize int, SellerId int, Account string, Opt string) (int, []DbAdminLog) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := DbAdminLog{}
	dbtable := Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	Db().AddWhere(&sql, &params, "and", "Account", "=", Account, "")
	Db().AddWhere(&sql, &params, "and", "Opt", "=", Opt, "")
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []DbAdminLog{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []DbAdminLog{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}

type DbAdminUser struct {
	Id      int    `gorm:"column:Id"`      //
	Account string `gorm:"column:Account"` //账号
	//Password string `gorm:"column:Password"` //密码
	SellerId int    `gorm:"column:SellerId"` //运营商
	RoleName string `gorm:"column:RoleName"` //角色名
	State    int    `gorm:"column:State"`    //状态 1启用 2禁用
	//Token    string `gorm:"column:Token"`    //token
	//GoogleSecret string `gorm:"column:GoogleSecret"` //谷歌验证码
	Remark      string `gorm:"column:Remark"`     //备注
	LoginCount  int    `gorm:"column:LoginCount"` //登录次数
	LoginTime   string `gorm:"column:LoginTime"`  //最后登录时间
	LoginIp     string `gorm:"column:LoginIp"`    //最后登录Ip
	CreateTime  string `gorm:"column:CreateTime"` //
	IpWhite     string `gorm:"column:IpWhite"`    //
	RecvNotice  int    `gorm:"column:RecvNotice"`
	ChannelId   int    `gorm:"column:ChannelId"`
	MaxAddMoney int    `gorm:"column:MaxAddMoney"`
	CSGroup     string `gorm:"column:CSGroup"`
	CSId        string `gorm:"column:CSId"`
	RoleType    int    `gorm:"column:RoleType"`
	IsIpWhite   int    `gorm:"column:IsIpWhite"`
}

func (*DbAdminUser) TableName() string {
	return "admin_user"
}

func DBAdminUser_Page_Data(Page int, PageSize int, SellerId int, Account string, ChannelId int, AccountType int, CSGroup string, CSId string) (int, []DbAdminUser) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := DbAdminUser{}
	dbtable := Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	Db().AddWhere(&sql, &params, "and", "Account", "=", Account, "")
	Db().AddWhere(&sql, &params, "and", "ChannelId", "=", ChannelId, 0)
	Db().AddWhere(&sql, &params, "and", "CSGroup", "=", CSGroup, "")
	Db().AddWhere(&sql, &params, "and", "CSId", "=", CSId, "")
	if AccountType == 1 {
		Db().AddWhere(&sql, &params, "and", "CSGroup", "=", "", nil)
	}
	if AccountType == 2 {
		Db().AddWhere(&sql, &params, "and", "CSGroup", "<>", "", nil)
	}
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []DbAdminUser{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []DbAdminUser{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}

type DbConfig struct {
	Id          int    `gorm:"column:Id"`          //
	SellerId    int    `gorm:"column:SellerId"`    //运营商
	ChannelId   int    `gorm:"column:ChannelId"`   //运营商
	ConfigName  string `gorm:"column:ConfigName"`  //配置名称
	ConfigValue string `gorm:"column:ConfigValue"` //配置值
	Remark      string `gorm:"column:Remark"`      //注释
	EditAble    int    `gorm:"column:EditAble"`
}

func (*DbConfig) TableName() string {
	return fmt.Sprintf("%sconfig", dbprefix)
}

func DBConfig_Page_Data(Page int, PageSize int, SellerId int, ConfigName string, Remark string, ChannelId int) (int, []DbConfig) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id asc"
	PageKey := "Id"
	data := DbConfig{}
	dbtable := Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	Db().AddWhere(&sql, &params, "and", "ChannelId", "=", ChannelId, 0)
	Db().AddWhere(&sql, &params, "and", "ConfigName", "=", ConfigName, "")
	Db().AddWhere(&sql, &params, "and", "IsShow", "=", 1, 0)
	Db().AddWhere(&sql, &params, "and", "Remark", "like", fmt.Sprintf("%%%s%%", Remark), "")
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []DbConfig{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []DbConfig{}
	dbtable.Where(fmt.Sprintf("%s >= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}
