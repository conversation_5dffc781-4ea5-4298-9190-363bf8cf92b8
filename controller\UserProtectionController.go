package controller

import (
	"fmt"
	"xserver/abugo"
	"xserver/controller/userbrand"
	"xserver/server"
)

type UserProtectionController struct{}

func (c *UserProtectionController) Init() {
	group := server.Http().NewGroup("/api/user_protection_setting")
	group.Post("/get", c.get)
	group.Post("/save", c.save)
	group.Post("/check_entry", c.CheckGameEntry)
	group.Post("/options", c.getOptions)         // 获取配置选项
	group.Post("/global_toggle", c.globalToggle) // 全局开关控制
}

func (c *UserProtectionController) get(ctx *abugo.AbuHttpContent) {
	db := server.Db().GormDao()
	setting, err := userbrand.UserProtectionSvc.GetUserProtectionSetting(db)
	if err != nil {
		ctx.RespErrString(true, nil, "获取配置失败")
		return
	}
	ctx.RespOK(setting)
}

func (c *UserProtectionController) save(ctx *abugo.AbuHttpContent) {
	db := server.Db().GormDao()
	var setting userbrand.UserProtectionSetting
	err := ctx.RequestData(&setting)
	if err != nil {
		ctx.RespErrString(true, nil, "参数错误")
		return
	}
	err = userbrand.UserProtectionSvc.SaveUserProtectionSetting(db, &setting)
	if err != nil {
		ctx.RespErrString(true, nil, "保存失败")
		return
	}
	ctx.RespOK()
}

func (c *UserProtectionController) CheckGameEntry(ctx *abugo.AbuHttpContent) {
	type Request struct {
		UserId int    `json:"UserId" validate:"required"`
		Brand  string `json:"Brand" validate:"required"`
	}
	var req Request
	errcode := 0
	err := ctx.RequestData(&req)
	if ctx.RespErr(err, &errcode) {
		return
	}
	db := server.Db().GormDao()

	allowed, hint, err := userbrand.UserProtectionSvc.CheckGameEntry(db, req.UserId, req.Brand, true)
	if ctx.RespErr(err, &errcode) {
		return
	}

	if !allowed {
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	ctx.RespOK()
}

func (c *UserProtectionController) getOptions(ctx *abugo.AbuHttpContent) {
	// 游戏品牌选项
	gameBrands := []map[string]interface{}{
		{"value": "gfg", "label": "GFG"},
		{"value": "omg", "label": "OMG"},
		{"value": "heibao", "label": "HeiBao"},
	}

	// 保护对象类型选项
	protectObjects := []map[string]interface{}{
		{"value": "only_tiyanjing", "label": "仅领体验金", "enabled": true},
		{"value": "only_first_recharge", "label": "仅首充", "enabled": false},
		{"value": "tiyanjing_and_recharge", "label": "领体验金且充值", "enabled": false},
		{"value": "recharge_less_10u", "label": "充值小于10U", "enabled": false},
	}

	// 比较操作符选项
	compareOperators := []map[string]interface{}{
		{"value": "=", "label": "等于(=)"},
		{"value": "<", "label": "小于(<)"},
		{"value": "<=", "label": "小于等于(≤)"},
		{"value": ">", "label": "大于(>)"},
		{"value": ">=", "label": "大于等于(≥)"},
	}

	response := map[string]interface{}{
		"gameBrands":       gameBrands,
		"protectObjects":   protectObjects,
		"compareOperators": compareOperators,
	}

	ctx.RespOK(response)
}

// globalToggle 全局开关控制 - 当总开关关闭时同步到所有域名
func (c *UserProtectionController) globalToggle(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Enabled    bool   `json:"enabled" validate:"required"`    // 用户保护总开关：true=启用，false=禁用
		GoogleCode string `json:"googleCode" validate:"required"` // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "新手保护", "改", "全局用户保护开关控制")
	if token == nil {
		return
	}

	// 验证谷歌验证码
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	db := server.Db().GormDao()

	// 1. 更新配置中的全局开关状态
	setting, err := userbrand.UserProtectionSvc.GetUserProtectionSetting(db)
	if err != nil {
		ctx.RespErrString(true, nil, "获取配置失败")
		return
	}

	setting.Enabled = reqdata.Enabled

	err = userbrand.UserProtectionSvc.SaveUserProtectionSetting(db, setting)
	if err != nil {
		ctx.RespErrString(true, nil, "保存配置失败")
		return
	}

	// 2. 如果是关闭操作，同步关闭所有域名的用户保护
	if !reqdata.Enabled {
		xChannelHost := server.DaoxHashGame().XChannelHost
		info, err := xChannelHost.WithContext(ctx.Gin()).
			Update(xChannelHost.UserProtect, int32(2)) // 2表示关闭

		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		// 记录操作日志
		logMessage := fmt.Sprintf("全局关闭用户保护功能，同步关闭所有域名的用户保护，影响%d个域名", info.RowsAffected)
		server.WriteAdminLog(logMessage, ctx, reqdata)

		ctx.Put("message", fmt.Sprintf("全局用户保护已关闭，同步关闭了%d个域名的用户保护功能", info.RowsAffected))
		ctx.Put("RowsAffected", info.RowsAffected)
	} else {
		// 开启操作只更新配置，不影响域名设置，允许差异化配置
		logMessage := "全局开启用户保护功能，各域名可进行差异化配置"
		server.WriteAdminLog(logMessage, ctx, reqdata)

		ctx.Put("message", "全局用户保护已开启，各运营商、渠道和域名可进行差异化配置")
		ctx.Put("RowsAffected", 0)
	}

	ctx.RespOK()
}
