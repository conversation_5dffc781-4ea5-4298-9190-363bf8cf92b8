// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXOperationsInputDataDate = "x_operations_input_data_date"

// XOperationsInputDataDate 运营报表平台汇总手动录入数据(按日期)
type XOperationsInputDataDate struct {
	RecordDate      time.Time `gorm:"column:record_date;primaryKey;comment:日期" json:"record_date"`                           // 日期
	SellerID        int32     `gorm:"column:seller_id;primaryKey;comment:运营商ID" json:"seller_id"`                            // 运营商ID
	SubOtherAmount  float64   `gorm:"column:sub_other_amount;default:0.000000;comment:其它扣除金额" json:"sub_other_amount"`       // 其它扣除金额
	ResourcesAmount float64   `gorm:"column:resources_amount;default:0.000000;comment:资源部" json:"resources_amount"`          // 资源部
	MarketAmount    float64   `gorm:"column:market_amount;default:0.000000;comment:市场部" json:"market_amount"`                // 市场部
	AdAmount        float64   `gorm:"column:ad_amount;default:0.000000;comment:广告部" json:"ad_amount"`                        // 广告部
	OpAmount        float64   `gorm:"column:op_amount;default:0.000000;comment:运营部" json:"op_amount"`                        // 运营部
	AgentAmount     float64   `gorm:"column:agent_amount;default:0.000000;comment:代理分红收支" json:"agent_amount"`               // 代理分红收支
	CreateTime      time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime      time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XOperationsInputDataDate's table name
func (*XOperationsInputDataDate) TableName() string {
	return TableNameXOperationsInputDataDate
}
