// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsActiveDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsActiveDailyStat {
	_xAdsActiveDailyStat := xAdsActiveDailyStat{}

	_xAdsActiveDailyStat.xAdsActiveDailyStatDo.UseDB(db, opts...)
	_xAdsActiveDailyStat.xAdsActiveDailyStatDo.UseModel(&model.XAdsActiveDailyStat{})

	tableName := _xAdsActiveDailyStat.xAdsActiveDailyStatDo.TableName()
	_xAdsActiveDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsActiveDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsActiveDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsActiveDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsActiveDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsActiveDailyStat.ActiveName = field.NewString(tableName, "active_name")
	_xAdsActiveDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsActiveDailyStat.ClickCountPc = field.NewInt32(tableName, "click_count_pc")
	_xAdsActiveDailyStat.ClickCountH5 = field.NewInt32(tableName, "click_count_h5")
	_xAdsActiveDailyStat.JoinCountPc = field.NewInt32(tableName, "join_count_pc")
	_xAdsActiveDailyStat.JoinCountH5 = field.NewInt32(tableName, "join_count_h5")
	_xAdsActiveDailyStat.CompletionCountPc = field.NewInt32(tableName, "completion_count_pc")
	_xAdsActiveDailyStat.CompletionCountH5 = field.NewInt32(tableName, "completion_count_h5")
	_xAdsActiveDailyStat.ReceivedCountPc = field.NewInt32(tableName, "received_count_pc")
	_xAdsActiveDailyStat.ReceivedCountH5 = field.NewInt32(tableName, "received_count_h5")
	_xAdsActiveDailyStat.ClickCtrPc = field.NewFloat32(tableName, "click_ctr_pc")
	_xAdsActiveDailyStat.ClickCtrH5 = field.NewFloat32(tableName, "click_ctr_h5")
	_xAdsActiveDailyStat.ActiveTarPc = field.NewFloat32(tableName, "active_tar_pc")
	_xAdsActiveDailyStat.ActiveTarH5 = field.NewFloat32(tableName, "active_tar_h5")
	_xAdsActiveDailyStat.TotalPayoutAmountUPc = field.NewFloat32(tableName, "total_payout_amountU_pc")
	_xAdsActiveDailyStat.TotalPayoutAmountUH5 = field.NewFloat32(tableName, "total_payout_amountU_h5")
	_xAdsActiveDailyStat.TotalPayoutAmountTPc = field.NewFloat32(tableName, "total_payout_amountT_pc")
	_xAdsActiveDailyStat.TotalPayoutAmountTH5 = field.NewFloat32(tableName, "total_payout_amountT_h5")
	_xAdsActiveDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsActiveDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsActiveDailyStat.fillFieldMap()

	return _xAdsActiveDailyStat
}

// xAdsActiveDailyStat 活动偏好每日统计表
type xAdsActiveDailyStat struct {
	xAdsActiveDailyStatDo xAdsActiveDailyStatDo

	ALL                  field.Asterisk
	ID                   field.Int64   // 主键ID
	SellerID             field.Int32   // 运营商ID
	ChannelID            field.Int32   // 渠道ID
	TopAgentID           field.Int64   // 顶级代理ID
	ActiveName           field.String  // 活动标签/ 名称
	StatDate             field.Time    // 统计日期
	ClickCountPc         field.Int32   // 点击次数pc
	ClickCountH5         field.Int32   // 点击次数h5
	JoinCountPc          field.Int32   // 参入人数pc
	JoinCountH5          field.Int32   // 参入人数h5
	CompletionCountPc    field.Int32   // 完成任务人数pc
	CompletionCountH5    field.Int32   // 完成任务人数h5
	ReceivedCountPc      field.Int32   // 领取奖励人数人数pc
	ReceivedCountH5      field.Int32   // 领取奖励人数人数h5
	ClickCtrPc           field.Float32 // 点击转化率pc
	ClickCtrH5           field.Float32 // 点击转化率h5
	ActiveTarPc          field.Float32 // 任务完成率pc
	ActiveTarH5          field.Float32 // 任务完成率h5
	TotalPayoutAmountUPc field.Float32 // 总派彩U pc
	TotalPayoutAmountUH5 field.Float32 // 总派彩U h5
	TotalPayoutAmountTPc field.Float32 // 总派彩T pc
	TotalPayoutAmountTH5 field.Float32 // 总派彩T h5
	CreateTime           field.Time    // 创建时间
	UpdateTime           field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsActiveDailyStat) Table(newTableName string) *xAdsActiveDailyStat {
	x.xAdsActiveDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsActiveDailyStat) As(alias string) *xAdsActiveDailyStat {
	x.xAdsActiveDailyStatDo.DO = *(x.xAdsActiveDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsActiveDailyStat) updateTableName(table string) *xAdsActiveDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.ActiveName = field.NewString(table, "active_name")
	x.StatDate = field.NewTime(table, "stat_date")
	x.ClickCountPc = field.NewInt32(table, "click_count_pc")
	x.ClickCountH5 = field.NewInt32(table, "click_count_h5")
	x.JoinCountPc = field.NewInt32(table, "join_count_pc")
	x.JoinCountH5 = field.NewInt32(table, "join_count_h5")
	x.CompletionCountPc = field.NewInt32(table, "completion_count_pc")
	x.CompletionCountH5 = field.NewInt32(table, "completion_count_h5")
	x.ReceivedCountPc = field.NewInt32(table, "received_count_pc")
	x.ReceivedCountH5 = field.NewInt32(table, "received_count_h5")
	x.ClickCtrPc = field.NewFloat32(table, "click_ctr_pc")
	x.ClickCtrH5 = field.NewFloat32(table, "click_ctr_h5")
	x.ActiveTarPc = field.NewFloat32(table, "active_tar_pc")
	x.ActiveTarH5 = field.NewFloat32(table, "active_tar_h5")
	x.TotalPayoutAmountUPc = field.NewFloat32(table, "total_payout_amountU_pc")
	x.TotalPayoutAmountUH5 = field.NewFloat32(table, "total_payout_amountU_h5")
	x.TotalPayoutAmountTPc = field.NewFloat32(table, "total_payout_amountT_pc")
	x.TotalPayoutAmountTH5 = field.NewFloat32(table, "total_payout_amountT_h5")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsActiveDailyStat) WithContext(ctx context.Context) *xAdsActiveDailyStatDo {
	return x.xAdsActiveDailyStatDo.WithContext(ctx)
}

func (x xAdsActiveDailyStat) TableName() string { return x.xAdsActiveDailyStatDo.TableName() }

func (x xAdsActiveDailyStat) Alias() string { return x.xAdsActiveDailyStatDo.Alias() }

func (x xAdsActiveDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsActiveDailyStatDo.Columns(cols...)
}

func (x *xAdsActiveDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsActiveDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 24)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["active_name"] = x.ActiveName
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["click_count_pc"] = x.ClickCountPc
	x.fieldMap["click_count_h5"] = x.ClickCountH5
	x.fieldMap["join_count_pc"] = x.JoinCountPc
	x.fieldMap["join_count_h5"] = x.JoinCountH5
	x.fieldMap["completion_count_pc"] = x.CompletionCountPc
	x.fieldMap["completion_count_h5"] = x.CompletionCountH5
	x.fieldMap["received_count_pc"] = x.ReceivedCountPc
	x.fieldMap["received_count_h5"] = x.ReceivedCountH5
	x.fieldMap["click_ctr_pc"] = x.ClickCtrPc
	x.fieldMap["click_ctr_h5"] = x.ClickCtrH5
	x.fieldMap["active_tar_pc"] = x.ActiveTarPc
	x.fieldMap["active_tar_h5"] = x.ActiveTarH5
	x.fieldMap["total_payout_amountU_pc"] = x.TotalPayoutAmountUPc
	x.fieldMap["total_payout_amountU_h5"] = x.TotalPayoutAmountUH5
	x.fieldMap["total_payout_amountT_pc"] = x.TotalPayoutAmountTPc
	x.fieldMap["total_payout_amountT_h5"] = x.TotalPayoutAmountTH5
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsActiveDailyStat) clone(db *gorm.DB) xAdsActiveDailyStat {
	x.xAdsActiveDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsActiveDailyStat) replaceDB(db *gorm.DB) xAdsActiveDailyStat {
	x.xAdsActiveDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsActiveDailyStatDo struct{ gen.DO }

func (x xAdsActiveDailyStatDo) Debug() *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsActiveDailyStatDo) WithContext(ctx context.Context) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsActiveDailyStatDo) ReadDB() *xAdsActiveDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsActiveDailyStatDo) WriteDB() *xAdsActiveDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsActiveDailyStatDo) Session(config *gorm.Session) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsActiveDailyStatDo) Clauses(conds ...clause.Expression) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsActiveDailyStatDo) Returning(value interface{}, columns ...string) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsActiveDailyStatDo) Not(conds ...gen.Condition) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsActiveDailyStatDo) Or(conds ...gen.Condition) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsActiveDailyStatDo) Select(conds ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsActiveDailyStatDo) Where(conds ...gen.Condition) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsActiveDailyStatDo) Order(conds ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsActiveDailyStatDo) Distinct(cols ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsActiveDailyStatDo) Omit(cols ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsActiveDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsActiveDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsActiveDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsActiveDailyStatDo) Group(cols ...field.Expr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsActiveDailyStatDo) Having(conds ...gen.Condition) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsActiveDailyStatDo) Limit(limit int) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsActiveDailyStatDo) Offset(offset int) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsActiveDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsActiveDailyStatDo) Unscoped() *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsActiveDailyStatDo) Create(values ...*model.XAdsActiveDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsActiveDailyStatDo) CreateInBatches(values []*model.XAdsActiveDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsActiveDailyStatDo) Save(values ...*model.XAdsActiveDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsActiveDailyStatDo) First() (*model.XAdsActiveDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsActiveDailyStat), nil
	}
}

func (x xAdsActiveDailyStatDo) Take() (*model.XAdsActiveDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsActiveDailyStat), nil
	}
}

func (x xAdsActiveDailyStatDo) Last() (*model.XAdsActiveDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsActiveDailyStat), nil
	}
}

func (x xAdsActiveDailyStatDo) Find() ([]*model.XAdsActiveDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsActiveDailyStat), err
}

func (x xAdsActiveDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsActiveDailyStat, err error) {
	buf := make([]*model.XAdsActiveDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsActiveDailyStatDo) FindInBatches(result *[]*model.XAdsActiveDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsActiveDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsActiveDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsActiveDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsActiveDailyStatDo) Joins(fields ...field.RelationField) *xAdsActiveDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsActiveDailyStatDo) Preload(fields ...field.RelationField) *xAdsActiveDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsActiveDailyStatDo) FirstOrInit() (*model.XAdsActiveDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsActiveDailyStat), nil
	}
}

func (x xAdsActiveDailyStatDo) FirstOrCreate() (*model.XAdsActiveDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsActiveDailyStat), nil
	}
}

func (x xAdsActiveDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsActiveDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsActiveDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsActiveDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsActiveDailyStatDo) Delete(models ...*model.XAdsActiveDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsActiveDailyStatDo) withDO(do gen.Dao) *xAdsActiveDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
