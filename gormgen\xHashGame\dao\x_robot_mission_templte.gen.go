// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotMissionTemplte(db *gorm.DB, opts ...gen.DOOption) xRobotMissionTemplte {
	_xRobotMissionTemplte := xRobotMissionTemplte{}

	_xRobotMissionTemplte.xRobotMissionTemplteDo.UseDB(db, opts...)
	_xRobotMissionTemplte.xRobotMissionTemplteDo.UseModel(&model.XRobotMissionTemplte{})

	tableName := _xRobotMissionTemplte.xRobotMissionTemplteDo.TableName()
	_xRobotMissionTemplte.ALL = field.NewAsterisk(tableName)
	_xRobotMissionTemplte.ID = field.NewInt64(tableName, "id")
	_xRobotMissionTemplte.Name = field.NewString(tableName, "name")
	_xRobotMissionTemplte.FullName = field.NewString(tableName, "full_name")
	_xRobotMissionTemplte.Types = field.NewInt32(tableName, "types")
	_xRobotMissionTemplte.UserTypes = field.NewInt32(tableName, "user_types")
	_xRobotMissionTemplte.DataText = field.NewString(tableName, "data_text")
	_xRobotMissionTemplte.Remark = field.NewString(tableName, "remark")
	_xRobotMissionTemplte.IsTemplate = field.NewInt32(tableName, "is_template")
	_xRobotMissionTemplte.IsDel = field.NewInt32(tableName, "is_del")
	_xRobotMissionTemplte.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotMissionTemplte.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotMissionTemplte.fillFieldMap()

	return _xRobotMissionTemplte
}

// xRobotMissionTemplte 活动任务模板配置
type xRobotMissionTemplte struct {
	xRobotMissionTemplteDo xRobotMissionTemplteDo

	ALL        field.Asterisk
	ID         field.Int64  // pk
	Name       field.String // 模板名称
	FullName   field.String // 全部名称
	Types      field.Int32  // 模板类型：0：未知，1：邀请任务，2：充值任务，3：流水任务，4：对局任务
	UserTypes  field.Int32  // 推送用户类型：0： 全部 1：在库，2：不在库，3；领U用户，4领Trx用户
	DataText   field.String // Json文本配置信息
	Remark     field.String // 备注
	IsTemplate field.Int32  // 是否为模板0:不是 1是
	IsDel      field.Int32  // 是否删除
	CreateTime field.Time
	UpdateTime field.Time

	fieldMap map[string]field.Expr
}

func (x xRobotMissionTemplte) Table(newTableName string) *xRobotMissionTemplte {
	x.xRobotMissionTemplteDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotMissionTemplte) As(alias string) *xRobotMissionTemplte {
	x.xRobotMissionTemplteDo.DO = *(x.xRobotMissionTemplteDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotMissionTemplte) updateTableName(table string) *xRobotMissionTemplte {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.Name = field.NewString(table, "name")
	x.FullName = field.NewString(table, "full_name")
	x.Types = field.NewInt32(table, "types")
	x.UserTypes = field.NewInt32(table, "user_types")
	x.DataText = field.NewString(table, "data_text")
	x.Remark = field.NewString(table, "remark")
	x.IsTemplate = field.NewInt32(table, "is_template")
	x.IsDel = field.NewInt32(table, "is_del")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotMissionTemplte) WithContext(ctx context.Context) *xRobotMissionTemplteDo {
	return x.xRobotMissionTemplteDo.WithContext(ctx)
}

func (x xRobotMissionTemplte) TableName() string { return x.xRobotMissionTemplteDo.TableName() }

func (x xRobotMissionTemplte) Alias() string { return x.xRobotMissionTemplteDo.Alias() }

func (x xRobotMissionTemplte) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotMissionTemplteDo.Columns(cols...)
}

func (x *xRobotMissionTemplte) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotMissionTemplte) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["id"] = x.ID
	x.fieldMap["name"] = x.Name
	x.fieldMap["full_name"] = x.FullName
	x.fieldMap["types"] = x.Types
	x.fieldMap["user_types"] = x.UserTypes
	x.fieldMap["data_text"] = x.DataText
	x.fieldMap["remark"] = x.Remark
	x.fieldMap["is_template"] = x.IsTemplate
	x.fieldMap["is_del"] = x.IsDel
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotMissionTemplte) clone(db *gorm.DB) xRobotMissionTemplte {
	x.xRobotMissionTemplteDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotMissionTemplte) replaceDB(db *gorm.DB) xRobotMissionTemplte {
	x.xRobotMissionTemplteDo.ReplaceDB(db)
	return x
}

type xRobotMissionTemplteDo struct{ gen.DO }

func (x xRobotMissionTemplteDo) Debug() *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotMissionTemplteDo) WithContext(ctx context.Context) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotMissionTemplteDo) ReadDB() *xRobotMissionTemplteDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotMissionTemplteDo) WriteDB() *xRobotMissionTemplteDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotMissionTemplteDo) Session(config *gorm.Session) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotMissionTemplteDo) Clauses(conds ...clause.Expression) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotMissionTemplteDo) Returning(value interface{}, columns ...string) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotMissionTemplteDo) Not(conds ...gen.Condition) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotMissionTemplteDo) Or(conds ...gen.Condition) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotMissionTemplteDo) Select(conds ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotMissionTemplteDo) Where(conds ...gen.Condition) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotMissionTemplteDo) Order(conds ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotMissionTemplteDo) Distinct(cols ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotMissionTemplteDo) Omit(cols ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotMissionTemplteDo) Join(table schema.Tabler, on ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotMissionTemplteDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotMissionTemplteDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotMissionTemplteDo) Group(cols ...field.Expr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotMissionTemplteDo) Having(conds ...gen.Condition) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotMissionTemplteDo) Limit(limit int) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotMissionTemplteDo) Offset(offset int) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotMissionTemplteDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotMissionTemplteDo) Unscoped() *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotMissionTemplteDo) Create(values ...*model.XRobotMissionTemplte) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotMissionTemplteDo) CreateInBatches(values []*model.XRobotMissionTemplte, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotMissionTemplteDo) Save(values ...*model.XRobotMissionTemplte) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotMissionTemplteDo) First() (*model.XRobotMissionTemplte, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionTemplte), nil
	}
}

func (x xRobotMissionTemplteDo) Take() (*model.XRobotMissionTemplte, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionTemplte), nil
	}
}

func (x xRobotMissionTemplteDo) Last() (*model.XRobotMissionTemplte, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionTemplte), nil
	}
}

func (x xRobotMissionTemplteDo) Find() ([]*model.XRobotMissionTemplte, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotMissionTemplte), err
}

func (x xRobotMissionTemplteDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotMissionTemplte, err error) {
	buf := make([]*model.XRobotMissionTemplte, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotMissionTemplteDo) FindInBatches(result *[]*model.XRobotMissionTemplte, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotMissionTemplteDo) Attrs(attrs ...field.AssignExpr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotMissionTemplteDo) Assign(attrs ...field.AssignExpr) *xRobotMissionTemplteDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotMissionTemplteDo) Joins(fields ...field.RelationField) *xRobotMissionTemplteDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotMissionTemplteDo) Preload(fields ...field.RelationField) *xRobotMissionTemplteDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotMissionTemplteDo) FirstOrInit() (*model.XRobotMissionTemplte, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionTemplte), nil
	}
}

func (x xRobotMissionTemplteDo) FirstOrCreate() (*model.XRobotMissionTemplte, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionTemplte), nil
	}
}

func (x xRobotMissionTemplteDo) FindByPage(offset int, limit int) (result []*model.XRobotMissionTemplte, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotMissionTemplteDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotMissionTemplteDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotMissionTemplteDo) Delete(models ...*model.XRobotMissionTemplte) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotMissionTemplteDo) withDO(do gen.Dao) *xRobotMissionTemplteDo {
	x.DO = *do.(*gen.DO)
	return x
}
