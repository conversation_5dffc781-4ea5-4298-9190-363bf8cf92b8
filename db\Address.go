package db

import (
	"fmt"
	"xserver/server"
)

type Address struct {
	Id         int    `gorm:"column:Id"`         //
	SellerId   int    `gorm:"column:SellerId"`   //运营商
	Address    string `gorm:"column:Address"`    //地址
	State      int    `gorm:"column:State"`      //状态 1未使用 2已使用
	CreateTime string `gorm:"column:CreateTime"` //创建时间
	GameId     int    `gorm:"column:GameId"`
	RoomLevel  int    `gorm:"column:RoomLevel"`
}

func (*Address) TableName() string {
	return "x_address"
}

func Address_Page_Data(Page int, PageSize int, SellerId int, State int) (int, []Address) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id asc"
	PageKey := "Id"
	data := Address{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "State", "=", State, 0)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []Address{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []Address{}
	dbtable.Where(fmt.Sprintf("%s >= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}
