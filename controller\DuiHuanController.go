package controller

import (
	"fmt"
	"path"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/server"
)

type DuiHuanController struct {
}

func (c *DuiHuanController) Init() {
	group := server.Http().NewGroup("/api/duihuan")
	{
		group.Post("/list", c.list)
	}
}

func (c *DuiHuanController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		Id        int
		Address   string
		Symbol    string
		State     int
		StartTime int64
		EndTime   int64
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "兑换管理", "兑换记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_兑换记录_%s", time.Now().Format("20060102150405")))
	//var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("Id", "订单编号")
		xlsx.SetTitle("FromAddress", "兑换地址")
		xlsx.SetTitle("ExchangeType", "兑换类型")
		xlsx.SetTitle("Amount", "兑换数量")
		xlsx.SetTitle("TrxPrice", "原汇率")
		xlsx.SetTitle("ExchangeRateFixed", "汇率差")
		xlsx.SetTitle("RealExchangeRate", "兑换汇率")
		xlsx.SetTitle("BackAmount", "兑换金额")
		xlsx.SetTitle("GasFee", "系统磨损")
		xlsx.SetTitle("State", "兑换状态")
		xlsx.SetTitle("Memo", "备注")
		xlsx.SetTitle("BackOrder", "出款订单")
		xlsx.SetTitle("TxId", "收款哈希")
		xlsx.SetTitle("BackTxId", "出款哈希")
		xlsx.SetTitle("CreateTime", "订单时间")
		xlsx.SetTitleStyle()
	}

	total, data := db.DuiHuan_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.Id, reqdata.Address, reqdata.Symbol, reqdata.State, reqdata.StartTime, reqdata.EndTime)
	if reqdata.Export != 1 {
		ctx.Put("data", data)
		ctx.Put("total", total)
	} else {
		totalAmount := float64(0.0)
		totalBackAmount := float64(0.0)
		totalGasFee := float64(0.0)
		for i := 0; i < len(data); i++ {
			xlsx.SetValue("Id", data[i].Id, int64(i+2))
			xlsx.SetValue("FromAddress", data[i].FromAddress, int64(i+2))
			if strings.EqualFold(data[i].Symbol, "usdt") {
				xlsx.SetValue("ExchangeType", "USTD-->TRX", int64(i+2))
				xlsx.SetValue("RealExchangeRate", data[i].TrxPrice*(1.0+data[i].ExchangeRateFixed), int64(i+2))
			} else if data[i].Symbol == "trx" {
				xlsx.SetValue("ExchangeType", "TRX-->USTD", int64(i+2))
				xlsx.SetValue("RealExchangeRate", data[i].TrxPrice*(1.0-data[i].ExchangeRateFixed), int64(i+2))
			} else {
				xlsx.SetValue("ExchangeType", "未知", int64(i+2))
			}
			xlsx.SetValue("Amount", data[i].Amount, int64(i+2))
			xlsx.SetValue("TrxPrice", data[i].TrxPrice, int64(i+2))
			xlsx.SetValue("ExchangeRateFixed", data[i].ExchangeRateFixed, int64(i+2))
			xlsx.SetValue("BackAmount", data[i].BackAmount, int64(i+2))
			xlsx.SetValue("GasFee", data[i].GasFee, int64(i+2))
			if data[i].State == 200 {
				xlsx.SetValue("State", "兑换成功", int64(i+2))
			} else {
				xlsx.SetValue("State", "兑换失败", int64(i+2))
			}
			xlsx.SetValue("Memo", data[i].Memo, int64(i+2))
			xlsx.SetValue("BackOrder", data[i].BackOrder, int64(i+2))
			xlsx.SetValue("TxId", data[i].TxId, int64(i+2))
			xlsx.SetValue("BackTxId", data[i].BackTxId, int64(i+2))
			xlsx.SetValue("CreateTime", data[i].CreateTime, int64(i+2))

			totalAmount += data[i].Amount
			totalBackAmount += data[i].BackAmount
			totalGasFee += data[i].GasFee
		}
		xlsx.SetValue("Id", "合计", int64(total+2))
		xlsx.SetValue("Amount", totalAmount, int64(total+2))
		xlsx.SetValue("BackAmount", totalBackAmount, int64(total+2))
		xlsx.SetValue("GasFee", totalGasFee, int64(total+2))
		xlsx.SetValueStyle(int64(total + 2))
	}

	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("兑换查询", ctx, reqdata)
}
