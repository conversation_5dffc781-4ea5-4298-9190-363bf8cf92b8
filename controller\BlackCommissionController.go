package controller

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type BlackCommissionController struct {
}

func (c *BlackCommissionController) Init() {
	group := server.Http().NewGroup("/api/blackCommission")
	{
		group.Post("/list", c.list)
		group.Post("/create", c.create)
		group.Post("/delete", c.delete)
		group.Post("/batchDelete", c.batchDelete)
	}
}

func (c *BlackCommissionController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		UserType int
		UserId   []string
	}

	errcode := 0
	reqdata := RequestData{UserType: 1}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "独立代理", "佣金黑名单", "查", "佣金黑名单列表查询")
	if token == nil {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	xAgentBlacklist := server.DaoxHashGame().XAgentBlacklist
	db := xAgentBlacklist.WithContext(ctx.Gin())
	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	if len(reqdata.UserId) > 0 {
		int32Slice, _ := convertStringSliceToInt32Slice(reqdata.UserId)
		db = db.Where(xAgentBlacklist.UserID.In(int32Slice...))
	}

	if reqdata.UserType == 1 || reqdata.UserType == 2 {
		type Result struct {
			model.XAgentBlacklist
			SellerName  string
			ChannelName string
		}
		var list []*Result
		total, err := db.
			Select(xAgentBlacklist.ALL, xSeller.SellerName, xChannel.ChannelName).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xAgentBlacklist.SellerID)).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xAgentBlacklist.ChannelID)).
			Where(xAgentBlacklist.UserType.Eq(int32(reqdata.UserType))).
			Order(xAgentBlacklist.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.UserType == 3 {
		type Result struct {
			model.XAgentBlacklist
			ChannelName string
		}
		var list []*Result
		total, err := db.
			Select(xAgentBlacklist.ALL, xChannel.ChannelName).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xAgentBlacklist.UserID)).
			Where(xAgentBlacklist.UserType.Eq(int32(reqdata.UserType))).
			Order(xAgentBlacklist.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.UserType == 4 {
		type Result struct {
			model.XAgentBlacklist
			SellerName string
		}
		var list []*Result
		total, err := db.
			Select(xAgentBlacklist.ALL, xSeller.SellerName).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xAgentBlacklist.SellerID)).
			Where(xAgentBlacklist.UserType.Eq(int32(reqdata.UserType))).
			Order(xAgentBlacklist.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

}

func (c *BlackCommissionController) create(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserType int
		UserIds  string
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "独立代理", "佣金黑名单", "改", "佣金黑名单添加")
	if token == nil {
		return
	}

	userIds := strings.Split(reqdata.UserIds, ",")

	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		for _, userId := range userIds {
			if reqdata.UserType == 1 || reqdata.UserType == 2 {
				userId, _ := strconv.Atoi(userId)
				_, err := tx.XAgentBlacklist.WithContext(ctx.Gin()).
					Where(tx.XAgentBlacklist.UserID.Eq(int32(userId)), tx.XAgentBlacklist.UserType.Eq(int32(reqdata.UserType))).First()

				userInfos, checkUserErr := tx.XUser.WithContext(ctx.Gin()).Where(tx.XUser.UserID.Eq(int32(userId))).First()
				if checkUserErr != nil {
					return errors.New(fmt.Sprintf("ID %d 不存在", userId))
				}

				if err == nil {
					return errors.New(fmt.Sprintf("已存%d在该黑名单", userId))
				}

				err = tx.XAgentBlacklist.WithContext(ctx.Gin()).Create(&model.XAgentBlacklist{
					UserID:        int32(userId),
					UserType:      int32(reqdata.UserType),
					SellerID:      userInfos.SellerID,
					ChannelID:     userInfos.ChannelID,
					TopAgentID:    userInfos.TopAgentID,
					AgentID:       userInfos.AgentID,
					CreateAccount: server.GetToken(ctx).Account,
				})
				if err != nil {
					return err
				}
			}

			if reqdata.UserType == 3 {
				userId, _ := strconv.Atoi(userId)
				// 判断id是否存在
				_, err := tx.XChannel.WithContext(ctx.Gin()).Where(tx.XChannel.ChannelID.Eq(int32(userId))).First()
				if err != nil {
					return errors.New(fmt.Sprintf("ID %d 不存在", userId))
				}
				err = tx.XAgentBlacklist.WithContext(ctx.Gin()).Create(&model.XAgentBlacklist{
					UserID:        int32(userId),
					UserType:      int32(reqdata.UserType),
					ChannelID:     int32(userId),
					CreateAccount: server.GetToken(ctx).Account,
				})
				if err != nil {
					return err
				}
			}

			if reqdata.UserType == 4 {
				userId, _ := strconv.Atoi(userId)
				// 判断id是否存在
				_, err := tx.XSeller.WithContext(ctx.Gin()).Where(tx.XSeller.SellerID.Eq(int32(userId))).First()
				if err != nil {
					return errors.New(fmt.Sprintf("ID %d 不存在", userId))
				}
				err = tx.XAgentBlacklist.WithContext(ctx.Gin()).Create(&model.XAgentBlacklist{
					UserID:        int32(userId),
					UserType:      int32(reqdata.UserType),
					SellerID:      int32(userId),
					CreateAccount: server.GetToken(ctx).Account,
				})
				if err != nil {
					return err
				}
			}

		}
		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

func (c *BlackCommissionController) delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "独立代理", "佣金黑名单", "删", "佣金黑名单删除")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XAgentBlacklist
	db := dao.WithContext(ctx.Gin())

	_, err := db.Where(dao.ID.Eq(int32(reqdata.Id))).Delete()
	if err != nil {
		return
	}

	ctx.RespOK()
}

func (c *BlackCommissionController) batchDelete(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		Ids []int32
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "独立代理", "佣金黑名单", "删", "佣金黑名单删除")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XAgentBlacklist
	db := dao.WithContext(ctx.Gin())

	_, err := db.Where(dao.ID.In(reqdata.Ids...)).Delete()
	if err != nil {
		return
	}

	ctx.RespOK()
}

func convertStringSliceToInt32Slice(stringSlice []string) ([]int32, error) {
	var int32Slice []int32
	for _, str := range stringSlice {
		// 解析字符串为int64，因为Atoi返回的是int
		intValue, err := strconv.Atoi(str)
		if err != nil {
			return nil, err
		}
		// 将int类型转换为int32类型
		int32Slice = append(int32Slice, int32(intValue))
	}
	return int32Slice, nil
}
