package utilsmodel

type Get<PERSON><PERSON><PERSON> struct {
	Name  string
	Value string
}

type UserTronAddress struct {
	AddressCheck uint32 `json:"Address_Check" gorm:"column:Address_Check"`
	Address      string `json:"Address" gorm:"column:Address"`
	AddressLevel int32  `json:"AddressLevel" gorm:"column:AddressLevel"`
	TransferTime string `json:"TransferTime" gorm:"column:TransferTime"`
	UserId       int32  `json:"UserId" gorm:"column:-"`
}
