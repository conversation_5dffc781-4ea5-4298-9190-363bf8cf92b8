package report

import (
	"github.com/shopspring/decimal"
	"xserver/utils"
)

type VipInfo struct {
	Id                  int             `json:"Id" gorm:"column:Id"`                                   //id
	SellerId            int             `json:"SellerId" gorm:"column:SellerId"`                       //SellerId
	ChannelId           int             `json:"ChannelId" gorm:"column:ChannelId"`                     //
	UserId              int             `json:"UserId" gorm:"column:UserId"`                           //UserId
	VipLevel            int             `json:"VipLevel" gorm:"column:VipLevel"`                       //当前vip等级
	Recharge            decimal.Decimal `json:"Recharge" gorm:"column:Recharge"`                       //累计充值
	LiuSui              decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`                           //累计流水
	KeepLiuSui          decimal.Decimal `json:"KeepLiuSui" gorm:"column:KeepLiuSui"`                   //保级流水
	UpgradeTime         utils.MyString  `json:"UpgradeTime" gorm:"column:UpgradeTime"`                 //升级时间
	UpgradeRewardRecord string          `json:"UpgradeRewardRecord" gorm:"column:UpgradeRewardRecord"` //升级时间
	Withdraw            decimal.Decimal `json:"Withdraw" gorm:"column:Withdraw"`                       //累计提现
	CaiJin              decimal.Decimal `json:"CaiJin" gorm:"column:CaiJin"`                           //累计彩金usdt
	CaiJinTrc           decimal.Decimal `json:"CaiJinTrc" gorm:"column:CaiJinTrc"`                     //累计彩金trx
}

type UserDailly struct {
	Id             int             `json:"Id" gorm:"column:Id"`                         //id
	UserId         int             `json:"UserId" gorm:"column:UserId"`                 //UserId
	ChannelId      int             `json:"ChannelId" gorm:"column:ChannelId"`           //
	SellerId       int             `json:"SellerId" gorm:"column:SellerId"`             //SellerId
	RecordDate     string          `json:"RecordDate" gorm:"column:RecordDate"`         //
	BetTrx         decimal.Decimal `json:"BetTrx" gorm:"column:BetTrx"`                 //trx 下注
	RewardTrx      decimal.Decimal `json:"RewardTrx" gorm:"column:RewardTrx"`           //trx 反奖
	LiuSuiTrx      decimal.Decimal `json:"LiuSuiTrx" gorm:"column:LiuSuiTrx"`           //trx 流水
	BetUsdt        decimal.Decimal `json:"BetUsdt" gorm:"column:BetUsdt"`               //usdt 下注
	RewardUsdt     decimal.Decimal `json:"RewardUsdt" gorm:"column:RewardUsdt"`         //usdt 反奖
	LiuSuiUsdt     decimal.Decimal `json:"LiuSuiUsdt" gorm:"column:LiuSuiUsdt"`         //usdt 流水
	FineLiuSuiTrx  decimal.Decimal `json:"FineLiuSuiTrx" gorm:"column:FineLiuSuiTrx"`   //扣除流水trx
	FineLiuSuiUsdt decimal.Decimal `json:"FineLiuSuiUsdt" gorm:"column:FineLiuSuiUsdt"` //扣除流水usdt
	FineAccount    string          `json:"FineAccount" gorm:"column:FineAccount"`       //扣除流水操作人
	FineTime       *utils.MyString `json:"FineTime" gorm:"column:FineTime"`             //扣除流水时间
	FineMemo       string          `json:"FineMemo" gorm:"column:FineMemo"`             //扣除流水备注
	BetCountTrx    string          `json:"BetCountTrx" gorm:"column:BetCountTrx"`       //
	BetCountUsdt   string          `json:"BetCountUsdt" gorm:"column:BetCountUsdt"`     //
	TotalWinLoss   decimal.Decimal `json:"TotalWinLoss" gorm:"column:TotalWinLoss"`     //今日输赢总额(包含所有游戏) 赢为正 输为负
	TotalCaiJin    decimal.Decimal `json:"TotalCaiJin" gorm:"column:TotalCaiJin"`       //今日赠送usdt彩金总额
	TotalCaiJinTrx decimal.Decimal `json:"TotalCaiJinTrx" gorm:"column:TotalCaiJinTrx"` //今日赠送trx彩金总额
}
