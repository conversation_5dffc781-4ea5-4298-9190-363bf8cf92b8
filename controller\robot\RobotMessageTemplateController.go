package robot

import (
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

// list 获取消息模板配置列表
func (c *Router) messagesList(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		Page          int    `json:"page"`
		PageSize      int    `json:"page_size"`
		FullName      string `json:"full_name"`
		Name          string `json:"name"`
		MessageType   *int32 `json:"message_type"`
		MessageObject *int32 `json:"message_object"`
		UserType      *int32 `json:"user_type"`
		RobotType int32 `json:"robot_type"`
	}

	reqData := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqData), reqData, "机器人管理", "消息模板配置", "查", "查询消息模板配置")
	if token == nil {
		return
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}

	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize
	type Result struct {
		model.XRobotMessageTemplte
	}
	var results []*Result
	dao := server.DaoxHashGame().XRobotMessageTemplte

	query := dao.WithContext(nil).Select(dao.ALL)

	if reqData.FullName != "" {
		query.Where(dao.FullName.Eq(reqData.FullName))
	}

	if reqData.Name != "" {
		query.Where(dao.Name.Eq(reqData.Name))
	}

	if reqData.MessageType != nil {
		query.Where(dao.MessageType.Eq(*reqData.MessageType))
	}

	if reqData.UserType != nil {
		query.Where(dao.UserType.Eq(*reqData.UserType))
	}

	if reqData.MessageObject != nil {
		query.Where(dao.MessageObject.Eq(*reqData.MessageObject))
	}

	if reqData.RobotType != 0 {
		query.Where(dao.RobotType.Eq(reqData.RobotType))
	}

	total, err := query.WithContext(nil).Where(dao.IsDel.Eq(0)).Order(dao.ID.Desc()).ScanByPage(&results, offset, limit)

	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	ctx.Put("total", total)
	ctx.Put("data", results)
	ctx.RespOK()
}

// create 创建消息模板配置
func (c *Router) messagesCreate(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := model.XRobotMessageTemplte{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "消息模板配置", "增", "创建消息模板配置")
	if token == nil {
		return
	}

	err := server.DaoxHashGame().XRobotMessageTemplte.WithContext(nil).Create(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

// update 更新消息模板配置
func (c *Router) messagesUpdate(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := model.XRobotMessageTemplte{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "消息模板配置", "改", "更新消息模板配置")
	if token == nil {
		return
	}

	err := server.DaoxHashGame().XRobotMessageTemplte.WithContext(nil).Where(server.DaoxHashGame().XRobotMessageTemplte.ID.Eq(reqdata.ID)).Save(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

// delete 删除消息模板配置
func (c *Router) messagesDelete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		ID int64 `json:"id"`
	}

	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "消息模板配置", "删", "删除消息模板配置")
	if token == nil {
		return
	}

	// 软删除
	_, err := server.DaoxHashGame().XRobotMessageTemplte.WithContext(nil).Where(server.DaoxHashGame().XRobotMessageTemplte.ID.Eq(reqdata.ID)).Update(server.DaoxHashGame().XRobotMessageTemplte.IsDel, 1)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}
