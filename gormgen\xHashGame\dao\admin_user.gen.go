// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newAdminUser(db *gorm.DB, opts ...gen.DOOption) adminUser {
	_adminUser := adminUser{}

	_adminUser.adminUserDo.UseDB(db, opts...)
	_adminUser.adminUserDo.UseModel(&model.AdminUser{})

	tableName := _adminUser.adminUserDo.TableName()
	_adminUser.ALL = field.NewAsterisk(tableName)
	_adminUser.ID = field.NewInt32(tableName, "Id")
	_adminUser.Account = field.NewString(tableName, "Account")
	_adminUser.Password = field.NewString(tableName, "Password")
	_adminUser.SellerID = field.NewInt32(tableName, "SellerId")
	_adminUser.RoleName = field.NewString(tableName, "RoleName")
	_adminUser.State = field.NewInt32(tableName, "State")
	_adminUser.Token = field.NewString(tableName, "Token")
	_adminUser.GoogleSecret = field.NewString(tableName, "GoogleSecret")
	_adminUser.Remark = field.NewString(tableName, "Remark")
	_adminUser.LoginCount = field.NewInt32(tableName, "LoginCount")
	_adminUser.LoginTime = field.NewTime(tableName, "LoginTime")
	_adminUser.LoginIP = field.NewString(tableName, "LoginIp")
	_adminUser.CreateTime = field.NewTime(tableName, "CreateTime")
	_adminUser.IPWhite = field.NewString(tableName, "IpWhite")
	_adminUser.OptGoogleSecret = field.NewString(tableName, "OptGoogleSecret")
	_adminUser.RecvNotice = field.NewInt32(tableName, "RecvNotice")
	_adminUser.ChannelID = field.NewInt32(tableName, "ChannelId")
	_adminUser.RoleType = field.NewInt32(tableName, "RoleType")
	_adminUser.MaxAddMoney = field.NewInt32(tableName, "MaxAddMoney")
	_adminUser.CSGroup = field.NewString(tableName, "CSGroup")
	_adminUser.CSID = field.NewString(tableName, "CSId")
	_adminUser.IsIPWhite = field.NewInt32(tableName, "IsIpWhite")

	_adminUser.fillFieldMap()

	return _adminUser
}

type adminUser struct {
	adminUserDo adminUserDo

	ALL             field.Asterisk
	ID              field.Int32
	Account         field.String // 账号
	Password        field.String // 密码
	SellerID        field.Int32  // 运营商
	RoleName        field.String // 角色名
	State           field.Int32  // 状态 1启用 2禁用
	Token           field.String // token
	GoogleSecret    field.String // 谷歌验证码
	Remark          field.String // 备注
	LoginCount      field.Int32  // 登录次数
	LoginTime       field.Time   // 最后登录时间
	LoginIP         field.String // 最后登录Ip
	CreateTime      field.Time   // 创建时间
	IPWhite         field.String // ip白名单,空表无白名单,所有ip均可登录
	OptGoogleSecret field.String // 操作谷歌验证码
	RecvNotice      field.Int32  // 是否接受通知 1是,2否
	ChannelID       field.Int32  // 渠道
	RoleType        field.Int32  // 1运营角色 2渠道角色
	MaxAddMoney     field.Int32  // 后台增资最大金额
	CSGroup         field.String // 客服组
	CSID            field.String // 客服Id
	IsIPWhite       field.Int32  // 1-开启 2-关闭

	fieldMap map[string]field.Expr
}

func (a adminUser) Table(newTableName string) *adminUser {
	a.adminUserDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a adminUser) As(alias string) *adminUser {
	a.adminUserDo.DO = *(a.adminUserDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *adminUser) updateTableName(table string) *adminUser {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "Id")
	a.Account = field.NewString(table, "Account")
	a.Password = field.NewString(table, "Password")
	a.SellerID = field.NewInt32(table, "SellerId")
	a.RoleName = field.NewString(table, "RoleName")
	a.State = field.NewInt32(table, "State")
	a.Token = field.NewString(table, "Token")
	a.GoogleSecret = field.NewString(table, "GoogleSecret")
	a.Remark = field.NewString(table, "Remark")
	a.LoginCount = field.NewInt32(table, "LoginCount")
	a.LoginTime = field.NewTime(table, "LoginTime")
	a.LoginIP = field.NewString(table, "LoginIp")
	a.CreateTime = field.NewTime(table, "CreateTime")
	a.IPWhite = field.NewString(table, "IpWhite")
	a.OptGoogleSecret = field.NewString(table, "OptGoogleSecret")
	a.RecvNotice = field.NewInt32(table, "RecvNotice")
	a.ChannelID = field.NewInt32(table, "ChannelId")
	a.RoleType = field.NewInt32(table, "RoleType")
	a.MaxAddMoney = field.NewInt32(table, "MaxAddMoney")
	a.CSGroup = field.NewString(table, "CSGroup")
	a.CSID = field.NewString(table, "CSId")
	a.IsIPWhite = field.NewInt32(table, "IsIpWhite")

	a.fillFieldMap()

	return a
}

func (a *adminUser) WithContext(ctx context.Context) *adminUserDo {
	return a.adminUserDo.WithContext(ctx)
}

func (a adminUser) TableName() string { return a.adminUserDo.TableName() }

func (a adminUser) Alias() string { return a.adminUserDo.Alias() }

func (a adminUser) Columns(cols ...field.Expr) gen.Columns { return a.adminUserDo.Columns(cols...) }

func (a *adminUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *adminUser) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 22)
	a.fieldMap["Id"] = a.ID
	a.fieldMap["Account"] = a.Account
	a.fieldMap["Password"] = a.Password
	a.fieldMap["SellerId"] = a.SellerID
	a.fieldMap["RoleName"] = a.RoleName
	a.fieldMap["State"] = a.State
	a.fieldMap["Token"] = a.Token
	a.fieldMap["GoogleSecret"] = a.GoogleSecret
	a.fieldMap["Remark"] = a.Remark
	a.fieldMap["LoginCount"] = a.LoginCount
	a.fieldMap["LoginTime"] = a.LoginTime
	a.fieldMap["LoginIp"] = a.LoginIP
	a.fieldMap["CreateTime"] = a.CreateTime
	a.fieldMap["IpWhite"] = a.IPWhite
	a.fieldMap["OptGoogleSecret"] = a.OptGoogleSecret
	a.fieldMap["RecvNotice"] = a.RecvNotice
	a.fieldMap["ChannelId"] = a.ChannelID
	a.fieldMap["RoleType"] = a.RoleType
	a.fieldMap["MaxAddMoney"] = a.MaxAddMoney
	a.fieldMap["CSGroup"] = a.CSGroup
	a.fieldMap["CSId"] = a.CSID
	a.fieldMap["IsIpWhite"] = a.IsIPWhite
}

func (a adminUser) clone(db *gorm.DB) adminUser {
	a.adminUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a adminUser) replaceDB(db *gorm.DB) adminUser {
	a.adminUserDo.ReplaceDB(db)
	return a
}

type adminUserDo struct{ gen.DO }

func (a adminUserDo) Debug() *adminUserDo {
	return a.withDO(a.DO.Debug())
}

func (a adminUserDo) WithContext(ctx context.Context) *adminUserDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a adminUserDo) ReadDB() *adminUserDo {
	return a.Clauses(dbresolver.Read)
}

func (a adminUserDo) WriteDB() *adminUserDo {
	return a.Clauses(dbresolver.Write)
}

func (a adminUserDo) Session(config *gorm.Session) *adminUserDo {
	return a.withDO(a.DO.Session(config))
}

func (a adminUserDo) Clauses(conds ...clause.Expression) *adminUserDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a adminUserDo) Returning(value interface{}, columns ...string) *adminUserDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a adminUserDo) Not(conds ...gen.Condition) *adminUserDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a adminUserDo) Or(conds ...gen.Condition) *adminUserDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a adminUserDo) Select(conds ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a adminUserDo) Where(conds ...gen.Condition) *adminUserDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a adminUserDo) Order(conds ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a adminUserDo) Distinct(cols ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a adminUserDo) Omit(cols ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a adminUserDo) Join(table schema.Tabler, on ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a adminUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a adminUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a adminUserDo) Group(cols ...field.Expr) *adminUserDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a adminUserDo) Having(conds ...gen.Condition) *adminUserDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a adminUserDo) Limit(limit int) *adminUserDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a adminUserDo) Offset(offset int) *adminUserDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a adminUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *adminUserDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a adminUserDo) Unscoped() *adminUserDo {
	return a.withDO(a.DO.Unscoped())
}

func (a adminUserDo) Create(values ...*model.AdminUser) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a adminUserDo) CreateInBatches(values []*model.AdminUser, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a adminUserDo) Save(values ...*model.AdminUser) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a adminUserDo) First() (*model.AdminUser, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdminUser), nil
	}
}

func (a adminUserDo) Take() (*model.AdminUser, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdminUser), nil
	}
}

func (a adminUserDo) Last() (*model.AdminUser, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdminUser), nil
	}
}

func (a adminUserDo) Find() ([]*model.AdminUser, error) {
	result, err := a.DO.Find()
	return result.([]*model.AdminUser), err
}

func (a adminUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AdminUser, err error) {
	buf := make([]*model.AdminUser, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a adminUserDo) FindInBatches(result *[]*model.AdminUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a adminUserDo) Attrs(attrs ...field.AssignExpr) *adminUserDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a adminUserDo) Assign(attrs ...field.AssignExpr) *adminUserDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a adminUserDo) Joins(fields ...field.RelationField) *adminUserDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a adminUserDo) Preload(fields ...field.RelationField) *adminUserDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a adminUserDo) FirstOrInit() (*model.AdminUser, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdminUser), nil
	}
}

func (a adminUserDo) FirstOrCreate() (*model.AdminUser, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AdminUser), nil
	}
}

func (a adminUserDo) FindByPage(offset int, limit int) (result []*model.AdminUser, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a adminUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a adminUserDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a adminUserDo) Delete(models ...*model.AdminUser) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *adminUserDo) withDO(do gen.Dao) *adminUserDo {
	a.DO = *do.(*gen.DO)
	return a
}
