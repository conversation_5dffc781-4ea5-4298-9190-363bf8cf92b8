package msg

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"
	"xserver/controller/msg/model"
	"xserver/server"

	"github.com/beego/beego/logs"
)

// SendMessageAPIService 消息发送API服务接口
type SendMessageAPIService interface {
	// SendAccountMessage 账户资金变动通知
	// @param messageType 消息类型，如 "1"=领取彩金提醒,"3"=充值到账提醒,"4": 提现成功提醒,"5": 提现提交提醒,"6": 提现审核提醒
	// @param userId 用户ID
	// @param amount 金额
	// @param variables 消息变量，可选，用于替换模板中的变量
	// @return error 发送失败时返回错误
	SendAccountMessage(messageType string, userId int, amount float64, variables map[string]interface{}) error

	// SendActivityMessage 活动相关通知（简化为一个统一方法）
	// @param userId 用户ID
	// @param amount 金额（彩金金额，可选）
	// @param variables 消息变量，可选，用于替换模板中的变量
	// @param activeId 活动ID（可选），当messageType="2"时，如果提供了活动ID，将使用活动的标题
	// @return error 发送失败时返回错误
	SendActivityMessage(userId int, amount float64, variables map[string]interface{}, activeId int) error

	// SendSystemMessage 系统通知（简化为一个统一方法）
	// @param messageType 消息类型，如"7"=系统维护通知，"8"=系统更新通知
	// @param userFilter 用户筛选条件，用于确定消息接收者
	// @param params 参数，如维护时间、版本号等
	// @param variables 消息变量，可选，用于替换模板中的变量
	// @return int 成功发送的消息数量
	// @return error 发送失败时返回错误
	SendSystemMessage(messageType string, userFilter UserFilter, params map[string]interface{}, variables map[string]interface{}) (int, error)

	// SendMessage 发送业务消息（针对单个用户） 1=领取彩金提醒, 2=活动参与提醒, 3=充值到账提醒, 4=提现成功提醒, 5=提现提交提醒, 6=提现审核提醒
	// @param messageType 消息类型，对应模板类型
	// @param userId 用户ID
	// @param variables 消息变量，可选，用于替换模板中的变量
	// @return error 发送失败时返回错误
	SendMessage(messageType string, userId int, variables map[string]interface{}) error

	// SendBatchMessage 发送系统消息（针对多个用户） 1=领取彩金提醒, 2=活动参与提醒, 3=充值到账提醒, 4=提现成功提醒, 5=提现提交提醒, 6=提现审核提醒
	// @param messageType 消息类型，对应模板类型
	// @param userFilter 用户筛选条件，用于确定消息接收者
	// @param variables 消息变量，可选，用于替换模板中的变量
	// @return int 成功发送的消息数量
	// @return error 发送失败时返回错误
	SendBatchMessage(messageType string, userFilter UserFilter, variables map[string]interface{}) (int, error)
}

// sendMessageAPIServiceImpl 消息发送API服务实现
type sendMessageAPIServiceImpl struct {
	messageSendService MessageSendService
}

// NewSendMessageAPIService 创建消息发送API服务实例
// @return SendMessageAPIService 返回消息发送API服务实例
func NewSendMessageAPIService() SendMessageAPIService {
	return &sendMessageAPIServiceImpl{
		messageSendService: NewMessageSendService(),
	}
}

//{彩金金额}、{充值金额}、{提现金额}

// SendAccountMessage 账户资金变动通知
// @param messageType 消息类型，如 "1"=: 领取彩金提醒,"3": 充值到账提醒,"4": 提现成功提醒,"5": 提现提交提醒,"6": 提现审核提醒
// @param userId 用户ID
// @param amount  金额
// @param variables 消息变量，可选，用于替换模板中的变量
// @return error 发送失败时返回错误
func (s *sendMessageAPIServiceImpl) SendAccountMessage(messageType string, userId int, amount float64, variables map[string]interface{}) error {
	if variables == nil {
		variables = make(map[string]interface{})
	}

	// 根据消息类型设置对应的变量名
	//switch messageType {
	//case TemplateTypeBonusReminder: // "1"=领取彩金提醒
	//	variables["彩金金额"] = amount
	//case TemplateTypeDepositReminder: // "3"=充值到账提醒
	//	variables["充值金额"] = amount
	//case TemplateTypeWithdrawSuccess, TemplateTypeWithdrawReview: // "4"=提现成功提醒，"5"=提现提交提醒，"6"=提现审核提醒
	//	variables["提现金额"] = amount
	//default:
	//	// 默认使用通用变量名
	//	variables["金额"] = amount
	//}
	//if messageTitle != "" {
	//	variables["消息标题"] = messageTitle
	//}
	amountStr := fmt.Sprintf("%.2f", amount) + " USDT" // "123.46" (保留2位小数，四舍五入)
	variables["彩金金额"] = amountStr
	variables["充值金额"] = amountStr
	variables["提现金额"] = amountStr
	variables["金额"] = amountStr
	logs.Info(" 收到站内信推送", " 用户Id=", userId, " 消息类型=", GetTemplateTypeName(messageType), " 金额=", amount)
	return s.SendMessage(messageType, userId, variables)
}

// SendActivityMessage 活动相关通知
// @param userId 用户ID
// @param amount 金额（彩金金额，可选）
// @param variables 消息变量，可选，用于替换模板中的变量
// @param activeId 活动ID（可选），当messageType="2"时，如果提供了活动ID，将使用活动的标题
// @return error 发送失败时返回错误
func (s *sendMessageAPIServiceImpl) SendActivityMessage(userId int, amount float64, variables map[string]interface{}, activeId int) error {
	if variables == nil {
		variables = make(map[string]interface{})
	}
	if amount > 0 {
		// 根据消息类型设置对应的变量名
		amountStr := fmt.Sprintf("%.2f", amount) + " USDT" // "123.46" (保留2位小数，四舍五入)
		variables["彩金金额"] = amountStr
	}
	messageType := "1"
	// 如果提供了活动ID，获取活动标题
	if activeId > 0 {
		// 查询活动信息
		var activeDefine struct {
			TitleLang string `json:"TitleLang"`
			Title     string `json:"Title"`
		}

		err := server.Db().GormDao().Table("x_active_define").
			Select("TitleLang, Title").
			Where("Id = ?", activeId).
			First(&activeDefine).Error

		if err == nil && (activeDefine.TitleLang != "" || activeDefine.Title != "") {
			// 设置活动标题到变量中，供模板使用
			if activeDefine.TitleLang != "" {
				variables["_titleLang"] = activeDefine.TitleLang
			} else if activeDefine.Title != "" {
				variables["_title"] = activeDefine.Title
			}
		} else {
			logs.Error("获取活动标题失败，活动ID: %d, 错误: %v", activeId, err)
		}
	}

	return s.SendMessage(messageType, userId, variables)
}

// SendSystemMessage 系统通知
// @param messageType 消息类型，如"7"=系统维护通知，"8"=系统更新通知
// @param userFilter 用户筛选条件，用于确定消息接收者
// @param params 参数，如维护时间、版本号等
// @param variables 消息变量，可选，用于替换模板中的变量
// @return int 成功发送的消息数量
// @return error 发送失败时返回错误
func (s *sendMessageAPIServiceImpl) SendSystemMessage(messageType string, userFilter UserFilter, params map[string]interface{}, variables map[string]interface{}) (int, error) {
	if variables == nil {
		variables = make(map[string]interface{})
	}

	// 合并params到variables
	if params != nil {
		for key, value := range params {
			variables[key] = value
		}
	}

	// 根据消息类型处理特定参数
	//switch messageType {
	//case MsgTypeSystemMaintenance: // "7"=系统维护通知
	//	if startTime, ok := params["startTime"].(time.Time); ok {
	//		variables["StartTime"] = startTime.Format("2006-01-02 15:04:05")
	//	}
	//	if endTime, ok := params["endTime"].(time.Time); ok {
	//		variables["EndTime"] = endTime.Format("2006-01-02 15:04:05")
	//	}
	//case MsgTypeSystemUpdate: // "8"=系统更新通知
	//	if version, ok := params["version"].(string); ok {
	//		variables["Version"] = version
	//	}
	//}

	return s.SendBatchMessage(messageType, userFilter, variables)
}

// SendMessage 发送业务消息（针对单个用户）
// @param messageType 消息类型，对应模板类型
// @param userId 用户ID
// @param variables 消息变量，可选，用于替换模板中的变量
// @return error 发送失败时返回错误
func (s *sendMessageAPIServiceImpl) SendMessage(messageType string, userId int, variables map[string]interface{}) error {
	// 创建只包含单个用户ID的用户筛选条件
	userFilter := UserFilter{
		//UserIds: strconv.FormatInt(userId, 10),
		UserIds: strconv.Itoa(userId),
	}

	// 调用SendBatchMessage发送消息
	count, err := s.SendBatchMessage(messageType, userFilter, variables)
	if err != nil {
		return err
	}

	if count == 0 {
		// 记录日志
		logs.Error("消息发送失败，没有成功发送的消息")
	}

	return nil
}

// SendBatchMessage 发送系统消息（针对多个用户）
// @param messageType 消息类型，对应模板类型
// @param userFilter 用户筛选条件，用于确定消息接收者
// @param variables 消息变量，可选，用于替换模板中的变量
// @return int 成功发送的消息数量
// @return error 发送失败时返回错误
func (s *sendMessageAPIServiceImpl) SendBatchMessage(messageType string, userFilter UserFilter, variables map[string]interface{}) (int, error) {
	// 查找对应类型的模板
	var templateId int64
	var err error

	// 查询模板ID
	templateId, err = s.getTemplateIdByType(messageType)
	if err != nil {
		return 0, fmt.Errorf("获取模板失败: %w", err)
	}
	// 生成任务ID - 使用时间戳+随机数
	taskId := fmt.Sprintf("task_%d_%d", time.Now().UnixNano(), rand.Intn(10000))
	// 调用MessageSendService的AsyncSendMessage方法异步发送消息
	taskId, err = s.messageSendService.AsyncSendMessage(templateId, userFilter, variables)
	if err != nil {
		return 0, fmt.Errorf("异步发送消息失败: %w", err)
	}

	// 记录日志
	logs.Info("异步发送消息任务已创建，任务ID: %s", taskId)

	// 返回任务ID长度作为成功标识，实际发送数量需要后续查询
	return 1, nil
}

// getTemplateIdByType 根据消息类型获取模板ID
// @param messageType 消息类型，对应模板类型
// @return int64 模板ID
// @return error 查询失败时返回错误
func (s *sendMessageAPIServiceImpl) getTemplateIdByType(messageType string) (int64, error) {
	// 查询数据库获取指定类型的模板
	var template model.StationMessageTemplate
	result := server.Db().GormDao().Model(&model.StationMessageTemplate{}).
		Where("Type = ? AND PushType = ?", messageType, 0). // 只按类型和推送类型过滤
		Order("Id DESC").                                   // 获取最新的模板
		Select("Id, Status").                               // 只选择需要的字段
		First(&template)

	if result.Error != nil {
		logs.Error("未找到类型为 %s 的模板: %v", messageType, result.Error)
		return 0, fmt.Errorf("未找到类型为 %s 的模板: %w", messageType, result.Error)
	}

	// 根据模板状态判断
	if template.Status == 0 {
		logs.Error("类型为 %s 的模板已被禁用", messageType)
		return 0, fmt.Errorf("类型为 %s 的模板已被禁用", messageType)
	} else if template.Status == 1 {
		// 模板状态为启用
		return template.ID, nil
	} else {
		// 其他未知状态
		logs.Error("类型为 %s 的模板状态异常: %d", messageType, template.Status)
		return 0, fmt.Errorf("类型为 %s 的模板状态异常: %d", messageType, template.Status)
	}
}

//// 调用示例 创建消息发送服务实例
//messageService := msg.NewSendMessageAPIService()
//
//// 发送提现审核提醒
//userId := 10001
//variables := map[string]interface{}{
//"{兑换码}": "SN123456789",
//}
// 业务类型，如 "1"=领取彩金提醒,"3"=充值到账提醒,"4": 提现成功提醒,"5": 提现提交提醒,"6": 提现审核提醒,"9": 登录设备变动通知
//err := messageService.SendMessage(msg.TemplateTypeWithdrawReview, userId, variables)
//if err != nil {
//logs.Error("发送提现审核提醒失败: %v", err)
//return err
//}

//// 创建消息发送服务实例
//messageService := msg.NewSendMessageAPIService()
//
//// 发送充值到账提醒
//userId := int64(10001)
//variables := map[string]interface{}{
//"{流水倍数}": 5,
//}
//// 使用 SendAccountMessage 方法，messageType="3"表示充值到账提醒
//err := messageService.SendAccountMessage(msg.TemplateTypeDepositReminder, userId, 1000.00, variables)
//if err != nil {
//logs.Error("发送充值到账提醒失败: %v", err)
//return err
//}

//// 创建消息发送服务实例
//messageService := msg.NewSendMessageAPIService()
//
//// 发送充值到账提醒
//userId := int64(10001)
//
//// 使用 SendAccountMessage 方法，messageType 消息类型，如"3"=充值到账提醒，"4"=提现成功提醒，"5"=提现提交提醒，"6"=提现审核提醒
//err := messageService.SendAccountMessage(msg.TemplateTypeDepositReminder, userId, 1000.00,nil)
//if err != nil {
//	logs.Error("发送充值到账提醒失败: %v", err)
//	return err
//}

// messageType="10" // 兑换码领取通知
//variables := map[string]interface{}{
//"{兑换码}": "A122333",
//"{到期时间-全格式}": "2008-08-08 18:28:38",// - 样式：2008-08-08 18:28:38
//"{到期时间-月/日格式}": "8月8日",
//"{变动时间-全格式}": "2008-08-08 18:28:38",
//}
