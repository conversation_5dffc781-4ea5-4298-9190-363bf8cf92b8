// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotRedbagQuestion(db *gorm.DB, opts ...gen.DOOption) xRobotRedbagQuestion {
	_xRobotRedbagQuestion := xRobotRedbagQuestion{}

	_xRobotRedbagQuestion.xRobotRedbagQuestionDo.UseDB(db, opts...)
	_xRobotRedbagQuestion.xRobotRedbagQuestionDo.UseModel(&model.XRobotRedbagQuestion{})

	tableName := _xRobotRedbagQuestion.xRobotRedbagQuestionDo.TableName()
	_xRobotRedbagQuestion.ALL = field.NewAsterisk(tableName)
	_xRobotRedbagQuestion.ID = field.NewInt64(tableName, "id")
	_xRobotRedbagQuestion.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotRedbagQuestion.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotRedbagQuestion.Name = field.NewString(tableName, "name")
	_xRobotRedbagQuestion.Token = field.NewString(tableName, "token")
	_xRobotRedbagQuestion.Question = field.NewString(tableName, "question")
	_xRobotRedbagQuestion.LangCode = field.NewString(tableName, "lang_code")
	_xRobotRedbagQuestion.Answer = field.NewString(tableName, "answer")
	_xRobotRedbagQuestion.Expire = field.NewInt32(tableName, "expire")
	_xRobotRedbagQuestion.Points = field.NewString(tableName, "points")
	_xRobotRedbagQuestion.Remark = field.NewString(tableName, "remark")
	_xRobotRedbagQuestion.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotRedbagQuestion.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotRedbagQuestion.fillFieldMap()

	return _xRobotRedbagQuestion
}

type xRobotRedbagQuestion struct {
	xRobotRedbagQuestionDo xRobotRedbagQuestionDo

	ALL        field.Asterisk
	ID         field.Int64  // pk
	SellerID   field.Int32  // 运营商ID
	ChannelID  field.Int32  // 渠道ID
	Name       field.String // 机器人name
	Token      field.String // 机器人token
	Question   field.String // 题目
	LangCode   field.String // 题目语言
	Answer     field.String // 答案
	Expire     field.Int32  // 题目过期时间
	Points     field.String // 积分
	Remark     field.String // 备注
	CreateTime field.Time   // 创建日期
	UpdateTime field.Time   // 更新日期

	fieldMap map[string]field.Expr
}

func (x xRobotRedbagQuestion) Table(newTableName string) *xRobotRedbagQuestion {
	x.xRobotRedbagQuestionDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotRedbagQuestion) As(alias string) *xRobotRedbagQuestion {
	x.xRobotRedbagQuestionDo.DO = *(x.xRobotRedbagQuestionDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotRedbagQuestion) updateTableName(table string) *xRobotRedbagQuestion {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.Name = field.NewString(table, "name")
	x.Token = field.NewString(table, "token")
	x.Question = field.NewString(table, "question")
	x.LangCode = field.NewString(table, "lang_code")
	x.Answer = field.NewString(table, "answer")
	x.Expire = field.NewInt32(table, "expire")
	x.Points = field.NewString(table, "points")
	x.Remark = field.NewString(table, "remark")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotRedbagQuestion) WithContext(ctx context.Context) *xRobotRedbagQuestionDo {
	return x.xRobotRedbagQuestionDo.WithContext(ctx)
}

func (x xRobotRedbagQuestion) TableName() string { return x.xRobotRedbagQuestionDo.TableName() }

func (x xRobotRedbagQuestion) Alias() string { return x.xRobotRedbagQuestionDo.Alias() }

func (x xRobotRedbagQuestion) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotRedbagQuestionDo.Columns(cols...)
}

func (x *xRobotRedbagQuestion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotRedbagQuestion) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["name"] = x.Name
	x.fieldMap["token"] = x.Token
	x.fieldMap["question"] = x.Question
	x.fieldMap["lang_code"] = x.LangCode
	x.fieldMap["answer"] = x.Answer
	x.fieldMap["expire"] = x.Expire
	x.fieldMap["points"] = x.Points
	x.fieldMap["remark"] = x.Remark
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotRedbagQuestion) clone(db *gorm.DB) xRobotRedbagQuestion {
	x.xRobotRedbagQuestionDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotRedbagQuestion) replaceDB(db *gorm.DB) xRobotRedbagQuestion {
	x.xRobotRedbagQuestionDo.ReplaceDB(db)
	return x
}

type xRobotRedbagQuestionDo struct{ gen.DO }

func (x xRobotRedbagQuestionDo) Debug() *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotRedbagQuestionDo) WithContext(ctx context.Context) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotRedbagQuestionDo) ReadDB() *xRobotRedbagQuestionDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotRedbagQuestionDo) WriteDB() *xRobotRedbagQuestionDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotRedbagQuestionDo) Session(config *gorm.Session) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotRedbagQuestionDo) Clauses(conds ...clause.Expression) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotRedbagQuestionDo) Returning(value interface{}, columns ...string) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotRedbagQuestionDo) Not(conds ...gen.Condition) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotRedbagQuestionDo) Or(conds ...gen.Condition) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotRedbagQuestionDo) Select(conds ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotRedbagQuestionDo) Where(conds ...gen.Condition) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotRedbagQuestionDo) Order(conds ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotRedbagQuestionDo) Distinct(cols ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotRedbagQuestionDo) Omit(cols ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotRedbagQuestionDo) Join(table schema.Tabler, on ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotRedbagQuestionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotRedbagQuestionDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotRedbagQuestionDo) Group(cols ...field.Expr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotRedbagQuestionDo) Having(conds ...gen.Condition) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotRedbagQuestionDo) Limit(limit int) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotRedbagQuestionDo) Offset(offset int) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotRedbagQuestionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotRedbagQuestionDo) Unscoped() *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotRedbagQuestionDo) Create(values ...*model.XRobotRedbagQuestion) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotRedbagQuestionDo) CreateInBatches(values []*model.XRobotRedbagQuestion, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotRedbagQuestionDo) Save(values ...*model.XRobotRedbagQuestion) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotRedbagQuestionDo) First() (*model.XRobotRedbagQuestion, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagQuestion), nil
	}
}

func (x xRobotRedbagQuestionDo) Take() (*model.XRobotRedbagQuestion, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagQuestion), nil
	}
}

func (x xRobotRedbagQuestionDo) Last() (*model.XRobotRedbagQuestion, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagQuestion), nil
	}
}

func (x xRobotRedbagQuestionDo) Find() ([]*model.XRobotRedbagQuestion, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotRedbagQuestion), err
}

func (x xRobotRedbagQuestionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotRedbagQuestion, err error) {
	buf := make([]*model.XRobotRedbagQuestion, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotRedbagQuestionDo) FindInBatches(result *[]*model.XRobotRedbagQuestion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotRedbagQuestionDo) Attrs(attrs ...field.AssignExpr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotRedbagQuestionDo) Assign(attrs ...field.AssignExpr) *xRobotRedbagQuestionDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotRedbagQuestionDo) Joins(fields ...field.RelationField) *xRobotRedbagQuestionDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotRedbagQuestionDo) Preload(fields ...field.RelationField) *xRobotRedbagQuestionDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotRedbagQuestionDo) FirstOrInit() (*model.XRobotRedbagQuestion, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagQuestion), nil
	}
}

func (x xRobotRedbagQuestionDo) FirstOrCreate() (*model.XRobotRedbagQuestion, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagQuestion), nil
	}
}

func (x xRobotRedbagQuestionDo) FindByPage(offset int, limit int) (result []*model.XRobotRedbagQuestion, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotRedbagQuestionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotRedbagQuestionDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotRedbagQuestionDo) Delete(models ...*model.XRobotRedbagQuestion) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotRedbagQuestionDo) withDO(do gen.Dao) *xRobotRedbagQuestionDo {
	x.DO = *do.(*gen.DO)
	return x
}
