// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXWhiteBlock(db *gorm.DB, opts ...gen.DOOption) xWhiteBlock {
	_xWhiteBlock := xWhiteBlock{}

	_xWhiteBlock.xWhiteBlockDo.UseDB(db, opts...)
	_xWhiteBlock.xWhiteBlockDo.UseModel(&model.XWhiteBlock{})

	tableName := _xWhiteBlock.xWhiteBlockDo.TableName()
	_xWhiteBlock.ALL = field.NewAsterisk(tableName)
	_xWhiteBlock.ID = field.NewInt32(tableName, "Id")
	_xWhiteBlock.BlockMaker = field.NewString(tableName, "BlockMaker")
	_xWhiteBlock.Info = field.NewString(tableName, "Info")
	_xWhiteBlock.Type = field.NewInt32(tableName, "Type")
	_xWhiteBlock.CreateTime = field.NewTime(tableName, "CreateTime")
	_xWhiteBlock.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xWhiteBlock.fillFieldMap()

	return _xWhiteBlock
}

// xWhiteBlock 区块白名单
type xWhiteBlock struct {
	xWhiteBlockDo xWhiteBlockDo

	ALL        field.Asterisk
	ID         field.Int32
	BlockMaker field.String // 出块者
	Info       field.String // 拉黑信息
	Type       field.Int32  // 1-玩家 2-钱包地址 3-代理 4-运营商 5-渠道 6-区块
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xWhiteBlock) Table(newTableName string) *xWhiteBlock {
	x.xWhiteBlockDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xWhiteBlock) As(alias string) *xWhiteBlock {
	x.xWhiteBlockDo.DO = *(x.xWhiteBlockDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xWhiteBlock) updateTableName(table string) *xWhiteBlock {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.BlockMaker = field.NewString(table, "BlockMaker")
	x.Info = field.NewString(table, "Info")
	x.Type = field.NewInt32(table, "Type")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xWhiteBlock) WithContext(ctx context.Context) *xWhiteBlockDo {
	return x.xWhiteBlockDo.WithContext(ctx)
}

func (x xWhiteBlock) TableName() string { return x.xWhiteBlockDo.TableName() }

func (x xWhiteBlock) Alias() string { return x.xWhiteBlockDo.Alias() }

func (x xWhiteBlock) Columns(cols ...field.Expr) gen.Columns { return x.xWhiteBlockDo.Columns(cols...) }

func (x *xWhiteBlock) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xWhiteBlock) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["BlockMaker"] = x.BlockMaker
	x.fieldMap["Info"] = x.Info
	x.fieldMap["Type"] = x.Type
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xWhiteBlock) clone(db *gorm.DB) xWhiteBlock {
	x.xWhiteBlockDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xWhiteBlock) replaceDB(db *gorm.DB) xWhiteBlock {
	x.xWhiteBlockDo.ReplaceDB(db)
	return x
}

type xWhiteBlockDo struct{ gen.DO }

func (x xWhiteBlockDo) Debug() *xWhiteBlockDo {
	return x.withDO(x.DO.Debug())
}

func (x xWhiteBlockDo) WithContext(ctx context.Context) *xWhiteBlockDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xWhiteBlockDo) ReadDB() *xWhiteBlockDo {
	return x.Clauses(dbresolver.Read)
}

func (x xWhiteBlockDo) WriteDB() *xWhiteBlockDo {
	return x.Clauses(dbresolver.Write)
}

func (x xWhiteBlockDo) Session(config *gorm.Session) *xWhiteBlockDo {
	return x.withDO(x.DO.Session(config))
}

func (x xWhiteBlockDo) Clauses(conds ...clause.Expression) *xWhiteBlockDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xWhiteBlockDo) Returning(value interface{}, columns ...string) *xWhiteBlockDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xWhiteBlockDo) Not(conds ...gen.Condition) *xWhiteBlockDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xWhiteBlockDo) Or(conds ...gen.Condition) *xWhiteBlockDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xWhiteBlockDo) Select(conds ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xWhiteBlockDo) Where(conds ...gen.Condition) *xWhiteBlockDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xWhiteBlockDo) Order(conds ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xWhiteBlockDo) Distinct(cols ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xWhiteBlockDo) Omit(cols ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xWhiteBlockDo) Join(table schema.Tabler, on ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xWhiteBlockDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xWhiteBlockDo) RightJoin(table schema.Tabler, on ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xWhiteBlockDo) Group(cols ...field.Expr) *xWhiteBlockDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xWhiteBlockDo) Having(conds ...gen.Condition) *xWhiteBlockDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xWhiteBlockDo) Limit(limit int) *xWhiteBlockDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xWhiteBlockDo) Offset(offset int) *xWhiteBlockDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xWhiteBlockDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xWhiteBlockDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xWhiteBlockDo) Unscoped() *xWhiteBlockDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xWhiteBlockDo) Create(values ...*model.XWhiteBlock) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xWhiteBlockDo) CreateInBatches(values []*model.XWhiteBlock, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xWhiteBlockDo) Save(values ...*model.XWhiteBlock) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xWhiteBlockDo) First() (*model.XWhiteBlock, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWhiteBlock), nil
	}
}

func (x xWhiteBlockDo) Take() (*model.XWhiteBlock, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWhiteBlock), nil
	}
}

func (x xWhiteBlockDo) Last() (*model.XWhiteBlock, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWhiteBlock), nil
	}
}

func (x xWhiteBlockDo) Find() ([]*model.XWhiteBlock, error) {
	result, err := x.DO.Find()
	return result.([]*model.XWhiteBlock), err
}

func (x xWhiteBlockDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XWhiteBlock, err error) {
	buf := make([]*model.XWhiteBlock, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xWhiteBlockDo) FindInBatches(result *[]*model.XWhiteBlock, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xWhiteBlockDo) Attrs(attrs ...field.AssignExpr) *xWhiteBlockDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xWhiteBlockDo) Assign(attrs ...field.AssignExpr) *xWhiteBlockDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xWhiteBlockDo) Joins(fields ...field.RelationField) *xWhiteBlockDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xWhiteBlockDo) Preload(fields ...field.RelationField) *xWhiteBlockDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xWhiteBlockDo) FirstOrInit() (*model.XWhiteBlock, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWhiteBlock), nil
	}
}

func (x xWhiteBlockDo) FirstOrCreate() (*model.XWhiteBlock, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWhiteBlock), nil
	}
}

func (x xWhiteBlockDo) FindByPage(offset int, limit int) (result []*model.XWhiteBlock, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xWhiteBlockDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xWhiteBlockDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xWhiteBlockDo) Delete(models ...*model.XWhiteBlock) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xWhiteBlockDo) withDO(do gen.Dao) *xWhiteBlockDo {
	x.DO = *do.(*gen.DO)
	return x
}
