package controller

import (
	"encoding/csv"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
)

type ROIController struct {
}

// ROIData 表示ROI数据结构
type ROIData struct {
	ID         int32      `json:"Id"`         // 记录ID
	RecordDate *time.Time `json:"RecordDate"` // 记录日期
	TopAgentID int32      `json:"TopAgentId"` // 顶级代理ID
	// SellerID             int32      `json:"SellerId"`             // 运营商ID
	SellerName string `json:"SellerName"` // 运营商名称
	// ChannelID            int32      `json:"ChannelId"`            // 渠道ID
	ChannelName          string  `json:"ChannelName"`          // 渠道名称
	SpendAmount          float64 `json:"SpendAmount"`          // 消耗金额
	RegUsers             int32   `json:"RegUsers"`             // 注册人数
	RegUsersSpendAmount  float64 `json:"RegUsersSpendAmount"`  // 注册成本
	NewRechargeUsers     int32   `json:"NewRechargeUsers"`     // 新增充值人数
	NewTransferBetUsers  int32   `json:"NewTransferBetUsers"`  // 新增转账人数
	NewPayConversionRate float64 `json:"NewPayConversionRate"` // 新增付费转化率 = (新增充值人数+新增转账人数)/注册人数
	NewRechargeAmount    int32   `json:"NewRechargeAmount"`    // 首充金额
	NewRechargeCost      float64 `json:"NewRechargeCost"`      // 首充成本 = 消耗金额/新增充值人数
	TotalNewUsers        int32   `json:"TotalNewUsers"`        // 总新增人数 新增充值+新增转账人数，同时人数需要去重
	TotalNewCost         float64 `json:"TotalNewCost"`         // 总新增成本 = 消耗金额/总新增人数
	RechargeUsers        int32   `json:"RechargeUsers"`        // 充值人数
	RechargeAmount       float64 `json:"RechargeAmount"`       // 充值金额
	BetAmount            float64 `json:"BetAmount"`            // 余额注单金额
	RechargeBetRatio     float64 `json:"RechargeBetRatio"`     // 充投比 = 余额注单金额/充值金额
	TransferBetUsers     int32   `json:"TransferBetUsers"`     // 转P人数
	TransferProfit       float64 `json:"TransferProfit"`       // 转账盈亏 = 转账注单金额 - 转账派彩金额
	TotalPayUsers        int32   `json:"TotalPayUsers"`        // 总付费人数 充值人数+转账人数，同时去重
	GameProfitAmount     float64 `json:"GameProfitAmount"`     // 游戏盈利 = 余额玩法输赢+转账玩法输赢
	GameProfitRatio      float64 `json:"GameProfitRatio"`      // 游戏盈亏率 = （余额玩法输赢+转账玩法输赢）/（余额玩法投注+转账玩法投注）
	WithdrawAmount       float64 `json:"WithdrawAmount"`       // 提款金额
	CtDiffRatio          float64 `json:"CtDiffRatio"`          // 充提差比 = （充值金额 - 提款金额）/充值金额
	// ROI相关计算字段
	ROI                        float64 `json:"ROI"`                        // ROI = 充值金额+转账输赢-提款-消耗）/消耗
	FirstRechargeAmountPerUser float64 `json:"FirstRechargeAmountPerUser"` // 首充人均充值金额 = 首充金额/首充人数
	FirstDayROI                float64 `json:"FirstDayROI"`                // 首充次日留存率 = 次日充值人数/首充人数
	ThreeDayROI                float64 `json:"ThreeDayROI"`                // 首充3日留存率 = 3日充值人数/首充人数
	SevenDayROI                float64 `json:"SevenDayROI"`                // 首充7日留存率 = 7日充值人数/首充人数
	FirstDayTransferROI        float64 `json:"FirstDayTransferROI"`        // 首次转账次日留存率
	ThreeDayTransferROI        float64 `json:"ThreeDayTransferROI"`        // 首次转账3日留存率
	SevenDayTransferROI        float64 `json:"SevenDayTransferROI"`        // 首次转账7日留存率
	FirstDayPayROI             float64 `json:"FirstDayPayROI"`             // 首次付费次日留存率
	ThreeDayPayROI             float64 `json:"ThreeDayPayROI"`             // 首次付费3日留存率
	SevenDayPayROI             float64 `json:"SevenDayPayROI"`             // 首次付费7日留存率
	SumSpendAmount             float64 `json:"SumSpendAmount"`             // 累计消耗金额
	SumNewRechargeUsers        int32   `json:"SumNewRechargeUsers"`        // 累计首充人数
	SumTotalPayUsers           int32   `json:"SumTotalPayUsers"`           // 累计获客 =累计消耗金额 /累计付费人数
	CumulativeProfitLoss       float64 `json:"CumulativeProfitLoss"`       // 累计盈亏 = 累计盈利-累计亏损
	CumulativeROI              float64 `json:"CumulativeROI"`              // 累计ROI =( 累计盈利-累计消耗 )/ 累计消耗
	TagName                    string  `json:"TagName"`                    // 标签
}

func (c *ROIController) Init() {
	group := server.Http().NewGroup("/api/roi")
	{
		group.Post("/list", c.list)
		group.Post("/update", c.update)
		group.Post("/upload-spend", c.uploadSpend) // 添加上传消耗数据的路由
	}
}

// update 更新ROI数据，只允许更新SpendAmount字段
func (c *ROIController) update(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ID          int32   `json:"id"`          // 记录ID
		SpendAmount float64 `json:"spendAmount"` // 消耗金额
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 验证参数
	if reqdata.ID <= 0 {
		ctx.RespErrString(false, &errcode, "记录ID不能为空")
		return
	}

	if reqdata.SpendAmount < 0 {
		ctx.RespErrString(false, &errcode, "消耗金额不能为负数")
		return
	}

	// 获取token进行权限验证
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "ROI报表", "改"), &errcode, "权限不足") {
		return
	}

	// 获取x_agent_data_date表
	agentDataDateDb := server.DaoxHashGame().XAgentDataDate

	// 构建查询条件
	query := agentDataDateDb.WithContext(ctx.Gin()).
		Where(agentDataDateDb.ID.Eq(reqdata.ID))

	// 查询记录是否存在
	count, err := query.Count()
	if ctx.RespErr(err, &errcode) {
		return
	}

	if count == 0 {
		ctx.RespErrString(false, &errcode, "记录不存在")
		return
	}

	// 更新SpendAmount字段
	_, err = query.Update(agentDataDateDb.SpendAmount, reqdata.SpendAmount)

	if ctx.RespErr(err, &errcode) {
		return
	}

	// 调用存储过程
	_, err = server.Db().CallProcedure("BackManage_agent_data_date_UpdateSpendAmount", reqdata.ID, reqdata.SpendAmount)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 记录操作日志
	server.WriteAdminLog(fmt.Sprintf("更新ROI数据消耗金额，ID: %d, 金额: %.2f",
		reqdata.ID, reqdata.SpendAmount), ctx, reqdata)

	// 返回成功
	ctx.Put("message", "更新成功")
	ctx.RespOK()
}

func (c *ROIController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		StartTime  string
		EndTime    string
		SellerId   int
		ChannelId  int
		Page       int
		PageSize   int
		TopAgentId []int32
		Export     int
		HostTagId  int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.StartTime == "" {
		reqdata.StartTime = "2023-01-01"
	}
	if reqdata.EndTime == "" {
		reqdata.EndTime = "2030-01-01"
	}
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	// 获取token
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "ROI报表", "查"), &errcode, "权限不足") {
		return
	}

	// 转换日期格式
	// 使用本地时区解析日期，确保日期从当地时间的0点开始
	loc, _ := time.LoadLocation("Local")
	startDate, err := time.ParseInLocation("2006-01-02", reqdata.StartTime, loc)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 解析结束日期，并设置为当天的23:59:59
	endDate, err := time.ParseInLocation("2006-01-02", reqdata.EndTime, loc)
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 设置结束时间为23:59:59
	endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, loc)

	// 获取x_agent_data_date表的数据
	agentDataDateDb := server.DaoxHashGame().XAgentDataDate
	xUserDb := server.DaoxHashGame().XUser
	xChannelHostDb := server.DaoxHashGame().XChannelHost
	xHostTagDb := server.DaoxHashGame().XHostTag
	query := agentDataDateDb.WithContext(ctx.Gin())

	// 添加查询条件
	query = query.Where(agentDataDateDb.RecordDate.Gte(startDate))
	query = query.Where(agentDataDateDb.RecordDate.Lt(endDate))

	// 如果是运营商账号，只查询该运营商的数据
	if reqdata.SellerId > 0 {
		query = query.Where(agentDataDateDb.SellerID.Eq(int32(reqdata.SellerId)))
	}

	// 如果是渠道账号，只查询该渠道的数据
	if reqdata.ChannelId > 0 {
		query = query.Where(agentDataDateDb.ChannelID.Eq(int32(reqdata.ChannelId)))
	}

	if len(reqdata.TopAgentId) > 0 {
		query = query.Where(agentDataDateDb.TopAgentID.In(reqdata.TopAgentId...))
	}

	if reqdata.HostTagId > 0 {
		query = query.Where(xHostTagDb.ID.Eq(int32(reqdata.HostTagId)))
	}

	// 查询总数
	total, err := query.
		LeftJoin(xUserDb, agentDataDateDb.TopAgentID.EqCol(xUserDb.UserID)).
		LeftJoin(xChannelHostDb, xUserDb.RegURL.EqCol(xChannelHostDb.Host)).
		LeftJoin(xHostTagDb, xChannelHostDb.HostTagID.EqCol(xHostTagDb.ID)).
		Count()
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 导出时在协程中创建Excel对象

	// 如果是导出，则不分页，获取所有数据
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		limit = 500000
		offset = 0
	}

	// 获取汇总数据（根据查询条件）
	// 创建一个新的查询，使用相同的条件但不分页
	summaryQuery := agentDataDateDb.WithContext(ctx.Gin())
	summaryQuery = summaryQuery.Where(agentDataDateDb.RecordDate.Gte(startDate))
	summaryQuery = summaryQuery.Where(agentDataDateDb.RecordDate.Lt(endDate))

	// 添加其他查询条件
	if reqdata.SellerId > 0 {
		summaryQuery = summaryQuery.Where(agentDataDateDb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if reqdata.ChannelId > 0 {
		summaryQuery = summaryQuery.Where(agentDataDateDb.ChannelID.Eq(int32(reqdata.ChannelId)))
	}
	if len(reqdata.TopAgentId) > 0 {
		summaryQuery = summaryQuery.Where(agentDataDateDb.TopAgentID.In(reqdata.TopAgentId...))
	}
	if reqdata.HostTagId > 0 {
		summaryQuery = summaryQuery.Where(xHostTagDb.ID.Eq(int32(reqdata.HostTagId)))
	}

	// 查询汇总数据
	var summaryResult struct {
		TotalSpendAmount         float64
		TotalRegUsers            int32
		TotalNewRechargeUsers    int32
		TotalNewTransferBetUsers int32
		TotalNewRechargeAmount   int32
		TotalTotalNewUsers       int32
		TotalRechargeUsers       int32
		TotalRechargeAmount      float64
		TotalBetAmount           float64
		TotalTransferBetUsers    int32
		TotalTransferBetAmount   float64
		TotalTransferWinAmount   float64
		TotalTotalPayUsers       int32
		TotalWithdrawAmount      float64
		TotalWinAmount           float64
	}

	// 使用聚合函数查询汇总数据
	err = summaryQuery.Select(
		agentDataDateDb.SpendAmount.Sum().As("total_spend_amount"),
		agentDataDateDb.RegUsers.Sum().As("total_reg_users"),
		agentDataDateDb.NewRechargeUsers.Sum().As("total_new_recharge_users"),
		agentDataDateDb.NewTransferBetUsers.Sum().As("total_new_transfer_bet_users"),
		agentDataDateDb.NewRechargeAmount.Sum().As("total_new_recharge_amount"),
		agentDataDateDb.TotalNewUsers.Sum().As("total_total_new_users"),
		agentDataDateDb.RechargeUsers.Sum().As("total_recharge_users"),
		agentDataDateDb.RechargeAmount.Sum().As("total_recharge_amount"),
		agentDataDateDb.BetAmount.Sum().As("total_bet_amount"),
		agentDataDateDb.TransferBetUsers.Sum().As("total_transfer_bet_users"),
		agentDataDateDb.TransferBetAmount.Sum().As("total_transfer_bet_amount"),
		agentDataDateDb.TransferWinAmount.Sum().As("total_transfer_win_amount"),
		agentDataDateDb.TotalPayUsers.Sum().As("total_total_pay_users"),
		agentDataDateDb.WithdrawAmount.Sum().As("total_withdraw_amount"),
		agentDataDateDb.WinAmount.Sum().As("total_win_amount"),
	).
		LeftJoin(xUserDb, agentDataDateDb.TopAgentID.EqCol(xUserDb.UserID)).
		LeftJoin(xChannelHostDb, xUserDb.RegURL.EqCol(xChannelHostDb.Host)).
		LeftJoin(xHostTagDb, xChannelHostDb.HostTagID.EqCol(xHostTagDb.ID)).
		Scan(&summaryResult)

	if ctx.RespErr(err, &errcode) {
		return
	}

	// 获取运营商和渠道信息
	xSellerDb := server.DaoxHashGame().XSeller
	xChannelDb := server.DaoxHashGame().XChannel

	// 查询分页数据，关联运营商和渠道表
	type ResultData struct {
		model.XAgentDataDate
		SellerName  string
		ChannelName string
		TagName     string
	}
	var result []ResultData
	err = query.Select(agentDataDateDb.ALL, xSellerDb.SellerName, xChannelDb.ChannelName, xHostTagDb.TagName.As("TagName")).
		LeftJoin(xSellerDb, agentDataDateDb.SellerID.EqCol(xSellerDb.SellerID)).
		LeftJoin(xChannelDb, agentDataDateDb.ChannelID.EqCol(xChannelDb.ChannelID)).
		Order(agentDataDateDb.RecordDate.Desc()).
		Offset(offset).
		Limit(limit).
		Scan(&result)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 构建返回数据

	// 将查询结果转换为返回数据
	roiDataList := make([]ROIData, 0, len(result))
	for _, item := range result {
		roiData := ROIData{
			ID: item.ID,
			// 基础数据
			RecordDate: &item.RecordDate,
			TopAgentID: item.TopAgentID,
			// SellerID:            item.SellerID,
			SellerName: item.SellerName,
			// ChannelID:           item.ChannelID,
			ChannelName:         item.ChannelName,
			SpendAmount:         item.SpendAmount,
			RegUsers:            item.RegUsers,
			NewRechargeUsers:    item.NewRechargeUsers,
			NewTransferBetUsers: item.NewTransferBetUsers,
			NewRechargeAmount:   item.NewRechargeAmount,
			TotalNewUsers:       item.TotalNewUsers,
			RechargeUsers:       item.RechargeUsers,
			RechargeAmount:      item.RechargeAmount,
			BetAmount:           item.BetAmount,
			TransferBetUsers:    item.TransferBetUsers,
			TotalPayUsers:       item.TotalPayUsers,
			WithdrawAmount:      item.WithdrawAmount,
			TagName:             item.TagName,

			// 计算字段
			// 注册成本
			RegUsersSpendAmount: calculateCost(
				item.SpendAmount,
				float64(item.RegUsers),
			),

			// 新增付费转化率
			NewPayConversionRate: calculateConversionRate(
				float64(item.TotalNewUsers),
				float64(item.RegUsers),
			),

			// 首充成本
			NewRechargeCost: calculateCost(
				item.SpendAmount,
				float64(item.NewRechargeUsers),
			),

			// 总新增成本
			TotalNewCost: calculateCost(
				item.SpendAmount,
				float64(item.TotalNewUsers),
			),

			// 充投比
			RechargeBetRatio: calculateRatio(
				item.BetAmount,
				item.RechargeAmount,
			),

			// 转账盈亏
			TransferProfit: calculateAmount(
				item.TransferBetAmount - item.TransferWinAmount,
			),

			// 游戏盈利
			GameProfitAmount: calculateAmount(
				item.BetAmount + item.TransferBetAmount - item.WinAmount - item.TransferWinAmount,
			),

			// 游戏盈亏率
			GameProfitRatio: calculateProfitRatio(
				item.BetAmount+item.TransferBetAmount,
				item.WinAmount+item.TransferWinAmount,
			),

			// 充提差比
			CtDiffRatio: calculateRatio(
				item.RechargeAmount-item.WithdrawAmount,
				item.RechargeAmount,
			),

			// ROI = (充值金额 + 转账输赢 - 提款 - 消耗) / 消耗
			ROI: calculateROI(
				item.RechargeAmount,
				item.TransferBetAmount-item.TransferWinAmount,
				item.WithdrawAmount,
				item.SpendAmount,
			),

			// 首充人均充值金额
			FirstRechargeAmountPerUser: calculateCost(
				float64(item.NewRechargeAmount),
				float64(item.NewRechargeUsers),
			),

			// 首充留存率
			FirstDayROI: calculateRetentionRate(
				item.NewRechargeUsers2,
				float64(item.NewRechargeUsers),
			),
			ThreeDayROI: calculateRetentionRate(
				item.NewRechargeUsers3,
				float64(item.NewRechargeUsers),
			),
			SevenDayROI: calculateRetentionRate(
				item.NewRechargeUsers7,
				float64(item.NewRechargeUsers),
			),

			// 首次转账留存率
			FirstDayTransferROI: calculateRetentionRate(
				float64(item.NewTransferBetUsers2),
				float64(item.NewTransferBetUsers),
			),
			ThreeDayTransferROI: calculateRetentionRate(
				float64(item.NewTransferBetUsers3),
				float64(item.NewTransferBetUsers),
			),
			SevenDayTransferROI: calculateRetentionRate(
				float64(item.NewTransferBetUsers7),
				float64(item.NewTransferBetUsers),
			),

			// 首次付费留存率
			FirstDayPayROI: calculateRetentionRate(
				float64(item.TotalPayUsers2),
				float64(item.TotalPayUsers),
			),
			ThreeDayPayROI: calculateRetentionRate(
				float64(item.TotalPayUsers3),
				float64(item.TotalPayUsers),
			),
			SevenDayPayROI: calculateRetentionRate(
				float64(item.TotalPayUsers7),
				float64(item.TotalPayUsers),
			),

			// 累计数据暂时设置为0
			SumSpendAmount:      item.SumSpendAmount,
			SumNewRechargeUsers: item.SumNewRechargeUsers,
			SumTotalPayUsers: calculateTotalPayUsers(
				item.SumSpendAmount,
				item.SumNewRechargeUsers,
			),
			// 累计盈亏 = 累计盈利 - 累计亏损
			CumulativeProfitLoss: calculateCumulativeProfitLoss(
				item.SumBetAmount,
				item.SumTransferBetAmount,
				item.SumWinAmount,
				item.SumTransferWinAmount,
			),
			CumulativeROI: calculateCumulativeROI(
				item.SumBetAmount,
				item.SumTransferBetAmount,
				item.SumWinAmount,
				item.SumTransferWinAmount,
				item.SumSpendAmount,
			),
		}
		roiDataList = append(roiDataList, roiData)
	}

	// 创建汇总数据
	summaryData := ROIData{
		ID:         -1,
		RecordDate: nil,
		TopAgentID: -1, // 使用-1表示汇总数据
		// SellerID:   -1,
		SellerName: "",
		// ChannelID:  -1,
		ChannelName: "",

		// 设置基础字段
		SpendAmount:         summaryResult.TotalSpendAmount,
		RegUsers:            summaryResult.TotalRegUsers,
		NewRechargeUsers:    summaryResult.TotalNewRechargeUsers,
		NewTransferBetUsers: summaryResult.TotalNewTransferBetUsers,
		NewRechargeAmount:   summaryResult.TotalNewRechargeAmount,
		TotalNewUsers:       summaryResult.TotalTotalNewUsers,
		RechargeUsers:       summaryResult.TotalRechargeUsers,
		RechargeAmount:      summaryResult.TotalRechargeAmount,
		BetAmount:           summaryResult.TotalBetAmount,
		TransferBetUsers:    summaryResult.TotalTransferBetUsers,
		TotalPayUsers:       summaryResult.TotalTotalPayUsers,
		WithdrawAmount:      summaryResult.TotalWithdrawAmount,

		// 计算字段
		// 注册成本
		RegUsersSpendAmount: calculateCost(
			summaryResult.TotalSpendAmount,
			float64(summaryResult.TotalRegUsers),
		),

		// 新增付费转化率
		NewPayConversionRate: calculateConversionRate(
			float64(summaryResult.TotalTotalNewUsers),
			float64(summaryResult.TotalRegUsers),
		),

		// 首充成本
		NewRechargeCost: calculateCost(
			summaryResult.TotalSpendAmount,
			float64(summaryResult.TotalNewRechargeUsers),
		),

		// 总新增成本
		TotalNewCost: calculateCost(
			summaryResult.TotalSpendAmount,
			float64(summaryResult.TotalTotalNewUsers),
		),

		// 充投比
		RechargeBetRatio: calculateRatio(
			summaryResult.TotalBetAmount,
			summaryResult.TotalRechargeAmount,
		),

		// 转账盈亏
		TransferProfit: calculateAmount(
			summaryResult.TotalTransferBetAmount - summaryResult.TotalTransferWinAmount,
		),

		// 游戏盈利
		GameProfitAmount: calculateAmount(
			summaryResult.TotalBetAmount + summaryResult.TotalTransferBetAmount - summaryResult.TotalWinAmount - summaryResult.TotalTransferWinAmount,
		),

		// 游戏盈亏率
		GameProfitRatio: calculateProfitRatio(
			summaryResult.TotalBetAmount+summaryResult.TotalTransferBetAmount,
			summaryResult.TotalWinAmount+summaryResult.TotalTransferWinAmount,
		),

		// 充提差比
		CtDiffRatio: calculateRatio(
			summaryResult.TotalRechargeAmount-summaryResult.TotalWithdrawAmount,
			summaryResult.TotalRechargeAmount,
		),

		// ROI = (充值金额 + 转账输赢 - 提款 - 消耗) / 消耗
		ROI: calculateROI(
			summaryResult.TotalRechargeAmount,
			summaryResult.TotalTransferBetAmount-summaryResult.TotalTransferWinAmount,
			summaryResult.TotalWithdrawAmount,
			summaryResult.TotalSpendAmount,
		),

		// 首充人均充值金额
		FirstRechargeAmountPerUser: calculateCost(
			float64(summaryResult.TotalNewRechargeAmount),
			float64(summaryResult.TotalNewRechargeUsers),
		),

		// 留存率相关字段在汇总数据中不太有意义，设为0
		FirstDayROI:         0,
		ThreeDayROI:         0,
		SevenDayROI:         0,
		FirstDayTransferROI: 0,
		ThreeDayTransferROI: 0,
		SevenDayTransferROI: 0,
		FirstDayPayROI:      0,
		ThreeDayPayROI:      0,
		SevenDayPayROI:      0,

		// 累计数据
		SumSpendAmount:       0,
		SumNewRechargeUsers:  0,
		SumTotalPayUsers:     0,
		CumulativeProfitLoss: 0,
		CumulativeROI:        0,
	}

	// 添加汇总数据到列表
	if len(roiDataList) > 0 {
		roiDataList = append(roiDataList, summaryData)
	}
	// roiDataList = append(roiDataList, summaryData)

	// 如果是导出，则写入Excel
	if reqdata.Export == 1 {
		// 创建文件名
		fileName := fmt.Sprintf("export_ROI数据_%s.xlsx", time.Now().Format("20060102150405"))

		// 使用协程处理Excel导出
		go func() {
			defer func() {
				if r := recover(); r != nil {
					fmt.Println("导出Excel时发生错误:", r)
				}
			}()

			// 创建一个新的Excel对象，用于协程中处理
			exportXlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), strings.TrimSuffix(fileName, ".xlsx"))
			defer exportXlsx.Close()

			// 打开Excel文件
			exportXlsx.Open()

			// 设置表头
			exportXlsx.SetTitle("RecordDate", "记录日期")
			exportXlsx.SetTitle("TopAgentId", "顶级代理ID")
			// exportXlsx.SetTitle("SellerId", "运营商ID")
			exportXlsx.SetTitle("SellerName", "运营商名称")
			// exportXlsx.SetTitle("ChannelId", "渠道ID")
			exportXlsx.SetTitle("ChannelName", "渠道名称")
			exportXlsx.SetTitle("SpendAmount", "消耗金额")
			exportXlsx.SetTitle("RegUsers", "注册人数")
			exportXlsx.SetTitle("RegUsersSpendAmount", "注册成本")
			exportXlsx.SetTitle("NewRechargeUsers", "新增充值人数")
			exportXlsx.SetTitle("NewTransferBetUsers", "新增转账人数")
			exportXlsx.SetTitle("NewPayConversionRate", "新增付费转化率")
			exportXlsx.SetTitle("NewRechargeAmount", "首充金额")
			exportXlsx.SetTitle("NewRechargeCost", "首充成本")
			exportXlsx.SetTitle("TotalNewUsers", "总新增人数")
			exportXlsx.SetTitle("TotalNewCost", "总新增成本")
			exportXlsx.SetTitle("RechargeUsers", "充值人数")
			exportXlsx.SetTitle("RechargeAmount", "充值金额")
			exportXlsx.SetTitle("BetAmount", "余额注单金额")
			exportXlsx.SetTitle("RechargeBetRatio", "充投比")
			exportXlsx.SetTitle("TransferBetUsers", "转账人数")
			exportXlsx.SetTitle("TransferProfit", "转账盈亏")
			exportXlsx.SetTitle("TotalPayUsers", "总付费人数")
			exportXlsx.SetTitle("GameProfitAmount", "游戏盈利")
			exportXlsx.SetTitle("GameProfitRatio", "游戏盈亏率")
			exportXlsx.SetTitle("WithdrawAmount", "提款金额")
			exportXlsx.SetTitle("CtDiffRatio", "充提差比")
			exportXlsx.SetTitle("ROI", "ROI")
			exportXlsx.SetTitle("FirstRechargeAmountPerUser", "首充人均充值金额")
			exportXlsx.SetTitle("FirstDayROI", "首充次日留存率")
			exportXlsx.SetTitle("ThreeDayROI", "首充3日留存率")
			exportXlsx.SetTitle("SevenDayROI", "首充7日留存率")
			exportXlsx.SetTitle("FirstDayTransferROI", "首次转账次日留存率")
			exportXlsx.SetTitle("ThreeDayTransferROI", "首次转账3日留存率")
			exportXlsx.SetTitle("SevenDayTransferROI", "首次转账7日留存率")
			exportXlsx.SetTitle("FirstDayPayROI", "首次付费次日留存率")
			exportXlsx.SetTitle("ThreeDayPayROI", "首次付费3日留存率")
			exportXlsx.SetTitle("SevenDayPayROI", "首次付费7日留存率")
			exportXlsx.SetTitle("SumSpendAmount", "累计消耗金额")
			exportXlsx.SetTitle("SumNewRechargeUsers", "累计首充人数")
			exportXlsx.SetTitle("SumTotalPayUsers", "累计获客人数")
			exportXlsx.SetTitle("CumulativeProfitLoss", "累计盈亏")
			exportXlsx.SetTitle("CumulativeROI", "累计ROI")
			exportXlsx.SetTitle("TagName", "标签")

			// 设置表头样式
			exportXlsx.SetTitleStyle()

			// 设置列宽
			exportXlsx.SetColumnWidth("RecordDate", 20)
			exportXlsx.SetColumnWidth("TopAgentId", 15)
			// exportXlsx.SetColumnWidth("SellerId", 15)
			exportXlsx.SetColumnWidth("SellerName", 20)
			// exportXlsx.SetColumnWidth("ChannelId", 15)
			exportXlsx.SetColumnWidth("ChannelName", 20)
			exportXlsx.SetColumnWidth("SpendAmount", 15)
			exportXlsx.SetColumnWidth("RegUsersSpendAmount", 15)
			exportXlsx.SetColumnWidth("NewPayConversionRate", 15)
			exportXlsx.SetColumnWidth("NewRechargeCost", 15)
			exportXlsx.SetColumnWidth("TotalNewCost", 15)
			exportXlsx.SetColumnWidth("RechargeBetRatio", 15)
			exportXlsx.SetColumnWidth("TransferProfit", 15)
			exportXlsx.SetColumnWidth("GameProfitAmount", 15)
			exportXlsx.SetColumnWidth("GameProfitRatio", 15)
			exportXlsx.SetColumnWidth("CtDiffRatio", 15)
			exportXlsx.SetColumnWidth("ROI", 15)
			exportXlsx.SetColumnWidth("FirstRechargeAmountPerUser", 20)
			exportXlsx.SetColumnWidth("SumSpendAmount", 20)
			exportXlsx.SetColumnWidth("SumNewRechargeUsers", 20)
			exportXlsx.SetColumnWidth("SumTotalPayUsers", 20)
			exportXlsx.SetColumnWidth("CumulativeProfitLoss", 20)
			exportXlsx.SetColumnWidth("CumulativeROI", 15)
			exportXlsx.SetColumnWidth("TagName", 20)

			// 使用多个协程并行处理数据写入
			// 计算每个协程处理的数据量
			dataLen := len(roiDataList)
			numGoroutines := 4 // 使用4个协程
			if dataLen < 1000 {
				numGoroutines = 1 // 数据量小时使用单协程
			}

			chunkSize := dataLen / numGoroutines
			if dataLen%numGoroutines != 0 {
				chunkSize++
			}

			var wg sync.WaitGroup
			wg.Add(numGoroutines)

			// 创建一个互斥锁，用于保护Excel写入操作
			var mu sync.Mutex

			// 启动多个协程处理数据
			for i := 0; i < numGoroutines; i++ {
				start := i * chunkSize
				end := (i + 1) * chunkSize
				if end > dataLen {
					end = dataLen
				}

				go func(start, end int) {
					defer wg.Done()

					for j := start; j < end; j++ {
						item := roiDataList[j]
						rowIndex := int64(j + 2) // 从第2行开始写入数据（第1行是表头）

						// 使用互斥锁保护Excel写入操作
						mu.Lock()
						// 写入每一列的数据
						if item.RecordDate != nil {
							exportXlsx.SetValue("RecordDate", item.RecordDate.Format("2006-01-02"), rowIndex)
						} else {
							exportXlsx.SetValue("RecordDate", "汇总", rowIndex)
						}
						exportXlsx.SetValue("TopAgentId", item.TopAgentID, rowIndex)
						// exportXlsx.SetValue("SellerId", item.SellerID, rowIndex)
						exportXlsx.SetValue("SellerName", item.SellerName, rowIndex)
						// exportXlsx.SetValue("ChannelId", item.ChannelID, rowIndex)
						exportXlsx.SetValue("ChannelName", item.ChannelName, rowIndex)
						exportXlsx.SetValue("SpendAmount", item.SpendAmount, rowIndex)
						exportXlsx.SetValue("RegUsers", item.RegUsers, rowIndex)
						exportXlsx.SetValue("RegUsersSpendAmount", item.RegUsersSpendAmount, rowIndex)
						exportXlsx.SetValue("NewRechargeUsers", item.NewRechargeUsers, rowIndex)
						exportXlsx.SetValue("NewTransferBetUsers", item.NewTransferBetUsers, rowIndex)
						exportXlsx.SetValue("NewPayConversionRate", item.NewPayConversionRate, rowIndex)
						exportXlsx.SetValue("NewRechargeAmount", item.NewRechargeAmount, rowIndex)
						exportXlsx.SetValue("NewRechargeCost", item.NewRechargeCost, rowIndex)
						exportXlsx.SetValue("TotalNewUsers", item.TotalNewUsers, rowIndex)
						exportXlsx.SetValue("TotalNewCost", item.TotalNewCost, rowIndex)
						exportXlsx.SetValue("RechargeUsers", item.RechargeUsers, rowIndex)
						exportXlsx.SetValue("RechargeAmount", item.RechargeAmount, rowIndex)
						exportXlsx.SetValue("BetAmount", item.BetAmount, rowIndex)
						exportXlsx.SetValue("RechargeBetRatio", item.RechargeBetRatio, rowIndex)
						exportXlsx.SetValue("TransferBetUsers", item.TransferBetUsers, rowIndex)
						exportXlsx.SetValue("TransferProfit", item.TransferProfit, rowIndex)
						exportXlsx.SetValue("TotalPayUsers", item.TotalPayUsers, rowIndex)
						exportXlsx.SetValue("GameProfitAmount", item.GameProfitAmount, rowIndex)
						exportXlsx.SetValue("GameProfitRatio", item.GameProfitRatio, rowIndex)
						exportXlsx.SetValue("WithdrawAmount", item.WithdrawAmount, rowIndex)
						exportXlsx.SetValue("CtDiffRatio", item.CtDiffRatio, rowIndex)
						exportXlsx.SetValue("ROI", item.ROI, rowIndex)
						exportXlsx.SetValue("FirstRechargeAmountPerUser", item.FirstRechargeAmountPerUser, rowIndex)
						exportXlsx.SetValue("FirstDayROI", item.FirstDayROI, rowIndex)
						exportXlsx.SetValue("ThreeDayROI", item.ThreeDayROI, rowIndex)
						exportXlsx.SetValue("SevenDayROI", item.SevenDayROI, rowIndex)
						exportXlsx.SetValue("FirstDayTransferROI", item.FirstDayTransferROI, rowIndex)
						exportXlsx.SetValue("ThreeDayTransferROI", item.ThreeDayTransferROI, rowIndex)
						exportXlsx.SetValue("SevenDayTransferROI", item.SevenDayTransferROI, rowIndex)
						exportXlsx.SetValue("FirstDayPayROI", item.FirstDayPayROI, rowIndex)
						exportXlsx.SetValue("ThreeDayPayROI", item.ThreeDayPayROI, rowIndex)
						exportXlsx.SetValue("SevenDayPayROI", item.SevenDayPayROI, rowIndex)
						exportXlsx.SetValue("SumSpendAmount", item.SumSpendAmount, rowIndex)
						exportXlsx.SetValue("SumNewRechargeUsers", item.SumNewRechargeUsers, rowIndex)
						exportXlsx.SetValue("SumTotalPayUsers", item.SumTotalPayUsers, rowIndex)
						exportXlsx.SetValue("CumulativeProfitLoss", item.CumulativeProfitLoss, rowIndex)
						exportXlsx.SetValue("CumulativeROI", item.CumulativeROI, rowIndex)
						exportXlsx.SetValue("TagName", item.TagName, rowIndex)
						mu.Unlock()
					}
				}(start, end)
			}

			// 等待所有协程完成
			wg.Wait()

			// 设置单元格样式
			exportXlsx.SetValueStyle(int64(len(roiDataList) + 1))

			// 生成文件
			filePath, err := exportXlsx.ProduceFile()
			if err != nil {
				fmt.Println("生成Excel文件失败:", err)
				return
			}

			// 文件已经生成，记录日志即可

			// 记录导出完成的日志
			fmt.Println("ROI数据导出完成，文件路径:", filePath)
		}()

		// 立即返回响应，告知前端导出任务已开始，并返回文件路径
		ctx.Put("message", "导出任务已开始，请稍后下载文件")
		ctx.Put("filename", fmt.Sprintf("/exports/%s", fileName))
		ctx.RespOK()
		server.WriteAdminLog("ROI数据导出任务已开始", ctx, reqdata)
	} else {
		// 普通查询返回JSON数据
		ctx.Put("data", roiDataList)
		ctx.Put("total", total)
		ctx.RespOK()
		server.WriteAdminLog("ROI数据查询", ctx, reqdata)
	}
}

// 计算ROI，保留4位小数
// ROI = (充值金额 + 转账输赢 - 提款 - 消耗) / 消耗
func calculateROI(rechargeAmount, transferProfit, withdrawAmount, spendAmount float64) float64 {
	if spendAmount == 0 {
		return 0
	}
	// 计算ROI并四舍五入到4位小数
	roi := (rechargeAmount + transferProfit - withdrawAmount - spendAmount) / spendAmount
	// 使用math.Round函数四舍五入到4位小数
	return math.Round(roi*10000) / 10000
}

// 计算留存率，保留4位小数
func calculateRetentionRate(retainedUsers, totalUsers float64) float64 {
	if totalUsers == 0 {
		return 0
	}
	// 计算留存率并四舍五入到4位小数
	rate := retainedUsers / totalUsers
	// 使用math.Round函数四舍五入到4位小数
	return math.Round(rate*10000) / 10000
}

// 计算转化率，保留4位小数
func calculateConversionRate(convertedUsers, totalUsers float64) float64 {
	logs.Info("convertedUsers:", convertedUsers, "totalUsers:", totalUsers)
	if totalUsers > convertedUsers {
		return 1
	}

	if totalUsers == 0 || (totalUsers == 0 && convertedUsers == 0) {
		return 0
	}

	// 计算转化率并四舍五入到4位小数
	rate := convertedUsers / totalUsers
	// 使用math.Round函数四舍五入到4位小数
	return math.Round(rate*10000) / 10000
}

// 计算成本，保留2位小数
func calculateCost(amount, users float64) float64 {
	if users == 0 {
		return 0
	}
	// 计算成本并四舍五入到2位小数
	cost := amount / users
	// 使用math.Round函数四舍五入到2位小数
	return math.Round(cost*100) / 100
}

// 计算比率，保留4位小数
func calculateRatio(numerator, denominator float64) float64 {
	if denominator == 0 {
		return 0
	}
	// 计算比率并四舍五入到4位小数
	ratio := numerator / denominator
	// 使用math.Round函数四舍五入到4位小数
	return math.Round(ratio*10000) / 10000
}

// 计算金额，保留2位小数
func calculateAmount(amount float64) float64 {
	// 四舍五入到2位小数
	return math.Round(amount*100) / 100
}

// 计算盈亏率，保留4位小数
func calculateProfitRatio(totalBet, totalWin float64) float64 {
	if totalBet == 0 {
		return 0
	}
	// 计算盈亏率并四舍五入到4位小数
	ratio := (totalBet - totalWin) / totalBet
	// 使用math.Round函数四舍五入到4位小数
	return math.Round(ratio*10000) / 10000
}

// uploadSpend 上传消耗数据CSV文件，批量更新SpendAmount字段
func (c *ROIController) uploadSpend(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 获取token进行权限验证
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "报表统计", "ROI报表", "改"), &errcode, "权限不足") {
		return
	}

	// 获取上传的文件
	file, header, err := ctx.Gin().Request.FormFile("file")
	if err != nil {
		ctx.RespErrString(false, &errcode, "获取上传文件失败: "+err.Error())
		return
	}
	defer file.Close()

	// 验证文件类型
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".csv") {
		ctx.RespErrString(false, &errcode, "文件格式错误，请上传CSV文件")
		return
	}

	// 解析CSV文件
	records, err := c.parseCSVFile(file)
	if err != nil {
		ctx.RespErrString(false, &errcode, "解析CSV文件失败: "+err.Error())
		return
	}

	// 验证CSV数据格式
	if len(records) < 2 {
		ctx.RespErrString(false, &errcode, "CSV文件内容为空，至少需要2行数据（包含表头）")
		return
	}

	// 忽略表头，直接从第二行开始处理数据
	// 验证第一行数据的列数是否足够
	if len(records) > 1 && len(records[1]) < 3 {
		ctx.RespErrString(false, &errcode, "CSV文件数据格式错误，每行至少需要3列：日期,渠道号,消耗金额 或 4列：日期,渠道号,顶级代理ID,消耗金额")
		return
	}

	fmt.Printf("CSV文件解析成功，共%d行数据（包含表头），将处理%d行数据\n", len(records), len(records)-1)

	// 处理数据行
	var updateCount int
	var errorCount int
	var errorMessages []string

	agentDataDateDb := server.DaoxHashGame().XAgentDataDate

	for i, record := range records[1:] {
		if len(record) < 3 {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行数据不完整", i+2))
			continue
		}

		// 解析日期
		dateStr := strings.TrimSpace(record[0])
		recordDate, err := time.Parse("20060102", dateStr)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行日期格式错误: %s", i+2, dateStr))
			continue
		}

		// 使用本地时区，确保日期时间为当天的 00:00:00
		loc, _ := time.LoadLocation("Local")
		recordDate = time.Date(recordDate.Year(), recordDate.Month(), recordDate.Day(), 0, 0, 0, 0, loc)

		// 添加调试信息，显示实际使用的日期时间
		fmt.Printf("第%d行：原始日期字符串: %s, 解析后的日期时间: %s\n", i+2, dateStr, recordDate.Format("2006-01-02 15:04:05"))

		// 解析渠道号
		channelStr := strings.TrimSpace(record[1])
		channelID, err := strconv.ParseInt(channelStr, 10, 32)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行渠道号格式错误: %s", i+2, channelStr))
			continue
		}

		// 解析顶级代理ID（如果有第4列）
		var topAgentID int32 = 0
		var hasTopAgent bool = false
		var spendStr string

		if len(record) >= 4 {
			// 4列格式：日期,渠道号,顶级代理ID,消耗金额
			topAgentStr := strings.TrimSpace(record[2])
			if topAgentStr != "" {
				topAgentIDInt64, err := strconv.ParseInt(topAgentStr, 10, 32)
				if err != nil {
					errorCount++
					errorMessages = append(errorMessages, fmt.Sprintf("第%d行顶级代理ID格式错误: %s", i+2, topAgentStr))
					continue
				}
				topAgentID = int32(topAgentIDInt64)
				hasTopAgent = true
			}
			spendStr = strings.TrimSpace(record[3])
		} else {
			// 3列格式：日期,渠道号,消耗金额
			spendStr = strings.TrimSpace(record[2])
		}

		// 解析消耗金额
		spendAmount, err := strconv.ParseFloat(spendStr, 64)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行消耗金额格式错误: %s", i+2, spendStr))
			continue
		}

		if spendAmount < 0 {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行消耗金额不能为负数: %f", i+2, spendAmount))
			continue
		}

		// 先查询记录获取ID
		if hasTopAgent {
			fmt.Printf("第%d行：准备查询记录 - 日期: %s, 渠道: %d, 顶级代理: %d\n", i+2, recordDate.Format("2006-01-02 15:04:05"), channelID, topAgentID)
		} else {
			fmt.Printf("第%d行：准备查询记录 - 日期: %s, 渠道: %d\n", i+2, recordDate.Format("2006-01-02 15:04:05"), channelID)
		}

		// 构建查询条件
		query := agentDataDateDb.WithContext(ctx.Gin()).
			Select(agentDataDateDb.ID).
			Where(agentDataDateDb.RecordDate.Eq(recordDate)).
			Where(agentDataDateDb.ChannelID.Eq(int32(channelID)))

		// 如果有顶级代理ID，添加顶级代理条件
		if hasTopAgent {
			query = query.Where(agentDataDateDb.TopAgentID.Eq(topAgentID))
		}

		// 查询记录获取ID
		var recordID int64
		err = query.Scan(&recordID)

		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行查询记录失败: %s", i+2, err.Error()))
			fmt.Printf("第%d行：查询记录错误: %s\n", i+2, err.Error())
			continue
		}

		if recordID == 0 {
			if hasTopAgent {
				errorCount++
				errorMessages = append(errorMessages, fmt.Sprintf("第%d行未找到匹配的记录 (日期: %s, 渠道: %d, 顶级代理: %d)", i+2, dateStr, channelID, topAgentID))
				fmt.Printf("第%d行：未找到匹配记录 - 日期: %s, 渠道: %d, 顶级代理: %d\n", i+2, recordDate.Format("2006-01-02 15:04:05"), channelID, topAgentID)
			} else {
				errorCount++
				errorMessages = append(errorMessages, fmt.Sprintf("第%d行未找到匹配的记录 (日期: %s, 渠道: %d)", i+2, dateStr, channelID))
				fmt.Printf("第%d行：未找到匹配记录 - 日期: %s, 渠道: %d\n", i+2, recordDate.Format("2006-01-02 15:04:05"), channelID)
			}
			continue
		}

		if hasTopAgent {
			fmt.Printf("第%d行：找到记录ID: %d，准备更新数据库 - 日期: %s, 渠道: %d, 顶级代理: %d, 金额: %.2f\n", i+2, recordID, recordDate.Format("2006-01-02 15:04:05"), channelID, topAgentID, spendAmount)
		} else {
			fmt.Printf("第%d行：找到记录ID: %d，准备更新数据库 - 日期: %s, 渠道: %d, 金额: %.2f\n", i+2, recordID, recordDate.Format("2006-01-02 15:04:05"), channelID, spendAmount)
		}

		// 构建更新查询条件
		updateQuery := agentDataDateDb.WithContext(ctx.Gin()).
			Where(agentDataDateDb.RecordDate.Eq(recordDate)).
			Where(agentDataDateDb.ChannelID.Eq(int32(channelID)))

		// 如果有顶级代理ID，添加顶级代理条件
		if hasTopAgent {
			updateQuery = updateQuery.Where(agentDataDateDb.TopAgentID.Eq(topAgentID))
		}

		// 更新数据库
		result, err := updateQuery.Update(agentDataDateDb.SpendAmount, spendAmount)

		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行更新失败: %s", i+2, err.Error()))
			fmt.Printf("第%d行：数据库更新错误: %s\n", i+2, err.Error())
			continue
		}

		// 调用存储过程，使用查询到的记录ID
		fmt.Printf("第%d行：调用存储过程 - ID: %d, 金额: %.2f\n", i+2, recordID, spendAmount)
		_, err = server.Db().CallProcedure("BackManage_agent_data_date_UpdateSpendAmount", recordID, spendAmount)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行调用存储过程失败: %s", i+2, err.Error()))
			fmt.Printf("第%d行：调用存储过程错误: %s\n", i+2, err.Error())
			continue
		}

		// if result.RowsAffected == 0 {
		// 	errorCount++
		// 	errorMessages = append(errorMessages, fmt.Sprintf("第%d行未找到匹配的记录 (日期: %s, 渠道: %d)", i+2, dateStr, channelID))
		// 	fmt.Printf("第%d行：未找到匹配记录 - 日期: %s, 渠道: %d\n", i+2, recordDate.Format("2006-01-02 15:04:05"), channelID)
		// 	continue
		// }

		fmt.Printf("第%d行：更新成功 - 影响行数: %d\n", i+2, result.RowsAffected)

		updateCount++
	}

	// 记录操作日志
	server.WriteAdminLog(fmt.Sprintf("批量上传消耗数据，文件: %s, 成功: %d条, 失败: %d条",
		header.Filename, updateCount, errorCount), ctx, nil)

	// 返回结果
	ctx.Put("message", "上传处理完成")
	ctx.Put("updateCount", updateCount)
	ctx.Put("errorCount", errorCount)
	if len(errorMessages) > 0 {
		ctx.Put("errors", errorMessages)
	}
	ctx.RespOK()
}

// parseCSVFile 解析CSV文件，支持多种编码格式
func (c *ROIController) parseCSVFile(file multipart.File) ([][]string, error) {
	// 重置文件指针到开头
	file.Seek(0, 0)

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 尝试检测和转换编码
	var reader *csv.Reader

	// 检查是否为UTF-8编码
	if utf8.Valid(content) {
		reader = csv.NewReader(strings.NewReader(string(content)))
	} else {
		// 尝试GBK编码转换
		gbkContent, err := c.convertGBKToUTF8(content)
		if err != nil {
			// 如果GBK转换失败，尝试直接使用原内容
			reader = csv.NewReader(strings.NewReader(string(content)))
		} else {
			reader = csv.NewReader(strings.NewReader(gbkContent))
		}
	}

	// 读取所有记录
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("解析CSV失败: %v", err)
	}

	return records, nil
}

// convertGBKToUTF8 将GBK编码转换为UTF-8
func (c *ROIController) convertGBKToUTF8(gbkData []byte) (string, error) {
	// 尝试多种编码转换方式

	// 方法1：直接转换为字符串
	str := string(gbkData)

	// 方法2：尝试检测BOM并处理
	if len(gbkData) >= 3 {
		// 检查UTF-8 BOM
		if gbkData[0] == 0xEF && gbkData[1] == 0xBB && gbkData[2] == 0xBF {
			return string(gbkData[3:]), nil
		}
		// 检查UTF-16 BOM
		if (gbkData[0] == 0xFF && gbkData[1] == 0xFE) || (gbkData[0] == 0xFE && gbkData[1] == 0xFF) {
			return string(gbkData[2:]), nil
		}
	}

	// 方法3：尝试替换常见的乱码字符
	str = c.fixCommonEncodingIssues(str)

	// 直接返回处理后的字符串
	return str, nil
}

// fixCommonEncodingIssues 修复常见的编码问题
func (c *ROIController) fixCommonEncodingIssues(str string) string {
	// 替换常见的乱码模式
	replacements := map[string]string{
		"??":   "",
		"???":  "",
		"????": "",
	}

	for old, new := range replacements {
		str = strings.ReplaceAll(str, old, new)
	}

	return str
}

// calculateCumulativeProfitLoss 计算累计盈亏，保留2位小数
// 累计盈亏 = 累计盈利 - 累计亏损 = (累计余额注单 + 累计转账注单) - (累计余额派彩 + 累计转账派彩)
func calculateCumulativeProfitLoss(sumBetAmount, sumTransferBetAmount, sumWinAmount, sumTransferWinAmount float64) float64 {
	// 计算累计盈亏
	profitLoss := (sumBetAmount + sumTransferBetAmount) - (sumWinAmount + sumTransferWinAmount)
	// 四舍五入到2位小数
	return math.Round(profitLoss*100) / 100
}

// calculateCumulativeROI 计算累计ROI，保留4位小数
// 累计ROI = 累计盈利 / 累计消耗
func calculateCumulativeROI(sumBetAmount, sumTransferBetAmount, sumWinAmount, sumTransferWinAmount, sumSpendAmount float64) float64 {
	if sumSpendAmount == 0 {
		return 0
	}
	profitLoss := (sumBetAmount + sumTransferBetAmount) - (sumWinAmount + sumTransferWinAmount) - sumSpendAmount
	// 计算累计ROI
	cumulativeROI := profitLoss / sumSpendAmount
	// 使用math.Round函数四舍五入到4位小数
	return math.Round(cumulativeROI*10000) / 10000
}

func calculateTotalPayUsers(sumSpendAmount float64, sumNewRechargeUsers int32) int32 {
	if sumNewRechargeUsers == 0 {
		return 1
	}
	return int32(math.Round(sumSpendAmount / float64(sumNewRechargeUsers)))
}
