// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXWithdrawGame(db *gorm.DB, opts ...gen.DOOption) xWithdrawGame {
	_xWithdrawGame := xWithdrawGame{}

	_xWithdrawGame.xWithdrawGameDo.UseDB(db, opts...)
	_xWithdrawGame.xWithdrawGameDo.UseModel(&model.XWithdrawGame{})

	tableName := _xWithdrawGame.xWithdrawGameDo.TableName()
	_xWithdrawGame.ALL = field.NewAsterisk(tableName)
	_xWithdrawGame.OrderID = field.NewInt32(tableName, "OrderId")
	_xWithdrawGame.Brand = field.NewString(tableName, "Brand")
	_xWithdrawGame.GameID = field.NewString(tableName, "GameId")
	_xWithdrawGame.Symbol = field.NewString(tableName, "Symbol")
	_xWithdrawGame.GameName = field.NewString(tableName, "GameName")
	_xWithdrawGame.GameType = field.NewInt32(tableName, "GameType")
	_xWithdrawGame.StartDate = field.NewTime(tableName, "StartDate")
	_xWithdrawGame.EndDate = field.NewTime(tableName, "EndDate")
	_xWithdrawGame.UserID = field.NewInt32(tableName, "UserId")
	_xWithdrawGame.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xWithdrawGame.SellerID = field.NewInt32(tableName, "SellerId")
	_xWithdrawGame.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xWithdrawGame.BetCount = field.NewInt32(tableName, "BetCount")
	_xWithdrawGame.WinCount = field.NewInt32(tableName, "WinCount")
	_xWithdrawGame.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xWithdrawGame.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xWithdrawGame.LiuSui = field.NewFloat64(tableName, "LiuSui")
	_xWithdrawGame.Fee = field.NewFloat64(tableName, "Fee")
	_xWithdrawGame.CreateTime = field.NewTime(tableName, "CreateTime")

	_xWithdrawGame.fillFieldMap()

	return _xWithdrawGame
}

// xWithdrawGame 提款订单游戏统计
type xWithdrawGame struct {
	xWithdrawGameDo xWithdrawGameDo

	ALL        field.Asterisk
	OrderID    field.Int32  // 订单id
	Brand      field.String // 三方品牌
	GameID     field.String // 游戏Id
	Symbol     field.String
	GameName   field.String  // 游戏名
	GameType   field.Int32   // 游戏分类
	StartDate  field.Time    // 统计开始日期
	EndDate    field.Time    // 统计结束日期
	UserID     field.Int32   // 玩家id
	TopAgentID field.Int32   // 顶级id
	SellerID   field.Int32   // 运营商
	ChannelID  field.Int32   // 渠道
	BetCount   field.Int32   // 投注次数
	WinCount   field.Int32   // 赢次数
	BetAmount  field.Float64 // 投注金额
	WinAmount  field.Float64 // 返奖金额
	LiuSui     field.Float64 // 有效投注
	Fee        field.Float64 // 手续费
	CreateTime field.Time    // 创建时间

	fieldMap map[string]field.Expr
}

func (x xWithdrawGame) Table(newTableName string) *xWithdrawGame {
	x.xWithdrawGameDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xWithdrawGame) As(alias string) *xWithdrawGame {
	x.xWithdrawGameDo.DO = *(x.xWithdrawGameDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xWithdrawGame) updateTableName(table string) *xWithdrawGame {
	x.ALL = field.NewAsterisk(table)
	x.OrderID = field.NewInt32(table, "OrderId")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.Symbol = field.NewString(table, "Symbol")
	x.GameName = field.NewString(table, "GameName")
	x.GameType = field.NewInt32(table, "GameType")
	x.StartDate = field.NewTime(table, "StartDate")
	x.EndDate = field.NewTime(table, "EndDate")
	x.UserID = field.NewInt32(table, "UserId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetCount = field.NewInt32(table, "BetCount")
	x.WinCount = field.NewInt32(table, "WinCount")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.LiuSui = field.NewFloat64(table, "LiuSui")
	x.Fee = field.NewFloat64(table, "Fee")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xWithdrawGame) WithContext(ctx context.Context) *xWithdrawGameDo {
	return x.xWithdrawGameDo.WithContext(ctx)
}

func (x xWithdrawGame) TableName() string { return x.xWithdrawGameDo.TableName() }

func (x xWithdrawGame) Alias() string { return x.xWithdrawGameDo.Alias() }

func (x xWithdrawGame) Columns(cols ...field.Expr) gen.Columns {
	return x.xWithdrawGameDo.Columns(cols...)
}

func (x *xWithdrawGame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xWithdrawGame) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 19)
	x.fieldMap["OrderId"] = x.OrderID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["StartDate"] = x.StartDate
	x.fieldMap["EndDate"] = x.EndDate
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetCount"] = x.BetCount
	x.fieldMap["WinCount"] = x.WinCount
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xWithdrawGame) clone(db *gorm.DB) xWithdrawGame {
	x.xWithdrawGameDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xWithdrawGame) replaceDB(db *gorm.DB) xWithdrawGame {
	x.xWithdrawGameDo.ReplaceDB(db)
	return x
}

type xWithdrawGameDo struct{ gen.DO }

func (x xWithdrawGameDo) Debug() *xWithdrawGameDo {
	return x.withDO(x.DO.Debug())
}

func (x xWithdrawGameDo) WithContext(ctx context.Context) *xWithdrawGameDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xWithdrawGameDo) ReadDB() *xWithdrawGameDo {
	return x.Clauses(dbresolver.Read)
}

func (x xWithdrawGameDo) WriteDB() *xWithdrawGameDo {
	return x.Clauses(dbresolver.Write)
}

func (x xWithdrawGameDo) Session(config *gorm.Session) *xWithdrawGameDo {
	return x.withDO(x.DO.Session(config))
}

func (x xWithdrawGameDo) Clauses(conds ...clause.Expression) *xWithdrawGameDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xWithdrawGameDo) Returning(value interface{}, columns ...string) *xWithdrawGameDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xWithdrawGameDo) Not(conds ...gen.Condition) *xWithdrawGameDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xWithdrawGameDo) Or(conds ...gen.Condition) *xWithdrawGameDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xWithdrawGameDo) Select(conds ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xWithdrawGameDo) Where(conds ...gen.Condition) *xWithdrawGameDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xWithdrawGameDo) Order(conds ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xWithdrawGameDo) Distinct(cols ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xWithdrawGameDo) Omit(cols ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xWithdrawGameDo) Join(table schema.Tabler, on ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xWithdrawGameDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xWithdrawGameDo) RightJoin(table schema.Tabler, on ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xWithdrawGameDo) Group(cols ...field.Expr) *xWithdrawGameDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xWithdrawGameDo) Having(conds ...gen.Condition) *xWithdrawGameDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xWithdrawGameDo) Limit(limit int) *xWithdrawGameDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xWithdrawGameDo) Offset(offset int) *xWithdrawGameDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xWithdrawGameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xWithdrawGameDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xWithdrawGameDo) Unscoped() *xWithdrawGameDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xWithdrawGameDo) Create(values ...*model.XWithdrawGame) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xWithdrawGameDo) CreateInBatches(values []*model.XWithdrawGame, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xWithdrawGameDo) Save(values ...*model.XWithdrawGame) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xWithdrawGameDo) First() (*model.XWithdrawGame, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawGame), nil
	}
}

func (x xWithdrawGameDo) Take() (*model.XWithdrawGame, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawGame), nil
	}
}

func (x xWithdrawGameDo) Last() (*model.XWithdrawGame, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawGame), nil
	}
}

func (x xWithdrawGameDo) Find() ([]*model.XWithdrawGame, error) {
	result, err := x.DO.Find()
	return result.([]*model.XWithdrawGame), err
}

func (x xWithdrawGameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XWithdrawGame, err error) {
	buf := make([]*model.XWithdrawGame, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xWithdrawGameDo) FindInBatches(result *[]*model.XWithdrawGame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xWithdrawGameDo) Attrs(attrs ...field.AssignExpr) *xWithdrawGameDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xWithdrawGameDo) Assign(attrs ...field.AssignExpr) *xWithdrawGameDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xWithdrawGameDo) Joins(fields ...field.RelationField) *xWithdrawGameDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xWithdrawGameDo) Preload(fields ...field.RelationField) *xWithdrawGameDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xWithdrawGameDo) FirstOrInit() (*model.XWithdrawGame, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawGame), nil
	}
}

func (x xWithdrawGameDo) FirstOrCreate() (*model.XWithdrawGame, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawGame), nil
	}
}

func (x xWithdrawGameDo) FindByPage(offset int, limit int) (result []*model.XWithdrawGame, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xWithdrawGameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xWithdrawGameDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xWithdrawGameDo) Delete(models ...*model.XWithdrawGame) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xWithdrawGameDo) withDO(do gen.Dao) *xWithdrawGameDo {
	x.DO = *do.(*gen.DO)
	return x
}
