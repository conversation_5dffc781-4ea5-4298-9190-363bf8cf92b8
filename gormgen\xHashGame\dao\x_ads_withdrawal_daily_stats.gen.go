// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsWithdrawalDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsWithdrawalDailyStat {
	_xAdsWithdrawalDailyStat := xAdsWithdrawalDailyStat{}

	_xAdsWithdrawalDailyStat.xAdsWithdrawalDailyStatDo.UseDB(db, opts...)
	_xAdsWithdrawalDailyStat.xAdsWithdrawalDailyStatDo.UseModel(&model.XAdsWithdrawalDailyStat{})

	tableName := _xAdsWithdrawalDailyStat.xAdsWithdrawalDailyStatDo.TableName()
	_xAdsWithdrawalDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsWithdrawalDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsWithdrawalDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsWithdrawalDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsWithdrawalDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsWithdrawalDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsWithdrawalDailyStat.WithdrawalType = field.NewInt32(tableName, "withdrawal_type")
	_xAdsWithdrawalDailyStat.WithdrawalChannel = field.NewString(tableName, "withdrawal_channel")
	_xAdsWithdrawalDailyStat.WithdrawalCountPc = field.NewInt32(tableName, "withdrawal_count_pc")
	_xAdsWithdrawalDailyStat.WithdrawalCountH5 = field.NewInt32(tableName, "withdrawal_count_h5")
	_xAdsWithdrawalDailyStat.WithdrawalCountSuccessPc = field.NewInt32(tableName, "withdrawal_count_success_pc")
	_xAdsWithdrawalDailyStat.WithdrawalCountSuccessH5 = field.NewInt32(tableName, "withdrawal_count_success_h5")
	_xAdsWithdrawalDailyStat.WithdrawalRatePc = field.NewFloat32(tableName, "withdrawal_rate_pc")
	_xAdsWithdrawalDailyStat.WithdrawalRateH5 = field.NewFloat32(tableName, "withdrawal_rate_h5")
	_xAdsWithdrawalDailyStat.WithdrawalSuccessRatePc = field.NewFloat32(tableName, "withdrawal_success_rate_pc")
	_xAdsWithdrawalDailyStat.WithdrawalSuccessRateH5 = field.NewFloat32(tableName, "withdrawal_success_rate_h5")
	_xAdsWithdrawalDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsWithdrawalDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsWithdrawalDailyStat.fillFieldMap()

	return _xAdsWithdrawalDailyStat
}

// xAdsWithdrawalDailyStat 提现偏好每日统计表
type xAdsWithdrawalDailyStat struct {
	xAdsWithdrawalDailyStatDo xAdsWithdrawalDailyStatDo

	ALL                      field.Asterisk
	ID                       field.Int64   // 主键ID
	SellerID                 field.Int32   // 运营商ID
	ChannelID                field.Int32   // 渠道ID
	TopAgentID               field.Int64   // 顶级代理ID
	StatDate                 field.Time    // 统计日期
	WithdrawalType           field.Int32   // 提现类型: 1:法币 2:加密货币
	WithdrawalChannel        field.String  // 提现渠道
	WithdrawalCountPc        field.Int32   // 提现次数pc
	WithdrawalCountH5        field.Int32   // 提现次数h5
	WithdrawalCountSuccessPc field.Int32   // 提现成功次数pc
	WithdrawalCountSuccessH5 field.Int32   // 提现成功次数h5
	WithdrawalRatePc         field.Float32 // 提现占比pc
	WithdrawalRateH5         field.Float32 // 提现占比h5
	WithdrawalSuccessRatePc  field.Float32 // 提现成功率 pc
	WithdrawalSuccessRateH5  field.Float32 // 提现成功率 h5
	CreateTime               field.Time    // 创建时间
	UpdateTime               field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsWithdrawalDailyStat) Table(newTableName string) *xAdsWithdrawalDailyStat {
	x.xAdsWithdrawalDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsWithdrawalDailyStat) As(alias string) *xAdsWithdrawalDailyStat {
	x.xAdsWithdrawalDailyStatDo.DO = *(x.xAdsWithdrawalDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsWithdrawalDailyStat) updateTableName(table string) *xAdsWithdrawalDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.StatDate = field.NewTime(table, "stat_date")
	x.WithdrawalType = field.NewInt32(table, "withdrawal_type")
	x.WithdrawalChannel = field.NewString(table, "withdrawal_channel")
	x.WithdrawalCountPc = field.NewInt32(table, "withdrawal_count_pc")
	x.WithdrawalCountH5 = field.NewInt32(table, "withdrawal_count_h5")
	x.WithdrawalCountSuccessPc = field.NewInt32(table, "withdrawal_count_success_pc")
	x.WithdrawalCountSuccessH5 = field.NewInt32(table, "withdrawal_count_success_h5")
	x.WithdrawalRatePc = field.NewFloat32(table, "withdrawal_rate_pc")
	x.WithdrawalRateH5 = field.NewFloat32(table, "withdrawal_rate_h5")
	x.WithdrawalSuccessRatePc = field.NewFloat32(table, "withdrawal_success_rate_pc")
	x.WithdrawalSuccessRateH5 = field.NewFloat32(table, "withdrawal_success_rate_h5")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsWithdrawalDailyStat) WithContext(ctx context.Context) *xAdsWithdrawalDailyStatDo {
	return x.xAdsWithdrawalDailyStatDo.WithContext(ctx)
}

func (x xAdsWithdrawalDailyStat) TableName() string { return x.xAdsWithdrawalDailyStatDo.TableName() }

func (x xAdsWithdrawalDailyStat) Alias() string { return x.xAdsWithdrawalDailyStatDo.Alias() }

func (x xAdsWithdrawalDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsWithdrawalDailyStatDo.Columns(cols...)
}

func (x *xAdsWithdrawalDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsWithdrawalDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 17)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["withdrawal_type"] = x.WithdrawalType
	x.fieldMap["withdrawal_channel"] = x.WithdrawalChannel
	x.fieldMap["withdrawal_count_pc"] = x.WithdrawalCountPc
	x.fieldMap["withdrawal_count_h5"] = x.WithdrawalCountH5
	x.fieldMap["withdrawal_count_success_pc"] = x.WithdrawalCountSuccessPc
	x.fieldMap["withdrawal_count_success_h5"] = x.WithdrawalCountSuccessH5
	x.fieldMap["withdrawal_rate_pc"] = x.WithdrawalRatePc
	x.fieldMap["withdrawal_rate_h5"] = x.WithdrawalRateH5
	x.fieldMap["withdrawal_success_rate_pc"] = x.WithdrawalSuccessRatePc
	x.fieldMap["withdrawal_success_rate_h5"] = x.WithdrawalSuccessRateH5
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsWithdrawalDailyStat) clone(db *gorm.DB) xAdsWithdrawalDailyStat {
	x.xAdsWithdrawalDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsWithdrawalDailyStat) replaceDB(db *gorm.DB) xAdsWithdrawalDailyStat {
	x.xAdsWithdrawalDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsWithdrawalDailyStatDo struct{ gen.DO }

func (x xAdsWithdrawalDailyStatDo) Debug() *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsWithdrawalDailyStatDo) WithContext(ctx context.Context) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsWithdrawalDailyStatDo) ReadDB() *xAdsWithdrawalDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsWithdrawalDailyStatDo) WriteDB() *xAdsWithdrawalDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsWithdrawalDailyStatDo) Session(config *gorm.Session) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsWithdrawalDailyStatDo) Clauses(conds ...clause.Expression) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsWithdrawalDailyStatDo) Returning(value interface{}, columns ...string) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsWithdrawalDailyStatDo) Not(conds ...gen.Condition) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsWithdrawalDailyStatDo) Or(conds ...gen.Condition) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsWithdrawalDailyStatDo) Select(conds ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsWithdrawalDailyStatDo) Where(conds ...gen.Condition) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsWithdrawalDailyStatDo) Order(conds ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsWithdrawalDailyStatDo) Distinct(cols ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsWithdrawalDailyStatDo) Omit(cols ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsWithdrawalDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsWithdrawalDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsWithdrawalDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsWithdrawalDailyStatDo) Group(cols ...field.Expr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsWithdrawalDailyStatDo) Having(conds ...gen.Condition) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsWithdrawalDailyStatDo) Limit(limit int) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsWithdrawalDailyStatDo) Offset(offset int) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsWithdrawalDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsWithdrawalDailyStatDo) Unscoped() *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsWithdrawalDailyStatDo) Create(values ...*model.XAdsWithdrawalDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsWithdrawalDailyStatDo) CreateInBatches(values []*model.XAdsWithdrawalDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsWithdrawalDailyStatDo) Save(values ...*model.XAdsWithdrawalDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsWithdrawalDailyStatDo) First() (*model.XAdsWithdrawalDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsWithdrawalDailyStat), nil
	}
}

func (x xAdsWithdrawalDailyStatDo) Take() (*model.XAdsWithdrawalDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsWithdrawalDailyStat), nil
	}
}

func (x xAdsWithdrawalDailyStatDo) Last() (*model.XAdsWithdrawalDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsWithdrawalDailyStat), nil
	}
}

func (x xAdsWithdrawalDailyStatDo) Find() ([]*model.XAdsWithdrawalDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsWithdrawalDailyStat), err
}

func (x xAdsWithdrawalDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsWithdrawalDailyStat, err error) {
	buf := make([]*model.XAdsWithdrawalDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsWithdrawalDailyStatDo) FindInBatches(result *[]*model.XAdsWithdrawalDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsWithdrawalDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsWithdrawalDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsWithdrawalDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsWithdrawalDailyStatDo) Joins(fields ...field.RelationField) *xAdsWithdrawalDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsWithdrawalDailyStatDo) Preload(fields ...field.RelationField) *xAdsWithdrawalDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsWithdrawalDailyStatDo) FirstOrInit() (*model.XAdsWithdrawalDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsWithdrawalDailyStat), nil
	}
}

func (x xAdsWithdrawalDailyStatDo) FirstOrCreate() (*model.XAdsWithdrawalDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsWithdrawalDailyStat), nil
	}
}

func (x xAdsWithdrawalDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsWithdrawalDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsWithdrawalDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsWithdrawalDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsWithdrawalDailyStatDo) Delete(models ...*model.XAdsWithdrawalDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsWithdrawalDailyStatDo) withDO(do gen.Dao) *xAdsWithdrawalDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
