package controller

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type WithdrawLimitConfigController struct{}

func (c *WithdrawLimitConfigController) Init() {
	group := server.Http().NewGroup("/api/WithdrawLimitConfig")
	{
		group.Post("/creat", c.creat)
		group.Post("/list", c.list)
		group.Post("/info", c.info)
		group.Post("/update", c.update)
		group.Post("/update_status", c.update_status)
		group.Post("/delete", c.delete)
	}
}

func (c *WithdrawLimitConfigController) creat(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XWithdrawLimitConfig
		VipLevels  []int32
		UserIds    []int32
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "提款限制", "增", "提款限制创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	if reqdata.Type == 0 {
		ctx.RespErrString(true, &errcode, "请选择配置类型")
		return
	}
	if reqdata.Type < 1 || reqdata.Type > 4 {
		ctx.RespErrString(true, &errcode, "不支持的配置类型")
		return
	}
	if reqdata.Type == 2 && len(reqdata.VipLevels) == 0 {
		ctx.RespErrString(true, &errcode, "请选择VIP")
		return
	}
	if (reqdata.Type == 3 || reqdata.Type == 4) && len(reqdata.UserIds) == 0 {
		ctx.RespErrString(true, &errcode, "请填写用户ID")
		return
	}

	// 如果是全局,VIP,代理设置: 设置资金密码验证类型
	if 1 == reqdata.Type || 2 == reqdata.Type || 3 == reqdata.Type {
		if reqdata.PwdVerificationType < 1 || reqdata.PwdVerificationType > 4 {
			ctx.RespErrString(true, &errcode, "不支持的资金密码验证类型")
			return
		}
	}
	dao := server.DaoxHashGame().XWithdrawLimitConfig
	db := dao.WithContext(ctx.Gin())
	var err error
	if reqdata.Type == 1 {
		err = db.Create(&reqdata.XWithdrawLimitConfig)
	} else if reqdata.Type == 2 {
		var vipLvList []int32
		_ = dao.WithContext(ctx.Gin()).Where(dao.SellerID.Eq(reqdata.SellerID)).Where(dao.ChannelID.Eq(reqdata.ChannelID)).
			Where(dao.VipLevel.In(reqdata.VipLevels...)).Pluck(dao.VipLevel, &vipLvList)
		if len(vipLvList) > 0 {
			var vipLvStrList []string
			for _, v := range vipLvList {
				vipLvStrList = append(vipLvStrList, strconv.Itoa(int(v)))
			}
			ctx.RespErrString(true, &errcode, fmt.Sprintf("%s配置已存在，请移除后再添加。", strings.Join(vipLvStrList, ",")))
			return
		}

		data, _ := json.Marshal(reqdata.XWithdrawLimitConfig)
		list := make([]*model.XWithdrawLimitConfig, 0)
		for _, id := range reqdata.VipLevels {
			var tb model.XWithdrawLimitConfig
			_ = json.Unmarshal(data, &tb)
			tb.VipLevel = id
			list = append(list, &tb)
		}
		err = db.CreateInBatches(list, 1000)
	} else if reqdata.Type == 3 || reqdata.Type == 4 {
		var uidList []int32
		_ = dao.WithContext(ctx.Gin()).Where(dao.SellerID.Eq(reqdata.SellerID)).Where(dao.ChannelID.Eq(reqdata.ChannelID)).
			Where(dao.UserID.In(reqdata.UserIds...)).Pluck(dao.UserID, &uidList)
		if len(uidList) > 0 {
			var uidStrList []string
			for _, v := range uidList {
				uidStrList = append(uidStrList, strconv.Itoa(int(v)))
			}
			ctx.RespErrString(true, &errcode, fmt.Sprintf("%s配置已存在，请移除后再添加。", strings.Join(uidStrList, ",")))
			return
		}

		data, _ := json.Marshal(reqdata.XWithdrawLimitConfig)
		list := make([]*model.XWithdrawLimitConfig, 0)
		for _, id := range reqdata.UserIds {
			var tb model.XWithdrawLimitConfig
			_ = json.Unmarshal(data, &tb)
			tb.UserID = id
			list = append(list, &tb)
		}
		err = db.CreateInBatches(list, 1000)
	}
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *WithdrawLimitConfigController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page      int
		PageSize  int
		SellerID  int32 `json:"SellerId"`
		ChannelID int32 `json:"ChannelId"`
		Type      int32 // 配置类型(1:全局 2:VIP 3:代理 4:个人)
		Status    int32
		VipLevel  int32
		UserIds   []int32
	}
	type Result struct {
		model.XWithdrawLimitConfig
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "提款限制", "查", "提款限制查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XWithdrawLimitConfig
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}
	if reqdata.ChannelID > 0 {
		db = db.Where(dao.ChannelID.Eq(reqdata.ChannelID))
	}
	if reqdata.Type > 0 {
		db = db.Where(dao.Type.Eq(reqdata.Type))
	}
	if reqdata.Status != 0 {
		db = db.Where(dao.Status.Eq(reqdata.Status))
	}
	if reqdata.Type == 2 && reqdata.VipLevel > 0 {
		db = db.Where(dao.VipLevel.Eq(reqdata.VipLevel))
	}
	if (reqdata.Type == 3 || reqdata.Type == 4) && len(reqdata.UserIds) > 0 {
		db = db.Where(dao.UserID.In(reqdata.UserIds...))
	}
	var list []Result
	total, err := db.ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("list", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *WithdrawLimitConfigController) info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID  int32 `json:"SellerId"`
		ChannelID int32 `json:"ChannelId"`
		ID        int32 `json:"Id"`
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "提款限制", "查", "提款限制详情查询")
	if token == nil {
		return
	}

	if reqdata.ID == 0 {
		ctx.RespErrString(true, &errcode, "Id不能为空")
		return
	}

	dao := server.DaoxHashGame().XWithdrawLimitConfig
	db := dao.WithContext(ctx.Gin())
	var data struct {
		model.XWithdrawLimitConfig
	}
	err := db.Where(dao.ID.Eq(reqdata.ID)).Select(dao.ALL, dao.VipLevel).Scan(&data)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *WithdrawLimitConfigController) update(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XWithdrawLimitConfig
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "提款限制", "改", "修改提款限制")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	if reqdata.ID == 0 {
		ctx.RespErrString(true, &errcode, "Id不能为空")
		return
	}

	dao := server.DaoxHashGame().XWithdrawLimitConfig
	db := dao.WithContext(ctx.Gin())
	info, err := db.Where(dao.ID.Eq(reqdata.ID)).
		Select(dao.Status, dao.MaxAmountPerDay, dao.MaxCountPerDay, dao.FeeFreeQuotaPerDay,
			dao.FeeFreeCountPerDay, dao.FeePercent, dao.MaxAmountEveryTime, dao.PwdVerificationType).
		Updates(&reqdata.XWithdrawLimitConfig)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}

func (c *WithdrawLimitConfigController) update_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XWithdrawLimitConfig
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "提款限制", "改", "修改提款限制状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	if reqdata.ID == 0 {
		ctx.RespErrString(true, &errcode, "Id不能为空")
		return
	}

	dao := server.DaoxHashGame().XWithdrawLimitConfig
	db := dao.WithContext(ctx.Gin())
	info, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.Status, reqdata.Status)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}

func (c *WithdrawLimitConfigController) delete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XWithdrawLimitConfig
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "提款限制", "删", "删除提款限制")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	if reqdata.ID == 0 {
		ctx.RespErrString(true, &errcode, "Id不能为空")
		return
	}

	dao := server.DaoxHashGame().XWithdrawLimitConfig
	db := dao.WithContext(ctx.Gin())
	info, err := db.Where(dao.ID.Eq(reqdata.ID)).Delete()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}
