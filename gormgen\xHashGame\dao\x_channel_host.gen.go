// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXChannelHost(db *gorm.DB, opts ...gen.DOOption) xChannelHost {
	_xChannelHost := xChannelHost{}

	_xChannelHost.xChannelHostDo.UseDB(db, opts...)
	_xChannelHost.xChannelHostDo.UseModel(&model.XChannelHost{})

	tableName := _xChannelHost.xChannelHostDo.TableName()
	_xChannelHost.ALL = field.NewAsterisk(tableName)
	_xChannelHost.ID = field.NewInt32(tableName, "Id")
	_xChannelHost.Host = field.NewString(tableName, "Host")
	_xChannelHost.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xChannelHost.State = field.NewInt32(tableName, "State")
	_xChannelHost.CreateTime = field.NewTime(tableName, "CreateTime")
	_xChannelHost.GameSort = field.NewString(tableName, "GameSort")
	_xChannelHost.GameSortEx = field.NewString(tableName, "GameSortEx")
	_xChannelHost.AgentCode = field.NewString(tableName, "AgentCode")
	_xChannelHost.GameSortNew = field.NewString(tableName, "GameSortNew")
	_xChannelHost.HostTagID = field.NewInt32(tableName, "HostTagId")
	_xChannelHost.WithdrawNeedActive = field.NewInt32(tableName, "WithdrawNeedActive")
	_xChannelHost.CustomService = field.NewString(tableName, "CustomService")
	_xChannelHost.CustomServiceState = field.NewInt32(tableName, "CustomServiceState")
	_xChannelHost.TJ51ID = field.NewString(tableName, "TJ51Id")
	_xChannelHost.SocialLinks = field.NewString(tableName, "SocialLinks")
	_xChannelHost.Maidian = field.NewString(tableName, "Maidian")
	_xChannelHost.IsAIEnable = field.NewInt32(tableName, "IsAIEnable")
	_xChannelHost.SportTarget = field.NewString(tableName, "SportTarget")
	_xChannelHost.IsTyjEnable = field.NewInt32(tableName, "IsTyjEnable")
	_xChannelHost.IsAutoDomainReg = field.NewInt32(tableName, "is_auto_domain_reg")
	_xChannelHost.TgAuthRobot = field.NewString(tableName, "TgAuthRobot")
	_xChannelHost.Webclip = field.NewString(tableName, "Webclip")
	_xChannelHost.CountryList = field.NewString(tableName, "CountryList")
	_xChannelHost.LoginRegisterType = field.NewString(tableName, "LoginRegisterType")
	_xChannelHost.PwdVerificationType = field.NewInt32(tableName, "PwdVerificationType")
	_xChannelHost.UserProtect = field.NewInt32(tableName, "UserProtect")
	_xChannelHost.UpdateTime = field.NewTime(tableName, "update_time")

	_xChannelHost.fillFieldMap()

	return _xChannelHost
}

type xChannelHost struct {
	xChannelHostDo xChannelHostDo

	ALL                 field.Asterisk
	ID                  field.Int32
	Host                field.String
	ChannelID           field.Int32
	State               field.Int32 // 1启用 2禁用
	CreateTime          field.Time
	GameSort            field.String
	GameSortEx          field.String
	AgentCode           field.String
	GameSortNew         field.String // 新游戏大类排序
	HostTagID           field.Int32  // x_host_tag.id
	WithdrawNeedActive  field.Int32  // 提款是否需要激活钱包 1激活 2不需要激活
	CustomService       field.String // 独立客服
	CustomServiceState  field.Int32  // 独立客服开关 1-开启 2关闭
	TJ51ID              field.String // 51统计ID
	SocialLinks         field.String // 社媒链接
	Maidian             field.String // 埋点统计
	IsAIEnable          field.Int32  // 是否开启AI客服（1:开启 2:关闭）
	SportTarget         field.String // 体育跳转
	IsTyjEnable         field.Int32  // 体验金开关 1:开 2:关
	IsAutoDomainReg     field.Int32  // 开启域名自动注册1开，2关
	TgAuthRobot         field.String // Tg授权机器人
	Webclip             field.String // webclip描述文件
	CountryList         field.String // 支持的地区（二位字母国家代码，英文逗号分隔）
	LoginRegisterType   field.String // 登录注册类型
	PwdVerificationType field.Int32  // 设置资金密码验证类型:1--邮箱 2 --手机号 3 -- (或关系) 两者满其一 4 -- (与关系)邮箱+手机号
	UserProtect         field.Int32  // 新手保护 1:开 2:关
	UpdateTime          field.Time   // 更新日期

	fieldMap map[string]field.Expr
}

func (x xChannelHost) Table(newTableName string) *xChannelHost {
	x.xChannelHostDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xChannelHost) As(alias string) *xChannelHost {
	x.xChannelHostDo.DO = *(x.xChannelHostDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xChannelHost) updateTableName(table string) *xChannelHost {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Host = field.NewString(table, "Host")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.State = field.NewInt32(table, "State")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.GameSort = field.NewString(table, "GameSort")
	x.GameSortEx = field.NewString(table, "GameSortEx")
	x.AgentCode = field.NewString(table, "AgentCode")
	x.GameSortNew = field.NewString(table, "GameSortNew")
	x.HostTagID = field.NewInt32(table, "HostTagId")
	x.WithdrawNeedActive = field.NewInt32(table, "WithdrawNeedActive")
	x.CustomService = field.NewString(table, "CustomService")
	x.CustomServiceState = field.NewInt32(table, "CustomServiceState")
	x.TJ51ID = field.NewString(table, "TJ51Id")
	x.SocialLinks = field.NewString(table, "SocialLinks")
	x.Maidian = field.NewString(table, "Maidian")
	x.IsAIEnable = field.NewInt32(table, "IsAIEnable")
	x.SportTarget = field.NewString(table, "SportTarget")
	x.IsTyjEnable = field.NewInt32(table, "IsTyjEnable")
	x.IsAutoDomainReg = field.NewInt32(table, "is_auto_domain_reg")
	x.TgAuthRobot = field.NewString(table, "TgAuthRobot")
	x.Webclip = field.NewString(table, "Webclip")
	x.CountryList = field.NewString(table, "CountryList")
	x.LoginRegisterType = field.NewString(table, "LoginRegisterType")
	x.PwdVerificationType = field.NewInt32(table, "PwdVerificationType")
	x.UserProtect = field.NewInt32(table, "UserProtect")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xChannelHost) WithContext(ctx context.Context) *xChannelHostDo {
	return x.xChannelHostDo.WithContext(ctx)
}

func (x xChannelHost) TableName() string { return x.xChannelHostDo.TableName() }

func (x xChannelHost) Alias() string { return x.xChannelHostDo.Alias() }

func (x xChannelHost) Columns(cols ...field.Expr) gen.Columns {
	return x.xChannelHostDo.Columns(cols...)
}

func (x *xChannelHost) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xChannelHost) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 27)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Host"] = x.Host
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["State"] = x.State
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["GameSort"] = x.GameSort
	x.fieldMap["GameSortEx"] = x.GameSortEx
	x.fieldMap["AgentCode"] = x.AgentCode
	x.fieldMap["GameSortNew"] = x.GameSortNew
	x.fieldMap["HostTagId"] = x.HostTagID
	x.fieldMap["WithdrawNeedActive"] = x.WithdrawNeedActive
	x.fieldMap["CustomService"] = x.CustomService
	x.fieldMap["CustomServiceState"] = x.CustomServiceState
	x.fieldMap["TJ51Id"] = x.TJ51ID
	x.fieldMap["SocialLinks"] = x.SocialLinks
	x.fieldMap["Maidian"] = x.Maidian
	x.fieldMap["IsAIEnable"] = x.IsAIEnable
	x.fieldMap["SportTarget"] = x.SportTarget
	x.fieldMap["IsTyjEnable"] = x.IsTyjEnable
	x.fieldMap["is_auto_domain_reg"] = x.IsAutoDomainReg
	x.fieldMap["TgAuthRobot"] = x.TgAuthRobot
	x.fieldMap["Webclip"] = x.Webclip
	x.fieldMap["CountryList"] = x.CountryList
	x.fieldMap["LoginRegisterType"] = x.LoginRegisterType
	x.fieldMap["PwdVerificationType"] = x.PwdVerificationType
	x.fieldMap["UserProtect"] = x.UserProtect
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xChannelHost) clone(db *gorm.DB) xChannelHost {
	x.xChannelHostDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xChannelHost) replaceDB(db *gorm.DB) xChannelHost {
	x.xChannelHostDo.ReplaceDB(db)
	return x
}

type xChannelHostDo struct{ gen.DO }

func (x xChannelHostDo) Debug() *xChannelHostDo {
	return x.withDO(x.DO.Debug())
}

func (x xChannelHostDo) WithContext(ctx context.Context) *xChannelHostDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xChannelHostDo) ReadDB() *xChannelHostDo {
	return x.Clauses(dbresolver.Read)
}

func (x xChannelHostDo) WriteDB() *xChannelHostDo {
	return x.Clauses(dbresolver.Write)
}

func (x xChannelHostDo) Session(config *gorm.Session) *xChannelHostDo {
	return x.withDO(x.DO.Session(config))
}

func (x xChannelHostDo) Clauses(conds ...clause.Expression) *xChannelHostDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xChannelHostDo) Returning(value interface{}, columns ...string) *xChannelHostDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xChannelHostDo) Not(conds ...gen.Condition) *xChannelHostDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xChannelHostDo) Or(conds ...gen.Condition) *xChannelHostDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xChannelHostDo) Select(conds ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xChannelHostDo) Where(conds ...gen.Condition) *xChannelHostDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xChannelHostDo) Order(conds ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xChannelHostDo) Distinct(cols ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xChannelHostDo) Omit(cols ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xChannelHostDo) Join(table schema.Tabler, on ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xChannelHostDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xChannelHostDo) RightJoin(table schema.Tabler, on ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xChannelHostDo) Group(cols ...field.Expr) *xChannelHostDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xChannelHostDo) Having(conds ...gen.Condition) *xChannelHostDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xChannelHostDo) Limit(limit int) *xChannelHostDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xChannelHostDo) Offset(offset int) *xChannelHostDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xChannelHostDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xChannelHostDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xChannelHostDo) Unscoped() *xChannelHostDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xChannelHostDo) Create(values ...*model.XChannelHost) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xChannelHostDo) CreateInBatches(values []*model.XChannelHost, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xChannelHostDo) Save(values ...*model.XChannelHost) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xChannelHostDo) First() (*model.XChannelHost, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelHost), nil
	}
}

func (x xChannelHostDo) Take() (*model.XChannelHost, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelHost), nil
	}
}

func (x xChannelHostDo) Last() (*model.XChannelHost, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelHost), nil
	}
}

func (x xChannelHostDo) Find() ([]*model.XChannelHost, error) {
	result, err := x.DO.Find()
	return result.([]*model.XChannelHost), err
}

func (x xChannelHostDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XChannelHost, err error) {
	buf := make([]*model.XChannelHost, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xChannelHostDo) FindInBatches(result *[]*model.XChannelHost, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xChannelHostDo) Attrs(attrs ...field.AssignExpr) *xChannelHostDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xChannelHostDo) Assign(attrs ...field.AssignExpr) *xChannelHostDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xChannelHostDo) Joins(fields ...field.RelationField) *xChannelHostDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xChannelHostDo) Preload(fields ...field.RelationField) *xChannelHostDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xChannelHostDo) FirstOrInit() (*model.XChannelHost, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelHost), nil
	}
}

func (x xChannelHostDo) FirstOrCreate() (*model.XChannelHost, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelHost), nil
	}
}

func (x xChannelHostDo) FindByPage(offset int, limit int) (result []*model.XChannelHost, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xChannelHostDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xChannelHostDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xChannelHostDo) Delete(models ...*model.XChannelHost) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xChannelHostDo) withDO(do gen.Dao) *xChannelHostDo {
	x.DO = *do.(*gen.DO)
	return x
}
