// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotAdStartRecord(db *gorm.DB, opts ...gen.DOOption) xRobotAdStartRecord {
	_xRobotAdStartRecord := xRobotAdStartRecord{}

	_xRobotAdStartRecord.xRobotAdStartRecordDo.UseDB(db, opts...)
	_xRobotAdStartRecord.xRobotAdStartRecordDo.UseModel(&model.XRobotAdStartRecord{})

	tableName := _xRobotAdStartRecord.xRobotAdStartRecordDo.TableName()
	_xRobotAdStartRecord.ALL = field.NewAsterisk(tableName)
	_xRobotAdStartRecord.ID = field.NewInt64(tableName, "id")
	_xRobotAdStartRecord.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotAdStartRecord.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotAdStartRecord.Name = field.NewString(tableName, "name")
	_xRobotAdStartRecord.Token = field.NewString(tableName, "token")
	_xRobotAdStartRecord.UserChatID = field.NewInt64(tableName, "user_chat_id")
	_xRobotAdStartRecord.UserName = field.NewString(tableName, "user_name")
	_xRobotAdStartRecord.UserFullName = field.NewString(tableName, "user_full_name")
	_xRobotAdStartRecord.LangCode = field.NewString(tableName, "lang_code")
	_xRobotAdStartRecord.StartFirstTime = field.NewTime(tableName, "start_first_time")
	_xRobotAdStartRecord.StartLastTime = field.NewTime(tableName, "start_last_time")
	_xRobotAdStartRecord.StartCnt = field.NewInt32(tableName, "start_cnt")
	_xRobotAdStartRecord.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotAdStartRecord.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotAdStartRecord.fillFieldMap()

	return _xRobotAdStartRecord
}

type xRobotAdStartRecord struct {
	xRobotAdStartRecordDo xRobotAdStartRecordDo

	ALL            field.Asterisk
	ID             field.Int64  // pk
	SellerID       field.Int32  // 运营商ID
	ChannelID      field.Int32  // 渠道ID
	Name           field.String // 机器人name
	Token          field.String // 机器人token
	UserChatID     field.Int64  // 用户chatID
	UserName       field.String // 用户名
	UserFullName   field.String // 全名
	LangCode       field.String // 用户语言
	StartFirstTime field.Time   // 首次启动时间
	StartLastTime  field.Time   // 最后启动时间
	StartCnt       field.Int32  // 启动次数
	CreateTime     field.Time   // 创建日期
	UpdateTime     field.Time   // 更新日期

	fieldMap map[string]field.Expr
}

func (x xRobotAdStartRecord) Table(newTableName string) *xRobotAdStartRecord {
	x.xRobotAdStartRecordDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotAdStartRecord) As(alias string) *xRobotAdStartRecord {
	x.xRobotAdStartRecordDo.DO = *(x.xRobotAdStartRecordDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotAdStartRecord) updateTableName(table string) *xRobotAdStartRecord {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.Name = field.NewString(table, "name")
	x.Token = field.NewString(table, "token")
	x.UserChatID = field.NewInt64(table, "user_chat_id")
	x.UserName = field.NewString(table, "user_name")
	x.UserFullName = field.NewString(table, "user_full_name")
	x.LangCode = field.NewString(table, "lang_code")
	x.StartFirstTime = field.NewTime(table, "start_first_time")
	x.StartLastTime = field.NewTime(table, "start_last_time")
	x.StartCnt = field.NewInt32(table, "start_cnt")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotAdStartRecord) WithContext(ctx context.Context) *xRobotAdStartRecordDo {
	return x.xRobotAdStartRecordDo.WithContext(ctx)
}

func (x xRobotAdStartRecord) TableName() string { return x.xRobotAdStartRecordDo.TableName() }

func (x xRobotAdStartRecord) Alias() string { return x.xRobotAdStartRecordDo.Alias() }

func (x xRobotAdStartRecord) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotAdStartRecordDo.Columns(cols...)
}

func (x *xRobotAdStartRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotAdStartRecord) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["name"] = x.Name
	x.fieldMap["token"] = x.Token
	x.fieldMap["user_chat_id"] = x.UserChatID
	x.fieldMap["user_name"] = x.UserName
	x.fieldMap["user_full_name"] = x.UserFullName
	x.fieldMap["lang_code"] = x.LangCode
	x.fieldMap["start_first_time"] = x.StartFirstTime
	x.fieldMap["start_last_time"] = x.StartLastTime
	x.fieldMap["start_cnt"] = x.StartCnt
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotAdStartRecord) clone(db *gorm.DB) xRobotAdStartRecord {
	x.xRobotAdStartRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotAdStartRecord) replaceDB(db *gorm.DB) xRobotAdStartRecord {
	x.xRobotAdStartRecordDo.ReplaceDB(db)
	return x
}

type xRobotAdStartRecordDo struct{ gen.DO }

func (x xRobotAdStartRecordDo) Debug() *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotAdStartRecordDo) WithContext(ctx context.Context) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotAdStartRecordDo) ReadDB() *xRobotAdStartRecordDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotAdStartRecordDo) WriteDB() *xRobotAdStartRecordDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotAdStartRecordDo) Session(config *gorm.Session) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotAdStartRecordDo) Clauses(conds ...clause.Expression) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotAdStartRecordDo) Returning(value interface{}, columns ...string) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotAdStartRecordDo) Not(conds ...gen.Condition) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotAdStartRecordDo) Or(conds ...gen.Condition) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotAdStartRecordDo) Select(conds ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotAdStartRecordDo) Where(conds ...gen.Condition) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotAdStartRecordDo) Order(conds ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotAdStartRecordDo) Distinct(cols ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotAdStartRecordDo) Omit(cols ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotAdStartRecordDo) Join(table schema.Tabler, on ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotAdStartRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotAdStartRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotAdStartRecordDo) Group(cols ...field.Expr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotAdStartRecordDo) Having(conds ...gen.Condition) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotAdStartRecordDo) Limit(limit int) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotAdStartRecordDo) Offset(offset int) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotAdStartRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotAdStartRecordDo) Unscoped() *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotAdStartRecordDo) Create(values ...*model.XRobotAdStartRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotAdStartRecordDo) CreateInBatches(values []*model.XRobotAdStartRecord, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotAdStartRecordDo) Save(values ...*model.XRobotAdStartRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotAdStartRecordDo) First() (*model.XRobotAdStartRecord, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotAdStartRecord), nil
	}
}

func (x xRobotAdStartRecordDo) Take() (*model.XRobotAdStartRecord, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotAdStartRecord), nil
	}
}

func (x xRobotAdStartRecordDo) Last() (*model.XRobotAdStartRecord, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotAdStartRecord), nil
	}
}

func (x xRobotAdStartRecordDo) Find() ([]*model.XRobotAdStartRecord, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotAdStartRecord), err
}

func (x xRobotAdStartRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotAdStartRecord, err error) {
	buf := make([]*model.XRobotAdStartRecord, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotAdStartRecordDo) FindInBatches(result *[]*model.XRobotAdStartRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotAdStartRecordDo) Attrs(attrs ...field.AssignExpr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotAdStartRecordDo) Assign(attrs ...field.AssignExpr) *xRobotAdStartRecordDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotAdStartRecordDo) Joins(fields ...field.RelationField) *xRobotAdStartRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotAdStartRecordDo) Preload(fields ...field.RelationField) *xRobotAdStartRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotAdStartRecordDo) FirstOrInit() (*model.XRobotAdStartRecord, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotAdStartRecord), nil
	}
}

func (x xRobotAdStartRecordDo) FirstOrCreate() (*model.XRobotAdStartRecord, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotAdStartRecord), nil
	}
}

func (x xRobotAdStartRecordDo) FindByPage(offset int, limit int) (result []*model.XRobotAdStartRecord, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotAdStartRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotAdStartRecordDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotAdStartRecordDo) Delete(models ...*model.XRobotAdStartRecord) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotAdStartRecordDo) withDO(do gen.Dao) *xRobotAdStartRecordDo {
	x.DO = *do.(*gen.DO)
	return x
}
