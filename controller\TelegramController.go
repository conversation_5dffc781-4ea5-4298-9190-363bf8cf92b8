package controller

import (
	"time"
	"xserver/abugo"
	"xserver/server"
)

type TelegramController struct {
}

func (c *TelegramController) Init() {
	server.Http().Post("/api/tg/notice_list", c.notice_list)
	server.Http().Post("/api/tg/notice_add", c.notice_add)
	server.Http().Post("/api/tg/notice_modify", c.notice_modify)
	server.Http().Post("/api/tg/notice_delete", c.notice_delete)

}

func (c *TelegramController) notice_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "活动管理", "机器人公告", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	total, presult := server.Db().Table("x_telegram_message").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if ctx.RespErr(err, &errcode) {
		return
	}
	for i := 0; i < len(*presult); i++ {
		ScheduleTime := abugo.GetStringFromInterface((*presult)[i]["ScheduleTime"])
		if ScheduleTime == "2000-01-01 00:00:00" {
			(*presult)[i]["ScheduleTime"] = ""
		}
	}
	ctx.Put("url", server.ImageUrl())
	ctx.Put("data", *presult)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TelegramController) notice_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId     int
		Title        string `validate:"required"`
		Img          string
		Content      string
		ScheduleTime int64
		ChannelId    int `validate:"required"`
		GoogleCode   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "活动管理", "机器人公告", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErrString(len(reqdata.Img) == 0 && len(reqdata.Content) == 0, &errcode, "参数不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "INSERT INTO x_telegram_message(SellerId,State,Img,Content,UpdateAccount,UpdateTime,ScheduleTime,Title,ChannelId) VALUES(?,?,?,?,?,now(),?,?,?)"
	state := 1
	ScheduleTimeStr := "2000-01-01 00:00:00"
	if reqdata.ScheduleTime == 0 {
		state = 3
	} else {
		ScheduleTimeStr = abugo.TimeStampToLocalTime(reqdata.ScheduleTime)
		ScheduleTime, _ := time.ParseInLocation(abugo.TimeLayout, ScheduleTimeStr, time.Local)
		if ctx.RespErrString(ScheduleTime.Unix() < time.Now().Unix(), &errcode, "定时发送时间不可早于现在时间") {
			return
		}
	}
	err2 := server.Db().QueryNoResult(sql, reqdata.SellerId, state, reqdata.Img, reqdata.Content, token.Account, ScheduleTimeStr, reqdata.Title, reqdata.ChannelId)
	if ctx.RespErr(err2, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *TelegramController) notice_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId     int
		Id           int `validate:"required"`
		Img          string
		Content      string
		ScheduleTime int64
		GoogleCode   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "活动管理", "机器人公告", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErrString(len(reqdata.Img) == 0 && len(reqdata.Content) == 0, &errcode, "参数不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.Id, 0)
	sql := "select * from x_telegram_message where id = ?"
	presult, err := server.Db().Table("x_telegram_message").Where(where).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(presult == nil, &errcode, "公告不存在") {
		return
	}
	orderstate := abugo.GetInt64FromInterface((*presult)["State"])
	if ctx.RespErrString(orderstate != 1, &errcode, "公告不可修改") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	ScheduleTimeStr := "2000-01-01 00:00:00"
	state := 1
	if reqdata.ScheduleTime == 0 {
		state = 3
	} else {
		ScheduleTimeStr = abugo.TimeStampToLocalTime(reqdata.ScheduleTime)
		ScheduleTime, _ := time.ParseInLocation(abugo.TimeLayout, ScheduleTimeStr, time.Local)
		if ctx.RespErrString(ScheduleTime.Unix() < time.Now().Unix(), &errcode, "定时发送时间不可早于现在时间") {
			return
		}
	}
	sql = "update x_telegram_message set State = ?,ScheduleTime = ?,Img = ?,Content= ?,UpdateAccount = ?,UpdateTime = now() where Id = ?"
	err2 := server.Db().QueryNoResult(sql, state, ScheduleTimeStr, reqdata.Img, reqdata.Content, token.Account, reqdata.Id)
	if ctx.RespErr(err2, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *TelegramController) notice_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Id         int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "活动管理", "机器人公告", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.Id, 0)
	sql := "select * from x_telegram_message where id = ?"
	presult, err := server.Db().Table("x_telegram_message").Where(where).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(presult == nil, &errcode, "公告不存在") {
		return
	}
	orderstate := abugo.GetInt64FromInterface((*presult)["State"])
	if ctx.RespErrString(orderstate != 1, &errcode, "公告不可删除") {
		return
	}
	sql = "delete from  x_telegram_message  where Id = ?"
	err2 := server.Db().QueryNoResult(sql, reqdata.Id)
	if ctx.RespErr(err2, &errcode) {
		return
	}
	ctx.RespOK()
}
