package controller

import (
	"errors"
	"fmt"
	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"gorm.io/gorm"
	"log"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"

	"xserver/server"
)

type TgUserRobotController struct{}

func (c *TgUserRobotController) Init() {
	group := server.Http().NewGroup("/api/tgUserRobot")
	{
		group.Post("/create", c.create)
		group.Post("/list", c.list)

	}
}

// 创建会员使用tg登录的机器人
func (c *TgUserRobotController) create(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgUserRobot
		GoogleCode string
		AccountId  int32
		SellerId   int32
		ChannelId  int32
	}{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "集成TG登录机器人", "增", "集成TG登录机器人创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	//机器人token
	_, err := tgbotapi.NewBotAPI(reqdata.TgToken)
	if err != nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("机器人token无效,请更换后再试. err:%s", err))
		return
	}
	//验证tg帐号参数
	if reqdata.AccountId <= 0 {
		ctx.RespErr(errors.New("请选择TG帐号"), &errcode)
		return
	} else {
		daoChan := server.DaoxHashGame().XTgAccount
		sellerData, err := daoChan.WithContext(ctx.Gin()).Where(daoChan.ID.Eq(reqdata.AccountId)).First()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(errors.New("请正确选择TG帐号"), &errcode)
				return
			}
			ctx.RespErr(err, &errcode)
			return
		}
		reqdata.TgUsername = sellerData.TgUsername
	}
	//验证运营商参数
	reqdata.SellerName = ""
	if reqdata.SellerId <= 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	} else {
		daoChan := server.DaoxHashGame().XSeller
		sellerData, err := daoChan.WithContext(ctx.Gin()).Where(daoChan.SellerID.Eq(reqdata.SellerId)).First()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(errors.New("请正确选择运营商"), &errcode)
				return
			}
			ctx.RespErr(err, &errcode)
			return
		}
		reqdata.SellerName = sellerData.SellerName
	}
	//验证渠道参数
	reqdata.ChannelName = ""
	if reqdata.ChannelId <= 0 {
		ctx.RespErr(errors.New("请选择渠道"), &errcode)
		return
	} else {
		daoChan := server.DaoxHashGame().XChannel
		channelData, err := daoChan.WithContext(ctx.Gin()).Where(daoChan.ChannelID.Eq(reqdata.ChannelId)).First()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(errors.New("请正确选择渠道"), &errcode)
				return
			}
			ctx.RespErr(err, &errcode)
			return
		}
		reqdata.ChannelName = channelData.ChannelName
	}
	// 开始创建
	dao := server.DaoxHashGame().XTgUserRobot
	db := dao.WithContext(ctx.Gin())
	dbErr := db.Create(&reqdata.XTgUserRobot)
	if dbErr != nil {
		log.Printf("Error occurred: %v", dbErr)
		ctx.RespErr(dbErr, &errcode)
		return
	}
	ctx.RespOK()
}

// 查询集成tg登录的机器人列表
func (c *TgUserRobotController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID  int32
		AccountId int32
		Page      int
		PageSize  int
		Status    string // yes:启用 no:禁用
	}
	type Result struct {
		model.XTgUserRobot
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "集成TG登录机器人", "查", "集成TG登录机器人查询")
	if token == nil {
		return
	}
	dao := server.DaoxHashGame().XTgUserRobot
	db := dao.WithContext(nil)
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	switch reqdata.Status {
	case "yes":
		db = db.Where(dao.Status.Eq("yes"))
		break
	case "no":
		db = db.Where(dao.Status.Eq("no"))
		break
	}
	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}
	if reqdata.AccountId > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.AccountId))
	}
	var list []Result
	total, err := db.ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}
