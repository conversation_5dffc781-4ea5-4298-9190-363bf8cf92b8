// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRedpacketLog = "x_tg_redpacket_log"

// XTgRedpacketLog mapped from table <x_tg_redpacket_log>
type XTgRedpacketLog struct {
	ID          int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID      int64     `gorm:"column:UserId" json:"UserId"`
	TgID        int32     `gorm:"column:TgId;not null;comment:后台TgId" json:"TgId"`        // 后台TgId
	SellerID    int32     `gorm:"column:SellerId;not null;comment:运营商id" json:"SellerId"` // 运营商id
	TgName      string    `gorm:"column:TgName" json:"TgName"`
	RedPacketID int32     `gorm:"column:RedPacketId" json:"RedPacketId"`
	Amount      float64   `gorm:"column:Amount" json:"Amount"`
	CreateTime  time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime  time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
}

// TableName XTgRedpacketLog's table name
func (*XTgRedpacketLog) TableName() string {
	return TableNameXTgRedpacketLog
}
