// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotRedbagReportUser = "x_robot_redbag_report_users"

// XRobotRedbagReportUser mapped from table <x_robot_redbag_report_users>
type XRobotRedbagReportUser struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                 // pk
	DateTime       time.Time `gorm:"column:date_time;not null;comment:日期" json:"date_time"`                        // 日期
	SellerID       int32     `gorm:"column:seller_id;not null;comment:运营商ID" json:"seller_id"`                     // 运营商ID
	ChannelID      int32     `gorm:"column:channel_id;not null;comment:渠道ID" json:"channel_id"`                    // 渠道ID
	UserID         int64     `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                          // 用户ID
	Name           string    `gorm:"column:name;not null;comment:机器人name" json:"name"`                             // 机器人name
	Token          string    `gorm:"column:token;not null;comment:机器人token" json:"token"`                          // 机器人token
	UserChatID     int64     `gorm:"column:user_chat_id;not null;comment:用户chatID" json:"user_chat_id"`            // 用户chatID
	UserName       string    `gorm:"column:user_name;comment:用户名" json:"user_name"`                                // 用户名
	UserFullName   string    `gorm:"column:user_full_name;comment:全名" json:"user_full_name"`                       // 全名
	LangCode       string    `gorm:"column:lang_code;comment:用户语言" json:"lang_code"`                               // 用户语言
	StartFirstTime time.Time `gorm:"column:start_first_time;comment:首次启动时间" json:"start_first_time"`               // 首次启动时间
	StartLastTime  time.Time `gorm:"column:start_last_time;comment:最后启动时间" json:"start_last_time"`                 // 最后启动时间
	StartCnt       int32     `gorm:"column:start_cnt;comment:启动次数" json:"start_cnt"`                               // 启动次数
	DrawsCnt       int32     `gorm:"column:draws_cnt;comment:抽奖次数" json:"draws_cnt"`                               // 抽奖次数
	SignDate       time.Time `gorm:"column:sign_date;comment:签到日期" json:"sign_date"`                               // 签到日期
	SignCnt        int32     `gorm:"column:sign_cnt;comment:签到次数" json:"sign_cnt"`                                 // 签到次数
	SignPoints     int32     `gorm:"column:sign_points;comment:签到积分" json:"sign_points"`                           // 签到积分
	GrabCnt        int32     `gorm:"column:grab_cnt;comment:红包次数" json:"grab_cnt"`                                 // 红包次数
	GrabPoints     int32     `gorm:"column:grab_points;comment:红包积分" json:"grab_points"`                           // 红包积分
	AnswerCnt      int32     `gorm:"column:answer_cnt;comment:用户答题次数" json:"answer_cnt"`                           // 用户答题次数
	AnswerPoints   int32     `gorm:"column:answer_points;comment:答题积分" json:"answer_points"`                       // 答题积分
	ExchangeCnt    int32     `gorm:"column:exchange_cnt;comment:兑换次数" json:"exchange_cnt"`                         // 兑换次数
	ExchangePoints int32     `gorm:"column:exchange_points;comment:兑换积分" json:"exchange_points"`                   // 兑换积分
	TotalPoints    int32     `gorm:"column:total_points;comment:总积分" json:"total_points"`                          // 总积分
	CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建日期" json:"create_time"` // 创建日期
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新日期" json:"update_time"` // 更新日期
}

// TableName XRobotRedbagReportUser's table name
func (*XRobotRedbagReportUser) TableName() string {
	return TableNameXRobotRedbagReportUser
}
