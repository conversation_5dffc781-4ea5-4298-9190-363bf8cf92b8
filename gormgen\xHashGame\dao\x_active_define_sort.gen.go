// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXActiveDefineSort(db *gorm.DB, opts ...gen.DOOption) xActiveDefineSort {
	_xActiveDefineSort := xActiveDefineSort{}

	_xActiveDefineSort.xActiveDefineSortDo.UseDB(db, opts...)
	_xActiveDefineSort.xActiveDefineSortDo.UseModel(&model.XActiveDefineSort{})

	tableName := _xActiveDefineSort.xActiveDefineSortDo.TableName()
	_xActiveDefineSort.ALL = field.NewAsterisk(tableName)
	_xActiveDefineSort.ID = field.NewInt32(tableName, "Id")
	_xActiveDefineSort.Lang = field.NewInt32(tableName, "Lang")
	_xActiveDefineSort.Sort = field.NewInt32(tableName, "Sort")
	_xActiveDefineSort.TopSort = field.NewInt32(tableName, "TopSort")
	_xActiveDefineSort.CreateTime = field.NewTime(tableName, "CreateTime")
	_xActiveDefineSort.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xActiveDefineSort.fillFieldMap()

	return _xActiveDefineSort
}

type xActiveDefineSort struct {
	xActiveDefineSortDo xActiveDefineSortDo

	ALL        field.Asterisk
	ID         field.Int32 // x_active_define.id
	Lang       field.Int32 // x_lang_list.id
	Sort       field.Int32
	TopSort    field.Int32
	CreateTime field.Time
	UpdateTime field.Time

	fieldMap map[string]field.Expr
}

func (x xActiveDefineSort) Table(newTableName string) *xActiveDefineSort {
	x.xActiveDefineSortDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xActiveDefineSort) As(alias string) *xActiveDefineSort {
	x.xActiveDefineSortDo.DO = *(x.xActiveDefineSortDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xActiveDefineSort) updateTableName(table string) *xActiveDefineSort {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Lang = field.NewInt32(table, "Lang")
	x.Sort = field.NewInt32(table, "Sort")
	x.TopSort = field.NewInt32(table, "TopSort")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xActiveDefineSort) WithContext(ctx context.Context) *xActiveDefineSortDo {
	return x.xActiveDefineSortDo.WithContext(ctx)
}

func (x xActiveDefineSort) TableName() string { return x.xActiveDefineSortDo.TableName() }

func (x xActiveDefineSort) Alias() string { return x.xActiveDefineSortDo.Alias() }

func (x xActiveDefineSort) Columns(cols ...field.Expr) gen.Columns {
	return x.xActiveDefineSortDo.Columns(cols...)
}

func (x *xActiveDefineSort) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xActiveDefineSort) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["TopSort"] = x.TopSort
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xActiveDefineSort) clone(db *gorm.DB) xActiveDefineSort {
	x.xActiveDefineSortDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xActiveDefineSort) replaceDB(db *gorm.DB) xActiveDefineSort {
	x.xActiveDefineSortDo.ReplaceDB(db)
	return x
}

type xActiveDefineSortDo struct{ gen.DO }

func (x xActiveDefineSortDo) Debug() *xActiveDefineSortDo {
	return x.withDO(x.DO.Debug())
}

func (x xActiveDefineSortDo) WithContext(ctx context.Context) *xActiveDefineSortDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xActiveDefineSortDo) ReadDB() *xActiveDefineSortDo {
	return x.Clauses(dbresolver.Read)
}

func (x xActiveDefineSortDo) WriteDB() *xActiveDefineSortDo {
	return x.Clauses(dbresolver.Write)
}

func (x xActiveDefineSortDo) Session(config *gorm.Session) *xActiveDefineSortDo {
	return x.withDO(x.DO.Session(config))
}

func (x xActiveDefineSortDo) Clauses(conds ...clause.Expression) *xActiveDefineSortDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xActiveDefineSortDo) Returning(value interface{}, columns ...string) *xActiveDefineSortDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xActiveDefineSortDo) Not(conds ...gen.Condition) *xActiveDefineSortDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xActiveDefineSortDo) Or(conds ...gen.Condition) *xActiveDefineSortDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xActiveDefineSortDo) Select(conds ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xActiveDefineSortDo) Where(conds ...gen.Condition) *xActiveDefineSortDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xActiveDefineSortDo) Order(conds ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xActiveDefineSortDo) Distinct(cols ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xActiveDefineSortDo) Omit(cols ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xActiveDefineSortDo) Join(table schema.Tabler, on ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xActiveDefineSortDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xActiveDefineSortDo) RightJoin(table schema.Tabler, on ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xActiveDefineSortDo) Group(cols ...field.Expr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xActiveDefineSortDo) Having(conds ...gen.Condition) *xActiveDefineSortDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xActiveDefineSortDo) Limit(limit int) *xActiveDefineSortDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xActiveDefineSortDo) Offset(offset int) *xActiveDefineSortDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xActiveDefineSortDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xActiveDefineSortDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xActiveDefineSortDo) Unscoped() *xActiveDefineSortDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xActiveDefineSortDo) Create(values ...*model.XActiveDefineSort) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xActiveDefineSortDo) CreateInBatches(values []*model.XActiveDefineSort, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xActiveDefineSortDo) Save(values ...*model.XActiveDefineSort) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xActiveDefineSortDo) First() (*model.XActiveDefineSort, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefineSort), nil
	}
}

func (x xActiveDefineSortDo) Take() (*model.XActiveDefineSort, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefineSort), nil
	}
}

func (x xActiveDefineSortDo) Last() (*model.XActiveDefineSort, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefineSort), nil
	}
}

func (x xActiveDefineSortDo) Find() ([]*model.XActiveDefineSort, error) {
	result, err := x.DO.Find()
	return result.([]*model.XActiveDefineSort), err
}

func (x xActiveDefineSortDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XActiveDefineSort, err error) {
	buf := make([]*model.XActiveDefineSort, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xActiveDefineSortDo) FindInBatches(result *[]*model.XActiveDefineSort, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xActiveDefineSortDo) Attrs(attrs ...field.AssignExpr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xActiveDefineSortDo) Assign(attrs ...field.AssignExpr) *xActiveDefineSortDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xActiveDefineSortDo) Joins(fields ...field.RelationField) *xActiveDefineSortDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xActiveDefineSortDo) Preload(fields ...field.RelationField) *xActiveDefineSortDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xActiveDefineSortDo) FirstOrInit() (*model.XActiveDefineSort, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefineSort), nil
	}
}

func (x xActiveDefineSortDo) FirstOrCreate() (*model.XActiveDefineSort, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefineSort), nil
	}
}

func (x xActiveDefineSortDo) FindByPage(offset int, limit int) (result []*model.XActiveDefineSort, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xActiveDefineSortDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xActiveDefineSortDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xActiveDefineSortDo) Delete(models ...*model.XActiveDefineSort) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xActiveDefineSortDo) withDO(do gen.Dao) *xActiveDefineSortDo {
	x.DO = *do.(*gen.DO)
	return x
}
