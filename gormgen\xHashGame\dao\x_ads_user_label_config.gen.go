// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsUserLabelConfig(db *gorm.DB, opts ...gen.DOOption) xAdsUserLabelConfig {
	_xAdsUserLabelConfig := xAdsUserLabelConfig{}

	_xAdsUserLabelConfig.xAdsUserLabelConfigDo.UseDB(db, opts...)
	_xAdsUserLabelConfig.xAdsUserLabelConfigDo.UseModel(&model.XAdsUserLabelConfig{})

	tableName := _xAdsUserLabelConfig.xAdsUserLabelConfigDo.TableName()
	_xAdsUserLabelConfig.ALL = field.NewAsterisk(tableName)
	_xAdsUserLabelConfig.ID = field.NewInt64(tableName, "id")
	_xAdsUserLabelConfig.Name = field.NewString(tableName, "name")
	_xAdsUserLabelConfig.GroupName = field.NewString(tableName, "group_name")
	_xAdsUserLabelConfig.LabelType = field.NewInt32(tableName, "label_type")
	_xAdsUserLabelConfig.JSON = field.NewString(tableName, "json")
	_xAdsUserLabelConfig.Remark = field.NewString(tableName, "remark")
	_xAdsUserLabelConfig.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsUserLabelConfig.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsUserLabelConfig.fillFieldMap()

	return _xAdsUserLabelConfig
}

// xAdsUserLabelConfig 用户标签
type xAdsUserLabelConfig struct {
	xAdsUserLabelConfigDo xAdsUserLabelConfigDo

	ALL        field.Asterisk
	ID         field.Int64  // id
	Name       field.String // 名称
	GroupName  field.String // 群组名称
	LabelType  field.Int32  // 标签类型1：群组标签2：普通标签，
	JSON       field.String // 数据字段
	Remark     field.String // 备注
	CreateTime field.Time   // 创建日期
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsUserLabelConfig) Table(newTableName string) *xAdsUserLabelConfig {
	x.xAdsUserLabelConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsUserLabelConfig) As(alias string) *xAdsUserLabelConfig {
	x.xAdsUserLabelConfigDo.DO = *(x.xAdsUserLabelConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsUserLabelConfig) updateTableName(table string) *xAdsUserLabelConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.Name = field.NewString(table, "name")
	x.GroupName = field.NewString(table, "group_name")
	x.LabelType = field.NewInt32(table, "label_type")
	x.JSON = field.NewString(table, "json")
	x.Remark = field.NewString(table, "remark")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsUserLabelConfig) WithContext(ctx context.Context) *xAdsUserLabelConfigDo {
	return x.xAdsUserLabelConfigDo.WithContext(ctx)
}

func (x xAdsUserLabelConfig) TableName() string { return x.xAdsUserLabelConfigDo.TableName() }

func (x xAdsUserLabelConfig) Alias() string { return x.xAdsUserLabelConfigDo.Alias() }

func (x xAdsUserLabelConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsUserLabelConfigDo.Columns(cols...)
}

func (x *xAdsUserLabelConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsUserLabelConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["id"] = x.ID
	x.fieldMap["name"] = x.Name
	x.fieldMap["group_name"] = x.GroupName
	x.fieldMap["label_type"] = x.LabelType
	x.fieldMap["json"] = x.JSON
	x.fieldMap["remark"] = x.Remark
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsUserLabelConfig) clone(db *gorm.DB) xAdsUserLabelConfig {
	x.xAdsUserLabelConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsUserLabelConfig) replaceDB(db *gorm.DB) xAdsUserLabelConfig {
	x.xAdsUserLabelConfigDo.ReplaceDB(db)
	return x
}

type xAdsUserLabelConfigDo struct{ gen.DO }

func (x xAdsUserLabelConfigDo) Debug() *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsUserLabelConfigDo) WithContext(ctx context.Context) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsUserLabelConfigDo) ReadDB() *xAdsUserLabelConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsUserLabelConfigDo) WriteDB() *xAdsUserLabelConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsUserLabelConfigDo) Session(config *gorm.Session) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsUserLabelConfigDo) Clauses(conds ...clause.Expression) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsUserLabelConfigDo) Returning(value interface{}, columns ...string) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsUserLabelConfigDo) Not(conds ...gen.Condition) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsUserLabelConfigDo) Or(conds ...gen.Condition) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsUserLabelConfigDo) Select(conds ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsUserLabelConfigDo) Where(conds ...gen.Condition) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsUserLabelConfigDo) Order(conds ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsUserLabelConfigDo) Distinct(cols ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsUserLabelConfigDo) Omit(cols ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsUserLabelConfigDo) Join(table schema.Tabler, on ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsUserLabelConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsUserLabelConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsUserLabelConfigDo) Group(cols ...field.Expr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsUserLabelConfigDo) Having(conds ...gen.Condition) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsUserLabelConfigDo) Limit(limit int) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsUserLabelConfigDo) Offset(offset int) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsUserLabelConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsUserLabelConfigDo) Unscoped() *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsUserLabelConfigDo) Create(values ...*model.XAdsUserLabelConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsUserLabelConfigDo) CreateInBatches(values []*model.XAdsUserLabelConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsUserLabelConfigDo) Save(values ...*model.XAdsUserLabelConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsUserLabelConfigDo) First() (*model.XAdsUserLabelConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserLabelConfig), nil
	}
}

func (x xAdsUserLabelConfigDo) Take() (*model.XAdsUserLabelConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserLabelConfig), nil
	}
}

func (x xAdsUserLabelConfigDo) Last() (*model.XAdsUserLabelConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserLabelConfig), nil
	}
}

func (x xAdsUserLabelConfigDo) Find() ([]*model.XAdsUserLabelConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsUserLabelConfig), err
}

func (x xAdsUserLabelConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsUserLabelConfig, err error) {
	buf := make([]*model.XAdsUserLabelConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsUserLabelConfigDo) FindInBatches(result *[]*model.XAdsUserLabelConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsUserLabelConfigDo) Attrs(attrs ...field.AssignExpr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsUserLabelConfigDo) Assign(attrs ...field.AssignExpr) *xAdsUserLabelConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsUserLabelConfigDo) Joins(fields ...field.RelationField) *xAdsUserLabelConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsUserLabelConfigDo) Preload(fields ...field.RelationField) *xAdsUserLabelConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsUserLabelConfigDo) FirstOrInit() (*model.XAdsUserLabelConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserLabelConfig), nil
	}
}

func (x xAdsUserLabelConfigDo) FirstOrCreate() (*model.XAdsUserLabelConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsUserLabelConfig), nil
	}
}

func (x xAdsUserLabelConfigDo) FindByPage(offset int, limit int) (result []*model.XAdsUserLabelConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsUserLabelConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsUserLabelConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsUserLabelConfigDo) Delete(models ...*model.XAdsUserLabelConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsUserLabelConfigDo) withDO(do gen.Dao) *xAdsUserLabelConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
