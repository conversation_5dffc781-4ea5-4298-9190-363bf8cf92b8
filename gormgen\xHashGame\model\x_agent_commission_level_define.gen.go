// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentCommissionLevelDefine = "x_agent_commission_level_define"

// XAgentCommissionLevelDefine 三级返佣等级定义
type XAgentCommissionLevelDefine struct {
	SchemeID           int32     `gorm:"column:SchemeId;primaryKey;comment:方案Id" json:"SchemeId"`                                  // 方案Id
	TeamLevel          int32     `gorm:"column:TeamLevel;primaryKey;comment:团队等级" json:"TeamLevel"`                                // 团队等级
	AgentLevel         int32     `gorm:"column:AgentLevel;primaryKey;default:1;comment:代理等级" json:"AgentLevel"`                    // 代理等级
	RewardHaXi         float64   `gorm:"column:RewardHaXi;default:0.000000;comment:哈希返佣(百分比)" json:"RewardHaXi"`                   // 哈希返佣(百分比)
	RewardHaXiRoulette float64   `gorm:"column:RewardHaXiRoulette;default:0.000000;comment:哈希轮盘返佣(百分比)" json:"RewardHaXiRoulette"` // 哈希轮盘返佣(百分比)
	RewardLottery      float64   `gorm:"column:RewardLottery;default:0.000000;comment:彩票返佣(百分比)" json:"RewardLottery"`             // 彩票返佣(百分比)
	RewardLowLottery   float64   `gorm:"column:RewardLowLottery;default:0.000000;comment:低频彩返佣(百分比)" json:"RewardLowLottery"`      // 低频彩返佣(百分比)
	RewardQiPai        float64   `gorm:"column:RewardQiPai;default:0.000000;comment:棋牌返佣(百分比)" json:"RewardQiPai"`                 // 棋牌返佣(百分比)
	RewardDianZhi      float64   `gorm:"column:RewardDianZhi;default:0.000000;comment:电子返佣(百分比)" json:"RewardDianZhi"`             // 电子返佣(百分比)
	RewardXiaoYouXi    float64   `gorm:"column:RewardXiaoYouXi;default:0.000000;comment:小游戏返佣(百分比)" json:"RewardXiaoYouXi"`        // 小游戏返佣(百分比)
	RewardLive         float64   `gorm:"column:RewardLive;default:0.000000;comment:真人返佣(百分比)" json:"RewardLive"`                   // 真人返佣(百分比)
	RewardSport        float64   `gorm:"column:RewardSport;default:0.000000;comment:体育返佣(百分比)" json:"RewardSport"`                 // 体育返佣(百分比)
	RewardTexas        float64   `gorm:"column:RewardTexas;default:0.000000;comment:德州返佣(百分比)" json:"RewardTexas"`                 // 德州返佣(百分比)
	CreateTime         time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`      // 创建时间
	UpdateTime         time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`      // 更新时间
}

// TableName XAgentCommissionLevelDefine's table name
func (*XAgentCommissionLevelDefine) TableName() string {
	return TableNameXAgentCommissionLevelDefine
}
