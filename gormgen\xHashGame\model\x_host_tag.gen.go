// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXHostTag = "x_host_tag"

// XHostTag 域名标签
type XHostTag struct {
	ID       int32  `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"` // id
	SellerID int32  `gorm:"column:SellerId;not null;comment:运营商" json:"SellerId"`         // 运营商
	TagName  string `gorm:"column:TagName;not null;comment:标签名称" json:"TagName"`          // 标签名称
}

// TableName XHostTag's table name
func (*XHostTag) TableName() string {
	return TableNameXHostTag
}
