server:
  debug: true
  local: false
  snowflakenode: 1 #分布式id生成器节点
  project: x_hash #系统名称
  module: adminapi #模块名称
  dbprefix: x_ #数据库前缀
  readonly: false
  http:
    http:
      port: 4534
  token:
    host: *************
    port: 19879 #6379
    db: 7
    password: dT&@h3ID^scKCahF #Ho9mpyeqaILEOfjM
    maxidle: 10 #最大的空闲连接数，表示即使没有redis连接时依然可以保持N个空闲的连接，而不被清除，随时处于待命状态。
    maxactive: 100 #最大的激活连接数，表示同时最多有N个连接
    idletimeout: 60 #最大的空闲连接等待时间，超过此时间后，空闲连接将被关闭
    lifetime: 2592000 #token过期时长
  redis:
    host: *************
    port: 19879
    db: 5
    password: dT&@h3ID^scKCahF #Ho9mpyeqaILEOfjM
    maxidle: 10 #最大的空闲连接数，表示即使没有redis连接时依然可以保持N个空闲的连接，而不被清除，随时处于待命状态。
    maxactive: 100 #最大的激活连接数，表示同时最多有N个连接
    idletimeout: 60 #最大的空闲连接等待时间，超过此时间后，空闲连接将被关闭
  db:
    host: *************
    port: 14578
    user: userbu
    password: aw9#gf*S7fT1P
    database: x_hash_game
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: true
  dbStatReadonly:
    host: *************
    port: 14578
    user: userbu
    password: aw9#gf*S7fT1P
    database: x_hash_stat
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: false
  dbreadonly:
    host: *************
    port: 14578
    user: userbu
    password: aw9#gf*S7fT1P
    database: x_hash_game
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: false
  dbreport:
    host: *************
    port: 14578
    user: userbu
    password: aw9#gf*S7fT1P
    database: x_hash_game
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: true
websocket:
  port: 4539
apis:
  clientapi: http://127.0.0.1:4533/api
file:
  uploaddir: ./upload
  exportdir: ./exports
image:
  url: https://test-cdn-img.w0zuv.live
  aws:
    key: ********************
    secret: ZsmgeeEt7XAkDkDGegAuh/ls5xI+LFs9gSSAeVhm
    endpoint: s3.ap-southeast-1.amazonaws.com
    bucket: newhash-test
    region: ap-southeast-1

tgservice: http://127.0.0.1:4514
adminapi_readonly: http://127.0.0.1:4634
tronscangoapi: http://**************:8810
hash_admin_api: https://panda-admin.allgame.top
newtgservie: http://************:9012