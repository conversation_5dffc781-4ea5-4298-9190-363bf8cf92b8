package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	daoStat "xserver/gormgen/xHashStat/dao"
	"xserver/utils"
	"xserver/webclip"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	redis2 "github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"github.com/valyala/fastjson"
	"github.com/yinheli/qqwry"
	"github.com/zhms/xgo/xgo"
)

var http *abugo.AbuHttp
var redis *abugo.AbuRedis
var db *abugo.AbuDb
var dbReport *abugo.AbuDb

var xdb *xgo.XDb = &xgo.XDb{}
var xdbReport *xgo.XDb = &xgo.XDb{}
var xredis *xgo.XRedis = &xgo.XRedis{}
var daoXHashGame *dao.Query
var dbStat *abugo.AbuDb
var daoXHashStat *daoStat.Query

var debug bool = false
var project string
var module string
var dbprefix string
var clientapi string

var uploaddir string
var exportdir string
var readonly bool
var imageurl string

func Project() string {
	return project
}
func Module() string {
	return module
}
func DbPrefix() string {
	return dbprefix
}
func ClientApi() string {
	return clientapi
}
func UploadDir() string {
	return uploaddir
}
func ExportDir() string {
	return exportdir
}
func ImageUrl() string {
	return imageurl
}
func Readonly() bool {
	return readonly
}

var Rdb redis2.UniversalClient

func redisInit() {
	// redis v9

	addr := []string{viper.GetString("server.redis.host") + ":" + viper.GetString("server.redis.port")}
	client := redis2.NewUniversalClient(&redis2.UniversalOptions{
		Addrs:          addr,
		Password:       viper.GetString("server.redis.password"),
		DB:             viper.GetInt("server.token.db"),
		PoolSize:       10,
		ReadTimeout:    time.Second * 1000 * 100,
		WriteTimeout:   time.Second * 1000 * 100,
		DialTimeout:    time.Second * 1000 * 100,
		MaxIdleConns:   viper.GetInt("server.redis.maxidle"), // 最大空闲连接数，多余会被关闭
		MaxActiveConns: viper.GetInt("server.redis.maxactive"),
	})

	Rdb = client
	p, err := Rdb.Ping(context.Background()).Result()
	fmt.Println("xxxxxxxxxxxx", p, err)

}

func Init() {
	initCarbonTime()

	abugo.Init()
	redisInit()
	debug = viper.GetBool("server.debug")
	project = viper.GetString("server.project")
	module = viper.GetString("server.module")
	dbprefix = viper.GetString("server.dbprefix")
	clientapi = viper.GetString("apis.clientapi")
	uploaddir = viper.GetString("file.uploaddir")
	imageurl = viper.GetString("image.url")
	readonly = viper.GetBool("server.readonly")

	if uploaddir == "" {
		os.Mkdir("upload", os.ModePerm)
		uploaddir = "./upload"
	}
	exportdir = viper.GetString("file.exportdir")
	if exportdir == "" {
		os.Mkdir("exports", os.ModePerm)
		uploaddir = "./exports"
	}
	db = new(abugo.AbuDb)
	dbReport = new(abugo.AbuDb)
	if readonly {
		db.Init("server.dbreadonly")
		xdb.Init("server.dbreadonly")
		dbReport.Init("server.dbreport")
		xdbReport.Init("server.dbreport")
	} else {
		db.Init("server.db")
		xdb.Init("server.db")
		dbReport.Init("server.dbreport")
		xdbReport.Init("server.dbreport")
	}
	daoXHashGame = dao.Use(db.GormDao())
	dbStat = new(abugo.AbuDb)
	dbStat.Init("server.dbStatReadonly")
	daoXHashStat = daoStat.Use(dbStat.GormDao())
	redis = new(abugo.AbuRedis)
	redis.Init("server.redis")
	xredis.Init("server.redis")
	{
		jdata := []interface{}{}
		json.Unmarshal([]byte(MenuDataStr), &jdata)
		for i := 0; i < len(jdata); i++ {
			jdata[i].(map[string]interface{})["index"] = fmt.Sprint(i)
		}
		jbytes, _ := json.Marshal(&jdata)
		MenuDataStr = string(jbytes)
	}
	// readonly = false
	if !readonly {
		SetupInit()
		// debug = false
		if !debug {
			//SetupDatabase()
			jdata := map[string]interface{}{}
			err := json.Unmarshal([]byte(AuthDataStr), &jdata)
			if err != nil {
				panic(err)
			}
			jbytes, _ := json.Marshal(&jdata)
			AuthDataStr = string(jbytes)

			xitong := jdata["系统管理"].(map[string]interface{})
			xitong["运营商管理"] = map[string]interface{}{"查:": 0, "增": 0, "删": 0, "改": 0}
			xitongsezhi := xitong["系统设置"].(map[string]interface{})
			xitongsezhi["删"] = 0

			jbytes, _ = json.Marshal(&jdata)
			SellerAuthDataStr := string(jbytes)
			{
				sql := fmt.Sprintf("select SellerId from %sseller", dbprefix)
				dbresult, _ := db.Conn().Query(sql)
				for dbresult.Next() {
					var sellerid int
					dbresult.Scan(&sellerid)
					sql = "insert ignore into admin_role(RoleName,SellerId,Parent,RoleData)values(?,?,?,?)"
					db.QueryNoResult(sql, "运营商超管", sellerid, "god", SellerAuthDataStr)
				}
				dbresult.Close()
			}
			{
				sql := "update admin_role set RoleData = ? where RoleName = ?"
				db.QueryNoResult(sql, SellerAuthDataStr, "运营商超管")
			}
			{
				sql := "select RoleData from admin_role where SellerId = -1 and RoleName = '超级管理员'"
				var dbauthdata string
				db.QueryScan(sql, []interface{}{}, &dbauthdata)
				if dbauthdata != AuthDataStr {
					sql = "select Id,SellerId,RoleName,RoleData from admin_role"
					dbresult, _ := db.Conn().Query(sql)
					for dbresult.Next() {
						var roleid int
						var sellerid int
						var rolename string
						var roledata string
						dbresult.Scan(&roleid, &sellerid, &rolename, &roledata)
						if (sellerid == -1 || sellerid == 0) && rolename == "超级管理员" {
							continue
						}
						jnewdata := make(map[string]interface{})
						json.Unmarshal([]byte(AuthDataStr), &jnewdata)
						clean_auth(jnewdata)
						jrdata := make(map[string]interface{})
						json.Unmarshal([]byte(roledata), &jrdata)
						for k, v := range jrdata {
							set_auth(k, jnewdata, v.(map[string]interface{}))
						}
						newauthbyte, _ := json.Marshal(&jnewdata)
						sql = "update admin_role set RoleData = ? where id = ?"
						db.QueryNoResult(sql, string(newauthbyte), roleid)

					}
					dbresult.Close()
					sql = "update admin_role set RoleData = ? where (SellerId = -1 or SellerId = 0) and RoleName = '超级管理员'"
					db.QueryNoResult(sql, AuthDataStr)
				}
			}
		}
	}
	http = new(abugo.AbuHttp)
	http.Init("server.http.http.port")
	{
		http.PostNoAuth("/api/admin/user/login", user_login)
		http.Post("/api/admin/login_log", login_log)
		http.Post("/api/admin/role/list", role_list)
		http.Post("/api/admin/role/listall", role_listall)
		http.Post("/api/admin/role/roledata", role_data)
		http.Post("/api/admin/role/modify", role_modify)
		http.Post("/api/admin/role/add", role_add)
		http.Post("/api/admin/role/delete", role_delete)
		http.Post("/api/admin/opt_log", opt_log)
		http.Post("/api/admin/user/list", user_list)
		http.Post("/api/admin/user/modify", user_modify)
		http.Post("/api/admin/user/delete", user_delete)
		http.Post("/api/admin/user/add", user_add)
		http.Post("/api/admin/user/google", user_google)
		http.Post("/api/admin/seller/name", seller_name)
		http.Post("/api/admin/seller/list", seller_list)
		http.Post("/api/admin/seller/add", seller_add)
		http.Post("/api/admin/seller/delete", seller_delete)
		http.Post("/api/admin/seller/modify", seller_modify)
		http.Post("/api/admin/seller/upwebclip", seller_webconfig_update)
		http.Post("/api/admin/seller/batch_user_protection", seller_batch_user_protection)
		http.Post("/api/admin/seller/batch_all_user_protection", seller_batch_all_user_protection)
		http.Post("/api/admin/config/list", config_list)
		http.Post("/api/admin/config/add", config_add)
		http.Post("/api/admin/config/modify", config_modify)
		http.Post("/api/admin/config/delete", config_delete)
		http.PostNoAuth("/api/admin/google_qrcode", google_qrcode)
	}
	logs.Debug("*******************start*******************")
}

func GetRequestDataAndToken(ctx *abugo.AbuHttpContent, errcode *int, binderr error, reqdata interface{}, mainmenu string, submenu string, opt string, logopt string) *TokenData {
	if ctx.RespErr(binderr, errcode) {
		return nil
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, mainmenu, submenu, opt), errcode, "权限不足") {
		return nil
	}
	v := reflect.ValueOf(reqdata).FieldByName("SellerId")
	if v.IsValid() {
		if ctx.RespErrString(token.SellerId > 0 && int(v.Int()) != token.SellerId, errcode, "运营商不正确") {
			return nil
		}
	}
	WriteAdminLog(logopt, ctx, reqdata)
	return token
}

func GetRequestDataAndTokenJudgeSellerId(ctx *abugo.AbuHttpContent, errcode *int, binderr error, reqdata interface{}, SellerId []int32, mainmenu string, submenu string, opt string, logopt string) *TokenData {
	if ctx.RespErr(binderr, errcode) {
		return nil
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, mainmenu, submenu, opt), errcode, "权限不足") {
		return nil
	}
	if token.SellerId > 0 {
		if len(SellerId) == 0 || SellerId[0] != int32(token.SellerId) {
			ctx.RespErrString(true, errcode, "运营商不正确")
			return nil
		}
	}
	WriteAdminLog(logopt, ctx, reqdata)
	return token
}

func seller_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "运营商管理", "查", "查询运营商")
	if token == nil {
		return
	}
	total, data := db.Table("x_seller").OrderBy("SellerId ASC").PageData(reqdata.Page, reqdata.PageSize)

	for _, item := range *data {
		for key, value := range item {
			if key == "SocialLinks" && value == nil {
				item[key] = `{"1":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"2":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"3":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"4":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"5":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"6":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"7":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"8":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"9":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"10":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"11":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"12":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"13":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"14":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""}}`
			}
		}

		// 添加新手保护状态：当运营商下任意域名的UserProtect为1时，该运营商状态为开启
		sellerId := item["SellerId"]
		userProtectEnabled := false

		// 查询该运营商下是否有任何域名开启了用户保护
		protectSQL := `
			SELECT COUNT(1) as cnt FROM x_channel_host 
			WHERE UserProtect = 1 AND ChannelId IN (
				SELECT ChannelId FROM x_channel WHERE SellerId = ?
			)
		`
		protectResult, err := db.Query(protectSQL, []interface{}{sellerId})
		if err == nil && protectResult != nil && len(*protectResult) > 0 {
			countData := (*protectResult)[0]
			if cnt, ok := countData["cnt"]; ok {
				if cntInt, ok := cnt.(int64); ok && cntInt > 0 {
					userProtectEnabled = true
				}
			}
		}

		// 添加用户保护状态字段
		if userProtectEnabled {
			item["UserProtectStatus"] = 1 // 开启
		} else {
			item["UserProtectStatus"] = 2 // 关闭
		}
	}

	ctx.Put("total", total)
	ctx.Put("data", *data)
	ctx.RespOK()
}

func seller_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	type RequestData struct {
		SellerName string `validate:"required"`
		State      int    `validate:"required"`
		Remark     string
		GoogleCode string
		//ChannelName string
		ShowName string
		//Host        string
		Icon          string
		Logo          string
		Logo2         string
		IosIcon       string
		SampleName    string
		SocialLinks   string
		ThirdAuth     string
		CustomerLinks string
		Lang          string
		AiSwitch      int32
	}
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "运营商管理", "增", "添加运营商")
	if token == nil {
		return
	}

	err := daoXHashGame.Transaction(func(tx *dao.Query) error {
		xSellerDao := tx.XSeller
		xSellerDb := xSellerDao.WithContext(nil)

		if reqdata.SocialLinks == "" {
			reqdata.SocialLinks = `{"1":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"2":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"3":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"4":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"5":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"6":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"7":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"8":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"9":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"10":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"11":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"12":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"13":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"14":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""}}`
		}

		if reqdata.ThirdAuth == "" {
			reqdata.ThirdAuth = `{"GoogleAppId":"","WhatsAppAppId:":"","LineAppId":"","InstagramAppId":"","XAppId":"","FacebookAppId":""}`
		}

		if reqdata.CustomerLinks == "" {
			reqdata.CustomerLinks = `{"ai":0,"1":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"2":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"3":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"4":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"5":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"6":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"7":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"8":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"9":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"10":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"11":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"12":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"13":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"14":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""}}`
		}

		seller := &model.XSeller{
			SellerName:    reqdata.SellerName,
			State:         2,
			Remark:        reqdata.Remark,
			Icon:          reqdata.Icon,
			Logo:          reqdata.Logo,
			Logo2:         reqdata.Logo2,
			IosIcon:       reqdata.IosIcon,
			ShowName:      reqdata.ShowName,
			SampleName:    reqdata.SampleName,
			SocialLinks:   reqdata.SocialLinks,
			ThirdAuth:     reqdata.ThirdAuth,
			CustomerLinks: reqdata.CustomerLinks,
			Lang:          reqdata.Lang,
			AiSwitch:      reqdata.AiSwitch,
		}
		err := xSellerDb.Create(seller)
		if err != nil {
			return err
		}

		// 新增x_hbc配置，复制SellerId = 20 的配置
		newSellerId := int(seller.SellerID)
		err = copyHbcConfig(newSellerId)
		if err != nil {
			logs.Error("Failed to copy HBC config for SellerId %d: %v", newSellerId, err)
			// 不返回错误，因为HBC配置复制失败不应该影响seller创建
		}
		// 新增x_config配置，复制SellerId=20 ChannelId=0的配置
		err = copyXConfigFromTemplate(newSellerId)
		if err != nil {
			logs.Error("Failed to copy x_config for SellerId %d: %v", newSellerId, err)
			// 不返回错误，因为x_config配置复制失败不应该影响seller创建
		}

		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

func seller_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId      int
		SellerName    string `validate:"required"`
		State         int    `validate:"required"`
		Remark        string
		GoogleCode    string
		ShowName      string
		Icon          string
		Logo          string
		Logo2         string
		IosIcon       string
		SampleName    string
		SocialLinks   string
		ThirdAuth     string
		CustomerLinks string
		Lang          string
		AiSwitch      int32
	}
	errcode := 0
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "运营商管理", "改", "修改运营商")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, "")
	db.Table("x_seller").Where(where).Update(map[string]interface{}{
		"SellerName":    reqdata.SellerName,
		"State":         reqdata.State,
		"Remark":        reqdata.Remark,
		"ShowName":      reqdata.ShowName,
		"Icon":          reqdata.Icon,
		"Logo":          reqdata.Logo,
		"Logo2":         reqdata.Logo2,
		"IosIcon":       reqdata.IosIcon,
		"SampleName":    reqdata.SampleName,
		"SocialLinks":   reqdata.SocialLinks,
		"ThirdAuth":     reqdata.ThirdAuth,
		"CustomerLinks": reqdata.CustomerLinks,
		"Lang":          reqdata.Lang,
		"AiSwitch":      reqdata.AiSwitch,
	})
	ctx.RespOK()
}

func seller_webconfig_update(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	err = webclip.GenerateWebConfig(int32(reqdata.SellerId), 0, daoXHashGame)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	// 打印输出结果
	ctx.RespOK()
}

func seller_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "运营商管理", "删", "删除运营商")
	if token == nil {
		return
	}

	db.Query("delete from x_seller where SellerId = ?", []interface{}{reqdata.SellerId})
	db.Query("delete from x_hbc where SellerId = ?", []interface{}{reqdata.SellerId})
	db.Query("delete from x_config where SellerId = ?", []interface{}{reqdata.SellerId})
	ctx.RespOK()
}

// seller_batch_user_protection 批量设置运营商用户保护开关
func seller_batch_user_protection(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerIds   []int32 `json:"sellerIds" validate:"required"`   // 运营商ID列表
		UserProtect int32   `json:"userProtect" validate:"required"` // 用户保护开关：1=开启，2=关闭
	}
	errcode := 0
	reqdata := RequestData{}

	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "运营商管理", "改", "批量设置运营商用户保护")
	if token == nil {
		return
	}

	if len(reqdata.SellerIds) == 0 {
		ctx.RespErrString(true, &errcode, "请选择要操作的运营商")
		return
	}

	if reqdata.UserProtect != 1 && reqdata.UserProtect != 2 {
		ctx.RespErrString(true, &errcode, "用户保护开关值无效，请传入1(开启)或2(关闭)")
		return
	}

	// 更新指定运营商下的所有渠道的所有域名的用户保护开关
	sellerIdList := make([]interface{}, len(reqdata.SellerIds))
	for i, v := range reqdata.SellerIds {
		sellerIdList[i] = v
	}

	placeholders := strings.Repeat("?,", len(reqdata.SellerIds))
	placeholders = placeholders[:len(placeholders)-1] // 移除最后一个逗号

	sql := fmt.Sprintf(`UPDATE x_channel_host 
		SET UserProtect = ?
		WHERE ChannelId IN (
			SELECT ChannelId FROM x_channel WHERE SellerId IN (%s)
		)`, placeholders)

	params := []interface{}{reqdata.UserProtect}
	params = append(params, sellerIdList...)

	result, err := db.Query(sql, params)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	rowsAffected := int64(0)
	if result != nil && len(*result) > 0 {
		// MySQL的UPDATE操作返回的是影响的行数，但具体获取方式可能因驱动而异
		// 这里先设置为0，实际使用时可能需要调整
	}

	operationText := "开启"
	if reqdata.UserProtect == 2 {
		operationText = "关闭"
	}

	ctx.Put("RowsAffected", rowsAffected)
	ctx.Put("message", fmt.Sprintf("成功%s%d个运营商下所有域名的用户保护功能", operationText, len(reqdata.SellerIds)))
	ctx.RespOK()
}

// seller_batch_all_user_protection 批量操作所有运营商的用户保护开关（全部开启或全部关闭）
func seller_batch_all_user_protection(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		UserProtect int32  `json:"userProtect" validate:"required"` // 用户保护开关：1=开启，2=关闭
		GoogleCode  string `json:"googleCode" validate:"required"`  // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}

	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "运营商管理", "改", "批量操作所有运营商新手保护")
	if token == nil {
		return
	}

	if reqdata.UserProtect != 1 && reqdata.UserProtect != 2 {
		ctx.RespErrString(true, &errcode, "用户保护开关值无效，请传入1(开启)或2(关闭)")
		return
	}

	// 验证谷歌验证码
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	// 更新所有域名的用户保护开关
	sql := "UPDATE x_channel_host SET UserProtect = ?"
	result, err := db.Query(sql, []interface{}{reqdata.UserProtect})
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	rowsAffected := int64(0)
	if result != nil && len(*result) > 0 {
		// MySQL的UPDATE操作返回的是影响的行数，但具体获取方式可能因驱动而异
		// 这里先设置为0，实际使用时可能需要调整
	}

	operationText := "开启"
	if reqdata.UserProtect == 2 {
		operationText = "关闭"
	}

	// 记录操作日志
	logMessage := fmt.Sprintf("批量%s所有运营商的用户保护功能，影响%d个域名", operationText, rowsAffected)
	WriteAdminLog(logMessage, ctx, reqdata)

	ctx.Put("RowsAffected", rowsAffected)
	ctx.Put("message", fmt.Sprintf("成功%s所有运营商下所有域名的用户保护功能", operationText))
	ctx.RespOK()
}
func clean_auth(node map[string]interface{}) {
	for k, v := range node {
		if strings.Index(reflect.TypeOf(v).Name(), "float") >= 0 {
			node[k] = 0
		} else {
			clean_auth(v.(map[string]interface{}))
		}
	}
}
func set_auth(parent string, newdata map[string]interface{}, node map[string]interface{}) {
	for k, v := range node {
		if strings.Index(reflect.TypeOf(v).Name(), "float") >= 0 {
			if abugo.GetFloat64FromInterface(v) != 1 {
				continue
			}
			path := strings.Split(parent, ".")
			if len(path) == 0 {
				continue
			}
			fk, fok := newdata[path[0]]
			if !fok {
				continue
			}
			var pn *interface{} = &fk
			var finded bool = true
			for i := 1; i < len(path); i++ {
				tk := path[i]
				tv, ok := (*pn).(map[string]interface{})[tk]
				if !ok {
					finded = false
					break
				}
				pn = &tv
			}
			if finded {
				(*pn).(map[string]interface{})[k] = 1
			}
		} else {
			set_auth(parent+"."+k, newdata, v.(map[string]interface{}))
		}
	}
}

func Http() *abugo.AbuHttp {
	return http
}

func Redis() *abugo.AbuRedis {
	return redis
}

func Db() *abugo.AbuDb {
	return db
}

func DbReport() *abugo.AbuDb {
	return dbReport
}

func DbStat() *abugo.AbuDb {
	return dbStat
}

func XRedis() *xgo.XRedis {
	return xredis
}

func XDbReport() *xgo.XDb {
	return xdbReport
}

func XDb() *xgo.XDb {
	return xdb
}

func DaoxHashGame() *dao.Query {
	return daoXHashGame
}
func DaoxHashStat() *daoStat.Query {
	return daoXHashStat
}

func Debug() bool {
	return debug
}

func Run() {
	abugo.Run()
}

type TokenData struct {
	Account      string
	SellerId     int
	ChannelId    int
	AuthData     string
	GoogleSecret string
	UserId       int
	Readonly     bool
}

func GetToken(ctx *abugo.AbuHttpContent) *TokenData {
	tokendata := TokenData{}
	err := json.Unmarshal([]byte(ctx.TokenData), &tokendata)
	if err != nil {
		return nil
	}
	return &tokendata
}

func WriteAdminLog(opt string, ctx *abugo.AbuHttpContent, data interface{}) {
	if !readonly {
		token := ctx.Token
		strdata, _ := json.Marshal(&data)
		tokendata := GetToken(ctx)
		Ip := ctx.GetIp()
		go func() {
			sql := "insert into admin_opt_log(Account,Opt,Token,Data,Ip,SellerId,ChannelId)values(?,?,?,?,?,?,?)"
			db.QueryNoResult(sql, tokendata.Account, opt, token, string(strdata), Ip, tokendata.SellerId, tokendata.ChannelId)
		}()
	}
}
func Auth2(td *TokenData, m string, s string, o string) bool {
	defer recover()
	authdata := make(map[string]interface{})
	json.Unmarshal([]byte(td.AuthData), &authdata)
	im, imok := authdata[m]
	if !imok {
		return false
	}
	is, isok := im.(map[string]interface{})[s]
	if !isok {
		return false
	}
	io, iook := is.(map[string]interface{})[o]
	if !iook {
		return false
	}
	if strings.Index(reflect.TypeOf(io).Name(), "float64") < 0 {
		return false
	}
	if abugo.GetFloat64FromInterface(io) != 1 {
		return false
	}
	return true
}
func user_login(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Account    string `validate:"required"`
		Password   string `validate:"required"`
		VerifyCode string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.VerifyCode == "" {
		reqdata.VerifyCode = reqdata.GoogleCode
	}
	type MenuData struct {
		Icon  string     `json:"icon"`
		Index string     `json:"index"`
		Title string     `json:"title"`
		Subs  []MenuData `json:"subs"`
	}
	rkey := fmt.Sprintf("%v:%v:admin_login:%v", Project(), Module(), reqdata.Account)
	loc := Redis().SetNxString(rkey, "1", 10)
	if ctx.RespErrString(loc != nil, &errcode, "操作频繁,请稍后再试") {
		return
	}
	puser, usererr := db.Query("select * from admin_user where Account = ?", []interface{}{reqdata.Account})
	if ctx.RespErr(usererr, &errcode) {
		return
	}
	if ctx.RespErrString(len(*puser) == 0, &errcode, "账号不存在") {
		return
	}
	IpWhite := abugo.GetStringFromInterface((*puser)[0]["IpWhite"])
	IsIpWhite := abugo.GetInt64FromInterface((*puser)[0]["IsIpWhite"])

	if !debug && IsIpWhite == 1 && len(IpWhite) == 0 {
		ctx.RespErrString(true, &errcode, "已开启IP白名单限制,请联系管理员")
		return
	}

	if !debug && IsIpWhite == 1 && len(IpWhite) > 0 && ctx.RespErrString(strings.Index(IpWhite, ctx.GetIp()) < 0, &errcode, "IP白名单限制") {
		logs.Debug("Account:", reqdata.Account, "IpWhite:", IpWhite, "ctx.GetIp():", ctx.GetIp())
		return
	}

	State := abugo.GetInt64FromInterface((*puser)[0]["State"])
	if ctx.RespErrString(State != 1, &errcode, "账号已被禁用") {
		return
	}

	Password := abugo.GetStringFromInterface((*puser)[0]["Password"])
	if ctx.RespErrString(Password != reqdata.Password, &errcode, "密码不正确") {
		return
	}
	SellerId := int(abugo.GetInt64FromInterface((*puser)[0]["SellerId"]))
	if SellerId > 0 {
		pseller, sellererr := db.Query("select * from x_seller where SellerId = ?", []interface{}{SellerId})
		if ctx.RespErr(sellererr, &errcode) {
			return
		}
		if ctx.RespErrString(len(*pseller) == 0, &errcode, "运营商不存在") {
			return
		}
		if ctx.RespErrString(abugo.GetInt64FromInterface((*pseller)[0]["State"]) != 1, &errcode, "所属运营商已停用") {
			return
		}
	}
	pauth, autherr := db.Query("select RoleData from admin_role where RoleName = ? and SellerId = ?", []interface{}{(*puser)[0]["RoleName"], SellerId})
	if ctx.RespErr(autherr, &errcode) {
		return
	}
	if ctx.RespErrString(len(*pauth) == 0, &errcode, "角色不存在") {
		return
	}
	GoogleSecret := abugo.GetStringFromInterface((*puser)[0]["GoogleSecret"])
	if ctx.RespErrString(!debug && len(GoogleSecret) > 0 && !abugo.VerifyGoogleCode(GoogleSecret, reqdata.VerifyCode), &errcode, "谷歌验证码不正确") {
		return
	}
	Token := abugo.GetStringFromInterface((*puser)[0]["Token"])
	if len(Token) > 0 {
		http.DelToken(Token)
	}
	Id := int(abugo.GetFloat64FromInterface((*puser)[0]["Id"]))
	ChannelId := int(abugo.GetInt64FromInterface((*puser)[0]["ChannelId"]))
	//if ctx.RespErrString(ChannelId > 0, &errcode, "该账号不可登录") {
	//	return
	//}
	tokendata := TokenData{}
	tokendata.Account = reqdata.Account
	tokendata.SellerId = SellerId
	tokendata.ChannelId = ChannelId
	tokendata.AuthData = abugo.GetStringFromInterface((*pauth)[0]["RoleData"])
	tokendata.GoogleSecret = abugo.GetStringFromInterface((*puser)[0]["OptGoogleSecret"])
	tokendata.UserId = Id // 用户id
	tokendata.Readonly = readonly
	token := abugo.GetUuid()
	LiftTime := int64(0)
	if !readonly {
		sqlstr := "update admin_user set Token = ?,LoginCount = LoginCount + 1,LoginTime = now(),LoginIp = ? where id = ?"
		db.Query(sqlstr, []interface{}{token, ctx.GetIp(), Id})

		sqlstr = "insert into admin_login_log(UserId,SellerId,Account,Token,LoginIp,ChannelId)values(?,?,?,?,?,?) "

		db.Query(sqlstr, []interface{}{Id, SellerId, reqdata.Account, token, ctx.GetIp(), ChannelId})
		LiftTime = GetConfigInt(0, 0, "tokenlifetime")
		http.SetTokenLifeTime(":lifetime", int(LiftTime))
	} else {
		LiftTime = GetConfigInt(0, 0, "tokenlifetimereadonly")
		http.SetTokenLifeTime(":lifetimereadonly", int(LiftTime))
	}
	http.SetToken(token, tokendata)

	http.RenewToken(token)
	menu := []MenuData{}
	json.Unmarshal([]byte(MenuDataStr), &menu)
	parser := fastjson.Parser{}
	jauthdata, _ := parser.ParseBytes([]byte(tokendata.AuthData))
	//三级菜单
	for i := 0; i < len(menu); i++ {
		for j := 0; j < len(menu[i].Subs); j++ {
			smenu := []MenuData{}
			for k := 0; k < len(menu[i].Subs[j].Subs); k++ {
				open := jauthdata.GetInt(menu[i].Title, menu[i].Subs[j].Title, menu[i].Subs[j].Subs[k].Title, "查")
				if open == 1 {
					smenu = append(smenu, menu[i].Subs[j].Subs[k])
				}
			}
			menu[i].Subs[j].Subs = smenu
		}
	}
	//二级菜单
	for i := 0; i < len(menu); i++ {
		smenu := []MenuData{}
		for j := 0; j < len(menu[i].Subs); j++ {
			open := jauthdata.GetInt(menu[i].Title, menu[i].Subs[j].Title, "查")
			if open == 1 || len(menu[i].Subs[j].Subs) > 0 {
				smenu = append(smenu, menu[i].Subs[j])
			}
		}
		menu[i].Subs = smenu
	}
	//一级菜单
	smenu := []MenuData{}
	for i := 0; i < len(menu); i++ {
		open := jauthdata.GetInt(menu[i].Title, "查")
		if open == 1 || len(menu[i].Subs) > 0 {
			smenu = append(smenu, menu[i])
		}
	}
	menu = smenu
	jauth := make(map[string]interface{})
	json.Unmarshal([]byte(tokendata.AuthData), &jauth)
	ctx.Put("UserId", Id)
	ctx.Put("SellerId", SellerId)
	ctx.Put("Account", reqdata.Account)
	ctx.Put("AuthData", jauth)
	ctx.Put("MenuData", menu)
	ctx.Put("Token", token)
	ctx.Put("LoginTime", (*puser)[0]["LoginTime"])
	ctx.Put("Ip", ctx.GetIp())
	ctx.Put("LoginCount", (*puser)[0]["LoginCount"])
	ctx.Put("Version", "1.0.0")
	ctx.Put("RecvNotice", (*puser)[0]["RecvNotice"])
	ctx.Put("FileBaseUrl", ImageUrl())
	ctx.RespOK()
}

func login_log(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		Account   string
		SellerId  int
		StartTime int64
		EndTime   int64
		LoginIp   string
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "登录日志", "查", "查看管理员登录日志")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "Account", "=", reqdata.Account, "")
	where.Add("and", "LoginIp", "=", reqdata.LoginIp, "")
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	total, presult := Db().Table("admin_login_log").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	q := qqwry.NewQQwry("./config/ipdata.dat")
	for i := 0; i < len(*presult); i++ {
		ip := abugo.GetStringFromInterface((*presult)[i]["LoginIp"])
		if strings.Index(ip, ".") > 0 {
			q.Find(ip)
			(*presult)[i]["IpAddr"] = fmt.Sprintf("%s %s", q.Country, q.City)
		} else {
			(*presult)[i]["IpAddr"] = ""
		}
	}
	ctx.Put("data", *presult)
	ctx.Put("total", total)
	ctx.RespOK()
}
func role_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page     int
		PageSize int
		SellerId int
		RoleType int
	}
	errcode := 0
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "角色管理", "查", "查看角色列表")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "RoleType", "=", reqdata.RoleType, 0)
	total, data := db.Table("admin_role").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()
}
func role_listall(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		RoleType int
	}
	errcode := 0
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "角色管理", "查", "查看角色列表")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, "")
	where.Add("and", "RoleType", "=", reqdata.RoleType, 0)
	pdata, err := db.Table("admin_role").Where(where).GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}
	names := []string{}
	for i := 0; i < len(*pdata); i++ {
		names = append(names, abugo.GetStringFromInterface((*pdata)[i]["RoleName"]))
	}
	ctx.RespOK(names)
}
func role_data(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		RoleName string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "角色管理", "查", "查看角色")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "RoleName", "=", reqdata.RoleName, 0)
	prole, roleerr := db.Table("admin_role").Where(where).GetOne()
	if prole != nil {
		ctx.Put("RoleData", (*prole)["RoleData"])
	}
	if ctx.RespErr(roleerr, &errcode) {
		return
	}
	psrole, sroleerr := db.Query("select RoleData from admin_role where SellerId = -1 and RoleName = '超级管理员' limit 1", []interface{}{})
	if psrole != nil {
		ctx.Put("SuperRoleData", (*psrole)[0]["RoleData"])
	}
	if ctx.RespErr(sroleerr, &errcode) {
		return
	}
	ctx.RespOK()
}
func role_check(parent string, parentdata map[string]interface{}, data map[string]interface{}, result *string) {
	defer recover()
	for k, v := range data {
		if strings.Index(reflect.TypeOf(v).Name(), "float") >= 0 {
			if abugo.GetFloat64FromInterface(v) != 1 {
				continue
			}
			path := strings.Split(parent, ".")
			if len(path) == 0 {
				continue
			}
			fk, fok := parentdata[path[0]]
			if !fok {
				continue
			}
			var pn *interface{} = &fk
			var finded bool = true
			for i := 1; i < len(path); i++ {
				tk := path[i]
				tv, ok := (*pn).(map[string]interface{})[tk]
				if !ok {
					finded = false
					break
				}
				pn = &tv
			}
			if finded {
				fv := abugo.GetFloat64FromInterface((*pn).(map[string]interface{})[k])
				if fv != 1 {
					(*result) = "fail"
				}
			} else {
				(*result) = "fail"
			}

		} else {
			role_check(parent+"."+k, parentdata, v.(map[string]interface{}), result)
		}
	}
}

func get_auth(jdata map[string]interface{}, path string) interface{} {
	paths := strings.Split(path, ".")
	for i := 0; i < len(paths); i++ {
		if strings.Index(reflect.TypeOf(jdata).Name(), "float") >= 0 {
			return nil
		}
		p := paths[i]
		v := jdata[p]
		if v == nil {
			return nil
		}
		if strings.Index(reflect.TypeOf(v).Name(), "float") >= 0 {
			return v.(float64)
		}
		jdata = v.(map[string]interface{})
	}
	return jdata
}

func set_auth_v(jdata map[string]interface{}, path string, val float64) {
	paths := strings.Split(path, ".")
	var pn *map[string]interface{}
	for i := 0; i < len(paths); i++ {
		p := paths[i]
		v := jdata[p]
		if strings.Index(reflect.TypeOf(v).Name(), "float") >= 0 {
			(*pn)[p] = val
			return
		}
		n := jdata[p].(map[string]interface{})
		pn = &n
		jdata = v.(map[string]interface{})
	}
}

func check_parent_auth(path string, jparent map[string]interface{}, jchild map[string]interface{}) {
	authc := get_auth(jchild, path)
	if strings.Index(reflect.TypeOf(authc).Name(), "float") >= 0 {
		vc := int(authc.(float64))
		if vc == 1 {
			authp := get_auth(jparent, path)
			if authp == nil {
				set_auth_v(jchild, path, 0)
				return
			}
			vp := int(authp.(float64))
			if vp != vc {
				set_auth_v(jchild, path, float64(vp))
			}
		}
	} else {
		for k := range authc.(map[string]interface{}) {
			check_parent_auth(path+"."+k, jparent, jchild)
		}
	}
}

func role_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		RoleName   string `validate:"required"`
		RoleData   string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "角色管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var ParentRoleName string
	sql := "select Parent from admin_role where SellerId = ? and RoleName = ?"
	db.QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.RoleName}, &ParentRoleName)
	if ctx.RespErrString(len(ParentRoleName) == 0, &errcode, "上级角色不存在") {
		return
	}
	var ParentRoleData string
	sql = "select RoleData from admin_role where SellerId = ? and RoleName = ?"
	db.QueryScan(sql, []interface{}{reqdata.SellerId, ParentRoleName}, &ParentRoleData)
	if ctx.RespErrString(len(ParentRoleData) == 0, &errcode, "获取上级角色数据失败") {
		return
	}
	jparent := make(map[string]interface{})
	err = json.Unmarshal([]byte(ParentRoleData), &jparent)
	if ctx.RespErr(err, &errcode) {
		return
	}
	jdata := make(map[string]interface{})
	err = json.Unmarshal([]byte(reqdata.RoleData), &jdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	result := ""
	for k, v := range jdata {
		role_check(k, jparent, v.(map[string]interface{}), &result)
	}
	if ctx.RespErrString(len(result) > 0, &errcode, "权限不可大于上级角色") {
		return
	}
	roles := []string{reqdata.RoleName}
	for {
		if len(roles) == 0 {
			break
		}
		rn := roles[0]
		roles = roles[1:]
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "Parent", "=", rn, "")
		presult, err := db.Table("admin_role").Where(where).GetList()
		if err != nil {
			break
		}
		for i := 0; i < len(*presult); i++ {
			roles = append(roles, abugo.GetStringFromInterface((*presult)[i]["RoleName"]))
		}
		var ChildRoleData string
		sql = "select RoleData from admin_role where SellerId = ? and RoleName = ?"
		db.QueryScan(sql, []interface{}{reqdata.SellerId, rn}, &ChildRoleData)
		jchild := make(map[string]interface{})
		json.Unmarshal([]byte(ChildRoleData), &jchild)
		for k := range jchild {
			check_parent_auth(k, jdata, jchild)
		}
		childauthbytes, err := json.Marshal(&jchild)
		if err == nil {
			sql = "update admin_role set RoleData = ? where SellerId = ? and RoleName = ?"
			db.QueryNoResult(sql, string(childauthbytes), reqdata.SellerId, rn)
		}
	}
	sql = "update admin_role set  RoleData = ? where SellerId = ? and RoleName = ?"
	err = db.QueryNoResult(sql, reqdata.RoleData, reqdata.SellerId, reqdata.RoleName)
	if ctx.RespErr(err, &errcode) {
		return
	}
	WriteAdminLog("修改角色", ctx, reqdata)
	ctx.RespOK()
}
func role_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Parent     string `validate:"required"`
		SellerId   int
		RoleName   string `validate:"required"`
		RoleData   string `validate:"required"`
		GoogleCode string
		RoleType   int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "角色管理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerId > 0 {
		sql := fmt.Sprintf("select SellerId from %s where SellerId = ? and state = 1", db_seller_tablename)
		var sellerid int
		db.QueryScan(sql, []interface{}{reqdata.SellerId}, &sellerid)
		if ctx.RespErrString(sellerid == 0, &errcode, "运营商已禁用或不存在") {
			return
		}
	}
	var roleid int = 0
	sql := "select id from admin_role where SellerId = ? and RoleName = ? "
	db.QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.RoleName}, &roleid)
	if ctx.RespErrString(roleid > 0, &errcode, "角色已经存在") {
		return
	}
	var ParentRoleData string
	sql = "select RoleData from admin_role where SellerId = ? and RoleName = ?"
	db.QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.Parent}, &ParentRoleData)
	if ctx.RespErrString(len(ParentRoleData) == 0, &errcode, "上级角色不存在") {
		return
	}
	jparent := make(map[string]interface{})
	err = json.Unmarshal([]byte(ParentRoleData), &jparent)
	if ctx.RespErr(err, &errcode) {
		return
	}
	jdata := make(map[string]interface{})
	err = json.Unmarshal([]byte(reqdata.RoleData), &jdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	result := ""
	for k, v := range jdata {
		role_check(k, jparent, v.(map[string]interface{}), &result)
	}
	if ctx.RespErrString(len(result) > 0, &errcode, "权限不可大于上级角色") {
		return
	}
	sql = "insert into admin_role(RoleName,SellerId,Parent,RoleData,RoleType)values(?,?,?,?,?)"
	param := []interface{}{reqdata.RoleName, reqdata.SellerId, reqdata.Parent, reqdata.RoleData, reqdata.RoleType}
	err = db.QueryNoResult(sql, param...)
	if ctx.RespErr(err, &errcode) {
		logs.Error(err)
		return
	}
	WriteAdminLog("添加角色", ctx, reqdata)
	ctx.RespOK()
}
func role_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		RoleName   string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "角色管理", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "select id,Parent from admin_role where SellerId = ? and Parent = ?"
	var id int
	var parent string
	db.QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.RoleName}, &id, &parent)
	if ctx.RespErrString(id > 0, &errcode, "该角色有下级角色,不可删除") {
		return
	}
	if ctx.RespErrString(parent == "god", &errcode, "该角色不可删除") {
		return
	}
	id = 0
	sql = "select id from admin_user where SellerId = ? and RoleName = ?"
	db.QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.RoleName}, &id)
	if ctx.RespErrString(id > 0, &errcode, "该角色下存在账号,不可删除") {
		return
	}
	sql = "delete from admin_role where SellerId = ? and RoleName = ?"
	db.QueryNoResult(sql, reqdata.SellerId, reqdata.RoleName)
	WriteAdminLog("删除角色", ctx, reqdata)
	ctx.RespOK()
}
func opt_log(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		Account   string
		Opt       string
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "操作日志", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "Account", "=", reqdata.Account, "")
	where.Add("and", "Opt", "=", reqdata.Opt, "")
	total, data := db.Table("admin_opt_log").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()
}
func user_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page        int
		PageSize    int
		Account     string
		SellerId    int
		ChannelId   int
		AccountType int //1普通账号,2客服工号
		CSGroup     string
		CSId        string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "账号管理", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	total, data := DBAdminUser_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.Account, reqdata.ChannelId, reqdata.AccountType, reqdata.CSGroup, reqdata.CSId)
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
}
func user_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Account     string `validate:"required"`
		SellerId    int
		Password    string
		RoleName    string `validate:"required"`
		State       int    `validate:"required"`
		Remark      string
		IpWhite     string
		RecvNotice  int
		ChannelId   int
		MaxAddMoney int
		CSGroup     string
		CSId        string
		GoogleCode  string
		RoleType    int `validate:"required"`
		IsIpWhite   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.CSId != "" && reqdata.CSGroup == "" {
		ctx.RespErrString(true, &errcode, "客服组不可为空")
		return
	}

	if reqdata.CSId != "" && reqdata.RoleName != "客服工号" {
		ctx.RespErrString(true, &errcode, "角色必须为:客服工号")
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "账号管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.MaxAddMoney > utils.ArtificialCapitalIncrease {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("最大增资,超过额度%d", utils.ArtificialCapitalIncrease))
		return
	}
	reqdata.IpWhite = strings.Trim(reqdata.IpWhite, "")
	sql := "select id from admin_role  where SellerId = ? and RoleName = ? and RoleType = ?"
	var rid int
	db.QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.RoleName, reqdata.RoleType}, &rid)
	if ctx.RespErrString(rid == 0, &errcode, "角色不存在") {
		return
	}
	if len(reqdata.Password) > 0 {
		sql = "update admin_user set RoleName = ?,State = ?,Remark = ?,`Password` = ?,IpWhite = ?,RecvNotice = ?,MaxAddMoney =?,CSGroup = ?,CSId = ?,RoleType = ?,IsIpWhite = ? where Account = ? and ChannelId = ? and SellerId = ?"
		db.QueryNoResult(sql, reqdata.RoleName, reqdata.State, reqdata.Remark, reqdata.Password, reqdata.IpWhite,
			reqdata.RecvNotice, reqdata.MaxAddMoney, reqdata.CSGroup, reqdata.CSId, reqdata.RoleType, reqdata.IsIpWhite,
			reqdata.Account, reqdata.ChannelId, reqdata.SellerId)
	} else {
		sql = "update admin_user set RoleName = ?,State = ?,Remark = ?,IpWhite = ?,RecvNotice = ? ,MaxAddMoney = ? ,CSGroup = ?,CSId = ?,RoleType = ?,IsIpWhite = ? where Account = ? and ChannelId = ? and SellerId = ?"
		db.QueryNoResult(sql, reqdata.RoleName, reqdata.State, reqdata.Remark, reqdata.IpWhite, reqdata.RecvNotice, reqdata.MaxAddMoney, reqdata.CSGroup, reqdata.CSId, reqdata.RoleType, reqdata.IsIpWhite,
			reqdata.Account, reqdata.ChannelId, reqdata.SellerId)
	}
	if reqdata.State != 1 {
		sql = "select Token from admin_user where Account = ? and SellerId = ? "
		var tokenstr string
		db.QueryScan(sql, []interface{}{reqdata.Account, reqdata.SellerId}, &tokenstr)
		if len(tokenstr) > 0 {
			http.DelToken(tokenstr)
		}
	}
	WriteAdminLog("修改管理员", ctx, reqdata)
	KickOutAdminLogin(reqdata.Account)
	ctx.RespOK()
}
func user_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id         int    `validate:"required"`
		Account    string `validate:"required"`
		SellerId   int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "账号管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	KickOutAdminLogin(reqdata.Account)
	sql := "delete from admin_user where Id = ? and Account = ? and SellerId = ?"
	db.QueryNoResult(sql, reqdata.Id, reqdata.Account, reqdata.SellerId)
	WriteAdminLog("删除管理员", ctx, reqdata)
	KickOutAdminLogin(reqdata.Account)
	ctx.RespOK()
}
func user_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Account     string `validate:"required"`
		SellerId    int
		Password    string `validate:"required"`
		RoleName    string `validate:"required"`
		State       int    `validate:"required"`
		Remark      string
		IpWhite     string
		RecvNotice  int
		ChannelId   int
		MaxAddMoney int
		CSGroup     string
		CSId        string
		GoogleCode  string
		RoleType    int `validate:"required"`
		IsIpWhite   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.CSId != "" && reqdata.CSGroup == "" {
		ctx.RespErrString(true, &errcode, "客服组不可为空")
		return
	}

	if reqdata.CSId != "" && reqdata.RoleName != "客服工号" {
		ctx.RespErrString(true, &errcode, "角色必须为:客服工号")
		return
	}

	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "账号管理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "select id from admin_role  where SellerId = ? and RoleName = ? and RoleType = ?"
	var rid int
	db.QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.RoleName, reqdata.RoleType}, &rid)
	if ctx.RespErrString(rid == 0, &errcode, "角色不存在") {
		return
	}
	sql = "select id from admin_user where Account = ?"
	var uid int
	db.QueryScan(sql, []interface{}{reqdata.Account}, &uid)
	if ctx.RespErrString(uid > 0, &errcode, "账号已经存在") {
		return
	}
	sql = "insert into admin_user(Account,Password,SellerId,RoleName,State,IpWhite,RecvNotice,Remark,ChannelId,MaxAddMoney,CSGroup,CSId,RoleType,IsIpWhite)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	err = db.QueryNoResult(sql, reqdata.Account, reqdata.Password,
		reqdata.SellerId, reqdata.RoleName, reqdata.State, reqdata.IpWhite, reqdata.RecvNotice, reqdata.Remark, reqdata.ChannelId,
		reqdata.MaxAddMoney, reqdata.CSGroup, reqdata.CSId, reqdata.RoleType, reqdata.IsIpWhite)
	if ctx.RespErr(err, &errcode) {
		return
	}
	WriteAdminLog("添加管理员", ctx, reqdata)
	ctx.RespOK()
}

type UserGooleLimitedData struct {
	LastTime int64
	Times    int
}

var userGooleLimitedAccount map[string]*UserGooleLimitedData = make(map[string]*UserGooleLimitedData)
var userGooleLimitedIp map[string]*UserGooleLimitedData = make(map[string]*UserGooleLimitedData)

func user_google(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Account    string `validate:"required"`
		SellerId   int
		GoogleCode string
		CodeType   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	//限制调用次数 每个账号每天最多调用20次 每个IP每天最多调用100次
	{
		nowTimeUnix := time.Now().Unix()
		if d, ok := userGooleLimitedAccount[reqdata.Account]; ok {
			if nowTimeUnix-d.LastTime < 86400 {
				if d.Times >= 20 {
					logs.Error("Account=", reqdata.Account, " IP=", ctx.GetIp(), " user_google超过每个账号每天20次限制")
					ctx.RespErrString(true, &errcode, "超过请求次数")
					return
				} else {
					d.Times = d.Times + 1
				}
			} else {
				d.LastTime = nowTimeUnix
				d.Times = 1
			}
		} else {
			userGooleLimitedAccount[reqdata.Account] = &UserGooleLimitedData{
				LastTime: nowTimeUnix,
				Times:    1,
			}
		}
		if d, ok := userGooleLimitedIp[ctx.GetIp()]; ok {
			if nowTimeUnix-d.LastTime < 86400 {
				if d.Times >= 100 {
					logs.Error("Account=", reqdata.Account, " IP=", ctx.GetIp(), " user_google超过每个IP每天100次限制")
					ctx.RespErrString(true, &errcode, "超过请求次数")
					return
				} else {
					d.Times = d.Times + 1
				}
			} else {
				d.LastTime = nowTimeUnix
				d.Times = 1
			}
		} else {
			userGooleLimitedIp[ctx.GetIp()] = &UserGooleLimitedData{
				LastTime: nowTimeUnix,
				Times:    1,
			}
		}
	}

	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "账号管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Account", "=", token.Account, "")
	presult, err := db.Table("admin_user").Select("GoogleSecret,OptGoogleSecret").Where(where).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(presult == nil, &errcode, "管理员不存在") {
		return
	}
	result := *presult
	if result["GoogleSecret"] == nil {
		result["GoogleSecret"] = ""
	}
	if result["OptGoogleSecret"] == nil {
		result["OptGoogleSecret"] = ""
	}
	GoogleSecret := abugo.GetStringFromInterface(result["GoogleSecret"])
	OptGoogleSecret := abugo.GetStringFromInterface(result["OptGoogleSecret"])
	if reqdata.Account != token.Account {
		if ctx.RespErrString(len(OptGoogleSecret) == 0, &errcode, "请先设置操作谷歌验证码") {
			return
		}
		if ctx.RespErr(VerifyGoogleCode(OptGoogleSecret, reqdata.GoogleCode), &errcode) {
			return
		}
	} else {
		if len(OptGoogleSecret) > 0 {
			if ctx.RespErr(VerifyGoogleCode(OptGoogleSecret, reqdata.GoogleCode), &errcode) {
				return
			}
		} else if len(GoogleSecret) > 0 {
			if ctx.RespErr(VerifyGoogleCode(GoogleSecret, reqdata.GoogleCode), &errcode) {
				return
			}
		}
		if reqdata.CodeType == 2 && len(OptGoogleSecret) == 0 {
			if ctx.RespErr(VerifyGoogleCode(GoogleSecret, reqdata.GoogleCode), &errcode) {
				return
			}
		}
	}
	var SellerId int
	sql := "select SellerId from admin_user where account = ?"
	db.QueryScan(sql, []interface{}{reqdata.Account}, &SellerId)
	var SellerName string
	sql = "select SellerName from x_seller where SellerId = ?"
	db.QueryScan(sql, []interface{}{SellerId}, &SellerName)
	if reqdata.CodeType == 1 {
		verifykey := abugo.GetGoogleSecret()
		verifyurl := fmt.Sprintf("otpauth://totp/%s?secret=%s&issuer=%s 登录", reqdata.Account, verifykey, SellerName)
		sql := "update admin_user set GoogleSecret = ? where Account = ? and SellerId = ?"
		db.QueryNoResult(sql, verifykey, reqdata.Account, reqdata.SellerId)
		WriteAdminLog("修改登录谷歌验证", ctx, reqdata)
		ctx.RespOK(verifyurl)
	} else if reqdata.CodeType == 2 {
		verifykey := abugo.GetGoogleSecret()
		verifyurl := fmt.Sprintf("otpauth://totp/%s?secret=%s&issuer=%s 操作", reqdata.Account, verifykey, SellerName)
		sql := "update admin_user set OptGoogleSecret = ? where Account = ? and SellerId = ?"
		db.QueryNoResult(sql, verifykey, reqdata.Account, reqdata.SellerId)
		WriteAdminLog("修改操作谷歌验证", ctx, reqdata)
		ctx.RespOK(verifyurl)
	}
	KickOutAdminLogin(reqdata.Account)
}
func seller_name(ctx *abugo.AbuHttpContent) {
	token := GetToken(ctx)
	if token.SellerId > 0 {
		ctx.Put("data", []interface{}{})
		ctx.RespOK()
		return
	}
	errcode := 0
	sql := "select * from x_seller where State = 1 order by SellerId desc"
	dbresult, err := db.Conn().Query(sql)
	if ctx.RespErr(err, &errcode) {
		return
	}
	type ReturnData struct {
		SellerId   int
		SellerName string
	}
	data := []ReturnData{}
	data = append(data, ReturnData{0, "全部"})
	//data = append(data, ReturnData{-1, "总后台"})
	for dbresult.Next() {
		data_element := ReturnData{}
		abugo.GetDbResult(dbresult, &data_element)
		data = append(data, data_element)
	}
	dbresult.Close()
	ctx.RespOK(data)
}
func config_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChannelId  int
		Page       int
		PageSize   int
		ConfigName string
		Remark     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "系统设置", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	total, data := DBConfig_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.ConfigName, reqdata.Remark, reqdata.ChannelId)
	ctx.Put("data", data)

	ctx.Put("total", total)
	ctx.RespOK()
}

func config_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId    int `validate:"required"`
		ChannelId   int
		ConfigName  string `validate:"required"`
		ConfigValue string `validate:"required"`
		Remark      string
		GoogleCode  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "系统设置", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.ConfigValue = strings.TrimSpace(reqdata.ConfigValue)
	isConfigValueValidStr := check_config_value_valid(reqdata.ConfigName, reqdata.ConfigValue)
	if ctx.RespErrString(isConfigValueValidStr != "", &errcode, isConfigValueValidStr) {
		return
	}

	xConfigDao := daoXHashGame.XConfig
	xConfigDb := xConfigDao.WithContext(nil)

	config, _ := xConfigDb.Where(xConfigDao.SellerID.Eq(int32(reqdata.SellerId))).
		Where(xConfigDao.ConfigName.Eq(reqdata.ConfigName)).
		Where(xConfigDao.ChannelID.Eq(int32(reqdata.ChannelId))).
		First()

	if config != nil {
		ctx.RespErrString(true, &errcode, "配置已存在")
		return
	}

	err = xConfigDb.Create(&model.XConfig{
		SellerID:    int32(reqdata.SellerId),
		ChannelID:   int32(reqdata.ChannelId),
		ConfigName:  reqdata.ConfigName,
		ConfigValue: reqdata.ConfigValue,
		Remark:      reqdata.Remark,
	})
	if err != nil {
		ctx.RespErrString(true, &errcode, "更新失败")
		return
	}

	if reqdata.ConfigName == "VipTrxPrice" { //同步到降龙伏虎活动的Trx计算价格
		xActiveDefineDao := daoXHashGame.XActiveDefine
		xActiveDefineDb := xActiveDefineDao.WithContext(nil)
		xActiveDefineDb.Where(xActiveDefineDao.SellerID.Eq(int32(reqdata.SellerId))).Update(xActiveDefineDao.TrxPrice, reqdata.ConfigValue)
		//db.QueryNoResult(fmt.Sprintf("update x_active_define set TrxPrice=%s where SellerId=%d", reqdata.ConfigValue, reqdata.SellerId))
	}
	WriteAdminLog("添加系统设置", ctx, reqdata)
	ctx.RespOK()
}

func config_modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId    int
		ChannelId   int
		ConfigName  string `validate:"required"`
		ConfigValue string
		Remark      string
		GoogleCode  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	reqdata.ConfigValue = strings.TrimSpace(reqdata.ConfigValue)

	token := GetToken(ctx)
	if reqdata.ConfigName == "BlackMaker" || reqdata.ConfigName == "OrderAuditTrxLimit" || reqdata.ConfigName == "OrderAuditUsdtLimit" {
		if ctx.RespErrString(!Auth2(token, "系统管理", "系统设置", "值班"), &errcode, "权限不足") {
			return
		}
	} else {
		if ctx.RespErrString(!Auth2(token, "系统管理", "系统设置", "改"), &errcode, "权限不足") {
			return
		}
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	isConfigValueValidStr := check_config_value_valid(reqdata.ConfigName, reqdata.ConfigValue)
	if ctx.RespErrString(isConfigValueValidStr != "", &errcode, isConfigValueValidStr) {
		return
	}
	if reqdata.ConfigName == "ValidBet" {
		lines := strings.Split(reqdata.ConfigValue, "\n")
		jdata := map[string]interface{}{}
		for i := 0; i < len(lines); i++ {
			k := strings.Split(lines[i], ":")
			if strings.Trim(k[0], " ") == "次数TRX" {
				v, _ := strconv.ParseInt(strings.Trim(k[1], " "), 10, 32)
				jdata["counttrx"] = v
			}
			if strings.Trim(k[0], " ") == "金额TRX" {
				v, _ := strconv.ParseFloat(strings.Trim(k[1], " "), 32)
				jdata["amounttrx"] = v
			}
			if strings.Trim(k[0], " ") == "次数USDT" {
				v, _ := strconv.ParseFloat(strings.Trim(k[1], " "), 32)
				jdata["countusdt"] = v
			}
			if strings.Trim(k[0], " ") == "金额USDT" {
				v, _ := strconv.ParseFloat(strings.Trim(k[1], " "), 32)
				jdata["amountusdt"] = v
			}
		}

		jbytes, _ := json.Marshal(&jdata)
		sql := fmt.Sprintf("update %sconfig set ConfigValue = ? where SellerId = ? and ConfigName = ?", dbprefix)
		db.QueryNoResult(sql, string(jbytes), reqdata.SellerId, "JValidBet")
	}
	if reqdata.ConfigName == "PgOpen" {
		if reqdata.ConfigValue == "1" {
			db.Conn().Exec("update x_game_list set openstate = '1' where brand = 'pg'")
		} else {
			db.Conn().Exec("update x_game_list set openstate = '2' where brand = 'pg'")
		}
	}
	if reqdata.ConfigName == "PpOpen" {
		if reqdata.ConfigValue == "1" {
			db.Conn().Exec("update x_game_list set openstate = '1' where brand = 'pp'")
		} else {
			db.Conn().Exec("update x_game_list set openstate = '2' where brand = 'pp'")
		}
	}
	if reqdata.ConfigName == "GfgOpen" {
		if reqdata.ConfigValue == "1" {
			db.Conn().Exec("update x_game_list set openstate = '1' where brand = 'gfg'")
		} else {
			db.Conn().Exec("update x_game_list set openstate = '2' where brand = 'gfg'")
		}
	}

	//判断时间是否在30秒到86400秒内
	if reqdata.ConfigName == "tokenlifetime" || reqdata.ConfigName == "tokenlifetimereadonly" {
		if lifttime, err := strconv.Atoi(reqdata.ConfigValue); err == nil {
			if lifttime < 30 || lifttime > 86400 {
				return
			}
		}
	}

	sql := fmt.Sprintf("update %sconfig set ConfigValue = ?,Remark = ? where SellerId = ? and ChannelId = ? and ConfigName = ? and EditAble = 1", dbprefix)
	db.QueryNoResult(sql, reqdata.ConfigValue, reqdata.Remark, reqdata.SellerId, reqdata.ChannelId, reqdata.ConfigName)
	if reqdata.ConfigName == "VipTrxPrice" { //同步到降龙伏虎活动的Trx计算价格
		db.QueryNoResult(fmt.Sprintf("update x_active_define set TrxPrice=%s where SellerId=%d", reqdata.ConfigValue, reqdata.SellerId))
	}
	WriteAdminLog("修改系统设置", ctx, reqdata)
	update_token_lift_time(reqdata.ConfigName, reqdata.ConfigValue) //修改所有Token超时时间
	ctx.RespOK()
}

func update_token_lift_time(ConfigName, ConfigValue string) {
	if lifttime, err := strconv.Atoi(ConfigValue); err == nil {
		if ConfigName == "tokenlifetime" {
			Http().SetTokenLifeTime(":lifetime", lifttime)
			Http().SetAllTokenLifeTime(false, lifttime) //设置所有Token超时时间
		}

		if ConfigName == "tokenlifetimereadonly" {
			Http().SetTokenLifeTime(":lifetimereadonly", lifttime)
			Http().SetAllTokenLifeTime(true, lifttime) //设置所有Token超时时间
		}
	}
}

func check_config_value_valid(ConfigName, ConfigValue string) string {
	if ConfigName == "WithdrawLimit" {
		v, err := strconv.ParseFloat(ConfigValue, 0)
		if err != nil {
			return "WithdrawLimit格式不正确"
		}
		if v < 0 {
			return "WithdrawLimit不能为负数"
		}
	}
	if ConfigName == "EthWithdrawLimit" {
		v, err := strconv.ParseFloat(ConfigValue, 0)
		if err != nil {
			return "EthWithdrawLimit格式不正确"
		}
		if v < 0 {
			return "EthWithdrawLimit不能为负数"
		}
	}
	if ConfigName == "ExchangeRateFixed" {
		v, err := strconv.ParseFloat(strings.Trim(ConfigValue, " "), 0)
		if err != nil {
			return "ExchangeRateFixed格式不正确"
		}
		if v < 0.01 || v > 0.5 {
			return "ExchangeRateFixed配置不正确[0.01-0.5]"
		}
	}
	if ConfigName == "VipTrxPrice" {
		v, err := strconv.ParseFloat(ConfigValue, 0)
		if err != nil {
			return "VipTrxPrice格式不正确"
		}
		if v < 15 || v > 50 {
			return "VipTrxPrice配置不正确[15-50]"
		}
	}
	if ConfigName == "WinJiangPeiTrx" || ConfigName == "WinJiangPeiUsdt" {
		type WinJiangPeiData struct {
			A float64 `json:"a"` //盈利
			O float64 `json:"o"` //降赔
			R float64 `json:"r"` //连赢系数
		}
		data := make([]WinJiangPeiData, 0)
		err := json.Unmarshal([]byte(ConfigValue), &data)
		if err != nil {
			return "降赔格式不正确"
		}
		for _, v := range data {
			if v.A < 0 {
				return "盈利不能是负数"
			}
			if v.O < 0 || v.O > 0.5 {
				return "降赔超出范围[0-0.5]"
			}
			// if v.O <= 0.001 || v.O > 0.5 {
			// 	return "降赔超出范围[0.001-0.5]"
			// }
			if v.R < 0.1 || v.R > 5 {
				return "降赔系数超出范围[0.1-5]"
			}
		}
	}
	if ConfigName == "BlackBlockFee" {
		v, err := strconv.ParseFloat(ConfigValue, 0)
		if err != nil {
			return "BlackBlockFee格式不正确"
		}
		if v < 0.001 || v > 0.5 {
			return "BlackBlockFee配置不正确[0.001-0.5]"
		}
	}
	if ConfigName == "WithdrawFee" {
		v, err := strconv.ParseFloat(ConfigValue, 0)
		if err != nil {
			return "WithdrawFee格式不正确"
		}
		if v < 0 || v > 100 {
			return "WithdrawFee配置不正确[0-100]"
		}
	}
	if ConfigName == "EthWithdrawFee" {
		v, err := strconv.ParseFloat(ConfigValue, 0)
		if err != nil {
			return "EthWithdrawFee格式不正确"
		}
		if v < 0 || v > 100 {
			return "EthWithdrawFee配置不正确[0-100]"
		}
	}
	if ConfigName == "BlackFeeRate" {
		v, err := strconv.ParseFloat(ConfigValue, 0)
		if err != nil {
			return "BlackFeeRate格式不正确"
		}
		if v < 0.001 || v > 0.5 {
			return "BlackFeeRate配置不正确[0.001-0.5]"
		}
	}
	return ""
}

func config_delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ConfigName string `validate:"required"`
		GoogleCode string
		Id         int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := GetToken(ctx)
	if ctx.RespErrString(!Auth2(token, "系统管理", "系统设置", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	config := daoXHashGame.XConfig
	_, err = config.WithContext(nil).Where(config.ID.Eq(int32(reqdata.Id))).Where(config.ConfigName.Eq(reqdata.ConfigName)).Delete()
	if err != nil {
		ctx.RespErrString(true, &errcode, "删除失败")
		return
	}

	WriteAdminLog("删除系统配置", ctx, reqdata)
	ctx.RespOK()
}

func GetConfigString(SellerId int, ChannelId int, ConfigName string) string {
	rediskey := fmt.Sprintf("%s:%s:systemconfig:%d:%d:%s", Project(), Module(), SellerId, ChannelId, ConfigName)
	redisdata := Redis().Get(rediskey)
	if redisdata != nil {
		return string(redisdata.([]byte))
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", SellerId, 0)
		where.Add("and", "ConfigName", "=", ConfigName, "")
		presult, err := Db().Table("x_config").Select("ConfigValue").Where(where).GetOne()
		if err != nil {
			return ""
		}
		if presult == nil {
			return ""
		}
		result := *presult
		if result["ConfigValue"] != nil {
			return abugo.GetStringFromInterface(result["ConfigValue"])
		}
	}
	return ""
}
func GetConfigInt(SellerId int, ChannelId int, ConfigName string) int64 {
	v := GetConfigString(SellerId, ChannelId, ConfigName)
	iv, err := strconv.ParseInt(v, 10, 64)
	if err != nil {
		return 0
	}
	return iv
}
func GetConfigFloat(SellerId int, ChannelId int, ConfigName string) float64 {
	v := GetConfigString(SellerId, ChannelId, ConfigName)
	iv, err := strconv.ParseFloat(v, 64)
	if err != nil {
		return 0
	}
	return iv
}

func SetConfig(SellerId int, ChannelId int, ConfigName string, v interface{}) {
	rediskey := fmt.Sprintf("%s:%s:systemconfig:%d:%d:%s", Project(), Module(), SellerId, ChannelId, ConfigName)
	redis.Del(rediskey)
	sql := "update x_config set ConfigValue = ? where SellerId = ? and ChannelId = ? and ConfigName = ?"
	db.QueryNoResult(sql, v, SellerId, ChannelId, ConfigName)
}

func VerifyGoogleCode(googlekey string, GoogleCode string) error {
	if len(GoogleCode) == 0 {
		return errors.New("请填写谷歌验证码")
	}
	if debug {
		return nil
	}
	if len(googlekey) == 0 {
		return errors.New("尚未绑定谷歌验证,请先绑定谷歌验证码再试")
	}
	if !abugo.VerifyGoogleCode(googlekey, GoogleCode) {
		return errors.New("谷歌验证码不正确")
	}
	return nil
}

func KickOutAdminLogin(account string) {
	var token string
	sql := "select Token from admin_user where account = ?"
	db.QueryScan(sql, []interface{}{account}, &token)
	http.DelToken(token)
}

func KickOutClientLogin(UserId int) {
	var token string
	sql := "select Token from x_user where userid = ?"
	db.QueryScan(sql, []interface{}{UserId}, &token)
	clienturl := clientapi + "/user/logout"
	data := make(map[string]string)
	data["Token"] = token
	databytes, _ := json.Marshal(data)
	_, err := req.Post(clienturl, string(databytes))
	if err != nil {
		logs.Error("KickOutClientLogin:", err)
	}
}

func google_qrcode(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Name   string
		Secret string
		Issuer string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	url := xgo.GetGoogleQrCodeUrl(reqdata.Secret, reqdata.Issuer, reqdata.Name)
	ctx.RespOK(url)
}

func initCarbonTime() {
	carbon.SetDefault(carbon.Default{
		Layout:       carbon.DateTimeLayout,
		Timezone:     carbon.Shanghai, // 同数据库
		WeekStartsAt: carbon.Monday,
		Locale:       "zh-CN", // i18n
	})
}

// HbcConfig 定义x_hbc表的结构
type HbcConfig struct {
	SellerId             int    `gorm:"column:SellerId;primaryKey"`
	GAppId               string `gorm:"column:GAppId"`               // 游戏商户 速度快
	GApiUrl              string `gorm:"column:GApiUrl"`              // 接口地址
	GLocalPublicKey      string `gorm:"column:GLocalPublicKey"`      // 给hbc，我方调用hbc，用此公钥解密
	GLocalPrivateKey     string `gorm:"column:GLocalPrivateKey"`     // 我方调用hbc加密密钥
	GRiskLocalPublicKey  string `gorm:"column:GRiskLocalPublicKey"`  // 风控回调，hbcrisk工程，给hbc，我方http回复hbc，hbc用此密钥解密
	GRiskLocalPrivateKey string `gorm:"column:GRiskLocalPrivateKey"` // 我方http回复hbc，用此密钥加密
	GServerPublicKey     string `gorm:"column:GServerPublicKey"`     // hbc给我方，我方请求hbc，得到回复用此密钥解密
	GRiskServerPublicKey string `gorm:"column:GRiskServerPublicKey"` // 风控，hbcrisk工程，hbc给我方，我方请求hbc，得到回复用此密钥解密
	DAppId               string `gorm:"column:DAppId"`               // 兑换商户 速度慢
	DApiUrl              string `gorm:"column:DApiUrl"`
	DLocalPublicKey      string `gorm:"column:DLocalPublicKey"`
	DLocalPrivateKey     string `gorm:"column:DLocalPrivateKey"`
	DRiskLocalPublicKey  string `gorm:"column:DRiskLocalPublicKey"`
	DRiskLocalPrivateKey string `gorm:"column:DRiskLocalPrivateKey"`
	DServerPublicKey     string `gorm:"column:DServerPublicKey"`
	DRiskServerPublicKey string `gorm:"column:DRiskServerPublicKey"`
	GMpcServerPublicKey  string `gorm:"column:GMpcServerPublicKey"`
	GMpcLoclPublicKey    string `gorm:"column:GMpcLoclPublicKey"`
	GMpcLocalPrivateKey  string `gorm:"column:GMpcLocalPrivateKey"`
	WAppId               string `gorm:"column:WAppId"` // 充提商户 速度慢
	WApiUrl              string `gorm:"column:WApiUrl"`
	WLocalPublicKey      string `gorm:"column:WLocalPublicKey"`
	WLocalPrivateKey     string `gorm:"column:WLocalPrivateKey"`
	WRiskLocalPublicKey  string `gorm:"column:WRiskLocalPublicKey"`
	WRiskLocalPrivateKey string `gorm:"column:WRiskLocalPrivateKey"`
	WServerPublicKey     string `gorm:"column:WServerPublicKey"`
	WRiskServerPublicKey string `gorm:"column:WRiskServerPublicKey"`
}

func (HbcConfig) TableName() string {
	return "x_hbc"
}

// copyHbcConfig 复制SellerId=20的HBC配置到新的SellerId
func copyHbcConfig(newSellerId int) error {
	// 获取模板配置（SellerId = 20）
	var templateConfig HbcConfig
	dbResult := db.Gorm().Table("x_hbc").Where("SellerId = ?", 20).First(&templateConfig)
	if dbResult.Error != nil {
		if dbResult.RowsAffected <= 0 {
			logs.Info("Template HBC config (SellerId=20) not found, skipping copy")
			return nil // 如果模板不存在，不报错，只是不创建配置
		}
		return dbResult.Error
	}

	// 检查新SellerId是否已存在配置
	var existingConfig HbcConfig
	existResult := db.Gorm().Table("x_hbc").Where("SellerId = ?", newSellerId).First(&existingConfig)
	if existResult.Error == nil {
		logs.Info("HBC config for SellerId %d already exists, skipping copy", newSellerId)
		return nil
	}

	// 创建新配置，复制模板数据
	newConfig := templateConfig
	newConfig.SellerId = newSellerId

	// 插入新配置
	createResult := db.Gorm().Table("x_hbc").Create(&newConfig)
	if createResult.Error != nil {
		return createResult.Error
	}

	logs.Info("Successfully copied HBC config from SellerId=20 to SellerId=%d", newSellerId)
	return nil
}

// copyXConfigFromTemplate 复制SellerId=20 ChannelId=0的x_config配置到新的SellerId
func copyXConfigFromTemplate(newSellerId int) error {
	// 获取模板配置（SellerId = 20, ChannelId = 0）
	var templateConfigs []model.XConfig
	dbResult := db.Gorm().Table("x_config").Where("SellerId = ? AND ChannelId = ?", 20, 0).Find(&templateConfigs)
	if dbResult.Error != nil {
		return dbResult.Error
	}

	if len(templateConfigs) == 0 {
		logs.Info("Template x_config (SellerId=20, ChannelId=0) not found, skipping copy")
		return nil // 如果模板不存在，不报错，只是不创建配置
	}

	// 检查新SellerId是否已存在配置
	var existingConfigs []model.XConfig
	existResult := db.Gorm().Table("x_config").Where("SellerId = ? AND ChannelId = ?", newSellerId, 0).Find(&existingConfigs)
	if existResult.Error == nil && len(existingConfigs) > 0 {
		logs.Info("x_config for SellerId %d ChannelId 0 already exists, skipping copy", newSellerId)
		return nil
	}

	// 创建新配置，复制模板数据
	for _, templateConfig := range templateConfigs {
		newConfig := model.XConfig{
			SellerID:    int32(newSellerId),
			ChannelID:   0,
			ConfigName:  templateConfig.ConfigName,
			ConfigValue: templateConfig.ConfigValue,
			Remark:      templateConfig.Remark,
			IsShow:      templateConfig.IsShow,
			EditAble:    templateConfig.EditAble,
		}

		// 特殊配置项需要清空值
		if newConfig.ConfigName == "DuiHuanAddress" {
			newConfig.ConfigValue = ""
		}
		if newConfig.ConfigName == "VerifyAddress" {
			newConfig.ConfigValue = ""
		}
		if newConfig.ConfigName == "BscVerifyAddress" {
			newConfig.ConfigValue = ""
		}

		// 单个插入，避免批量插入的反射问题
		createResult := db.Gorm().Table("x_config").Create(&newConfig)
		if createResult.Error != nil {
			logs.Error("Failed to copy x_config %s for SellerId %d: %v", newConfig.ConfigName, newSellerId, createResult.Error)
			continue // 继续处理其他配置项
		}
	}

	logs.Info("Successfully copied x_config records from SellerId=20 ChannelId=0 to SellerId=%d ChannelId=0", newSellerId)

	return nil
}
