// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXDictChangeParentype = "x_dict_change_parentype"

// XDictChangeParentype 资金上级类型
type XDictChangeParentype struct {
	ParentType     int32     `gorm:"column:ParentType;primaryKey;comment:上级分类 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金" json:"ParentType"` // 上级分类 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金
	ParentTypeName string    `gorm:"column:ParentTypeName;comment:上级分类名 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金" json:"ParentTypeName"`   // 上级分类名 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金
	Memo           string    `gorm:"column:Memo;comment:描述" json:"Memo"`                                                                                    // 描述
	Status         int32     `gorm:"column:Status;not null;default:1;comment:0无效 1有效" json:"Status"`                                                        // 0无效 1有效
	CreateTime     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`                                   // 创建时间
	UpdateTime     time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`                                   // 更新时间
}

// TableName XDictChangeParentype's table name
func (*XDictChangeParentype) TableName() string {
	return TableNameXDictChangeParentype
}
