package controller

import (
	"errors"
	"fmt"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type WhiteBlockController struct {
}

func (c *WhiteBlockController) Init() {
	group := server.Http().NewGroup("/api/whiteblock")
	{
		group.Post("/list", c.list)
		group.Post("/create", c.create)
		group.Post("/delete", c.delete)
		group.Post("/batchDelete", c.batchDelete)
	}
}

func (c *WhiteBlockController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		Type     int
		Info     []string
	}

	errcode := 0
	reqdata := RequestData{Type: 1}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块白名单", "查", "区块白名单列表查询")
	if token == nil {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	xBlackBlock := server.DaoxHashGame().XWhiteBlock
	db := xBlackBlock.WithContext(ctx.Gin())
	xUser := server.DaoxHashGame().XUser
	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel

	if len(reqdata.Info) > 0 {
		db = db.Where(xBlackBlock.Info.In(reqdata.Info...))
	}

	if reqdata.Type == 1 {
		type Result struct {
			model.XWhiteBlock
			SellerName  string
			ChannelName string
			TopAgentId  int
			AgentId     int
		}
		var list []Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName, xChannel.ChannelName, xUser.TopAgentID, xUser.AgentID).
			LeftJoin(xUser, xUser.UserID.EqCol(xBlackBlock.Info)).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xUser.SellerID)).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xUser.ChannelID)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 2 {
		type Result struct {
			model.XWhiteBlock
		}
		var list []Result
		total, err := db.
			Select(xBlackBlock.ALL).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 3 {
		type Result struct {
			model.XWhiteBlock
			SellerName  string
			ChannelName string
			TopAgentId  int
			AgentId     int
		}
		var list []Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName, xChannel.ChannelName, xUser.TopAgentID, xUser.AgentID).
			LeftJoin(xUser, xUser.UserID.EqCol(xBlackBlock.Info)).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xUser.SellerID)).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xUser.ChannelID)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 4 {
		type Result struct {
			model.XWhiteBlock
			SellerName string
		}
		var list []Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xBlackBlock.Info)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 5 {
		type Result struct {
			model.XWhiteBlock
			SellerName  string
			ChannelName string
		}
		var list []Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName, xChannel.ChannelName).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xBlackBlock.Info)).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xChannel.SellerID)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 6 {
		type Result struct {
			model.XWhiteBlock
		}
		var list []Result
		total, err := db.
			Select(xBlackBlock.ALL).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

}

func (c *WhiteBlockController) create(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Type       int
		Info       string
		BlockMaker string
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块白名单", "改", "区块白名单添加")
	if token == nil {
		return
	}

	if strings.Contains(reqdata.Info, "，") {
		ctx.RespErr(errors.New("多个ID请使用英文逗号(,)隔开，不要使用中文逗号(，)"), &errcode)
		return
	}

	if strings.Contains(reqdata.BlockMaker, "，") {
		ctx.RespErr(errors.New("多个出快者请使用英文逗号(,)隔开，不要使用中文逗号(，)"), &errcode)
		return
	}

	blackInfos := strings.Split(reqdata.Info, ",")
	blockMakers := strings.Split(reqdata.BlockMaker, ",")

	//xblackBlock := server.DaoxHashGame().XWhiteBlock
	//db := xblackBlock.WithContext(ctx.Gin())

	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		for _, blackInfo := range blackInfos {
			for _, blockMaker := range blockMakers {

				_, err := tx.XWhiteBlock.WithContext(ctx.Gin()).Where(tx.XWhiteBlock.Info.Eq(blackInfo), tx.XWhiteBlock.Type.Eq(int32(reqdata.Type)), tx.XWhiteBlock.BlockMaker.Eq(blockMaker)).First()
				if err == nil {
					return errors.New(fmt.Sprintf("已存%s-%s在该白名单信息", blockMaker, blackInfo))
				}

				err = tx.XWhiteBlock.WithContext(ctx.Gin()).Create(&model.XWhiteBlock{
					BlockMaker: blockMaker,
					Info:       blackInfo,
					Type:       int32(reqdata.Type),
				})
				if err != nil {
					return err
				}
			}
		}
		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

func (c *WhiteBlockController) delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块白名单", "删", "区块白名单删除")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XWhiteBlock
	db := dao.WithContext(ctx.Gin())

	_, err := db.Where(dao.ID.Eq(int32(reqdata.Id))).Delete()
	if err != nil {
		return
	}

	ctx.RespOK()
}

func (c *WhiteBlockController) batchDelete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Ids []int32
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块黑名单", "删", "区块黑名单删除")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XWhiteBlock
	db := dao.WithContext(ctx.Gin())

	_, err := db.Where(dao.ID.In(reqdata.Ids...)).Delete()
	if err != nil {
		return
	}

	ctx.RespOK()
}
