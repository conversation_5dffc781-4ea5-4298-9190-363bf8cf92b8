// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotRedbagQuestion = "x_robot_redbag_questions"

// XRobotRedbagQuestion mapped from table <x_robot_redbag_questions>
type XRobotRedbagQuestion struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                 // pk
	SellerID   int32     `gorm:"column:seller_id;not null;comment:运营商ID" json:"seller_id"`                     // 运营商ID
	ChannelID  int32     `gorm:"column:channel_id;not null;comment:渠道ID" json:"channel_id"`                    // 渠道ID
	Name       string    `gorm:"column:name;not null;comment:机器人name" json:"name"`                             // 机器人name
	Token      string    `gorm:"column:token;comment:机器人token" json:"token"`                                   // 机器人token
	Question   string    `gorm:"column:question;comment:题目" json:"question"`                                   // 题目
	LangCode   string    `gorm:"column:lang_code;comment:题目语言" json:"lang_code"`                               // 题目语言
	Answer     string    `gorm:"column:answer;comment:答案" json:"answer"`                                       // 答案
	Expire     int32     `gorm:"column:expire;comment:题目过期时间" json:"expire"`                                   // 题目过期时间
	Points     string    `gorm:"column:points;comment:积分" json:"points"`                                       // 积分
	Remark     string    `gorm:"column:remark;comment:备注" json:"remark"`                                       // 备注
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建日期" json:"create_time"` // 创建日期
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新日期" json:"update_time"` // 更新日期
}

// TableName XRobotRedbagQuestion's table name
func (*XRobotRedbagQuestion) TableName() string {
	return TableNameXRobotRedbagQuestion
}
