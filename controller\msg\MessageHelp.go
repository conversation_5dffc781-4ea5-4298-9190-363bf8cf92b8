package msg

import (
	"fmt"
)

// 模板类型常量
const (
	TemplateTypeBonusReminder    = "1" // 领取彩金提醒
	TemplateTypeActivityReminder = "2" // 活动参与提醒
	TemplateTypeDepositReminder  = "3" // 充值到账提醒
	TemplateTypeWithdrawSuccess  = "4" // 提现成功提醒
	TemplateTypeWithdrawFail     = "5" //  提现失败提醒
	TemplateTypeWithdrawReview   = "6" // 提现审核提醒
	// 账户安全通知
	MsgTypeLoginPasswordChanged = "7"  // 修改登录密码通知
	MsgTypeFundPasswordChanged  = "8"  // 修改资金密码通知
	MsgTypeLoginDeviceChange    = "9"  // 登录设备变动通知
	MsgTypeRedemptionCode       = "10" // 兑换码领取通知

)

// 模板类型的推送类型
// 0: 未启用
// 1: 系统消息
// 2: 手动消息
// 3: 系统消息和手动消息都启用
var templateTypePushType = map[string]int{
	TemplateTypeBonusReminder:    3, // 系统消息和手动消息都启用
	TemplateTypeActivityReminder: 2, // 手动消息
	TemplateTypeDepositReminder:  3,
	TemplateTypeWithdrawSuccess:  3,
	TemplateTypeWithdrawFail:     3, //
	TemplateTypeWithdrawReview:   0, // 未启用
	MsgTypeLoginPasswordChanged:  1, //
	MsgTypeFundPasswordChanged:   1, //
	MsgTypeLoginDeviceChange:     1, //
	MsgTypeRedemptionCode:        3, //
}

// GetAllTemplateTypeNames 获取所有模板类型及其名称（包括未启用的）
func GetAllTemplateTypeNames() map[string]string {
	return map[string]string{
		TemplateTypeBonusReminder:    "领取彩金提醒",
		TemplateTypeActivityReminder: "活动参与提醒",
		TemplateTypeDepositReminder:  "充值到账提醒",
		TemplateTypeWithdrawSuccess:  "提现成功提醒",
		TemplateTypeWithdrawFail:     "提现失败提醒",
		TemplateTypeWithdrawReview:   "提现审核提醒",
		MsgTypeLoginPasswordChanged:  "修改登录密码通知",
		MsgTypeFundPasswordChanged:   "修改资金密码通知",
		MsgTypeLoginDeviceChange:     "登录设备变动通知",
		MsgTypeRedemptionCode:        "兑换码领取通知",
	}
}

// GetTemplateTypeNames 获取启用的模板类型及其名称
func GetTemplateTypeNames() map[string]string {
	allTypes := GetAllTemplateTypeNames()
	enabledTypes := make(map[string]string)

	for typeCode, typeName := range allTypes {
		if pushType, exists := templateTypePushType[typeCode]; exists && pushType > 0 {
			enabledTypes[typeCode] = typeName
		}
	}

	return enabledTypes
}

// GetSystemTemplateTypeNames 获取启用的系统消息模板类型及其名称
func GetSystemTemplateTypeNames() map[string]string {
	allTypes := GetAllTemplateTypeNames()
	systemTypes := make(map[string]string)

	for typeCode, typeName := range allTypes {
		// 检查是否支持系统消息
		if pushType, exists := templateTypePushType[typeCode]; exists && (pushType == 1 || pushType == 3) {
			systemTypes[typeCode] = typeName
		}
	}

	return systemTypes
}

// GetManualTemplateTypeNames 获取启用的手动消息模板类型及其名称
func GetManualTemplateTypeNames() map[string]string {
	allTypes := GetAllTemplateTypeNames()
	manualTypes := make(map[string]string)

	for typeCode, typeName := range allTypes {
		// 检查是否支持手动消息
		if pushType, exists := templateTypePushType[typeCode]; exists && (pushType == 2 || pushType == 3) {
			manualTypes[typeCode] = typeName
		}
	}

	return manualTypes
}

// GetTemplateTypeName 获取指定模板类型的名称
func GetTemplateTypeName(typeCode string) string {
	typeNames := GetAllTemplateTypeNames()
	if name, exists := typeNames[typeCode]; exists {
		return name
	}
	return fmt.Sprintf("类型%s", typeCode)
}

// IsTemplateTypeEnabled 检查指定模板类型是否启用
func IsTemplateTypeEnabled(typeCode string) bool {
	if pushType, exists := templateTypePushType[typeCode]; exists && pushType > 0 {
		return true
	}
	return false
}

// UserFilter 用户筛选条件
type UserFilter struct {
	UserIds     string // 用户ID列表，逗号分隔
	UserLabels  string // 用户标签列表，逗号分隔
	SellerType  int    // 运营商类型 运营商类型：1-指定运营商，2-指定顶级代理ID
	SellerIds   string // 运营商ID列表， 逗号分隔
	ChannelIds  string // 渠道ID列表，逗号分隔
	VipLevels   string // VIP等级列表，逗号分隔
	TopAgentIds string // 顶级代理ID列表，逗号分隔
}

//1：领取彩金提醒 - 用户领取彩金时的通知消息
//2：活动参与提醒 - 用户参与活动时的通知消息
//3：充值到账提醒 - 用户充值成功到账时的通知消息
//4：提现成功提醒 - 用户提现成功时的通知消息
//5：提现失败提醒 - 用户提现失败提醒的通知消息
//6：提现审核提醒 - 用户提现申请进入审核状态时的通知消息
//10：兑换码领取通知 - 用户成功领取兑换码时的通知消息

//1：领取彩金提醒 - 用户领取彩金时的通知消息
//3：充值到账提醒 - 用户充值成功到账时的通知消息
//4：提现成功提醒 - 用户提现成功时的通知消息
//5：提现失败提醒 - 用户提现失败提醒的通知消息
//10：兑换码领取通知 - 用户成功领取兑换码时的通知消息
