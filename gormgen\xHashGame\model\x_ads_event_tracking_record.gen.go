// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsEventTrackingRecord = "x_ads_event_tracking_record"

// XAdsEventTrackingRecord 埋点事件记录表
type XAdsEventTrackingRecord struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                        // 主键ID
	SellerID    int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                       // 运营商ID
	ChannelID   int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                      // 渠道ID
	TopAgentID  int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                // 顶级代理ID
	UserID      int64     `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                                   // 用户ID
	Os          int32     `gorm:"column:os;comment:系统: 1 -- PC  2 -- h5  3 -- ios  4 -- android 5 -- 其他" json:"os"`      // 系统: 1 -- PC  2 -- h5  3 -- ios  4 -- android 5 -- 其他
	Network     int32     `gorm:"column:network;comment:网络: 1 -- WIFI   2 -- 流量" json:"network"`                         // 网络: 1 -- WIFI   2 -- 流量
	EventType   int32     `gorm:"column:event_type;comment:事件类型" json:"event_type"`                                      // 事件类型
	EventName   string    `gorm:"column:event_name;not null;comment:事件名称" json:"event_name"`                             // 事件名称
	EventParams string    `gorm:"column:event_params;not null;comment:事件参数" json:"event_params"`                         // 事件参数
	EventDate   time.Time `gorm:"column:event_date;not null;comment:事件日期" json:"event_date"`                             // 事件日期
	CreateTime  time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
}

// TableName XAdsEventTrackingRecord's table name
func (*XAdsEventTrackingRecord) TableName() string {
	return TableNameXAdsEventTrackingRecord
}
