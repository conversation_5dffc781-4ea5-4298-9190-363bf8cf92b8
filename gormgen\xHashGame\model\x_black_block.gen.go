// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXBlackBlock = "x_black_block"

// XBlackBlock 区块黑名单
type XBlackBlock struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	BlockMaker string    `gorm:"column:BlockMaker;not null;comment:出块者" json:"BlockMaker"`                   // 出块者
	Info       string    `gorm:"column:Info;comment:拉黑信息" json:"Info"`                                       // 拉黑信息
	Type       int32     `gorm:"column:Type;not null;comment:1-玩家 2-钱包地址 3-代理 4-运营商 5-渠道 6-区块" json:"Type"`  // 1-玩家 2-钱包地址 3-代理 4-运营商 5-渠道 6-区块
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XBlackBlock's table name
func (*XBlackBlock) TableName() string {
	return TableNameXBlackBlock
}
