package share

import "strings"

type Lang int32

const (
	<PERSON>_nil Lang = iota
	Lang_zh
	Lang_en
	Lang_th
	Lang_ko
	Lang_tr
	Lang_vi
	Lang_pt
	Lang_hi
	Lang_ja
	Lang_ru
	Lang_km
	Lang_uk
	Lang_zh_tw
	Lang_es
)

var LangToString = map[Lang]string{
	Lang_zh:    "中文",
	Lang_en:    "英语",
	Lang_th:    "泰语",
	Lang_ko:    "韩语",
	Lang_tr:    "土耳其语",
	Lang_vi:    "越南语",
	Lang_pt:    "葡萄牙语",
	Lang_hi:    "印地语",
	Lang_ja:    "日语",
	Lang_ru:    "俄语",
	Lang_km:    "柬埔寨语",
	Lang_uk:    "乌克兰语",
	Lang_zh_tw: "中文繁体",
	Lang_es:    "西班牙",
}

var LangToSimple = map[Lang]string{
	Lang_zh:    "zh",
	Lang_en:    "en",
	Lang_th:    "th",
	Lang_ko:    "ko",
	Lang_tr:    "tr",
	Lang_vi:    "vi",
	Lang_pt:    "pt",
	Lang_hi:    "hi",
	Lang_ja:    "ja",
	Lang_ru:    "ru",
	Lang_km:    "km",
	Lang_uk:    "uk",
	Lang_zh_tw: "zh-tw",
	Lang_es:    "es",
}

var simpleToLang = map[string]Lang{
	"zh":    Lang_zh,
	"en":    Lang_en,
	"th":    Lang_th,
	"ko":    Lang_ko,
	"tr":    Lang_tr,
	"vi":    Lang_vi,
	"pt":    Lang_pt,
	"hi":    Lang_hi,
	"ja":    Lang_ja,
	"ru":    Lang_ru,
	"km":    Lang_km,
	"uk":    Lang_uk,
	"zh-tw": Lang_zh_tw,
	"es":    Lang_es,
}

func SimpleToLang(s string) Lang {
	if v, ok := simpleToLang[strings.ToLower(s)]; ok {
		return v
	}
	return Lang_nil
}
