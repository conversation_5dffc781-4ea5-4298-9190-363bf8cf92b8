package msg

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/msg/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"gorm.io/gorm"
)

// SiteMessageController 站内信管理控制器
type SiteMessageController struct {
}

// Init 初始化路由
func (c *SiteMessageController) Init() {
	group := server.Http().NewGroup("/api/message")
	{
		// 管理端接口
		group.Post("/list", c.GetTemplateList)                         //获取模板列表
		group.Post("/detail", c.GetTemplateDetail)                     //获取模板详情
		group.Post("/create", c.CreateTemplate)                        //创建模板
		group.Post("/update", c.UpdateTemplate)                        //更新模板
		group.Post("/delete", c.DeleteTemplate)                        //删除模板
		group.Post("/records", c.GetMessageRecords)                    //获取发送记录
		group.Post("/update_status", c.UpdateTemplateStatus)           // 修改：统一的模板状态更新接口
		group.Post("/system_template_types", c.GetSystemTemplateTypes) // 新增：获取系统消息模板类型存在情况
		group.Post("/manual_template_types", c.GetManualTemplateTypes) // 新增：获取手动消息模板类型和名称
		group.Post("/seller_list", c.GetSellerList)                    // 新增：获取运营商列表
		group.Post("/channel_list", c.GetChannelList)                  // 新增：获取指定运营商下的渠道列表
		group.Post("/validate_top_agents", c.ValidateTopAgents)        // 新增：验证顶级代理ID是否存在
		group.Post("/validate_users", c.ValidateUsers)                 // 新增：验证用户ID是否存在

		// 注册定时任务
		server.RegisterCronJob("0 */15 * * * *", SendTimedMessages) // 每15分钟执行一次
	}
}

// GetTemplateList 获取站内信模板列表
func (c *SiteMessageController) GetTemplateList(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		Page     int    `json:"Page"`
		PageSize int    `json:"PageSize"`
		Type     string `json:"Type"`
		Status   int    `json:"Status"`
		Alias    string `json:"Alias"`
		PushType int    `json:"PushType"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析 请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	//token := server.GetToken(ctx)
	//if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	//	return
	//}

	// 查询模板列表
	db := server.Db().GormDao()
	query := db.Model(&model.StationMessageTemplate{})

	if reqData.Alias != "" {
		query = query.Where("Alias LIKE ?", "%"+reqData.Alias+"%")
	}
	if reqData.Type != "" {
		query = query.Where("Type = ?", reqData.Type)
	}
	if reqData.Status != -1 {
		query = query.Where("Status = ?", reqData.Status)
	}
	if reqData.PushType != -1 {
		query = query.Where("PushType = ?", reqData.PushType)
	}

	// 查询数据
	var templates []model.StationMessageTemplate
	var total int64
	db.Model(&model.StationMessageTemplate{}).Where(query.Statement.Clauses["WHERE"].Expression).Count(&total)
	query.Order("Id desc").Offset((reqData.Page - 1) * reqData.PageSize).Limit(reqData.PageSize).Find(&templates)

	// 将模板数据转换为响应格式
	result := make([]map[string]interface{}, len(templates))
	for i, template := range templates {
		// 将结构体转换为 map
		item := map[string]interface{}{
			"Id":           template.ID,
			"Alias":        template.Alias,
			"Type":         template.Type,
			"TypeName":     GetTemplateTypeName(template.Type),
			"PushType":     template.PushType,
			"Status":       template.Status,
			"IsPopup":      template.IsPopup,
			"IsTimed":      template.IsTimed,
			"TimedAt":      template.TimedAt,
			"SellerType":   template.SellerType,
			"SellerIds":    template.SellerIds,
			"ChannelIds":   template.ChannelIds,
			"VipLevels":    template.VipLevels,
			"UserIds":      template.UserIds,
			"UserLabels":   template.UserLabels,
			"TopAgentIds":  template.TopAgentIds,
			"GameType":     template.GameType,
			"GameTypeName": getGameTypeName(template.GameType),
			"CreatedAt":    template.CreatedAt,
			"UpdatedAt":    template.UpdatedAt,
		}
		result[i] = item

		templateId := template.ID

		// 查询发送数量
		var sentCount int64
		db.Model(&model.MessageRecord{}).Where("TemplateId = ?", templateId).Count(&sentCount)
		result[i]["SentCount"] = sentCount

		// 查询已读数量
		var readCount int64
		db.Model(&model.MessageRecord{}).Where("TemplateId = ? AND IsRead = ?", templateId, 1).Count(&readCount)

		// 计算阅读率
		readRate := 0.0
		if sentCount > 0 {
			readRate = float64(readCount) / float64(sentCount) * 100
		}
		result[i]["ReadRate"] = fmt.Sprintf("%.2f%%", readRate)

		// 如果是系统消息
		if template.PushType == 0 {
			result[i]["SellerTypeName"] = "全平台"
		} else { //手动消息
			// 处理运营商类型显示
			if template.SellerType == 1 {
				result[i]["SellerTypeName"] = "运营商"
			} else if template.SellerType == 2 {
				result[i]["SellerTypeName"] = "顶级代理"
			}
		}

		// 处理模板状态显示
		if template.Status == 0 {
			result[i]["StatusName"] = "禁用"
		} else {
			result[i]["StatusName"] = "启用"
		}

		// 处理定时发送显示
		if template.IsTimed == 0 {
			result[i]["IsTimedText"] = "否"
		} else {
			result[i]["IsTimedText"] = "是"
		}

		// 处理弹窗显示
		if template.IsPopup == 0 {
			result[i]["IsPopupText"] = "否"
		} else {
			result[i]["IsPopupText"] = "是"
		}
	}

	ctx.Put("data", result)
	ctx.Put("total", total)
	ctx.RespOK()
}

// CreateTemplate 创建站内信模板
func (c *SiteMessageController) CreateTemplate(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type TemplateContent struct {
		Lang    string `json:"Lang"`
		Title   string `json:"Title"`
		Content string `json:"Content"`
	}

	type RequestData struct {
		Alias       string            `json:"Alias"`
		Type        string            `json:"Type"`
		PushType    int               `json:"PushType"`
		Status      int               `json:"Status"`
		IsPopup     int               `json:"IsPopup"`
		IsTimed     int               `json:"IsTimed"`
		TimedAt     string            `json:"TimedAt"`
		SellerType  int               `json:"SellerType"`
		SellerIds   string            `json:"SellerIds"`
		ChannelIds  string            `json:"ChannelIds"`
		VipLevels   string            `json:"VipLevels"`
		UserIds     string            `json:"UserIds"`
		UserLabels  string            `json:"UserLabels"`
		TopAgentIds string            `json:"TopAgentIds"`
		GameType    int               `json:"GameType"` // 游戏类型：1电子 2棋牌 3趣味 4彩票 5真人 6体育 7真人 0代表没选
		Contents    []TemplateContent `json:"Contents"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	token := server.GetToken(ctx)
	//if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "增"), &errcode, "权限不足") {
	//	return
	//}

	//全局并发锁
	rediskey := fmt.Sprintf("%v:%v:message_create_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 10)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "系统正在处理中,请不要重复点击,避免重复发送消息,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	if reqData.PushType != 0 && reqData.PushType != 1 {
		ctx.RespErrString(true, &errcode, "参数错误、pushType必须为0或1")
		return
	}

	if reqData.PushType == 1 { //如果是手动消息
		if reqData.SellerType != 1 && reqData.SellerType != 2 {
			ctx.RespErrString(true, &errcode, "参数错误、sellerType必须为1或2")
			return
		}
	}

	// 参数验证
	if reqData.Alias == "" || reqData.Type == "" {
		ctx.RespErrString(true, &errcode, "别名和发送类型不能为空")
		return
	}

	// 验证模板内 容
	if len(reqData.Contents) < 2 {
		ctx.RespErrString(true, &errcode, "至少需要配置中、英文两种语言的站内信标题、内容")
		return
	}

	for _, content := range reqData.Contents {
		if content.Lang == "" || content.Title == "" || content.Content == "" {
			ctx.RespErrString(true, &errcode, "语言、标题和内容不能为空")
			return
		}
	}

	// 当PushType为0(系统消息)时，检查同类型的系统消息模板是否已存在
	if reqData.PushType == 0 {
		var count int64
		result := server.Db().GormDao().Model(&model.StationMessageTemplate{}).
			Where("Type = ? AND PushType = 0 AND Status = 1", reqData.Type).
			Count(&count)

		if result.Error != nil {
			logs.Error("查询同类型模板失败: %v", result.Error)
			ctx.RespErrString(true, &errcode, fmt.Sprintf("查询同类型站内信失败: %v", result.Error))
			return
		}

		if count > 0 {
			// 使用辅助函数获取模板类型名称
			typeName := GetTemplateTypeName(reqData.Type)

			ctx.RespErrString(true, &errcode, fmt.Sprintf("已存在启用中的 %s 系统站内信类型，不能创建重复的系统消息模板", typeName))
			return
		}
	}

	// 创建模板
	var templateId int64 = 0
	err := server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 更新模板记录
		var timedAt *time.Time
		if reqData.TimedAt != "" {
			t, pass := utils.ParseTimeWithFormats(reqData.TimedAt)
			if pass == true {
				timedAt = &t
			}
		}

		template := model.StationMessageTemplate{
			Alias:       reqData.Alias,
			Type:        reqData.Type,
			PushType:    reqData.PushType,
			Status:      reqData.Status,
			IsPopup:     reqData.IsPopup,
			IsTimed:     reqData.IsTimed,
			TimedAt:     timedAt,
			SellerType:  reqData.SellerType,
			SellerIds:   reqData.SellerIds,
			ChannelIds:  reqData.ChannelIds,
			VipLevels:   reqData.VipLevels,
			UserIds:     reqData.UserIds,
			UserLabels:  reqData.UserLabels,
			TopAgentIds: reqData.TopAgentIds,
			GameType:    reqData.GameType,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		// 创建模板记录
		result := tx.Create(&template)
		if result.Error != nil {
			logs.Error("创建模板记录失败: %v", result.Error)
			return result.Error
		}

		templateId = template.ID
		// 创建模板内容
		for _, content := range reqData.Contents {
			contentData := map[string]interface{}{
				"TemplateId": template.ID,
				"Lang":       content.Lang,
				"Title":      content.Title,
				"Content":    content.Content,
			}

			result := tx.Table("x_message_template_content").Create(contentData)
			if result.Error != nil {
				logs.Error("创建模板内容失败: %v", result.Error)
				return result.Error
			}
		}
		return nil
	})
	if err != nil {
		ctx.RespErrString(true, &errcode, "创建站内信失败: "+err.Error())
		return
	}

	// 如果是手动推送且非定时发送，则立即向目标对象发送消息
	if reqData.PushType == 1 && reqData.IsTimed == 0 && reqData.Status == 1 {
		// 异步发送消息，避免阻塞当前请求
		go func(templateId int64) {
			if templateId == 0 {
				ctx.RespErrString(true, &errcode, "发送消息获取模板ID失败: "+err.Error())
				return
			}
			logs.Info("发送手动推送消息 %v", reqData)
			// 创建消息发送服务
			messageSendService := NewMessageSendService()
			// 构建用户筛选条件
			userFilter := UserFilter{
				UserIds:     reqData.UserIds,
				UserLabels:  reqData.UserLabels,
				SellerType:  reqData.SellerType,
				SellerIds:   reqData.SellerIds,
				ChannelIds:  reqData.ChannelIds,
				VipLevels:   reqData.VipLevels,
				TopAgentIds: reqData.TopAgentIds,
			}

			// 发送消息
			successCount, err := messageSendService.SendBatchMessage(templateId, userFilter, nil)
			if err != nil {
				logs.Error("发送消息失败: %v", err)
			} else {
				logs.Info("成功发送消息给 %d 个用户", successCount)
			}
		}(templateId)
	}

	ctx.RespOK()
}

// UpdateTemplate 更新站内信模板
func (c *SiteMessageController) UpdateTemplate(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type TemplateContent struct {
		Lang    string `json:"Lang"`
		Title   string `json:"Title"`
		Content string `json:"Content"`
	}

	type RequestData struct {
		ID          int               `json:"Id"`
		Alias       string            `json:"Alias"`
		Type        string            `json:"Type"`
		PushType    int               `json:"PushType"`
		Status      int               `json:"Status"`
		IsPopup     int               `json:"IsPopup"`
		IsTimed     int               `json:"IsTimed"`
		TimedAt     string            `json:"TimedAt"`
		SellerType  int               `json:"SellerType"`
		SellerIds   string            `json:"SellerIds"`
		ChannelIds  string            `json:"ChannelIds"`
		VipLevels   string            `json:"VipLevels"`
		UserIds     string            `json:"UserIds"`
		UserLabels  string            `json:"UserLabels"`
		TopAgentIds string            `json:"TopAgentIds"`
		GameType    int               `json:"GameType"` // 游戏类型：1电子 2棋牌 3趣味 4彩票 5真人 6体育 7真人 0代表没选
		Contents    []TemplateContent `json:"Contents"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	token := server.GetToken(ctx)
	//if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "改"), &errcode, "权限不足") {
	//	return
	//}

	//全局并发锁
	rediskey := fmt.Sprintf("%v:%v:message_update_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 10)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "系统正在处理中,请不要重复点击,避免重复发送消息,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	// 参数验证
	if reqData.ID <= 0 {
		ctx.RespErrString(true, &errcode, "模板ID不能为空")
		return
	}

	if reqData.Alias == "" || reqData.Type == "" {
		ctx.RespErrString(true, &errcode, "别名和发送类型不能为空")
		return
	}

	// 验证模板内 容
	if len(reqData.Contents) < 2 {
		ctx.RespErrString(true, &errcode, "至少需要配置中、英文两种语言的站内信标题、内容")
		return
	}

	if reqData.PushType != 0 && reqData.PushType != 1 {
		ctx.RespErrString(true, &errcode, "参数错误、pushType必须为0或1")
		return
	}

	if reqData.PushType == 1 { //如果是手动消息
		if reqData.SellerType != 1 && reqData.SellerType != 2 {
			ctx.RespErrString(true, &errcode, "参数错误、sellerType必须为1或2")
			return
		}
	}

	for _, content := range reqData.Contents {
		if content.Lang == "" || content.Title == "" || content.Content == "" {
			ctx.RespErrString(true, &errcode, "语言、标题和内容不能为空")
			return
		}
	}

	// 更新模板
	err := server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 更新模板记录
		var timedAt *time.Time
		if reqData.TimedAt != "" {
			t, pass := utils.ParseTimeWithFormats(reqData.TimedAt)
			if pass == true {
				timedAt = &t
			}
		}

		template := map[string]interface{}{
			"Alias":       reqData.Alias,
			"Type":        reqData.Type,
			"Status":      reqData.Status,
			"IsPopup":     reqData.IsPopup,
			"IsTimed":     reqData.IsTimed,
			"TimedAt":     timedAt,
			"SellerType":  reqData.SellerType,
			"SellerIds":   reqData.SellerIds,
			"ChannelIds":  reqData.ChannelIds,
			"VipLevels":   reqData.VipLevels,
			"UserIds":     reqData.UserIds,
			"UserLabels":  reqData.UserLabels,
			"TopAgentIds": reqData.TopAgentIds,
			"GameType":    reqData.GameType,
			"UpdatedAt":   time.Now(),
		}

		result := tx.Table("x_message_template").Where("Id = ?", reqData.ID).Updates(template)
		if result.Error != nil {
			logs.Error("更新模板记录失败: %v", result.Error)
			return result.Error
		}
		// 删除原有模板内容
		result = tx.Table("x_message_template_content").Where("TemplateId = ?", reqData.ID).Delete(map[string]interface{}{})
		if result.Error != nil {
			logs.Error("删除站内信内容失败: %v", result.Error)
			return result.Error
		}

		// 创建新的模板内容
		for _, content := range reqData.Contents {
			contentData := map[string]interface{}{
				"TemplateId": reqData.ID,
				"Lang":       content.Lang,
				"Title":      content.Title,
				"Content":    content.Content,
			}

			result := tx.Table("x_message_template_content").Create(contentData)
			if result.Error != nil {
				logs.Error("创建站内信失败: %v", result.Error)
				return result.Error
			}
		}
		return nil
	})

	if err != nil {
		ctx.RespErrString(true, &errcode, "更新站内信内容失败: "+err.Error())
		return
	}

	// 创建模板服务
	templateService := NewTemplateService()

	template, err := templateService.GetTemplateById(int64(reqData.ID))
	if err != nil {
		ctx.RespErrString(true, &errcode, "查询站内信内容失败,发送消息失败 ")
		return
	}

	// 如果是手动推送且非定时发送，则立即向目标对象发送消息
	if template.PushType == 1 && template.IsTimed == 0 && template.Status == 1 {
		// 异步发送消息，避免阻塞当前请求
		go func(templateId int64) {
			// 创建消息发送服务
			messageSendService := NewMessageSendService()

			// 构建用户筛选条件
			userFilter := UserFilter{
				UserIds:     reqData.UserIds,
				UserLabels:  reqData.UserLabels,
				SellerType:  int(reqData.SellerType),
				SellerIds:   reqData.SellerIds,
				ChannelIds:  reqData.ChannelIds,
				VipLevels:   reqData.VipLevels,
				TopAgentIds: reqData.TopAgentIds,
			}

			// 发送消息
			successCount, err := messageSendService.SendBatchMessage(templateId, userFilter, nil)
			if err != nil {
				logs.Error("发送消息失败: %v", err)
			} else {
				logs.Info("成功发送消息给 %d 个用户", successCount)
			}
		}(int64(reqData.ID))
	}

	ctx.RespOK()
}

// DeleteTemplate 删除站内信模板
func (c *SiteMessageController) DeleteTemplate(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		ID int `json:"Id"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	//token := server.GetToken(ctx)
	//if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "删"), &errcode, "权限不足") {
	//	return
	//}

	// 参数验证
	if reqData.ID <= 0 {
		ctx.RespErrString(true, &errcode, "模板ID不能为空")
		return
	}

	// 删除模板
	err := server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 删除模板内容
		result := tx.Table("x_message_template_content").Where("TemplateId = ?", reqData.ID).Delete(map[string]interface{}{})
		if result.Error != nil {
			logs.Error("删除内容失败: %v", result.Error)
			return result.Error
		}

		// 删除模板记录
		result = tx.Table("x_message_template").Where("Id = ?", reqData.ID).Delete(map[string]interface{}{})
		if result.Error != nil {
			logs.Error("删除记录失败: %v", result.Error)
			return result.Error
		}

		return nil
	})

	if err != nil {
		ctx.RespErrString(true, &errcode, "删除失败: "+err.Error())
		return
	}

	ctx.RespOK()
}

// GetTemplateDetail 获取站内信模板详情
func (c *SiteMessageController) GetTemplateDetail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		Id int `json:"Id"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	//token := server.GetToken(ctx)
	//if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	//	return
	//}

	// 参数验证
	if reqData.Id <= 0 {
		ctx.RespErrString(true, &errcode, "模板ID不能为空")
		return
	}

	// 查询模板
	db := server.Db().GormDao()
	var template model.StationMessageTemplate
	result := db.Where("Id = ?", reqData.Id).First(&template)
	if result.Error != nil {
		logs.Error("查询模板失败: %v", result.Error)
		ctx.RespErrString(true, &errcode, "模板不存在")
		return
	}

	// 查询模板内容
	var contents []model.TemplateContent
	result = db.Where("TemplateId = ?", reqData.Id).Find(&contents)
	if result.Error != nil {
		logs.Error("获取模板内容失败: %v", result.Error)
		ctx.RespErrString(true, &errcode, "获取模板内容失败")
		return
	}

	// 构造响应数据
	type ContentResponse struct {
		Lang    string `json:"Lang"`
		Title   string `json:"Title"`
		Content string `json:"Content"`
	}

	contentResponses := make([]ContentResponse, 0, len(contents))
	for _, content := range contents {
		contentResponses = append(contentResponses, ContentResponse{
			Lang:    content.Lang,
			Title:   content.Title,
			Content: content.Content,
		})
	}

	response := map[string]interface{}{
		"Template": template,
		"Contents": contentResponses,
	}

	ctx.Put("data", response)
	ctx.RespOK()
}

// GetMessageRecords 获取站内信发送记录
func (c *SiteMessageController) GetMessageRecords(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page       int `json:"Page"`
		PageSize   int `json:"PageSize"`
		TemplateID int `json:"TemplateId"`
		UserID     int `json:"UserId"`
		IsRead     int `json:"IsRead"`
	}

	var reqData RequestData
	errcode := 0
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	// 	return
	// }

	// 构建查询条件
	where := abugo.AbuDbWhere{}
	if reqData.TemplateID > 0 {
		where.Add("and", "TemplateId", "=", reqData.TemplateID, 0)
	}
	if reqData.UserID > 0 {
		where.Add("and", "UserId", "=", reqData.UserID, 0)
	}
	if reqData.IsRead != -1 {
		where.Add("and", "IsRead", "=", reqData.IsRead, -1)
	}

	// 查询数据
	var records []model.MessageRecord
	var total int64
	db := server.Db().GormDao()
	query := db.Model(&model.MessageRecord{})

	// 添加查询条件
	if reqData.TemplateID > 0 {
		query = query.Where("TemplateId = ?", reqData.TemplateID)
	}
	if reqData.UserID > 0 {
		query = query.Where("UserId = ?", reqData.UserID)
	}
	if reqData.IsRead != -1 {
		query = query.Where("IsRead = ?", reqData.IsRead)
	}

	// 计算总数
	db.Model(&model.MessageRecord{}).Where(query.Statement.Clauses["WHERE"].Expression).Count(&total)
	// 查询分页数据
	query.Order("SentAt desc").Offset((reqData.Page - 1) * reqData.PageSize).Limit(reqData.PageSize).Find(&records)

	// 将记录转换为响应格式
	result := make([]map[string]interface{}, len(records))
	for i, record := range records {
		// 将结构体转换为 map
		item := map[string]interface{}{
			"Id":          record.ID,
			"TemplateId":  record.TemplateID,
			"UserId":      record.UserID,
			"SellerId":    record.SellerID,
			"ChannelId":   record.ChannelID,
			"Type":        record.Type,
			"TypeName":    GetTemplateTypeName(record.Type),
			"Title":       record.Title,
			"Content":     record.Content,
			"TitleLang":   record.TitleLang,
			"ContentLang": record.ContentLang,
			"IsRead":      record.IsRead,
			"ReadAt":      record.ReadAt,
			"SentAt":      record.SentAt,
		}
		result[i] = item

		// 处理已读状态显示
		if record.IsRead == 1 {
			result[i]["IsReadText"] = "是"
		} else {
			result[i]["IsReadText"] = "否"
		}

		// 获取用户名
		var user struct {
			NickName string
		}
		userResult := db.Table("x_user").Select("NickName").Where("UserId = ?", record.UserID).First(&user)
		if userResult.Error == nil {
			result[i]["NickName"] = user.NickName
		} else {
			result[i]["NickName"] = fmt.Sprintf("用户%d", record.UserID)
		}

		// 获取渠道名称
		var channel struct {
			ChannelName string
		}
		channelResult := db.Table("x_channel").Select("ChannelName").Where("ChannelId = ?", record.ChannelID).First(&channel)
		if channelResult.Error == nil {
			result[i]["ChannelName"] = channel.ChannelName
		} else {
			result[i]["ChannelName"] = fmt.Sprintf("渠道%d", record.ChannelID)
		}
	}

	ctx.Put("data", result)
	ctx.Put("total", total)
	ctx.RespOK()
}

// UpdateTemplateStatus 更新消息模板状态（启用/停用）
func (c *SiteMessageController) UpdateTemplateStatus(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ID         int64  `json:"Id" validate:"required"`
		Status     *int   `json:"Status" validate:"required,oneof=0 1"` // 使用指针类型避免零值问题
		GoogleCode string `json:"GoogleCode,omitempty"`                 // 添加GoogleCode参数，设为可选
	}

	var reqData RequestData
	errcode := 0
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	//token := server.GetToken(ctx)
	//if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "修改"), &errcode, "权限不足") {
	//	return
	//}

	// 创建模板服务
	templateService := NewTemplateService()

	// 确保Status不为nil，并获取其值
	if reqData.Status == nil {
		ctx.RespErrString(true, &errcode, "状态参数不能为空")
		return
	}
	status := *reqData.Status

	// 更新模板状态
	err := templateService.UpdateTemplateStatus(reqData.ID, status)
	if err != nil {
		logs.Error("更新模板状态失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("更新模板状态失败: %v", err))
		return
	}

	// 返回成功响应，包含状态描述
	statusText := "停用"
	if status == 1 {
		statusText = "启用"
	}
	ctx.Put("message", fmt.Sprintf("模板已成功%s", statusText))
	ctx.RespOK()
}

// GetSystemTemplateTypes 获取系统消息模板类型存在情况
func (c *SiteMessageController) GetSystemTemplateTypes(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 权限检查（临时注释）
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	// 	return
	// }

	// 创建模板服务
	templateService := NewTemplateService()

	// 获取系统消息模板类型存在情况
	types, err := templateService.GetSystemTemplateTypes()
	if err != nil {
		logs.Error("获取系统消息模板类型存在情况失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("获取系统消息模板类型存在情况失败: %v", err))
		return
	}

	// 按Type字段排序，确保返回结果的顺序一致性
	sort.Slice(types, func(i, j int) bool {
		return types[i]["Type"].(string) < types[j]["Type"].(string)
	})

	ctx.Put("data", types)
	ctx.RespOK()
}

// GetManualTemplateTypes 获取手动消息模板类型和名称
func (c *SiteMessageController) GetManualTemplateTypes(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 权限检查（临时注释）
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	// 	return
	// }

	// 获取所有手动消息模板类型及其名称
	manualTypes := GetManualTemplateTypeNames()
	if len(manualTypes) == 0 {
		logs.Error("未找到任何手动消息模板类型")
		ctx.RespErrString(true, &errcode, "未找到任何手动消息模板类型")
		return
	}

	// 将映射转换为数组格式，便于前端处理
	result := make([]map[string]interface{}, 0, len(manualTypes))
	for typeCode, typeName := range manualTypes {
		item := map[string]interface{}{
			"Type":     typeCode,
			"TypeName": typeName,
		}
		result = append(result, item)
	}

	// 按Type字段排序，确保返回结果的顺序一致性
	sort.Slice(result, func(i, j int) bool {
		return result[i]["Type"].(string) < result[j]["Type"].(string)
	})

	ctx.Put("data", result)
	ctx.RespOK()
}

// GetSellerList 获取运营商列表
func (c *SiteMessageController) GetSellerList(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 权限检查（临时注释）
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	// 	return
	// }

	// 创建运营商和渠道服务
	sellerChannelService := NewSellerChannelService()

	// 获取运营商列表
	sellers, err := sellerChannelService.GetSellers()
	if err != nil {
		logs.Error("获取运营商列表失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("获取运营商列表失败: %v", err))
		return
	}

	// 返回成功响应
	ctx.Put("data", sellers)
	ctx.RespOK()
}

// GetChannelList 获取指定运营商下的渠道列表
func (c *SiteMessageController) GetChannelList(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 解析请求数据
	type RequestData struct {
		SellerId int64 `json:"SellerId" validate:"required"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	// 	return
	// }

	// 创建运营商和渠道服务
	sellerChannelService := NewSellerChannelService()

	// 获取渠道列表
	channels, err := sellerChannelService.GetChannelsBySellerId(reqData.SellerId)
	if err != nil {
		logs.Error("获取渠道列表失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("获取渠道列表失败: %v", err))
		return
	}

	// 返回成功响应
	ctx.Put("data", channels)
	ctx.RespOK()
}

// ValidateTopAgents 验证顶级代理ID是否存在
func (c *SiteMessageController) ValidateTopAgents(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 解析请求数据
	type RequestData struct {
		TopAgentIds string `json:"TopAgentIds" validate:"required"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（临时注释）
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	// 	return
	// }

	// 如果没有提供ID，返回错误
	if reqData.TopAgentIds == "" {
		ctx.RespErrString(true, &errcode, "请提供顶级代理ID")
		return
	}

	// 创建运营商和渠道服务
	sellerChannelService := NewSellerChannelService()

	// 验证顶级代理ID是否存在
	agents, err := sellerChannelService.ValidateTopAgentIds(reqData.TopAgentIds)
	if err != nil {
		logs.Error("验证顶级代理ID失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("验证顶级代理ID失败: %v", err))
		return
	}

	// 获取请求中的ID列表
	requestedIds := make(map[string]bool)
	for _, idStr := range strings.Split(reqData.TopAgentIds, ",") {
		idStr = strings.TrimSpace(idStr)
		if idStr != "" {
			requestedIds[idStr] = false // 初始化为未找到
		}
	}

	// 处理找到的代理信息，标记存在的ID
	existIds := []string{}
	for _, agent := range agents {
		topAgentId := abugo.GetInt64FromInterface(agent["TopAgentId"])
		topAgentIdStr := strconv.FormatInt(topAgentId, 10)

		// 标记该ID已找到
		if _, exists := requestedIds[topAgentIdStr]; exists {
			requestedIds[topAgentIdStr] = true
			existIds = append(existIds, topAgentIdStr)
		}
	}

	// 收集不存在的ID
	notExistIds := []string{}
	for idStr, found := range requestedIds {
		if !found {
			notExistIds = append(notExistIds, idStr)
		}
	}

	// 构建简化的响应
	response := map[string]interface{}{
		"existIds":    existIds,
		"notExistIds": notExistIds,
		"allExist":    len(notExistIds) == 0,
	}

	// 返回响应
	ctx.Put("data", response)
	ctx.RespOK()
}

// ValidateUsers 验证用户ID是否存在
func (c *SiteMessageController) ValidateUsers(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 解析请求数据
	type RequestData struct {
		UserIds string `json:"UserIds" validate:"required"`
	}

	var reqData RequestData
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	// 权限检查（ 临时注释）
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "系统管理", "站内信管理", "查"), &errcode, "权限不足") {
	// 	return
	// }

	// 如果没有提供ID，返回错误
	if reqData.UserIds == "" {
		ctx.RespErrString(true, &errcode, "请提供 用户ID")
		return
	}

	// 创建运营商和渠道服务
	sellerChannelService := NewSellerChannelService()

	// 验证用户ID是否存在
	users, err := sellerChannelService.ValidateUserIds(reqData.UserIds)
	if err != nil {
		logs.Error("验证用户ID失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("验证用户ID失败: %v", err))
		return
	}

	// 获取请求中的ID列表
	requestedIds := make(map[string]bool)
	for _, idStr := range strings.Split(reqData.UserIds, ",") {
		idStr = strings.TrimSpace(idStr)
		if idStr != "" {
			requestedIds[idStr] = false // 初始化为未找到
		}
	}

	// 处理找到的用户信息，标记存在的ID
	existIds := []string{}
	for _, user := range users {
		userId := abugo.GetInt64FromInterface(user["UserId"])
		userIdStr := strconv.FormatInt(userId, 10)

		// 标记该ID已找到
		if _, exists := requestedIds[userIdStr]; exists {
			requestedIds[userIdStr] = true
			existIds = append(existIds, userIdStr)
		}
	}

	// 收集不存在的ID
	notExistIds := []string{}
	for idStr, found := range requestedIds {
		if !found {
			notExistIds = append(notExistIds, idStr)
		}
	}

	// 构建简化的响应
	response := map[string]interface{}{
		"existIds":    existIds,
		"notExistIds": notExistIds,
		"allExist":    len(notExistIds) == 0,
	}

	// 返回响应
	ctx.Put("data", response)
	ctx.RespOK()
}

// getGameTypeName 根据游戏类型ID获取游戏类型名称
func getGameTypeName(gameType int) string {
	switch gameType {
	case 1:
		return "电子"
	case 2:
		return "棋牌"
	case 3:
		return "趣味"
	case 4:
		return "彩票"
	case 5:
		return "真人"
	case 6:
		return "体育"
	case 7:
		return "真人"
	case 0:
		return "未选择"
	default:
		return "未知"
	}
}
