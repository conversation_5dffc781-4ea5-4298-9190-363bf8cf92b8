package robot

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"log"
	"os"
	"path/filepath"
	"runtime/debug"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	//"github.com/beego/beego/logs"
	//"github.com/go-resty/resty/v2"
	//"github.com/spf13/viper"
)

// reportList 获取TG接待机器人列表
func (c *Router) reportListByAD(ctx *abugo.AbuHttpContent) {
	errCode := 0

	type RequestData struct {
		Page         int      `json:"page"`
		PageSize     int      `json:"page_size"`
		StartTime    int64    `json:"start_time"`
		EndTime      int64    `json:"end_time"`
		SellerID     []int32  `json:"seller_id"`
		ChannelID    []int32  `json:"channel_id"`
		UserChatID   []int64  `json:"user_chat_id"`
		Name         []string `json:"name"`
		UserName     []string `json:"user_name"`
		UserFullName []string `json:"user_full_name"`
		IsExport     int      `json:"is_export"` // 是否导出 默认0 不导出
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "广告机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	type Result struct {
		model.XRobotAdStartRecord
		SellerName  string `json:"seller_name"`
		ChannelName string `json:"channel_name"`
	}
	var results []*Result

	dao := server.DaoxHashGame().XRobotAdStartRecord
	query := dao.WithContext(nil).Select(dao.ALL)

	if len(req.SellerID) > 0 {
		query.Where(dao.SellerID.In(req.SellerID...))
	}
	if len(req.ChannelID) > 0 {
		query.Where(dao.ChannelID.In(req.ChannelID...))

	}
	if len(req.UserChatID) > 0 {
		query.Where(dao.UserChatID.In(req.UserChatID...))
	}
	if len(req.Name) > 0 {
		query.Where(dao.Name.In(req.Name...))
	}
	if len(req.UserName) > 0 {
		query.Where(dao.UserName.In(req.UserName...))
	}

	tm1 := time.Unix(0, req.StartTime*int64(time.Millisecond))
	tm2 := time.Unix(0, req.EndTime*int64(time.Millisecond))
	if !tm1.IsZero() && !tm2.IsZero() && req.StartTime != 0 && req.EndTime != 0 {
		query.Where(dao.CreateTime.Between(tm1, tm2))
	}

	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	total, err := query.WithContext(nil).
		Select(dao.ALL, xSeller.SellerName, xChannel.ChannelName).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		LeftJoin(xChannel, xChannel.ChannelID.EqCol(dao.ChannelID)).
		Order(dao.StartCnt.Desc(), dao.CreateTime.Desc()).
		ScanByPage(&results, offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	if 1 == req.IsExport {
		var fileMap = map[string]string{
			"运营商ID":    "seller_id",
			"运营商名称":    "seller_name",
			"渠道号ID":    "channel_id",
			"渠道号名称":    "channel_name",
			"机器人名称":    "name",
			"用户TGID":   "user_chat_id",
			"用户名称":     "user_name",
			"用户全称":     "user_full_name",
			"用户最近启动语言": "lang_code",
			"首次启动时间":   "start_first_time",
			"最近启动时间":   "start_last_time",
			"启动次数":     "start_cnt",
		}
		// 设置表头
		var headerLine = []string{"运营商ID", "运营商名称", "渠道号ID", "渠道号名称", "机器人名称",
			"用户TGID", "用户名称", "用户全称", "用户最近启动语言", "首次启动时间", "最近启动时间", "启动次数",
		}
		var mappedResults []map[string]interface{}
		for _, r := range results {
			mapped := map[string]interface{}{
				"id":               r.ID,
				"seller_id":        r.SellerID,
				"channel_id":       r.ChannelID,
				"name":             r.Name,
				"token":            r.Token,
				"user_chat_id":     r.UserChatID,
				"user_name":        r.UserName,
				"user_full_name":   r.UserFullName,
				"lang_code":        r.LangCode,
				"start_first_time": r.StartFirstTime,
				"start_last_time":  r.StartLastTime,
				"start_cnt":        r.StartCnt,
				"seller_name":      r.SellerName,
				"channel_name":     r.ChannelName,
			}
			mappedResults = append(mappedResults, mapped)
		}

		filename, err := c.exportExcel(&mappedResults, headerLine, fileMap)
		if err != nil {
			ctx.RespErrString(true, &errCode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("total", total)
		ctx.Put("results", results)
		ctx.RespOK()
	}
}

// 导出Excel文件
func (c *Router) exportExcel(list *[]map[string]interface{}, headerLine []string, filterMap map[string]string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("❌ 导出 Excel panic: %v\n%s", r, debug.Stack())
		}
	}()
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	if err := excel.SetSheetName("Sheet1", sheetName); err != nil {
		return "", err
	}

	// 写入表头
	if err := excel.SetSheetRow(sheetName, "A1", &headerLine); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", err
	}

	// 逐行写入数据（串行）
	for j, d := range *list {
		rowIndex := j + 2
		row := make([]interface{}, len(headerLine))
		for k, h := range headerLine {
			if dbField, ok := filterMap[h]; ok {
				val := d[dbField]
				row[k] = val
			} else {
				row[k] = ""
			}
		}
		cell := fmt.Sprintf("A%d", rowIndex)
		if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
			log.Println("❌ 写入 Excel 失败:", err)
		}
	}

	filename := "export_report_ad_robot_" + time.Now().Format("20060102150405") + ".xlsx"
	filePath := filepath.Join(server.ExportDir(), filename)
	if err := excel.SaveAs(filePath); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功:", filePath)
	return filename, nil
}

func (c *Router) exportCSV(list *[]map[string]interface{},
	headerLine []string, filterMap map[string]string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("❌ 导出 CSV panic: %v\n%s", r, debug.Stack())
		}
	}()
	exportDir := server.ExportDir()
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return "", fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 跨平台路径拼接
	filename := fmt.Sprintf("export_data_%s.csv", time.Now().Format("20060102150405"))
	filePath := filepath.Join(exportDir, filename)

	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
		}
	}(file)
	// 写入表头
	var headerLineBuilder strings.Builder
	for i, h := range headerLine {
		if i > 0 {
			headerLineBuilder.WriteString(",")
		}
		headerLineBuilder.WriteString(`"` + strings.ReplaceAll(h, `"`, `""`) + `"`)
	}
	headerLineBuilder.WriteString("\n")
	if _, err := file.WriteString(headerLineBuilder.String()); err != nil {
		return "", fmt.Errorf("写入表头失败: %v", err)
	}

	// 逐行写入数据
	for i, data := range *list {
		var rowBuilder strings.Builder
		for j, h := range headerLine {
			if j > 0 {
				rowBuilder.WriteString(",")
			}
			if filterMap != nil {
				dbField := filterMap[h]
				val := fmt.Sprintf("%v", data[dbField])

				// 处理值中的特殊字符（引号、逗号）
				if strings.ContainsAny(val, `,"\n`) {
					val = `"` + strings.ReplaceAll(val, `"`, `""`) + `"`
				}
				rowBuilder.WriteString(val)
			}
		}
		rowBuilder.WriteString("\n")

		if _, err := file.WriteString(rowBuilder.String()); err != nil {
			return "", fmt.Errorf("写入数据失败: %v", err)
		}

		// 每100行强制刷盘
		if i%100 == 0 {
			if err := file.Sync(); err != nil {
				return "", fmt.Errorf("文件同步失败: %v", err)
			}
		}
	}

	// 最终强制刷盘一次
	if err := file.Sync(); err != nil {
		return "", fmt.Errorf("最终文件同步失败: %v", err)
	}

	return filename, nil
}
