package warning

import (
	"github.com/beego/beego/logs"
	"strconv"
	"time"

	"xserver/abugo"
	"xserver/server"
)

// WinScoreWarningService 赢分预警服务
type WinScoreWarningService struct{}

// NewWinScoreWarningService 创建赢分预警服务实例
func NewWinScoreWarningService() *WinScoreWarningService {
	return &WinScoreWarningService{}
}

// WarningData 预警数据结构
type WarningData struct {
	SellerId  int     `json:"seller_id"`  // 运营商ID
	ChannelId int     `json:"channel_id"` // 渠道ID
	UserId    int64   `json:"user_id"`    // 用户ID
	GameType  int     `json:"game_type"`  // 游戏类型
	GameId    string  `json:"game_id"`    // 游戏ID
	GameName  string  `json:"game_name"`  // 游戏名称
	Brand     string  `json:"brand"`      // 游戏品牌
	ThirdId   string  `json:"third_id"`   // 第三方订单号
	BetAmount float64 `json:"bet_amount"` // 投注金额
	WinAmount float64 `json:"win_amount"` // 派彩金额
	Symbol    string  `json:"symbol"`     // 币种
	ThirdTime string  `json:"third_time"` // 游戏时间
}

// CheckAndInsertWarning 检查并插入预警数据
func (s *WinScoreWarningService) CheckAndInsertWarning(data WarningData) error {
	// 计算赢分（派彩金额 - 投注金额）
	winScore := data.WinAmount - data.BetAmount
	gameType, _, _ := GetGameTypeInfo(data.GameType, data.GameId)
	if gameType == -1 {
		data.Symbol = "trx"
	} else {
		data.Symbol = "usdt"
	}
	// 获取该运营商的预警阈值
	usdtThreshold, trxThreshold := GetWarningThresholds(data.SellerId)

	logs.Info("检查预警阈值",
		"sellerId", data.SellerId,
		"userId", data.UserId,
		"betAmount", data.BetAmount,
		"winAmount", data.WinAmount,
		"winScore", winScore,
		"symbol", data.Symbol,
		"usdtThreshold", usdtThreshold,
		"trxThreshold", trxThreshold)

	// 检查是否触发预警
	shouldAlert := false
	if data.Symbol == "usdt" && usdtThreshold > 0 && winScore >= usdtThreshold {
		shouldAlert = true
	} else if data.Symbol == "trx" && trxThreshold > 0 && winScore >= trxThreshold {
		shouldAlert = true
	}

	if !shouldAlert {
		logs.Debug("未触发预警条件", "sellerId", data.SellerId, "userId", data.UserId, "winScore", winScore)
		return nil
	}

	// 使用GormDao操作数据库
	db := server.Db().GormDao()

	// 构建预警数据记录
	warningRecord := map[string]interface{}{
		"SellerId":  data.SellerId,
		"ChannelId": data.ChannelId,
		"UserId":    data.UserId,
		"GameType":  data.GameType,
		"GameId":    data.GameId,
		"GameName":  data.GameName,
		"Brand":     data.Brand,
		"ThirdId":   data.ThirdId,
		"BetAmount": data.BetAmount,
		"WinAmount": data.WinAmount,
		"WinScore":  winScore,
		"Symbol":    data.Symbol,
		"ThirdTime": data.ThirdTime,
	}

	// 插入预警数据
	err := db.Table("x_win_score_warning").Create(warningRecord).Error

	if err != nil {
		logs.Error("插入预警数据失败", "error", err)
		return err
	}

	logs.Info("成功插入预警数据",
		"sellerId", data.SellerId,
		"userId", data.UserId,
		"winScore", winScore,
		"symbol", data.Symbol)

	// 增加Redis中的预警计数
	err = IncrementWarningCount(data.SellerId)
	if err != nil {
		logs.Error("增加预警计数失败", "sellerId", data.SellerId, "error", err)
		// 不返回错误，避免影响主流程
	}

	return nil
}

// Redis Key常量
const (
	WinScoreWarningCountKey = "win_score_warning_count_"
)

// 配置Key常量
const (
	ConfigKeyWinScoreWarningUsdt = "WinScoreWarningUsdt"
	ConfigKeyWinScoreWarningTrx  = "WinScoreWarningTrx"
)

// GetWarningCount 获取运营商预警数量
func GetWarningCount(sellerId int) (int, error) {
	key := WinScoreWarningCountKey + strconv.Itoa(sellerId)
	rValueInterface := server.Redis().Get(key)
	if rValueInterface == nil {
		return 0, nil
	}
	// 将[]uint8转换为字符串，再转换为int
	dataBytes := rValueInterface.([]uint8)
	dataStr := string(dataBytes)
	rValue := int(abugo.GetInt64FromInterface(dataStr))
	return rValue, nil
}

// GetAllWarningCount 获取所有运营商预警数量总和
func GetAllWarningCount() (int, error) {
	key := WinScoreWarningCountKey + "all"
	rValueInterface := server.Redis().Get(key)
	if rValueInterface == nil {
		return 0, nil
	}
	// 将[]uint8转换为字符串，再转换为int
	dataBytes := rValueInterface.([]uint8)
	dataStr := string(dataBytes)
	rValue := int(abugo.GetInt64FromInterface(dataStr))
	return rValue, nil
}

// IncrementWarningCount 增加运营商预警数量
func IncrementWarningCount(sellerId int) error {
	// 增加指定运营商的预警数量
	sellerKey := WinScoreWarningCountKey + strconv.Itoa(sellerId)
	currentCount, _ := GetWarningCount(sellerId)
	newCount := currentCount + 1
	err := server.Redis().SetString(sellerKey, strconv.Itoa(newCount))
	if err != nil {
		logs.Error("增加运营商预警数量失败", "sellerId", sellerId, "error", err)
		return err
	}

	// 增加总预警数量
	allKey := WinScoreWarningCountKey + "all"
	allCurrentCount, _ := GetAllWarningCount()
	allNewCount := allCurrentCount + 1
	err = server.Redis().SetString(allKey, strconv.Itoa(allNewCount))
	if err != nil {
		logs.Error("增加总预警数量失败", "error", err)
		return err
	}

	logs.Info("预警数量已增加", "sellerId", sellerId, "newCount", newCount, "allNewCount", allNewCount)
	return nil
}

// ClearWarningCount 清除运营商预警数量
func ClearWarningCount(sellerId int) error {
	key := WinScoreWarningCountKey + strconv.Itoa(sellerId)
	err := server.Redis().Del(key)
	if err != nil {
		logs.Error("清除运营商预警数量失败", "sellerId", sellerId, "error", err)
	}
	return err
}

// ClearAllWarningCount 清除所有预警数量
func ClearAllWarningCount() error {
	allKey := WinScoreWarningCountKey + "all"
	err := server.Redis().Del(allKey)
	if err != nil {
		logs.Error("清除所有预警数量失败", "error", err)
	}
	return err
}

// GetWarningThresholds 获取运营商的预警阈值配置
func GetWarningThresholds(sellerId int) (usdtThreshold, trxThreshold float64) {
	usdtThreshold = abugo.GetFloat64FromInterface(server.GetConfigString(sellerId, 0, ConfigKeyWinScoreWarningUsdt))
	trxThreshold = abugo.GetFloat64FromInterface(server.GetConfigString(sellerId, 0, ConfigKeyWinScoreWarningTrx))
	return usdtThreshold, trxThreshold
}

// PushWinWarning 插入预警数据
func PushWinWarning(gameType int, sellerId int, userId int64, gameId string, gameName string, betAmount float64, winAmount float64) error {
	// 创建预警服务实例
	warningService := NewWinScoreWarningService()

	// 获取游戏类型名称和品牌信息
	//_, brand := getGameTypeNameAndBrand(gameType, gameId)

	// 构建预警数据
	warningData := WarningData{
		SellerId: sellerId,
		//ChannelId: 0, // 默认渠道，可根据需要调整
		UserId:   userId,
		GameType: gameType,
		GameId:   gameId,
		GameName: gameName,
		//Brand:     brand,
		//ThirdId:   "", // 第三方订单号，可根据需要传入
		BetAmount: betAmount,
		WinAmount: winAmount,
		Symbol:    "usdt", // 默认币种，可根据需要调整
		ThirdTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 异步检查并插入预警数据
	go func() {
		err := warningService.CheckAndInsertWarning(warningData)
		if err != nil {
			logs.Error("游戏派奖预警检查失败",
				"gameType", gameType,
				"sellerId", sellerId,
				"userId", userId,
				"gameId", gameId,
				"betAmount", betAmount,
				"winAmount", winAmount,
				"error", err)
		} else {
			logs.Debug("游戏派奖预警检查完成",
				"gameType", gameType,
				"sellerId", sellerId,
				"userId", userId,
				"gameId", gameId,
				"winScore", winAmount-betAmount)
		}
	}()

	return nil
}

// getGameTypeNameAndBrand 根据游戏类型和游戏ID获取游戏类型名称和品牌
func getGameTypeNameAndBrand(gameType int, gameId int) (gameTypeName string, brand string) {
	switch gameType {
	case -2:
		return "余额哈希", ""
	case -1:
		return "哈希游戏", ""
	case 1:
		return "彩票游戏", "PP" // 可根据实际情况调整品牌
	case 2:
		return "电子游戏", "PP"
	case 3:
		return "棋牌游戏", "PP"
	case 4:
		return "小游戏", "PP"
	case 5:
		return "真人游戏", "PP"
	case 6:
		return "体育游戏", "PP"
	case 7:
		return "德州扑克", "PP"
	default:
		return "未知游戏", ""
	}
}

// GetGameTypeInfo 根据表名和游戏ID获取游戏类型信息
func GetGameTypeInfo(gameType_ int, gameId string) (gameType int, gameTypeName string, gameName string) {
	switch gameType {
	case 0:
		//// 哈希游戏需要根据GameId细分
		//if gameId >= 101 && gameId <= 206 {
		//	// 余额哈希游戏ID范围
		//	return -2, "余额哈希", "余额哈希"
		//} else {
		//	// 转账哈希游戏ID范围
		//	return -1, "哈希游戏", "哈希游戏"
		//}
		return -1, "哈希游戏", "哈希游戏"
	case 1:
		return 1, "彩票游戏", "彩票游戏"
	case 2:
		return 2, "电子游戏", "电子游戏"
	case 3:
		return 3, "棋牌游戏", "棋牌游戏"
	case 4:
		return 4, "小游戏", "小游戏"
	case 5:
		return 5, "真人游戏", "真人游戏"
	case 6:
		return 6, "体育游戏", "体育游戏"
	case 7:
		return 7, "德州扑克", "德州扑克"
	default:
		return 0, "未知游戏", "未知游戏"
	}
}
