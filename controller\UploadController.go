package controller

import (
	"fmt"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/share"
	"xserver/utils"

	"github.com/golang-module/carbon/v2"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/beego/beego/logs"
	"github.com/spf13/viper"
	daoGrom "gorm.io/gorm"
)

type UploadController struct {
	aws_key      string
	aws_secret   string
	aws_endpoint string
	aws_bucket   string
	aws_region   string
}

func (c *UploadController) Init() {
	c.aws_key = viper.GetString("image.aws.key")
	c.aws_secret = viper.GetString("image.aws.secret")
	c.aws_endpoint = viper.GetString("image.aws.endpoint")
	c.aws_bucket = viper.GetString("image.aws.bucket")
	c.aws_region = viper.GetString("image.aws.region")
	//logs.Debug("aws:", c.aws_key, c.aws_secret, c.aws_endpoint, c.aws_bucket, c.aws_region)
	server.Http().Post("/api/upload", c.upload)
	server.Http().Post("/api/uploadThirdGameInfo", c.uploadThirdGameInfo)
	server.Http().Post("/api/uploadActive", c.uploadActive)
	server.Http().Post("/api/uploadHomeCarousel", c.uploadHomeCarousel)
	server.Http().Post("/api/uploadNoticce", c.uploadNoticce)
}

func (c *UploadController) upload(ctx *abugo.AbuHttpContent) {
	errcode := 0
	_, headers, err := ctx.FromFile("file")
	if ctx.RespErr(err, &errcode) {
		logs.Error("upload FromFile ", err)
		return
	}
	logs.Debug("upload headers.Size:", headers.Size, headers.Filename)
	//if ctx.RespErrString(headers.Size > 1024*1024*5, &errcode, "上传文件太大") {
	//	return
	//}
	ct := headers.Header.Get("Content-Type")
	//if ctx.RespErrString(ct != "image/png" && ct != "image/jpg" && ct != "image/jpeg", &errcode, "只能上传图片文件(png,jpg,jpeg)") {
	//	return
	//}
	sess, err := session.NewSession(&aws.Config{
		Credentials:      credentials.NewStaticCredentials(c.aws_key, c.aws_secret, ""),
		Endpoint:         aws.String(c.aws_endpoint),
		Region:           aws.String(c.aws_region),
		S3ForcePathStyle: aws.Bool(false),
	})
	if ctx.RespErr(err, &errcode) {
		logs.Error("upload NewSession ", err)
		return
	}
	logs.Debug("upload success NewSession ")
	uploader := s3manager.NewUploader(sess, func(u *s3manager.Uploader) {
		u.PartSize = 64 * 1024 * 1024 // 64MB per part
	})
	ext := strings.Split(ct, "/")[1]
	filename := strings.ReplaceAll(abugo.GetUuid(), "-", "")
	//filename = filename + "." + ext
	filename = getImagesName(filename, ext)
	file, ferr := headers.Open()
	if ctx.RespErr(ferr, &errcode) {
		logs.Error("upload headers Open ", ferr)
		return
	}
	logs.Debug("upload success headers Open ")
	_, uerr := uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(c.aws_bucket),
		Key:         aws.String(filename),
		Body:        file,
		ContentType: aws.String(ct),
	})
	file.Close()
	if ctx.RespErr(uerr, &errcode) {
		logs.Error("upload uerr", err)
		return
	}
	logs.Debug("upload success uerr ")
	ctx.Put("url", server.ImageUrl())
	ctx.Put("filename", "/"+filename)
	ctx.RespOK()
}

func (c *UploadController) uploadThirdGameInfo(ctx *abugo.AbuHttpContent) {
	errcode := 0
	_, headers, err := ctx.FromFile("file")
	if ctx.RespErr(err, &errcode) {
		logs.Error(err)
		return
	}
	logs.Debug("uploadThirdGameInfo headers.Size:", headers.Size)
	//if ctx.RespErrString(headers.Size > 1024*1024*5, &errcode, "上传文件太大") {
	//	return
	//}
	ct := headers.Header.Get("Content-Type")
	disposition := headers.Header.Get("Content-Disposition")
	dis := strings.Split(disposition, ";")
	tempname := ""
	for _, v := range dis {
		vv := strings.Split(v, "=")
		if len(vv) == 2 && strings.TrimSpace(vv[0]) == "filename" {
			tempname = vv[1]
		}
	}
	filenames := removeImageExtension(tempname)
	names := strings.Split(strings.ReplaceAll(filenames, "\"", ""), "@")
	if len(names) != 4 {
		ctx.RespErrString(true, &errcode, "文件名称格式不正确")
		return
	}
	//找到相关图片
	plat := names[0]
	lang := names[1]
	gameId := names[2]
	gameName := names[3]
	where := abugo.AbuDbWhere{}
	where.Add("and", "GameId", "=", gameId, nil)
	where.Add("and", "Brand", "=", plat, nil)
	betTran, err := server.Db().Table("x_game_list").Where(where).GetOne()
	if err != nil || betTran == nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("游戏厂商:%s id:%s 不存在!", plat, gameId))
		return
	}

	sess, err := session.NewSession(&aws.Config{
		Credentials:      credentials.NewStaticCredentials(c.aws_key, c.aws_secret, ""),
		Endpoint:         aws.String(c.aws_endpoint),
		Region:           aws.String(c.aws_region),
		S3ForcePathStyle: aws.Bool(false),
	})
	if ctx.RespErr(err, &errcode) {
		logs.Error(err)
		return
	}
	uploader := s3manager.NewUploader(sess, func(u *s3manager.Uploader) {
		u.PartSize = 64 * 1024 * 1024 // 64MB per part
	})
	ext := strings.Split(ct, "/")[1]

	filename := strings.ReplaceAll(abugo.GetUuid(), "-", "")
	//filename = filename + "." + ext
	filename = getImagesName(filename, ext)
	file, ferr := headers.Open()
	if ctx.RespErr(ferr, &errcode) {
		logs.Error(err)
		return
	}
	_, uerr := uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(c.aws_bucket),
		Key:         aws.String(filename),
		Body:        file,
		ContentType: aws.String(ct),
	})
	file.Close()
	if ctx.RespErr(uerr, &errcode) {
		logs.Error(err)
		return
	}
	//修改相关数据
	if lang == "zh" {
		_, err = server.Db().Conn().Exec(`update x_game_list set
		Name = ?,
		Icon = ?
		where Id = ?`,
			gameName,
			"/"+filename,
			(*betTran)["Id"],
		)
		if err != nil {
			ctx.RespErrString(true, &errcode, fmt.Sprintf("游戏厂商:%s id:%s 修改失败!", plat, gameId))
			return
		}
	} else if lang == "en" {
		_, err = server.Db().Conn().Exec(`update x_game_list set
		EName = ?,
		EIcon = ?
		where Id = ?`,
			gameName,
			"/"+filename,
			(*betTran)["Id"],
		)
		if err != nil {
			ctx.RespErrString(true, &errcode, fmt.Sprintf("游戏厂商:%s id:%s 修改失败!", plat, gameId))
			return
		}
	} else {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("游戏厂商:%s id:%s 语言不正确!", plat, gameId))
		return
	}

	ctx.Put("url", server.ImageUrl())
	ctx.Put("filename", "/"+filename)
	ctx.RespOK()
}

func (c *UploadController) uploadActive(ctx *abugo.AbuHttpContent) {
	errcode := 0
	_, headers, err := ctx.FromFile("file")
	if ctx.RespErr(err, &errcode) {
		logs.Error("uploadActive FromFile ", err)
		return
	}
	ct := headers.Header.Get("Content-Type")
	logs.Debug("uploadActive headers.Size:", headers.Size, headers.Filename, ct)
	fileNameExt := removeImageExtension(headers.Filename)
	fileNames := strings.Split(strings.ReplaceAll(fileNameExt, "\"", ""), "@")
	if len(fileNames) != 4 {
		ctx.RespErrString(true, &errcode, "文件名称格式不正确")
		return
	}
	typ := fileNames[0]
	channelId := abugo.GetInt64FromInterface(fileNames[1])
	activeId := abugo.GetInt64FromInterface(fileNames[2])
	langD := share.SimpleToLang(fileNames[3])
	if langD == share.Lang_nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("多语言不存在 %s", fileNames[3]))
		return
	}
	lang := abugo.GetStringFromInterface2(int32(langD))
	var data model.XActiveDefine
	result := server.Db().GormDao().Table("x_active_define").Where("channelId = ? and activeid = ?", channelId, activeId).First(&data)
	if result.Error != nil && result.Error != daoGrom.ErrRecordNotFound {
		ctx.RespErr(result.Error, &errcode)
		return
	}
	if result.RowsAffected < 1 {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("活动不存在 渠道 %d id %d", channelId, activeId))
		return
	}
	var field string
	if typ == "top" {
		field = "TopImgLang"
	} else if typ == "title" {
		field = "TitleImgLang"
	} else {
		ctx.RespErrString(true, &errcode, "格式错误")
		return
	}

	sess, err := session.NewSession(&aws.Config{
		Credentials:      credentials.NewStaticCredentials(c.aws_key, c.aws_secret, ""),
		Endpoint:         aws.String(c.aws_endpoint),
		Region:           aws.String(c.aws_region),
		S3ForcePathStyle: aws.Bool(false),
	})
	if ctx.RespErr(err, &errcode) {
		logs.Error("uploadActive NewSession ", err)
		return
	}
	logs.Debug("uploadActive success NewSession ")
	uploader := s3manager.NewUploader(sess, func(u *s3manager.Uploader) {
		u.PartSize = 64 * 1024 * 1024 // 64MB per part
	})
	ext := strings.Split(ct, "/")[1]
	filename := strings.ReplaceAll(abugo.GetUuid(), "-", "")
	//filename = filename + "." + ext
	filename = getImagesName(filename, ext)
	file, ferr := headers.Open()
	if ctx.RespErr(ferr, &errcode) {
		logs.Error("uploadActive headers Open ", ferr)
		return
	}
	logs.Debug("uploadActive success headers Open ")
	_, uerr := uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(c.aws_bucket),
		Key:         aws.String(filename),
		Body:        file,
		ContentType: aws.String(ct),
	})
	file.Close()
	if ctx.RespErr(uerr, &errcode) {
		logs.Error("uploadActive uerr", err)
		return
	}
	logs.Debug("uploadActive success uerr ")
	filename = "/" + filename

	sql := `UPDATE x_active_define SET %s = JSON_SET(IFNULL(%s, "{}"), '$."%s"', '%s') WHERE channelid = %d AND activeid = %d`
	sql = fmt.Sprintf(sql, field, field, lang, filename, channelId, activeId)
	result = server.Db().GormDao().Exec(sql)
	if ctx.RespErr(result.Error, &errcode) {
		logs.Error("uploadActive update err", result.Error)
		return
	}
	ctx.Put("url", server.ImageUrl())
	ctx.Put("filename", filename)
	ctx.RespOK()
}

func (c *UploadController) uploadHomeCarousel(ctx *abugo.AbuHttpContent) {
	errcode := 0
	_, headers, err := ctx.FromFile("file")
	if ctx.RespErr(err, &errcode) {
		logs.Error("uploadHomeCarousel FromFile ", err)
		return
	}
	ct := headers.Header.Get("Content-Type")
	logs.Debug("uploadHomeCarousel headers.Size:", headers.Size, headers.Filename, ct)
	fileNameExt := removeImageExtension(headers.Filename)
	fileNames := strings.Split(strings.ReplaceAll(fileNameExt, "\"", ""), "@")
	if len(fileNames) != 4 {
		ctx.RespErrString(true, &errcode, "文件名称格式不正确")
		return
	}
	typ := fileNames[0]
	channelId := abugo.GetInt64FromInterface(fileNames[1])
	id := abugo.GetInt64FromInterface(fileNames[2])
	langD := share.SimpleToLang(fileNames[3])
	if langD == share.Lang_nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("多语言不存在 %s", fileNames[3]))
		return
	}
	lang := abugo.GetStringFromInterface2(int32(langD))
	var data model.XHomeCarouselV2
	result := server.Db().GormDao().Table("x_home_carousel_v2").Where("channelId = ? and id = ?", channelId, id).First(&data)
	if result.Error != nil && result.Error != daoGrom.ErrRecordNotFound {
		ctx.RespErr(result.Error, &errcode)
		return
	}
	if result.RowsAffected < 1 {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("轮播图不存在 渠道 %d id %d", channelId, id))
		return
	}
	var field string
	typ = strings.ToLower(typ)
	if typ == "h5" {
		field = "Url"
	} else if typ == "pc" {
		field = "PcUrl"
	} else {
		ctx.RespErrString(true, &errcode, "格式错误")
		return
	}

	sess, err := session.NewSession(&aws.Config{
		Credentials:      credentials.NewStaticCredentials(c.aws_key, c.aws_secret, ""),
		Endpoint:         aws.String(c.aws_endpoint),
		Region:           aws.String(c.aws_region),
		S3ForcePathStyle: aws.Bool(false),
	})
	if ctx.RespErr(err, &errcode) {
		logs.Error("uploadHomeCarousel NewSession ", err)
		return
	}
	logs.Debug("uploadHomeCarousel success NewSession ")
	uploader := s3manager.NewUploader(sess, func(u *s3manager.Uploader) {
		u.PartSize = 64 * 1024 * 1024 // 64MB per part
	})
	ext := strings.Split(ct, "/")[1]
	filename := strings.ReplaceAll(abugo.GetUuid(), "-", "")
	//filename = filename + "." + ext
	filename = getImagesName(filename, ext)
	file, ferr := headers.Open()
	if ctx.RespErr(ferr, &errcode) {
		logs.Error("uploadHomeCarousel headers Open ", ferr)
		return
	}
	logs.Debug("uploadHomeCarousel success headers Open ")
	_, uerr := uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(c.aws_bucket),
		Key:         aws.String(filename),
		Body:        file,
		ContentType: aws.String(ct),
	})
	file.Close()
	if ctx.RespErr(uerr, &errcode) {
		logs.Error("uploadHomeCarousel uerr", err)
		return
	}
	logs.Debug("uploadHomeCarousel success uerr ")
	filename = "/" + filename

	sql := `insert into x_home_carousel_v2 (sellerid, channelid, id, lang, name, %s) values (?, ?, ?, ?, ?, ?) 
		on duplicate key update %s = ?`
	sql = fmt.Sprintf(sql, field, field)
	result = server.Db().GormDao().Exec(sql, data.SellerID, channelId, id, lang, data.Name, filename, filename)
	if ctx.RespErr(result.Error, &errcode) {
		logs.Error("uploadActive update err", result.Error)
		return
	}
	ctx.Put("url", server.ImageUrl())
	ctx.Put("filename", filename)
	ctx.RespOK()
}

func (c *UploadController) uploadNoticce(ctx *abugo.AbuHttpContent) {
	errcode := 0
	_, headers, err := ctx.FromFile("file")
	if ctx.RespErr(err, &errcode) {
		logs.Error("uploadNoticce FromFile ", err)
		return
	}
	ct := headers.Header.Get("Content-Type")
	logs.Debug("uploadNoticce headers.Size:", headers.Size, headers.Filename, ct)
	fileNameExt := removeImageExtension(headers.Filename)
	fileNames := strings.Split(strings.ReplaceAll(fileNameExt, "\"", ""), "@")
	if len(fileNames) != 4 {
		ctx.RespErrString(true, &errcode, "文件名称格式不正确")
		return
	}
	typ := fileNames[0]
	channelId := abugo.GetInt64FromInterface(fileNames[1])
	id := abugo.GetInt64FromInterface(fileNames[2])
	langD := share.SimpleToLang(fileNames[3])
	if langD == share.Lang_nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("多语言不存在 %s", fileNames[3]))
		return
	}
	lang := abugo.GetStringFromInterface2(int32(langD))
	var data model.XNoticeV2
	result := server.Db().GormDao().Table("x_notice_v2").Where("channelId = ? and id = ?", channelId, id).First(&data)
	if result.Error != nil && result.Error != daoGrom.ErrRecordNotFound {
		ctx.RespErr(result.Error, &errcode)
		return
	}
	if result.RowsAffected < 1 {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("公告不存在 渠道 %d id %d", channelId, id))
		return
	}
	var field string
	typ = strings.ToLower(typ)
	if typ == "img" {
		field = "Img"
	} else {
		ctx.RespErrString(true, &errcode, "格式错误")
		return
	}

	sess, err := session.NewSession(&aws.Config{
		Credentials:      credentials.NewStaticCredentials(c.aws_key, c.aws_secret, ""),
		Endpoint:         aws.String(c.aws_endpoint),
		Region:           aws.String(c.aws_region),
		S3ForcePathStyle: aws.Bool(false),
	})
	if ctx.RespErr(err, &errcode) {
		logs.Error("uploadNoticce NewSession ", err)
		return
	}
	logs.Debug("uploadNoticce success NewSession ")
	uploader := s3manager.NewUploader(sess, func(u *s3manager.Uploader) {
		u.PartSize = 64 * 1024 * 1024 // 64MB per part
	})
	ext := strings.Split(ct, "/")[1]
	filename := strings.ReplaceAll(abugo.GetUuid(), "-", "")
	//filename = filename + "." + ext
	filename = getImagesName(filename, ext)
	file, ferr := headers.Open()
	if ctx.RespErr(ferr, &errcode) {
		logs.Error("uploadNoticce headers Open ", ferr)
		return
	}
	logs.Debug("uploadNoticce success headers Open ")
	_, uerr := uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(c.aws_bucket),
		Key:         aws.String(filename),
		Body:        file,
		ContentType: aws.String(ct),
	})
	file.Close()
	if ctx.RespErr(uerr, &errcode) {
		logs.Error("uploadNoticce uerr", err)
		return
	}
	logs.Debug("uploadNoticce success uerr ")
	filename = "/" + filename

	sql := `insert into x_notice_v2 (sellerid, channelid, id, LangId, Title, %s) values (?, ?, ?, ?, ?, ?) 
		on duplicate key update %s = ?`
	sql = fmt.Sprintf(sql, field, field)
	result = server.Db().GormDao().Exec(sql, data.SellerID, channelId, id, lang, data.Title, filename, filename)
	if ctx.RespErr(result.Error, &errcode) {
		logs.Error("uploadActive update err", result.Error)
		return
	}
	ctx.Put("url", server.ImageUrl())
	ctx.Put("filename", filename)
	ctx.RespOK()
}

func removeImageExtension(imageName string) string {
	// 分割文件名和后缀
	ext := strings.Split(imageName, ".")
	// 如果后缀长度大于1，则去除后缀
	if len(ext) > 1 {
		return strings.Join(ext[:len(ext)-1], ".")
	}
	// 否则返回原图片名
	return imageName
}
func getImagesName(filename, ext string) string {
	folderPath := "image/" + carbon.Parse(carbon.Now().String()).StdTime().Format(utils.DateFormatStr)
	filename = folderPath + "/" + filename + "." + ext
	return filename
}
