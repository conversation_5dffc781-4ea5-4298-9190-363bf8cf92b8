package controller

import (
	"bytes"
	"errors"
	"github.com/go-sql-driver/mysql"
	"github.com/shopspring/decimal"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"
)

type TyjBroadcastController struct{}

func (c *TyjBroadcastController) Init() {
	group := server.Http().NewGroup("/api/TyjBroadcast")
	{
		group.Post("/creat", c.creat)
		group.Post("/list", c.list)
		group.Post("/info", c.info)
		group.Post("/update", c.update)
		group.Post("/update_status", c.update_status)
		group.Post("/delete", c.delete)
		group.PostNoAuth("/broadcast", c.broadcast)
	}
}

func (c *TyjBroadcastController) creat(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTyjBroadcastConfig
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "语音播报设置", "增", "语音播报创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	if bytes.Count([]byte(reqdata.Content), nil) >= 8000 {
		ctx.RespErr(errors.New("内容长度需小于8000字节"), &errcode)
		return
	}

	dao := server.DaoxHashGame().XTyjBroadcastConfig
	db := dao.WithContext(ctx.Gin())
	err := db.Create(&reqdata.XTyjBroadcastConfig)
	if err != nil {
		if e, ok := err.(*mysql.MySQLError); ok && e.Number == 1062 {
			ctx.RespErrString(true, &errcode, "配置已存在，请勿重复添加！")
			return
		}
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TyjBroadcastController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID int32
		Page     int
		PageSize int
		Status   int32 // 状态（1开启 2关闭）
	}
	type Result struct {
		model.XTyjBroadcastConfig
		SellerName string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "语音播报设置", "查", "语音播报查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTyjBroadcastConfig
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}
	if reqdata.Status != 0 {
		db = db.Where(dao.Status.Eq(reqdata.Status))
	}
	var list []Result
	xSeller := server.DaoxHashGame().XSeller
	total, err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Select(dao.ALL, xSeller.SellerName).ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("list", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TyjBroadcastController) info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id       int32
		SellerID int32
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "语音播报设置", "查", "语音播报查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTyjBroadcastConfig
	db := dao.WithContext(ctx.Gin())
	xSeller := server.DaoxHashGame().XSeller
	var data struct {
		model.XTyjBroadcastConfig
		SellerName string
	}
	err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Where(dao.ID.Eq(reqdata.Id)).Select(dao.ALL, xSeller.SellerName).
		Scan(&data)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *TyjBroadcastController) update(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTyjBroadcastConfig
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "语音播报设置", "改", "修改语音播报")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	if bytes.Count([]byte(reqdata.Content), nil) >= 8000 {
		ctx.RespErr(errors.New("内容长度需小于8000字节"), &errcode)
		return
	}

	dao := server.DaoxHashGame().XTyjBroadcastConfig
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).
		Select(dao.Symbol, dao.Title, dao.Content, dao.Status).
		Updates(&reqdata.XTyjBroadcastConfig)
	if err != nil {
		if e, ok := err.(*mysql.MySQLError); ok && e.Number == 1062 {
			ctx.RespErrString(true, &errcode, "配置已存在，请勿重复添加！")
			return
		}
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TyjBroadcastController) update_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTyjBroadcastConfig
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "语音播报设置", "改", "修改语音播报状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTyjBroadcastConfig
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.Status, reqdata.Status)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TyjBroadcastController) delete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTyjBroadcastConfig
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "语音播报设置", "删", "删除语音播报")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTyjBroadcastConfig
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Delete()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TyjBroadcastController) broadcast(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId    int32           `validate:"required"`
		Symbol      string          `validate:"required"`
		UserId      int32           `validate:"required"`
		KefuAccount string          `validate:"required"`
		Amount      decimal.Decimal `validate:"required"`
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	daoUser := server.DaoxHashGame().XUser
	xUser, err := daoUser.WithContext(ctx.Gin()).Where(daoUser.UserID.Eq(reqdata.UserId)).First()
	if ctx.RespErr(err, &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTyjBroadcastConfig
	db := dao.WithContext(ctx.Gin())
	tb, err := db.Where(dao.SellerID.Eq(reqdata.SellerId)).Where(dao.Symbol.Eq(reqdata.Symbol)).
		Where(dao.Status.Eq(1)).First()
	if ctx.RespErr(err, &errcode) {
		return
	}

	msgData := struct {
		UserAccount string
		KefuAccount string
		Amount      string
		Symbol      string
	}{
		UserAccount: xUser.Account,
		KefuAccount: reqdata.KefuAccount,
		Amount:      reqdata.Amount.String(),
		Symbol:      reqdata.Symbol,
	}

	content := utils.TemplateToText("broadcast", tb.Content, msgData)

	wsData := make(map[string]interface{})
	wsData["title"] = tb.Title
	wsData["content"] = content
	server.GetWebSocketInstance().WriteTextMessage(&server.WsMsg{
		MsgType: "broadcast",
		Data:    wsData,
	})
	ctx.RespOK()
}
