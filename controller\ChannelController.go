package controller

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	xdao "xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/webclip"

	"github.com/beego/beego/logs"
	"github.com/go-sql-driver/mysql"

	"github.com/zhms/xgo/xgo"
	"gorm.io/gorm"
)

var channelNameMap map[int]string = make(map[int]string)

type ChannelController struct {
}

func (c *ChannelController) Init() {
	c.InitChannelNameMap()
	group := server.Http().NewGroup("/api/channel")
	{
		group.Post("/list", c.list)
		group.Post("/modify", c.modify)
		group.Post("/add", c.add)
		group.Post("/sync", c.sync)
		group.Post("/host/sync", c.hostSync)
		group.Post("/agentMode/batch/modify", c.agentModeBatchModify)

		group.Post("/agentMode/modify/logs", c.agentModeModifyLogs)

		group.Post("/list_url", c.list_url)
		group.Post("/modify_url", c.modify_url)
		group.Post("/delete_url", c.delete_url)
		group.Post("/add_url", c.add_url)
		group.Post("/reset_gamesort_new", c.resetGamesortNew)

		group.Post("/host_tag_list", c.host_tag_list)
		group.Post("/host_tag_creat", c.host_tag_creat)
		group.Post("/host_tag_update", c.host_tag_update)
		group.Post("/host_tag_delete", c.host_tag_delete)

		group.Post("/host_bind_tag", c.host_bind_tag)
		group.Post("/sport_target", c.getSportTarget)
		group.Post("/tg_guide", c.getTgGuide)

		// 新增域名用户保护批量操作接口
		group.Post("/batch_user_protection", c.batchUserProtection)
		// 新增渠道用户保护批量操作接口
		group.Post("/batch_channel_user_protection", c.batchChannelUserProtection)
		// 新增全部渠道用户保护批量操作接口
		group.Post("/batch_all_user_protection", c.batchAllUserProtection)
		// 新增批量设置默认支付接口
		group.Post("/batch_payment_method", c.batchPaymentMethod)

	}
}

func (c *ChannelController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		AgentMode int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	xChannelDao := server.DaoxHashGame().XChannel
	xSellerDao := server.DaoxHashGame().XSeller
	xChannelDb := xChannelDao.WithContext(nil)
	type Result struct {
		model.XChannel
		SellerName        string `json:"SellerName"`
		UserProtectStatus int32  `json:"UserProtectStatus"` // 新手保护状态：1=开启，2=关闭
	}

	var list []Result
	query := xChannelDb.Select(xChannelDao.ALL, xSellerDao.SellerName).LeftJoin(xSellerDao, xSellerDao.SellerID.EqCol(xChannelDao.SellerID))
	if reqdata.SellerId > 0 {
		query.Where(xChannelDao.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if token.ChannelId > 0 {
		query.Where(xChannelDao.ChannelID.Eq(int32(token.ChannelId)))
	}
	if reqdata.AgentMode > 0 {
		query.Where(xChannelDao.AgentMode.Eq(int32(reqdata.AgentMode)))
	}

	total, err := query.Order(xChannelDao.ChannelID.Desc()).ScanByPage(&list, offset, limit)
	if err != nil {
		return
	}

	// 为每个渠道计算新手保护状态
	for i := range list {
		// 查询该渠道下是否有任何域名开启了用户保护
		xChannelHostDao := server.DaoxHashGame().XChannelHost
		count, err := xChannelHostDao.WithContext(ctx.Gin()).
			Where(xChannelHostDao.ChannelID.Eq(list[i].ChannelID), xChannelHostDao.UserProtect.Eq(1)).
			Count()

		if err == nil && count > 0 {
			list[i].UserProtectStatus = 1 // 开启
		} else {
			list[i].UserProtectStatus = 2 // 关闭
		}
	}

	//where := abugo.AbuDbWhere{}
	//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	//total, data := server.Db().Table("x_channel").Where(where).OrderBy("ChannelId DESC").PageData(reqdata.Page, reqdata.PageSize)
	//

	ctx.Put("url", server.ImageUrl())
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}
func (c *ChannelController) modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId      int    `validate:"required"`
		ChannelId     int    `validate:"required"`
		ChannelName   string `validate:"required"`
		ShowName      string `validate:"required"`
		State         int    `validate:"required"`
		Icon          string
		Logo          string
		Remark        string
		ChatCompanyId string
		AgentMode     int32
		AgentCaseId   int32
		PaymentMethod int32 // 支付方式 1=虚拟币，2=法币
		//GoogleCode    string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "修改渠道")
	if token == nil {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	// 查询修改前的渠道信息，用于记录变更日志
	xChannelDao := server.DaoxHashGame().XChannel
	xChannelDb := xChannelDao.WithContext(nil)
	originalChannel, err := xChannelDb.Where(xChannelDao.ChannelID.Eq(int32(reqdata.ChannelId))).First()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	if reqdata.AgentMode == 0 {
		reqdata.AgentMode = 1
	}

	if reqdata.AgentMode == 1 {
		reqdata.AgentCaseId = 0
	}

	// 执行更新操作
	sql := "update x_channel set ChannelName = ?,State = ?,Remark = ?,Icon = ?,Logo = ? ,ShowName = ? ,ChatCompanyId = ?,AgentMode = ?,AgentCaseId = ?,PaymentMethod = ? where ChannelId = ?"
	server.Db().QueryNoResult(sql, reqdata.ChannelName, reqdata.State, reqdata.Remark, reqdata.Icon, reqdata.Logo, reqdata.ShowName, reqdata.ChatCompanyId, reqdata.AgentMode, reqdata.AgentCaseId, reqdata.PaymentMethod, reqdata.ChannelId)

	// 记录代理模式变更日志（仅当 AgentModel 或 AgentCaseId 或 State 发生变化时）
	if originalChannel.AgentMode != reqdata.AgentMode || originalChannel.AgentCaseID != reqdata.AgentCaseId || originalChannel.State != int32(reqdata.State) {
		xAgentModeLogDao := server.DaoxHashGame().XAgentModeLog
		xAgentModeLogDb := xAgentModeLogDao.WithContext(nil)

		agentModeLog := &model.XAgentModeLog{
			ChannelID:         int32(reqdata.ChannelId),
			OriginAgentMode:   originalChannel.AgentMode,
			NowAgentMode:      reqdata.AgentMode,
			OrginState:        originalChannel.State,
			NowState:          int32(reqdata.State),
			Remark:            reqdata.Remark,
			OriginAgentCaseID: originalChannel.AgentCaseID,
			NowAgentCaseID:    reqdata.AgentCaseId,
			Operator:          token.Account,
			CreateTime:        time.Now(),
		}

		err = xAgentModeLogDb.Create(agentModeLog)
		if err != nil {
			logs.Error("创建代理模式变更日志失败: %v", err)
		}
	}

	server.WriteAdminLog("修改渠道", ctx, reqdata)
	ctx.RespOK()
	c.InitChannelNameMap()
}

func (c *ChannelController) agentModeBatchModify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		ChannelIds  []int32 `validate:"required"`
		AgentMode   int     `validate:"required"`
		AgentCaseId int
		GoogleCode  string
	}

	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "修改渠道代理模式")
	if token == nil {
		return
	}

	if len(reqdata.ChannelIds) == 0 {
		ctx.RespErrString(true, &errcode, "请选择要修改的渠道")
		return
	}

	if reqdata.AgentMode == 0 {
		reqdata.AgentMode = 1
	}

	if reqdata.AgentMode == 1 {
		reqdata.AgentCaseId = 0
	}

	// 更新渠道代理模式 使用orm写法
	xChannelDao := server.DaoxHashGame().XChannel
	xChannelDb := xChannelDao.WithContext(nil)

	// 查询当前受影响渠道的原始代理模式和状态
	originalChannels, err := xChannelDb.Where(xChannelDao.ChannelID.In(reqdata.ChannelIds...)).Find()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	// 更新渠道代理模式
	_, err = xChannelDb.Where(xChannelDao.ChannelID.In(reqdata.ChannelIds...)).Updates(&model.XChannel{
		AgentMode:   int32(reqdata.AgentMode),
		AgentCaseID: int32(reqdata.AgentCaseId),
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 记录代理模式变更日志
	xAgentModeLogDao := server.DaoxHashGame().XAgentModeLog
	xAgentModeLogDb := xAgentModeLogDao.WithContext(nil)

	for _, channel := range originalChannels {
		// 发生变化时才记录日志
		if channel.AgentMode == int32(reqdata.AgentMode) && channel.AgentCaseID == int32(reqdata.AgentCaseId) {
			continue
		}

		agentModeLog := &model.XAgentModeLog{
			ChannelID:         channel.ChannelID,
			OriginAgentMode:   channel.AgentMode,
			NowAgentMode:      int32(reqdata.AgentMode),
			OrginState:        channel.State,
			NowState:          channel.State,
			Remark:            "批量修改渠道代理模式",
			OriginAgentCaseID: channel.AgentCaseID,
			NowAgentCaseID:    int32(reqdata.AgentCaseId),
			Operator:          token.Account,
			CreateTime:        time.Now(),
		}

		err = xAgentModeLogDb.Create(agentModeLog)
		if err != nil {
			logs.Error("创建代理模式变更日志失败: %v", err)
		}
	}

	server.WriteAdminLog("批量修改渠道代理模式", ctx, reqdata)
	ctx.RespOK()
}

func (c *ChannelController) sync(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		ChannelIds      []int `validate:"required"`
		OriginChannelId int   `validate:"required"`
		SyncType        []int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "修改渠道")
	if token == nil {
		return
	}
	// Convert []int to []string for strings.Join
	channelIdStrs := make([]string, len(reqdata.ChannelIds))
	for i, id := range reqdata.ChannelIds {
		channelIdStrs[i] = strconv.Itoa(id)
	}
	channelIds := strings.Join(channelIdStrs, ",")

	errStr := ""
	for _, id := range reqdata.SyncType {
		// 调用存储过程
		_, err := server.Db().CallProcedure("ChannelManage_x_base_data_UpdateSync", channelIds, reqdata.OriginChannelId, id)
		// 将error转换为字符串
		if err != nil {
			errStr += err.Error() + "\n"
		}

		log := fmt.Sprintf("同步渠道数据,源渠道:%v,目标渠道:%v,同步类型:%v", reqdata.OriginChannelId, channelIds, id)
		logs.Info(log)
		server.WriteAdminLog(log, ctx, reqdata)
	}

	if errStr != "" {
		ctx.RespErrString(true, &errcode, errStr)
		return
	}

	ctx.RespOK()
}

func (c *ChannelController) hostSync(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		HostIds        []int `validate:"required"`
		OriginChanneId int   `validate:"required"`
		OriginHostId   int   `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "同步域名")
	if token == nil {
		return
	}

	// Convert []int to []string for strings.Join
	hostIdStrs := make([]string, len(reqdata.HostIds))
	for i, id := range reqdata.HostIds {
		hostIdStrs[i] = strconv.Itoa(id)
	}
	hostIds := strings.Join(hostIdStrs, ",")

	errStr := ""
	// 调用存储过程
	_, err := server.Db().CallProcedure("ChannelManage_x_channel_host_UpdateGameSortEx", hostIds, reqdata.OriginChanneId, reqdata.OriginHostId)
	// 将error转换为字符串
	if err != nil {
		errStr += err.Error() + "\n"
	}

	if errStr != "" {
		ctx.RespErrString(true, &errcode, errStr)
		return
	}

	ctx.RespOK()
}

func (c *ChannelController) add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId      int    `validate:"required"`
		ChannelName   string `validate:"required"`
		ShowName      string `validate:"required"`
		Host          string
		State         int `validate:"required"`
		Icon          string
		Logo          string
		Remark        string
		ChatCompanyId string
		AgentMode     int32
		AgentCaseId   int32
		PaymentMethod int32 // 支付方式 1=虚拟币，2=法币
		//GoogleCode    string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "增", "查看渠道")
	if token == nil {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}
	rediskey := fmt.Sprintf("%v:%v:channel:add", server.Project(), server.Module())
	lck := server.Redis().SetNxString(rediskey, "1", 60)
	if lck != nil {
		ctx.RespErrString(true, &errcode, "正在添加中,请稍后再试")
		return
	}

	defer func() {
		server.Redis().Del(rediskey)
	}()

	// 特殊处理
	addressSellerId := reqdata.SellerId
	if reqdata.SellerId != 34 {
		addressSellerId = 0
	}

	games, _ := server.Db().Query("select * from x_game where (GameId < 100 or ( GameId > 300 and GameId < 350)) and ChannelId = ? and TopAgentId = 0", []interface{}{2})
	needaddresscount := int64(len(*games))
	addresscnt, _ := server.Db().Query("select count(id) as count from x_game_address_pool where SellerId = ? and State = 1 and AddType = 1", []interface{}{addressSellerId})
	if ctx.RespErrString(abugo.GetInt64FromInterface((*addresscnt)[0]["count"]) < needaddresscount, &errcode, "Tron地址池不足,请联系管理员") {
		return
	}

	bscAddresscnt, _ := server.Db().Query("select count(id) as count from x_game_address_pool where SellerId = ? and State = 1 and AddType = 2", []interface{}{addressSellerId})
	if ctx.RespErrString(abugo.GetInt64FromInterface((*bscAddresscnt)[0]["count"]) < needaddresscount, &errcode, "Bsc地址池不足,请联系管理员") {
		return
	}

	if reqdata.AgentMode == 0 {
		reqdata.AgentMode = 1
	}
	if reqdata.AgentMode == 1 {
		reqdata.AgentCaseId = 0
	}
	if reqdata.PaymentMethod == 0 {
		reqdata.PaymentMethod = 1 // 默认设置为虚拟币
	}

	nid, err := server.XDb().Table("x_channel").Insert(xgo.H{
		"SellerId":      reqdata.SellerId,
		"ChannelName":   reqdata.ChannelName,
		"State":         reqdata.State,
		"Remark":        reqdata.Remark,
		"Icon":          reqdata.Icon,
		"Logo":          reqdata.Logo,
		"ShowName":      reqdata.ShowName,
		"ChatCompanyId": reqdata.ChatCompanyId,
		"AgentMode":     reqdata.AgentMode,
		"AgentCaseId":   reqdata.AgentCaseId,
		"PaymentMethod": reqdata.PaymentMethod,
	})

	if err == nil {
		if reqdata.Host != "" {
			// 新增域名
			xChannelHostDao := server.DaoxHashGame().XChannelHost
			xChannelHostDb := xChannelHostDao.WithContext(nil)
			channelHost := &model.XChannelHost{
				Host:              reqdata.Host,
				ChannelID:         int32(nid),
				State:             1,
				SocialLinks:       `{"1":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"2":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"3":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"4":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"5":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"6":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"7":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"8":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"9":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"10":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"11":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"12":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"13":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"14":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""}}`,
				LoginRegisterType: `{"email":1,"phone":0,"account":1}`,
			}
			err := xChannelHostDb.Create(channelHost)
			if err != nil {
				ctx.RespErrString(true, &errcode, "添加失败,域名创建失败")
			}
		}

		games, _ = server.Db().Query("select * from x_game where ChannelId = ? and TopAgentId = 0", []interface{}{2})

		for i := 0; i < len(*games); i++ {
			g := (*games)[i]
			GAddress := abugo.GetStringFromInterface(g["Address"])
			BscAddress := abugo.GetStringFromInterface(g["BscAddress"])
			gameId := abugo.GetInt64FromInterface(g["GameId"])
			if (gameId >= 1 && gameId <= 13) || (gameId >= 301 && gameId <= 333) {
				addressdata, _ := server.Db().Query("select * from x_game_address_pool where SellerId = ? and AddType = 1 and State = 1 order by id asc limit 1", []interface{}{addressSellerId})
				bscAddressdata, _ := server.Db().Query("select * from x_game_address_pool where SellerId = ? and AddType = 2 and State = 1 order by id asc limit 1", []interface{}{addressSellerId})
				gid := abugo.GetInt64FromInterface((*addressdata)[0]["Id"])
				bscGid := abugo.GetInt64FromInterface((*bscAddressdata)[0]["Id"])
				// gameId already obtained above
				RoomLevel := abugo.GetInt64FromInterface(g["RoomLevel"])
				GAddress = abugo.GetStringFromInterface((*addressdata)[0]["Address"])
				BscAddress = abugo.GetStringFromInterface((*bscAddressdata)[0]["Address"])
				server.Db().Query("update x_game_address_pool set State = 2,GameId = ?,RoomLevel = ? ,AgentId = ?,ChannelId = ?,SellerId = ? where Id = ?", []interface{}{gameId, RoomLevel, 0, nid, reqdata.SellerId, gid})
				server.Db().Query("update x_game_address_pool set State = 2,GameId = ?,RoomLevel = ? ,AgentId = ?,ChannelId = ?,SellerId = ? where Id = ?", []interface{}{gameId, RoomLevel, 0, nid, reqdata.SellerId, bscGid})
			} else {
				GAddress = GAddress + fmt.Sprintf("_%v", nid)
				BscAddress = BscAddress + fmt.Sprintf("_%v", nid)
			}
			g["Address"] = GAddress
			g["BscAddress"] = BscAddress
			delete(g, "Id")
			g["TopAgentId"] = 0
			g["SellerId"] = reqdata.SellerId

			g["ChannelId"] = nid
			server.Db().Table("x_game").Insert(g)
		}
		cnofigs, _ := server.Db().Query("select * from x_config where ChannelId = 2", []interface{}{})
		for i := 0; i < len(*cnofigs); i++ {
			c := (*cnofigs)[i]
			delete(c, "Id")
			c["SellerId"] = reqdata.SellerId
			c["ChannelId"] = nid
			c["ConfigValue"] = ""
			// 设置筹码默认值
			if c["ConfigName"] == "GameChip" {
				c["ConfigValue"] = `[{"chip":1,"select":1},{"chip":5,"select":1},{"chip":10,"select":1},{"chip":50,"select":1},{"chip":100,"select":1},{"chip":200,"select":0},{"chip":500,"select":0},{"chip":1000,"select":0},{"chip":5000,"select":0},{"chip":10000,"select":0}]`
			}
			server.Db().Table("x_config").Insert(c)
		}
	}
	server.WriteAdminLog("添加渠道", ctx, reqdata)
	ctx.RespOK()
	c.InitChannelNameMap()
}

func (c *ChannelController) list_url(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int32
		ChannelId int32
		Host      string
		Page      int
		PageSize  int
	}
	type ResponseData struct {
		model.XChannelHost
		TagName     string `json:"TagName"`
		SellerName  string `json:"SellerName"`
		ChannelName string `json:"ChannelName"`
		AiSwitch    int    `json:"AiSwitch"`
		SellerId    int    `json:"SellerId"`
	}

	errcode := 0
	reqdata := RequestData{Page: 1, PageSize: 10000}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "查", "查看渠道域名")
	if token == nil {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	var list []ResponseData
	xChannelHost := server.DaoxHashGame().XChannelHost
	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	xHostTag := server.DaoxHashGame().XHostTag

	query := xChannelHost.WithContext(ctx.Gin()).
		Select(xChannelHost.ALL, xHostTag.TagName, xSeller.SellerID.As("SellerId"), xSeller.SellerName, xSeller.AiSwitch, xChannel.ChannelName).
		LeftJoin(xHostTag, xChannelHost.HostTagID.EqCol(xHostTag.ID)).
		LeftJoin(xChannel, xChannelHost.ChannelID.EqCol(xChannel.ChannelID)).
		LeftJoin(xSeller, xChannel.SellerID.EqCol(xSeller.SellerID)).
		Order(xChannelHost.ID.Desc())

	if reqdata.ChannelId != 0 {
		query.Where(xChannelHost.ChannelID.Eq(reqdata.ChannelId))
	}

	if reqdata.Host != "" {
		query.Where(xChannelHost.Host.Like("%" + reqdata.Host + "%"))
	}

	total, err := query.ScanByPage(&list, offset, limit)

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	for i, _ := range list {
		if list[i].Maidian == "" {
			list[i].Maidian = `{"FB":{"BackEnd":{"PIXEL_ID":"","ACCESS_TOKEN":""},"Front":{"PIXEL_ID":""}},"Twitter":{"BackEnd":{"PIXEL_ID":"","ACCESS_TOKEN":"","CompleteRegistration":"","Purchase":"","AddToCart":"","FirstRecharge":""},"Front":{"PIXEL_ID":"","CompleteRegistration":"","Purchase":"","AddToCart":"","FirstRecharge":""}},"Tiktok":{"BackEnd":{"PIXEL_ID":"","ACCESS_TOKEN":""},"Front":{"PIXEL_ID":""}},"Kwai":{"BackEnd":{"PIXEL_ID":"","ACCESS_TOKEN":"","IsTest":false},"Front":{"PIXEL_ID":"","IsTest":false}},"Bigo":{"BackEnd":{"PIXEL_ID":""},"Front":{"PIXEL_ID":""}}}`
		}

		if list[i].SportTarget == "" {
			list[i].SportTarget = `{"Brand":"","GameId":""}`
		}

		if list[i].TgAuthRobot == "" {
			list[i].TgAuthRobot = `{"AuthRobotName":"","AuthRobotToken":"","TgRobotGuideToken":""}`
		}

		if list[i].SocialLinks == "" {
			list[i].SocialLinks = `{"1":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"2":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"3":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"4":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"5":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"6":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"7":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"8":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"9":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"10":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"11":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"12":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"13":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""},"14":{"1":"","2":"","3":"","4":"","6":"","7":"","8":""}}`
		}

		if list[i].LoginRegisterType == "" {
			list[i].LoginRegisterType = `{"LoginType":1,"RegisterType":1,"LoginRegisterType":1}`
		}
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *ChannelController) add_url(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		ChannelId int    `validate:"required"`
		Host      string `validate:"required"`
		HostTagId int32
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "增", "添加渠道域名")
	if token == nil {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}
	reqdata.Host = strings.TrimSpace(reqdata.Host)

	channelHost := &model.XChannelHost{
		ChannelID:         int32(reqdata.ChannelId),
		Host:              reqdata.Host,
		State:             1,
		HostTagID:         reqdata.HostTagId,
		SocialLinks:       `{"1":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"2":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"3":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"4":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"5":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"6":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"7":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"8":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"9":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"10":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"11":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"12":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"13":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""},"14":{"1":"","2":"","3":"","4":"","5":"","6":"","7":"","8":""}}`,
		LoginRegisterType: `{"email":1,"phone":0,"account":1}`,
	}
	xChannelHost := server.DaoxHashGame().XChannelHost
	err := xChannelHost.WithContext(nil).Create(channelHost)
	//result, err := server.Db().Query("insert into x_channel_host(ChannelId,Host,State,HostTagId) values(?,?,1,?)", []interface{}{reqdata.ChannelId, reqdata.Host, reqdata.HostTagId})
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	channelHost, err = xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(reqdata.Host)).First()

	fmt.Println("channelHost:", channelHost.ID)
	err = webclip.GenerateWebConfig(0, channelHost.ID, server.DaoxHashGame())
	if err != nil {
		ctx.RespErrString(true, &errcode, "添加失败,webclip创建失败")
		return
	}

	server.Db().Query("update x_agent_independence set ChannelId = ? where Host = ?", []interface{}{reqdata.ChannelId, reqdata.Host})
	ctx.RespOK()
}

func (c *ChannelController) modify_url(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id          int `validate:"required"`
		State       int `validate:"required"`
		GameSort    string
		GameSortNew string
		GameSortEx  string
		AgentCode   string
		//GoogleCode         string
		IsBatch                int // 是否批量修改渠道下所有域名的游戏排序和开关 1:是 2:否
		ChannelId              int
		SellerId               int
		CustomServiceState     int
		WithdrawNeedActive     int
		CustomService          string
		TJ51Id                 string
		SocialLinks            string
		Maidian                string
		IsAIEnable             int32
		SportTarget            string
		IsTyjEnable            int32
		TgAuthRobot            string
		CountryList            string
		CountryListBatchUpdate int // 1:当前 2:当前渠道所有域名 3:所有域名
		LoginRegisterType      string
		LoginRegisterTypeBatch int // 1:当前 2:当前渠道所有域名 3:所有域名
		PwdVerificationType    int // 设置资金密码验证类型:1--邮箱 2 --手机号 3 -- (或关系) 两者满其一 4 -- (与关系)邮箱+手机号
		IsAutoDomainReg        int `json:"is_auto_domain_reg"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "修改渠道域名")
	if token == nil {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	if reqdata.CountryListBatchUpdate == 2 {
		server.Db().Query("update x_channel_host set CountryList = ? where ChannelId = ?",
			[]interface{}{reqdata.CountryList, reqdata.ChannelId})

		ctx.RespOK()
		return
	}

	if reqdata.CountryListBatchUpdate == 3 {
		server.Db().Query("update x_channel_host set CountryList = ?",
			[]interface{}{reqdata.CountryList})

		ctx.RespOK()
		return
	}

	if reqdata.LoginRegisterTypeBatch == 2 {
		server.Db().Query("update x_channel_host set LoginRegisterType = ? where ChannelId = ?",
			[]interface{}{reqdata.LoginRegisterType, reqdata.ChannelId})

		ctx.RespOK()
		return
	}

	if reqdata.LoginRegisterTypeBatch == 3 {
		server.Db().Query("update x_channel_host set LoginRegisterType = ?",
			[]interface{}{reqdata.LoginRegisterType})

		ctx.RespOK()
		return
	}

	if reqdata.IsBatch == 1 && reqdata.ChannelId != 0 {
		server.Db().Query("update x_channel_host set GameSort = ?,GameSortEx = ? ,GameSortNew = ? where ChannelId = ?",
			[]interface{}{reqdata.GameSort, reqdata.GameSortEx, reqdata.GameSortNew, reqdata.ChannelId})
	} else {
		xChannelHostDao := server.DaoxHashGame().XChannelHost
		xChannelHostDb := xChannelHostDao.WithContext(nil)
		query := xChannelHostDb.Where(xChannelHostDao.ID.Eq(int32(reqdata.Id)))
		updateData := map[string]interface{}{}

		if reqdata.State != 0 {
			updateData["State"] = reqdata.State
		}

		if reqdata.AgentCode != "" {
			agentCodeTb := server.DaoxHashGame().XAgentCode
			agentCodeDb := server.DaoxHashGame().XAgentCode.WithContext(context.Background())
			userTb := server.DaoxHashGame().XUser
			userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
			user, err := userDb.Select(userTb.SellerID, userTb.ChannelID).
				Join(agentCodeDb, agentCodeTb.UserID.EqCol(userTb.UserID)).
				Where(agentCodeTb.AgentCode.Eq(reqdata.AgentCode)).First()
			if err != nil {
				errmsg := "系统错误,请稍后再试"
				if errors.Is(err, gorm.ErrRecordNotFound) {
					errmsg = "邀请码不存在"
				}
				ctx.RespErr(errors.New(errmsg), &errcode)
				return
			}
			if reqdata.SellerId > 0 && int32(reqdata.SellerId) != user.SellerID {
				ctx.RespErrString(true, &errcode, "该用户不存在")
				return
			}
			if user.ChannelID != int32(reqdata.ChannelId) {
				ctx.RespErr(errors.New("邀请码渠道不正确"), &errcode)
				return
			}
			updateData["AgentCode"] = reqdata.AgentCode
		}
		if reqdata.GameSort != "" {
			updateData["GameSort"] = reqdata.GameSort
		}
		if reqdata.GameSortEx != "" {
			updateData["GameSortEx"] = reqdata.GameSortEx
		}
		if reqdata.GameSortNew != "" {
			updateData["GameSortNew"] = reqdata.GameSortNew
		}
		if reqdata.CustomServiceState != 0 {
			updateData["CustomServiceState"] = reqdata.CustomServiceState
		}
		if reqdata.CustomService != "" {
			updateData["CustomService"] = reqdata.CustomService
		}
		if reqdata.WithdrawNeedActive != 0 {
			updateData["WithdrawNeedActive"] = reqdata.WithdrawNeedActive
		}

		updateData["TJ51Id"] = reqdata.TJ51Id

		if reqdata.SocialLinks != "" {
			updateData["SocialLinks"] = reqdata.SocialLinks
		}
		if reqdata.Maidian != "" {
			updateData["Maidian"] = reqdata.Maidian
		}
		if reqdata.IsAIEnable != 0 {
			updateData["IsAIEnable"] = reqdata.IsAIEnable
		}

		if reqdata.SportTarget != "" {
			updateData["SportTarget"] = reqdata.SportTarget
		}

		if reqdata.IsTyjEnable != 0 {
			updateData["IsTyjEnable"] = reqdata.IsTyjEnable
		}

		if reqdata.TgAuthRobot != "" {
			updateData["TgAuthRobot"] = reqdata.TgAuthRobot
		}

		if reqdata.CountryList != "" {
			updateData["CountryList"] = reqdata.CountryList
		}

		if reqdata.LoginRegisterType != "" {
			updateData["LoginRegisterType"] = reqdata.LoginRegisterType
		}
		updateData["PwdVerificationType"] = reqdata.PwdVerificationType
		updateData["IsAutoDomainReg"] = reqdata.IsAutoDomainReg
		_, err := query.Updates(updateData)
		if err != nil {
			ctx.RespErr(errors.New("更新失败"), &errcode)
			return
		}
		//server.Db().Query("update x_channel_host set State = ?,GameSort = ?,GameSortEx = ?,GameSortNew = ? ,AgentCode = ? where id = ?", []interface{}{reqdata.State, reqdata.GameSort, reqdata.GameSortEx, reqdata.GameSortNew, reqdata.AgentCode, reqdata.Id})
	}
	ctx.RespOK()
}

func (c *ChannelController) delete_url(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id int `validate:"required"`
		//GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "删", "删除渠道域名")
	if token == nil {
		return
	}
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}
	server.Db().Query("delete from x_channel_host where id = ?", []interface{}{reqdata.Id})
	ctx.RespOK()
}

func (c *ChannelController) InitChannelNameMap() {
	where := abugo.AbuDbWhere{}
	data, err := server.Db().Table("x_channel").Where(where).GetList()
	if err != nil {
		return
	}
	if data != nil {
		for _, d := range *data {
			channelNameMap[int(abugo.GetInt64FromInterface(d["ChannelId"]))] = abugo.GetStringFromInterface(d["ChannelName"])
		}
	}
}

func ChannelName(channelId int) string {
	if name, ok := channelNameMap[channelId]; ok {
		return name
	}
	if channelId == 0 {
		return "全部"
	}
	// logs.Error("ChannelId=", channelId, " not exist")
	str := strconv.Itoa(channelId)
	return str
}

func InitSellerNameMap() (result map[int]string, err error) {
	sellerTb := server.DaoxHashGame().XSeller
	sellerDb := server.DaoxHashGame().XSeller.WithContext(context.Background())
	sellerList, err := sellerDb.Select(sellerTb.SellerID, sellerTb.SellerName).Find()
	if err != nil {
		return
	}
	result = make(map[int]string)
	for _, seller := range sellerList {
		result[int(seller.SellerID)] = seller.SellerName
	}
	return
}

func SellerIDName(SellerMap map[int]string, sellerID int) string {
	if name, ok := SellerMap[sellerID]; ok {
		return name
	}
	return ""
}

func (c *ChannelController) host_tag_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		model.XHostTag
		SellerIds []int32
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "查", "查询域名标签")
	if token == nil {
		return
	}
	if token.SellerId > 0 {
		reqdata.SellerID = int32(token.SellerId)
	}

	dao := server.DaoxHashGame().XHostTag
	db := dao.WithContext(ctx.Gin())

	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}

	if len(reqdata.SellerIds) > 0 {
		db = db.Where(dao.SellerID.In(reqdata.SellerIds...))
	}
	list, err := db.Find()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("list", list)
	ctx.RespOK()
}

func (c *ChannelController) host_tag_creat(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		model.XHostTag
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "增", "创建域名标签")
	if token == nil {
		return
	}
	if token.SellerId > 0 {
		reqdata.SellerID = int32(token.SellerId)
	}

	if reqdata.TagName == "" {
		ctx.RespErrString(true, &errcode, "请输入标签名称")
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErrString(true, &errcode, "请选择运营商")
		return
	}

	dao := server.DaoxHashGame().XHostTag
	db := dao.WithContext(ctx.Gin())

	err := db.Create(&reqdata.XHostTag)
	if err != nil {
		if e, ok := err.(*mysql.MySQLError); ok && e.Number == 1062 {
			ctx.RespErrString(true, &errcode, "标签已存在，请勿重复添加！")
			return
		}
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *ChannelController) host_tag_update(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		model.XHostTag
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "修改域名标签")
	if token == nil {
		return
	}
	if token.SellerId > 0 {
		reqdata.SellerID = int32(token.SellerId)
	}

	if reqdata.ID == 0 {
		ctx.RespErrString(true, &errcode, "请选择标签")
		return
	}
	if reqdata.TagName == "" {
		ctx.RespErrString(true, &errcode, "请输入标签名称")
		return
	}

	dao := server.DaoxHashGame().XHostTag
	db := dao.WithContext(ctx.Gin())

	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}

	info, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.TagName, reqdata.TagName)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}

func (c *ChannelController) host_tag_delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		model.XHostTag
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "删", "删除域名标签")
	if token == nil {
		return
	}
	if token.SellerId > 0 {
		reqdata.SellerID = int32(token.SellerId)
	}

	if reqdata.ID == 0 {
		ctx.RespErrString(true, &errcode, "请选择标签")
		return
	}

	if token.SellerId > 0 {
		xHostTag := server.DaoxHashGame().XHostTag
		tb, err := xHostTag.WithContext(ctx.Gin()).Where(xHostTag.ID.Eq(reqdata.ID)).First()
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		if tb.SellerID != int32(token.SellerId) {
			ctx.RespErrString(true, &errcode, "无效操作!")
			return
		}
	}

	err := server.DaoxHashGame().Transaction(func(tx *xdao.Query) error {
		_, err := tx.XHostTag.WithContext(ctx.Gin()).
			Where(tx.XHostTag.ID.Eq(reqdata.ID)).Delete()
		if err != nil {
			return err
		}
		_, err = tx.XChannelHost.WithContext(ctx.Gin()).
			Where(tx.XChannelHost.HostTagID.Eq(reqdata.ID)).
			Update(tx.XChannelHost.HostTagID, nil)
		return err
	})
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *ChannelController) host_bind_tag(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		HostIds   []int32
		HostTagId int32
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "批量绑定域名标签")
	if token == nil {
		return
	}

	if len(reqdata.HostIds) == 0 {
		ctx.RespErrString(true, &errcode, "请选择域名")
		return
	}
	if reqdata.HostTagId == 0 {
		ctx.RespErrString(true, &errcode, "请选择标签")
		return
	}

	xHostTag := server.DaoxHashGame().XHostTag
	tbHostTag, err := xHostTag.WithContext(ctx.Gin()).Where(xHostTag.ID.Eq(reqdata.HostTagId)).First()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	if token.SellerId > 0 && int32(token.SellerId) != tbHostTag.SellerID {
		ctx.RespErrString(true, &errcode, "无效操作!")
		return
	}

	xChannelHost := server.DaoxHashGame().XChannelHost
	info, err := xChannelHost.WithContext(ctx.Gin()).
		Where(xChannelHost.ID.In(reqdata.HostIds...)).
		Update(xChannelHost.HostTagID, reqdata.HostTagId)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}

func (c *ChannelController) getSportTarget(ctx *abugo.AbuHttpContent) {
	type Result struct {
		Brand  string
		GameId string
	}
	errcod := 0
	var results []Result
	xGameList := server.DaoxHashGame().XGameList
	err := xGameList.WithContext(nil).Select(xGameList.Brand, xGameList.GameID.As("GameId")).Where(xGameList.GameType.Eq(6)).Scan(&results)
	if err != nil {
		ctx.RespErr(err, &errcod)
		return
	}
	ctx.RespOK(results)
}

func (c *ChannelController) getTgGuide(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId    int32
		TgRobotType int32
	}
	errCode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "查", "查询TG教程")
	if token == nil {
		return
	}
	if token.SellerId > 0 {
		reqdata.SellerId = int32(token.SellerId)
	}

	if reqdata.SellerId == 0 {
		ctx.RespErrString(true, &errCode, "请选择运营商")
		return
	}

	type Result struct {
		Id          int32  `gorm:"column:Id" json:"Id"`
		SellerId    int32  `gorm:"column:SellerId" json:"SellerId"`
		TgRobotType int32  `gorm:"column:TgRobotType" json:"TgRobotType"`
		SubTitle    string `gorm:"column:SubTitle" json:"SubTitle"`
		Content     string `gorm:"column:Content" json:"Content"`
		PicUrls     string `gorm:"column:PicUrls" json:"PicUrls"`
		SortNum     int32  `gorm:"column:SortNum" json:"SortNum"`
		CreateTime  string `gorm:"column:CreateTime" json:"CreateTime"`
		UpdateTime  string `gorm:"column:UpdateTime" json:"UpdateTime"`
	}

	var result []Result
	query := server.Db().Gorm().Table("x_tg_tutorial").Where("SellerId = ?", reqdata.SellerId).Order("SortNum desc")

	if reqdata.TgRobotType > 0 {
		query = query.Where("TgRobotType = ?", reqdata.TgRobotType)
	}

	err := query.Find(&result).Error
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	ctx.Put("data", result)
	ctx.RespOK()
}

func (c *ChannelController) resetGamesortNew(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int32
	}
	errcode := 0
	reqdata := RequestData{}
	if err := ctx.RequestData(&reqdata); ctx.RespErr(err, &errcode) {
		return
	}

	xChannelHost := server.DaoxHashGame().XChannelHost
	query := xChannelHost.WithContext(nil)
	if reqdata.Id > 0 {
		query = query.Where(xChannelHost.ID.Eq(reqdata.Id))
	} else {
		query = query.Where(xChannelHost.GameSortNew.IsNotNull())
	}

	_, err := query.Update(xChannelHost.GameSortNew, nil)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

// agentModeModifyLogs 代理模式变更日志查询
func (c *ChannelController) agentModeModifyLogs(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		ChannelId int
		Page      int
		PageSize  int
	}
	errcode := 0
	reqdata := RequestData{Page: 1, PageSize: 15}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "系统管理", "渠道管理", "查"), &errcode, "权限不足") {
		return
	}

	// 权限检查：如果是运营商账号，只能查看自己的数据
	if token.SellerId > 0 && reqdata.SellerId != token.SellerId {
		ctx.RespErrString(true, &errcode, "运营商不正确")
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	// 定义返回结果结构
	type Result struct {
		model.XAgentModeLog
		SellerName  string `json:"SellerName"`  // 运营商名称
		ChannelName string `json:"ChannelName"` // 渠道名称
		ShowName    string `json:"ShowName"`    // 渠道显示名称
	}

	var list []Result
	xAgentModeLogDao := server.DaoxHashGame().XAgentModeLog
	xChannelDao := server.DaoxHashGame().XChannel
	xSellerDao := server.DaoxHashGame().XSeller

	query := xAgentModeLogDao.WithContext(nil).
		Select(xAgentModeLogDao.ALL, xChannelDao.ChannelName, xSellerDao.SellerName, xChannelDao.ShowName).
		LeftJoin(xChannelDao, xAgentModeLogDao.ChannelID.EqCol(xChannelDao.ChannelID)).
		LeftJoin(xSellerDao, xChannelDao.SellerID.EqCol(xSellerDao.SellerID)).
		Order(xAgentModeLogDao.CreateTime.Desc())

	// 添加查询条件
	if reqdata.SellerId > 0 {
		query = query.Where(xChannelDao.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if reqdata.ChannelId > 0 {
		query = query.Where(xAgentModeLogDao.ChannelID.Eq(int32(reqdata.ChannelId)))
	}
	if token.ChannelId > 0 {
		query = query.Where(xAgentModeLogDao.ChannelID.Eq(int32(token.ChannelId)))
	}

	total, err := query.ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// batchUserProtection 批量设置域名用户保护开关
func (c *ChannelController) batchUserProtection(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		HostIds     []int32 `json:"hostIds" validate:"required"`     // 域名ID列表
		UserProtect int32   `json:"userProtect" validate:"required"` // 用户保护开关：1=开启，2=关闭
	}
	errcode := 0
	reqdata := RequestData{}

	//if err := ctx.RequestData(&reqdata); ctx.RespErr(err, &errcode) {
	//	return
	//}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "增", "查看渠道")
	if token == nil {
		return
	}

	if len(reqdata.HostIds) == 0 {
		ctx.RespErrString(true, &errcode, "请选择要操作的域名")
		return
	}

	if reqdata.UserProtect != 1 && reqdata.UserProtect != 2 {
		ctx.RespErrString(true, &errcode, "用户保护开关值无效，请传入1(开启)或2(关闭)")
		return
	}

	xChannelHost := server.DaoxHashGame().XChannelHost
	info, err := xChannelHost.WithContext(ctx.Gin()).
		Where(xChannelHost.ID.In(reqdata.HostIds...)).
		Update(xChannelHost.UserProtect, reqdata.UserProtect)

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	operationText := "开启"
	if reqdata.UserProtect == 2 {
		operationText = "关闭"
	}

	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.Put("message", fmt.Sprintf("成功%s%d个域名的用户保护功能", operationText, info.RowsAffected))
	ctx.RespOK()
}

// batchChannelUserProtection 批量设置渠道用户保护开关
func (c *ChannelController) batchChannelUserProtection(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ChannelIds  []int32 `json:"channelIds" validate:"required"`  // 渠道ID列表
		UserProtect int32   `json:"userProtect" validate:"required"` // 用户保护开关：1=开启，2=关闭
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "批量设置渠道用户保护")
	if token == nil {
		return
	}

	if len(reqdata.ChannelIds) == 0 {
		ctx.RespErrString(true, &errcode, "请选择要操作的渠道")
		return
	}

	if reqdata.UserProtect != 1 && reqdata.UserProtect != 2 {
		ctx.RespErrString(true, &errcode, "用户保护开关值无效，请传入1(开启)或2(关闭)")
		return
	}

	// 更新指定渠道下的所有域名的用户保护开关
	xChannelHost := server.DaoxHashGame().XChannelHost
	info, err := xChannelHost.WithContext(ctx.Gin()).
		Where(xChannelHost.ChannelID.In(reqdata.ChannelIds...)).
		Update(xChannelHost.UserProtect, reqdata.UserProtect)

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	operationText := "开启"
	if reqdata.UserProtect == 2 {
		operationText = "关闭"
	}

	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.Put("message", fmt.Sprintf("成功%s%d个渠道下共%d个域名的用户保护功能", operationText, len(reqdata.ChannelIds), info.RowsAffected))
	ctx.RespOK()
}

// batchAllUserProtection 批量操作所有渠道的用户保护开关（全部开启或全部关闭）
func (c *ChannelController) batchAllUserProtection(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserProtect int32  `json:"userProtect" validate:"required"` // 用户保护开关：1=开启，2=关闭
		GoogleCode  string `json:"googleCode" validate:"required"`  // 谷歌验证码
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "批量操作所有渠道新手保护")
	if token == nil {
		return
	}

	if reqdata.UserProtect != 1 && reqdata.UserProtect != 2 {
		ctx.RespErrString(true, &errcode, "用户保护开关值无效，请传入1(开启)或2(关闭)")
		return
	}

	// 验证谷歌验证码
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	// 更新所有域名的用户保护开关
	xChannelHost := server.DaoxHashGame().XChannelHost
	info, err := xChannelHost.WithContext(ctx.Gin()).
		Update(xChannelHost.UserProtect, reqdata.UserProtect)

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	operationText := "开启"
	if reqdata.UserProtect == 2 {
		operationText = "关闭"
	}

	// 记录操作日志
	logMessage := fmt.Sprintf("批量%s所有渠道的用户保护功能，影响%d个域名", operationText, info.RowsAffected)
	server.WriteAdminLog(logMessage, ctx, reqdata)

	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.Put("message", fmt.Sprintf("成功%s所有渠道下共%d个域名的用户保护功能", operationText, info.RowsAffected))
	ctx.RespOK()
}

// batchPaymentMethod 批量设置默认支付方式
func (c *ChannelController) batchPaymentMethod(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ChannelIds    []int32 `json:"channelIds" validate:"required"`    // 渠道ID列表
		PaymentMethod int32   `json:"paymentMethod" validate:"required"` // 支付方式：1=虚拟币，2=法币
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "系统管理", "渠道管理", "改", "批量设置默认支付方式")
	if token == nil {
		return
	}

	if len(reqdata.ChannelIds) == 0 {
		ctx.RespErrString(true, &errcode, "请选择要操作的渠道")
		return
	}

	if reqdata.PaymentMethod != 1 && reqdata.PaymentMethod != 2 {
		ctx.RespErrString(true, &errcode, "支付方式值无效，请传入1(虚拟币)或2(法币)")
		return
	}

	xChannelDao := server.DaoxHashGame().XChannel
	xChannelDb := xChannelDao.WithContext(nil)

	_, err := xChannelDb.Where(xChannelDao.ChannelID.In(reqdata.ChannelIds...)).Updates(&model.XChannel{
		PaymentMethod: reqdata.PaymentMethod,
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	operationText := "虚拟币"
	if reqdata.PaymentMethod == 2 {
		operationText = "法币"
	}

	ctx.Put("RowsAffected", int64(len(reqdata.ChannelIds)))
	ctx.Put("message", fmt.Sprintf("成功批量设置%d个渠道的默认支付方式为%s", len(reqdata.ChannelIds), operationText))
	ctx.RespOK()
}
