// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXLoginLog = "x_login_log"

// XLoginLog mapped from table <x_login_log>
type XLoginLog struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID       int32     `gorm:"column:UserId;comment:玩家id" json:"UserId"`    // 玩家id
	SellerID     int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"` // 运营商
	IP           string    `gorm:"column:Ip" json:"Ip"`
	Lang         string    `gorm:"column:Lang;comment:登录语言" json:"Lang"`                 // 登录语言
	LoginTime    time.Time `gorm:"column:LoginTime;comment:登录时间" json:"LoginTime"`       // 登录时间
	RegisterIP   string    `gorm:"column:RegisterIp;comment:注册ip" json:"RegisterIp"`     // 注册ip
	RegisterTime time.Time `gorm:"column:RegisterTime;comment:注册时间" json:"RegisterTime"` // 注册时间
	Account      string    `gorm:"column:Account;comment:账号" json:"Account"`             // 账号
	ChannelID    int32     `gorm:"column:ChannelId;default:1" json:"ChannelId"`
	Address      string    `gorm:"column:Address" json:"Address"`
	DeviceID     string    `gorm:"column:DeviceId" json:"DeviceId"`
	DeviceType   string    `gorm:"column:DeviceType" json:"DeviceType"`
}

// TableName XLoginLog's table name
func (*XLoginLog) TableName() string {
	return TableNameXLoginLog
}
