// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRedpacketInviteCount = "x_tg_redpacket_invite_count"

// XTgRedpacketInviteCount mapped from table <x_tg_redpacket_invite_count>
type XTgRedpacketInviteCount struct {
	ID          int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	TgUserID    int64     `gorm:"column:TgUserId;not null;comment:Tg用户ID" json:"TgUserId"`     // Tg用户ID
	TgID        int32     `gorm:"column:TgId;not null;comment:Tg后台ID" json:"TgId"`             // Tg后台ID
	InviteCount int32     `gorm:"column:InviteCount;not null;comment:邀请次数" json:"InviteCount"` // 邀请次数
	CountDate   time.Time `gorm:"column:CountDate;comment:统计日期" json:"CountDate"`              // 统计日期
	CreateTime  time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime  time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
}

// TableName XTgRedpacketInviteCount's table name
func (*XTgRedpacketInviteCount) TableName() string {
	return TableNameXTgRedpacketInviteCount
}
