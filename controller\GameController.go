package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
	"gorm.io/gorm"
)

type GameController struct {
}

func (c *GameController) Init() {
	group := server.Http().NewGroup("/api/game")
	{
		group.Post("/list", c.list)
		group.Post("/add", c.add)
		group.Post("/modify", c.modify)
		group.Post("/state", c.state)
		group.Post("/tiyan", c.tiyan)
		group.Post("/set_jiangpei", c.set_jiangpei)
		group.Post("/set_jiangpei_state", c.set_jiangpei_state)
		group.Post("/get_jiangpei", c.get_jiangpei)
		group.Post("/get_stopjiangpei", c.get_stopjiangpei)
		group.Post("/set_stopjiangpei", c.set_stopjiangpei)
		group.Post("/third_list", c.third_list)
		group.Post("/third_modify", c.third_modify)
		group.Post("/country_list_with_brand", c.country_list_with_brand)
		group.Post("/third_modify_with_brand", c.third_modify_with_brand)
		group.Post("/third_add", c.third_add)
		group.Post("/third_del", c.third_del)
		group.Post("/sync_setting", c.sync_setting)
		group.Post("/batch", c.batch)
		group.Post("/agents", c.agents)

		group.Post("/get_jiangpei_templete", c.get_jiangpei_templete)
		group.Post("/add_jiangpei_templete", c.add_jiangpei_templete)
		group.Post("/modify_jiangpei_templete", c.modify_jiangpei_templete)
		group.Post("/delete_jiangpei_templete", c.delete_jiangpei_templete)

		group.Post("/templete_get_jiangpei", c.templete_get_jiangpei)
		group.Post("/templete_set_jiangpei", c.templete_set_jiangpei)
		group.Post("/templete_set_jiangpei_state", c.templete_set_jiangpei_state)

		group.Post("/game_use_jiangpei_templete", c.game_use_jiangpei_templete)
		group.Post("/user_use_jiangpei_templete", c.user_use_jiangpei_templete)
		group.Post("/agent_use_jiangpei_templete", c.agent_use_jiangpei_templete)
		group.Post("/guest_use_jiangpei_templete", c.guest_use_jiangpei_templete)

		group.Post("/get_peilv_templete", c.get_peilv_templete)
		group.Post("/add_peilv_templete", c.add_peilv_templete)
		group.Post("/modify_peilv_templete", c.modify_peilv_templete)
		group.Post("/delete_peilv_templete", c.delete_peilv_templete)

		group.Post("/user_use_peilv_templete", c.user_use_peilv_templete)
		group.Post("/guest_use_peilv_templete", c.guest_use_peilv_templete)

		group.Post("/game_type", c.game_type)

		group.Post("/profit_list", c.profit_list)
		group.Post("/profit_add", c.profit_add)
		group.Post("/profit_update", c.profit_update)

		// 修改前端排序方式
		group.Post("/sort_type", c.sort_type)
		group.Post("/modify_sort_type", c.modify_sort_type)

		// 更新在线人数
		group.Post("/update_online", c.update_online)

		group.Post("/chain_list", c.chain_list)
		group.Post("/chain_update_state", c.chain_update_state)
	}
}
func (c *GameController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Page       int
		PageSize   int
		GameType   int //-1转账玩法,-1余额玩法,-3一分哈希
		GameId     []int
		ChannelId  int
		RoomLevel  int
		TopAgentId []int
		Address    string
		BscAddress string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "游戏列表", "查", "查看游戏列表")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	if reqdata.GameType == -1 { //转账玩法
		where.Add("and", "GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12, 13, 301, 302, 303, 313, 323, 331, 332, 333)", 0)
	} else if reqdata.GameType == -2 { //余额玩法
		where.Add("and", "GameId", "in", "(101,102,103,104,105,106,116,126,131,132,133,134,135,136,201,202,203,204,205,206)", 0)
	}

	gameid := ""
	for i := 0; i < len(reqdata.GameId); i++ {
		gameid = gameid + fmt.Sprintf("%d,", reqdata.GameId[i])
	}

	if len(gameid) > 0 {
		gameid = gameid[0 : len(gameid)-1]
		gameid = gameid + ")"
		where.Add("and", "GameId", "in", fmt.Sprintf("(%s", gameid), nil)
	}

	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "RoomLevel", "=", reqdata.RoomLevel, 0)
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "BscAddress", "=", reqdata.BscAddress, "")

	topagentid := ""
	for i := 0; i < len(reqdata.TopAgentId); i++ {
		topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	}

	if len(topagentid) > 0 {
		topagentid = topagentid[0 : len(topagentid)-1]

		where.Add("and", "TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	}

	total, data := server.Db().Table("x_game").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *GameController) sync_setting(ctx *abugo.AbuHttpContent) {
	type GameData struct {
		Id         int
		SellerId   int
		ChannelId  int
		GameId     int
		RoomLevel  int
		TopAgentId int
		GameName   string
		Address    string
		BscAddress string
	}
	type RequestData struct {
		SellerId   int
		Src        GameData
		Dests      []GameData
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	retdata := []interface{}{}
	for i := 0; i < len(reqdata.Dests); i++ {
		if (reqdata.Src.GameId == 1 || reqdata.Src.GameId == 2 || reqdata.Src.GameId == 3 || reqdata.Src.GameId == 6 || reqdata.Src.GameId == 7 ||
			reqdata.Src.GameId == 11 || reqdata.Src.GameId == 12 || reqdata.Src.GameId == 13 || reqdata.Src.GameId == 101 || reqdata.Src.GameId == 102 ||
			reqdata.Src.GameId == 103 || reqdata.Src.GameId == 106 || reqdata.Src.GameId == 116 || reqdata.Src.GameId == 126 || reqdata.Src.GameId == 201 || reqdata.Src.GameId == 202 || reqdata.Src.GameId == 203 || reqdata.Src.GameId == 206) &&
			(reqdata.Dests[i].GameId == 4 || reqdata.Dests[i].GameId == 5 || reqdata.Dests[i].GameId == 104 || reqdata.Dests[i].GameId == 105 ||
				reqdata.Dests[i].GameId == 204 || reqdata.Dests[i].GameId == 205) {
			retdata = append(retdata, reqdata.Dests[i])
			continue
		}
		if (reqdata.Src.GameId == 4 || reqdata.Src.GameId == 104 || reqdata.Src.GameId == 204) && reqdata.Dests[i].GameId != 4 && reqdata.Dests[i].GameId != 104 && reqdata.Dests[i].GameId != 204 {
			retdata = append(retdata, reqdata.Dests[i])
			continue
		}
		if (reqdata.Src.GameId == 5 || reqdata.Src.GameId == 105 || reqdata.Src.GameId == 205) && reqdata.Dests[i].GameId != 5 && reqdata.Dests[i].GameId != 105 && reqdata.Dests[i].GameId != 205 {
			retdata = append(retdata, reqdata.Dests[i])
			continue
		}
		data, _ := server.XDb().Table("x_game").Where("SellerId = ? and ChannelId = ? and GameId = ? and RoomLevel = ? and TopAgentId = ?",
			reqdata.Src.SellerId, reqdata.Src.ChannelId, reqdata.Src.GameId, reqdata.Src.RoomLevel, reqdata.Src.TopAgentId).First()
		data.Delete("Id")
		data.Delete("SellerId")
		data.Delete("ChannelId")
		data.Delete("GameId")
		data.Delete("RoomLevel")
		data.Delete("GameName")
		data.Delete("TopAgentId")
		data.Delete("Address")
		data.Delete("BscAddress")
		data.Delete("AmountTrx")
		data.Delete("AmountUsdt")
		data.Delete("State")
		data.Delete("Chip")
		data.Delete("TiYan")
		data.Delete("Lottery")
		data.Delete("AgentName")
		data.Delete("RewardDownRole")
		data.Delete("StopRewardDownRole")
		server.XDb().Table("x_game").Where("SellerId = ? and ChannelId = ? and GameId = ? and RoomLevel = ? and TopAgentId = ?", reqdata.Dests[i].SellerId,
			reqdata.Dests[i].ChannelId, reqdata.Dests[i].GameId, reqdata.Dests[i].RoomLevel, reqdata.Dests[i].TopAgentId).Update(data.Map())
	}
	ctx.RespOK(retdata)
}

func (c *GameController) set_stopjiangpei(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId    int
		ChannelId   int `validate:"required"`
		BetInterval int
		LimitTrx    float64
		LimitUsdt   float64
		GameId      int `validate:"required"`
		//RoomLevel int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	data := make(map[string]interface{})
	data["BetInterval"] = reqdata.BetInterval
	data["LimitTrx"] = reqdata.LimitTrx
	data["LimitUsdt"] = reqdata.LimitUsdt
	sql := ""
	if reqdata.GameId > 0 && reqdata.GameId <= 5 || reqdata.GameId == 11 || reqdata.GameId == 12 || reqdata.GameId == 201 || reqdata.GameId == 202 {
		sql = "update x_game set StopRewardDownRole = ? where SellerId = ? and GameId in(1,2,3,4,5,11,12,201,202)"
	} else {
		sql = "update x_game set StopRewardDownRole = ? where SellerId = ? and GameId in(6,7,101,102,301,302,331,332,131,132)"
	}
	bdata, _ := json.Marshal(data)
	err = server.Db().QueryNoResult(sql, string(bdata), reqdata.SellerId)
	if err != nil {
		logs.Error(err)
	}
	ctx.RespOK()
	server.WriteAdminLog("设置停止降赔", ctx, reqdata)
}
func (c *GameController) get_stopjiangpei(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		ChannelId int `validate:"required"`
		GameId    int `validate:"required"`
		//RoomLevel int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	sql := "select StopRewardDownRole from x_game where SellerId = ? and GameId = ? limit 1"
	var StopRewardDownRole string
	server.Db().QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.GameId}, &StopRewardDownRole)
	if len(StopRewardDownRole) == 0 {
		StopRewardDownRole = "{}"
	}
	ctx.RespOK(StopRewardDownRole)
	server.WriteAdminLog("查询停止降赔", ctx, reqdata)

}
func (c *GameController) get_jiangpei(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		GameId     int `validate:"required"`
		ChannelId  int `validate:"required"`
		TopAgentId int
		AgentId    int
		UserId     int
		Address    string
		RoomLevel  int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.AgentId != 0 {
		sql := "select RewardDownRole from x_game_agent_jiangpei where UserId = ? and GameId = ? and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.AgentId, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		ctx.RespOK(RewardDownRole)
		return
	} else if reqdata.UserId != 0 {
		sql := "select RewardDownRole from x_game_user_jiangpei where UserId = ? and GameId = ? and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.UserId, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		ctx.RespOK(RewardDownRole)
		return
	} else if reqdata.Address != "" {
		sql := "select RewardDownRole from x_game_guest_jiangpei where ChannelId = ? and Address = ? and GameId = ? and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.ChannelId, reqdata.Address, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		ctx.RespOK(RewardDownRole)
		return
	}
	if reqdata.AgentId == 0 && reqdata.UserId == 0 && reqdata.Address == "" {
		sql := "select RewardDownRole from x_game where SellerId = ? and GameId = ? and ChannelId = ?  and RoomLevel = ? and TopAgentId = ? limit 1"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.GameId, reqdata.ChannelId, reqdata.RoomLevel, reqdata.TopAgentId}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		ctx.RespOK(RewardDownRole)
	}
	server.WriteAdminLog("查询降赔", ctx, reqdata)
}
func (c *GameController) set_jiangpei(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChannelId  int `validate:"required"`
		GameId     int `validate:"required"`
		TopAgentId int
		WanFa      string `validate:"required"`
		Data       string `validate:"required"` //
		AgentId    int
		UserId     int
		Address    string
		RoomLevel  int    `validate:"required"`
		Symbol     string `validate:"required"`
		Amount     int
		State      int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	type JDataType struct {
		WinCount int
		RateDown float32
	}
	jd := []JDataType{}
	json.Unmarshal([]byte(reqdata.Data), &jd)
	for i := 0; i < len(jd); i++ {
		if jd[i].WinCount < 0 {
			ctx.RespErrString(true, &errcode, "局数不能为负数")
			return
		}
		if jd[i].RateDown < 0.001 || jd[i].RateDown > 0.5 {
			ctx.RespErrString(true, &errcode, "降赔参数不正确[0.001,0.5]")
			return
		}
	}
	server.WriteAdminLog("设置降赔", ctx, reqdata)
	if reqdata.AgentId != 0 {
		sql := "insert ignore into x_game_agent_jiangpei(UserId,GameId,RoomLevel)values(?,?,?)"
		server.Db().QueryNoResult(sql, reqdata.AgentId, reqdata.GameId, reqdata.RoomLevel)
		sql = "select RewardDownRole from x_game_agent_jiangpei where UserId = ? and GameId = ?  and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.AgentId, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					if len(reqdata.WanFa) > 0 {
						jarr[i].(map[string]interface{})["data"].(map[string]interface{})[reqdata.WanFa] = reqdata.Data
					}
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{reqdata.WanFa: reqdata.Data}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game_agent_jiangpei set RewardDownRole = ? where UserId = ? and GameId = ?  and RoomLevel = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.AgentId, reqdata.GameId, reqdata.RoomLevel)
		ctx.RespOK()
		return
	} else if reqdata.UserId != 0 {
		sql := "insert ignore into x_game_user_jiangpei(UserId,GameId,RoomLevel)values(?,?,?)"
		server.Db().QueryNoResult(sql, reqdata.UserId, reqdata.GameId, reqdata.RoomLevel)
		sql = "select RewardDownRole from x_game_user_jiangpei where UserId = ? and GameId = ?  and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.UserId, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					if len(reqdata.WanFa) > 0 {
						jarr[i].(map[string]interface{})["data"].(map[string]interface{})[reqdata.WanFa] = reqdata.Data
					}
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{reqdata.WanFa: reqdata.Data}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game_user_jiangpei set RewardDownRole = ? where UserId = ? and GameId = ? and RoomLevel = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.UserId, reqdata.GameId, reqdata.RoomLevel)
		ctx.RespOK()
		return
	} else if reqdata.Address != "" {
		sql := "insert ignore into x_game_guest_jiangpei(ChannelId,Address,GameId,RoomLevel)values(?,?,?,?)"
		server.Db().QueryNoResult(sql, reqdata.ChannelId, reqdata.Address, reqdata.GameId, reqdata.RoomLevel)
		sql = "select RewardDownRole from x_game_guest_jiangpei where ChannelId = ? and Address = ? and GameId = ?  and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.ChannelId, reqdata.Address, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					if len(reqdata.WanFa) > 0 {
						jarr[i].(map[string]interface{})["data"].(map[string]interface{})[reqdata.WanFa] = reqdata.Data
					}
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{reqdata.WanFa: reqdata.Data}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game_guest_jiangpei set RewardDownRole = ? where ChannelId = ? and Address = ? and GameId = ? and RoomLevel = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.ChannelId, reqdata.Address, reqdata.GameId, reqdata.RoomLevel)
		ctx.RespOK()
		return
	}
	if reqdata.AgentId == 0 && reqdata.UserId == 0 && reqdata.Address == "" {
		sql := "select RewardDownRole from x_game where SellerId = ? and GameId = ?  and RoomLevel = ? and TopAgentId = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.GameId, reqdata.RoomLevel, reqdata.TopAgentId}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					if len(reqdata.WanFa) > 0 {
						jarr[i].(map[string]interface{})["data"].(map[string]interface{})[reqdata.WanFa] = reqdata.Data
					}
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{reqdata.WanFa: reqdata.Data}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game set RewardDownRole = ? where SellerId = ? and GameId = ? and RoomLevel = ? and TopAgentId = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.SellerId, reqdata.GameId, reqdata.RoomLevel, reqdata.TopAgentId)
		ctx.RespOK()
	}
}
func (c *GameController) set_jiangpei_state(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChannelId  int `validate:"required"`
		GameId     int `validate:"required"`
		TopAgentId int
		AgentId    int
		UserId     int
		Address    string
		RoomLevel  int    `validate:"required"`
		Symbol     string `validate:"required"`
		Amount     int
		State      int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.WriteAdminLog("设置降赔", ctx, reqdata)
	if reqdata.AgentId != 0 {
		sql := "insert ignore into x_game_agent_jiangpei(UserId,GameId,RoomLevel)values(?,?,?)"
		server.Db().QueryNoResult(sql, reqdata.AgentId, reqdata.GameId, reqdata.RoomLevel)
		sql = "select RewardDownRole from x_game_agent_jiangpei where UserId = ? and GameId = ?  and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.AgentId, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game_agent_jiangpei set RewardDownRole = ? where UserId = ? and GameId = ?  and RoomLevel = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.AgentId, reqdata.GameId, reqdata.RoomLevel)
		ctx.RespOK()
		return
	} else if reqdata.UserId != 0 {
		sql := "insert ignore into x_game_user_jiangpei(UserId,GameId,RoomLevel)values(?,?,?)"
		server.Db().QueryNoResult(sql, reqdata.UserId, reqdata.GameId, reqdata.RoomLevel)
		sql = "select RewardDownRole from x_game_user_jiangpei where UserId = ? and GameId = ?  and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.UserId, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game_user_jiangpei set RewardDownRole = ? where UserId = ? and GameId = ? and RoomLevel = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.UserId, reqdata.GameId, reqdata.RoomLevel)
		ctx.RespOK()
		return
	} else if reqdata.Address != "" {
		sql := "insert ignore into x_game_guest_jiangpei(ChannelId,Address,GameId,RoomLevel)values(?,?,?,?)"
		server.Db().QueryNoResult(sql, reqdata.ChannelId, reqdata.Address, reqdata.GameId, reqdata.RoomLevel)
		sql = "select RewardDownRole from x_game_guest_jiangpei where ChannelId = ? and Address = ? and GameId = ?  and RoomLevel = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.ChannelId, reqdata.Address, reqdata.GameId, reqdata.RoomLevel}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game_guest_jiangpei set RewardDownRole = ? where ChannelId = ? and Address = ? and GameId = ? and RoomLevel = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.ChannelId, reqdata.Address, reqdata.GameId, reqdata.RoomLevel)
		ctx.RespOK()
		return
	}
	if reqdata.AgentId == 0 && reqdata.UserId == 0 && reqdata.Address == "" {
		sql := "select RewardDownRole from x_game where SellerId = ? and GameId = ?  and RoomLevel = ? and TopAgentId = ?"
		var RewardDownRole string
		server.Db().QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.GameId, reqdata.RoomLevel, reqdata.TopAgentId}, &RewardDownRole)
		if len(RewardDownRole) == 0 {
			RewardDownRole = "{}"
		}
		jdata := make(map[string]interface{})
		json.Unmarshal([]byte(RewardDownRole), &jdata)
		if jdata[reqdata.Symbol] == nil {
			jdata[reqdata.Symbol] = []interface{}{}
		}
		jarr := jdata[reqdata.Symbol].([]interface{})
		if reqdata.State > 0 {
			added := false
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am == reqdata.Amount {
					added = true
					if jarr[i].(map[string]interface{})["data"] == nil {
						jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
					}
					jarr[i].(map[string]interface{})["state"] = reqdata.State
					break
				}
			}
			if !added && reqdata.State > 0 {
				jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{}}
				jarr = append(jarr, jd)
			}
			jdata[reqdata.Symbol] = jarr
		} else {
			jarr := jdata[reqdata.Symbol].([]interface{})
			jarrt := []interface{}{}
			for i := 0; i < len(jarr); i++ {
				am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
				if am != reqdata.Amount {
					jarrt = append(jarrt, jarr[i])
				}
			}
			jdata[reqdata.Symbol] = jarrt
		}
		bdata, _ := json.Marshal(&jdata)
		sql = "update x_game set RewardDownRole = ? where SellerId = ? and GameId = ? and RoomLevel = ? and TopAgentId = ?"
		server.Db().QueryNoResult(sql, string(bdata), reqdata.SellerId, reqdata.GameId, reqdata.RoomLevel, reqdata.TopAgentId)
		ctx.RespOK()
	}
}
func (c *GameController) add(ctx *abugo.AbuHttpContent) {
	defer recover()
	// type RequestData struct {
	// 	ChannelId          int    `validate:"required"`
	// 	SellerId int
	// 	GameId             int    `validate:"required"`
	// 	RoomLevel          int    `validate:"required"`
	// 	GameName           string `validate:"required"`
	// 	RoomName           string `validate:"required"`
	// 	Address            string `validate:"required"`
	// 	RewardRate         float64
	// 	RewardRateEx       string
	// 	UsdtLimitMin       int
	// 	UsdtLimitMax       int
	// 	TrxLimitMin        int
	// 	TrxLimitMax        int
	// 	BackFeeRate        float64
	// 	RewardDownRole     string
	// 	StopRewardDownRole string
	// 	AgentRate          float64
	// 	State              int
	// 	LiuSuiType         int
	// 	FenChengRate       float64
	// 	GoogleCode         string
	// }
	// errcode := 0
	// reqdata := RequestData{}
	// err := ctx.RequestData(&reqdata)
	// if ctx.RespErr(err, &errcode) {
	// 	return
	// }
	// token := server.GetToken(ctx)
	// if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "增"), &errcode, "权限不足") {
	// 	return
	// }
	// if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
	// 	return
	// }
	// if ctx.RespErrString(reqdata.FenChengRate < 0 || reqdata.FenChengRate > 0.045, &errcode, "分成比例超出范围:[0,0.045]") {
	// 	return
	// }
	// sql := "select State,GameId,RoomLevel from x_address where SellerId = ? and Address = ?"
	// var State int
	// var GameId int
	// var RoomLevel int
	// server.Db().QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.Address}, &State, &GameId, &RoomLevel)
	// if ctx.RespErrString(State == 0, &errcode, "地址不存在") {
	// 	return
	// }
	// if ctx.RespErrString(State == 2 && ((GameId != reqdata.GameId) || (RoomLevel != reqdata.RoomLevel)), &errcode, "地址已被使用,请更换地址重试") {
	// 	return
	// }
	// if reqdata.State == 0 {
	// 	reqdata.State = 1
	// }
	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }
	// var gameid int
	// sql = "select GameId from x_game where SellerId = ? and GameId = ? and RoomLevel = ? "
	// server.Db().QueryScan(sql, []interface{}{reqdata.SellerId, reqdata.GameId, reqdata.RoomLevel}, &gameid)
	// if ctx.RespErrString(gameid > 0, &errcode, "游戏已存在") {
	// 	return
	// }
	// sql = "update x_address set State = 2,GameId = ? , RoomLevel = ? where SellerId = ? and Address = ?"
	// server.Db().QueryNoResult(sql, reqdata.GameId, reqdata.RoomLevel, reqdata.SellerId, reqdata.Address)
	// sql = "insert into x_game(SellerId,GameId,RoomLevel,GameName,RoomName,Address,RewardRate,RewardRateEx,UsdtLimitMin,UsdtLimitMax,TrxLimitMin,TrxLimitMax,BackFeeRate,State,AgentRate,LiuSuiType)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	// server.Db().QueryNoResult(sql, reqdata.SellerId, reqdata.GameId, reqdata.RoomLevel, reqdata.GameName, reqdata.RoomName, reqdata.Address, reqdata.RewardRate, reqdata.RewardRateEx, reqdata.UsdtLimitMin, reqdata.UsdtLimitMax, reqdata.TrxLimitMin, reqdata.TrxLimitMax, reqdata.BackFeeRate, reqdata.State, reqdata.AgentRate, reqdata.LiuSuiType)
	// server.WriteAdminLog("新增游戏", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) modify(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id                 int `validate:"required"`
		SellerId           int
		RewardRateEx       string
		UsdtLimitMin       float64
		UsdtLimitMax       int
		UsdtBscLimitMin    float64
		UsdtBscLimitMax    int
		UsdtEthLimitMin    float64
		UsdtEthLimitMax    int
		TrxLimitMin        int
		TrxLimitMax        int
		BackFeeRate        float64
		RewardDownRole     string
		StopRewardDownRole string
		State              int
		LiuSuiType         int
		FenChengRate       float64
		FeeRate            float64
		GoogleCode         string
		SUsdtLimitMin      int
		SUsdtLimitMax      int
		STrxLimitMin       int
		STrxLimitMax       int
		PUsdtLimitMin      int
		PUsdtLimitMax      int
		PTrxLimitMin       int
		PTrxLimitMax       int
		IsVipGame          int
		UsdtLimitMinHe     int
		UsdtLimitMaxHe     int
		TrxLimitMinHe      int
		TrxLimitMaxHe      int
		IsNew              int
		IsHot              int
		IsRecom            int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.IsNew != 1 && reqdata.IsNew != 2 {
		reqdata.IsNew = 2
	}
	if reqdata.IsHot != 1 && reqdata.IsHot != 2 {
		reqdata.IsHot = 2
	}
	if reqdata.IsRecom != 1 && reqdata.IsRecom != 2 {
		reqdata.IsRecom = 2
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.FenChengRate < 0 || reqdata.FenChengRate > 0.045 {
		ctx.RespErrString(true, &errcode, "分成比例不正确[0, 0.045]")
		return
	}
	if reqdata.FeeRate < 0.001 || reqdata.FeeRate > 0.5 {
		ctx.RespErrString(true, &errcode, "手续费率不正确[0.001, 0.5]")
		return
	}
	if reqdata.BackFeeRate < 0.001 || reqdata.BackFeeRate > 0.5 {
		ctx.RespErrString(true, &errcode, "返还费率不正确[0.001, 0.5]")
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.Id, nil)
	gameinfo, _ := server.Db().Table("x_game").Where(where).GetOne()
	GameId := abugo.GetInt64FromInterface((*gameinfo)["GameId"])
	//[1] = "哈希大小" [2] = "哈希单双" 3 = "幸运哈希" [4] = "幸运庄闲" [5] = "哈希牛牛" [6] = "哈希快3" [7] = "哈希pk10" [11] = "和值大小" [12] = "和值单双"
	//[101] = "一分大小" [102] = "一分单双" [103] = "一分幸运" [104] = "一分庄闲" [105] = "一分牛牛" [201] = "余额大小" [202] = "余额单双" [203] = "幸运余额" [204] = "余额庄闲" [205] = "余额牛牛"
	if GameId == 5 || GameId == 105 || GameId == 205 { //检查牛牛游戏的费率范围是否合法
		type RewardRateEx struct {
			Beishu    []float64
			Feerate   []float64
			Hefeerate float64
		}
		if len(reqdata.RewardRateEx) > 0 {
			rre := RewardRateEx{}
			err = json.Unmarshal([]byte(reqdata.RewardRateEx), &rre)
			for i := 0; i < len(rre.Beishu); i++ {
				if rre.Beishu[i] < 0.001 || rre.Beishu[i] > float64(i+1) {
					ctx.RespErrString(true, &errcode, fmt.Sprintf("倍数不正确[0.001, %v]", i+1))
					return
				}
			}
			for i := 0; i < len(rre.Feerate); i++ {
				if rre.Feerate[i] < 0.001 || rre.Feerate[i] > 0.5 {
					ctx.RespErrString(true, &errcode, "手续费率不正确[0.001, 0.5]")
					return
				}
			}
			if rre.Hefeerate < 0.001 || rre.Hefeerate > 0.5 {
				ctx.RespErrString(true, &errcode, "和返还费率不正确[0.001, 0.5]")
				return
			}
		}
	}
	if GameId == 4 || GameId == 104 || GameId == 204 { //检查庄闲游戏的费率范围是否合法
		type RewardRateEx struct {
			Hebeishu      float64
			Wuxiaofeerate float64
			Hefeerate     float64
		}
		if len(reqdata.RewardRateEx) > 0 {
			rre := RewardRateEx{}
			err = json.Unmarshal([]byte(reqdata.RewardRateEx), &rre)
			if rre.Hebeishu < 0 || rre.Hebeishu > 8 {
				ctx.RespErrString(true, &errcode, "和倍数不正确[0, 8]")
				return
			}
			if rre.Wuxiaofeerate < 0.001 || rre.Wuxiaofeerate > 0.2 {
				ctx.RespErrString(true, &errcode, "无效下注返还费率不正确[0.001, 0.02]")
				return
			}
			if rre.Hefeerate < 0.001 || rre.Hefeerate > 0.8 {
				ctx.RespErrString(true, &errcode, "和返还费率不正确[0.001, 0.2]")
				return
			}
		}
	}
	if GameId == 4 || GameId == 104 || GameId == 204 { //检查庄闲游戏和的下注范围是否合法
		if GameId < 100 && (reqdata.TrxLimitMinHe < 1 || reqdata.TrxLimitMinHe > 500000) {
			ctx.RespErrString(true, &errcode, "和最小Trx不正确[1, 500000]")
			return
		}
		if GameId < 100 && (reqdata.TrxLimitMaxHe < 1 || reqdata.TrxLimitMaxHe > 500000) {
			ctx.RespErrString(true, &errcode, "和最大Trx不正确[1, 500000]")
			return
		}
		if reqdata.UsdtLimitMinHe < 0 || reqdata.UsdtLimitMinHe > 50000 {
			ctx.RespErrString(true, &errcode, "和最小Usdt不正确[0, 50000]")
			return
		}
		if reqdata.UsdtLimitMaxHe < 1 || reqdata.UsdtLimitMaxHe > 50000 {
			ctx.RespErrString(true, &errcode, "和最大Usdt不正确[1, 50000]")
			return
		}
	}
	if GameId < 100 { //只有转账玩法才检查trx的下注范围
		if reqdata.TrxLimitMin < 0 {
			ctx.RespErrString(true, &errcode, "最小Trx不正确[必须大于0]")
			return
		}
		if reqdata.TrxLimitMax < 0 {
			ctx.RespErrString(true, &errcode, "最大Trx不正确[必须大于0]")
			return
		}
	}
	{ //所有玩法都要检查usdt的下注范围
		if reqdata.UsdtLimitMin < 0 {
			ctx.RespErrString(true, &errcode, "最小Usdt不正确[必须大于0]")
			return
		}
		if reqdata.UsdtLimitMax < 0 {
			ctx.RespErrString(true, &errcode, "最大Usdt不正确[必须大于0]")
			return
		}
	}

	sql := "update x_game set FeeRate = ?,RewardRateEx = ?,UsdtLimitMin = ?,UsdtLimitMax = ?,UsdtBscLimitMin = ?,UsdtBscLimitMax = ?,UsdtEthLimitMin = ?,UsdtEthLimitMax = ?,TrxLimitMin = ?,TrxLimitMax = ?,BackFeeRate = ?,RewardDownRole = ?,StopRewardDownRole = ?,State = ?,FenChengRate = ?,LiuSuiType = ?,STrxLimitMin = ?,STrxLimitMax = ?,SUsdtLimitMin = ?,SUsdtLimitMax = ?,PTrxLimitMin = ?,PTrxLimitMax = ?,PUsdtLimitMin = ?,PUsdtLimitMax = ?,IsVipGame = ?,UsdtLimitMinHe = ?,UsdtLimitMaxHe = ?,TrxLimitMinHe = ?, TrxLimitMaxHe = ?,IsHot = ? ,IsNew = ?,IsRecom = ? where id = ?"
	server.Db().QueryNoResult(sql, reqdata.FeeRate, reqdata.RewardRateEx, reqdata.UsdtLimitMin, reqdata.UsdtLimitMax, reqdata.UsdtBscLimitMin, reqdata.UsdtBscLimitMax, reqdata.UsdtEthLimitMin, reqdata.UsdtEthLimitMax, reqdata.TrxLimitMin, reqdata.TrxLimitMax, reqdata.BackFeeRate, reqdata.RewardDownRole, reqdata.StopRewardDownRole, reqdata.State, reqdata.FenChengRate, reqdata.LiuSuiType, reqdata.STrxLimitMin, reqdata.STrxLimitMax, reqdata.SUsdtLimitMin, reqdata.SUsdtLimitMax, reqdata.PTrxLimitMin, reqdata.PTrxLimitMax, reqdata.PUsdtLimitMin, reqdata.PUsdtLimitMax, reqdata.IsVipGame, reqdata.UsdtLimitMinHe, reqdata.UsdtLimitMaxHe, reqdata.TrxLimitMinHe, reqdata.TrxLimitMaxHe, reqdata.IsHot, reqdata.IsNew, reqdata.IsRecom, reqdata.Id)
	ctx.RespOK()
	server.WriteAdminLog("修改游戏", ctx, reqdata)
}

func (c *GameController) state(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Id         int
		State      int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "update x_game set State = ? where id = ?"
	server.Db().QueryNoResult(sql, reqdata.State, reqdata.Id)
	server.WriteAdminLog("修改游戏", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) tiyan(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int
		TiYan      int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "update x_game set TiYan = ? where id = ?"
	server.Db().QueryNoResult(sql, reqdata.TiYan, reqdata.Id)
	server.WriteAdminLog("修改游戏", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) third_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		SellerId int
		Brand    string // pp,pg,gfg,xyx
		GameId   string
		GameName string
		GameType int // 1电子,2棋牌,3小游戏,4彩票
		Sort     struct {
			Field string
			Type  string
		}
		State int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "三方游戏", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	gameListTb := server.DaoxHashGame().XGameList
	gameListTbDb := server.DaoxHashGame().XGameList.WithContext(context.Background())

	if reqdata.Brand != "" {
		gameListTbDb = gameListTbDb.Where(gameListTb.Brand.Eq(reqdata.Brand))
	}
	if reqdata.GameId != "" {
		gameListTbDb = gameListTbDb.Where(gameListTb.GameID.Eq(reqdata.GameId))
	}
	if reqdata.GameName != "" {
		gameListTbDb = gameListTbDb.Where(gameListTb.Name.Like("%" + reqdata.GameName + "%"))
	}
	if reqdata.GameType > 0 {
		gameListTbDb = gameListTbDb.Where(gameListTb.GameType.Eq(int32(reqdata.GameType)))
	}

	if reqdata.State != 0 {
		gameListTbDb = gameListTbDb.Where(gameListTb.State.Eq(int32(reqdata.State)))
	}

	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)

	switch reqdata.Sort.Field {
	case "UserCount":
		if reqdata.Sort.Type == "asc" {
			gameListTbDb = gameListTbDb.Order(gameListTb.UserCount.Asc()).Order(gameListTb.ID.Desc())
		} else {
			gameListTbDb = gameListTbDb.Order(gameListTb.UserCount.Desc()).Order(gameListTb.ID.Desc())
		}
		break
	case "BetAmount":
		if reqdata.Sort.Type == "asc" {
			gameListTbDb = gameListTbDb.Order(gameListTb.BetAmount.Asc()).Order(gameListTb.ID.Desc())
		} else {
			gameListTbDb = gameListTbDb.Order(gameListTb.BetAmount.Desc()).Order(gameListTb.ID.Desc())
		}
		break
	case "Rtp":
		if reqdata.Sort.Type == "asc" {
			gameListTbDb = gameListTbDb.Order(gameListTb.Rtp.Asc()).Order(gameListTb.ID.Desc())
		} else {
			gameListTbDb = gameListTbDb.Order(gameListTb.Rtp.Desc()).Order(gameListTb.ID.Desc())
		}
	case "Sort":
		if reqdata.Sort.Type == "asc" {
			gameListTbDb = gameListTbDb.Order(gameListTb.Sort.Asc()).Order(gameListTb.ID.Desc())
		} else {
			gameListTbDb = gameListTbDb.Order(gameListTb.Sort.Desc()).Order(gameListTb.ID.Desc())
		}
	case "Id":
		if reqdata.Sort.Type == "asc" {
			gameListTbDb = gameListTbDb.Order(gameListTb.ID.Asc())
		} else {
			gameListTbDb = gameListTbDb.Order(gameListTb.ID.Desc())
		}
	default:
		{
			type SortTypeData struct {
				SortType       int32 `json:"sortType"`
				ArtificialType int32 `json:"artificialType"`
			}
			redisSortTypeData := server.Redis().HGet("CONFIG", "GAME_LIST_SORT_TYPE")
			sortTypeData := &SortTypeData{}
			if redisSortTypeData != nil {
				// redisData 是 []uint8 类型，将其转换为字符串
				dataBytes := redisSortTypeData.([]uint8)
				// 将字节数据转为字符串
				dataStr := string(dataBytes)
				json.Unmarshal([]byte(dataStr), &sortTypeData)
			}

			switch sortTypeData.SortType {
			case 1:
				if sortTypeData.ArtificialType == 1 {
					gameListTbDb = gameListTbDb.Order(gameListTb.UserCount.Desc()).Order(gameListTb.ID.Desc())
				} else {
					//query.OrderBy("Sort desc, UserCount desc")
					gameListTbDb = gameListTbDb.Order(gameListTb.Sort.Desc()).Order(gameListTb.UserCount.Desc()).Order(gameListTb.ID.Desc())
				}
			case 2:
				if sortTypeData.ArtificialType == 1 {
					gameListTbDb = gameListTbDb.Order(gameListTb.BetAmount.Desc()).Order(gameListTb.ID.Desc())
				} else {
					gameListTbDb = gameListTbDb.Order(gameListTb.Sort.Desc()).Order(gameListTb.BetAmount.Desc()).Order(gameListTb.ID.Desc())
				}
			case 3:
				if sortTypeData.ArtificialType == 1 {
					gameListTbDb = gameListTbDb.Order(gameListTb.Rtp.Desc()).Order(gameListTb.ID.Desc())
				} else {
					gameListTbDb = gameListTbDb.Order(gameListTb.Sort.Desc()).Order(gameListTb.Rtp.Desc()).Order(gameListTb.ID.Desc())
				}
			default:
				gameListTbDb = gameListTbDb.Order(gameListTb.Sort.Desc()).Order(gameListTb.ID.Desc())
			}

		}
	}

	result, count, err := gameListTbDb.FindByPage(offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("url", server.ImageUrl())
	ctx.Put("data", result)
	ctx.Put("total", count)
	ctx.RespOK()
	server.WriteAdminLog("查看三方游戏", ctx, reqdata)
}

// 根据厂商查询三方游戏支持的地区
func (c *GameController) country_list_with_brand(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		SellerId int
		GameType int32  `validate:"required"`
		Brand    string `validate:"required"`
	}{}
	errcode := 0
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方游戏", "查", "根据厂商查询三方游戏支持的地区")
	if token == nil {
		return
	}

	xGameBrand := server.DaoxHashGame().XGameBrand
	db := xGameBrand.WithContext(ctx.Gin())
	if reqdata.GameType > 0 {
		db = db.Where(xGameBrand.GameType.Eq(reqdata.GameType))
	}
	if reqdata.Brand != "" {
		db = db.Where(xGameBrand.Brand.Eq(reqdata.Brand))
	}
	tb, err := db.Select(xGameBrand.CountryList).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.Put("CountryList", tb.CountryList)
	ctx.RespOK()
}

// 根据厂商批量修改三方游戏
func (c *GameController) third_modify_with_brand(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		SellerId    int
		GameType    int32  `validate:"required"`
		Brand       string `validate:"required"`
		CountryList string // 支持的地区（二位字母国家代码，英文逗号分隔）
		GoogleCode  string
	}{}
	errcode := 0
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方游戏", "改", "根据厂商批量修改三方游戏")
	if token == nil {
		return
	}
	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }

	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		_, err := tx.XGameList.WithContext(ctx.Gin()).
			Where(tx.XGameList.GameType.Eq(reqdata.GameType)).
			Where(tx.XGameList.Brand.Eq(reqdata.Brand)).
			Update(tx.XGameList.CountryList, reqdata.CountryList)
		if err != nil {
			return err
		}
		_, err = tx.XGameBrand.WithContext(ctx.Gin()).
			Where(tx.XGameBrand.GameType.Eq(reqdata.GameType)).
			Where(tx.XGameBrand.Brand.Eq(reqdata.Brand)).
			Update(tx.XGameBrand.CountryList, reqdata.CountryList)
		return err
	})
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *GameController) third_modify(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		SellerId    int
		Ids         []int32
		Sort        int
		Hot         int
		New         int
		State       int
		Name        string
		EName       string
		Icon        string
		EIcon       string
		IsRecom     int
		CountryList string // 支持的地区（二位字母国家代码，英文逗号分隔）
		GoogleCode  string
		IsBatCh     bool // 是否批量编辑
		LiuSui      int
	}{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "三方游戏", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }
	reqdata.Name = strings.Trim(reqdata.Name, " ")
	reqdata.Name = strings.Trim(reqdata.Name, "\t")
	reqdata.Name = strings.Trim(reqdata.Name, "\r")
	reqdata.Name = strings.Trim(reqdata.Name, "\n")

	reqdata.EName = strings.Trim(reqdata.EName, " ")
	reqdata.EName = strings.Trim(reqdata.EName, "\t")
	reqdata.EName = strings.Trim(reqdata.EName, "\r")
	reqdata.EName = strings.Trim(reqdata.EName, "\n")

	data := make(map[string]interface{})
	if reqdata.Sort > 0 {
		data["Sort"] = reqdata.Sort
	}
	if reqdata.New > 0 {
		data["IsNew"] = reqdata.New
	}
	if reqdata.Hot > 0 {
		data["IsHot"] = reqdata.Hot
	}
	if reqdata.State > 0 {
		data["State"] = reqdata.State
	}
	if len(reqdata.Name) > 0 {
		data["Name"] = reqdata.Name
	}
	if len(reqdata.EName) > 0 {
		data["EName"] = reqdata.EName
	}
	if len(reqdata.Icon) > 0 {
		data["Icon"] = reqdata.Icon
	}
	if len(reqdata.EIcon) > 0 {
		data["EIcon"] = reqdata.EIcon
	}
	if reqdata.IsRecom > 0 {
		data["IsRecom"] = reqdata.IsRecom
	}
	if !reqdata.IsBatCh {
		data["CountryList"] = reqdata.CountryList
	}
	if reqdata.LiuSui > 0 {
		data["LiuSui"] = reqdata.LiuSui
	}
	xGameList := server.DaoxHashGame().XGameList
	info, err := xGameList.WithContext(ctx.Gin()).Where(xGameList.ID.In(reqdata.Ids...)).Updates(data)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
	server.WriteAdminLog("修改三方游戏", ctx, reqdata)
}

func (c *GameController) third_add(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId    int
		Brand       string `validate:"required"`
		GameId      string `validate:"required"`
		Name        string
		EName       string
		Sort        int
		Hot         int
		New         int
		State       int `validate:"required"`
		GameType    int //`validate:"required"`
		Icon        string
		EIcon       string
		IsRecom     int
		CountryList string // 支持的地区（二位字母国家代码，英文逗号分隔）
		GoogleCode  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	reqdata.EName = strings.Trim(reqdata.EName, " ")
	reqdata.EName = strings.Trim(reqdata.EName, "\t")
	reqdata.EName = strings.Trim(reqdata.EName, "\r")
	reqdata.EName = strings.Trim(reqdata.EName, "\n")

	reqdata.Name = strings.Trim(reqdata.Name, " ")
	reqdata.Name = strings.Trim(reqdata.Name, "\t")
	reqdata.Name = strings.Trim(reqdata.Name, "\r")
	reqdata.Name = strings.Trim(reqdata.Name, "\n")

	reqdata.GameId = strings.Trim(reqdata.GameId, " ")
	reqdata.GameId = strings.Trim(reqdata.GameId, "\t")
	reqdata.GameId = strings.Trim(reqdata.GameId, "\r")
	reqdata.GameId = strings.Trim(reqdata.GameId, "\n")

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "三方游戏", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }
	pdata := abugo.ObjectToMap(reqdata)
	(*pdata)["IsHot"] = (*pdata)["Hot"]
	(*pdata)["IsNew"] = (*pdata)["New"]
	delete(*pdata, "GoogleCode")
	delete(*pdata, "SellerId")
	delete(*pdata, "Hot")
	delete(*pdata, "New")
	_, err = server.Db().Table("x_game_list").Insert(*pdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, "游戏已存在")
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("新增三方游戏", ctx, reqdata)
}

func (c *GameController) third_del(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "三方游戏", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }
	server.Db().Conn().Exec("delete from x_game_list where Id = ? ", reqdata.Id)
	ctx.RespOK()
	server.WriteAdminLog("删除三方游戏", ctx, reqdata)
}

func (c *GameController) batch(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		State      int   `validate:"required"`
		Ids        []int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "三方游戏", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }
	if reqdata.State != 1 && reqdata.State != 2 {
		ctx.RespErrString(true, &errcode, "状态不正确")
		return
	}
	for i := 0; i < len(reqdata.Ids); i++ {
		server.Db().Conn().Exec("update x_game set State = ? where Id = ? ", reqdata.State, reqdata.Ids[i])
	}
	ctx.RespOK()
	server.WriteAdminLog("批量修改游戏", ctx, reqdata)
}

func (c *GameController) agents(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	db := server.XDb().Table("x_game")
	if reqdata.SellerId > 0 {
		db = db.Where("SellerId=?", reqdata.SellerId)
	}
	data, _ := db.Select("TopAgentId,AgentName").Find()
	rd := xgo.H{}
	data.ForEach(func(d *xgo.XMap) bool {
		rd[d.String("TopAgentId")] = d.String("AgentName")
		return true
	})
	ctx.RespOK(rd)
}

func (c *GameController) get_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	query := server.XDb().Table("x_jiangpei_template")
	if reqdata.SellerId > 0 {
		query = query.Where("SellerId = ?", reqdata.SellerId)
	}
	data, err := query.OrderBy("Id desc").Find()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK(data.Maps())
}

func (c *GameController) add_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Name       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	_, err = server.XDb().Table("x_jiangpei_template").Insert(xgo.H{"SellerId": reqdata.SellerId, "Name": reqdata.Name, "Data": "{}"})
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("添加降赔方案", ctx, reqdata)
}

func (c *GameController) delete_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Name       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	_, err = server.XDb().Table("x_jiangpei_template").Where("name = ?", reqdata.Name).Delete()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("删除降赔方案", ctx, reqdata)
}

func (c *GameController) modify_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int
		Name       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	_, err = server.XDb().Table("x_jiangpei_template").Where("Id = ?", reqdata.Id).Update(xgo.H{"Name": reqdata.Name})
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("修改降赔方案", ctx, reqdata)
}

func (c *GameController) templete_set_jiangpei(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int    `validate:"required"`
		Name       string `validate:"required"`
		GameId     int    `validate:"required"`
		WanFa      string `validate:"required"`
		Data       string `validate:"required"`
		Symbol     string `validate:"required"`
		Amount     int
		State      int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	type JDataType struct {
		WinCount int
		RateDown float32
	}
	jd := []JDataType{}
	json.Unmarshal([]byte(reqdata.Data), &jd)
	for i := 0; i < len(jd); i++ {
		if jd[i].WinCount < 0 {
			ctx.RespErrString(true, &errcode, "局数不能为负数")
			return
		}
		if jd[i].RateDown < 0.001 || jd[i].RateDown > 0.5 {
			ctx.RespErrString(true, &errcode, "降赔参数不正确[0.001,0.5]")
			return
		}
	}
	server.WriteAdminLog("设置降赔方案", ctx, reqdata)
	sql := "select Data from x_jiangpei_template where Id = ? and Name = ?"
	var Data string
	server.Db().QueryScan(sql, []interface{}{reqdata.Id, reqdata.Name}, &Data)
	if len(Data) == 0 {
		Data = "{}"
	}
	jdata := make(map[string]interface{})
	json.Unmarshal([]byte(Data), &jdata)
	_, ok := jdata[fmt.Sprint(reqdata.GameId)]
	if !ok {
		jdata[fmt.Sprint(reqdata.GameId)] = make(map[string]interface{})

	}
	jdataex := jdata[fmt.Sprint(reqdata.GameId)].(map[string]interface{})
	_, ok = jdataex[reqdata.Symbol]
	if !ok {
		jdataex[reqdata.Symbol] = []interface{}{}
	}
	jarr := jdataex[reqdata.Symbol].([]interface{})
	if reqdata.State > 0 {
		added := false
		for i := 0; i < len(jarr); i++ {
			am := int(xgo.ToInt(jarr[i].(map[string]interface{})["amount"]))
			if am == reqdata.Amount {
				added = true
				if jarr[i].(map[string]interface{})["data"] == nil {
					jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
				}
				jarr[i].(map[string]interface{})["state"] = reqdata.State
				if len(reqdata.WanFa) > 0 {
					jarr[i].(map[string]interface{})["data"].(map[string]interface{})[reqdata.WanFa] = reqdata.Data
				}
				break
			}
		}
		if !added && reqdata.State > 0 {
			jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{reqdata.WanFa: reqdata.Data}}
			jarr = append(jarr, jd)
		}
		jdataex[reqdata.Symbol] = jarr
	} else {
		jarr := jdataex[reqdata.Symbol].([]interface{})
		jarrt := []interface{}{}
		for i := 0; i < len(jarr); i++ {
			am := int(xgo.ToInt(jarr[i].(map[string]interface{})["amount"]))
			if am != reqdata.Amount {
				jarrt = append(jarrt, jarr[i])
			}
		}
		jdataex[reqdata.Symbol] = jarrt
	}
	bdata, _ := json.Marshal(&jdata)
	server.XDb().Table("x_jiangpei_template").Where("Id = ? and Name = ?", reqdata.Id, reqdata.Name).Update(xgo.H{"Data": string(bdata)})
	ctx.RespOK()
	server.WriteAdminLog("设置将配方案数据", ctx, reqdata)
}

func (c *GameController) templete_set_jiangpei_state(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int    `validate:"required"`
		Name       string `validate:"required"`
		GameId     int    `validate:"required"`
		Symbol     string `validate:"required"`
		Amount     int
		State      int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.WriteAdminLog("设置降赔方案", ctx, reqdata)
	sql := "select Data from x_jiangpei_template where Id = ? and Name = ?"
	var Data string
	server.Db().QueryScan(sql, []interface{}{reqdata.Id, reqdata.Name}, &Data)
	if len(Data) == 0 {
		Data = "{}"
	}
	jdata := make(map[string]interface{})
	json.Unmarshal([]byte(Data), &jdata)
	_, ok := jdata[fmt.Sprint(reqdata.GameId)]
	if !ok {
		jdata[fmt.Sprint(reqdata.GameId)] = make(map[string]interface{})
	}
	jdataex := jdata[fmt.Sprint(reqdata.GameId)].(map[string]interface{})
	_, ok = jdataex[reqdata.Symbol]
	if !ok {
		jdataex[reqdata.Symbol] = []interface{}{}
	}
	jarr := jdataex[reqdata.Symbol].([]interface{})
	if reqdata.State > 0 {
		added := false
		for i := 0; i < len(jarr); i++ {
			am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
			if am == reqdata.Amount {
				added = true
				if jarr[i].(map[string]interface{})["data"] == nil {
					jarr[i].(map[string]interface{})["data"] = map[string]interface{}{}
				}
				jarr[i].(map[string]interface{})["state"] = reqdata.State
				break
			}
		}
		if !added && reqdata.State > 0 {
			jd := gin.H{"amount": reqdata.Amount, "state": reqdata.State, "data": gin.H{}}
			jarr = append(jarr, jd)
		}
		jdataex[reqdata.Symbol] = jarr
	} else {
		jarr := jdataex[reqdata.Symbol].([]interface{})
		jarrt := []interface{}{}
		for i := 0; i < len(jarr); i++ {
			am := int(abugo.GetInt64FromInterface(jarr[i].(map[string]interface{})["amount"]))
			if am != reqdata.Amount {
				jarrt = append(jarrt, jarr[i])
			}
		}
		jdataex[reqdata.Symbol] = jarrt
	}
	bdata, _ := json.Marshal(&jdata)
	server.XDb().Table("x_jiangpei_template").Where("Id = ? and Name = ?", reqdata.Id, reqdata.Name).Update(xgo.H{"Data": string(bdata)})
	ctx.RespOK()
	server.WriteAdminLog("设置将配方案状态", ctx, reqdata)
}

func (c *GameController) templete_get_jiangpei(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Id       int    `validate:"required"`
		Name     string `validate:"required"`
		GameId   int    `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	sql := "select Data from x_jiangpei_template where Id = ? and Name = ?"
	var Data string
	server.Db().QueryScan(sql, []interface{}{reqdata.Id, reqdata.Name}, &Data)
	if len(Data) == 0 {
		Data = "{}"
	}
	jdata := make(map[string]interface{})
	json.Unmarshal([]byte(Data), &jdata)
	_, ok := jdata[fmt.Sprint(reqdata.GameId)]
	if !ok {
		jdata[fmt.Sprint(reqdata.GameId)] = make(map[string]interface{})
	}
	ctx.RespOK(jdata[fmt.Sprint(reqdata.GameId)])
}

func (c *GameController) game_use_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		TempleteId int
		Games      []int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tdata, _ := server.XDb().Table("x_jiangpei_template").Where("Id = ?", reqdata.TempleteId).First()
	if tdata == nil {
		if ctx.RespErrString(true, &errcode, "方案不存在") {
			return
		}
		return
	}

	reqbytes, _ := json.Marshal(reqdata)
	logs.Debug("游戏应用降赔方案:", string(reqbytes), tdata.String("Data"))

	jtdata := make(map[string]interface{})
	json.Unmarshal([]byte(tdata.String("Data")), &jtdata)
	for i := 0; i < len(reqdata.Games); i++ {
		gid := reqdata.Games[i]
		gdata, _ := server.XDb().Table("x_game").Where("Id = ?", gid).First()
		if gdata == nil {
			continue
		}
		GameId := gdata.Int("GameId")
		jgdata := jtdata[fmt.Sprint(GameId)]
		if jgdata == nil {
			jgdata = map[string]interface{}{}
		}
		bytes, _ := json.Marshal(jgdata)
		server.XDb().Table("x_game").Where("Id = ?", gid).Update(xgo.H{
			"RewardDownRole": string(bytes),
		})
	}
	ctx.RespOK()
	server.WriteAdminLog("游戏应用降赔方案", ctx, reqdata)
}

func (c *GameController) user_use_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		TempleteId int
		Users      []int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tdata, _ := server.XDb().Table("x_jiangpei_template").Where("Id = ?", reqdata.TempleteId).First()
	if tdata == nil {
		if ctx.RespErrString(true, &errcode, "方案不存在") {
			return
		}
		return
	}
	jtdata := make(map[string]interface{})
	json.Unmarshal([]byte(tdata.String("Data")), &jtdata)
	for i := 0; i < len(reqdata.Users); i++ {
		uid := reqdata.Users[i]
		server.XDb().Table("x_game_user_jiangpei").Where("UserId = ?", uid).Delete()
		for k, v := range jtdata {
			bytes, _ := json.Marshal(&v)
			server.XDb().Table("x_game_user_jiangpei").Insert(xgo.H{
				"UserId":         uid,
				"GameId":         k,
				"RoomLevel":      1,
				"RewardDownRole": string(bytes),
			})
			server.XDb().Table("x_game_user_jiangpei").Insert(xgo.H{
				"UserId":         uid,
				"GameId":         k,
				"RoomLevel":      2,
				"RewardDownRole": string(bytes),
			})
			server.XDb().Table("x_game_user_jiangpei").Insert(xgo.H{
				"UserId":         uid,
				"GameId":         k,
				"RoomLevel":      3,
				"RewardDownRole": string(bytes),
			})
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("玩家应用降赔方案", ctx, reqdata)
}

func (c *GameController) agent_use_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		TempleteId int
		Agents     []int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tdata, _ := server.XDb().Table("x_jiangpei_template").Where("Id = ?", reqdata.TempleteId).First()
	if tdata == nil {
		if ctx.RespErrString(true, &errcode, "方案不存在") {
			return
		}
		return
	}
	jtdata := make(map[string]interface{})
	json.Unmarshal([]byte(tdata.String("Data")), &jtdata)
	for i := 0; i < len(reqdata.Agents); i++ {
		uid := reqdata.Agents[i]
		server.XDb().Table("x_game_agent_jiangpei").Where("UserId = ?", uid).Delete()
		for k, v := range jtdata {
			bytes, _ := json.Marshal(&v)
			server.XDb().Table("x_game_agent_jiangpei").Insert(xgo.H{
				"UserId":         uid,
				"GameId":         k,
				"RoomLevel":      1,
				"RewardDownRole": string(bytes),
			})
			server.XDb().Table("x_game_agent_jiangpei").Insert(xgo.H{
				"UserId":         uid,
				"GameId":         k,
				"RoomLevel":      2,
				"RewardDownRole": string(bytes),
			})
			server.XDb().Table("x_game_agent_jiangpei").Insert(xgo.H{
				"UserId":         uid,
				"GameId":         k,
				"RoomLevel":      3,
				"RewardDownRole": string(bytes),
			})
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("代理应用降赔方案", ctx, reqdata)
}

func (c *GameController) guest_use_jiangpei_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		TempleteId int
		Address    []string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tdata, _ := server.XDb().Table("x_jiangpei_template").Where("Id = ?", reqdata.TempleteId).First()
	if tdata == nil {
		if ctx.RespErrString(true, &errcode, "方案不存在") {
			return
		}
		return
	}
	channels, _ := server.XDb().Table("x_channel").Where("SellerId = ?", reqdata.SellerId).Find()
	jtdata := make(map[string]interface{})
	json.Unmarshal([]byte(tdata.String("Data")), &jtdata)
	for i := 0; i < len(reqdata.Address); i++ {
		addr := reqdata.Address[i]
		server.XDb().Table("x_game_guest_jiangpei").Where("Address = ?", addr).Delete()
		for k, v := range jtdata {
			bytes, _ := json.Marshal(&v)
			channels.ForEach(func(d *xgo.XMap) bool {
				server.XDb().Table("x_game_guest_jiangpei").Insert(xgo.H{
					"ChannelId":      d.Int("ChannelId"),
					"Address":        addr,
					"GameId":         k,
					"RoomLevel":      1,
					"RewardDownRole": string(bytes),
				})
				server.XDb().Table("x_game_guest_jiangpei").Insert(xgo.H{
					"ChannelId":      d.Int("ChannelId"),
					"Address":        addr,
					"GameId":         k,
					"RoomLevel":      2,
					"RewardDownRole": string(bytes),
				})
				server.XDb().Table("x_game_guest_jiangpei").Insert(xgo.H{
					"ChannelId":      d.Int("ChannelId"),
					"Address":        addr,
					"GameId":         k,
					"RoomLevel":      3,
					"RewardDownRole": string(bytes),
				})
				return true
			})
		}
	}
	ctx.RespOK()
}

func (c *GameController) get_peilv_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	query := server.XDb().Table("x_gamefee_templete")
	if reqdata.SellerId > 0 {
		query = query.Where("SellerId = ?", reqdata.SellerId)
	}
	data, err := query.OrderBy("Id desc").Find()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK(data.Maps())
}

func (c *GameController) add_peilv_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Name       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.XDb().Table("x_gamefee_templete").Insert(xgo.H{"SellerId": reqdata.SellerId, "Name": reqdata.Name, "Data": "{}"})
	ctx.RespOK()
	server.WriteAdminLog("添加赔率方案", ctx, reqdata)
}

func (c *GameController) delete_peilv_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Name       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.XDb().Table("x_gamefee_templete").Where("name = ?", reqdata.Name).Delete()
	ctx.RespOK()
	server.WriteAdminLog("删除赔率方案", ctx, reqdata)
}

func (c *GameController) modify_peilv_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Id         int
		Name       string
		Data       string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "风控系统", "赔率降赔", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	isGameFeeValidStr := check_user_game_fee_valid(reqdata.Data)
	if ctx.RespErrString(isGameFeeValidStr != "", &errcode, isGameFeeValidStr) {
		return
	}
	_, err = server.XDb().Table("x_gamefee_templete").Where("Id = ?", reqdata.Id).Update(xgo.H{"Name": reqdata.Name, "Data": reqdata.Data})
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("修改赔率方案", ctx, reqdata)
}

func (c *GameController) user_use_peilv_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		TempleteId int
		Users      []int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tdata, _ := server.XDb().Table("x_gamefee_templete").Where("Id = ?", reqdata.TempleteId).First()
	if tdata == nil {
		if ctx.RespErrString(true, &errcode, "方案不存在") {
			return
		}
		return
	}

	jtdata := make(map[string]interface{})
	json.Unmarshal([]byte(tdata.String("Data")), &jtdata)
	reqbytes, _ := json.Marshal(reqdata)
	logs.Debug("游客应用赔率方案:", string(reqbytes), tdata.String("Data"))
	for i := 0; i < len(reqdata.Users); i++ {
		uid := reqdata.Users[i]
		server.XDb().Table("x_user").Where("UserId = ?", uid).Update(xgo.H{
			"GameFee": tdata.String("Data"),
		})
	}
	ctx.RespOK()
	server.WriteAdminLog("玩家应用赔率方案", ctx, reqdata)
}

func (c *GameController) guest_use_peilv_templete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		TempleteId int
		Address    []string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	tdata, _ := server.XDb().Table("x_gamefee_templete").Where("Id = ?", reqdata.TempleteId).First()
	if tdata == nil {
		if ctx.RespErrString(true, &errcode, "方案不存在") {
			return
		}
		return
	}
	reqbytes, _ := json.Marshal(reqdata)
	jtdata := make(map[string]interface{})
	json.Unmarshal([]byte(tdata.String("Data")), &jtdata)
	logs.Debug("游客应用赔率方案:", string(reqbytes), tdata.String("Data"))
	for i := 0; i < len(reqdata.Address); i++ {
		addr := reqdata.Address[i]
		server.XDb().Table("x_guest").Where("Address = ?", addr).Update(xgo.H{
			"GameFee": tdata.String("Data"),
		})
	}
	ctx.RespOK()
	server.WriteAdminLog("游客应用赔率方案", ctx, reqdata)
}

func (c *GameController) game_type(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	dictGametypeTb := server.DaoxHashGame().XDictGametype
	dictGametypeDb := server.DaoxHashGame().XDictGametype.WithContext(context.Background())
	list, err := dictGametypeDb.Where(dictGametypeTb.Status.Eq(1)).Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("list", list)
	server.WriteAdminLog("游戏场馆", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) profit_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		ChannelId []int32
		Status    int32
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "最近盈利管理", "查", "查看最近盈利管理列表")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	winlostConfigTb := server.DaoxHashGame().XTbWinlostConfig
	winlostConfigDb := server.DaoxHashGame().XTbWinlostConfig.WithContext(context.Background())
	if reqdata.SellerId > 0 {
		winlostConfigDb = winlostConfigDb.Where(winlostConfigTb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if reqdata.Status > 0 {
		winlostConfigDb = winlostConfigDb.Where(winlostConfigTb.Status.Eq(reqdata.Status))
	}
	if len(reqdata.ChannelId) > 0 {
		winlostConfigDb = winlostConfigDb.Where(winlostConfigTb.ChannelID.In(reqdata.ChannelId...))
	}
	limit := reqdata.PageSize
	offset := reqdata.PageSize * (reqdata.Page - 1)
	result, count, err := winlostConfigDb.Order(winlostConfigTb.CreateTime.Desc()).FindByPage(offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("data", result)
	ctx.Put("total", count)
	server.WriteAdminLog("最近盈利列表", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) profit_add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId        int    `validate:"required"`
		ChannelId       int32  `validate:"required"`
		GameType        string `validate:"required"`
		IntervalTime    int32
		DisplayRows     int32
		IsUserDistinct  int32 `validate:"oneof=1 2"`
		MinRewardAmount float64
		MinOnlineUsers  int32
		Memo            string
		Status          int32 `validate:"oneof=1 2"`
		GoogleCode      string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "最近盈利管理", "增", "添加最近盈利")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.IntervalTime == 0 {
		reqdata.IntervalTime = 30 * 60
	} else {
		reqdata.IntervalTime = reqdata.IntervalTime * 60
	}
	if reqdata.DisplayRows == 0 {
		reqdata.DisplayRows = 10
	}
	winlostConfigTb := server.DaoxHashGame().XTbWinlostConfig
	winlostConfigDb := server.DaoxHashGame().XTbWinlostConfig.WithContext(context.Background())
	winlostConfig, err := winlostConfigDb.Where(winlostConfigTb.ChannelID.Eq(reqdata.ChannelId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if winlostConfig != nil {
		ctx.RespErrString(true, &errcode, "渠道已存在")
		return
	}
	data := &model.XTbWinlostConfig{
		SellerID:        int32(reqdata.SellerId),
		ChannelID:       reqdata.ChannelId,
		GameType:        reqdata.GameType,
		IntervalTime:    reqdata.IntervalTime,
		DisplayRows:     reqdata.DisplayRows,
		IsUserDistinct:  reqdata.IsUserDistinct,
		MinRewardAmount: reqdata.MinRewardAmount,
		MinOnlineUsers:  reqdata.MinOnlineUsers,
		Memo:            reqdata.Memo,
		Status:          reqdata.Status,
	}
	err = winlostConfigDb.Create(data)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	server.WriteAdminLog("添加最近盈利", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) profit_update(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		ChannelId       int32  `validate:"required"`
		SellerId        int    `validate:"required"`
		GameType        string `validate:"required"`
		IntervalTime    int32
		DisplayRows     int32
		IsUserDistinct  int32 `validate:"oneof=1 2"`
		MinRewardAmount float64
		MinOnlineUsers  int32
		Memo            string
		Status          int32 `validate:"oneof=1 2"`
		GoogleCode      string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "最近盈利管理", "改", "修改最近盈利")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.IntervalTime == 0 {
		reqdata.IntervalTime = 30 * 60
	} else {
		reqdata.IntervalTime = reqdata.IntervalTime * 60
	}
	if reqdata.DisplayRows == 0 {
		reqdata.DisplayRows = 10
	}
	winlostConfigTb := server.DaoxHashGame().XTbWinlostConfig
	winlostConfigDb := server.DaoxHashGame().XTbWinlostConfig.WithContext(context.Background())
	data := make(map[string]any)
	data["GameType"] = reqdata.GameType
	data["IntervalTime"] = reqdata.IntervalTime
	data["DisplayRows"] = reqdata.DisplayRows
	data["IsUserDistinct"] = reqdata.IsUserDistinct
	data["MinRewardAmount"] = reqdata.MinRewardAmount
	data["MinOnlineUsers"] = reqdata.MinOnlineUsers
	data["Memo"] = reqdata.Memo
	data["Status"] = reqdata.Status
	_, err := winlostConfigDb.Where(winlostConfigTb.SellerID.Eq(int32(reqdata.SellerId))).
		Where(winlostConfigTb.ChannelID.Eq(reqdata.ChannelId)).Updates(data)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	server.WriteAdminLog("修改最近盈利", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) sort_type(ctx *abugo.AbuHttpContent) {
	type SortTypeData struct {
		SortType       int32 `json:"sortType"`
		ArtificialType int32 `json:"artificialType"`
	}

	redisData := server.Redis().HGet("CONFIG", "GAME_LIST_SORT_TYPE")
	sortTypeData := &SortTypeData{}
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)
		json.Unmarshal([]byte(dataStr), &sortTypeData)
	}

	ctx.RespOK(sortTypeData)
}

func (c *GameController) modify_sort_type(ctx *abugo.AbuHttpContent) {
	type SortTypeData struct {
		SortType       int32 `json:"sortType"`
		ArtificialType int32 `json:"artificialType"`
	}

	type ModifySortTypeRequestData struct {
		SortType       int32
		ArtificialType int32
		ResetSort      int32
		GoogleCode     string
	}

	reqdata := ModifySortTypeRequestData{}
	errcode := 0

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "三方游戏", "改", "修改三方游戏")
	if token == nil {
		return
	}

	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }

	sortTypeData := &SortTypeData{
		SortType:       reqdata.SortType,
		ArtificialType: reqdata.ArtificialType,
	}

	server.Redis().HSet("CONFIG", "GAME_LIST_SORT_TYPE", sortTypeData)

	if reqdata.ResetSort == 1 {
		dao := server.DaoxHashGame().XGameList
		db := dao.WithContext(nil)

		db.Where(dao.Sort.IsNotNull()).Update(dao.Sort, 0)
	}

	server.WriteAdminLog("修改三方游戏", ctx, sortTypeData)
	ctx.RespOK()

}

func (c *GameController) update_online(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id         int
		Type       int `validate:"oneof=1 2"`
		OnlineMin  int
		OnlineMax  int
		GoogleCode string
	}

	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "游戏管理", "游戏列表", "改", "修改游戏列表")
	if token == nil {
		return
	}
	// if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	// 	return
	// }

	if reqdata.OnlineMin > reqdata.OnlineMax {
		ctx.RespErrString(true, &errcode, "最小在线人数不能大于最大在线人数")
		return
	}

	if reqdata.Type == 1 {
		dao := server.DaoxHashGame().XGameList
		db := dao.WithContext(nil)

		if _, err := db.Where(dao.ID.Eq(int32(reqdata.Id))).Updates(map[string]any{
			"OnlineMin": reqdata.OnlineMin,
			"OnlineMax": reqdata.OnlineMax,
		}); err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
	}

	if reqdata.Type == 2 {
		dao := server.DaoxHashGame().XGame
		db := dao.WithContext(nil)

		// 获取id对应的游戏id
		game, err := db.Where(dao.ID.Eq(int32(reqdata.Id))).First()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}

		if _, err := db.Where(dao.GameID.Eq(game.GameID)).Updates(map[string]any{
			"OnlineMin": reqdata.OnlineMin,
			"OnlineMax": reqdata.OnlineMax,
		}); err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
	}

	server.WriteAdminLog("修改游戏列表在线人数", ctx, reqdata)
	ctx.RespOK()
}

func (c *GameController) chain_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	gameChainDb := server.DaoxHashGame().XGameChain.WithContext(ctx.Gin())
	gameChains, err := gameChainDb.Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.RespOK(gameChains)
}

func (c *GameController) chain_update_state(ctx *abugo.AbuHttpContent) {
	defer recover()
	type Item struct {
		ChainType int32
		State     int32
		TranState int32
	}
	type RequestData struct {
		SellerId   int
		ChainData  []Item `json:"ChainData"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if len(reqdata.ChainData) > 0 {
		updateData := make([]*model.XGameChain, 0, len(reqdata.ChainData))
		for _, chain := range reqdata.ChainData {
			if chain.ChainType > 0 && chain.State > 0 {
				updateData = append(updateData, &model.XGameChain{
					ChainType: chain.ChainType,
					State:     chain.State,
					TranState: chain.TranState,
				})
			}
		}
		if len(updateData) > 0 {
			gameChainTb := server.DaoxHashGame().XGameChain
			gameChainDb := server.DaoxHashGame().XGameChain.WithContext(ctx.Gin())
			err = gameChainDb.Omit(gameChainTb.Chain).Save(updateData...)
			if err != nil {
				logs.Error("chain_update_state save ameChain err: ", err)
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
				return
			}
		} else {
			ctx.RespErrString(true, &errcode, "参数错误")
			return
		}
	} else {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}
	ctx.RespOK()
}
