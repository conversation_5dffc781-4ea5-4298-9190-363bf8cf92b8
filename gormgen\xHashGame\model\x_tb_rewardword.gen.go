// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTbRewardword = "x_tb_rewardword"

// XTbRewardword 激励文字
type XTbRewardword struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Content    string    `gorm:"column:Content;comment:内容" json:"Content"`                                                           // 内容
	RewardType int32     `gorm:"column:RewardType;default:1;comment:激励分类 1中奖(系统) 2未中奖(系统)  11中奖(自定义)  21未中奖(自定义)" json:"RewardType"` // 激励分类 1中奖(系统) 2未中奖(系统)  11中奖(自定义)  21未中奖(自定义)
	Status     int32     `gorm:"column:Status;not null;default:1;comment:0无效 1有效" json:"Status"`                                     // 0无效 1有效
	Memo       string    `gorm:"column:Memo;comment:描述" json:"Memo"`                                                                 // 描述
	Operator   string    `gorm:"column:Operator;comment:操作员" json:"Operator"`                                                        // 操作员
	OperUserID int32     `gorm:"column:OperUserID;comment:操作员ID" json:"OperUserID"`                                                  // 操作员ID
	DeviceType int32     `gorm:"column:DeviceType;comment:设备类型" json:"DeviceType"`                                                   // 设备类型
	DeviceID   string    `gorm:"column:DeviceID;comment:设备ID" json:"DeviceID"`                                                       // 设备ID
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`                // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`                // 更新时间
}

// TableName XTbRewardword's table name
func (*XTbRewardword) TableName() string {
	return TableNameXTbRewardword
}
