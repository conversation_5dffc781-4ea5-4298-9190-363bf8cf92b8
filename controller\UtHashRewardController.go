package controller

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/gormgen/xHashGame/dao"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/imroc/req/v3"
	"github.com/spf13/viper"
)

// 签名密钥
const (
	SignKey = "kTEGAJ59zZABQ4dRInVcOC1og1vj06t1"
)

// generateSign 生成签名
func generateSign(params map[string]interface{}) string {
	var pairs []string
	for k, v := range params {
		if v == nil || v == "" || k == "sign" {
			continue
		}
		pairs = append(pairs, fmt.Sprintf("%s=%v", k, v))
	}

	sort.Strings(pairs)
	str := strings.Join(pairs, "&")
	str = str + "&key=" + SignKey

	hash := md5.New()
	hash.Write([]byte(str))
	return strings.ToLower(hex.EncodeToString(hash.Sum(nil)))
}

// parseTime 解析时间字符串
func parseTime(timeStr string) *time.Time {
	if timeStr == "" || timeStr == "0000-00-00 00:00:00" {
		return nil
	}
	t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		return nil
	}
	return &t
}

type UtHashRewardController struct{}

func (c *UtHashRewardController) Init() {
	group := server.Http().NewGroup("/api/ut/agent")
	{
		group.Post("/reward", c.reward)          // 获取奖励记录
		group.Post("/reward_details", c.details) // 获取奖励详情
		group.Post("/audit", c.audit)            // 结算审核
		// group.Post("/distribute", c.distributeReward) // 发放奖励
	}
}

// RewardResponse 奖励记录返回结构
type RewardResponse struct {
	TotalCount int64        `json:"totalCount"`
	Items      []RewardItem `json:"items"`
}

// RewardItem 奖励记录项
type RewardItem struct {
	Id                  int64   `json:"Id"`
	UserId              int32   `json:"UserId"`   // 代理ID，如 12342YYa
	NickName            string  `json:"NickName"` // 代理昵称，如 张三
	UserName            string  `json:"UserName"` // 用户名，如 张三
	DateInt             int     `json:"DateInt"`
	FlowRewardTodayTRX  float64 `json:"FlowRewardTodayTRX"`
	FlowRewardTodayUSDT float64 `json:"FlowRewardTodayUSDT"`
	FlowRewardTRX       float64 `json:"FlowRewardTRX"`
	FlowRewardUSDT      float64 `json:"FlowRewardUSDT"`
	RealRewardUSDT      float64 `json:"RealRewardUSDT"`
	TrxPrice            float64 `json:"TrxPrice"`
	IsReceived          int32   `json:"IsReceived"`
	State               int32   `json:"State"`
	CreatedAt           string  `json:"CreatedAt"`
	UpdatedBy           string  `json:"UpdatedBy"`
	ReceivedAt          string  `json:"ReceivedAt"`
	FinishedAt          string  `json:"FinishedAt"`
}

// reward 获取奖励记录（从三方接口获取）
func (c *UtHashRewardController) reward(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		Page                int      `json:"page"`
		PageSize            int      `json:"pageSize"`
		UserName            []string `json:"userName"`
		Id                  int32    `json:"id"`
		IsReceived          *int     `json:"isReceived"`
		State               *int     `json:"state"`
		FinishedAtStartTime string   `json:"finishedAtStartTime"`
		FinishedAtEndTime   string   `json:"finishedAtEndTime"`
		CreatedAtStartTime  string   `json:"createdAtStartTime"`
		CreatedAtEndTime    string   `json:"createdAtEndTime"`
		ReceivedAtStartTime string   `json:"receivedAtStartTime"`
		ReceivedAtEndTime   string   `json:"receivedAtEndTime"`
	}

	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "代理系统", "UT佣金审核", "查", "查佣金审核")
	if token == nil {
		return
	}

	// 构建请求参数
	params := map[string]interface{}{
		"page":     reqdata.Page,
		"pageSize": reqdata.PageSize,
	}

	// 添加可选参数
	if len(reqdata.UserName) > 0 {
		params["userName"] = strings.Join(reqdata.UserName, ",")
	}
	if reqdata.Id > 0 {
		params["id"] = reqdata.Id
	}
	if reqdata.IsReceived != nil {
		params["isReceived"] = *reqdata.IsReceived
	}
	if reqdata.State != nil {
		params["state"] = *reqdata.State
	}
	if reqdata.FinishedAtStartTime != "" {
		params["finishedAtStartTime"] = reqdata.FinishedAtStartTime
	}
	if reqdata.FinishedAtEndTime != "" {
		params["finishedAtEndTime"] = reqdata.FinishedAtEndTime
	}
	if reqdata.CreatedAtStartTime != "" {
		params["createdAtStartTime"] = reqdata.CreatedAtStartTime
	}
	if reqdata.CreatedAtEndTime != "" {
		params["createdAtEndTime"] = reqdata.CreatedAtEndTime
	}
	if reqdata.ReceivedAtStartTime != "" {
		params["receivedAtStartTime"] = reqdata.ReceivedAtStartTime
	}
	if reqdata.ReceivedAtEndTime != "" {
		params["receivedAtEndTime"] = reqdata.ReceivedAtEndTime
	}

	// 生成签名
	params["sign"] = generateSign(params)

	// 将params转换为string类型的map
	stringParams := make(map[string]string)
	for k, v := range params {
		if v != nil {
			stringParams[k] = fmt.Sprintf("%v", v)
		}
	}

	// 发送请求到三方接口
	url := viper.GetString("hash_admin_api") + "/hash/reward"
	client := req.C().EnableDebugLog()
	resp, err := client.R().
		SetQueryParams(stringParams).
		Get(url)

	if err != nil {
		logs.Error("请求失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("请求失败: %v", err))
		return
	}

	// 检查响应状态码
	if resp.StatusCode != 200 {
		logs.Error("请求失败，服务器返回错误: %v", resp.StatusCode)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("请求失败，服务器返回错误: %v", resp.StatusCode))
		return
	}

	var result struct {
		Code    int            `json:"code"`
		Message string         `json:"message"`
		Data    RewardResponse `json:"data"`
	}

	if err := json.Unmarshal(resp.Bytes(), &result); err != nil {
		logs.Error("解析响应失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("解析响应失败: %v", err))
		return
	}

	if result.Code != 0 {
		logs.Error("请求失败: %s", result.Message)
		ctx.RespErrString(true, &errcode, result.Message)
		return
	}

	// 获取所有用户名
	userNames := make([]string, 0)
	for _, item := range result.Data.Items {
		userNames = append(userNames, item.UserName)
	}

	// 查询用户信息
	db := server.Db().Gorm().LogMode(true)
	var users []struct {
		UserID   int32  `gorm:"column:UserId"`
		Account  string `gorm:"column:Account"`
		NickName string `gorm:"column:NickName"`
	}
	if err := db.Table("x_user").
		Select("UserId, Account, NickName").
		Where("Account IN (?)", userNames).
		Find(&users).Error; err != nil {
		logs.Error("查询用户信息失败: %v", err)
	}

	// 构建用户信息映射
	userMap := make(map[string]struct {
		UserID   int32
		NickName string
	})
	for _, user := range users {
		userMap[user.Account] = struct {
			UserID   int32
			NickName string
		}{
			UserID:   user.UserID,
			NickName: user.NickName,
		}
	}

	// 关联用户信息
	for i := range result.Data.Items {
		if userInfo, exists := userMap[result.Data.Items[i].UserName]; exists {
			result.Data.Items[i].UserId = userInfo.UserID
			result.Data.Items[i].NickName = userInfo.NickName
		}
	}

	ctx.Put("totalCount", result.Data.TotalCount)
	ctx.Put("items", result.Data.Items)
	ctx.RespOK()
}

// details 获取奖励详情
func (c *UtHashRewardController) details(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		Page      int    `json:"page"`
		PageSize  int    `json:"pageSize"`
		AgentName string `json:"agentName"`
		Date      string `json:"date"`
		Symbol    string `json:"symbol"`
	}

	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "代理系统", "UT佣金审核", "查", "查佣金审核")
	if token == nil {
		return
	}

	// 构建请求参数
	params := map[string]interface{}{
		"page":     reqdata.Page,
		"pageSize": reqdata.PageSize,
	}

	if reqdata.AgentName != "" {
		params["agentName"] = reqdata.AgentName
	}
	if reqdata.Date != "" {
		params["date"] = reqdata.Date
	}
	if reqdata.Symbol != "" {
		params["symbol"] = reqdata.Symbol
	}

	// 生成签名
	params["sign"] = generateSign(params)

	// 发送请求到三方接口
	url := viper.GetString("hash_admin_api") + "/hash/rewardDetails"

	// 将params转换为string类型的map
	stringParams := make(map[string]string)
	for k, v := range params {
		if v != nil {
			stringParams[k] = fmt.Sprintf("%v", v)
		}
	}

	client := req.C().EnableDebugLog()
	resp, err := client.R().
		SetQueryParams(stringParams).
		Get(url)

	if err != nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("请求失败: %v", err))
		return
	}

	// 检查响应状态码
	if resp.StatusCode != 200 {
		ctx.RespErrString(true, &errcode, "请求失败，服务器返回错误")
		return
	}

	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			TotalCount int64       `json:"totalCount"`
			Items      interface{} `json:"items"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.Bytes(), &result); err != nil {
		logs.Error("解析奖励详情响应失败: %v", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("解析响应失败: %v", err))
		return
	}

	if result.Code != 0 {
		logs.Error("获取奖励详情失败: %s", result.Message)
		ctx.RespErrString(true, &errcode, result.Message)
		return
	}

	ctx.Put("totalCount", result.Data.TotalCount)
	ctx.Put("items", result.Data.Items)
	ctx.RespOK()
}

type GroupData struct {
	Id             int64   `json:"id"`
	UserId         int32   `json:"userId"`
	RealRewardUSDT float64 `json:"realRewardUSDT"`
}

// distributeReward 发放奖励
func (c *UtHashRewardController) audit(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		Datas []GroupData `json:"datas"` // 支持多选，使用数组接收多个ID
	}

	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "代理系统", "UT佣金审核", "改", "佣金审核")
	if token == nil {
		return
	}

	if len(reqdata.Datas) == 0 {
		ctx.RespErrString(true, &errcode, "请选择需要发放的记录")
		return
	}

	for _, data := range reqdata.Datas {

		// 构建请求参数
		params := map[string]interface{}{
			"id":      data.Id,
			"AuditBy": token.Account,
		}

		// 生成签名
		params["sign"] = generateSign(params)

		// 发送请求到三方接口
		url := viper.GetString("hash_admin_api") + "/hash/audit"
		client := req.C().EnableDebugLog()
		resp, err := client.R().
			SetBody(params).
			Post(url)

		var errIds []int64
		if err != nil {
			errIds = append(errIds, data.Id)
		}

		var result struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		if err := json.Unmarshal(resp.Bytes(), &result); err != nil {
			ctx.RespErrString(true, &errcode, fmt.Sprintf("解析响应失败: %v", err))
			return
		}

		if result.Code != 0 {
			errIds = append(errIds, data.Id)
		}

		if len(errIds) > 0 {
			ctx.RespErrString(true, &errcode, fmt.Sprintf("发放失败的订单ID: %v", errIds))
			return
		}

		// 更新发放状态为成功
		err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
			_, err = tx.XUser.WithContext(nil).Where(tx.XUser.UserID.Eq(data.UserId)).First()

			activeAddUseBalancerInfo := struct {
				UserId            int32
				ActiveName        string
				RealAmount        float64
				WithdrawLiuSuiAdd float64
				BalanceCReason    int
			}{
				UserId:            int32(data.UserId),
				ActiveName:        "UT代理佣金发放",
				RealAmount:        data.RealRewardUSDT,
				WithdrawLiuSuiAdd: 0,
				BalanceCReason:    4,
			}
			err = db.ActiveAddUseBalancer(tx, activeAddUseBalancerInfo)
			if err != nil {
				return err
			}

			return nil
		})

	}

	ctx.RespOK()
}
