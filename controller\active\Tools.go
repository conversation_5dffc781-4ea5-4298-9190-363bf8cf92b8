// Package active 处理活动相关的工具函数
package active

import (
	"context"
	"encoding/json"
	"errors"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
)

// 活动ID常量定义
const (
	KActiveIdEachBetFanShui int = 1 // 每笔投注返水活动ID
	KActiveIdRechange       int = 2 // 充值活动ID
	KActiveIdHaXiBreakout   int = 3 // 哈希闯关活动ID
	KActiveIdQipaiBreakout  int = 4 // 棋牌闯关活动ID
	KActiveIdDianziBreakout int = 5 // 电子闯关活动ID
	KActiveIdRescue         int = 6 // 救援金活动ID
	KActiveIdInvite         int = 7 // 邀请活动ID
	KActiveIdVipFanShui     int = 8 // VIP返水活动ID
	KActiveIdXianglongfuhu  int = 9 // 降龙伏虎活动ID
)

// ValidateDelSerial 验证删除序列
// 确保只能删除最后一个序列号的记录
func ValidateDelSerial(level int, tabName, columnName string, where abugo.AbuDbWhere) error {
	data, err := server.Db().Table(tabName).Select(columnName).Where(where).OrderBy(columnName + " desc").GetOne()
	if err != nil {
		return err
	}
	if data == nil {
		return errors.New(columnName + " must be last one")
	}
	if l, ok := (*data)[columnName]; !ok {
		return errors.New(columnName + " must be last one")
	} else {
		if abugo.GetInt64FromInterface(l) == int64(level) {
			return nil
		} else {
			return errors.New(columnName + " must be last one")
		}
	}
}

// ValidateAddSerial 验证添加序列
// 确保添加的序列号是连续的
func ValidateAddSerial(level int, tabName, columnName string, where abugo.AbuDbWhere) error {
	data, err := server.Db().Table(tabName).Select(columnName).Where(where).OrderBy(columnName + " desc").GetOne()
	if err != nil {
		return err
	}
	if data == nil {
		if level == 1 {
			return nil
		} else {
			return errors.New(columnName + " must be serially")
		}
	}
	if l, ok := (*data)[columnName]; !ok {
		return errors.New(columnName + " must be serially")
	} else {
		if abugo.GetInt64FromInterface(l)+1 == int64(level) {
			return nil
		} else {
			return errors.New(columnName + " must be serially")
		}
	}
}

// SaveActiveSort 保存活动排序
// 为每种语言保存活动的排序信息
func SaveActiveSort(id, sort, topSort int32) error {
	if id == 0 {
		return errors.New("Id不能为空")
	}
	ctx := context.Background()
	xLangList := server.DaoxHashGame().XLangList
	langList, err := xLangList.WithContext(ctx).Find()
	if err != nil {
		return err
	}
	xActiveDefineSort := server.DaoxHashGame().XActiveDefineSort
	for _, tbLang := range langList {
		tbActiveSort := &model.XActiveDefineSort{ID: id, Lang: tbLang.ID, Sort: sort, TopSort: topSort}
		err = xActiveDefineSort.WithContext(ctx).Save(tbActiveSort)
		if err != nil {
			logs.Error("SaveActiveSort err:", err)
		}
	}
	return nil
}

// CumulativeWeeklyRechargeCheckParameter 累计周充值参数校验
func CumulativeWeeklyRechargeCheckParameter(reqdata DefineModReq) error {
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []CumulativeWeeklyRechargeConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.RechargeAmount < 0 || v.GiveAmount < 0 {
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// SignRewardCheckParameter 签到参数校验
func SignRewardCheckParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig SignRewardBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.MixBetLimit < 0 {
			return errors.New("有效下注金额不能小于0")
		}
		if baseConfig.TrxPrice < 0 {
			return errors.New("Trx价格不能小于0")
		}
		if baseConfig.RemakeDay < 1 {
			return errors.New("重置天数必须大于等于1")
		}
	}
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []SignRewardConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.SignDay < 1 || v.Award < 0 || v.AdditionalReward < 0 {
				return errors.New("参数错误")
			}
		}
	}

	// 验证彩金钱包相关配置
	if reqdata.GiftWallet == 2 { // 彩金钱包
		// 验证彩金钱包下的活动奖励配置数据
		if reqdata.AwardData != "" {
			var awardData SignRewardAwardData
			if err := json.Unmarshal([]byte(reqdata.AwardData), &awardData); err != nil {
				return errors.New("彩金钱包活动奖励配置格式错误")
			}

			// 验证奖励打码倍数类型（必须是彩金类型）
			if awardData.AwardBetType != 2 {
				return errors.New("彩金钱包活动的打码类型必须为（彩金）")
			}

			// 验证奖励配置
			if len(awardData.AwardConfig) == 0 {
				return errors.New("彩金钱包活动的奖励配置不能为空")
			}

			for _, v := range awardData.AwardConfig {
				if v.ID < 1 || v.SignDay < 1 || v.Award < 0 || v.AdditionalReward < 0 {
					return errors.New("彩金钱包活动的奖励配置参数错误")
				}
			}
		}
	}

	return nil
}

// RecommendFriendRewardCheckParameter 推荐好友多重奖励参数校验
func RecommendFriendRewardCheckParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig RecommendFriendRewardBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.RegisterDay < 1 {
			return errors.New("注册天数必须大于等于1")
		}
		if baseConfig.Level < 1 {
			return errors.New("vip级别必须大于等于1")
		}
		if baseConfig.FirstChargeUstdLimit < 0 {
			return errors.New("首次存款不能小于0")
		}
		if baseConfig.Award < 0 {
			return errors.New("单人奖励不能小于0")
		}
	}
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []RecommendFriendRewardConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.TotalMin < 0 || v.TotalMax < 0 || v.AdditionalReward < 0 {
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// WeekendBreakThroughConfigParameter 周末突破参数校验
func WeekendBreakThroughConfigParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig BreakThroughBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.TrxPrice < 0 {
			return errors.New("Trx价格不能小于0")
		}
	}
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []BreakThroughConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.LimitValue < 0 || v.RewardValue < 0 {
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// BreakThroughConfigParameter 今日突破参数校验
func BreakThroughConfigParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig TodayBreakThroughBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.TrxPrice < 0 {
			return errors.New("Trx价格不能小于0")
		}
		if baseConfig.RechargeAmount < 0 {
			return errors.New("充值金额不能小于0")
		}
	}
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []BreakThroughConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.LimitValue < 0 || v.RewardValue < 0 {
				return errors.New("参数错误")
			}
		}
	}

	// 验证彩金钱包相关配置
	if reqdata.GiftWallet == 2 { // 彩金钱包

		// 验证彩金钱包下的活动奖励配置数据
		if reqdata.AwardData != "" {
			var awardData BreakThroughAwardData
			if err := json.Unmarshal([]byte(reqdata.AwardData), &awardData); err != nil {
				return errors.New("彩金钱包活动奖励配置格式错误")
			}

			// 验证奖励打码倍数类型（必须是彩金类型）
			if awardData.AwardBetType != 2 {
				return errors.New("彩金钱包活动的打码类型必须为（彩金）")
			}
		}
	}

	return nil
}

// EnergyWhitelistParameter 能量白名单参数校验
func EnergyWhitelistParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig EnergyWhitelistBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.UstdLimit < 0 {
			return errors.New("单笔投注金额不能小于0")
		}
	}
	return nil
}

// NewFirstDepositCheckParameter 新首存活动180参数校验
func NewFirstDepositCheckParameter(reqdata DefineModReq) error {
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []NewFirstDepositConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			logs.Info("NewFirstDepositCheckParameter:%v", err)
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			logs.Info("NewFirstDepositCheckParameter:%v", config)
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.GiveProportion < 0 || v.GiveLimit < 0 || v.LiushuiMultiple <= 0 {
				logs.Info("NewFirstDepositCheckParameter:%v", v)
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// DailyRechargeRebateCheckParameter 每日充值洗码返利参数校验
func DailyRechargeRebateCheckParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig DailyRechargeRebateBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.RechargeMultiple < 0 {
			return errors.New("单笔投注金额不能小于0")
		}
		if baseConfig.FirstChargeProportion < 0 {
			return errors.New("首充金额返利不能小于0")
		}
		if baseConfig.GiveLimit < 0 {
			return errors.New("最高赠送不能小于0")
		}
	}
	return nil
}

// RecommendNewMemberGiftCheckParameter 推荐新会员，充值享豪礼参数校验
func RecommendNewMemberGiftCheckParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig RecommendNewMemberGiftBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.RedbagMember < 1 {
			return errors.New("红包个数不能小于1")
		}
		if baseConfig.RegisterDay < 1 {
			return errors.New("首充金额返利不能小于1")
		}
		if baseConfig.RechargeAmount < 0 {
			return errors.New("充值金额不能小于0")
		}
		if baseConfig.FinishMultiple < 0 {
			return errors.New("完成流水倍数不能小于0")
		}
		if baseConfig.RedbagAmountMin < 0 {
			return errors.New("红包金额最小值不能小于0")
		}
		if baseConfig.RedbagAmountMax < 0 {
			return errors.New("红包金额最大值不能小于0")
		}
	}
	return nil
}

// LuckyDiceBaseCheckParameter 幸运骰子参数校验
func LuckyDiceBaseCheckParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig []LuckyDiceBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(baseConfig) == 0 {
			return errors.New("基础参数需要上传")
		}
		for _, v := range baseConfig {
			if v.ID < 1 || v.Number < 1 || v.Award < 0 {
				return errors.New("参数错误")
			}
		}
	}
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []LuckyDiceConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.LiushuiMin < 0 || v.LiushuiMax < 0 || v.AwardDice < 1 {
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// BoomingRewardCheckParameter 爆庄奖励参数校验
func BoomingRewardCheckParameter(reqdata DefineModReq) error {
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []BoomingRewardConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.SingleDayExplosion < 0 || v.Gift == "" || v.Value < 0 {
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// PointGiftCheckParameter 积分兑豪礼参数校验
func PointGiftCheckParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig PointGiftBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if baseConfig.UstdLimit < 0 {
			return errors.New("有效投注不能小于0")
		}
		if baseConfig.Point < 1 {
			return errors.New("积分不能小于1")
		}
	}
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []PointGiftConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.Gift == "" || v.Value < 0 || v.Point < 1 {
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// WeeklySignActiveRewardCheckParameter 周签到活跃奖励参数校验
func WeeklySignActiveRewardCheckParameter(reqdata DefineModReq) error {
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig WeeklySignActiveRewardBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
		if len(baseConfig.LiushuiMultiple) == 0 {
			return errors.New("每日流水达标不能为空")
		}
	}
	if reqdata.Config == "" {
		return errors.New("参数需要配置")
	} else {
		var config []WeeklySignActiveRewardConfig
		err := json.Unmarshal([]byte(reqdata.Config), &config)
		if err != nil {
			return errors.New("参数格式错误")
		}
		if len(config) == 0 {
			return errors.New("参数需要上传")
		}
		for _, v := range config {
			if v.ID < 1 || v.Number < 1 || v.Award < 0 {
				return errors.New("参数错误")
			}
		}
	}
	return nil
}

// UpdateCiRiSongActive 更新次日送活动配置
// 更新活动的基本信息和配置参数
func UpdateCiRiSongActive(req DefineModReq, _ time.Time) error {
	if req.CiRiConfig == nil ||
		req.CiRiConfig.MinAwardLiuShui <= 0.1 {
		return errors.New("参数错误")
	}
	//交换数值之后
	updates := RequestToUpdates(req)
	var cStr string = ""
	b, err := json.Marshal(*req.CiRiConfig)
	if err == nil {
		cStr = string(b)
	}

	if cStr != "" {
		updates["Config"] = cStr
	}
	query := server.Db().Gorm().Table(utils.TableActiveDefine).Select("*").
		Where("ChannelId = ? and ActiveId = ? ", req.ChannelId, req.ActiveId)
	if req.SellerId > 0 {
		query = query.Where("SellerId = ?", req.SellerId)
	}
	err = query.Updates(updates).Error
	if err != nil {
		return err
	}
	err = SaveActiveSort(int32(req.Id), int32(req.Sort), int32(req.TopSort))

	return err
}

// RequestToUpdates 将请求数据转换为更新字段映射
// 将DefineModReq结构体转换为map用于数据库更新
func RequestToUpdates(req DefineModReq) map[string]interface{} {
	return gin.H{
		"Title":           req.Title,           // 活动标题
		"Memo":            req.Memo,            // 活动备注
		"AuditType":       req.AuditType,       // 审核类型
		"State":           req.State,           // 活动状态
		"Sort":            req.Sort,            // 排序值
		"EffectStartTime": req.EffectStartTime, // 生效开始时间
		"EffectEndTime":   req.EffectEndTime,   // 生效结束时间
		"TitleImg":        req.TitleImg,        // 标题图片
		"ExtReward":       req.ExtReward,       // 额外奖励
		"MinLiuShui":      req.MinLiuShui,      // 最小流水
		"MinDeposit":      req.MinDeposit,      // 最小存款
		"MaxReward":       req.MaxReward,       // 最大奖励
		"ValidRecharge":   req.ValidRecharge,   // 有效充值
		"ValidLiuShui":    req.ValidLiuShui,    // 有效流水
		"Config":          req.Config,          // 活动配置
		"BaseConfig":      req.BaseConfig,      // 基础配置
		"TopImg":          req.TopImg,          // 顶部图片
		"IsTop":           req.IsTop,           // 是否置顶
		"TopSort":         req.TopSort,         // 置顶排序
		"TitleImgCn":      req.TitleImgCn,      // 中文标题图片
		"TitleImgEn":      req.TitleImgEn,      // 英文标题图片
		"TopImgEn":        req.TopImgEn,        // 英文置顶图片
		"GameType":        req.GameType,        // 游戏类型
		"TitleImgLang":    req.TitleImgLang,    // 多语言标题图片
		"TopImgLang":      req.TopImgLang,      // 多语言置顶图片
		"DetailImgLang":   req.DetailImgLang,   // 多语言详情图片
		"PcDetailImgLang": req.PcDetailImgLang, // 多语言详情图片
		"IsUseDetail":     req.IsUseDetail,     // 是否使用详情
		"Cats":            req.Cats,            // 活动分类
		"TitleLang":       req.TitleLang,       // 多语言标题
		"CustomerUrl":     req.CustomerUrl,     // 客服url

		// 充值活动相关字段 (只包含数据库中实际存在的字段)
		"AwardType":  req.AwardType,  // 奖金类别
		"AwardTab":   req.AwardTab,   // 奖金页面分类
		"GiftWallet": req.GiftWallet, // 赠送钱包类型
		"AwardData":  req.AwardData,  // 彩金钱包下的活动励配置数据
	}
}

// UpdateNormalActive 更新普通活动配置
// 更新除次日送以外的其他活动配置
func UpdateNormalActive(req DefineModReq, _ time.Time) error {
	where := abugo.AbuDbWhere{}
	// 注册赠送活动只使用id作为唯一标识
	if req.ActiveId == utils.RegisterGift {
		where.Add("and", "Id", "=", req.Id, 0)
	} else {
		where.Add("and", "SellerId", "=", req.SellerId, 0)
		where.Add("and", "ChannelId", "=", req.ChannelId, 0)
		where.Add("and", "ActiveId", "=", req.ActiveId, 0)
	}

	updates := RequestToUpdates(req)
	_, err := server.Db().Table("x_active_define").Select("*").Where(where).Update(updates)
	if err != nil {
		return err
	}

	err = SaveActiveSort(int32(req.Id), int32(req.Sort), int32(req.TopSort))
	return err
}

// UpdateNormalActiveById 通过ID更新普通活动配置
// 根据活动ID更新活动配置，但不更新商户ID、渠道ID和活动ID
func UpdateNormalActiveById(tb *model.XActiveDefine) error {
	if tb.ID == 0 {
		return errors.New("Id不能为空")
	}

	// 使用 map[string]interface{} 显式指定要更新的字段，包括时间字段
	dao := server.DaoxHashGame().XActiveDefine
	updates := map[string]interface{}{
		"Title":           tb.Title,
		"Memo":            tb.Memo,
		"AuditType":       tb.AuditType,
		"State":           tb.State,
		"Sort":            tb.Sort,
		"EffectStartTime": tb.EffectStartTime, // 即使为0也会更新
		"EffectEndTime":   tb.EffectEndTime,   // 即使为0也会更新
		"TitleImg":        tb.TitleImg,
		"TitleImgCn":      tb.TitleImgCn,
		"TitleImgEn":      tb.TitleImgEn,
		"TitleImgLang":    tb.TitleImgLang,
		"TopImg":          tb.TopImg,
		"TopImgEn":        tb.TopImgEn,
		"TopImgLang":      tb.TopImgLang,
		"GameType":        tb.GameType,
		"BaseConfig":      tb.BaseConfig,
		"Config":          tb.Config,
		"IsTop":           tb.IsTop,
		"TopSort":         tb.TopSort,
		"TitleLang":       tb.TitleLang,
		"Cats":            tb.Cats, // 添加分类字段
		// 充值活动相关字段 (只包含数据库中实际存在的字段)
		"AwardType":  tb.AwardType,  // 奖金类别
		"AwardTab":   tb.AwardTab,   // 奖金页面分类
		"GiftWallet": tb.GiftWallet, // 赠送钱包类型
		"AwardData":  tb.AwardData,  // 彩金钱包下的活动励配置数据
	}

	_, err := dao.WithContext(context.Background()).
		Where(dao.ID.Eq(tb.ID)).
		Updates(updates)
	if err != nil {
		logs.Error("UpdateNormalActiveById 更新失败: %v", err)
		return err
	}

	err = SaveActiveSort(tb.ID, tb.Sort, tb.TopSort)
	return err
}

// UpdateActiveDefine 更新活动定义
// 根据活动类型选择不同的更新方法
func UpdateActiveDefine(req DefineModReq, now time.Time) error {
	if req.ActiveId != utils.KActiveIdCiRiSong {
		return UpdateNormalActive(req, now)
	} else {
		return UpdateCiRiSongActive(req, now)
	}
}

// CheckActiveInfoValid 检查活动信息的有效性
// 根据不同活动类型验证限制值和奖励值的合法性
func CheckActiveInfoValid(ActiveId int, LimitValue, RewardValue float64, RewardJson string) string {
	switch ActiveId {
	case KActiveIdEachBetFanShui:
		if RewardValue < 0 || RewardValue > 100 {
			return "能量补给站返水金额不正确[0-100]"
		}
	case KActiveIdRechange:
		if RewardValue < 0 || RewardValue > 3000 {
			return "充值任务返奖金额不正确[0-3000]"
		}
	case KActiveIdHaXiBreakout, KActiveIdQipaiBreakout, KActiveIdDianziBreakout:
		if RewardValue < 0 || RewardValue > 20000 {
			return "闯关活动奖励金额不正确[0-20000]"
		}
	case KActiveIdRescue:
		if RewardValue < 0 || RewardValue > 0.5 {
			return "救援金返水比例不正确[0-0.5]"
		}
	case KActiveIdInvite:
		if RewardValue < 0 || RewardValue > 3000 {
			return "邀请好友返奖金额不正确[0-3000]"
		}
		if LimitValue < 0 {
			return "好友人数必须大于0"
		}
	case utils.KActiveIdCiRiSong:
		if RewardValue < 0 || RewardValue > 1 {
			return "只能获取0-1倍的昨日首充彩金"
		}
	case utils.KActiveIdMeiZhouSong:
		if RewardValue < 0 {
			return "彩金只能大于0"
		}
	case KActiveIdXianglongfuhu:
		type XianglongfuhuData struct {
			Level       int
			LimitValue  float64
			RewardValue float64
		}
		tmpData := make([]XianglongfuhuData, 0)
		err := json.Unmarshal([]byte(RewardJson), &tmpData)
		if err != nil {
			return "降龙伏虎奖励金额不正确,格式不正确"
		}
		for _, d := range tmpData {
			if d.Level < 0 {
				return "降龙伏虎档位不能不能为负数"
			}
			if d.LimitValue < 0 {
				return "降龙伏虎投注笔数不能为负数"
			}
			if d.RewardValue < 0 || d.RewardValue > 20000 {
				return "降龙伏虎奖励金额不正确[0-20000]"
			}
		}
	case utils.RegisterGift:
		if RewardValue < 0 || RewardValue > 10000 {
			return "注册赠送活动奖励金额不正确[0-10000]"
		}
	default:
		if RewardValue < 0 || RewardValue > 3000 {
			return "其他活动返奖金额不正确[0-3000]"
		}
	}
	return ""
}

// GetActiveNameByActiveId 根据活动ID获取活动名称
// 返回活动的中文名称
func GetActiveNameByActiveId(activeId int) string {
	switch activeId {
	case 0:
		return "全部"
	case KActiveIdRechange:
		return "充值任务"
	case KActiveIdHaXiBreakout:
		return "哈希闯关任"
	case KActiveIdQipaiBreakout:
		return "棋牌闯关任"
	case KActiveIdDianziBreakout:
		return "电子闯关任"
	case KActiveIdRescue:
		return "救援金"
	case KActiveIdInvite:
		return "邀请好友"
	case KActiveIdEachBetFanShui:
		return "能量补给站"
	case KActiveIdVipFanShui:
		return "VIP返水"
	case KActiveIdXianglongfuhu:
		return "降龙伏虎"
	case utils.KActiveIdFirstDepositGift:
		return "新用户首存送豪礼"
	case utils.KActiveIdCumulativeWeeklyRecharge:
		return "累计周充值，豪礼享不停"
	case utils.KActiveIdSignReward:
		return "签到奖励"
	case utils.KActiveIdRecommendFriendReward:
		return "推荐好友多重奖励"
	case utils.KActiveIdZhenrenBreakout:
		return "真人视讯闯关"
	case utils.KActiveIdDianziMeiZhouSong:
		return "电子周末狂欢送"
	case utils.KActiveIdZhenrenMeiZhouSong:
		return "真人视讯周末狂欢送"
	case utils.KActiveIdQiPaiMeiZhouSong:
		return "棋牌游戏周末狂欢送"
	case utils.KActiveIdNewFirstDeposit:
		return "新首存活动180"
	case utils.KActiveIdMidAutumnZhenrenBreakout:
		return "中秋每日真人闯关"
	case utils.KActiveIdMidAutumnBreakout:
		return "中秋每日流水闯关"
	case utils.KActiveIdCryptoGamesBreakout:
		return "加密游戏闯关"
	case utils.SocialMediaFollowGift:
		return "关注社媒，福利领不停"
	case utils.ChatRoomBonusGift:
		return "聊天室红包活动"
	case utils.RedeemCodeGift_1003, utils.RedeemCodeGift_1015, utils.RedeemCodeGift_1016, utils.RedeemCodeGift_1017:
		return "兑换码活动"
	case utils.SpecialBonusGift:
		return "特殊礼金活动"
	case utils.RegisterGift:
		return "注册赠送活动"
	case utils.NewUserDepositGift:
		return "新用户充值赠送活动"
	default:
		return "未定义"
	}
}
