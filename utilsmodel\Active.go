package utilsmodel

import "github.com/shopspring/decimal"

type FirstDepositGiftBaseConfig struct {
	RegisterDay int32 `json:"RegisterDay"`
	ReceiveDay  int32 `json:"ReceiveDay"`
}

type FirstDepositGiftConfig struct {
	ID                   int32   `json:"Id"`
	FirstChargeUstdLimit float32 `json:"FirstChargeUstdLimit"`
	LiushuiMultiple      int32   `json:"LiushuiMultiple"` // 真金流水倍数
	BonusMultiple        int32   `json:"BonusMultiple"`   // 彩金流水倍数
	GiveProportion       float32 `json:"GiveProportion"`
	GiveLimit            float32 `json:"GiveLimit"`
}

type CumulativeWeeklyRechargeConfig struct {
	ID             int32   `json:"Id"`
	RechargeAmount float32 `json:"RechargeAmount"`
	GiveAmount     float32 `json:"GiveAmount"`
}

// 签到奖励
type SignRewardBaseConfig struct {
	MixBetLimit float32 `json:"MixBetLimit"`
	TrxPrice    float32 `json:"TrxPrice"`
	RemakeDay   int8    `json:"RemakeDay"`
}

type SignRewardConfig struct {
	ID               int32   `json:"Id"`
	SignDay          int32   `json:"SignDay"`
	Award            float32 `json:"Award"`
	AdditionalReward float32 `json:"AdditionalReward"`
}

// 推荐好友多重奖励
type RecommendFriendRewardBaseConfig struct {
	RegisterDay          int32   `json:"RegisterDay"`          // 注册天数
	FirstChargeUstdLimit float32 `json:"FirstChargeUstdLimit"` // 首次存款
	Award                float32 `json:"Award"`                // 单次奖励
	Level                int32   `json:"Level"`                // vip等级
}

type RecommendFriendRewardConfig struct {
	ID               int32   `json:"Id"`
	TotalMin         int32   `json:"TotalMin"`         // 积累邀请人数最小
	TotalMax         int32   `json:"TotalMax"`         // 积累邀请人数最大
	AdditionalReward float32 `json:"AdditionalReward"` // 额外奖励
}

type BreakThroughConfig struct {
	ID          int32   `json:"Id"`
	LimitValue  float32 `json:"LimitValue"`
	RewardValue float32 `json:"RewardValue"`
}
type BreakThroughBaseConfig struct {
	TrxPrice float32 `json:"TrxPrice"`
}
type TodayBreakThroughBaseConfig struct {
	TrxPrice       float32 `json:"TrxPrice"`
	RechargeAmount float32 `json:"RechargeAmount"`
}

type EnergyWhitelistBaseConfig struct {
	UstdLimit float32 `json:"UstdLimit"` // 单笔投注金额
}

type NewFirstDepositConfig struct {
	ID              int32   `json:"Id"`
	GiveProportion  float32 `json:"GiveProportion"`
	GiveLimit       float32 `json:"GiveLimit"`
	LiushuiMultiple int32   `json:"LiushuiMultiple"`
}

// 每日充值洗码返利
type DailyRechargeRebateBaseConfig struct {
	RechargeMultiple      float32 `json:"RechargeMultiple"`      // 充值打码倍数流水
	FirstChargeProportion float32 `json:"FirstChargeProportion"` // 首充金额返利
	GiveLimit             float32 `json:"GiveLimit"`             // 最高赠送
}

// 推荐新会员，充值享豪礼
type RecommendNewMemberGiftBaseConfig struct {
	RedbagMember    int32   `json:"RedbagMember"`    // 红包个数
	RegisterDay     int32   `json:"RegisterDay"`     // 注册后几天累计
	RechargeAmount  float32 `json:"RechargeAmount"`  // 充值金额
	FinishMultiple  float32 `json:"FinishMultiple"`  //  完成流水倍数
	RedbagAmountMin float32 `json:"RedbagAmountMin"` // 红包金额最小值
	RedbagAmountMax float32 `json:"RedbagAmountMax"` // 红包金额最大值
}

// 幸运骰子
type LuckyDiceBaseConfig struct {
	ID     int32   `json:"Id"`
	Number int32   `json:"Number"` // 1的数量
	Award  float32 `json:"Award"`  // 奖励
}

type LuckyDiceConfig struct {
	ID         int32 `json:"Id"`
	LiushuiMin int32 `json:"LiushuiMin"` // 流水区间
	LiushuiMax int32 `json:"LiushuiMax"` // 流水区间
	AwardDice  int32 `json:"AwardDice"`  // 奖励骰子
}

// 爆庄奖励
type BoomingRewardConfig struct {
	ID                 int32   `json:"Id"`
	SingleDayExplosion float32 `json:"SingleDayExplosion"` // 单日爆庄
	Gift               string  `json:"Gift"`               // 礼品
	Value              float32 `json:"Value"`              // 价值
}

// 积分兑豪礼
type PointGiftBaseConfig struct {
	UstdLimit float32 `json:"UstdLimit"` // 有效投注
	Point     int32   `json:"Point"`     // 积分
}
type PointGiftConfig struct {
	ID    int32   `json:"Id"`
	Gift  string  `json:"Gift"`  // 礼品
	Value float32 `json:"Value"` // 价值
	Point int32   `json:"Point"` // 积分
}

// 周签到活跃奖励
type WeeklySignActiveRewardBaseConfig struct {
	LiushuiMultiple []int32 `json:"LiushuiMultiple"` // 每日流水达标
}

type WeeklySignActiveRewardConfig struct {
	ID     int32   `json:"Id"`
	Number int32   `json:"Number"` // 每周签到次数
	Award  float32 `json:"Award"`  // 奖励u
}

// 注册赠送活动基础配置
type RegisterGiftBaseConfig struct {
	RequireDuringRegistration int32    `json:"RequireDuringRegistration"` // 是否要求在注册时参与，0=否，1=是
	MaxIPAttempts             int32    `json:"MaxIPAttempts"`             // 同一IP最大领取次数，0表示不限制，大于0表示限制
	BlockedIPList             string   `json:"BlockedIPList"`             // 黑名单IP列表，多个IP用逗号分隔
	ApplicableGames           int32    `json:"ApplicableGames"`           // 适用游戏范围，0表示全站通用，1表示仅限指定游戏
	RewardAmount              float32  `json:"RewardAmount"`              // 奖励金额
	RewardWalletType          int32    `json:"RewardWalletType"`          // 奖励账户类型，0表示真金账户，1表示彩金账户
	UnlockType                int32    `json:"UnlockType"`                // 解锁类型，0=无要求，1=充值
	UnlockAmount              int32    `json:"UnlockAmount"`              // 解锁金额当UnlockType是1时需要
	CanPlayAfterBonus         int32    `json:"CanPlayAfterBonus"`         // 领取奖励后是否立即可玩游戏，0=否，1=是
	WagerRequirement          int32    `json:"WagerRequirement"`          // 提取奖励需打流水要求，0=倍数，1=金额
	WagerMultiple             []int32  `json:"WagerMultiple"`             // 提取奖励需打流水倍数[3,8] 3倍充值流水和8倍奖励流水
	WagerAmount               int32    `json:"WagerAmount"`               // 提取奖励需打固定金额
	RewardAmountType          int32    `json:"RewardAmountType"`          // 奖励金额类型，当WagerRequirement=0时使用：0=奖励金额，1=充值金额，2=充值与奖励金额
	RechargeTurnover          int32    `json:"RechargeTurnover"`          // 提现所需的充值流水
	RewardTurnover            int32    `json:"RewardTurnover"`            // 提现所需的奖励流水
	RechargeRewardTurnover    [2]int32 `json:"RechargeRewardTurnover"`    // 提现所需的充值与奖励流水
}

// 自定义充值活动-基础配置
type DIYRechargeActiveBaseConfig struct {
	/*// 置顶活动图
	TopImages []struct {
		LanguageTag  string // 语言标签(ISO 639-1 和 RFC 4646 标准) 例：zh,en,zh-hant,th,ko,tr,vi,pt,hi,ja,ru,km,uk
		LanguageName string // 语言名称 例：中文,英文,繁体中文,泰语,韩语,土耳其语,越南语,葡萄牙语,印地语,日语,俄语,高棉语(柬埔寨),乌克兰语
		ImageUrl     string // 图片链接
	}
	// 活动图
	Images []struct {
		LanguageTag  string // 语言标签(ISO 639-1 和 RFC 4646 标准) 例：zh,en,zh-hant,th,ko,tr,vi,pt,hi,ja,ru,km,uk
		LanguageName string // 语言名称 例：中文,英文,繁体中文,泰语,韩语,土耳其语,越南语,葡萄牙语,印地语,日语,俄语,高棉语(柬埔寨),乌克兰语
		ImageUrl     string // 图片链接
	}*/
	// 参与方式与条件
	Join struct {
		Type         int      // 参与方式: 1手动 2自动
		RegisterDays int      // 注册天数
		IsBindEmail  bool     // 是否需要绑定邮箱
		IsDuringReg  bool     // 是否在活动时间内注册
		IsIPLimit    bool     // 是否限制某些IP不能参加
		BlackIPList  string   // 限制的IP列表
		GameType     []string // 参与活动的场馆
		VIPLevels    []int32  // 参与活动的VIP等级
	}
	// 奖励发放方式与条件
	Reward struct {
		SendType  int32 // 1人工发放 2自动发放
		IsIPLimit bool  // 同IP每日只能领取一次
	}
	RechargeCalcType   int   // 充值计算方式:  1每日单笔 2每日累计 3活动期内单笔 4活动期内累计
	CalcType           int   // 计算方式: 1单次 2累计
	UnitType           int   // 计算单位: 1金额 2次数
	CycleType          int   // 计算周期: 0活动期内 1每日 2每周 3每月
	TotalNumberOfCycle int64 // 总可参与周期数
}

// 自定义充值活动-奖励方案配置
type DIYRechargeActiveRewardConfig struct {
	PlanList []struct {
		IsEnable       bool // 是否启用
		PlanType       int  // 方案类型: 1(方案1)按充值金额匹配 2(方案2)按第几次充值和充值金额匹配
		EffectiveHours int  // 活动有效期（小时）
		// 奖励档位
		RewardList []struct {
			NumberOfTimes int64 // 可参与次数
			// 奖励发放条件
			RewardCondition struct {
				RechargeNumber    int             // 充值次数
				MinRechargeAmount decimal.Decimal // 最低充值金额
				MaxRechargeAmount decimal.Decimal // 最高充值金额
				LiushuiMultiple   int64           // 充值流水倍数
			}
			// 奖励内容
			Reward struct {
				//Symbol    string          // 币种 目前只有USDT
				Amount    decimal.Decimal // 奖励金额
				IsPercent bool            // 奖励金额百分号
				MaxAmount decimal.Decimal // 最高奖励金额
			}
			// 奖励提现条件
			WithdrawCondition struct {
				Type                    int    // 1充值流水 2奖励流水 3充值流水+奖励流水
				Multiple                string // 流水倍数
				RechargeLiushuiMultiple int    // 充值流水倍数
				RewardLiushuiMultiple   int    // 奖励流水倍数
			}
		}
	}
}
