package main

import (
	_ "net/http/pprof"
	"xserver/controller"
	"xserver/controller/msg"
	"xserver/controller/robot"
	"xserver/controller/stats"
	"xserver/controller/userManger"
	"xserver/server"
)

func main() {
	server.Init()
	server.Http().Static("/api/exports", server.ExportDir())
	new(controller.UserController).Init()
	new(controller.AddressController).Init()
	new(controller.GameController).Init()
	new(controller.GameBrandController).Init()
	new(controller.OrderController).Init()
	new(controller.AgentController).Init()
	new(controller.ThreeLevelAgentController).Init()
	new(controller.GuestController).Init()
	new(controller.ReportController).Init()
	new(controller.DuiHuanController).Init()
	new(controller.ActiveController).Init()
	new(controller.TiyanjinActiveController).Init()
	new(controller.NoticeController).Init()
	new(controller.SocketController).Init()
	new(controller.UploadController).Init()
	new(controller.BackController).Init()
	new(controller.ChannelController).Init()
	new(controller.TelegramController).Init()
	new(controller.LotteryController).Init()
	new(controller.WalletController).Init()
	new(controller.VipController).Init()
	new(controller.ThirdController).Init()
	new(controller.PandaController).Init()
	new(controller.ChatController).Init()
	new(controller.KeFuController).Init()
	new(controller.PayController).Init()
	new(controller.RiskController).Init()
	new(controller.SourceController).Init()
	new(controller.FillOrderController).Init()
	new(controller.TgRobotController).Init()
	new(controller.TgAccountController).Init()
	new(controller.TgUserRobotController).Init()
	new(controller.TyjBroadcastController).Init()
	new(controller.ShuangZhuangController).Init()
	new(controller.TgRobotRedPacketController).Init()
	new(controller.TgRedPacketController).Init()
	new(controller.TgRedPacketWithdrawController).Init()
	new(controller.BlackBlockController).Init()
	new(controller.BlackCommissionController).Init()
	new(controller.WhiteBlockController).Init()
	new(controller.RedPacketAddCashController).Init()
	new(controller.WithdrawLimitConfigController).Init()
	new(controller.SetOnlineCntController).Init()
	new(controller.DomainGameController).Init()
	new(controller.LangController).Init()
	new(controller.BonusController).Init()
	new(controller.OnlineSocketController).Init()
	new(controller.RechargeReportController).Init()
	new(controller.TgPushMsgController).Init()
	new(controller.UtHashRewardController).Init()
	new(controller.BroadcastRewardController).Init()
	new(controller.UserProtectionController).Init()
	new(robot.Router).Init()
	new(userManger.UserManagerDataController).Init()
	new(msg.SiteMessageController).Init()
	new(userManger.UserClassController).Init()
	new(controller.ROIController).Init()
	new(stats.StatsClassController).Init()
	new(userManger.PlayerRetentionController).Init()
	new(controller.UserGameBrandController).Init()
	new(userManger.MarketDataController).Init()
	new(userManger.OperationsDataController).Init()
	server.Run()
}
