package robot

import (
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

// list 获取活动模板配置列表
func (c *Router) missionTempList(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		Page      int    `json:"page"`
		PageSize  int    `json:"page_size"`
		FullName  string `json:"full_name"`
		Name      string `json:"name"`
		Types     *int32 `json:"types"`
		UserTypes *int32 `json:"user_types"`
	}

	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "活动模板配置", "查", "查询活动模板配置")
	if token == nil {
		return
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}

	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	type Result struct {
		model.XRobotMissionTemplte
	}
	var results []*Result
	dao := server.DaoxHashGame().XRobotMissionTemplte

	query := dao.WithContext(nil).Select(dao.ALL)

	if reqdata.FullName != "" {
		query.Where(dao.FullName.Eq(reqdata.FullName))
	}

	if reqdata.Name != "" {
		query.Where(dao.Name.Eq(reqdata.Name))
	}

	if reqdata.Types != nil {
		query.Where(dao.Types.Eq(*reqdata.Types))
	}

	if reqdata.UserTypes != nil {
		query.Where(dao.UserTypes.Eq(*reqdata.UserTypes))
	}

	total, err := query.WithContext(nil).Where(dao.IsDel.Eq(0)).Order(dao.ID.Desc()).ScanByPage(&results, offset, limit)

	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	ctx.Put("total", total)
	ctx.Put("data", results)
	ctx.RespOK()
}

// create 创建活动模板配置
func (c *Router) missionTempCreate(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := model.XRobotMissionTemplte{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "活动模板配置", "增", "创建活动模板配置")
	if token == nil {
		return
	}

	// 设置创建人和更新时间
	reqdata.CreateTime = time.Now()
	reqdata.UpdateTime = time.Now()

	err := server.DaoxHashGame().XRobotMissionTemplte.WithContext(nil).Create(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

// update 更新活动模板配置
func (c *Router) missionTempUpdate(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := model.XRobotMissionTemplte{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "活动模板配置", "改", "更新活动模板配置")
	if token == nil {
		return
	}

	// 设置更新时间
	reqdata.UpdateTime = time.Now()

	err := server.DaoxHashGame().XRobotMissionTemplte.WithContext(nil).Where(server.DaoxHashGame().XRobotMissionTemplte.ID.Eq(reqdata.ID)).Save(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

// delete 删除活动模板配置
func (c *Router) missionTempDelete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type RequestData struct {
		ID int64 `json:"id"`
	}

	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "活动模板配置", "删", "删除活动模板配置")
	if token == nil {
		return
	}

	// 软删除
	_, err := server.DaoxHashGame().XRobotMissionTemplte.WithContext(nil).Where(server.DaoxHashGame().XRobotMissionTemplte.ID.Eq(reqdata.ID)).Update(server.DaoxHashGame().XRobotMissionTemplte.IsDel, 1)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}
