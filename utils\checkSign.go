package utils

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
)

func CheckSign() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 读取请求体数据
		bodyBytes, err := ioutil.ReadAll(c.Request.Body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "unable to read request body"})
			c.Abort()
			return
		}

		// 将请求体数据重新赋值给 c.Request.Body，以便后续处理函数可以再次读取
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))

		logs.Info(string(bodyBytes))
		// 解析请求体中的 JSON 数据
		var params map[string]interface{}

		if err := json.Unmarshal(bodyBytes, &params); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		reqSign, exists := params["sign"]
		if !exists {
			c.JSON(200, gin.H{
				"code": 0,
				"msg":  "sign not found",
				"data": "",
			})
			c.Abort()
			return
		}
		// 排除参数中的 "sign" 参数
		delete(params, "sign")

		// 对参数按照键排序
		keys := make([]string, 0, len(params))
		for key := range params {
			keys = append(keys, key)
		}
		sort.Strings(keys)

		// 遍历排序后的参数，处理键值对
		//var sortedParams []string
		//for _, key := range keys {
		//
		//	sortedParams = append(sortedParams, fmt.Sprintf("%v=%v", key, params[key]))
		//}
		//params["user_id"] = params["user_id"].(int64)
		//params["timestamp"] = params["timestamp"].(int64)
		//params["amount"] = params["amount"].(float64)
		var sortedParams []string
		for _, key := range keys {
			var value string
			switch v := params[key].(type) {
			case int:
				value = strconv.Itoa(v)
			case int64:
				value = strconv.FormatInt(v, 10)
			case float64:
				value = strconv.FormatFloat(v, 'f', -1, 64) // 保持原始形式
			case string:
				value = v
			default:
				value = fmt.Sprintf("%v", v)
			}
			sortedParams = append(sortedParams, fmt.Sprintf("%v=%v", key, value))
		}

		sortedParamsStr := strings.Join(sortedParams, "&")
		str := sortedParamsStr + "&key=kTEGAJ59zZABQ4dRInVcOC1og1vj06t1"

		// 创建一个 MD5 实例
		hash := md5.New()

		// 将字符串转换为字节数组并计算 MD5 值
		hash.Write([]byte(str))
		hashBytes := hash.Sum(nil)

		// 将 MD5 值转换为十六进制字符串
		md5Str := hex.EncodeToString(hashBytes)
		logs.Info(str, md5Str)
		if md5Str != reqSign {
			c.JSON(200, gin.H{
				"code": 0,
				"msg":  "sign error",
				"data": "",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
