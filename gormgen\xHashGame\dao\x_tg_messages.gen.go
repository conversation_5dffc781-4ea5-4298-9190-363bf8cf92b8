// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgMessage(db *gorm.DB, opts ...gen.DOOption) xTgMessage {
	_xTgMessage := xTgMessage{}

	_xTgMessage.xTgMessageDo.UseDB(db, opts...)
	_xTgMessage.xTgMessageDo.UseModel(&model.XTgMessage{})

	tableName := _xTgMessage.xTgMessageDo.TableName()
	_xTgMessage.ALL = field.NewAsterisk(tableName)
	_xTgMessage.ID = field.NewInt32(tableName, "Id")
	_xTgMessage.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgMessage.Message = field.NewString(tableName, "Message")
	_xTgMessage.State = field.NewInt32(tableName, "State")
	_xTgMessage.Robots = field.NewString(tableName, "Robots")
	_xTgMessage.PushType = field.NewInt32(tableName, "PushType")
	_xTgMessage.Users = field.NewString(tableName, "Users")
	_xTgMessage.RobotType = field.NewInt32(tableName, "RobotType")
	_xTgMessage.PushTime = field.NewTime(tableName, "PushTime")
	_xTgMessage.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgMessage.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xTgMessage.Operator = field.NewString(tableName, "Operator")

	_xTgMessage.fillFieldMap()

	return _xTgMessage
}

// xTgMessage Tg机器人消息推送
type xTgMessage struct {
	xTgMessageDo xTgMessageDo

	ALL        field.Asterisk
	ID         field.Int32
	SellerID   field.Int32  // 运营商
	Message    field.String // 文案消息
	State      field.Int32  // 发送状态 1:已发送 2:等待发送 3:已撤销 4:正在发送
	Robots     field.String // 需要发送的机器人ID，逗号隔开
	PushType   field.Int32  // 推送类型 1:群 2:用户 3:指定用户 4:指定群
	Users      field.String // 推送名单，逗号隔开
	RobotType  field.Int32  // 机器人类型 1:中文机器人 2:英文机器人
	PushTime   field.Time   // 推送时间
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间
	Operator   field.String // 操作人

	fieldMap map[string]field.Expr
}

func (x xTgMessage) Table(newTableName string) *xTgMessage {
	x.xTgMessageDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgMessage) As(alias string) *xTgMessage {
	x.xTgMessageDo.DO = *(x.xTgMessageDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgMessage) updateTableName(table string) *xTgMessage {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.Message = field.NewString(table, "Message")
	x.State = field.NewInt32(table, "State")
	x.Robots = field.NewString(table, "Robots")
	x.PushType = field.NewInt32(table, "PushType")
	x.Users = field.NewString(table, "Users")
	x.RobotType = field.NewInt32(table, "RobotType")
	x.PushTime = field.NewTime(table, "PushTime")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.Operator = field.NewString(table, "Operator")

	x.fillFieldMap()

	return x
}

func (x *xTgMessage) WithContext(ctx context.Context) *xTgMessageDo {
	return x.xTgMessageDo.WithContext(ctx)
}

func (x xTgMessage) TableName() string { return x.xTgMessageDo.TableName() }

func (x xTgMessage) Alias() string { return x.xTgMessageDo.Alias() }

func (x xTgMessage) Columns(cols ...field.Expr) gen.Columns { return x.xTgMessageDo.Columns(cols...) }

func (x *xTgMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgMessage) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["Message"] = x.Message
	x.fieldMap["State"] = x.State
	x.fieldMap["Robots"] = x.Robots
	x.fieldMap["PushType"] = x.PushType
	x.fieldMap["Users"] = x.Users
	x.fieldMap["RobotType"] = x.RobotType
	x.fieldMap["PushTime"] = x.PushTime
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["Operator"] = x.Operator
}

func (x xTgMessage) clone(db *gorm.DB) xTgMessage {
	x.xTgMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgMessage) replaceDB(db *gorm.DB) xTgMessage {
	x.xTgMessageDo.ReplaceDB(db)
	return x
}

type xTgMessageDo struct{ gen.DO }

func (x xTgMessageDo) Debug() *xTgMessageDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgMessageDo) WithContext(ctx context.Context) *xTgMessageDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgMessageDo) ReadDB() *xTgMessageDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgMessageDo) WriteDB() *xTgMessageDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgMessageDo) Session(config *gorm.Session) *xTgMessageDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgMessageDo) Clauses(conds ...clause.Expression) *xTgMessageDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgMessageDo) Returning(value interface{}, columns ...string) *xTgMessageDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgMessageDo) Not(conds ...gen.Condition) *xTgMessageDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgMessageDo) Or(conds ...gen.Condition) *xTgMessageDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgMessageDo) Select(conds ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgMessageDo) Where(conds ...gen.Condition) *xTgMessageDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgMessageDo) Order(conds ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgMessageDo) Distinct(cols ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgMessageDo) Omit(cols ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgMessageDo) Join(table schema.Tabler, on ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgMessageDo) Group(cols ...field.Expr) *xTgMessageDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgMessageDo) Having(conds ...gen.Condition) *xTgMessageDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgMessageDo) Limit(limit int) *xTgMessageDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgMessageDo) Offset(offset int) *xTgMessageDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgMessageDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgMessageDo) Unscoped() *xTgMessageDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgMessageDo) Create(values ...*model.XTgMessage) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgMessageDo) CreateInBatches(values []*model.XTgMessage, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgMessageDo) Save(values ...*model.XTgMessage) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgMessageDo) First() (*model.XTgMessage, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgMessage), nil
	}
}

func (x xTgMessageDo) Take() (*model.XTgMessage, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgMessage), nil
	}
}

func (x xTgMessageDo) Last() (*model.XTgMessage, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgMessage), nil
	}
}

func (x xTgMessageDo) Find() ([]*model.XTgMessage, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgMessage), err
}

func (x xTgMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgMessage, err error) {
	buf := make([]*model.XTgMessage, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgMessageDo) FindInBatches(result *[]*model.XTgMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgMessageDo) Attrs(attrs ...field.AssignExpr) *xTgMessageDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgMessageDo) Assign(attrs ...field.AssignExpr) *xTgMessageDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgMessageDo) Joins(fields ...field.RelationField) *xTgMessageDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgMessageDo) Preload(fields ...field.RelationField) *xTgMessageDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgMessageDo) FirstOrInit() (*model.XTgMessage, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgMessage), nil
	}
}

func (x xTgMessageDo) FirstOrCreate() (*model.XTgMessage, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgMessage), nil
	}
}

func (x xTgMessageDo) FindByPage(offset int, limit int) (result []*model.XTgMessage, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgMessageDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgMessageDo) Delete(models ...*model.XTgMessage) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgMessageDo) withDO(do gen.Dao) *xTgMessageDo {
	x.DO = *do.(*gen.DO)
	return x
}
