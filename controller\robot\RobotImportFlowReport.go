package robot

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"math"
	"mime/multipart"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

// 机器人引流报表

func (c *Router) uploadExtendFileData(ctx *abugo.AbuHttpContent) {
	errCode := 0

	if ctx.RespErrString(!server.Auth2(server.GetToken(ctx), "报表统计", "哈希机器人引流报表", "改"), &errCode, "权限不足") {
		return
	}
	file, header, err := ctx.Gin().Request.FormFile("file")
	if err != nil {
		ctx.RespErrString(false, &errCode, "获取上传文件失败: "+err.Error())
		return
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			ctx.RespErrString(false, &errCode, "文件读取/关闭失败: "+err.<PERSON>rror())
			return
		}
	}(file)
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".csv") {
		ctx.RespErrString(false, &errCode, "文件格式错误，请上传CSV文件")
		return
	}
	records, err := ParseCSVByIndexes(file, []int{0, 1, 2, 3, 4})
	if err != nil {
		ctx.RespErrString(false, &errCode, "解析CSV文件失败: "+err.Error())
		return
	}
	if len(records) < 1 {
		ctx.RespErrString(false, &errCode, "CSV文件内容为空，至少需要2行数据（包含表头）")
		return
	}
	if len(records) > 1 && len(records[1]) < 5 {
		ctx.RespErrString(false, &errCode, "CSV文件数据格式错误，每行至少需要3列：发送数量,使用小号数量,号单价")
		return
	}

	var updateCount int
	var errorCount int
	var errorMessages []string

	recordsByCreate := make([]*model.XRobotReportImportFlowUpload, 0)
	for i, j := range records {
		r := &model.XRobotReportImportFlowUpload{}
		fmt.Printf("%d,%#v\n", i, j)
		r.DateTime, err = time.Parse("2006-01-02", strings.TrimSpace(records[i][0]))
		r.SellerID = cast.ToInt32(strings.TrimSpace(records[i][1]))
		r.ChannelID = 0
		r.SendCnt = cast.ToInt32(strings.TrimSpace(records[i][2]))
		r.SmallAccontCnt = cast.ToInt32(strings.TrimSpace(records[i][3]))
		r.AccountPrice = cast.ToFloat64(strings.TrimSpace(records[i][4]))
		r.SendCostPrice = cast.ToFloat64(r.SmallAccontCnt) * r.AccountPrice
		r.SendAvgCnt = cast.ToFloat64(r.SendCnt) / cast.ToFloat64(r.SmallAccontCnt)
		if err != nil {
			errorCount++
			errorMessages = append(errorMessages, fmt.Sprintf("第%d行日期格式错误: %s", i, records[i][1]))
			continue
		}
		updateCount++
		recordsByCreate = append(recordsByCreate, r)
	}
	dao := server.DaoxHashGame().XRobotReportImportFlowUpload
	err = dao.WithContext(context.Background()).Select(dao.DateTime, dao.SellerID, dao.ChannelID, dao.SendCnt, dao.SmallAccontCnt, dao.AccountPrice).
		CreateInBatches(recordsByCreate, 100)
	if err != nil && strings.Contains(err.Error(), "Duplicate entry") {
		for _, r := range recordsByCreate {
			date := r.DateTime.Format("2006-01-02")
			start, _ := time.ParseInLocation("2006-01-02", date, time.Local)
			end := start.Add(24 * time.Hour)

			_, _ = dao.WithContext(context.Background()).
				Where(dao.DateTime.Gte(start), dao.DateTime.Lt(end)).
				Where(dao.SellerID.Eq(r.SellerID), dao.ChannelID.Eq(r.ChannelID)).
				Delete()

		}

		err = dao.WithContext(context.Background()).
			Select(dao.DateTime, dao.SellerID, dao.ChannelID, dao.SendCnt, dao.SmallAccontCnt, dao.AccountPrice, dao.SendCostPrice, dao.SendAvgCnt).
			CreateInBatches(recordsByCreate, 100)
	}
	if err != nil {
		ctx.RespErrString(false, &errCode, err.Error())
		return
	}

	// 返回结果
	ctx.Put("message", "上传处理完成")
	ctx.Put("updateCount", updateCount)
	ctx.Put("errorCount", errorCount)
	if len(errorMessages) > 0 {
		ctx.Put("errors", errorMessages)
	}
	ctx.RespOK()

}

func ParseCSVByIndexes(file multipart.File, indexes []int) ([][]string, error) {
	reader := csv.NewReader(file)
	reader.FieldsPerRecord = -1
	reader.LazyQuotes = true

	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}
	if len(records) < 1 {
		return nil, errors.New("CSV 文件为空")
	}

	var result [][]string
	for _, row := range records[1:] { // 跳过表头
		var values []string
		for _, idx := range indexes {
			if idx < len(row) {
				val := strings.TrimSpace(row[idx])
				values = append(values, val)
			} else {
				values = append(values, "") // 补空
			}
		}
		result = append(result, values)
	}
	return result, nil
}

func (c *Router) reportData(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page      int     `json:"page"`
		PageSize  int     `json:"page_size"`
		StartTime int64   `json:"start_time"`
		EndTime   int64   `json:"end_time"`
		SellerID  []int   `json:"seller_id"`
		ChannelID []int   `json:"channel_id"`
		AgentID   []int64 `json:"agent_id"`
		ValidPay  int     `json:"valid_pay"`
		IsExport  int     `json:"is_export"` // 是否导出 默认0 不导出
	}
	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "报表统计", "哈希机器人引流报表", "查", "查询哈希机器人引流报表")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 15
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	where := abugo.AbuDbWhere{}
	groupSQL := "GROUP BY a.date_time "

	if len(req.SellerID) > 0 {
		where.Add("and", "a.seller_id", "in", SliceToSQLTuple(req.SellerID), nil)
		groupSQL += ", a.seller_id "
	}
	if len(req.ChannelID) > 0 {
		where.Add("and", "a.channel_id", "in", SliceToSQLTuple(req.ChannelID), nil)
		groupSQL += ", a.channel_id "
	}
	if len(req.AgentID) > 0 {
		where.Add("and", "a.top_agent_id", "in", SliceToSQLTuple(req.AgentID), nil)
		groupSQL += ", a.top_agent_id "
	}
	if req.ValidPay == 0 {
		req.ValidPay = 10
	}
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "a.date_time", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "a.date_time", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
	}
	whereSQL, whereData := where.Sql()
	if whereSQL != "" {
		whereSQL = "WHERE " + whereSQL
	}
	sql := fmt.Sprintf(`
	SELECT 
	 a.date_time  , a.seller_id , a.channel_id  , top_agent_id , 

	 IFNULL(send_cnt,0) send_cnt  ,
	 IFNULL(small_accont_cnt,0) small_accont_cnt , 
	 IFNULL(account_price,0) account_price, 
	 IFNULL(send_cost_price,0) send_cost_price , 
	 IFNULL(send_avg_cnt,0) send_avg_cnt ,

	 SUM(CASE WHEN hash_balance_bet_amount>0 OR  hash_transfer_usdt_bet_amount>0  OR hash_transfer_trx_bet_amount>0 THEN 1 ELSE 0 END ) bet_cnt,
	 SUM(CASE WHEN recharge_amount>0  OR  (gift_usdt_status>0 AND  hash_transfer_usdt_bet_amount >10 )
		 OR  (gift_trx_status >0 AND hash_transfer_trx_bet_amount*(SELECT rechargerate FROM x_hash_game.x_finance_symbol WHERE symbol='TRX' LIMIT 1) >10) 
		 OR  (gift_usdt_status=0 AND  hash_transfer_usdt_bet_amount >0 ) OR  ( gift_trx_status=0 AND  hash_transfer_trx_bet_amount >0 ) THEN 1 ELSE 0 END ) pay_cnt,
	 SUM(CASE WHEN recharge_amount + hash_transfer_usdt_bet_amount+
		 hash_transfer_trx_bet_amount*(SELECT rechargerate FROM x_hash_game.x_finance_symbol WHERE symbol='TRX' LIMIT 1)>=%d THEN 1 ELSE 0 END ) valid_pay_cnt,
	 SUM(start_cnt) start_cnt ,
	 IFNULL(SUM(start_cnt) / send_cnt,0)*100 start_rate ,
	 SUM( CASE WHEN start_cnt=1 AND is_indb=1 THEN 1 ELSE 0 END )  start_cnt_indb,
	 IFNULL(SUM( CASE WHEN start_cnt=1 AND is_indb=1 THEN 1 ELSE 0 END ) / send_cnt ,0)*100 start_cnt_indb_rate ,
	 SUM(gift_trx_amount)  gift_trx_amount ,
	 SUM(CASE WHEN gift_trx_amount>0 THEN 1 ELSE 0 END ) gift_trx_cnt,
	 SUM(CASE WHEN gift_trx_amount>0 AND 
	  ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_trx_bet_cnt ,
	 SUM(CASE WHEN gift_trx_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount>0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount>0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_trx_win_cnt , 
	 SUM(CASE WHEN gift_trx_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount<0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount<0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount<0 ) THEN 1 ELSE 0 END ) gift_trx_loss_cnt , 
	 IFNULL(SUM(CASE WHEN gift_trx_status>0 AND 
	  ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END )/
	        SUM(CASE WHEN gift_trx_amount>0 THEN 1 ELSE 0 END ),0)*100 gift_trx_bet_rate , 
	 SUM(gift_usdt_amount)  gift_usdt_amount , 
	 SUM(CASE WHEN gift_usdt_amount>0 THEN 1 ELSE 0 END ) gift_usdt_cnt ,
	 SUM(CASE WHEN gift_usdt_amount>0 AND 
	  ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_usdt_bet_cnt  ,
	 SUM(CASE WHEN gift_usdt_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount>0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount>0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_usdt_win_cnt , 
	 SUM(CASE WHEN gift_usdt_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount<0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount<0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount<0 ) THEN 1 ELSE 0 END ) gift_usdt_loss_cnt  ,
	 IFNULL(SUM(CASE WHEN gift_usdt_amount>0 AND 
	                      ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) /
	        SUM(CASE WHEN gift_usdt_amount>0 THEN 1 ELSE 0 END ) ,0)*100 gift_usdt_bet_rate , 
	  
	 SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END ) hash_transfer_trx_first_bet_cnt ,
	 SUM(hash_transfer_trx_first_bet) hash_transfer_trx_first_bet ,
	 SUM(CASE WHEN hash_transfer_trx_bet_amount>0 THEN 1 ELSE 0 END ) hash_transfer_trx_bet_cnt ,
	 SUM(hash_transfer_trx_bet_amount) hash_transfer_trx_bet ,
	 SUM(hash_transfer_trx_bet_amount-hash_transfer_trx_win_amount) hash_transfer_trx_winloss , 
	 
	 SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END ) hash_transfer_usdt_first_bet_cnt ,
	 SUM(hash_transfer_usdt_first_bet) hash_transfer_usdt_first_bet ,
	 SUM(CASE WHEN hash_transfer_usdt_bet_amount>0 THEN 1 ELSE 0 END ) hash_transfer_usdt_bet_cnt ,
	 SUM(hash_transfer_usdt_bet_amount) hash_transfer_usdt_bet ,
	 SUM(hash_transfer_usdt_bet_amount-hash_transfer_usdt_win_amount) hash_transfer_usdt_winloss ,  
	 
	 SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END ) first_recharge_amount_cnt ,
	 SUM(first_recharge_amount) first_recharge_amount, 
	 SUM(CASE WHEN recharge_amount>0 THEN 1 ELSE 0 END ) recharge_amount_cnt , 
	 SUM(recharge_amount ) recharge_amount,  
	 SUM(CASE WHEN withdraw_amount>0 THEN 1 ELSE 0 END ) withdraw_amount_cnt , 
	 SUM(withdraw_amount ) withdraw_amount,  
	 SUM(recharge_amount ) -   SUM(withdraw_amount ) recharge_withdraw_sub ,
	 
	 IFNULL(send_cost_price / 
	 ( SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END ) - SUM(CASE WHEN gift_trx_amount>0 AND (hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) ),0) new_pay_cost ,
	 IFNULL(send_cost_price /  SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END ),0) new_recharge_cost,
	 IFNULL(SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END )+ 
	   SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END ) + 
		SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END )/send_cnt ,0)*100 bet_convert_rate ,
	 
	 IFNULL(SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END) -   
	   SUM(CASE WHEN gift_trx_amount>0 AND (hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END )/send_cnt,0) pay_convert_rate
	FROM   x_robot_report_import_flow  a
	LEFT JOIN x_robot_report_import_flow_upload  b 
	ON  a.seller_id =b.seller_id AND a.date_time =b.date_time 
	%s
	%s
	ORDER BY a.date_time   DESC  
	limit %d OFFSET %d`, req.ValidPay, whereSQL, groupSQL, limit, offset)
	results, err := server.Db().Query(sql, whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	total, err := server.Db().Query(fmt.Sprintf(`
	SELECT  COUNT(1) as count FROM   x_robot_report_import_flow  a
	LEFT JOIN 
	x_robot_report_import_flow_upload  b 
	ON  a.seller_id =b.seller_id AND a.date_time =b.date_time %s %s`, whereSQL, groupSQL), whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	pSum, err := server.Db().Query(fmt.Sprintf(`
	SELECT 
	 "合计"  date_time  , a.seller_id , a.channel_id  , top_agent_id , 
	 
	 SUM(IFNULL(send_cnt,0)) send_cnt  ,
	 SUM(IFNULL(small_accont_cnt,0)) small_accont_cnt , 
	 SUM(IFNULL(account_price,0)) account_price, 
	 SUM(IFNULL(send_cost_price,0)) send_cost_price , 
	 SUM(IFNULL(send_avg_cnt,0)) send_avg_cnt ,

	 SUM(CASE WHEN hash_balance_bet_amount>0 OR  hash_transfer_usdt_bet_amount>0  OR hash_transfer_trx_bet_amount>0 THEN 1 ELSE 0 END ) bet_cnt,
	 SUM(CASE WHEN recharge_amount>0  OR  (gift_usdt_status>0 AND  hash_transfer_usdt_bet_amount >10 )
		 OR  (gift_trx_status >0 AND hash_transfer_trx_bet_amount*(SELECT rechargerate FROM x_hash_game.x_finance_symbol WHERE symbol='TRX' LIMIT 1) >10) 
		 OR  (gift_usdt_status=0 AND  hash_transfer_usdt_bet_amount >0 ) OR  ( gift_trx_status=0 AND  hash_transfer_trx_bet_amount >0 ) THEN 1 ELSE 0 END ) pay_cnt,
	 SUM(CASE WHEN recharge_amount + hash_transfer_usdt_bet_amount+
		 hash_transfer_trx_bet_amount*(SELECT rechargerate FROM x_hash_game.x_finance_symbol WHERE symbol='TRX' LIMIT 1)>=%d THEN 1 ELSE 0 END ) valid_pay_cnt,
	 SUM(start_cnt) start_cnt ,
	 IFNULL(SUM(start_cnt) / send_cnt,0)*100 start_rate ,
	 SUM( CASE WHEN start_cnt=1 AND is_indb=1 THEN 1 ELSE 0 END )  start_cnt_indb,
	 IFNULL(SUM( CASE WHEN start_cnt=1 AND is_indb=1 THEN 1 ELSE 0 END ) / send_cnt ,0)*100 start_cnt_indb_rate ,
	 SUM(gift_trx_amount)  gift_trx_amount ,
	 SUM(CASE WHEN gift_trx_amount>0 THEN 1 ELSE 0 END ) gift_trx_cnt,
	 SUM(CASE WHEN gift_trx_amount>0 AND 
	  ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_trx_bet_cnt ,
	 SUM(CASE WHEN gift_trx_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount>0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount>0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_trx_win_cnt , 
	 SUM(CASE WHEN gift_trx_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount<0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount<0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount<0 ) THEN 1 ELSE 0 END ) gift_trx_loss_cnt , 
	 IFNULL(SUM(CASE WHEN gift_trx_status>0 AND 
	  ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) /
	        SUM(CASE WHEN gift_trx_amount>0 THEN 1 ELSE 0 END ),0)*100 gift_trx_bet_rate , 
	 SUM(gift_usdt_amount)  gift_usdt_amount , 
	 SUM(CASE WHEN gift_usdt_amount>0 THEN 1 ELSE 0 END ) gift_usdt_cnt ,
	 SUM(CASE WHEN gift_usdt_amount>0 AND 
	  ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_usdt_bet_cnt  ,
	 SUM(CASE WHEN gift_usdt_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount>0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount>0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) gift_usdt_win_cnt , 
	 SUM(CASE WHEN gift_usdt_amount>0 AND 
	  ( hash_balance_win_amount- hash_balance_bet_amount<0 OR hash_transfer_trx_win_amount-hash_transfer_trx_bet_amount<0 
		 OR hash_transfer_usdt_win_amount-hash_transfer_usdt_bet_amount<0 ) THEN 1 ELSE 0 END ) gift_usdt_loss_cnt  ,
	 IFNULL(SUM(CASE WHEN gift_usdt_amount>0 AND 
	  ( hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) / 
	        SUM(CASE WHEN gift_usdt_amount>0 THEN 1 ELSE 0 END ) ,0)*100 gift_usdt_bet_rate , 
	  
	 SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END ) hash_transfer_trx_first_bet_cnt ,
	 SUM(hash_transfer_trx_first_bet) hash_transfer_trx_first_bet ,
	 SUM(CASE WHEN hash_transfer_trx_bet_amount>0 THEN 1 ELSE 0 END ) hash_transfer_trx_bet_cnt ,
	 SUM(hash_transfer_trx_bet_amount) hash_transfer_trx_bet ,
	 SUM(hash_transfer_trx_bet_amount-hash_transfer_trx_win_amount) hash_transfer_trx_winloss , 
	 
	 SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END ) hash_transfer_usdt_first_bet_cnt ,
	 SUM(hash_transfer_usdt_first_bet) hash_transfer_usdt_first_bet ,
	 SUM(CASE WHEN hash_transfer_usdt_bet_amount>0 THEN 1 ELSE 0 END ) hash_transfer_usdt_bet_cnt ,
	 SUM(hash_transfer_usdt_bet_amount) hash_transfer_usdt_bet ,
	 SUM(hash_transfer_usdt_bet_amount-hash_transfer_usdt_win_amount) hash_transfer_usdt_winloss ,  
	 
	 SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END ) first_recharge_amount_cnt ,
	 SUM(first_recharge_amount) first_recharge_amount, 
	 SUM(CASE WHEN recharge_amount>0 THEN 1 ELSE 0 END ) recharge_amount_cnt , 
	 SUM(recharge_amount ) recharge_amount,  
	 SUM(CASE WHEN withdraw_amount>0 THEN 1 ELSE 0 END ) withdraw_amount_cnt , 
	 SUM(withdraw_amount ) withdraw_amount,  
	 SUM(recharge_amount ) -   SUM(withdraw_amount ) recharge_withdraw_sub ,
	 
	 IFNULL(send_cost_price / 
	 ( SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END ) - SUM(CASE WHEN gift_trx_amount>0 AND (hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END ) ),0) new_pay_cost ,
	 IFNULL(send_cost_price /  SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END ),0) new_recharge_cost,
	 IFNULL(SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END )+ 
	   SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END ) + 
		SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END )/send_cnt ,0)*100 bet_convert_rate ,
	 
	 IFNULL(SUM(CASE WHEN hash_transfer_trx_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN hash_transfer_usdt_first_bet>0 THEN 1 ELSE 0 END )+
	   SUM(CASE WHEN first_recharge_amount>0 THEN 1 ELSE 0 END) -   
	   SUM(CASE WHEN gift_trx_amount>0 AND (hash_balance_bet_amount>0 OR hash_transfer_trx_bet_amount>0 OR hash_transfer_usdt_bet_amount>0 ) THEN 1 ELSE 0 END )/send_cnt,0)*100 pay_convert_rate
	FROM   x_robot_report_import_flow  a
	LEFT JOIN 
	x_robot_report_import_flow_upload  b 
	ON  a.seller_id =b.seller_id AND a.date_time =b.date_time  %s `, req.ValidPay, whereSQL), whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	// 求平均
	ignoreFields := map[string]struct{}{
		"top_agent_id": {},
		"date_time":    {},
		"channel_id":   {},
		"seller_id":    {},
	}
	pAvg := make([]map[string]interface{}, 0)

	count := 0.0
	if len(*total) > 0 {
		count = cast.ToFloat64((*total)[0]["count"])
	} else {
		*total = append(*total, map[string]interface{}{"count": 0})

	}
	if count == 0 {
		count = 1.0
	}
	for _, row := range *pSum {
		newItem := make(map[string]interface{}, len(row))
		for key, val := range row {
			if _, skip := ignoreFields[key]; skip {
				if key == "date_time" {
					newItem[key] = "每日平均统计"
				}
				continue
			}
			switch v := val.(type) {
			case float64:
				newItem[key] = math.Round((v/count)*1e6) / 1e6
			case int:
				newItem[key] = math.Round((float64(v)/count)*1e6) / 1e6
			case int64:
				newItem[key] = math.Round((float64(v)/count)*1e6) / 1e6
			default:
				continue
			}
		}
		pAvg = append(pAvg, newItem)
	}
	if req.IsExport == 1 {
		var headerLine = []string{"日期", "总投注人数", "总付费用户", "总有效付费用户", "发送数量", "使用小号数量", "号单价",
			"发送总费用", "平均发送", "启动人数", "启动率", "在库启动人数", "在库启动率", "赠送TRX金额", "领取TRX人数",
			"领取TRX投注人数", "领取TRX盈利人数", "领取TRX亏损人数", "领取TRX投注率", "赠送U金额", "领取U人数", "领取U投注人数",
			"领取U盈利人数", "领取U亏损人数", "领取U投注率", "首次转账TRX人数", "首次转账TRX总转金额", "总转账TRX人数", "总转账TRX流水",
			"TRX盈亏", "首次转账U人数", "首次转账U总转金额", "总转账U人数", "总转账U流水", "U盈亏", "首充人数", "首充用户总充金额",
			"充值人数", "充值金额", "提款人数", "提款金额充提差", "新付费用户成本", "新增充值获客成本", "投注转化率", "付费转化率"}
		var filterMap = map[string]string{
			"日期":          "date_time",
			"总投注人数":       "bet_cnt",
			"总付费用户":       "pay_cnt",
			"总有效付费用户":     "valid_pay_cnt",
			"发送数量":        "send_cnt",
			"使用小号数量":      "small_accont_cnt",
			"号单价":         "account_price",
			"发送总费用":       "send_cost_price",
			"平均发送":        "send_avg_cnt",
			"启动人数":        "start_cnt",
			"启动率":         "start_rate",
			"在库启动人数":      "start_cnt_indb",
			"在库启动率":       "start_cnt_indb_rate",
			"赠送TRX金额":     "gift_trx_amount",
			"领取TRX人数":     "gift_trx_cnt",
			"领取TRX投注人数":   "gift_trx_bet_cnt",
			"领取TRX盈利人数":   "gift_trx_win_cnt",
			"领取TRX亏损人数":   "gift_trx_loss_cnt",
			"领取TRX投注率":    "gift_trx_bet_rate",
			"赠送U金额":       "gift_usdt_amount",
			"领取U人数":       "gift_usdt_cnt",
			"领取U投注人数":     "gift_usdt_bet_cnt",
			"领取U盈利人数":     "gift_usdt_win_cnt",
			"领取U亏损人数":     "gift_usdt_loss_cnt",
			"领取U投注率":      "gift_usdt_bet_rate",
			"首次转账TRX人数":   "hash_transfer_trx_first_bet_cnt",
			"首次转账TRX总转金额": "hash_transfer_trx_first_bet",
			"总转账TRX人数":    "hash_transfer_trx_bet_cnt",
			"总转账TRX流水":    "hash_transfer_trx_bet",
			"TRX盈亏":       "hash_transfer_trx_winloss",
			"首次转账U人数":     "hash_transfer_usdt_first_bet_cnt",
			"首次转账U总转金额":   "hash_transfer_usdt_first_bet",
			"总转账U人数":      "hash_transfer_usdt_bet_cnt",
			"总转账U流水":      "hash_transfer_usdt_bet",
			"U盈亏":         "hash_transfer_usdt_winloss",
			"首充人数":        "first_recharge_amount_cnt",
			"首充用户总充金额":    "first_recharge_amount",
			"充值人数":        "recharge_amount_cnt",
			"充值金额":        "recharge_amount",
			"提款人数":        "withdraw_amount_cnt",
			"提款金额":        "withdraw_amount",
			"充提差":         "recharge_withdraw_sub",
			"新付费用户成本":     "new_pay_cost",
			"新增充值获客成本":    "new_recharge_cost",
			"投注转化率":       "bet_convert_rate",
			"付费转化率":       "pay_convert_rate",
		}

		var mappedResults []map[string]interface{}
		mappedResults = append(mappedResults, *results...)
		mappedResults = append(mappedResults, *pSum...)
		mappedResults = append(mappedResults, pAvg...)
		filename, err := c.exportCSV(&mappedResults, headerLine, filterMap)
		if err != nil {
			ctx.RespErrString(true, &errCode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	}
	ctx.Put("total", total)
	ctx.Put("results", results)
	ctx.Put("sum", pSum)
	ctx.Put("avg", pAvg)
	ctx.RespOK()
}
