// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXDictChangetype = "x_dict_changetype"

// XDictChangetype 资金变化类型
type XDictChangetype struct {
	ChangeType int64     `gorm:"column:ChangeType;primaryKey;comment:变化类型" json:"ChangeType"`                                                             // 变化类型
	ChangeName string    `gorm:"column:ChangeName;not null;comment:变化名" json:"ChangeName"`                                                                // 变化名
	ParentType int32     `gorm:"column:ParentType;default:2001;comment:上级分类 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金" json:"ParentType"` // 上级分类 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金
	Sort       int32     `gorm:"column:Sort;default:1;comment:排序" json:"Sort"`                                                                            // 排序
	Memo       string    `gorm:"column:Memo;comment:描述" json:"Memo"`                                                                                      // 描述
	Status     int32     `gorm:"column:Status;not null;default:1;comment:0无效 1有效" json:"Status"`                                                          // 0无效 1有效
	Operator   string    `gorm:"column:Operator;comment:操作员" json:"Operator"`                                                                             // 操作员
	OperUserID int32     `gorm:"column:OperUserID;comment:操作员ID" json:"OperUserID"`                                                                       // 操作员ID
	DeviceType int32     `gorm:"column:DeviceType;comment:设备类型" json:"DeviceType"`                                                                        // 设备类型
	DeviceID   string    `gorm:"column:DeviceID;comment:设备ID" json:"DeviceID"`                                                                            // 设备ID
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`                                     // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`                                     // 更新时间
}

// TableName XDictChangetype's table name
func (*XDictChangetype) TableName() string {
	return TableNameXDictChangetype
}
