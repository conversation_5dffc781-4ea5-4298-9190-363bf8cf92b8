// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotMessageTemplte(db *gorm.DB, opts ...gen.DOOption) xRobotMessageTemplte {
	_xRobotMessageTemplte := xRobotMessageTemplte{}

	_xRobotMessageTemplte.xRobotMessageTemplteDo.UseDB(db, opts...)
	_xRobotMessageTemplte.xRobotMessageTemplteDo.UseModel(&model.XRobotMessageTemplte{})

	tableName := _xRobotMessageTemplte.xRobotMessageTemplteDo.TableName()
	_xRobotMessageTemplte.ALL = field.NewAsterisk(tableName)
	_xRobotMessageTemplte.ID = field.NewInt64(tableName, "id")
	_xRobotMessageTemplte.Name = field.NewString(tableName, "name")
	_xRobotMessageTemplte.FullName = field.NewString(tableName, "full_name")
	_xRobotMessageTemplte.MessageType = field.NewInt32(tableName, "message_type")
	_xRobotMessageTemplte.MessageObject = field.NewInt32(tableName, "message_object")
	_xRobotMessageTemplte.UserType = field.NewInt32(tableName, "user_type")
	_xRobotMessageTemplte.RobotType = field.NewInt32(tableName, "robot_type")
	_xRobotMessageTemplte.DataText = field.NewString(tableName, "data_text")
	_xRobotMessageTemplte.IsDel = field.NewInt32(tableName, "is_del")
	_xRobotMessageTemplte.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotMessageTemplte.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotMessageTemplte.fillFieldMap()

	return _xRobotMessageTemplte
}

// xRobotMessageTemplte 推送消息发送 模板
type xRobotMessageTemplte struct {
	xRobotMessageTemplteDo xRobotMessageTemplteDo

	ALL           field.Asterisk
	ID            field.Int64  // id
	Name          field.String // 名称
	FullName      field.String // 全称
	MessageType   field.Int32  // 消息类型1 模板回复类型  2.主动推送
	MessageObject field.Int32  // 消息对象 0私发 1：群发
	UserType      field.Int32  // 推送用户类型：0： 全部 1：在库，2：不在库，3；领U用户，4领Trx用户
	RobotType     field.Int32  // 机器人配置类型：1 接待2 广告
	DataText      field.String // json字段
	IsDel         field.Int32  // 删除
	CreateTime    field.Time   // 创建日期
	UpdateTime    field.Time   // 更新日期

	fieldMap map[string]field.Expr
}

func (x xRobotMessageTemplte) Table(newTableName string) *xRobotMessageTemplte {
	x.xRobotMessageTemplteDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotMessageTemplte) As(alias string) *xRobotMessageTemplte {
	x.xRobotMessageTemplteDo.DO = *(x.xRobotMessageTemplteDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotMessageTemplte) updateTableName(table string) *xRobotMessageTemplte {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.Name = field.NewString(table, "name")
	x.FullName = field.NewString(table, "full_name")
	x.MessageType = field.NewInt32(table, "message_type")
	x.MessageObject = field.NewInt32(table, "message_object")
	x.UserType = field.NewInt32(table, "user_type")
	x.RobotType = field.NewInt32(table, "robot_type")
	x.DataText = field.NewString(table, "data_text")
	x.IsDel = field.NewInt32(table, "is_del")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotMessageTemplte) WithContext(ctx context.Context) *xRobotMessageTemplteDo {
	return x.xRobotMessageTemplteDo.WithContext(ctx)
}

func (x xRobotMessageTemplte) TableName() string { return x.xRobotMessageTemplteDo.TableName() }

func (x xRobotMessageTemplte) Alias() string { return x.xRobotMessageTemplteDo.Alias() }

func (x xRobotMessageTemplte) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotMessageTemplteDo.Columns(cols...)
}

func (x *xRobotMessageTemplte) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotMessageTemplte) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["id"] = x.ID
	x.fieldMap["name"] = x.Name
	x.fieldMap["full_name"] = x.FullName
	x.fieldMap["message_type"] = x.MessageType
	x.fieldMap["message_object"] = x.MessageObject
	x.fieldMap["user_type"] = x.UserType
	x.fieldMap["robot_type"] = x.RobotType
	x.fieldMap["data_text"] = x.DataText
	x.fieldMap["is_del"] = x.IsDel
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotMessageTemplte) clone(db *gorm.DB) xRobotMessageTemplte {
	x.xRobotMessageTemplteDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotMessageTemplte) replaceDB(db *gorm.DB) xRobotMessageTemplte {
	x.xRobotMessageTemplteDo.ReplaceDB(db)
	return x
}

type xRobotMessageTemplteDo struct{ gen.DO }

func (x xRobotMessageTemplteDo) Debug() *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotMessageTemplteDo) WithContext(ctx context.Context) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotMessageTemplteDo) ReadDB() *xRobotMessageTemplteDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotMessageTemplteDo) WriteDB() *xRobotMessageTemplteDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotMessageTemplteDo) Session(config *gorm.Session) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotMessageTemplteDo) Clauses(conds ...clause.Expression) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotMessageTemplteDo) Returning(value interface{}, columns ...string) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotMessageTemplteDo) Not(conds ...gen.Condition) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotMessageTemplteDo) Or(conds ...gen.Condition) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotMessageTemplteDo) Select(conds ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotMessageTemplteDo) Where(conds ...gen.Condition) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotMessageTemplteDo) Order(conds ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotMessageTemplteDo) Distinct(cols ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotMessageTemplteDo) Omit(cols ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotMessageTemplteDo) Join(table schema.Tabler, on ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotMessageTemplteDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotMessageTemplteDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotMessageTemplteDo) Group(cols ...field.Expr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotMessageTemplteDo) Having(conds ...gen.Condition) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotMessageTemplteDo) Limit(limit int) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotMessageTemplteDo) Offset(offset int) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotMessageTemplteDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotMessageTemplteDo) Unscoped() *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotMessageTemplteDo) Create(values ...*model.XRobotMessageTemplte) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotMessageTemplteDo) CreateInBatches(values []*model.XRobotMessageTemplte, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotMessageTemplteDo) Save(values ...*model.XRobotMessageTemplte) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotMessageTemplteDo) First() (*model.XRobotMessageTemplte, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMessageTemplte), nil
	}
}

func (x xRobotMessageTemplteDo) Take() (*model.XRobotMessageTemplte, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMessageTemplte), nil
	}
}

func (x xRobotMessageTemplteDo) Last() (*model.XRobotMessageTemplte, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMessageTemplte), nil
	}
}

func (x xRobotMessageTemplteDo) Find() ([]*model.XRobotMessageTemplte, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotMessageTemplte), err
}

func (x xRobotMessageTemplteDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotMessageTemplte, err error) {
	buf := make([]*model.XRobotMessageTemplte, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotMessageTemplteDo) FindInBatches(result *[]*model.XRobotMessageTemplte, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotMessageTemplteDo) Attrs(attrs ...field.AssignExpr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotMessageTemplteDo) Assign(attrs ...field.AssignExpr) *xRobotMessageTemplteDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotMessageTemplteDo) Joins(fields ...field.RelationField) *xRobotMessageTemplteDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotMessageTemplteDo) Preload(fields ...field.RelationField) *xRobotMessageTemplteDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotMessageTemplteDo) FirstOrInit() (*model.XRobotMessageTemplte, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMessageTemplte), nil
	}
}

func (x xRobotMessageTemplteDo) FirstOrCreate() (*model.XRobotMessageTemplte, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMessageTemplte), nil
	}
}

func (x xRobotMessageTemplteDo) FindByPage(offset int, limit int) (result []*model.XRobotMessageTemplte, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotMessageTemplteDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotMessageTemplteDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotMessageTemplteDo) Delete(models ...*model.XRobotMessageTemplte) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotMessageTemplteDo) withDO(do gen.Dao) *xRobotMessageTemplteDo {
	x.DO = *do.(*gen.DO)
	return x
}
