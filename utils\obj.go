package utils

import (
	"encoding/json"
	"fmt"
	"net"
	"strings"

	"github.com/beego/beego/logs"
	"github.com/oschwald/geoip2-golang"
)

func Copy(src, dist any) error {
	b, err := json.Marshal(src)
	if err != nil {
		return err
	}
	return json.Unmarshal(b, dist)
}

func ToString(obj interface{}) {

}

// 渠道多选
func ToChannels(channelId []int) string {
	in := ""
	if len(channelId) > 0 {
		for _, v := range channelId {
			if v == 0 {
				continue
			}
			in += fmt.Sprintf("%d,", v)
		}
		if len(in) > 0 {
			in = in[0 : len(in)-1]
		}
	}
	return in
}

func ToSellers(sellerId []int32) string {
	in := ""
	if len(sellerId) > 0 {
		for _, v := range sellerId {
			if v == 0 {
				continue
			}
			in += fmt.Sprintf("%d,", v)
		}
		if len(in) > 0 {
			in = in[0 : len(in)-1]
		}
	}
	return in
}

func ToChannelsDistinctUser(channelId []int, SellerId int, Symbol string, SpecialAgent int, TopAgentId int) []string {
	in := make([]string, 0, len(channelId))
	if len(channelId) > 0 {
		for _, v := range channelId {
			if v == 0 {
				continue
			}
			in = append(in, fmt.Sprintf("%d_%d_%s_%d_%d_%d", SellerId, v, Symbol, SpecialAgent, TopAgentId, 0))
		}
	}
	return in
}

func ToChannelsDistinctUserGameType(channelId []int, gametype string, SellerId int, Symbol string, SpecialAgent int, TopAgentId int) []interface{} {
	in := make([]interface{}, 0, len(channelId))
	if len(channelId) > 0 {
		for _, v := range channelId {
			if v == 0 {
				continue
			}
			in = append(in, fmt.Sprintf("%s_%d_%d_%s_%d_%d_%d", gametype, SellerId, v, Symbol, SpecialAgent, TopAgentId, 0))
		}
	}
	return in
}

func ToChannelsComprehensive(channelId []int, gametype string, SellerId int, Symbol string, SpecialAgent int, TopAgentId int) []interface{} {
	in := make([]interface{}, 0, len(channelId))
	if len(channelId) > 0 {
		for _, v := range channelId {
			if v == 0 {
				continue
			}
			in = append(in, fmt.Sprintf("%s_%d_%d_%s_%d_%d_%d", gametype, SellerId, v, Symbol, SpecialAgent, TopAgentId, 0))
		}
	}
	return in
}

// 邮箱@前面保留3位
func MaskEmail(email string) string {
	// 检查电子邮件地址是否有效。
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return email
	}

	// 分割电子邮件地址的本地部分和域部分。
	parts := strings.Split(email, "@")
	localPart := parts[0]
	domainPart := parts[1]

	// 检查本地部分的长度是否大于等于 3 位
	if len(localPart) >= 3 {
		// 掩码电子邮件地址的本地部分。
		maskedLocalPart := localPart[:3] + strings.Repeat("*", len(localPart)-3)
		// 返回掩码的电子邮件地址。
		return maskedLocalPart + "@" + domainPart
	} else {
		// 处理本地部分长度小于 3 位的情况
		return localPart + "@" + domainPart
	}
}

func ReplaceMiddleWithStars(s string) string {
	length := len(s)
	start := length / 4
	end := length - start
	middle := strings.Repeat("*", end-start)
	return s[:start] + middle + s[end:]
}

func IpToLocation(ipStr string) string {
	// 打开 MaxMind 数据库文件
	db, err := geoip2.Open("./config/GeoLite2-Country.mmdb")
	if err != nil {
		logs.Error("geoip2.Open", err)
		return ""
	}
	defer db.Close()

	// 解析 IP 地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		logs.Error("Invalid IP address format")
		return ""
	}

	// 查询 IP 地址的国家和洲信息
	record, err := db.Country(ip)
	if err != nil {
		logs.Error("db.Country(ip)", err)
		return ""
	}

	// 返回洲信息
	return record.Country.Names["zh-CN"]
}

func RemoveHTTPPrefix(url string) string {
	// 去除 https:// 前缀
	url = strings.TrimPrefix(url, "https://")
	// 去除 http:// 前缀
	url = strings.TrimPrefix(url, "http://")
	return url
}
