package controller

import (
	"encoding/json"
	"strconv"
	"xserver/abugo"
	"xserver/controller/userbrand"
	"xserver/server"

	"github.com/beego/beego/logs"
)

// UserGameBrandController 处理用户游戏厂商禁止相关功能
type UserGameBrandController struct {
}

// Init 初始化路由
func (c *UserGameBrandController) Init() {
	group := server.Http().NewGroup("/api/admin/user/game_brand")
	{
		group.Post("/set", c.setBlockedGameBrands)
		group.Post("/get", c.getBlockedGameBrands)
		group.Post("/list", c.listAllGameBrands)
	}
}

// 设置玩家禁止的游戏厂商
func (c *UserGameBrandController) setBlockedGameBrands(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		UserId     int                 `json:"UserId" validate:"required"`
		TypeBrands map[string][]string `json:"TypeBrands"` // 按类型分组的禁止厂商，格式：{"1": ["gfg", "mg"], "3": ["og"]}
		GoogleCode string              `json:"GoogleCode" validate:"required"`
	}{}

	// 获取请求数据并验证权限 设置禁止游戏厂商 查询玩家列表
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "改", "查询玩家列表")
	if token == nil {
		return
	}

	// 验证谷歌验证码
	//if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
	//	return
	//}

	// 验证运营商权限
	if ctx.RespErrString(token.SellerId > 0, &errcode, "运营商不正确") {
		return
	}

	// 构建禁止配置（简化结构）
	blockedConfig := struct {
		TypeBrands map[string][]string `json:"typeBrands"` // 按类型分组的禁止厂商
	}{
		TypeBrands: reqdata.TypeBrands,
	}

	// 如果TypeBrands为空，初始化为空map
	if blockedConfig.TypeBrands == nil {
		blockedConfig.TypeBrands = make(map[string][]string)
	}

	// 将配置序列化为JSON
	configJson, err := json.Marshal(blockedConfig)
	if err != nil {
		logs.Error("序列化游戏禁止配置失败 UserId=", reqdata.UserId, " config=", blockedConfig, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "序列化游戏禁止配置失败")
		return
	}

	// 使用GormDao操作数据库
	db := server.Db().GormDao()

	// 检查记录是否存在
	var existingRecord struct {
		UserId int32 `gorm:"column:UserId"`
	}

	err = db.Table("x_user_more").
		Select("UserId").
		Where("UserId = ?", reqdata.UserId).
		First(&existingRecord).Error

	if err != nil {
		// 记录不存在，创建新记录
		err = db.Table("x_user_more").Create(map[string]interface{}{
			"UserId":            reqdata.UserId,
			"BlockedGameBrands": string(configJson),
		}).Error

		if err != nil {
			logs.Error("创建用户扩展记录失败 UserId=", reqdata.UserId, " err=", err.Error())
			ctx.RespErrString(true, &errcode, "创建用户记录失败")
			return
		}
	} else {
		// 记录存在，更新记录
		err = db.Table("x_user_more").
			Where("UserId = ?", reqdata.UserId).
			Update("BlockedGameBrands", string(configJson)).Error

		if err != nil {
			logs.Error("更新用户禁止游戏配置失败 UserId=", reqdata.UserId, " err=", err.Error())
			ctx.RespErrString(true, &errcode, "更新用户记录失败")
			return
		}
	}
	logs.Info("更新成功")
	// 更新Redis缓存
	err = userbrand.SetUserBlockedGameBrands(reqdata.UserId, string(configJson))
	if err != nil {
		logs.Error("更新Redis缓存失败 UserId=", reqdata.UserId, " err=", err.Error())
		// Redis错误不影响主流程，只记录日志
	}

	// 收集所有被禁止的厂商（用于踢出用户）
	allBlockedBrands := make(map[string]bool)
	for _, brands := range reqdata.TypeBrands {
		for _, brand := range brands {
			allBlockedBrands[brand] = true
		}
	}
	currentGame := ""
	kickData := map[string]interface{}{
		"Reason":       "game_brand_blocked",
		"CurrentBrand": currentGame,
		"Message":      "暂时不能进入，请联系客服",
	}
	err = userbrand.PublishToUser(reqdata.UserId, "kick_game", kickData)
	if err != nil {
		logs.Error("发送踢出消息失 败 UserId=", reqdata.UserId, " err=", err.Error())
	}

	ctx.RespOK()
	server.WriteAdminLog("设置玩家禁止游戏厂商", ctx, reqdata)
}

// 查询玩家禁止的游戏厂商
func (c *UserGameBrandController) getBlockedGameBrands(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		UserId int `json:"UserId" validate:"required"`
	}{}

	// 获取请求数据并验证权限 // 查询禁止游戏厂商 查询玩家列表
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "查", "查询玩家列表")
	if token == nil {
		return
	}

	// 验证运营商权限
	if ctx.RespErrString(token.SellerId > 0, &errcode, "运营商不正确") {
		return
	}

	// 硬编码游戏类型名称映射
	gameTypeNames := map[int32]string{
		1: "电子",
		2: "棋牌",
		3: "小游戏",
		4: "彩票",
		5: "真人",
		6: "体育",
		7: "德州扑克",
	}

	// 使用GormDao操作数据库
	db := server.Db().GormDao()

	// 从Redis获取用户禁止的游戏配置
	blockedConfigJson, err := userbrand.GetUserBlockedGameBrands(reqdata.UserId)
	if err != nil {
		logs.Error("从Redis获取用户禁止游戏配置失败 UserId=", reqdata.UserId, " err=", err.Error())
		// Redis获取失败，尝试从数据库获取
		blockedConfigJson = ""
	}

	if blockedConfigJson == "" {
		// 如果Redis中没有，从数据库获取
		var userMore struct {
			BlockedGameBrands string `gorm:"column:BlockedGameBrands"`
		}

		err = db.Table("x_user_more").
			Select("BlockedGameBrands").
			Where("UserId = ?", reqdata.UserId).
			First(&userMore).Error

		if err != nil {
			logs.Info("用户扩展记录不存在 UserId=", reqdata.UserId, " err=", err.Error())
			// 如果记录不存在，返回空数组（没有被禁止的厂商）
			ctx.RespOK([]map[string]interface{}{})
			return
		}

		blockedConfigJson = userMore.BlockedGameBrands

		// 更新Redis缓存
		if blockedConfigJson != "" {
			err = userbrand.SetUserBlockedGameBrands(reqdata.UserId, blockedConfigJson)
			if err != nil {
				logs.Error("更新Redis缓存失败 UserId=", reqdata.UserId, " err=", err.Error())
				// Redis更新失败不影响主流程
			}
		}
	}

	// 如果没有禁止配置，直接返回空数组
	if blockedConfigJson == "" {
		ctx.RespOK([]map[string]interface{}{})
		return
	}

	// 解析用户禁止的游戏配置
	var blockedConfig struct {
		TypeBrands map[string][]string `json:"typeBrands"` // 按类型分组的禁止厂商
	}

	err = json.Unmarshal([]byte(blockedConfigJson), &blockedConfig)
	if err != nil {
		logs.Error("解析用户禁止游戏配置JSON失败 UserId=", reqdata.UserId, " json=", blockedConfigJson, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "解析游戏禁止配置失败")
		return
	}

	// 初始化空值
	if blockedConfig.TypeBrands == nil {
		ctx.RespOK([]map[string]interface{}{})
		return
	}

	// 收集所有被禁止的厂商
	blockedBrandsByType := make(map[int32][]string)
	for gameTypeStr, brands := range blockedConfig.TypeBrands {
		gameType := int32(0)
		if gt, err := strconv.ParseInt(gameTypeStr, 10, 32); err == nil {
			gameType = int32(gt)
		}

		if gameType > 0 && len(brands) > 0 {
			blockedBrandsByType[gameType] = brands
		}
	}

	// 如果没有被禁止的厂商，返回空数组
	if len(blockedBrandsByType) == 0 {
		ctx.RespOK([]map[string]interface{}{})
		return
	}

	// 查询被禁止厂商的详细信息
	var result []map[string]interface{}
	for gameType, brands := range blockedBrandsByType {
		typeName, exists := gameTypeNames[gameType]
		if !exists {
			typeName = "未知类型"
		}

		// 查询该类型下被禁止厂商的详细信息
		var gameBrands []struct {
			Brand     string `gorm:"column:Brand"`
			BrandName string `gorm:"column:BrandName"`
		}

		err := db.Table("x_game_brand").
			Select("Brand, BrandName").
			Where("GameType = ? AND Brand IN ? AND Status = ?", gameType, brands, 1).
			Find(&gameBrands).Error

		if err != nil {
			logs.Error("查询游戏厂商详细信息失败 gameType=", gameType, " brands=", brands, " err=", err.Error())
			continue
		}

		// 构建该类型的厂商列表
		if len(gameBrands) > 0 {
			var blockedBrands []map[string]interface{}
			for _, brand := range gameBrands {
				blockedBrands = append(blockedBrands, map[string]interface{}{
					"brand":     brand.Brand,
					"name":      brand.BrandName,
					"isBlocked": true,
				})
			}

			result = append(result, map[string]interface{}{
				"gameType":     gameType,
				"gameTypeName": typeName,
				"brands":       blockedBrands,
			})
		}
	}

	ctx.RespOK(result)
}

// 获取所有游戏厂商列表
func (c *UserGameBrandController) listAllGameBrands(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		UserId int `json:"UserId" validate:"required"`
	}{}

	// 获取请求数据并验证权限 查询游戏厂商列表 查询玩家列表
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "查", "查询玩家列表")
	if token == nil {
		return
	}

	// 使用GormDao从数据库获取游戏厂商列表
	db := server.Db().GormDao()

	// 硬编码游戏类型名称映射
	gameTypeNames := map[int32]string{
		1: "电子",
		2: "棋牌",
		3: "小游戏",
		4: "彩票",
		5: "真人",
		6: "体育",
		7: "德州扑克",
	}

	// 获取所有游戏厂商列表
	var gameBrands []struct {
		GameType  int32  `gorm:"column:GameType"`
		Brand     string `gorm:"column:Brand"`
		BrandName string `gorm:"column:BrandName"`
	}

	err := db.Table("x_game_brand").
		Select("GameType, Brand, BrandName").
		Where("Status = ?", 1).
		Order("GameType ASC, Brand ASC").
		Find(&gameBrands).Error

	if err != nil {
		logs.Error("获取游戏厂商列表失败 err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取游戏厂商列表失败")
		return
	}

	// 获取用户禁止的游戏厂商列表
	blockedBrandsJson, err := userbrand.GetUserBlockedGameBrands(reqdata.UserId)
	if err != nil {
		logs.Error("从Redis获取用户禁止游戏厂商失败 UserId=", reqdata.UserId, " err=", err.Error())
		blockedBrandsJson = ""
	}

	if blockedBrandsJson == "" {
		// 如果Redis中没有，从数据库获取
		var userMore struct {
			BlockedGameBrands string `gorm:"column:BlockedGameBrands"`
		}

		err = db.Table("x_user_more").
			Select("BlockedGameBrands").
			Where("UserId = ?", reqdata.UserId).
			First(&userMore).Error

		if err == nil {
			blockedBrandsJson = userMore.BlockedGameBrands
		}
	}

	// 解析用户禁止的游戏配置
	var typeBrandMap map[int32]map[string]bool = make(map[int32]map[string]bool)

	if blockedBrandsJson != "" {
		// 解析配置格式
		var blockedConfig struct {
			TypeBrands map[string][]string `json:"typeBrands"` // 按类型分组的禁止厂商
		}

		err = json.Unmarshal([]byte(blockedBrandsJson), &blockedConfig)
		if err != nil {
			logs.Error("解析用户禁止游戏配置JSON失败 UserId=", reqdata.UserId, " json=", blockedBrandsJson, " err=", err.Error())
			ctx.RespErrString(true, &errcode, "解析游戏禁止配置失败")
			return
		}

		// 初始化空值
		if blockedConfig.TypeBrands == nil {
			blockedConfig.TypeBrands = map[string][]string{}
		}

		// 处理按类型分组的禁止厂商
		for gameTypeStr, brands := range blockedConfig.TypeBrands {
			gameType := int32(0)
			if gt, err := strconv.ParseInt(gameTypeStr, 10, 32); err == nil {
				gameType = int32(gt)
			}

			if typeBrandMap[gameType] == nil {
				typeBrandMap[gameType] = make(map[string]bool)
			}

			for _, brand := range brands {
				typeBrandMap[gameType][brand] = true
			}
		}
	}

	// 游戏厂商信息结构
	type GameBrandInfo struct {
		Brand     string `json:"brand"`
		Name      string `json:"name"`
		IsBlocked bool   `json:"isBlocked"` // 是否被禁止
	}

	// 游戏类型信息结构
	type GameTypeInfo struct {
		GameType     int32           `json:"gameType"`     // 游戏类型ID
		GameTypeName string          `json:"gameTypeName"` // 游戏类型名称
		Brands       []GameBrandInfo `json:"brands"`       // 该类型下的厂商列表
	}

	// 按游戏类型分组厂商
	gameTypeMap := make(map[int32][]GameBrandInfo)
	for _, brand := range gameBrands {
		// 检查是否被禁止：该类型下特定禁止
		isBlocked := typeBrandMap[brand.GameType] != nil && typeBrandMap[brand.GameType][brand.Brand]

		brandInfo := GameBrandInfo{
			Brand:     brand.Brand,
			Name:      brand.BrandName,
			IsBlocked: isBlocked,
		}
		gameTypeMap[brand.GameType] = append(gameTypeMap[brand.GameType], brandInfo)
	}

	// 构建最终结果 - 只包含有厂商的游戏类型
	var result []GameTypeInfo
	for gameType := int32(1); gameType <= 7; gameType++ {
		brands, exists := gameTypeMap[gameType]
		if exists && len(brands) > 0 {
			typeName, nameExists := gameTypeNames[gameType]
			if !nameExists {
				typeName = "未知类型"
			}

			result = append(result, GameTypeInfo{
				GameType:     gameType,
				GameTypeName: typeName,
				Brands:       brands,
			})
		}
	}

	ctx.RespOK(result)
}
