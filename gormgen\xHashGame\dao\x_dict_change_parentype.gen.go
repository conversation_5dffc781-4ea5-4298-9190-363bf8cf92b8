// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXDictChangeParentype(db *gorm.DB, opts ...gen.DOOption) xDictChangeParentype {
	_xDictChangeParentype := xDictChangeParentype{}

	_xDictChangeParentype.xDictChangeParentypeDo.UseDB(db, opts...)
	_xDictChangeParentype.xDictChangeParentypeDo.UseModel(&model.XDictChangeParentype{})

	tableName := _xDictChangeParentype.xDictChangeParentypeDo.TableName()
	_xDictChangeParentype.ALL = field.NewAsterisk(tableName)
	_xDictChangeParentype.ParentType = field.NewInt32(tableName, "ParentType")
	_xDictChangeParentype.ParentTypeName = field.NewString(tableName, "ParentTypeName")
	_xDictChangeParentype.Memo = field.NewString(tableName, "Memo")
	_xDictChangeParentype.Status = field.NewInt32(tableName, "Status")
	_xDictChangeParentype.CreateTime = field.NewTime(tableName, "CreateTime")
	_xDictChangeParentype.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xDictChangeParentype.fillFieldMap()

	return _xDictChangeParentype
}

// xDictChangeParentype 资金上级类型
type xDictChangeParentype struct {
	xDictChangeParentypeDo xDictChangeParentypeDo

	ALL            field.Asterisk
	ParentType     field.Int32  // 上级分类 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金
	ParentTypeName field.String // 上级分类名 101充提 102哈希游戏 103聊天室 104活动 105三方游戏 106单一钱包 107其他 2001人工彩金
	Memo           field.String // 描述
	Status         field.Int32  // 0无效 1有效
	CreateTime     field.Time   // 创建时间
	UpdateTime     field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xDictChangeParentype) Table(newTableName string) *xDictChangeParentype {
	x.xDictChangeParentypeDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xDictChangeParentype) As(alias string) *xDictChangeParentype {
	x.xDictChangeParentypeDo.DO = *(x.xDictChangeParentypeDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xDictChangeParentype) updateTableName(table string) *xDictChangeParentype {
	x.ALL = field.NewAsterisk(table)
	x.ParentType = field.NewInt32(table, "ParentType")
	x.ParentTypeName = field.NewString(table, "ParentTypeName")
	x.Memo = field.NewString(table, "Memo")
	x.Status = field.NewInt32(table, "Status")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xDictChangeParentype) WithContext(ctx context.Context) *xDictChangeParentypeDo {
	return x.xDictChangeParentypeDo.WithContext(ctx)
}

func (x xDictChangeParentype) TableName() string { return x.xDictChangeParentypeDo.TableName() }

func (x xDictChangeParentype) Alias() string { return x.xDictChangeParentypeDo.Alias() }

func (x xDictChangeParentype) Columns(cols ...field.Expr) gen.Columns {
	return x.xDictChangeParentypeDo.Columns(cols...)
}

func (x *xDictChangeParentype) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xDictChangeParentype) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["ParentType"] = x.ParentType
	x.fieldMap["ParentTypeName"] = x.ParentTypeName
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Status"] = x.Status
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xDictChangeParentype) clone(db *gorm.DB) xDictChangeParentype {
	x.xDictChangeParentypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xDictChangeParentype) replaceDB(db *gorm.DB) xDictChangeParentype {
	x.xDictChangeParentypeDo.ReplaceDB(db)
	return x
}

type xDictChangeParentypeDo struct{ gen.DO }

func (x xDictChangeParentypeDo) Debug() *xDictChangeParentypeDo {
	return x.withDO(x.DO.Debug())
}

func (x xDictChangeParentypeDo) WithContext(ctx context.Context) *xDictChangeParentypeDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xDictChangeParentypeDo) ReadDB() *xDictChangeParentypeDo {
	return x.Clauses(dbresolver.Read)
}

func (x xDictChangeParentypeDo) WriteDB() *xDictChangeParentypeDo {
	return x.Clauses(dbresolver.Write)
}

func (x xDictChangeParentypeDo) Session(config *gorm.Session) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Session(config))
}

func (x xDictChangeParentypeDo) Clauses(conds ...clause.Expression) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xDictChangeParentypeDo) Returning(value interface{}, columns ...string) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xDictChangeParentypeDo) Not(conds ...gen.Condition) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xDictChangeParentypeDo) Or(conds ...gen.Condition) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xDictChangeParentypeDo) Select(conds ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xDictChangeParentypeDo) Where(conds ...gen.Condition) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xDictChangeParentypeDo) Order(conds ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xDictChangeParentypeDo) Distinct(cols ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xDictChangeParentypeDo) Omit(cols ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xDictChangeParentypeDo) Join(table schema.Tabler, on ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xDictChangeParentypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xDictChangeParentypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xDictChangeParentypeDo) Group(cols ...field.Expr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xDictChangeParentypeDo) Having(conds ...gen.Condition) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xDictChangeParentypeDo) Limit(limit int) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xDictChangeParentypeDo) Offset(offset int) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xDictChangeParentypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xDictChangeParentypeDo) Unscoped() *xDictChangeParentypeDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xDictChangeParentypeDo) Create(values ...*model.XDictChangeParentype) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xDictChangeParentypeDo) CreateInBatches(values []*model.XDictChangeParentype, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xDictChangeParentypeDo) Save(values ...*model.XDictChangeParentype) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xDictChangeParentypeDo) First() (*model.XDictChangeParentype, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangeParentype), nil
	}
}

func (x xDictChangeParentypeDo) Take() (*model.XDictChangeParentype, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangeParentype), nil
	}
}

func (x xDictChangeParentypeDo) Last() (*model.XDictChangeParentype, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangeParentype), nil
	}
}

func (x xDictChangeParentypeDo) Find() ([]*model.XDictChangeParentype, error) {
	result, err := x.DO.Find()
	return result.([]*model.XDictChangeParentype), err
}

func (x xDictChangeParentypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XDictChangeParentype, err error) {
	buf := make([]*model.XDictChangeParentype, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xDictChangeParentypeDo) FindInBatches(result *[]*model.XDictChangeParentype, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xDictChangeParentypeDo) Attrs(attrs ...field.AssignExpr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xDictChangeParentypeDo) Assign(attrs ...field.AssignExpr) *xDictChangeParentypeDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xDictChangeParentypeDo) Joins(fields ...field.RelationField) *xDictChangeParentypeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xDictChangeParentypeDo) Preload(fields ...field.RelationField) *xDictChangeParentypeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xDictChangeParentypeDo) FirstOrInit() (*model.XDictChangeParentype, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangeParentype), nil
	}
}

func (x xDictChangeParentypeDo) FirstOrCreate() (*model.XDictChangeParentype, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictChangeParentype), nil
	}
}

func (x xDictChangeParentypeDo) FindByPage(offset int, limit int) (result []*model.XDictChangeParentype, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xDictChangeParentypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xDictChangeParentypeDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xDictChangeParentypeDo) Delete(models ...*model.XDictChangeParentype) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xDictChangeParentypeDo) withDO(do gen.Dao) *xDictChangeParentypeDo {
	x.DO = *do.(*gen.DO)
	return x
}
