package stats

import (
	"time"
	daoModel "xserver/gormgen/xHashGame/model"
)

const (
	// 埋点数据队列Key
	RdsKeyEventList string = "hx_game:event:list"
	// 埋点数据统计任务所Key
	RdsKeyLockStatsTask string = "hx_game:lock:stats_task"
	// 埋点数据幂等Key
	RdsKeyIdemEventId string = "hx_game:lock:event_id:%s"
	// 统计任务类型: PV统计
	Enum_Stats_Task_Type_PV int = 1
	// 统计任务类型: 关键词搜索
	Enum_Stats_Task_Type_KeyWord int = 2
	// 统计任务类型: 活动统计
	Enum_Stats_Task_Type_Activity int = 3
	// 统计任务类型: 充值偏好统计
	Enum_Stats_Task_Type_Recharge int = 4
	// 统计任务类型: 提现偏好统计
	Enum_Stats_Task_Type_Withdrawal int = 5
	// 统计任务类型: 游戏偏好统计
	Enum_Stats_Task_Type_Game int = 6
	// 统计任务类型: 功能交互Button统计
	Enum_Stats_Task_Type_Function_Button int = 7
	// 统计任务类型: 功能交互Tab统计
	Enum_Stats_Task_Type_Function_Tab int = 8
	// 系统类型
	Enum_OS_Type_PC      int32 = 1
	Enum_OS_Type_H5      int32 = 2
	Enum_OS_Type_IOS     int32 = 3
	Enum_OS_Type_Android int32 = 4
	Enum_OS_Type_Other   int32 = 5
	// 网络类型
	Enum_Network_Type_WIFI int32 = 1
	Enum_Network_Type_Flow int32 = 2
)

var (
	Snowflakenode int64
)

// 埋点事件数据list请求
type EventListReq struct {
	Page      int    `json:"page" form:"page"` // 页码
	PageSize  int    `json:"pageSize"`         // 页大小
	EventType int32  `json:"eventType"`        // 事件类型
	EventName string `json:"eventName"`        // 事件名称
}

type ReportEvents struct {
	EventType   int32  `json:"eventType"`   // 事件类型
	EventName   string `json:"eventName"`   // 事件名称
	EventParams string `json:"eventParams"` // 事件参数
}

// 埋点事件数据提交请求
type ReportReq struct {
	SellerId   int32           `json:"sellerId"`   // 运营商
	ChannelId  int32           `json:"channelId"`  // 渠道ID
	TopAgentId int64           `json:"topAgentId"` // 渠道ID
	UserId     int64           `json:"userId"`     // 用户ID
	OsType     int32           `json:"osType"`     // 操作系统类型
	Network    int32           `json:"network"`    // 网络类型
	EventId    string          `json:"eventId"`    // 事件 ID
	Events     []*ReportEvents `json:"events"`     // 数据
}

// PV 列表请求
type PVListReq struct {
	Page       int   `json:"page" form:"page"` // 页码
	PageSize   int   `json:"pageSize"`         // 页大小
	SellerId   int32 `json:"sellerId"`         // 运营商
	ChannelId  int32 `json:"channelId"`        // 渠道ID
	TopAgentId int64 `json:"topAgentId"`       // 渠道ID
	StartTime  int64 `json:"startTime"`        // 开始日期
	EndTime    int64 `json:"endTime"`          // 结束日期
}

// PV 列表请求回应xv
type PVListResp struct {
	StatDate              *time.Time `gorm:"column:stat_date" json:"statDate"`                              // 日期
	SellerId              int32      `gorm:"column:seller_id" json:"sellerId"`                              // 运营商
	ChannelId             int32      `gorm:"column:channel_id" json:"channelId"`                            // 渠道ID
	TopAgentId            int64      `gorm:"column:top_agent_id" json:"topAgentId"`                         // 渠道ID
	PageName              string     `gorm:"column:page_name" json:"pageName"`                              // 网页名称
	VisitCountPC          int32      `gorm:"column:visit_count_pc" json:"visitCountPC"`                     // 访问次数pc
	VisitCountH5          int32      `gorm:"column:visit_count_h5" json:"visitCountH5"`                     // 访问次数h5
	VisitorCountPC        int32      `gorm:"column:visitor_count_pc" json:"visitorCountPC"`                 // 访问人数pc
	VisitorCountH5        int32      `gorm:"column:visitor_count_h5" json:"visitorCountH5"`                 // 访问次数h5
	TotalStayDurationPC   float32    `gorm:"column:total_stay_duration_pc" json:"totalStayDurationPC"`      // 总停留时长(秒)pc
	TotalStayDurationH5   float32    `gorm:"column:total_stay_duration_h5" json:"totalStayDurationH5"`      // 总停留时长(秒)h5
	AvgLoadDurationPCWifi float32    `gorm:"column:avg_load_duration_pc_wifi" json:"avgLoadDurationPCWifi"` // 平均加载时长(秒)pc wifi
	AvgStayDurationH5Flow float32    `gorm:"column:avg_load_duration_pc_flow" json:"avgLoadDurationPCFlow"` // 平均加载时长(秒)pc flow
	AvgLoadDurationH5Wifi float32    `gorm:"column:avg_load_duration_h5_wifi" json:"avgLoadDurationH5Wifi"` // 平均加载时长(秒)h5 wifi
	AvgLoadDurationH5Flow float32    `gorm:"column:avg_load_duration_h5_flow" json:"avgLoadDurationH5Flow"` // 平均加载时长(秒)h5 flow
}

// 人工触发统计请求
type ManualStatsReq struct {
	StatsType int   `json:"statsType"` // 统计类型
	StartTime int64 `json:"startTime"` // 开始日期
	EndTime   int64 `json:"endTime"`   // 结束日期
}

type TempPVStats struct {
	SellerID                int32
	ChannelID               int32
	TopAgentID              int64
	PageName                string
	StatDate                time.Time
	VisitCountPc            int32
	VisitCountH5            int32
	VisitorSetPc            map[string]bool // 不存入 DB
	VisitorSetH5            map[string]bool // 不存入 DB
	TotalStayDurationPc     float32
	TotalStayDurationH5     float32
	CountLoadPcWifi         int32
	CountLoadPcFlow         int32
	CountLoadH5Wifi         int32
	CountLoadH5Flow         int32
	TotalLoadDurationPcWifi float32
	TotalLoadDurationPcFlow float32
	TotalLoadDurationH5Wifi float32
	TotalLoadDurationH5Flow float32
}

func (this *TempPVStats) ToDBModel() *daoModel.XAdsPageDailyStat {
	return &daoModel.XAdsPageDailyStat{
		SellerID:                this.SellerID,
		ChannelID:               this.ChannelID,
		TopAgentID:              this.TopAgentID,
		PageName:                this.PageName,
		StatDate:                this.StatDate,
		VisitCountPc:            this.VisitCountPc,
		VisitCountH5:            this.VisitCountH5,
		VisitorCountPc:          int32(len(this.VisitorSetPc)),
		VisitorCountH5:          int32(len(this.VisitorSetH5)),
		TotalStayDurationPc:     this.TotalStayDurationPc,
		TotalStayDurationH5:     this.TotalStayDurationH5,
		CountLoadPcWifi:         this.CountLoadPcWifi,
		CountLoadPcFlow:         this.CountLoadPcFlow,
		CountLoadH5Wifi:         this.CountLoadH5Wifi,
		CountLoadH5Flow:         this.CountLoadH5Flow,
		TotalLoadDurationPcWifi: this.TotalLoadDurationPcWifi,
		TotalLoadDurationPcFlow: this.TotalLoadDurationPcFlow,
		TotalLoadDurationH5Wifi: this.TotalLoadDurationH5Wifi,
		TotalLoadDurationH5Flow: this.TotalLoadDurationH5Flow,
		AvgLoadDurationPcWifi:   this.CalcAvgLoad(this.CountLoadPcWifi, this.TotalLoadDurationPcWifi),
		AvgLoadDurationPcFlow:   this.CalcAvgLoad(this.CountLoadPcFlow, this.TotalLoadDurationPcFlow),
		AvgLoadDurationH5Wifi:   this.CalcAvgLoad(this.CountLoadH5Wifi, this.TotalLoadDurationH5Wifi),
		AvgLoadDurationH5Flow:   this.CalcAvgLoad(this.CountLoadH5Flow, this.TotalLoadDurationH5Flow),
	}
}

func (t *TempPVStats) CalcAvgLoad(count int32, total float32) float32 {
	if count > 0 {
		return total / float32(count)
	}
	return 0
}

// 关键词搜索列表请求
type QueryKeywordListReq struct {
	Page       int   `json:"page" form:"page"` // 页码
	PageSize   int   `json:"pageSize"`         // 页大小
	SellerId   int32 `json:"sellerId"`         // 运营商
	ChannelId  int32 `json:"channelId"`        // 渠道ID
	TopAgentId int64 `json:"topAgentId"`       // 渠道ID
	StartTime  int64 `json:"startTime"`        // 开始日期
	EndTime    int64 `json:"endTime"`          // 结束日期
}

// 关键词搜索列表请求回应
type QueryKeywordListResp struct {
	StatDate   *time.Time `gorm:"column:stat_date" json:"statDate"`      // 日期
	SellerId   int32      `gorm:"column:seller_id" json:"sellerId"`      // 运营商
	ChannelId  int32      `gorm:"column:channel_id" json:"channelId"`    // 渠道ID
	TopAgentId int64      `gorm:"column:top_agent_id" json:"topAgentId"` // 渠道ID
	Keyword    string     `gorm:"column:key_word" json:"keyword"`        // 关键词
}

// 活动偏好表请求
type ActivityListReq struct {
	Page       int   `json:"page" form:"page"` // 页码
	PageSize   int   `json:"pageSize"`         // 页大小
	SellerId   int32 `json:"sellerId"`         // 运营商
	ChannelId  int32 `json:"channelId"`        // 渠道ID
	TopAgentId int64 `json:"topAgentId"`       // 渠道ID
	StartTime  int64 `json:"startTime"`        // 开始日期
	EndTime    int64 `json:"endTime"`          // 结束日期
}

// 活动偏好表请求回应
type ActivityListResp struct {
	StatDate           *time.Time `gorm:"column:stat_date" json:"statDate"`                      // 日期
	SellerId           int32      `gorm:"column:seller_id" json:"sellerId"`                      // 运营商
	ChannelId          int32      `gorm:"column:channel_id" json:"channelId"`                    // 渠道ID
	TopAgentId         int64      `gorm:"column:top_agent_id" json:"topAgentId"`                 // 渠道ID
	ActiveName         string     `gorm:"column:active_name" json:"activeName"`                  // 活动名称
	ClickCountPC       int32      `gorm:"column:click_count_pc" json:"clickCountPC"`             // 点击次数pc
	ClickCountH5       int32      `gorm:"column:click_count_h5" json:"clickCountH5"`             // 点击次数h5
	JoinCount          int32      `gorm:"column:join_count" json:"joinCount"`                    // 参入人数
	CompletionCount    int32      `gorm:"column:completion_count" json:"completionCount"`        // 完成任务人数
	ReceivedCount      int32      `gorm:"column:received_count" json:"receivedCount"`            // 领取奖励人数
	ClickCTR           float32    `gorm:"column:click_ctr" json:"clickCTR"`                      // 点击转化率
	ActiveTAR          float32    `gorm:"column:active_tar" json:"activeTAR"`                    // 任务完成率
	TotalPayoutAmountU float32    `gorm:"column:total_payout_amountU" json:"totalPayoutAmountU"` // 总派彩U
	TotalPayoutAmountT float32    `gorm:"column:total_payout_amountT" json:"totalPayoutAmountT"` // 总派彩T
}

// 充值偏好表请求
type RechargeListReq struct {
	Page         int   `json:"page" form:"page"` // 页码
	PageSize     int   `json:"pageSize"`         // 页大小
	SellerId     int32 `json:"sellerId"`         // 运营商
	ChannelId    int32 `json:"channelId"`        // 渠道ID
	TopAgentId   int64 `json:"topAgentId"`       // 渠道ID
	RechargeType int32 `json:"rechargeType"`     // 充值类型: 1 -- 法币  2 -- 加密货币
	StartTime    int64 `json:"startTime"`        // 开始日期
	EndTime      int64 `json:"endTime"`          // 结束日期
}

// 充值偏好表请求回应
type RechargeListResp struct {
	StatDate                *time.Time `gorm:"column:stat_date" json:"statDate"`                                 // 日期
	SellerId                int32      `gorm:"column:seller_id" json:"sellerId"`                                 // 运营商
	ChannelId               int32      `gorm:"column:channel_id" json:"channelId"`                               // 渠道ID
	TopAgentId              int64      `gorm:"column:top_agent_id" json:"topAgentId"`                            // 渠道ID
	RechargeChannel         string     `gorm:"column:recharge_channel" json:"recharge_channel"`                  // 充值渠道
	RechargeType            int32      `gorm:"column:recharge_type" json:"rechargeType"`                         // 充值类型: 1:法币 2:加密货币
	RechargeRatePC          float32    `gorm:"column:recharge_rate_pc" json:"rechargeRatePC"`                    // 充值占比pc
	RechargeRateH5          float32    `gorm:"column:recharge_rate_h5" json:"rechargeRateH5"`                    // 充值占比h5
	RechargeSuccessRatePC   float32    `gorm:"column:recharge_success_rate_pc" json:"rechargeSuccessRatePC"`     // 充值成功率 pc
	RechargeSuccessRateH5   float32    `gorm:"column:recharge_success_rate_h5" json:"rechargeSuccessRateH5"`     // 充值成功率 h5
	RechargeCount100PC      int32      `gorm:"column:recharge_count_100_pc" json:"rechargeCount100PC"`           // 充值≤100 U次数 pc
	RechargeCount100H5      int32      `gorm:"column:recharge_count_100_h5" json:"rechargeCount100H5"`           // 充值≤100 U次数 h5
	RechargeCount100_500PC  int32      `gorm:"column:recharge_count_100_500_pc" json:"rechargeCount100_500PC"`   // 充值100-500 U次数 pc
	RechargeCount100_500H5  int32      `gorm:"column:recharge_count_100_500_h5" json:"rechargeCount100_500H5"`   // 充值100-500 U次数 h5
	RechargeCount500_1000PC int32      `gorm:"column:recharge_count_500_1000_pc" json:"rechargeCount500_1000PC"` // 充值500-1000 U次数 pc
	RechargeCount500_1000H5 int32      `gorm:"column:recharge_count_500_1000_h5" json:"rechargeCount500_1000H5"` // 充值100-1000 U次数 h5
	RechargeCount1000PC     int32      `gorm:"column:recharge_count_1000_pc" json:"rechargeCount1000PC"`         // 充值>1000 U次数 pc
	RechargeCount1000H5     int32      `gorm:"column:recharge_count_1000_h5" json:"rechargeCount1000H5"`         // 充值>1000 U次数 h5
}

// 提现偏好表请求
type WithdrawalListReq struct {
	Page           int   `json:"page" form:"page"` // 页码
	PageSize       int   `json:"pageSize"`         // 页大小
	SellerId       int32 `json:"sellerId"`         // 运营商
	ChannelId      int32 `json:"channelId"`        // 渠道ID
	TopAgentId     int64 `json:"topAgentId"`       // 渠道ID
	WithdrawalType int32 `json:"withdrawalType"`   // 提现类型: 1 -- 法币  2 -- 加密货币
	StartTime      int64 `json:"startTime"`        // 开始日期
	EndTime        int64 `json:"endTime"`          // 结束日期
}

// 提现偏好表请求回应
type WithdrawalListResp struct {
	StatDate                *time.Time `gorm:"column:stat_date" json:"statDate"`                                 // 日期
	SellerId                int32      `gorm:"column:seller_id" json:"sellerId"`                                 // 运营商
	ChannelId               int32      `gorm:"column:channel_id" json:"channelId"`                               // 渠道ID
	TopAgentId              int64      `gorm:"column:top_agent_id" json:"topAgentId"`                            // 渠道ID
	WithdrawalChannel       string     `gorm:"column:withdrawal_channel" json:"withdrawalChannel"`               // 提现渠道
	WithdrawalType          int32      `gorm:"column:withdrawal_type" json:"withdrawalType"`                     // 提现类型: 1:法币 2:加密货币
	WithdrawalRatePC        float32    `gorm:"column:withdrawal_rate_pc" json:"withdrawalRatePC"`                // 提现占比pc
	WithdrawalRateH5        float32    `gorm:"column:withdrawal_rate_h5" json:"withdrawalRateH5"`                // 提现占比h5
	WithdrawalSuccessRatePC float32    `gorm:"column:withdrawal_success_rate_pc" json:"withdrawalSuccessRatePC"` // 提现成功率 pc
	WithdrawalSuccessRateH5 float32    `gorm:"column:withdrawal_success_rate_h5" json:"withdrawalSuccessRateH5"` // 提现成功率 h5
}

// 游戏偏好表请求
type GameListReq struct {
	Page       int   `json:"page" form:"page"` // 页码
	PageSize   int   `json:"pageSize"`         // 页大小
	SellerId   int32 `json:"sellerId"`         // 运营商
	ChannelId  int32 `json:"channelId"`        // 渠道ID
	TopAgentId int64 `json:"topAgentId"`       // 渠道ID
	StartTime  int64 `json:"startTime"`        // 开始日期
	EndTime    int64 `json:"endTime"`          // 结束日期
}

// 游戏偏好表请求回应s
type GameListResp struct {
	StatDate     *time.Time `gorm:"column:stat_date" json:"statDate"`           // 日期
	SellerId     int32      `gorm:"column:seller_id" json:"sellerId"`           // 运营商
	ChannelId    int32      `gorm:"column:channel_id" json:"channelId"`         // 渠道ID
	TopAgentId   int64      `gorm:"column:top_agent_id" json:"topAgentId"`      // 渠道ID
	GameTag      string     `gorm:"column:game_tag" json:"gameTag"`             // 游戏标签
	GameName     string     `gorm:"column:game_name" json:"gameName"`           // 游戏名称
	GameCompany  string     `gorm:"column:game_company" json:"gameCompany"`     // 游戏厂商
	BetCountUPC  int32      `gorm:"column:bet_count_u_pc" json:"betCountUPC"`   // 下注次数U pc
	BetCountUH5  int32      `gorm:"column:bet_count_u_h5" json:"betCountUH5"`   // 下注次数U h5
	BetCountTPC  int32      `gorm:"column:bet_count_t_pc" json:"betCountTPC"`   // 下注次数T pc
	BetCountTH5  int32      `gorm:"column:bet_count_t_h5" json:"betCountTH5"`   // 下注次数T h5
	BetAmountUPC float32    `gorm:"column:bet_amount_u_pc" json:"betAmountUPC"` // 下注金额U pc
	BetAmountUH5 float32    `gorm:"column:bet_amount_u_h5" json:"betAmountUH5"` // 下注金额U h5
	BetAmountTPC float32    `gorm:"column:bet_amount_t_pc" json:"betAmountTPC"` // 下注金额T pc
	BetAmountTH5 float32    `gorm:"column:bet_amount_t_h5" json:"betAmountTH5"` // 下注金额T h5
}

// 功能交互按钮请求
type FunctionButtonListReq struct {
	Page       int    `json:"page" form:"page"` // 页码
	PageSize   int    `json:"pageSize"`         // 页大小
	SellerId   int32  `json:"sellerId"`         // 运营商
	ChannelId  int32  `json:"channelId"`        // 渠道ID
	TopAgentId int64  `json:"topAgentId"`       // 渠道ID
	ButtonName string `json:"buttonName"`       // 按钮名称
	StartTime  int64  `json:"startTime"`        // 开始日期
	EndTime    int64  `json:"endTime"`          // 结束日期
}

// 功能交互按钮请求回应
type FunctionButtonListResp struct {
	StatDate          *time.Time `gorm:"column:stat_date" json:"statDate"`                     // 日期
	SellerId          int32      `gorm:"column:seller_id" json:"sellerId"`                     // 运营商
	ChannelId         int32      `gorm:"column:channel_id" json:"channelId"`                   // 渠道ID
	TopAgentId        int64      `gorm:"column:top_agent_id" json:"topAgentId"`                // 渠道ID
	ButtonName        string     `gorm:"column:button_name" json:"buttonName"`                 // 按钮名称
	ClickCountPC      int32      `gorm:"column:click_count_pc" json:"clickCountPC"`            // 点击次数 pc
	ClickCountH5      int32      `gorm:"column:click_count_h5" json:"clickCountH5"`            // 点击次数 h5
	AvgResponseTimePC float32    `gorm:"column:avg_response_time_pc" json:"avgResponseTimePC"` // 平均响应时长(s) pc
	AvgResponseTimeH5 float32    `gorm:"column:avg_response_time_h5" json:"avgResponseTimeH5"` // 平均响应时长(s) h5
}

// 功能交互Tab请求
type FunctionTabListReq struct {
	Page       int    `json:"page" form:"page"` // 页码
	PageSize   int    `json:"pageSize"`         // 页大小
	SellerId   int32  `json:"sellerId"`         // 运营商
	ChannelId  int32  `json:"channelId"`        // 渠道ID
	TopAgentId int64  `json:"topAgentId"`       // 渠道ID
	ButtonName string `json:"buttonName"`       // 按钮名称
	TabName    string `json:"tabName"`          // TAB名称
	StartTime  int64  `json:"startTime"`        // 开始日期
	EndTime    int64  `json:"endTime"`          // 结束日期
}

// 功能交互Tab请求回应
type FunctionTabListResp struct {
	StatDate      *time.Time `gorm:"column:stat_date" json:"statDate"`            // 日期
	SellerId      int32      `gorm:"column:seller_id" json:"sellerId"`            // 运营商
	ChannelId     int32      `gorm:"column:channel_id" json:"channelId"`          // 渠道ID
	TopAgentId    int64      `gorm:"column:top_agent_id" json:"topAgentId"`       // 渠道ID
	ButtonName    string     `gorm:"column:button_name" json:"buttonName"`        // 按钮名称
	TabName       string     `gorm:"column:tab_name" json:"tabName"`              // Tab名称
	ClickCountAll int32      `gorm:"column:click_count_all" json:"clickCountAll"` // 点击次数
	TabRateAll    float32    `gorm:"column:tab_rate_all" json:"tabRateAll"`       // 占比: 与所有tab之和的比值
}
