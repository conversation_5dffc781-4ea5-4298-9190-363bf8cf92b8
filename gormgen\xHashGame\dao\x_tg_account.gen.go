// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgAccount(db *gorm.DB, opts ...gen.DOOption) xTgAccount {
	_xTgAccount := xTgAccount{}

	_xTgAccount.xTgAccountDo.UseDB(db, opts...)
	_xTgAccount.xTgAccountDo.UseModel(&model.XTgAccount{})

	tableName := _xTgAccount.xTgAccountDo.TableName()
	_xTgAccount.ALL = field.NewAsterisk(tableName)
	_xTgAccount.ID = field.NewInt32(tableName, "Id")
	_xTgAccount.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgAccount.Phone = field.NewString(tableName, "Phone")
	_xTgAccount.TgUsername = field.NewString(tableName, "TgUsername")
	_xTgAccount.KefuAccount = field.NewString(tableName, "KefuAccount")
	_xTgAccount.Status = field.NewInt32(tableName, "Status")
	_xTgAccount.OperUserAccount = field.NewString(tableName, "OperUserAccount")
	_xTgAccount.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgAccount.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTgAccount.fillFieldMap()

	return _xTgAccount
}

type xTgAccount struct {
	xTgAccountDo xTgAccountDo

	ALL             field.Asterisk
	ID              field.Int32  // 自增id
	SellerID        field.Int32  // 运营商
	Phone           field.String // 注册tg时使用的手机号
	TgUsername      field.String // TG号的Username
	KefuAccount     field.String // 客服后台账号（admin_user.Account）
	Status          field.Int32  // TG号状态（1正常 2禁用）
	OperUserAccount field.String // 操作员账号
	CreateTime      field.Time   // 创建时间
	UpdateTime      field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTgAccount) Table(newTableName string) *xTgAccount {
	x.xTgAccountDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgAccount) As(alias string) *xTgAccount {
	x.xTgAccountDo.DO = *(x.xTgAccountDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgAccount) updateTableName(table string) *xTgAccount {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.Phone = field.NewString(table, "Phone")
	x.TgUsername = field.NewString(table, "TgUsername")
	x.KefuAccount = field.NewString(table, "KefuAccount")
	x.Status = field.NewInt32(table, "Status")
	x.OperUserAccount = field.NewString(table, "OperUserAccount")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTgAccount) WithContext(ctx context.Context) *xTgAccountDo {
	return x.xTgAccountDo.WithContext(ctx)
}

func (x xTgAccount) TableName() string { return x.xTgAccountDo.TableName() }

func (x xTgAccount) Alias() string { return x.xTgAccountDo.Alias() }

func (x xTgAccount) Columns(cols ...field.Expr) gen.Columns { return x.xTgAccountDo.Columns(cols...) }

func (x *xTgAccount) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgAccount) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["Phone"] = x.Phone
	x.fieldMap["TgUsername"] = x.TgUsername
	x.fieldMap["KefuAccount"] = x.KefuAccount
	x.fieldMap["Status"] = x.Status
	x.fieldMap["OperUserAccount"] = x.OperUserAccount
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTgAccount) clone(db *gorm.DB) xTgAccount {
	x.xTgAccountDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgAccount) replaceDB(db *gorm.DB) xTgAccount {
	x.xTgAccountDo.ReplaceDB(db)
	return x
}

type xTgAccountDo struct{ gen.DO }

func (x xTgAccountDo) Debug() *xTgAccountDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgAccountDo) WithContext(ctx context.Context) *xTgAccountDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgAccountDo) ReadDB() *xTgAccountDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgAccountDo) WriteDB() *xTgAccountDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgAccountDo) Session(config *gorm.Session) *xTgAccountDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgAccountDo) Clauses(conds ...clause.Expression) *xTgAccountDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgAccountDo) Returning(value interface{}, columns ...string) *xTgAccountDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgAccountDo) Not(conds ...gen.Condition) *xTgAccountDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgAccountDo) Or(conds ...gen.Condition) *xTgAccountDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgAccountDo) Select(conds ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgAccountDo) Where(conds ...gen.Condition) *xTgAccountDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgAccountDo) Order(conds ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgAccountDo) Distinct(cols ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgAccountDo) Omit(cols ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgAccountDo) Join(table schema.Tabler, on ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgAccountDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgAccountDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgAccountDo) Group(cols ...field.Expr) *xTgAccountDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgAccountDo) Having(conds ...gen.Condition) *xTgAccountDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgAccountDo) Limit(limit int) *xTgAccountDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgAccountDo) Offset(offset int) *xTgAccountDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgAccountDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgAccountDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgAccountDo) Unscoped() *xTgAccountDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgAccountDo) Create(values ...*model.XTgAccount) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgAccountDo) CreateInBatches(values []*model.XTgAccount, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgAccountDo) Save(values ...*model.XTgAccount) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgAccountDo) First() (*model.XTgAccount, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgAccount), nil
	}
}

func (x xTgAccountDo) Take() (*model.XTgAccount, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgAccount), nil
	}
}

func (x xTgAccountDo) Last() (*model.XTgAccount, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgAccount), nil
	}
}

func (x xTgAccountDo) Find() ([]*model.XTgAccount, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgAccount), err
}

func (x xTgAccountDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgAccount, err error) {
	buf := make([]*model.XTgAccount, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgAccountDo) FindInBatches(result *[]*model.XTgAccount, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgAccountDo) Attrs(attrs ...field.AssignExpr) *xTgAccountDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgAccountDo) Assign(attrs ...field.AssignExpr) *xTgAccountDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgAccountDo) Joins(fields ...field.RelationField) *xTgAccountDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgAccountDo) Preload(fields ...field.RelationField) *xTgAccountDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgAccountDo) FirstOrInit() (*model.XTgAccount, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgAccount), nil
	}
}

func (x xTgAccountDo) FirstOrCreate() (*model.XTgAccount, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgAccount), nil
	}
}

func (x xTgAccountDo) FindByPage(offset int, limit int) (result []*model.XTgAccount, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgAccountDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgAccountDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgAccountDo) Delete(models ...*model.XTgAccount) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgAccountDo) withDO(do gen.Dao) *xTgAccountDo {
	x.DO = *do.(*gen.DO)
	return x
}
