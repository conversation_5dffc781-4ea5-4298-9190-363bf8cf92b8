// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentCommissionTeamDefine(db *gorm.DB, opts ...gen.DOOption) xAgentCommissionTeamDefine {
	_xAgentCommissionTeamDefine := xAgentCommissionTeamDefine{}

	_xAgentCommissionTeamDefine.xAgentCommissionTeamDefineDo.UseDB(db, opts...)
	_xAgentCommissionTeamDefine.xAgentCommissionTeamDefineDo.UseModel(&model.XAgentCommissionTeamDefine{})

	tableName := _xAgentCommissionTeamDefine.xAgentCommissionTeamDefineDo.TableName()
	_xAgentCommissionTeamDefine.ALL = field.NewAsterisk(tableName)
	_xAgentCommissionTeamDefine.SchemeID = field.NewInt32(tableName, "SchemeId")
	_xAgentCommissionTeamDefine.TeamLevel = field.NewInt32(tableName, "TeamLevel")
	_xAgentCommissionTeamDefine.TotalValidUsers = field.NewInt32(tableName, "TotalValidUsers")
	_xAgentCommissionTeamDefine.TotalValidLiuShui = field.NewFloat64(tableName, "TotalValidLiuShui")
	_xAgentCommissionTeamDefine.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentCommissionTeamDefine.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentCommissionTeamDefine.fillFieldMap()

	return _xAgentCommissionTeamDefine
}

// xAgentCommissionTeamDefine 三级返佣团队定义
type xAgentCommissionTeamDefine struct {
	xAgentCommissionTeamDefineDo xAgentCommissionTeamDefineDo

	ALL               field.Asterisk
	SchemeID          field.Int32   // 方案Id
	TeamLevel         field.Int32   // 团队等级
	TotalValidUsers   field.Int32   // 总有效会员数
	TotalValidLiuShui field.Float64 // 总有效流水
	CreateTime        field.Time    // 创建时间
	UpdateTime        field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentCommissionTeamDefine) Table(newTableName string) *xAgentCommissionTeamDefine {
	x.xAgentCommissionTeamDefineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentCommissionTeamDefine) As(alias string) *xAgentCommissionTeamDefine {
	x.xAgentCommissionTeamDefineDo.DO = *(x.xAgentCommissionTeamDefineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentCommissionTeamDefine) updateTableName(table string) *xAgentCommissionTeamDefine {
	x.ALL = field.NewAsterisk(table)
	x.SchemeID = field.NewInt32(table, "SchemeId")
	x.TeamLevel = field.NewInt32(table, "TeamLevel")
	x.TotalValidUsers = field.NewInt32(table, "TotalValidUsers")
	x.TotalValidLiuShui = field.NewFloat64(table, "TotalValidLiuShui")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentCommissionTeamDefine) WithContext(ctx context.Context) *xAgentCommissionTeamDefineDo {
	return x.xAgentCommissionTeamDefineDo.WithContext(ctx)
}

func (x xAgentCommissionTeamDefine) TableName() string {
	return x.xAgentCommissionTeamDefineDo.TableName()
}

func (x xAgentCommissionTeamDefine) Alias() string { return x.xAgentCommissionTeamDefineDo.Alias() }

func (x xAgentCommissionTeamDefine) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentCommissionTeamDefineDo.Columns(cols...)
}

func (x *xAgentCommissionTeamDefine) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentCommissionTeamDefine) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["SchemeId"] = x.SchemeID
	x.fieldMap["TeamLevel"] = x.TeamLevel
	x.fieldMap["TotalValidUsers"] = x.TotalValidUsers
	x.fieldMap["TotalValidLiuShui"] = x.TotalValidLiuShui
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentCommissionTeamDefine) clone(db *gorm.DB) xAgentCommissionTeamDefine {
	x.xAgentCommissionTeamDefineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentCommissionTeamDefine) replaceDB(db *gorm.DB) xAgentCommissionTeamDefine {
	x.xAgentCommissionTeamDefineDo.ReplaceDB(db)
	return x
}

type xAgentCommissionTeamDefineDo struct{ gen.DO }

func (x xAgentCommissionTeamDefineDo) Debug() *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentCommissionTeamDefineDo) WithContext(ctx context.Context) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentCommissionTeamDefineDo) ReadDB() *xAgentCommissionTeamDefineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentCommissionTeamDefineDo) WriteDB() *xAgentCommissionTeamDefineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentCommissionTeamDefineDo) Session(config *gorm.Session) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentCommissionTeamDefineDo) Clauses(conds ...clause.Expression) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentCommissionTeamDefineDo) Returning(value interface{}, columns ...string) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentCommissionTeamDefineDo) Not(conds ...gen.Condition) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentCommissionTeamDefineDo) Or(conds ...gen.Condition) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentCommissionTeamDefineDo) Select(conds ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentCommissionTeamDefineDo) Where(conds ...gen.Condition) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentCommissionTeamDefineDo) Order(conds ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentCommissionTeamDefineDo) Distinct(cols ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentCommissionTeamDefineDo) Omit(cols ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentCommissionTeamDefineDo) Join(table schema.Tabler, on ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentCommissionTeamDefineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentCommissionTeamDefineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentCommissionTeamDefineDo) Group(cols ...field.Expr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentCommissionTeamDefineDo) Having(conds ...gen.Condition) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentCommissionTeamDefineDo) Limit(limit int) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentCommissionTeamDefineDo) Offset(offset int) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentCommissionTeamDefineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentCommissionTeamDefineDo) Unscoped() *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentCommissionTeamDefineDo) Create(values ...*model.XAgentCommissionTeamDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentCommissionTeamDefineDo) CreateInBatches(values []*model.XAgentCommissionTeamDefine, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentCommissionTeamDefineDo) Save(values ...*model.XAgentCommissionTeamDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentCommissionTeamDefineDo) First() (*model.XAgentCommissionTeamDefine, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionTeamDefine), nil
	}
}

func (x xAgentCommissionTeamDefineDo) Take() (*model.XAgentCommissionTeamDefine, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionTeamDefine), nil
	}
}

func (x xAgentCommissionTeamDefineDo) Last() (*model.XAgentCommissionTeamDefine, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionTeamDefine), nil
	}
}

func (x xAgentCommissionTeamDefineDo) Find() ([]*model.XAgentCommissionTeamDefine, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentCommissionTeamDefine), err
}

func (x xAgentCommissionTeamDefineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentCommissionTeamDefine, err error) {
	buf := make([]*model.XAgentCommissionTeamDefine, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentCommissionTeamDefineDo) FindInBatches(result *[]*model.XAgentCommissionTeamDefine, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentCommissionTeamDefineDo) Attrs(attrs ...field.AssignExpr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentCommissionTeamDefineDo) Assign(attrs ...field.AssignExpr) *xAgentCommissionTeamDefineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentCommissionTeamDefineDo) Joins(fields ...field.RelationField) *xAgentCommissionTeamDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentCommissionTeamDefineDo) Preload(fields ...field.RelationField) *xAgentCommissionTeamDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentCommissionTeamDefineDo) FirstOrInit() (*model.XAgentCommissionTeamDefine, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionTeamDefine), nil
	}
}

func (x xAgentCommissionTeamDefineDo) FirstOrCreate() (*model.XAgentCommissionTeamDefine, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionTeamDefine), nil
	}
}

func (x xAgentCommissionTeamDefineDo) FindByPage(offset int, limit int) (result []*model.XAgentCommissionTeamDefine, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentCommissionTeamDefineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentCommissionTeamDefineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentCommissionTeamDefineDo) Delete(models ...*model.XAgentCommissionTeamDefine) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentCommissionTeamDefineDo) withDO(do gen.Dao) *xAgentCommissionTeamDefineDo {
	x.DO = *do.(*gen.DO)
	return x
}
