package msg

import (
	"fmt"
	"time"
	"xserver/abugo"
	"xserver/controller/msg/model"
	"xserver/server"
)

// MessageManageService 消息管理接口
type MessageManageService interface {
	// 获取用户消息列表
	GetUserMessages(userId int64, page, pageSize int) ([]model.MessageRecord, int, error)
	// 标记消息为已读
	MarkMessageAsRead(messageId int64, userId int64) error
	// 删除用户消息
	DeleteUserMessage(messageId int64, userId int64) error
	// 恢复已删除的用户消息
	RestoreUserMessage(messageId int64, userId int64) error
	// 获取用户未读消息数
	GetUserUnreadCount(userId int64) (int, error)
}

// messageManageServiceImpl 消息管理服务实现
type messageManageServiceImpl struct {
	db *abugo.AbuDb
}

// GetUserMessages 获取用户消息列表
func (s *messageManageServiceImpl) GetUserMessages(userId int64, page, pageSize int) ([]model.MessageRecord, int, error) {
	var messages []model.MessageRecord
	var total int64

	offset := (page - 1) * pageSize

	// 查询总数，只计算未删除的消息
	result := server.Db().GormDao().Model(&model.MessageRecord{}).
		Where("UserId = ? AND IsDeleted = 0", userId).
		Count(&total)
	if result.Error != nil {
		return nil, 0, fmt.Errorf("查询消息总数失败: %w", result.Error)
	}

	// 查询消息列表，只返回未删除的消息
	result = server.Db().GormDao().Where("UserId = ? AND IsDeleted = 0", userId).
		Order("SentAt DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&messages)
	if result.Error != nil {
		return nil, 0, fmt.Errorf("查询消息列表失败: %w", result.Error)
	}

	return messages, int(total), nil
}

// MarkMessageAsRead 标记消息为已读
func (s *messageManageServiceImpl) MarkMessageAsRead(messageId int64, userId int64) error {
	result := server.Db().GormDao().Model(&model.MessageRecord{}).
		Where("Id = ? AND UserId = ?", messageId, userId).
		Updates(map[string]interface{}{
			"IsRead": 1,
			"ReadAt": time.Now(),
		})

	if result.Error != nil {
		return fmt.Errorf("标记消息已读失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("消息不存在或不属于该用户")
	}

	return nil
}

// DeleteUserMessage 假删除用户消息
func (s *messageManageServiceImpl) DeleteUserMessage(messageId int64, userId int64) error {
	result := server.Db().GormDao().Model(&model.MessageRecord{}).
		Where("Id = ? AND UserId = ?", messageId, userId).
		Updates(map[string]interface{}{
			"IsDeleted": 1,
			"DeletedAt": time.Now(),
		})

	if result.Error != nil {
		return fmt.Errorf("删除消息失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("消息不存在或不属于该用户")
	}

	return nil
}

// RestoreUserMessage 恢复已删除的用户消息
func (s *messageManageServiceImpl) RestoreUserMessage(messageId int64, userId int64) error {
	result := server.Db().GormDao().Model(&model.MessageRecord{}).
		Where("Id = ? AND UserId = ? AND IsDeleted = 1", messageId, userId).
		Updates(map[string]interface{}{
			"IsDeleted": 0,
			"DeletedAt": nil,
		})

	if result.Error != nil {
		return fmt.Errorf("恢复消息失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("消息不存在、不属于该用户或未被删除")
	}

	return nil
}

// GetUserUnreadCount 获取用户未读消息数
func (s *messageManageServiceImpl) GetUserUnreadCount(userId int64) (int, error) {
	var count int64
	result := server.Db().GormDao().Model(&model.MessageRecord{}).
		Where("UserId = ? AND IsRead = 0 AND IsDeleted = 0", userId).
		Count(&count)

	if result.Error != nil {
		return 0, fmt.Errorf("获取未读消息数失败: %w", result.Error)
	}

	return int(count), nil
}

// NewMessageManageService 创建消息管理服务实例
func NewMessageManageService() MessageManageService {
	return &messageManageServiceImpl{
		db: server.Db(),
	}
}
