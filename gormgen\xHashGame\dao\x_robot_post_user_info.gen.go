// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotPostUserInfo(db *gorm.DB, opts ...gen.DOOption) xRobotPostUserInfo {
	_xRobotPostUserInfo := xRobotPostUserInfo{}

	_xRobotPostUserInfo.xRobotPostUserInfoDo.UseDB(db, opts...)
	_xRobotPostUserInfo.xRobotPostUserInfoDo.UseModel(&model.XRobotPostUserInfo{})

	tableName := _xRobotPostUserInfo.xRobotPostUserInfoDo.TableName()
	_xRobotPostUserInfo.ALL = field.NewAsterisk(tableName)
	_xRobotPostUserInfo.ID = field.NewInt64(tableName, "id")
	_xRobotPostUserInfo.ChatID = field.NewInt64(tableName, "chat_id")
	_xRobotPostUserInfo.UserName = field.NewString(tableName, "user_name")
	_xRobotPostUserInfo.PhoneNumber = field.NewString(tableName, "phone_number")
	_xRobotPostUserInfo.Type = field.NewInt32(tableName, "type")
	_xRobotPostUserInfo.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotPostUserInfo.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotPostUserInfo.fillFieldMap()

	return _xRobotPostUserInfo
}

type xRobotPostUserInfo struct {
	xRobotPostUserInfoDo xRobotPostUserInfoDo

	ALL         field.Asterisk
	ID          field.Int64  // pk
	ChatID      field.Int64  // 飞机ID
	UserName    field.String // 用户名
	PhoneNumber field.String // 手机号
	Type        field.Int32  // 类型：拉人0，私信1，采集2
	CreateTime  field.Time   // 创建
	UpdateTime  field.Time   // 更新

	fieldMap map[string]field.Expr
}

func (x xRobotPostUserInfo) Table(newTableName string) *xRobotPostUserInfo {
	x.xRobotPostUserInfoDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotPostUserInfo) As(alias string) *xRobotPostUserInfo {
	x.xRobotPostUserInfoDo.DO = *(x.xRobotPostUserInfoDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotPostUserInfo) updateTableName(table string) *xRobotPostUserInfo {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.ChatID = field.NewInt64(table, "chat_id")
	x.UserName = field.NewString(table, "user_name")
	x.PhoneNumber = field.NewString(table, "phone_number")
	x.Type = field.NewInt32(table, "type")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotPostUserInfo) WithContext(ctx context.Context) *xRobotPostUserInfoDo {
	return x.xRobotPostUserInfoDo.WithContext(ctx)
}

func (x xRobotPostUserInfo) TableName() string { return x.xRobotPostUserInfoDo.TableName() }

func (x xRobotPostUserInfo) Alias() string { return x.xRobotPostUserInfoDo.Alias() }

func (x xRobotPostUserInfo) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotPostUserInfoDo.Columns(cols...)
}

func (x *xRobotPostUserInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotPostUserInfo) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 7)
	x.fieldMap["id"] = x.ID
	x.fieldMap["chat_id"] = x.ChatID
	x.fieldMap["user_name"] = x.UserName
	x.fieldMap["phone_number"] = x.PhoneNumber
	x.fieldMap["type"] = x.Type
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotPostUserInfo) clone(db *gorm.DB) xRobotPostUserInfo {
	x.xRobotPostUserInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotPostUserInfo) replaceDB(db *gorm.DB) xRobotPostUserInfo {
	x.xRobotPostUserInfoDo.ReplaceDB(db)
	return x
}

type xRobotPostUserInfoDo struct{ gen.DO }

func (x xRobotPostUserInfoDo) Debug() *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotPostUserInfoDo) WithContext(ctx context.Context) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotPostUserInfoDo) ReadDB() *xRobotPostUserInfoDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotPostUserInfoDo) WriteDB() *xRobotPostUserInfoDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotPostUserInfoDo) Session(config *gorm.Session) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotPostUserInfoDo) Clauses(conds ...clause.Expression) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotPostUserInfoDo) Returning(value interface{}, columns ...string) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotPostUserInfoDo) Not(conds ...gen.Condition) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotPostUserInfoDo) Or(conds ...gen.Condition) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotPostUserInfoDo) Select(conds ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotPostUserInfoDo) Where(conds ...gen.Condition) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotPostUserInfoDo) Order(conds ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotPostUserInfoDo) Distinct(cols ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotPostUserInfoDo) Omit(cols ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotPostUserInfoDo) Join(table schema.Tabler, on ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotPostUserInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotPostUserInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotPostUserInfoDo) Group(cols ...field.Expr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotPostUserInfoDo) Having(conds ...gen.Condition) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotPostUserInfoDo) Limit(limit int) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotPostUserInfoDo) Offset(offset int) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotPostUserInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotPostUserInfoDo) Unscoped() *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotPostUserInfoDo) Create(values ...*model.XRobotPostUserInfo) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotPostUserInfoDo) CreateInBatches(values []*model.XRobotPostUserInfo, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotPostUserInfoDo) Save(values ...*model.XRobotPostUserInfo) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotPostUserInfoDo) First() (*model.XRobotPostUserInfo, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPostUserInfo), nil
	}
}

func (x xRobotPostUserInfoDo) Take() (*model.XRobotPostUserInfo, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPostUserInfo), nil
	}
}

func (x xRobotPostUserInfoDo) Last() (*model.XRobotPostUserInfo, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPostUserInfo), nil
	}
}

func (x xRobotPostUserInfoDo) Find() ([]*model.XRobotPostUserInfo, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotPostUserInfo), err
}

func (x xRobotPostUserInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotPostUserInfo, err error) {
	buf := make([]*model.XRobotPostUserInfo, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotPostUserInfoDo) FindInBatches(result *[]*model.XRobotPostUserInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotPostUserInfoDo) Attrs(attrs ...field.AssignExpr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotPostUserInfoDo) Assign(attrs ...field.AssignExpr) *xRobotPostUserInfoDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotPostUserInfoDo) Joins(fields ...field.RelationField) *xRobotPostUserInfoDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotPostUserInfoDo) Preload(fields ...field.RelationField) *xRobotPostUserInfoDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotPostUserInfoDo) FirstOrInit() (*model.XRobotPostUserInfo, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPostUserInfo), nil
	}
}

func (x xRobotPostUserInfoDo) FirstOrCreate() (*model.XRobotPostUserInfo, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPostUserInfo), nil
	}
}

func (x xRobotPostUserInfoDo) FindByPage(offset int, limit int) (result []*model.XRobotPostUserInfo, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotPostUserInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotPostUserInfoDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotPostUserInfoDo) Delete(models ...*model.XRobotPostUserInfo) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotPostUserInfoDo) withDO(do gen.Dao) *xRobotPostUserInfoDo {
	x.DO = *do.(*gen.DO)
	return x
}
