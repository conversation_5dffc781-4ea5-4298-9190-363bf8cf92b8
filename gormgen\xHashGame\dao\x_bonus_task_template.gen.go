// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXBonusTaskTemplate(db *gorm.DB, opts ...gen.DOOption) xBonusTaskTemplate {
	_xBonusTaskTemplate := xBonusTaskTemplate{}

	_xBonusTaskTemplate.xBonusTaskTemplateDo.UseDB(db, opts...)
	_xBonusTaskTemplate.xBonusTaskTemplateDo.UseModel(&model.XBonusTaskTemplate{})

	tableName := _xBonusTaskTemplate.xBonusTaskTemplateDo.TableName()
	_xBonusTaskTemplate.ALL = field.NewAsterisk(tableName)
	_xBonusTaskTemplate.ID = field.NewInt32(tableName, "Id")
	_xBonusTaskTemplate.Typ = field.NewInt32(tableName, "Typ")
	_xBonusTaskTemplate.Nick = field.NewString(tableName, "Nick")
	_xBonusTaskTemplate.Bonus = field.NewFloat64(tableName, "Bonus")
	_xBonusTaskTemplate.LiuShuiOdd = field.NewInt32(tableName, "LiuShuiOdd")
	_xBonusTaskTemplate.CreateTime = field.NewTime(tableName, "CreateTime")
	_xBonusTaskTemplate.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xBonusTaskTemplate.Condition1 = field.NewInt64(tableName, "Condition1")
	_xBonusTaskTemplate.Condition2 = field.NewInt64(tableName, "Condition2")
	_xBonusTaskTemplate.Condition3 = field.NewInt64(tableName, "Condition3")

	_xBonusTaskTemplate.fillFieldMap()

	return _xBonusTaskTemplate
}

type xBonusTaskTemplate struct {
	xBonusTaskTemplateDo xBonusTaskTemplateDo

	ALL        field.Asterisk
	ID         field.Int32
	Typ        field.Int32 // 1、充值 2、流水 3、亏损 4、邀请
	Nick       field.String
	Bonus      field.Float64
	LiuShuiOdd field.Int32 // 几倍流水
	CreateTime field.Time
	UpdateTime field.Time
	Condition1 field.Int64
	Condition2 field.Int64
	Condition3 field.Int64

	fieldMap map[string]field.Expr
}

func (x xBonusTaskTemplate) Table(newTableName string) *xBonusTaskTemplate {
	x.xBonusTaskTemplateDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xBonusTaskTemplate) As(alias string) *xBonusTaskTemplate {
	x.xBonusTaskTemplateDo.DO = *(x.xBonusTaskTemplateDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xBonusTaskTemplate) updateTableName(table string) *xBonusTaskTemplate {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Typ = field.NewInt32(table, "Typ")
	x.Nick = field.NewString(table, "Nick")
	x.Bonus = field.NewFloat64(table, "Bonus")
	x.LiuShuiOdd = field.NewInt32(table, "LiuShuiOdd")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.Condition1 = field.NewInt64(table, "Condition1")
	x.Condition2 = field.NewInt64(table, "Condition2")
	x.Condition3 = field.NewInt64(table, "Condition3")

	x.fillFieldMap()

	return x
}

func (x *xBonusTaskTemplate) WithContext(ctx context.Context) *xBonusTaskTemplateDo {
	return x.xBonusTaskTemplateDo.WithContext(ctx)
}

func (x xBonusTaskTemplate) TableName() string { return x.xBonusTaskTemplateDo.TableName() }

func (x xBonusTaskTemplate) Alias() string { return x.xBonusTaskTemplateDo.Alias() }

func (x xBonusTaskTemplate) Columns(cols ...field.Expr) gen.Columns {
	return x.xBonusTaskTemplateDo.Columns(cols...)
}

func (x *xBonusTaskTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xBonusTaskTemplate) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 10)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Typ"] = x.Typ
	x.fieldMap["Nick"] = x.Nick
	x.fieldMap["Bonus"] = x.Bonus
	x.fieldMap["LiuShuiOdd"] = x.LiuShuiOdd
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["Condition1"] = x.Condition1
	x.fieldMap["Condition2"] = x.Condition2
	x.fieldMap["Condition3"] = x.Condition3
}

func (x xBonusTaskTemplate) clone(db *gorm.DB) xBonusTaskTemplate {
	x.xBonusTaskTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xBonusTaskTemplate) replaceDB(db *gorm.DB) xBonusTaskTemplate {
	x.xBonusTaskTemplateDo.ReplaceDB(db)
	return x
}

type xBonusTaskTemplateDo struct{ gen.DO }

func (x xBonusTaskTemplateDo) Debug() *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Debug())
}

func (x xBonusTaskTemplateDo) WithContext(ctx context.Context) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xBonusTaskTemplateDo) ReadDB() *xBonusTaskTemplateDo {
	return x.Clauses(dbresolver.Read)
}

func (x xBonusTaskTemplateDo) WriteDB() *xBonusTaskTemplateDo {
	return x.Clauses(dbresolver.Write)
}

func (x xBonusTaskTemplateDo) Session(config *gorm.Session) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Session(config))
}

func (x xBonusTaskTemplateDo) Clauses(conds ...clause.Expression) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xBonusTaskTemplateDo) Returning(value interface{}, columns ...string) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xBonusTaskTemplateDo) Not(conds ...gen.Condition) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xBonusTaskTemplateDo) Or(conds ...gen.Condition) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xBonusTaskTemplateDo) Select(conds ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xBonusTaskTemplateDo) Where(conds ...gen.Condition) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xBonusTaskTemplateDo) Order(conds ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xBonusTaskTemplateDo) Distinct(cols ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xBonusTaskTemplateDo) Omit(cols ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xBonusTaskTemplateDo) Join(table schema.Tabler, on ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xBonusTaskTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xBonusTaskTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xBonusTaskTemplateDo) Group(cols ...field.Expr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xBonusTaskTemplateDo) Having(conds ...gen.Condition) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xBonusTaskTemplateDo) Limit(limit int) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xBonusTaskTemplateDo) Offset(offset int) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xBonusTaskTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xBonusTaskTemplateDo) Unscoped() *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xBonusTaskTemplateDo) Create(values ...*model.XBonusTaskTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xBonusTaskTemplateDo) CreateInBatches(values []*model.XBonusTaskTemplate, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xBonusTaskTemplateDo) Save(values ...*model.XBonusTaskTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xBonusTaskTemplateDo) First() (*model.XBonusTaskTemplate, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskTemplate), nil
	}
}

func (x xBonusTaskTemplateDo) Take() (*model.XBonusTaskTemplate, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskTemplate), nil
	}
}

func (x xBonusTaskTemplateDo) Last() (*model.XBonusTaskTemplate, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskTemplate), nil
	}
}

func (x xBonusTaskTemplateDo) Find() ([]*model.XBonusTaskTemplate, error) {
	result, err := x.DO.Find()
	return result.([]*model.XBonusTaskTemplate), err
}

func (x xBonusTaskTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XBonusTaskTemplate, err error) {
	buf := make([]*model.XBonusTaskTemplate, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xBonusTaskTemplateDo) FindInBatches(result *[]*model.XBonusTaskTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xBonusTaskTemplateDo) Attrs(attrs ...field.AssignExpr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xBonusTaskTemplateDo) Assign(attrs ...field.AssignExpr) *xBonusTaskTemplateDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xBonusTaskTemplateDo) Joins(fields ...field.RelationField) *xBonusTaskTemplateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xBonusTaskTemplateDo) Preload(fields ...field.RelationField) *xBonusTaskTemplateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xBonusTaskTemplateDo) FirstOrInit() (*model.XBonusTaskTemplate, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskTemplate), nil
	}
}

func (x xBonusTaskTemplateDo) FirstOrCreate() (*model.XBonusTaskTemplate, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskTemplate), nil
	}
}

func (x xBonusTaskTemplateDo) FindByPage(offset int, limit int) (result []*model.XBonusTaskTemplate, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xBonusTaskTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xBonusTaskTemplateDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xBonusTaskTemplateDo) Delete(models ...*model.XBonusTaskTemplate) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xBonusTaskTemplateDo) withDO(do gen.Dao) *xBonusTaskTemplateDo {
	x.DO = *do.(*gen.DO)
	return x
}
