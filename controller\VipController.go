package controller

import (
	"context"
	"fmt"
	"path"
	"sort"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

type VipController struct {
}

func (c *VipController) Init() {
	group := server.Http().NewGroup("/api/vip")
	{
		group.Post("/vip_define", c.define)
		group.Post("/vip_add", c.vip_add)
		group.Post("/vip_modify", c.vip_modify)
		group.Post("/vip_delete", c.vip_delete)

		group.Post("/vip_reward", c.vip_reward)
		group.Post("/vip_reward_report", c.vip_reward_report)
		group.Post("/vip_user_list", c.vip_user_list)

		group.Post("/vip_user_change_log", c.vip_user_change_log)
	}
	fmt.Println("")
}

func (c *VipController) define(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		Page      int
		PageSize  int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip定义", "查", "查看vip定义")
	if token == nil {
		return
	}
	type XVipGameDefine struct {
		GameID     string  `json:"GameId"` // 游戏Id
		CatID      int32   `json:"CatId"`  // 彩票大类ID
		RewardRate float64 `json:"RewardRate"`
	}

	type ResponseData struct {
		model.XVipDefine
		// XVipGameDefineArr []*XVipGameDefine
	}

	// where := abugo.AbuDbWhere{}
	xVipDefineDb := server.DaoxHashGame().XVipDefine
	query := xVipDefineDb.WithContext(nil)

	if reqdata.SellerId > 0 {
		query = query.Where(xVipDefineDb.SellerID.Eq(int32(reqdata.SellerId)))
	}

	if reqdata.ChannelId > 0 {
		query = query.Where(xVipDefineDb.ChannelID.Eq(int32(reqdata.ChannelId)))
	}

	var limit, offset int

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}

	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15
	}

	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	// total, presult := server.Db().Table("x_vip_define").Where(where).OrderBy("VipLevel ASC").PageData(reqdata.Page, reqdata.PageSize)
	var result []*ResponseData
	total, err := query.Order(xVipDefineDb.VipLevel.Asc()).ScanByPage(&result, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 查询香港六合彩vip返水配置
	// xgWhere := abugo.AbuDbWhere{}
	// xgWhere.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	// xgWhere.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	// xgVipRows, _ := server.Db().Table("x_vip_game_define").Where(where).OrderBy("VipLevel ASC").GetList()
	// xVipResultArr := XVipResultArr{}
	// for i := 0; i < len(*presult); i++ {
	// vipDefine := (*presult)[i]
	// var responseData ResponseData
	// responseData.ID = int32(ChangeInt(vipDefine, "Id"))
	// responseData.SellerID = int32(ChangeInt(vipDefine, "SellerId"))
	// responseData.ChannelID = int32(ChangeInt(vipDefine, "ChannelId"))
	// responseData.VipLevel = int32(ChangeInt(vipDefine, "VipLevel"))
	// responseData.KeepLiuSui = int32(ChangeInt(vipDefine, "KeepLiuSui"))
	// responseData.State = int32(ChangeInt(vipDefine, "State"))
	// responseData.Recharge = int32(ChangeInt(vipDefine, "Recharge"))                     // 累计存款
	// responseData.LiuSui = int32(ChangeInt(vipDefine, "LiuSui"))                         // 累计流水
	// responseData.UpgradeReward = vipDefine["UpgradeReward"].(float64)                   // 升级礼金
	// responseData.MonthlyReward = vipDefine["MonthlyReward"].(float64)                   // 每月礼金
	// responseData.RewardRateHaXi = vipDefine["RewardRateHaXi"].(float64)                 // 哈希返点
	// responseData.RewardRateLottery = vipDefine["RewardRateLottery"].(float64)           // 彩票返点
	// responseData.RewardRateLowLottery = vipDefine["RewardRateLowLottery"].(float64)     // 低频彩返点
	// responseData.RewardRateQiPai = vipDefine["RewardRateQiPai"].(float64)               // 棋牌返点
	// responseData.RewardRateDianZhi = vipDefine["RewardRateDianZhi"].(float64)           // 电子返点
	// responseData.RewardRateXiaoYouXi = vipDefine["RewardRateXiaoYouXi"].(float64)       // 小游戏返点
	// responseData.RewardRateCryptoMarket = vipDefine["RewardRateCryptoMarket"].(float64) // 加密市场返点
	// responseData.RewardRateLive = vipDefine["RewardRateLive"].(float64)                 // 真人返点
	// responseData.RewardRateSport = vipDefine["RewardRateSport"].(float64)               // 体育返点
	// responseData.RewardRateTexas = vipDefine["RewardRateTexas"].(float64)               // 德州返点
	// responseData.RewardRateHaXiRoulette = vipDefine["RewardRateHaXiRoulette"].(float64) // 哈希轮盘返点
	// responseData.WeeklyLiuSui = vipDefine["WeeklyLiuSui"].(float64)                     // 周流水
	// responseData.MonthlyLiuSui = vipDefine["MonthlyLiuSui"].(float64)                   // 月流水
	// responseData.WeeklyRechargeTimes = int(vipDefine["WeeklyRechargeTimes"].(int64))    // 周充值次数
	// responseData.WeeklyRechargeAmount = vipDefine["WeeklyRechargeAmount"].(float64)     // 周充值金额
	// responseData.MonthlyRechargeTimes = int(vipDefine["MonthlyRechargeTimes"].(int64))  // 月充值次数
	// responseData.MonthlyRechargeAmount = vipDefine["MonthlyRechargeAmount"].(float64)   // 月充值金额
	// tempWeek := vipDefine["WeeklyReward"]
	// if tempWeek != nil {
	// 	responseData.WeeklyReward = tempWeek.(float64)
	// }

	// 	for j := 0; j < len(*xgVipRows); j++ {
	// 		xgVipGameDefine := (*xgVipRows)[j]
	// 		if responseData.VipLevel != int32(ChangeInt(xgVipGameDefine, "VipLevel")) {
	// 			continue
	// 		}
	// 		var tempGameDefine XVipGameDefine
	// 		tempGameDefine.CatID = int32(ChangeInt(xgVipGameDefine, "CatId"))
	// 		tempGameDefine.GameID = xgVipGameDefine["GameId"].(string)
	// 		tempGameDefine.RewardRate = xgVipGameDefine["RewardRate"].(float64)
	// 		responseData.XVipGameDefineArr = append(responseData.XVipGameDefineArr, tempGameDefine)
	// 	}
	// 	xVipResultArr = append(xVipResultArr, responseData)
	// }

	ctx.Put("data", result)
	ctx.Put("total", total)

	ctx.RespOK()
	server.WriteAdminLog("查看Vip定义", ctx, reqdata)
}

func (c *VipController) vip_add(ctx *abugo.AbuHttpContent) {
	type XVipGameDefineReq struct {
		//SellerId  int `validate:"required"`
		//ChannelId int `validate:"required"`
		//VipLevel  int
		//Brand     string `validate:"required"`
		//GameId    string `validate:"required"`
		CatId      int `validate:"required"`
		RewardRate float64
		Memo       string
	}
	type XVipGameDefineArr []XVipGameDefineReq
	type RequestData struct {
		SellerId               int `validate:"required"`
		ChannelId              int `validate:"required"`
		VipLevel               int
		Recharge               int
		LiuSui                 int
		KeepLiuSui             int
		State                  int     `validate:"required"`
		UpgradeReward          float64 `validate:"min=0,max=1000000"`
		MonthlyReward          float64 `validate:"min=0,max=1000000"`
		WeeklyReward           float64 `validate:"min=0,max=1000000"`
		WeeklyLiuSui           float64
		MonthlyLiuSui          float64
		RewardRateHaXi         float64 `validate:"min=0,max=1"`
		RewardRateLottery      float64 `validate:"min=0,max=1"`
		RewardRateQiPai        float64 `validate:"min=0,max=1"`
		RewardRateDianZhi      float64 `validate:"min=0,max=1"`
		RewardRateXiaoYouXi    float64 `validate:"min=0,max=1"`
		RewardRateLive         float64 `validate:"min=0,max=1"`
		RewardRateSport        float64 `validate:"min=0,max=1"`
		RewardRateTexas        float64 `validate:"min=0,max=1"`
		RewardRateHaXiRoulette float64 `validate:"min=0,max=1"`
		RewardRateLowLottery   float64 `validate:"min=0,max=1"`
		RewardRateCryptoMarket float64 `validate:"min=0,max=1"`
		GoogleCode             string
		XVipGameDefineArr      XVipGameDefineArr `json:"xVipGameDefineArr"`
		WeeklyRechargeTimes    int64             `validate:"min=0"`
		WeeklyRechargeAmount   float64           `validate:"min=0"`
		MonthlyRechargeTimes   int64             `validate:"min=0"`
		MonthlyRechargeAmount  float64           `validate:"min=0"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip定义", "增", "添加vip定义")
	if token == nil {
		return
	}
	//reqdata.SellerId = token.SellerId
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	// 获取当前渠道最大vip等级、遍历并初始化香港六合彩玩法大类
	trErr := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {

		_, err := server.Db().Table("x_vip_define").Insert(gin.H{
			"SellerId":               reqdata.SellerId,
			"ChannelId":              reqdata.ChannelId,
			"VipLevel":               reqdata.VipLevel,
			"Recharge":               reqdata.Recharge,
			"LiuSui":                 reqdata.LiuSui,
			"KeepLiuSui":             reqdata.KeepLiuSui,
			"State":                  reqdata.State,
			"UpgradeReward":          reqdata.UpgradeReward,
			"MonthlyReward":          reqdata.MonthlyReward,
			"WeeklyReward":           reqdata.WeeklyReward,
			"RewardRateHaXi":         reqdata.RewardRateHaXi,
			"RewardRateLottery":      reqdata.RewardRateLottery,
			"RewardRateQiPai":        reqdata.RewardRateQiPai,
			"RewardRateDianZhi":      reqdata.RewardRateDianZhi,
			"RewardRateXiaoYouXi":    reqdata.RewardRateXiaoYouXi,
			"RewardRateLive":         reqdata.RewardRateLive,
			"RewardRateSport":        reqdata.RewardRateSport,
			"RewardRateTexas":        reqdata.RewardRateTexas,
			"RewardRateHaXiRoulette": reqdata.RewardRateHaXiRoulette,
			"RewardRateLowLottery":   reqdata.RewardRateLowLottery,
			"RewardRateCryptoMarket": reqdata.RewardRateCryptoMarket,
			"WeeklyLiuSui":           reqdata.WeeklyLiuSui,
			"MonthlyLiuSui":          reqdata.MonthlyLiuSui,
			"WeeklyRechargeTimes":    reqdata.WeeklyRechargeTimes,
			"WeeklyRechargeAmount":   reqdata.WeeklyRechargeAmount,
			"MonthlyRechargeTimes":   reqdata.MonthlyRechargeTimes,
			"MonthlyRechargeAmount":  reqdata.MonthlyRechargeAmount,
		})
		if err != nil {
			logs.Error("新增vip返水错误, err:", err.Error())
			return err
		}
		// 香港六合彩大类集合
		type LotteryPlay struct {
			Id    int64
			PName string
		}
		type ResponseData struct {
			lotteryPlay []LotteryPlay
		}
		responseData := ResponseData{}
		query := `select Id, PName from x_liuhecai_map GROUP BY id, PName`
		params := make([]interface{}, 0)
		rows, err := server.Db().Query(query, params)
		if err != nil {
			logs.Error("查询相关六合彩玩法大类出错：err:", err)
			panic(err)
		}
		//var respData ResponseData
		for i := 0; i < len(*rows); i++ {
			play := (*rows)[i]
			var lotteryPlay LotteryPlay
			idStr := fmt.Sprintf("%v", play["Id"])
			lotteryPlay.Id, err = strconv.ParseInt(idStr, 10, 64)
			lotteryPlay.PName = fmt.Sprintf("%v", play["PName"])
			responseData.lotteryPlay = append(responseData.lotteryPlay, lotteryPlay)
			//server.Db().GormDao().Table("x_liuhecai_map").Select("Id, PName").Group("Id")
		}

		vipGameDb := tx.XVipGameDefine.WithContext(context.Background())
		vipGameData := make([]*model.XVipGameDefine, 0, len(reqdata.XVipGameDefineArr))
		// 获取佣金配置最大等级
		checkData := make([]*model.XVipDefine, 0)
		err = server.Db().Gorm().Table("x_vip_define").
			Where("SellerId=? and ChannelId=?", reqdata.SellerId, reqdata.ChannelId).
			Order("VipLevel desc").
			First(&checkData).Error
		if ctx.RespErr(err, &errcode) {
			logs.Error("查询原有vip最大等级出错：err:", err)
			return err
		}
		checkDefine := checkData[0]
		maxLevel := int(checkDefine.VipLevel)
		logs.Info("当前最大vip返水配置等级：", maxLevel)
		if maxLevel > 0 {
			for i := 1; i <= maxLevel; i++ {
				// 检查香港六合彩不存在的佣金等级，进行初始化
				xgDefine := model.XVipGameDefine{}
				if reqdata.VipLevel == i {
					continue
				}
				err = server.Db().Gorm().Table("x_vip_game_define").
					Where("SellerId=? and ChannelId=? and VipLevel=?", reqdata.SellerId, reqdata.ChannelId, i).First(&xgDefine).Error
				if err == nil {
					continue
				}
				if xgDefine.VipLevel == 0 && xgDefine.VipLevel != int32(reqdata.VipLevel) {
					// 原渠道最大vip等级下所有的香港六合彩大类都初始化
					initGameData := make([]*model.XVipGameDefine, 0, len(reqdata.XVipGameDefineArr))
					for _, v := range responseData.lotteryPlay {
						// 开始新增
						intGameDefine := &model.XVipGameDefine{
							SellerID:   int32(reqdata.SellerId),
							ChannelID:  int32(reqdata.ChannelId),
							VipLevel:   int32(i),
							Brand:      "gfg",
							GameID:     "1",
							CatID:      int32(v.Id),
							RewardRate: 0,
							Memo:       "",
							CreateTime: time.Now().In(agentUTC8),
							UpdateTime: time.Now().In(agentUTC8),
						}
						initGameData = append(initGameData, intGameDefine)
					}
					err = vipGameDb.CreateInBatches(initGameData, len(initGameData))
					if err != nil {
						logs.Error("初始化香港彩vip返水配置错误，初始化佣金等级：", i, "\n err:", err)
						ctx.RespErr(err, &errcode)
						return err
					}
					logs.Info("初始化香港彩vip返水配置成功，初始化佣金等级：", i)
				}
			}
		}

		for _, gameDefine := range reqdata.XVipGameDefineArr {

			gameDefineModel := &model.XVipGameDefine{
				SellerID:   int32(reqdata.SellerId),
				ChannelID:  int32(reqdata.ChannelId),
				VipLevel:   int32(reqdata.VipLevel),
				Brand:      "gfg", //使用香港六合彩的brand和gameId
				GameID:     "1",
				CatID:      int32(gameDefine.CatId),
				RewardRate: gameDefine.RewardRate,
				Memo:       gameDefine.Memo,
				CreateTime: time.Now().In(agentUTC8),
				UpdateTime: time.Now().In(agentUTC8),
			}
			vipGameData = append(vipGameData, gameDefineModel)
		}
		err = vipGameDb.CreateInBatches(vipGameData, len(vipGameData))
		if err != nil {
			logs.Error("新增香港彩vip返水配置错误：", err)
			ctx.RespErr(err, &errcode)
			return err
		}
		return nil
	})
	if trErr != nil {
		logs.Error("新增香港六合彩vip返水配置错误，err：", trErr)
		ctx.RespErr(trErr, &errcode)
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("添加Vip定义", ctx, reqdata)
}

func (c *VipController) vip_modify(ctx *abugo.AbuHttpContent) {
	type XVipGameDefineReq struct {
		//SellerId  int `validate:"required"`
		//ChannelId int `validate:"required"`
		//VipLevel  int
		//Brand     string `validate:"required"`
		//GameId    string `validate:"required"`
		CatId      int `validate:"required"`
		RewardRate float64
		Memo       string
	}
	type XVipGameDefineArr []XVipGameDefineReq
	type RequestData struct {
		SellerId  int `validate:"required"`
		ChannelId int `validate:"required"`
		Id        int `validate:"required"`

		VipLevel      int
		Recharge      int
		LiuSui        int
		KeepLiuSui    int
		State         int     `validate:"required"`
		UpgradeReward float64 `validate:"min=0,max=1000000"`
		MonthlyReward float64 `validate:"min=0,max=1000000"`
		WeeklyReward  float64 `validate:"min=0,max=1000000"`

		RewardRateHaXi         float64 `validate:"min=0,max=1"`
		RewardRateLottery      float64 `validate:"min=0,max=1"`
		RewardRateQiPai        float64 `validate:"min=0,max=1"`
		RewardRateDianZhi      float64 `validate:"min=0,max=1"`
		RewardRateXiaoYouXi    float64 `validate:"min=0,max=1"`
		RewardRateLive         float64 `validate:"min=0,max=1"`
		RewardRateSport        float64 `validate:"min=0,max=1"`
		RewardRateTexas        float64 `validate:"min=0,max=1"`
		RewardRateHaXiRoulette float64 `validate:"min=0,max=1"`
		RewardRateLowLottery   float64 `validate:"min=0,max=1"`
		RewardRateCryptoMarket float64 `validate:"min=0,max=1"`
		WeeklyLiuSui           float64
		MonthlyLiuSui          float64

		GoogleCode            string
		XVipGameDefineArr     XVipGameDefineArr
		WeeklyRechargeTimes   int64   `validate:"min=0"`
		WeeklyRechargeAmount  float64 `validate:"min=0"`
		MonthlyRechargeTimes  int64   `validate:"min=0"`
		MonthlyRechargeAmount float64 `validate:"min=0"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip定义", "改", "修改vip定义")
	if token == nil {
		return
	}
	//reqdata.SellerId = token.SellerId
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "Id", "=", reqdata.Id, 0)

	// 更新香港六合彩
	errcode = -1
	rerr := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		_, err := server.Db().Table("x_vip_define").Select("*").Where(where).Update(gin.H{
			"VipLevel":               reqdata.VipLevel,
			"Recharge":               reqdata.Recharge,
			"LiuSui":                 reqdata.LiuSui,
			"KeepLiuSui":             reqdata.KeepLiuSui,
			"State":                  reqdata.State,
			"UpgradeReward":          reqdata.UpgradeReward,
			"MonthlyReward":          reqdata.MonthlyReward,
			"WeeklyReward":           reqdata.WeeklyReward,
			"RewardRateHaXi":         reqdata.RewardRateHaXi,
			"RewardRateLottery":      reqdata.RewardRateLottery,
			"RewardRateQiPai":        reqdata.RewardRateQiPai,
			"RewardRateDianZhi":      reqdata.RewardRateDianZhi,
			"RewardRateXiaoYouXi":    reqdata.RewardRateXiaoYouXi,
			"RewardRateLive":         reqdata.RewardRateLive,
			"RewardRateSport":        reqdata.RewardRateSport,
			"RewardRateTexas":        reqdata.RewardRateTexas,
			"RewardRateHaXiRoulette": reqdata.RewardRateHaXiRoulette,
			"RewardRateLowLottery":   reqdata.RewardRateLowLottery,
			"RewardRateCryptoMarket": reqdata.RewardRateCryptoMarket,
			"MonthlyLiuSui":          reqdata.MonthlyLiuSui,
			"WeeklyLiuSui":           reqdata.WeeklyLiuSui,
			"WeeklyRechargeTimes":    reqdata.WeeklyRechargeTimes,
			"WeeklyRechargeAmount":   reqdata.WeeklyRechargeAmount,
			"MonthlyRechargeTimes":   reqdata.MonthlyRechargeTimes,
			"MonthlyRechargeAmount":  reqdata.MonthlyRechargeAmount,
		})
		if err != nil {
			logs.Error("修改vip返水错误，err:", err.Error())
			return err
		}

		vipGameData := make([]*model.XVipGameDefine, 0, len(reqdata.XVipGameDefineArr))
		for _, gameDefine := range reqdata.XVipGameDefineArr {
			gameDefineModel := &model.XVipGameDefine{
				SellerID:   int32(reqdata.SellerId),
				ChannelID:  int32(reqdata.ChannelId),
				VipLevel:   int32(reqdata.VipLevel),
				Brand:      "gfg",
				GameID:     "1",
				CatID:      int32(gameDefine.CatId),
				RewardRate: gameDefine.RewardRate,
				Memo:       gameDefine.Memo,
				CreateTime: time.Now().In(agentUTC8),
				UpdateTime: time.Now().In(agentUTC8),
			}
			vipGameData = append(vipGameData, gameDefineModel)
		}
		// 开始批量更新
		for _, update := range vipGameData {
			uQuery := "UPDATE x_vip_game_define SET RewardRate = ? WHERE SellerID = ? and ChannelID = ? and VipLevel = ? and Brand = ? and GameID = ? and CatID = ?"
			uErr := server.Db().QueryNoResult(uQuery, update.RewardRate, update.SellerID, update.ChannelID, update.VipLevel, update.Brand, update.GameID, update.CatID)
			if uErr != nil {
				fmt.Println("香港六合彩vip返水配置更新错误:", err, "\n data:", update)
			} else {
				fmt.Printf("成功更新香港六合彩vip返水配置，大类ID 为 %d 的记录\n", update.CatID)
			}
		}
		return nil
	})
	if rerr != nil {
		logs.Error("香港六合彩vip返水配置更新错误，err:", rerr)
		ctx.RespErr(rerr, &errcode)
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("修改Vip定义", ctx, reqdata)
}

func (c *VipController) vip_delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id         int `validate:"required"`
		SellerId   int
		ChannelId  int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip定义", "删", "删除vip定义")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		server.Db().Query("delete from x_vip_define where SellerId = ? and ChannelId = ? and Id = ?", []interface{}{reqdata.SellerId, reqdata.ChannelId, reqdata.Id})
		server.Db().Query("delete from x_vip_game_define where SellerId = ? and ChannelId = ?", []interface{}{reqdata.SellerId, reqdata.ChannelId})
		return nil
	})
	if err != nil {
		logs.Error("删除vip返水配置错误：", err.Error())
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("删除Vip定义", ctx, reqdata)
}

func (c *VipController) vip_reward(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		ChannelId  int
		Account    string
		UserId     int
		RewardType int
		StartTime  int64
		EndTime    int64
		Export     int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip礼金记录", "查", "查看Vip礼金记录")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}
	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_VIP礼金记录_%s", time.Now().Format("**************")))
	var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("Account", "账号")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("TopAgentId", "顶级ID")
		xlsx.SetTitle("AgentId", "上级ID")
		xlsx.SetTitle("CurVipLevel", "当前VIP等级")
		xlsx.SetTitle("VipLevel", "返奖时VIP等级")
		xlsx.SetTitle("RecordDate", "礼金日期")
		xlsx.SetTitle("RewardType", "礼金类型")
		xlsx.SetTitle("RewardAmount", "金额")
		xlsx.SetTitle("Memo", "备注")
		xlsx.SetTitle("CreateTime", "发放时间")
		xlsx.SetTitleStyle()
	}

	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "x_vip_reward.SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "x_vip_reward.ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "x_vip_reward.UserId", "=", reqdata.UserId, 0)
		where.Add("and", "x_user.Account", "=", reqdata.Account, "")
		where.Add("and", "x_vip_reward.RewardType", "=", reqdata.RewardType, 0)
		where.Add("and", "x_vip_reward.CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("and", "x_vip_reward.CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
		join := "left join x_user on x_vip_reward.UserId = x_user.UserId left join x_vip_info on x_vip_reward.UserId = x_vip_info.UserId"
		sel := "x_vip_reward.*,x_user.Account,x_user.AgentId,x_user.TopAgentId,x_vip_info.VipLevel as CurVipLevel"
		total, data := server.Db().Table("x_vip_reward").Select(sel).Join(join).Where(where).OrderBy("x_vip_reward.id desc").PageData(reqdata.Page, reqdata.PageSize)
		if reqdata.Export != 1 {
			ctx.Put("data", *data)
			ctx.Put("total", total)
		} else {
			for i := 0; i < len(*data); i++ {
				for k, v := range (*data)[i] {
					if k == "CurVipLevel" || k == "VipLevel" {
						xlsx.SetValue(k, fmt.Sprintf("VIP%d", abugo.GetInt64FromInterface(v)), int64(i+2))
					} else if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "RewardType" {
						if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "晋级礼金", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 2 {
							xlsx.SetValue(k, "VIP返水", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 3 {
							xlsx.SetValue(k, "月礼金", int64(i+2))
						} else {
							xlsx.SetValue(k, "未定义", int64(i+2))
						}
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
			}
		}
		totalSize = total
	}
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "x_vip_reward.SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "x_vip_reward.ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "x_vip_reward.UserId", "=", reqdata.UserId, 0)
		where.Add("and", "x_user.Account", "=", reqdata.Account, "")
		where.Add("and", "x_vip_reward.RewardType", "=", reqdata.RewardType, 0)
		where.Add("and", "x_vip_reward.CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("and", "x_vip_reward.CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
		join := "left join x_user on x_vip_reward.UserId = x_user.UserId"
		sel := "IFNULL(sum(x_vip_reward.RewardAmount),0) as TotalRewardAmount"
		data, _ := server.Db().Table("x_vip_reward").Select(sel).Join(join).Where(where).GetList()
		if reqdata.Export != 1 {
			ctx.Put("totalrewardamount", (*data)[0]["TotalRewardAmount"])
		} else {
			xlsx.SetValue("ChannelId", "合计", totalSize+2)
			xlsx.SetValue("RewardAmount", (*data)[0]["TotalRewardAmount"], totalSize+2)
			xlsx.SetValueStyle(totalSize + 2)
		}
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("查询vip礼金记录", ctx, reqdata)
}

type VipReward struct {
	Id           int     `gorm:"column:Id"`
	SellerId     int     `gorm:"column:SellerId"`
	ChannelId    int     `gorm:"column:ChannelId"`
	UserId       int     `gorm:"column:UserId"`
	VipLevel     int     `gorm:"column:VipLevel"`
	RecordDate   string  `gorm:"column:RecordDate"`
	Memo         string  `gorm:"column:Memo"`
	RewardAmount float64 `gorm:"column:RewardAmount"`
	RewardType   int     `gorm:"column:RewardType"`
	CreateTime   string  `gorm:"column:CreateTime"`
}

func VipLevel2String(vipLevel int) string {
	return fmt.Sprintf("VIP%d", vipLevel)
}

func (c *VipController) vip_reward_report(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId []int
		StartTime int64
		EndTime   int64
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip礼金领取报表", "查", "查看Vip礼金领取报表")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}

	query := server.Db().Gorm().Table("x_vip_reward")
	if reqdata.SellerId > 0 {
		query = query.Where("SellerId = ?", reqdata.SellerId)
	}
	if len(reqdata.ChannelId) > 0 {
		query = query.Where("ChannelId in (?)", reqdata.ChannelId)
	}
	if reqdata.StartTime > 0 {
		query = query.Where("CreateTime >= ?", abugo.TimeStampToLocalTime(reqdata.StartTime))
	}
	if reqdata.EndTime > 0 {
		query = query.Where("CreateTime < ?", abugo.TimeStampToLocalTime(reqdata.EndTime))
	}
	dataSlice := make([]VipReward, 0)
	err := query.Find(&dataSlice).Error
	if ctx.RespErr(err, &errcode) {
		return
	}
	query2 := server.Db().Gorm().Table("x_vip_define")
	if reqdata.SellerId > 0 {
		query2 = query2.Where("SellerId = ?", reqdata.SellerId)
	}
	if len(reqdata.ChannelId) > 0 {
		query2 = query2.Where("ChannelId in (?)", reqdata.ChannelId)
	}
	vipDefineSlice := make([]struct {
		SellerId  int `gorm:"column:SellerId"`
		ChannelId int `gorm:"column:ChannelId"`
		VipLevel  int `gorm:"column:VipLevel"`
	}, 0)
	err = query2.Find(&vipDefineSlice).Error
	if ctx.RespErr(err, &errcode) {
		return
	}
	sort.Slice(vipDefineSlice, func(i, j int) bool {
		if vipDefineSlice[i].ChannelId == vipDefineSlice[j].ChannelId {
			return vipDefineSlice[i].VipLevel < vipDefineSlice[j].VipLevel
		} else {
			return vipDefineSlice[i].ChannelId > vipDefineSlice[j].ChannelId
		}
	})

	//统计操作
	type ReportData struct {
		SellerId      int
		ChannelId     int
		VipLevel      int
		totalUsers    map[int]struct{}
		TotalUsers    int
		upgradeUsers  map[int]struct{}
		UpgradeUsers  int
		UpgradeTimes  int
		UpgradeAmount float64
		dailyUsers    map[int]struct{}
		DailyUsers    int
		DailyTimes    int
		DailyAmount   float64
		monthlyUsers  map[int]struct{}
		MonthlyUsers  int
		MonthlyTimes  int
		MonthlyAmount float64
		weeklyUsers   map[int]struct{}
		WeeklyUsers   int
		WeeklyTimes   int
		WeeklyAmount  float64
	}
	keyMap := make(map[string]*ReportData)
	TotalUsers := make(map[int]struct{})
	UpgradeTotalUsers := make(map[int]struct{})
	UpgradeTotalTimes := int(0)
	UpgradeTotalAmount := float64(0)
	DailyTotalUsers := make(map[int]struct{})
	DailyTotalTimes := int(0)
	DailyTotalAmount := float64(0)
	MonthlyTotalUsers := make(map[int]struct{})
	MonthlyTotalTimes := int(0)
	MonthlyTotalAmount := float64(0)
	WeeklyTotalUsers := make(map[int]struct{})
	WeeklyTotalTimes := int(0)
	WeeklyTotalAmount := float64(0)

	for _, d := range dataSlice {
		k := fmt.Sprintf("%d-%d-%d", d.SellerId, d.ChannelId, d.VipLevel)
		r, ok := keyMap[k]
		if !ok {
			r = &ReportData{
				SellerId:      d.SellerId,
				ChannelId:     d.ChannelId,
				VipLevel:      d.VipLevel,
				totalUsers:    make(map[int]struct{}),
				TotalUsers:    0,
				upgradeUsers:  make(map[int]struct{}),
				UpgradeUsers:  0,
				UpgradeTimes:  0,
				UpgradeAmount: 0,
				dailyUsers:    make(map[int]struct{}),
				DailyUsers:    0,
				DailyTimes:    0,
				DailyAmount:   0,
				monthlyUsers:  make(map[int]struct{}),
				MonthlyUsers:  0,
				MonthlyTimes:  0,
				MonthlyAmount: 0,
				weeklyUsers:   make(map[int]struct{}),
				WeeklyUsers:   0,
				WeeklyTimes:   0,
				WeeklyAmount:  0,
			}
			keyMap[k] = r
		}

		r.totalUsers[d.UserId] = struct{}{}
		TotalUsers[d.UserId] = struct{}{}
		if d.RewardType == 1 { //晋级礼金
			r.upgradeUsers[d.UserId] = struct{}{}
			r.UpgradeTimes++
			r.UpgradeAmount += d.RewardAmount

			UpgradeTotalUsers[d.UserId] = struct{}{}
			UpgradeTotalTimes++
			UpgradeTotalAmount += d.RewardAmount
		} else if d.RewardType == 2 { //VIP反水
			r.dailyUsers[d.UserId] = struct{}{}
			r.DailyTimes++
			r.DailyAmount += d.RewardAmount

			DailyTotalUsers[d.UserId] = struct{}{}
			DailyTotalTimes++
			DailyTotalAmount += d.RewardAmount
		} else if d.RewardType == 3 { //月礼金
			r.monthlyUsers[d.UserId] = struct{}{}
			r.MonthlyTimes++
			r.MonthlyAmount += d.RewardAmount

			MonthlyTotalUsers[d.UserId] = struct{}{}
			MonthlyTotalTimes++
			MonthlyTotalAmount += d.RewardAmount
		} else if d.RewardType == 4 { //月礼金
			r.weeklyUsers[d.UserId] = struct{}{}
			r.WeeklyTimes++
			r.WeeklyAmount += d.RewardAmount

			WeeklyTotalUsers[d.UserId] = struct{}{}
			WeeklyTotalTimes++
			WeeklyTotalAmount += d.RewardAmount
		} else {
			//未定义类型 不进行统计
		}
	}

	data := make([]*ReportData, 0, len(keyMap))
	for _, v := range keyMap {
		v.TotalUsers = len(v.totalUsers)
		v.UpgradeUsers = len(v.upgradeUsers)
		v.DailyUsers = len(v.dailyUsers)
		v.MonthlyUsers = len(v.monthlyUsers)
		v.WeeklyUsers = len(v.weeklyUsers)
		data = append(data, v)
	}

	for i := 0; i < len(vipDefineSlice); i++ {
		add := true
		for j := 0; j < len(data); j++ {
			if vipDefineSlice[i].ChannelId == data[j].ChannelId && vipDefineSlice[i].VipLevel == data[j].VipLevel {
				add = false
				break
			}
		}
		if add {
			data = append(data, &ReportData{
				SellerId:  vipDefineSlice[i].SellerId,
				ChannelId: vipDefineSlice[i].ChannelId,
				VipLevel:  vipDefineSlice[i].VipLevel,
			})
		}
	}

	sort.Slice(data, func(i, j int) bool {
		if data[i].ChannelId == data[j].ChannelId {
			return data[i].VipLevel < data[j].VipLevel
		} else {
			return data[i].ChannelId > data[j].ChannelId
		}
	})
	if reqdata.Export != 1 {
		ctx.Put("data", data)
		ctx.Put("TotalUsers", len(TotalUsers))
		ctx.Put("UpgradeTotalUsers", len(UpgradeTotalUsers))
		ctx.Put("UpgradeTotalTimes", UpgradeTotalTimes)
		ctx.Put("UpgradeTotalAmount", UpgradeTotalAmount)
		ctx.Put("DailyTotalUsers", len(DailyTotalUsers))
		ctx.Put("DailyTotalTimes", DailyTotalTimes)
		ctx.Put("DailyTotalAmount", DailyTotalAmount)
		ctx.Put("MonthlyTotalUsers", len(MonthlyTotalUsers))
		ctx.Put("MonthlyTotalTimes", MonthlyTotalTimes)
		ctx.Put("MonthlyTotalAmount", MonthlyTotalAmount)

		ctx.Put("WeeklyTotalUsers", len(WeeklyTotalUsers))
		ctx.Put("WeeklyTotalTimes", WeeklyTotalTimes)
		ctx.Put("WeeklyTotalAmount", WeeklyTotalAmount)
	} else {
		xlsx := excelize.NewFile()
		defer xlsx.Close()
		xlsx.SetSheetRow("Sheet1", "A1", &[]string{"", "", "", "", "", "晋级礼金", "", "", "月礼金", "", "", "VIP返水", "", "", "周礼金", ""})
		xlsx.MergeCell("Sheet1", "E1", "G1")
		xlsx.SetCellValue("Sheet1", "E1", "晋级礼金")
		xlsx.MergeCell("Sheet1", "H1", "J1")
		xlsx.SetCellValue("Sheet1", "H1", "月礼金")
		xlsx.MergeCell("Sheet1", "K1", "M1")
		xlsx.SetCellValue("Sheet1", "K1", "VIP返水")
		xlsx.MergeCell("Sheet1", "N1", "P1")
		xlsx.SetCellValue("Sheet1", "N1", "周礼金")
		xlsx.SetSheetRow("Sheet1", "A2", &[]string{"序号", "渠道", "VIP等级", "人数", "领取人数", "领取次数", "领取金额", "领取人数", "领取次数", "领取金额", "领取人数", "领取次数", "领取金额", "领取人数", "领取次数", "领取金额"})
		xlsx.SetPanes("Sheet1", &excelize.Panes{
			Freeze:     true,
			Split:      false,
			XSplit:     0,
			YSplit:     2,
			ActivePane: "bottomLeft",
		})
		titleStyle, _ := xlsx.NewStyle(
			&excelize.Style{
				Alignment: &excelize.Alignment{
					Horizontal: "center",
					Vertical:   "center",
				},
				Font: &excelize.Font{
					Bold: true,
				},
			},
		)
		xlsx.SetCellStyle("Sheet1", "A1", "P2", titleStyle)
		for i, d := range data {
			xlsx.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+3), &[]interface{}{
				i + 1,
				ChannelName(d.ChannelId),
				VipLevel2String(d.VipLevel),
				d.TotalUsers,
				d.UpgradeUsers,
				d.UpgradeTimes,
				d.UpgradeAmount,
				d.MonthlyUsers,
				d.MonthlyTimes,
				d.MonthlyAmount,
				d.DailyUsers,
				d.DailyTimes,
				d.DailyAmount,
				d.WeeklyUsers,
				d.WeeklyTimes,
				d.WeeklyAmount,
			})
		}
		xlsx.SetSheetRow("Sheet1", fmt.Sprintf("A%d", len(data)+3), &[]interface{}{
			"",
			"合计",
			"",
			len(TotalUsers),
			len(UpgradeTotalUsers),
			UpgradeTotalTimes,
			UpgradeTotalAmount,
			len(MonthlyTotalUsers),
			MonthlyTotalTimes,
			MonthlyTotalAmount,
			len(DailyTotalUsers),
			DailyTotalTimes,
			DailyTotalAmount,
			len(WeeklyTotalUsers),
			WeeklyTotalTimes,
			WeeklyTotalAmount,
		})
		xlsx.MergeCell("Sheet1", fmt.Sprintf("A%d", len(data)+3), fmt.Sprintf("C%d", len(data)+3))
		xlsx.SetCellValue("Sheet1", fmt.Sprintf("A%d", len(data)+3), "合计")
		valueStyle, _ := xlsx.NewStyle(
			&excelize.Style{
				Alignment: &excelize.Alignment{
					Horizontal: "center",
					Vertical:   "center",
				},
			},
		)
		xlsx.SetCellStyle("Sheet1", "A3", fmt.Sprintf("L%d", len(data)+3), valueStyle)

		filename := "export_礼金领取报表_" + time.Now().Format("**************") + ".xlsx"
		xlsx.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
	}
	ctx.RespOK()
	server.WriteAdminLog("查询Vip礼金领取报表", ctx, reqdata)
}

func (c *VipController) vip_user_change_log(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		ChannelId int
		Account   string
		UserId    int
		StartTime int64
		EndTime   int64
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip修改记录", "查", "查看Vip修改记录")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_VIP修改记录_%s", time.Now().Format("**************")))
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("Account", "账号")
		xlsx.SetTitle("BeforeVipLevel", "修改前VIP等级")
		xlsx.SetTitle("AfterVipLevel", "修改后VIP等级")
		xlsx.SetTitle("OptTime", "修改时间")
		xlsx.SetTitle("OptAccount", "操作人")
		xlsx.SetTitleStyle()
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "x_vip_user_change_log.SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "x_vip_user_change_log.ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "x_vip_user_change_log.UserId", "=", reqdata.UserId, 0)
	where.Add("and", "x_user.Account", "=", reqdata.Account, "")
	where.Add("and", "x_vip_user_change_log.OptTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "x_vip_user_change_log.OptTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	sel := "x_vip_user_change_log.*,x_user.Account,x_user.AgentId,x_user.TopAgentId"
	join := "left join x_user on x_vip_user_change_log.UserId = x_user.UserId"
	total, data := server.Db().Table("x_vip_user_change_log").Select(sel).Join(join).Where(where).OrderBy("x_vip_user_change_log.id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("data", *data)
		ctx.Put("total", total)
	} else {
		if data != nil {
			for i := 0; i < len(*data); i++ {
				for k, v := range (*data)[i] {
					if k == "BeforeVipLevel" || k == "AfterVipLevel" {
						xlsx.SetValue(k, fmt.Sprintf("VIP%d", abugo.GetInt64FromInterface(v)), int64(i+2))
					} else if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
			}
		}
		xlsx.SetValueStyle(total + 2)
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("查询vip修改记录", ctx, reqdata)
	return
}

func (c *VipController) vip_user_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		ChannelId int
		VipLevel  int
		Account   string
		UserId    int
		StartTime int64
		EndTime   int64
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "Vip管理", "Vip礼金领取报表", "查", "查看Vip礼金领取报表详情")
	if token == nil {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += ********
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_VIP用户列表_%s", time.Now().Format("**************")))
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("Account", "账号")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("VipLevel", "VIP等级")
		xlsx.SetTitle("Amount", "账户余额")
		xlsx.SetTitle("RegisterTime", "注册时间")
		xlsx.SetTitle("LoginTime", "最近登录时间")
		xlsx.SetTitle("Recharge", "历史充值")
		xlsx.SetTitle("Withdraw", "历史提款")
		// xlsx.SetTitle("CaiJin", "历史彩金U")
		// xlsx.SetTitle("CaiJinTrx", "历史彩金T")
		xlsx.SetTitle("BetUsdt", "历史投注U")
		xlsx.SetTitle("RewardUsdt", "历史返奖U")
		xlsx.SetTitle("WinLossUsdt", "平台盈亏U")
		xlsx.SetTitle("BetTrx", "历史投注T")
		xlsx.SetTitle("RewardTrx", "历史返奖T")
		xlsx.SetTitle("WinLossTrx", "平台盈亏T")
		xlsx.SetTitleStyle()
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "x_vip_reward.SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "x_vip_reward.ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "x_vip_reward.UserId", "=", reqdata.UserId, 0)
	where.Add("and", "x_user.Account", "=", reqdata.Account, "")
	where.Add("and", "x_vip_reward.VipLevel", "=", reqdata.VipLevel, "")
	where.Add("and", "x_vip_reward.CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "x_vip_reward.CreateTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	sel := "DISTINCT(x_vip_reward.UserId),x_vip_reward.ChannelId,x_user.Account,x_vip_info.VipLevel,x_user.Amount,x_user.RegisterTime,x_user.LoginTime,x_user.BetTrx,x_user.RewardTrx,x_user.BetUsdt,x_user.RewardUsdt,x_vip_info.Recharge,x_vip_info.Withdraw" //,x_vip_info.CaiJin,x_vip_info.CaiJinTrx"
	join := "left join x_user on x_vip_reward.UserId=x_user.UserId left join x_vip_info on x_vip_reward.UserId=x_vip_info.UserId"
	total, data := server.Db().Table("x_vip_reward").Select(sel).Join(join).Where(where).OrderBy("DISTINCT(x_vip_reward.UserId) desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("data", *data)
		ctx.Put("total", total)
	} else {
		if data != nil {
			for i := 0; i < len(*data); i++ {
				for k, v := range (*data)[i] {
					if k == "VipLevel" {
						xlsx.SetValue(k, fmt.Sprintf("VIP%d", abugo.GetInt64FromInterface(v)), int64(i+2))
					} else if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
				xlsx.SetValue("WinLossUsdt", abugo.GetFloat64FromInterface((*data)[i]["BetUsdt"])-abugo.GetFloat64FromInterface((*data)[i]["RewardUsdt"]), int64(i+2))
				xlsx.SetValue("WinLossTrx", abugo.GetFloat64FromInterface((*data)[i]["BetTrx"])-abugo.GetFloat64FromInterface((*data)[i]["RewardTrx"]), int64(i+2))
			}
		}
		xlsx.SetValueStyle(total + 2)
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("查询Vip礼金领取报表详情", ctx, reqdata)
	return
}
