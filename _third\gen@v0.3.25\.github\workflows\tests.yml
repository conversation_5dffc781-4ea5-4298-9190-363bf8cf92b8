name: tests

on:
  push:
    branches-ignore:
      - 'gh-pages'
  pull_request:
    branches-ignore:
      - 'gh-pages'

jobs:
  # Label of the container job
  tests:
    strategy:
      matrix:
        go: ['1.19', '1.18']
        platform: [ubuntu-latest] # can not run in windows OS
    runs-on: ${{ matrix.platform }}

    steps:
    - name: Set up Go 1.x
      uses: actions/setup-go@v5
      with:
        go-version: ${{ matrix.go }}

    - name: Check out code into the Go module directory
      uses: actions/checkout@v3

    - name: go mod package cache
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ matrix.go }}-${{ hashFiles('go.sum') }}

    - name: Tests
      run: go test ./...

  mysql:
    strategy:
      matrix:
        dbversion: ['mysql:5.7', 'mysql:latest']
        go: ['1.19', '1.18']
        platform: [ubuntu-latest]
    runs-on: ${{ matrix.platform }}

    services:
      mysql:
        image: ${{ matrix.dbversion }}
        env:
          MYSQL_DATABASE: gen
          MYSQL_USER: gen
          MYSQL_PASSWORD: gen
          MYSQL_ROOT_PASSWORD: 123456
        ports:
          - 9910:3306
        options: >-
          --health-cmd "mysqladmin ping -ugen -pgen"
          --health-interval 10s
          --health-start-period 10s
          --health-timeout 5s
          --health-retries 10
          --name mysql_server

    steps:
    - name: Change MySQL sql_mode
      run: docker exec mysql_server mysql -uroot -p123456 -e "SET GLOBAL sql_mode = 'NO_ENGINE_SUBSTITUTION';"

    - name: Set up Go 1.x
      uses: actions/setup-go@v5
      with:
        go-version: ${{ matrix.go }}

    - name: Check out code into the Go module directory
      uses: actions/checkout@v3


    - name: go mod package cache
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ matrix.go }}-${{ hashFiles('tests/go.mod') }}

    - name: Tests
      run: GITHUB_ACTION=true GORM_DIALECT=mysql GORM_DSN="gen:gen@tcp(localhost:9910)/gen?charset=utf8&parseTime=True" ./tests/test.sh

