// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXOperationsInputDataDate(db *gorm.DB, opts ...gen.DOOption) xOperationsInputDataDate {
	_xOperationsInputDataDate := xOperationsInputDataDate{}

	_xOperationsInputDataDate.xOperationsInputDataDateDo.UseDB(db, opts...)
	_xOperationsInputDataDate.xOperationsInputDataDateDo.UseModel(&model.XOperationsInputDataDate{})

	tableName := _xOperationsInputDataDate.xOperationsInputDataDateDo.TableName()
	_xOperationsInputDataDate.ALL = field.NewAsterisk(tableName)
	_xOperationsInputDataDate.RecordDate = field.NewTime(tableName, "record_date")
	_xOperationsInputDataDate.SellerID = field.NewInt32(tableName, "seller_id")
	_xOperationsInputDataDate.SubOtherAmount = field.NewFloat64(tableName, "sub_other_amount")
	_xOperationsInputDataDate.ResourcesAmount = field.NewFloat64(tableName, "resources_amount")
	_xOperationsInputDataDate.MarketAmount = field.NewFloat64(tableName, "market_amount")
	_xOperationsInputDataDate.AdAmount = field.NewFloat64(tableName, "ad_amount")
	_xOperationsInputDataDate.OpAmount = field.NewFloat64(tableName, "op_amount")
	_xOperationsInputDataDate.AgentAmount = field.NewFloat64(tableName, "agent_amount")
	_xOperationsInputDataDate.CreateTime = field.NewTime(tableName, "create_time")
	_xOperationsInputDataDate.UpdateTime = field.NewTime(tableName, "update_time")

	_xOperationsInputDataDate.fillFieldMap()

	return _xOperationsInputDataDate
}

// xOperationsInputDataDate 运营报表平台汇总手动录入数据(按日期)
type xOperationsInputDataDate struct {
	xOperationsInputDataDateDo xOperationsInputDataDateDo

	ALL             field.Asterisk
	RecordDate      field.Time    // 日期
	SellerID        field.Int32   // 运营商ID
	SubOtherAmount  field.Float64 // 其它扣除金额
	ResourcesAmount field.Float64 // 资源部
	MarketAmount    field.Float64 // 市场部
	AdAmount        field.Float64 // 广告部
	OpAmount        field.Float64 // 运营部
	AgentAmount     field.Float64 // 代理分红收支
	CreateTime      field.Time    // 创建时间
	UpdateTime      field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xOperationsInputDataDate) Table(newTableName string) *xOperationsInputDataDate {
	x.xOperationsInputDataDateDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xOperationsInputDataDate) As(alias string) *xOperationsInputDataDate {
	x.xOperationsInputDataDateDo.DO = *(x.xOperationsInputDataDateDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xOperationsInputDataDate) updateTableName(table string) *xOperationsInputDataDate {
	x.ALL = field.NewAsterisk(table)
	x.RecordDate = field.NewTime(table, "record_date")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.SubOtherAmount = field.NewFloat64(table, "sub_other_amount")
	x.ResourcesAmount = field.NewFloat64(table, "resources_amount")
	x.MarketAmount = field.NewFloat64(table, "market_amount")
	x.AdAmount = field.NewFloat64(table, "ad_amount")
	x.OpAmount = field.NewFloat64(table, "op_amount")
	x.AgentAmount = field.NewFloat64(table, "agent_amount")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xOperationsInputDataDate) WithContext(ctx context.Context) *xOperationsInputDataDateDo {
	return x.xOperationsInputDataDateDo.WithContext(ctx)
}

func (x xOperationsInputDataDate) TableName() string { return x.xOperationsInputDataDateDo.TableName() }

func (x xOperationsInputDataDate) Alias() string { return x.xOperationsInputDataDateDo.Alias() }

func (x xOperationsInputDataDate) Columns(cols ...field.Expr) gen.Columns {
	return x.xOperationsInputDataDateDo.Columns(cols...)
}

func (x *xOperationsInputDataDate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xOperationsInputDataDate) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 10)
	x.fieldMap["record_date"] = x.RecordDate
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["sub_other_amount"] = x.SubOtherAmount
	x.fieldMap["resources_amount"] = x.ResourcesAmount
	x.fieldMap["market_amount"] = x.MarketAmount
	x.fieldMap["ad_amount"] = x.AdAmount
	x.fieldMap["op_amount"] = x.OpAmount
	x.fieldMap["agent_amount"] = x.AgentAmount
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xOperationsInputDataDate) clone(db *gorm.DB) xOperationsInputDataDate {
	x.xOperationsInputDataDateDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xOperationsInputDataDate) replaceDB(db *gorm.DB) xOperationsInputDataDate {
	x.xOperationsInputDataDateDo.ReplaceDB(db)
	return x
}

type xOperationsInputDataDateDo struct{ gen.DO }

func (x xOperationsInputDataDateDo) Debug() *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Debug())
}

func (x xOperationsInputDataDateDo) WithContext(ctx context.Context) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xOperationsInputDataDateDo) ReadDB() *xOperationsInputDataDateDo {
	return x.Clauses(dbresolver.Read)
}

func (x xOperationsInputDataDateDo) WriteDB() *xOperationsInputDataDateDo {
	return x.Clauses(dbresolver.Write)
}

func (x xOperationsInputDataDateDo) Session(config *gorm.Session) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Session(config))
}

func (x xOperationsInputDataDateDo) Clauses(conds ...clause.Expression) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xOperationsInputDataDateDo) Returning(value interface{}, columns ...string) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xOperationsInputDataDateDo) Not(conds ...gen.Condition) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xOperationsInputDataDateDo) Or(conds ...gen.Condition) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xOperationsInputDataDateDo) Select(conds ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xOperationsInputDataDateDo) Where(conds ...gen.Condition) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xOperationsInputDataDateDo) Order(conds ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xOperationsInputDataDateDo) Distinct(cols ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xOperationsInputDataDateDo) Omit(cols ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xOperationsInputDataDateDo) Join(table schema.Tabler, on ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xOperationsInputDataDateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xOperationsInputDataDateDo) RightJoin(table schema.Tabler, on ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xOperationsInputDataDateDo) Group(cols ...field.Expr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xOperationsInputDataDateDo) Having(conds ...gen.Condition) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xOperationsInputDataDateDo) Limit(limit int) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xOperationsInputDataDateDo) Offset(offset int) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xOperationsInputDataDateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xOperationsInputDataDateDo) Unscoped() *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xOperationsInputDataDateDo) Create(values ...*model.XOperationsInputDataDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xOperationsInputDataDateDo) CreateInBatches(values []*model.XOperationsInputDataDate, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xOperationsInputDataDateDo) Save(values ...*model.XOperationsInputDataDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xOperationsInputDataDateDo) First() (*model.XOperationsInputDataDate, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOperationsInputDataDate), nil
	}
}

func (x xOperationsInputDataDateDo) Take() (*model.XOperationsInputDataDate, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOperationsInputDataDate), nil
	}
}

func (x xOperationsInputDataDateDo) Last() (*model.XOperationsInputDataDate, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOperationsInputDataDate), nil
	}
}

func (x xOperationsInputDataDateDo) Find() ([]*model.XOperationsInputDataDate, error) {
	result, err := x.DO.Find()
	return result.([]*model.XOperationsInputDataDate), err
}

func (x xOperationsInputDataDateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XOperationsInputDataDate, err error) {
	buf := make([]*model.XOperationsInputDataDate, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xOperationsInputDataDateDo) FindInBatches(result *[]*model.XOperationsInputDataDate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xOperationsInputDataDateDo) Attrs(attrs ...field.AssignExpr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xOperationsInputDataDateDo) Assign(attrs ...field.AssignExpr) *xOperationsInputDataDateDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xOperationsInputDataDateDo) Joins(fields ...field.RelationField) *xOperationsInputDataDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xOperationsInputDataDateDo) Preload(fields ...field.RelationField) *xOperationsInputDataDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xOperationsInputDataDateDo) FirstOrInit() (*model.XOperationsInputDataDate, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOperationsInputDataDate), nil
	}
}

func (x xOperationsInputDataDateDo) FirstOrCreate() (*model.XOperationsInputDataDate, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOperationsInputDataDate), nil
	}
}

func (x xOperationsInputDataDateDo) FindByPage(offset int, limit int) (result []*model.XOperationsInputDataDate, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xOperationsInputDataDateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xOperationsInputDataDateDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xOperationsInputDataDateDo) Delete(models ...*model.XOperationsInputDataDate) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xOperationsInputDataDateDo) withDO(do gen.Dao) *xOperationsInputDataDateDo {
	x.DO = *do.(*gen.DO)
	return x
}
