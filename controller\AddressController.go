package controller

import (
	"xserver/abugo"
	"xserver/db"
	"xserver/server"
)

type AddressController struct {
}

func (c *AddressController) Init() {
	group := server.Http().NewGroup("/api/address")
	{
		group.Post("/list", c.list)
		group.Post("/add", c.add)
		group.Post("/alloc", c.alloc)
	}
}

func (c *AddressController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		Page     int
		PageSize int
		State    int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "地址管理", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	total, data := db.Address_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.State)
	ctx.Put("data", data)

	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("游戏地址查询", ctx, reqdata)
}
func (c *AddressController) add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		Address  []string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "地址管理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	for i := 0; i < len(reqdata.Address); i++ {
		if len(reqdata.Address[i]) > 0 {
			sql := "insert ignore into x_address(id,SellerId,Address,State,CreateTime) values(0,?,?,1,now())"
			server.Db().QueryNoResult(sql, reqdata.SellerId, reqdata.Address[i])
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("新增游戏地址", ctx, reqdata)
}
func (c *AddressController) alloc(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		GameId    int `validate:"required"`
		RoomLevel int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "游戏列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	type ReturnData struct {
		Address string
	}
	returndata := ReturnData{}
	{
		sql := "select	* from x_address where SellerId = ? and  GameId = ? and RoomLevel = ?"
		dbresult, err := server.Db().Conn().Query(sql, reqdata.SellerId, reqdata.GameId, reqdata.RoomLevel)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if dbresult.Next() {
			abugo.GetDbResult(dbresult, &returndata)
			dbresult.Close()
			server.WriteAdminLog("分配地址", ctx, reqdata)
			ctx.RespOK(returndata)
			return
		}
		dbresult.Close()
	}
	{
		sql := "select * from x_address where SellerId = ? and state = 1  order by id asc limit 1"
		dbresult, err := server.Db().Conn().Query(sql, reqdata.SellerId)
		if ctx.RespErr(err, &errcode) {
			return
		}
		returndata := ReturnData{}
		if ctx.RespErrString(!dbresult.Next(), &errcode, "无可分配地址,请先添加地址") {
			return
		}
		abugo.GetDbResult(dbresult, &returndata)
		dbresult.Close()
		server.WriteAdminLog("分配地址", ctx, reqdata)
		ctx.RespOK(returndata)
	}
	server.WriteAdminLog("分配游戏地址", ctx, reqdata)
}
