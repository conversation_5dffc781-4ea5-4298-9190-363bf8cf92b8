// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsWithdrawalDailyStat = "x_ads_withdrawal_daily_stats"

// XAdsWithdrawalDailyStat 提现偏好每日统计表
type XAdsWithdrawalDailyStat struct {
	ID                       int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                  // 主键ID
	SellerID                 int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                                 // 运营商ID
	ChannelID                int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                                // 渠道ID
	TopAgentID               int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                          // 顶级代理ID
	StatDate                 time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                                         // 统计日期
	WithdrawalType           int32     `gorm:"column:withdrawal_type;not null;comment:提现类型: 1:法币 2:加密货币" json:"withdrawal_type"`                // 提现类型: 1:法币 2:加密货币
	WithdrawalChannel        string    `gorm:"column:withdrawal_channel;not null;comment:提现渠道" json:"withdrawal_channel"`                       // 提现渠道
	WithdrawalCountPc        int32     `gorm:"column:withdrawal_count_pc;not null;comment:提现次数pc" json:"withdrawal_count_pc"`                   // 提现次数pc
	WithdrawalCountH5        int32     `gorm:"column:withdrawal_count_h5;not null;comment:提现次数h5" json:"withdrawal_count_h5"`                   // 提现次数h5
	WithdrawalCountSuccessPc int32     `gorm:"column:withdrawal_count_success_pc;not null;comment:提现成功次数pc" json:"withdrawal_count_success_pc"` // 提现成功次数pc
	WithdrawalCountSuccessH5 int32     `gorm:"column:withdrawal_count_success_h5;not null;comment:提现成功次数h5" json:"withdrawal_count_success_h5"` // 提现成功次数h5
	WithdrawalRatePc         float32   `gorm:"column:withdrawal_rate_pc;not null;comment:提现占比pc" json:"withdrawal_rate_pc"`                     // 提现占比pc
	WithdrawalRateH5         float32   `gorm:"column:withdrawal_rate_h5;not null;comment:提现占比h5" json:"withdrawal_rate_h5"`                     // 提现占比h5
	WithdrawalSuccessRatePc  float32   `gorm:"column:withdrawal_success_rate_pc;not null;comment:提现成功率 pc" json:"withdrawal_success_rate_pc"`   // 提现成功率 pc
	WithdrawalSuccessRateH5  float32   `gorm:"column:withdrawal_success_rate_h5;not null;comment:提现成功率 h5" json:"withdrawal_success_rate_h5"`   // 提现成功率 h5
	CreateTime               time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`           // 创建时间
	UpdateTime               time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`           // 更新时间
}

// TableName XAdsWithdrawalDailyStat's table name
func (*XAdsWithdrawalDailyStat) TableName() string {
	return TableNameXAdsWithdrawalDailyStat
}
