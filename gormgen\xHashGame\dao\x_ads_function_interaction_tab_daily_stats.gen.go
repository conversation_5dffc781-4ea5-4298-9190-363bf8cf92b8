// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsFunctionInteractionTabDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsFunctionInteractionTabDailyStat {
	_xAdsFunctionInteractionTabDailyStat := xAdsFunctionInteractionTabDailyStat{}

	_xAdsFunctionInteractionTabDailyStat.xAdsFunctionInteractionTabDailyStatDo.UseDB(db, opts...)
	_xAdsFunctionInteractionTabDailyStat.xAdsFunctionInteractionTabDailyStatDo.UseModel(&model.XAdsFunctionInteractionTabDailyStat{})

	tableName := _xAdsFunctionInteractionTabDailyStat.xAdsFunctionInteractionTabDailyStatDo.TableName()
	_xAdsFunctionInteractionTabDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsFunctionInteractionTabDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsFunctionInteractionTabDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsFunctionInteractionTabDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsFunctionInteractionTabDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsFunctionInteractionTabDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsFunctionInteractionTabDailyStat.ButtonName = field.NewString(tableName, "button_name")
	_xAdsFunctionInteractionTabDailyStat.TabName = field.NewString(tableName, "tab_name")
	_xAdsFunctionInteractionTabDailyStat.ClickCountPc = field.NewInt32(tableName, "click_count_pc")
	_xAdsFunctionInteractionTabDailyStat.ClickCountH5 = field.NewInt32(tableName, "click_count_h5")
	_xAdsFunctionInteractionTabDailyStat.ClickCountAll = field.NewInt32(tableName, "click_count_all")
	_xAdsFunctionInteractionTabDailyStat.TabRatePc = field.NewFloat32(tableName, "tab_rate_pc")
	_xAdsFunctionInteractionTabDailyStat.TabRateH5 = field.NewFloat32(tableName, "tab_rate_h5")
	_xAdsFunctionInteractionTabDailyStat.TabRateAll = field.NewFloat32(tableName, "tab_rate_all")
	_xAdsFunctionInteractionTabDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsFunctionInteractionTabDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsFunctionInteractionTabDailyStat.fillFieldMap()

	return _xAdsFunctionInteractionTabDailyStat
}

// xAdsFunctionInteractionTabDailyStat 功能交互TAB每日统计表
type xAdsFunctionInteractionTabDailyStat struct {
	xAdsFunctionInteractionTabDailyStatDo xAdsFunctionInteractionTabDailyStatDo

	ALL           field.Asterisk
	ID            field.Int64   // 主键ID
	SellerID      field.Int32   // 运营商ID
	ChannelID     field.Int32   // 渠道ID
	TopAgentID    field.Int64   // 顶级代理ID
	StatDate      field.Time    // 统计日期
	ButtonName    field.String  // 按钮名称
	TabName       field.String  // TAB名称
	ClickCountPc  field.Int32   // 点击次数 pc
	ClickCountH5  field.Int32   // 点击次数 h5
	ClickCountAll field.Int32   // 点击次数 all
	TabRatePc     field.Float32 // 占比: 与所有tab之和的比值 pc
	TabRateH5     field.Float32 // 占比: 与所有tab之和的比值 h5
	TabRateAll    field.Float32 // 占比: 与所有tab之和的比值
	CreateTime    field.Time    // 创建时间
	UpdateTime    field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsFunctionInteractionTabDailyStat) Table(newTableName string) *xAdsFunctionInteractionTabDailyStat {
	x.xAdsFunctionInteractionTabDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsFunctionInteractionTabDailyStat) As(alias string) *xAdsFunctionInteractionTabDailyStat {
	x.xAdsFunctionInteractionTabDailyStatDo.DO = *(x.xAdsFunctionInteractionTabDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsFunctionInteractionTabDailyStat) updateTableName(table string) *xAdsFunctionInteractionTabDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.StatDate = field.NewTime(table, "stat_date")
	x.ButtonName = field.NewString(table, "button_name")
	x.TabName = field.NewString(table, "tab_name")
	x.ClickCountPc = field.NewInt32(table, "click_count_pc")
	x.ClickCountH5 = field.NewInt32(table, "click_count_h5")
	x.ClickCountAll = field.NewInt32(table, "click_count_all")
	x.TabRatePc = field.NewFloat32(table, "tab_rate_pc")
	x.TabRateH5 = field.NewFloat32(table, "tab_rate_h5")
	x.TabRateAll = field.NewFloat32(table, "tab_rate_all")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsFunctionInteractionTabDailyStat) WithContext(ctx context.Context) *xAdsFunctionInteractionTabDailyStatDo {
	return x.xAdsFunctionInteractionTabDailyStatDo.WithContext(ctx)
}

func (x xAdsFunctionInteractionTabDailyStat) TableName() string {
	return x.xAdsFunctionInteractionTabDailyStatDo.TableName()
}

func (x xAdsFunctionInteractionTabDailyStat) Alias() string {
	return x.xAdsFunctionInteractionTabDailyStatDo.Alias()
}

func (x xAdsFunctionInteractionTabDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsFunctionInteractionTabDailyStatDo.Columns(cols...)
}

func (x *xAdsFunctionInteractionTabDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsFunctionInteractionTabDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 15)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["button_name"] = x.ButtonName
	x.fieldMap["tab_name"] = x.TabName
	x.fieldMap["click_count_pc"] = x.ClickCountPc
	x.fieldMap["click_count_h5"] = x.ClickCountH5
	x.fieldMap["click_count_all"] = x.ClickCountAll
	x.fieldMap["tab_rate_pc"] = x.TabRatePc
	x.fieldMap["tab_rate_h5"] = x.TabRateH5
	x.fieldMap["tab_rate_all"] = x.TabRateAll
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsFunctionInteractionTabDailyStat) clone(db *gorm.DB) xAdsFunctionInteractionTabDailyStat {
	x.xAdsFunctionInteractionTabDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsFunctionInteractionTabDailyStat) replaceDB(db *gorm.DB) xAdsFunctionInteractionTabDailyStat {
	x.xAdsFunctionInteractionTabDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsFunctionInteractionTabDailyStatDo struct{ gen.DO }

func (x xAdsFunctionInteractionTabDailyStatDo) Debug() *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsFunctionInteractionTabDailyStatDo) WithContext(ctx context.Context) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsFunctionInteractionTabDailyStatDo) ReadDB() *xAdsFunctionInteractionTabDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsFunctionInteractionTabDailyStatDo) WriteDB() *xAdsFunctionInteractionTabDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsFunctionInteractionTabDailyStatDo) Session(config *gorm.Session) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Clauses(conds ...clause.Expression) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Returning(value interface{}, columns ...string) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Not(conds ...gen.Condition) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Or(conds ...gen.Condition) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Select(conds ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Where(conds ...gen.Condition) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Order(conds ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Distinct(cols ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Omit(cols ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Group(cols ...field.Expr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Having(conds ...gen.Condition) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Limit(limit int) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Offset(offset int) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Unscoped() *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsFunctionInteractionTabDailyStatDo) Create(values ...*model.XAdsFunctionInteractionTabDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsFunctionInteractionTabDailyStatDo) CreateInBatches(values []*model.XAdsFunctionInteractionTabDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsFunctionInteractionTabDailyStatDo) Save(values ...*model.XAdsFunctionInteractionTabDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsFunctionInteractionTabDailyStatDo) First() (*model.XAdsFunctionInteractionTabDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionTabDailyStat), nil
	}
}

func (x xAdsFunctionInteractionTabDailyStatDo) Take() (*model.XAdsFunctionInteractionTabDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionTabDailyStat), nil
	}
}

func (x xAdsFunctionInteractionTabDailyStatDo) Last() (*model.XAdsFunctionInteractionTabDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionTabDailyStat), nil
	}
}

func (x xAdsFunctionInteractionTabDailyStatDo) Find() ([]*model.XAdsFunctionInteractionTabDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsFunctionInteractionTabDailyStat), err
}

func (x xAdsFunctionInteractionTabDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsFunctionInteractionTabDailyStat, err error) {
	buf := make([]*model.XAdsFunctionInteractionTabDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsFunctionInteractionTabDailyStatDo) FindInBatches(result *[]*model.XAdsFunctionInteractionTabDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsFunctionInteractionTabDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsFunctionInteractionTabDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsFunctionInteractionTabDailyStatDo) Joins(fields ...field.RelationField) *xAdsFunctionInteractionTabDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsFunctionInteractionTabDailyStatDo) Preload(fields ...field.RelationField) *xAdsFunctionInteractionTabDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsFunctionInteractionTabDailyStatDo) FirstOrInit() (*model.XAdsFunctionInteractionTabDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionTabDailyStat), nil
	}
}

func (x xAdsFunctionInteractionTabDailyStatDo) FirstOrCreate() (*model.XAdsFunctionInteractionTabDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsFunctionInteractionTabDailyStat), nil
	}
}

func (x xAdsFunctionInteractionTabDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsFunctionInteractionTabDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsFunctionInteractionTabDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsFunctionInteractionTabDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsFunctionInteractionTabDailyStatDo) Delete(models ...*model.XAdsFunctionInteractionTabDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsFunctionInteractionTabDailyStatDo) withDO(do gen.Dao) *xAdsFunctionInteractionTabDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
