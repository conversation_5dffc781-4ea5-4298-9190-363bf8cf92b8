// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsActiveDailyStat = "x_ads_active_daily_stats"

// XAdsActiveDailyStat 活动偏好每日统计表
type XAdsActiveDailyStat struct {
	ID                   int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                         // 主键ID
	SellerID             int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                        // 运营商ID
	ChannelID            int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                       // 渠道ID
	TopAgentID           int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                 // 顶级代理ID
	ActiveName           string    `gorm:"column:active_name;not null;comment:活动标签/ 名称" json:"active_name"`                        // 活动标签/ 名称
	StatDate             time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                                // 统计日期
	ClickCountPc         int32     `gorm:"column:click_count_pc;not null;comment:点击次数pc" json:"click_count_pc"`                    // 点击次数pc
	ClickCountH5         int32     `gorm:"column:click_count_h5;not null;comment:点击次数h5" json:"click_count_h5"`                    // 点击次数h5
	JoinCountPc          int32     `gorm:"column:join_count_pc;not null;comment:参入人数pc" json:"join_count_pc"`                      // 参入人数pc
	JoinCountH5          int32     `gorm:"column:join_count_h5;not null;comment:参入人数h5" json:"join_count_h5"`                      // 参入人数h5
	CompletionCountPc    int32     `gorm:"column:completion_count_pc;not null;comment:完成任务人数pc" json:"completion_count_pc"`        // 完成任务人数pc
	CompletionCountH5    int32     `gorm:"column:completion_count_h5;not null;comment:完成任务人数h5" json:"completion_count_h5"`        // 完成任务人数h5
	ReceivedCountPc      int32     `gorm:"column:received_count_pc;not null;comment:领取奖励人数人数pc" json:"received_count_pc"`          // 领取奖励人数人数pc
	ReceivedCountH5      int32     `gorm:"column:received_count_h5;not null;comment:领取奖励人数人数h5" json:"received_count_h5"`          // 领取奖励人数人数h5
	ClickCtrPc           float32   `gorm:"column:click_ctr_pc;not null;comment:点击转化率pc" json:"click_ctr_pc"`                       // 点击转化率pc
	ClickCtrH5           float32   `gorm:"column:click_ctr_h5;not null;comment:点击转化率h5" json:"click_ctr_h5"`                       // 点击转化率h5
	ActiveTarPc          float32   `gorm:"column:active_tar_pc;not null;comment:任务完成率pc" json:"active_tar_pc"`                     // 任务完成率pc
	ActiveTarH5          float32   `gorm:"column:active_tar_h5;not null;comment:任务完成率h5" json:"active_tar_h5"`                     // 任务完成率h5
	TotalPayoutAmountUPc float32   `gorm:"column:total_payout_amountU_pc;not null;comment:总派彩U pc" json:"total_payout_amountU_pc"` // 总派彩U pc
	TotalPayoutAmountUH5 float32   `gorm:"column:total_payout_amountU_h5;not null;comment:总派彩U h5" json:"total_payout_amountU_h5"` // 总派彩U h5
	TotalPayoutAmountTPc float32   `gorm:"column:total_payout_amountT_pc;not null;comment:总派彩T pc" json:"total_payout_amountT_pc"` // 总派彩T pc
	TotalPayoutAmountTH5 float32   `gorm:"column:total_payout_amountT_h5;not null;comment:总派彩T h5" json:"total_payout_amountT_h5"` // 总派彩T h5
	CreateTime           time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`  // 创建时间
	UpdateTime           time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`  // 更新时间
}

// TableName XAdsActiveDailyStat's table name
func (*XAdsActiveDailyStat) TableName() string {
	return TableNameXAdsActiveDailyStat
}
