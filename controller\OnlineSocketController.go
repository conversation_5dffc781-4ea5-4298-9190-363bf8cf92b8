package controller

import (
	"github.com/gorilla/websocket"
	"log"
	"net/http"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/server"
)

type OnlineSocketController struct {
}

func (c *OnlineSocketController) Init() {
	go c.connectToDownstream()
	go c.broadcastMessages()
	server.Http().GetNoAuth("/api/ws/online", c.handleConnections)
	server.Http().Post("/api/online/web", c.hourlyTopWebOnline)
	server.Http().Post("/api/online/play", c.hourlyTopWebOnline)
	server.Http().Post("/api/online/brand", c.hourlyTopBrandOnline)
	server.Http().Post("/api/online/game", c.hourlyTopGameOnline)
}

var upgrade = websocket.Upgrader{
	// 允许所有请求来源
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// 存储所有连接到服务的客户端
var clients = make(map[*websocket.Conn]bool)
var mutex = &sync.Mutex{}

// 广播通道，供下游 WebSocket 服务的消息传递
var broadcast = make(chan []byte)

func (c *OnlineSocketController) handleConnections(ctx *abugo.AbuHttpContent) {
	ws, err := upgrade.Upgrade(ctx.Gin().Writer, ctx.Gin().Request, nil)
	if err != nil {
		log.Println("客户端 WebSocket 连接失败:", err)
		return
	}
	defer ws.Close()
	// 将新连接加入到客户端池
	mutex.Lock()
	clients[ws] = true
	mutex.Unlock()

	// 等待并处理客户端断开连接的情况
	for {
		_, _, err := ws.ReadMessage()
		if err != nil {
			log.Println("客户端断开连接:", err)
			mutex.Lock()
			delete(clients, ws)
			mutex.Unlock()
			break
		}
	}
}

func (c *OnlineSocketController) connectToDownstream() {
	// 连接到下游 WebSocket 服务
	url := "ws://localhost:4533/api/ws/online" // 替换为下游服务的 WebSocket URL
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Println("无法连接下游 WebSocket 服务:", err)
	}
	defer conn.Close()

	for {
		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			//log.Println("无法连接下游 WebSocket 服务:", err)
			//log.Println("正在重试连接...")
			// 等待一段时间再尝试重连
			time.Sleep(5 * time.Second)
			continue
		}
		log.Println("成功连接到下游 WebSocket 服务")
		c.handleDownstreamMessages(conn)
	}
}

// 将接收到的消息广播给所有客户端
func (c *OnlineSocketController) broadcastMessages() {
	for {
		// 从广播通道接收消息
		message := <-broadcast

		// 向所有已连接的客户端广播消息
		mutex.Lock()
		for client := range clients {
			err := client.WriteMessage(websocket.TextMessage, message)
			if err != nil {
				log.Println("广播消息失败:", err)
				client.Close()
				delete(clients, client)
			}
		}
		mutex.Unlock()
	}
}

func (c *OnlineSocketController) handleDownstreamMessages(conn *websocket.Conn) {
	defer conn.Close()

	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			log.Println("读取下游消息失败:", err)
			// 读取失败，可能是连接断开，退出循环重新连接
			break
		}
		// 将消息发送到广播通道
		broadcast <- message
	}
}

// 获取每个小时的站点峰值在线数据
func (c *OnlineSocketController) hourlyTopWebOnline(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type Result struct {
		Hour      int `json:"hour"`
		MaxOnline int `json:"max_online"`
	}

	var results []Result

	err := server.Db().Gorm().Table("x_online_web_user").
		Select("HOUR(CreateTime) AS hour, MAX(Online) AS max_online").
		Where("DATE(CreateTime) = CURDATE()").
		Group("HOUR(CreateTime)").
		Find(&results).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK(results)
}

// 获取每个小时的站点峰值在线数据
func (c *OnlineSocketController) hourlyTopPlayOnline(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type Result struct {
		Hour      int `json:"hour"`
		MaxOnline int `json:"max_online"`
	}

	var results []Result

	err := server.Db().Gorm().Table("x_online_play_user").
		Select("HOUR(CreateTime) AS hour, MAX(Online) AS max_online").
		Where("DATE(CreateTime) = CURDATE()").
		Group("HOUR(CreateTime)").
		Find(&results).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK(results)
}

func (c *OnlineSocketController) hourlyTopBrandOnline(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type OnlineBrandUser struct {
		Brand  string `json:"Brand" gorm:"column:Brand"`
		Hour00 int    `json:"Hour00" gorm:"column:Hour00"`
		Hour01 int    `json:"Hour01" gorm:"column:Hour01"`
		Hour02 int    `json:"Hour02" gorm:"column:Hour02"`
		Hour03 int    `json:"Hour03" gorm:"column:Hour03"`
		Hour04 int    `json:"Hour04" gorm:"column:Hour04"`
		Hour05 int    `json:"Hour05" gorm:"column:Hour05"`
		Hour06 int    `json:"Hour06" gorm:"column:Hour06"`
		Hour07 int    `json:"Hour07" gorm:"column:Hour07"`
		Hour08 int    `json:"Hour08" gorm:"column:Hour08"`
		Hour09 int    `json:"Hour09" gorm:"column:Hour09"`
		Hour10 int    `json:"Hour10" gorm:"column:Hour10"`
		Hour11 int    `json:"Hour11" gorm:"column:Hour11"`
		Hour12 int    `json:"Hour12" gorm:"column:Hour12"`
		Hour13 int    `json:"Hour13" gorm:"column:Hour13"`
		Hour14 int    `json:"Hour14" gorm:"column:Hour14"`
		Hour15 int    `json:"Hour15" gorm:"column:Hour15"`
		Hour16 int    `json:"Hour16" gorm:"column:Hour16"`
		Hour17 int    `json:"Hour17" gorm:"column:Hour17"`
		Hour18 int    `json:"Hour18" gorm:"column:Hour18"`
		Hour19 int    `json:"Hour19" gorm:"column:Hour19"`
		Hour20 int    `json:"Hour20" gorm:"column:Hour20"`
		Hour21 int    `json:"Hour21" gorm:"column:Hour21"`
		Hour22 int    `json:"Hour22" gorm:"column:Hour22"`
		Hour23 int    `json:"Hour23" gorm:"column:Hour23"`
	}

	var results []OnlineBrandUser

	err := server.Db().Gorm().Table("x_online_brand_user").
		Select(`Brand,
       MAX(CASE WHEN HOUR(CreateTime) = 0 THEN Online END) AS Hour00,
       MAX(CASE WHEN HOUR(CreateTime) = 1 THEN Online END) AS Hour01,
       MAX(CASE WHEN HOUR(CreateTime) = 2 THEN Online END) AS Hour02,
       MAX(CASE WHEN HOUR(CreateTime) = 3 THEN Online END) AS Hour03,
       MAX(CASE WHEN HOUR(CreateTime) = 4 THEN Online END) AS Hour04,
       MAX(CASE WHEN HOUR(CreateTime) = 5 THEN Online END) AS Hour05,
       MAX(CASE WHEN HOUR(CreateTime) = 6 THEN Online END) AS Hour06,
       MAX(CASE WHEN HOUR(CreateTime) = 7 THEN Online END) AS Hour07,
       MAX(CASE WHEN HOUR(CreateTime) = 8 THEN Online END) AS Hour08,
       MAX(CASE WHEN HOUR(CreateTime) = 9 THEN Online END) AS Hour09,
       MAX(CASE WHEN HOUR(CreateTime) = 10 THEN Online END) AS Hour10,
       MAX(CASE WHEN HOUR(CreateTime) = 11 THEN Online END) AS Hour11,
       MAX(CASE WHEN HOUR(CreateTime) = 12 THEN Online END) AS Hour12,
       MAX(CASE WHEN HOUR(CreateTime) = 13 THEN Online END) AS Hour13,
       MAX(CASE WHEN HOUR(CreateTime) = 14 THEN Online END) AS Hour14,
       MAX(CASE WHEN HOUR(CreateTime) = 15 THEN Online END) AS Hour15,
       MAX(CASE WHEN HOUR(CreateTime) = 16 THEN Online END) AS Hour16,
       MAX(CASE WHEN HOUR(CreateTime) = 17 THEN Online END) AS Hour17,
       MAX(CASE WHEN HOUR(CreateTime) = 18 THEN Online END) AS Hour18,
       MAX(CASE WHEN HOUR(CreateTime) = 19 THEN Online END) AS Hour19,
       MAX(CASE WHEN HOUR(CreateTime) = 20 THEN Online END) AS Hour20,
       MAX(CASE WHEN HOUR(CreateTime) = 21 THEN Online END) AS Hour21,
       MAX(CASE WHEN HOUR(CreateTime) = 22 THEN Online END) AS Hour22,
       MAX(CASE WHEN HOUR(CreateTime) = 23 THEN Online END) AS Hour23`).
		Where("DATE(CreateTime) = CURDATE()").
		Group("Brand").
		Find(&results).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK(results)
}

func (c *OnlineSocketController) hourlyTopGameOnline(ctx *abugo.AbuHttpContent) {
	errcode := 0
	type OnlineGameUser struct {
		Brand  string `json:"Brand" gorm:"column:Brand"`
		GameId string `json:"GameId" gorm:"column:GameId"`
		Hour00 int    `json:"Hour00" gorm:"column:Hour00"`
		Hour01 int    `json:"Hour01" gorm:"column:Hour01"`
		Hour02 int    `json:"Hour02" gorm:"column:Hour02"`
		Hour03 int    `json:"Hour03" gorm:"column:Hour03"`
		Hour04 int    `json:"Hour04" gorm:"column:Hour04"`
		Hour05 int    `json:"Hour05" gorm:"column:Hour05"`
		Hour06 int    `json:"Hour06" gorm:"column:Hour06"`
		Hour07 int    `json:"Hour07" gorm:"column:Hour07"`
		Hour08 int    `json:"Hour08" gorm:"column:Hour08"`
		Hour09 int    `json:"Hour09" gorm:"column:Hour09"`
		Hour10 int    `json:"Hour10" gorm:"column:Hour10"`
		Hour11 int    `json:"Hour11" gorm:"column:Hour11"`
		Hour12 int    `json:"Hour12" gorm:"column:Hour12"`
		Hour13 int    `json:"Hour13" gorm:"column:Hour13"`
		Hour14 int    `json:"Hour14" gorm:"column:Hour14"`
		Hour15 int    `json:"Hour15" gorm:"column:Hour15"`
		Hour16 int    `json:"Hour16" gorm:"column:Hour16"`
		Hour17 int    `json:"Hour17" gorm:"column:Hour17"`
		Hour18 int    `json:"Hour18" gorm:"column:Hour18"`
		Hour19 int    `json:"Hour19" gorm:"column:Hour19"`
		Hour20 int    `json:"Hour20" gorm:"column:Hour20"`
		Hour21 int    `json:"Hour21" gorm:"column:Hour21"`
		Hour22 int    `json:"Hour22" gorm:"column:Hour22"`
		Hour23 int    `json:"Hour23" gorm:"column:Hour23"`
	}

	var results []OnlineGameUser

	err := server.Db().Gorm().Table("x_online_game_user").
		Select(`Brand,GameId,
       MAX(CASE WHEN HOUR(CreateTime) = 0 THEN Online END) AS Hour00,
       MAX(CASE WHEN HOUR(CreateTime) = 1 THEN Online END) AS Hour01,
       MAX(CASE WHEN HOUR(CreateTime) = 2 THEN Online END) AS Hour02,
       MAX(CASE WHEN HOUR(CreateTime) = 3 THEN Online END) AS Hour03,
       MAX(CASE WHEN HOUR(CreateTime) = 4 THEN Online END) AS Hour04,
       MAX(CASE WHEN HOUR(CreateTime) = 5 THEN Online END) AS Hour05,
       MAX(CASE WHEN HOUR(CreateTime) = 6 THEN Online END) AS Hour06,
       MAX(CASE WHEN HOUR(CreateTime) = 7 THEN Online END) AS Hour07,
       MAX(CASE WHEN HOUR(CreateTime) = 8 THEN Online END) AS Hour08,
       MAX(CASE WHEN HOUR(CreateTime) = 9 THEN Online END) AS Hour09,
       MAX(CASE WHEN HOUR(CreateTime) = 10 THEN Online END) AS Hour10,
       MAX(CASE WHEN HOUR(CreateTime) = 11 THEN Online END) AS Hour11,
       MAX(CASE WHEN HOUR(CreateTime) = 12 THEN Online END) AS Hour12,
       MAX(CASE WHEN HOUR(CreateTime) = 13 THEN Online END) AS Hour13,
       MAX(CASE WHEN HOUR(CreateTime) = 14 THEN Online END) AS Hour14,
       MAX(CASE WHEN HOUR(CreateTime) = 15 THEN Online END) AS Hour15,
       MAX(CASE WHEN HOUR(CreateTime) = 16 THEN Online END) AS Hour16,
       MAX(CASE WHEN HOUR(CreateTime) = 17 THEN Online END) AS Hour17,
       MAX(CASE WHEN HOUR(CreateTime) = 18 THEN Online END) AS Hour18,
       MAX(CASE WHEN HOUR(CreateTime) = 19 THEN Online END) AS Hour19,
       MAX(CASE WHEN HOUR(CreateTime) = 20 THEN Online END) AS Hour20,
       MAX(CASE WHEN HOUR(CreateTime) = 21 THEN Online END) AS Hour21,
       MAX(CASE WHEN HOUR(CreateTime) = 22 THEN Online END) AS Hour22,
       MAX(CASE WHEN HOUR(CreateTime) = 23 THEN Online END) AS Hour23`).
		Where("DATE(CreateTime) = CURDATE()").
		Group("Brand,GameId").
		Find(&results).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK(results)
}
