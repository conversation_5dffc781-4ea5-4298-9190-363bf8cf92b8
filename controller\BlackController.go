package controller

import (
	"strings"
	"xserver/abugo"
	"xserver/server"
)

type BackController struct {
}

func (c *BackController) Init() {
	group := server.Http().NewGroup("/api/black")
	{
		group.Post("/list", c.list)
		group.Post("/add", c.add)
		group.Post("/delete", c.delete)
	}
}

func (c *BackController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		BlackType int // 1退本,2没收
		Address   string
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "黑名单列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "BlackType", "=", reqdata.BlackType, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	total, presult := server.Db().Table("x_balck_address").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("rate", server.GetConfigFloat(reqdata.SellerId, 0, "BlackFeeRate"))
	ctx.Put("data", *presult)
	ctx.Put("total", total)
	ctx.RespOK()

}

func (c *BackController) add(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		ChannelId  int
		Memo       string   `validate:"required"`
		BlackType  int      `validate:"required"` // 1退本,2没收
		Address    []string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "黑名单列表", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	for _, v := range reqdata.Address {
		if len(v) > 0 {
			sql := "replace into x_balck_address(SellerId,Address,BlackType,Memo,CreateAccount,ChannelId)values(?,?,?,?,?,?) "
			server.Db().QueryNoResult(sql, reqdata.SellerId, strings.Trim(v, " "), reqdata.BlackType, reqdata.Memo, token.Account, reqdata.ChannelId)
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("添加黑名单", ctx, reqdata)
}

func (c *BackController) delete(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Id         int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "黑名单列表", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "delete from x_balck_address where SellerId = ? and Id = ? "
	server.Db().QueryNoResult(sql, reqdata.SellerId, reqdata.Id)
	ctx.RespOK()
	server.WriteAdminLog("删除黑名单", ctx, reqdata)
}
