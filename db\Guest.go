package db

import (
	"fmt"
	"xserver/server"
)

type Guest struct {
	Id         int     `gorm:"column:Id"`         //自增id
	Address    string  `gorm:"column:Address"`    //地址
	LiuSuiTrx  float64 `gorm:"column:LiuSuiTrx"`  //trx业绩
	LiuSuiUsdt float64 `gorm:"column:LiuSuiUsdt"` //usdt业绩
	BetTrx     float64 `gorm:"column:BetTrx"`     //trx下注
	BetUsdt    float64 `gorm:"column:BetUsdt"`    //usdt下注
	RewardTrx  float64 `gorm:"column:RewardTrx"`  //trx返奖
	RewardUsdt float64 `gorm:"column:RewardUsdt"` //usdt返奖
	TopAgentId int     `gorm:"column:TopAgentId"` //顶级代理
	AgentId    int     `gorm:"column:AgentId"`    //直属代理
	Agents     string  `gorm:"column:Agents"`     //所有上级代理 数组第一个为直属代理,最后一个为顶级代理
	SellerId   int     `gorm:"column:SellerId"`
}

func (*Guest) TableName() string {
	return "x_guest"
}

func Guest_Page_Data(Page int, PageSize int, SellerId int, Address string, IsBinded int) (int, []Guest) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := Guest{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "Address", "=", Address, "")
	if IsBinded == 1 {
		dbtable = dbtable.Where("AgentId is not null")
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}

	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []Guest{}
	}
	result := []Guest{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result
}
