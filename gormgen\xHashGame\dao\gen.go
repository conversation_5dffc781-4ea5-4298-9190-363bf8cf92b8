// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                                     db,
		AdminUser:                              newAdminUser(db, opts...),
		XActiveDefine:                          newXActiveDefine(db, opts...),
		XActiveDefineSort:                      newXActiveDefineSort(db, opts...),
		XActiveRedeemcodeRecord:                newXActiveRedeemcodeRecord(db, opts...),
		XActiveSellerDefine:                    newXActiveSellerDefine(db, opts...),
		XAdsActiveDailyStat:                    newXAdsActiveDailyStat(db, opts...),
		XAdsDictEvent:                          newXAdsDictEvent(db, opts...),
		XAdsEventTrackingRecord:                newXAdsEventTrackingRecord(db, opts...),
		XAdsFunctionInteractionButtonDailyStat: newXAdsFunctionInteractionButtonDailyStat(db, opts...),
		XAdsFunctionInteractionTabDailyStat:    newXAdsFunctionInteractionTabDailyStat(db, opts...),
		XAdsGameDailyStat:                      newXAdsGameDailyStat(db, opts...),
		XAdsKeywordDailyStat:                   newXAdsKeywordDailyStat(db, opts...),
		XAdsPageDailyStat:                      newXAdsPageDailyStat(db, opts...),
		XAdsRechargeDailyStat:                  newXAdsRechargeDailyStat(db, opts...),
		XAdsUserLabelConfig:                    newXAdsUserLabelConfig(db, opts...),
		XAdsUserProfile:                        newXAdsUserProfile(db, opts...),
		XAdsWithdrawalDailyStat:                newXAdsWithdrawalDailyStat(db, opts...),
		XAgent:                                 newXAgent(db, opts...),
		XAgentBlacklist:                        newXAgentBlacklist(db, opts...),
		XAgentChild:                            newXAgentChild(db, opts...),
		XAgentCode:                             newXAgentCode(db, opts...),
		XAgentCodeT1:                           newXAgentCodeT1(db, opts...),
		XAgentCommissionConfig:                 newXAgentCommissionConfig(db, opts...),
		XAgentCommissionLevelDefine:            newXAgentCommissionLevelDefine(db, opts...),
		XAgentCommissionScheme:                 newXAgentCommissionScheme(db, opts...),
		XAgentCommissionTeamDefine:             newXAgentCommissionTeamDefine(db, opts...),
		XAgentDailly:                           newXAgentDailly(db, opts...),
		XAgentDataDate:                         newXAgentDataDate(db, opts...),
		XAgentGameCommissionConfig:             newXAgentGameCommissionConfig(db, opts...),
		XAgentGameDailly:                       newXAgentGameDailly(db, opts...),
		XAgentGameDefine:                       newXAgentGameDefine(db, opts...),
		XAgentIndependence:                     newXAgentIndependence(db, opts...),
		XAgentIndependenceFenchengHistory:      newXAgentIndependenceFenchengHistory(db, opts...),
		XAgentIndependentCommissionDetail:      newXAgentIndependentCommissionDetail(db, opts...),
		XAgentModeLog:                          newXAgentModeLog(db, opts...),
		XAmountChangeLog:                       newXAmountChangeLog(db, opts...),
		XBlackBlock:                            newXBlackBlock(db, opts...),
		XBonusTaskTemplate:                     newXBonusTaskTemplate(db, opts...),
		XBonusTaskUser:                         newXBonusTaskUser(db, opts...),
		XCaijingDetail:                         newXCaijingDetail(db, opts...),
		XChannel:                               newXChannel(db, opts...),
		XChannelGameList:                       newXChannelGameList(db, opts...),
		XChannelHost:                           newXChannelHost(db, opts...),
		XChatHongbaoCondition:                  newXChatHongbaoCondition(db, opts...),
		XConfig:                                newXConfig(db, opts...),
		XDictChangeParentype:                   newXDictChangeParentype(db, opts...),
		XDictChangetype:                        newXDictChangetype(db, opts...),
		XDictGametype:                          newXDictGametype(db, opts...),
		XFinanceMethod:                         newXFinanceMethod(db, opts...),
		XFinanceSymbol:                         newXFinanceSymbol(db, opts...),
		XGame:                                  newXGame(db, opts...),
		XGameBrand:                             newXGameBrand(db, opts...),
		XGameChain:                             newXGameChain(db, opts...),
		XGameList:                              newXGameList(db, opts...),
		XGamePeriod:                            newXGamePeriod(db, opts...),
		XHomeCarouselV2:                        newXHomeCarouselV2(db, opts...),
		XHostTag:                               newXHostTag(db, opts...),
		XKefuBindHistory:                       newXKefuBindHistory(db, opts...),
		XLangGameList:                          newXLangGameList(db, opts...),
		XLangList:                              newXLangList(db, opts...),
		XLoginLog:                              newXLoginLog(db, opts...),
		XManAmountDetail:                       newXManAmountDetail(db, opts...),
		XNoticeV2:                              newXNoticeV2(db, opts...),
		XOperationsInputDataDate:               newXOperationsInputDataDate(db, opts...),
		XOrder:                                 newXOrder(db, opts...),
		XRecharge:                              newXRecharge(db, opts...),
		XRobotAdStartRecord:                    newXRobotAdStartRecord(db, opts...),
		XRobotConfig:                           newXRobotConfig(db, opts...),
		XRobotMessageTemplte:                   newXRobotMessageTemplte(db, opts...),
		XRobotMissionTemplte:                   newXRobotMissionTemplte(db, opts...),
		XRobotPostUserInfo:                     newXRobotPostUserInfo(db, opts...),
		XRobotPushActivityRecord:               newXRobotPushActivityRecord(db, opts...),
		XRobotPushMsgConfig:                    newXRobotPushMsgConfig(db, opts...),
		XRobotRedbagQuestion:                   newXRobotRedbagQuestion(db, opts...),
		XRobotRedbagReportUser:                 newXRobotRedbagReportUser(db, opts...),
		XRobotReportImportFlow:                 newXRobotReportImportFlow(db, opts...),
		XRobotReportImportFlowUpload:           newXRobotReportImportFlowUpload(db, opts...),
		XRobotStatisticsDay:                    newXRobotStatisticsDay(db, opts...),
		XSeller:                                newXSeller(db, opts...),
		XSellerInputDataDate:                   newXSellerInputDataDate(db, opts...),
		XTbBankerConfig:                        newXTbBankerConfig(db, opts...),
		XTbBankerUser:                          newXTbBankerUser(db, opts...),
		XTbRewardword:                          newXTbRewardword(db, opts...),
		XTbWinlostConfig:                       newXTbWinlostConfig(db, opts...),
		XTgAccount:                             newXTgAccount(db, opts...),
		XTgChat:                                newXTgChat(db, opts...),
		XTgMessage:                             newXTgMessage(db, opts...),
		XTgRedpacket:                           newXTgRedpacket(db, opts...),
		XTgRedpacketInviteCount:                newXTgRedpacketInviteCount(db, opts...),
		XTgRedpacketLog:                        newXTgRedpacketLog(db, opts...),
		XTgRedpacketUser:                       newXTgRedpacketUser(db, opts...),
		XTgRedpacketWithdrawLog:                newXTgRedpacketWithdrawLog(db, opts...),
		XTgRobotGuide:                          newXTgRobotGuide(db, opts...),
		XTgRobotRedpacket:                      newXTgRobotRedpacket(db, opts...),
		XTgUserRobot:                           newXTgUserRobot(db, opts...),
		XThirdDianzhi:                          newXThirdDianzhi(db, opts...),
		XThirdLive:                             newXThirdLive(db, opts...),
		XThirdLottery:                          newXThirdLottery(db, opts...),
		XThirdQipai:                            newXThirdQipai(db, opts...),
		XThirdQuwei:                            newXThirdQuwei(db, opts...),
		XThirdSport:                            newXThirdSport(db, opts...),
		XThirdTexa:                             newXThirdTexa(db, opts...),
		XTiyanjinActive:                        newXTiyanjinActive(db, opts...),
		XTiyanjing:                             newXTiyanjing(db, opts...),
		XTyjBroadcastConfig:                    newXTyjBroadcastConfig(db, opts...),
		XUser:                                  newXUser(db, opts...),
		XUserDailly:                            newXUserDailly(db, opts...),
		XVerify:                                newXVerify(db, opts...),
		XVipDefine:                             newXVipDefine(db, opts...),
		XVipGameDefine:                         newXVipGameDefine(db, opts...),
		XVipInfo:                               newXVipInfo(db, opts...),
		XWhiteBlock:                            newXWhiteBlock(db, opts...),
		XWithdraw:                              newXWithdraw(db, opts...),
		XWithdrawAddress:                       newXWithdrawAddress(db, opts...),
		XWithdrawGame:                          newXWithdrawGame(db, opts...),
		XWithdrawLimitConfig:                   newXWithdrawLimitConfig(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	AdminUser                              adminUser
	XActiveDefine                          xActiveDefine
	XActiveDefineSort                      xActiveDefineSort
	XActiveRedeemcodeRecord                xActiveRedeemcodeRecord
	XActiveSellerDefine                    xActiveSellerDefine
	XAdsActiveDailyStat                    xAdsActiveDailyStat
	XAdsDictEvent                          xAdsDictEvent
	XAdsEventTrackingRecord                xAdsEventTrackingRecord
	XAdsFunctionInteractionButtonDailyStat xAdsFunctionInteractionButtonDailyStat
	XAdsFunctionInteractionTabDailyStat    xAdsFunctionInteractionTabDailyStat
	XAdsGameDailyStat                      xAdsGameDailyStat
	XAdsKeywordDailyStat                   xAdsKeywordDailyStat
	XAdsPageDailyStat                      xAdsPageDailyStat
	XAdsRechargeDailyStat                  xAdsRechargeDailyStat
	XAdsUserLabelConfig                    xAdsUserLabelConfig
	XAdsUserProfile                        xAdsUserProfile
	XAdsWithdrawalDailyStat                xAdsWithdrawalDailyStat
	XAgent                                 xAgent
	XAgentBlacklist                        xAgentBlacklist
	XAgentChild                            xAgentChild
	XAgentCode                             xAgentCode
	XAgentCodeT1                           xAgentCodeT1
	XAgentCommissionConfig                 xAgentCommissionConfig
	XAgentCommissionLevelDefine            xAgentCommissionLevelDefine
	XAgentCommissionScheme                 xAgentCommissionScheme
	XAgentCommissionTeamDefine             xAgentCommissionTeamDefine
	XAgentDailly                           xAgentDailly
	XAgentDataDate                         xAgentDataDate
	XAgentGameCommissionConfig             xAgentGameCommissionConfig
	XAgentGameDailly                       xAgentGameDailly
	XAgentGameDefine                       xAgentGameDefine
	XAgentIndependence                     xAgentIndependence
	XAgentIndependenceFenchengHistory      xAgentIndependenceFenchengHistory
	XAgentIndependentCommissionDetail      xAgentIndependentCommissionDetail
	XAgentModeLog                          xAgentModeLog
	XAmountChangeLog                       xAmountChangeLog
	XBlackBlock                            xBlackBlock
	XBonusTaskTemplate                     xBonusTaskTemplate
	XBonusTaskUser                         xBonusTaskUser
	XCaijingDetail                         xCaijingDetail
	XChannel                               xChannel
	XChannelGameList                       xChannelGameList
	XChannelHost                           xChannelHost
	XChatHongbaoCondition                  xChatHongbaoCondition
	XConfig                                xConfig
	XDictChangeParentype                   xDictChangeParentype
	XDictChangetype                        xDictChangetype
	XDictGametype                          xDictGametype
	XFinanceMethod                         xFinanceMethod
	XFinanceSymbol                         xFinanceSymbol
	XGame                                  xGame
	XGameBrand                             xGameBrand
	XGameChain                             xGameChain
	XGameList                              xGameList
	XGamePeriod                            xGamePeriod
	XHomeCarouselV2                        xHomeCarouselV2
	XHostTag                               xHostTag
	XKefuBindHistory                       xKefuBindHistory
	XLangGameList                          xLangGameList
	XLangList                              xLangList
	XLoginLog                              xLoginLog
	XManAmountDetail                       xManAmountDetail
	XNoticeV2                              xNoticeV2
	XOperationsInputDataDate               xOperationsInputDataDate
	XOrder                                 xOrder
	XRecharge                              xRecharge
	XRobotAdStartRecord                    xRobotAdStartRecord
	XRobotConfig                           xRobotConfig
	XRobotMessageTemplte                   xRobotMessageTemplte
	XRobotMissionTemplte                   xRobotMissionTemplte
	XRobotPostUserInfo                     xRobotPostUserInfo
	XRobotPushActivityRecord               xRobotPushActivityRecord
	XRobotPushMsgConfig                    xRobotPushMsgConfig
	XRobotRedbagQuestion                   xRobotRedbagQuestion
	XRobotRedbagReportUser                 xRobotRedbagReportUser
	XRobotReportImportFlow                 xRobotReportImportFlow
	XRobotReportImportFlowUpload           xRobotReportImportFlowUpload
	XRobotStatisticsDay                    xRobotStatisticsDay
	XSeller                                xSeller
	XSellerInputDataDate                   xSellerInputDataDate
	XTbBankerConfig                        xTbBankerConfig
	XTbBankerUser                          xTbBankerUser
	XTbRewardword                          xTbRewardword
	XTbWinlostConfig                       xTbWinlostConfig
	XTgAccount                             xTgAccount
	XTgChat                                xTgChat
	XTgMessage                             xTgMessage
	XTgRedpacket                           xTgRedpacket
	XTgRedpacketInviteCount                xTgRedpacketInviteCount
	XTgRedpacketLog                        xTgRedpacketLog
	XTgRedpacketUser                       xTgRedpacketUser
	XTgRedpacketWithdrawLog                xTgRedpacketWithdrawLog
	XTgRobotGuide                          xTgRobotGuide
	XTgRobotRedpacket                      xTgRobotRedpacket
	XTgUserRobot                           xTgUserRobot
	XThirdDianzhi                          xThirdDianzhi
	XThirdLive                             xThirdLive
	XThirdLottery                          xThirdLottery
	XThirdQipai                            xThirdQipai
	XThirdQuwei                            xThirdQuwei
	XThirdSport                            xThirdSport
	XThirdTexa                             xThirdTexa
	XTiyanjinActive                        xTiyanjinActive
	XTiyanjing                             xTiyanjing
	XTyjBroadcastConfig                    xTyjBroadcastConfig
	XUser                                  xUser
	XUserDailly                            xUserDailly
	XVerify                                xVerify
	XVipDefine                             xVipDefine
	XVipGameDefine                         xVipGameDefine
	XVipInfo                               xVipInfo
	XWhiteBlock                            xWhiteBlock
	XWithdraw                              xWithdraw
	XWithdrawAddress                       xWithdrawAddress
	XWithdrawGame                          xWithdrawGame
	XWithdrawLimitConfig                   xWithdrawLimitConfig
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                                     db,
		AdminUser:                              q.AdminUser.clone(db),
		XActiveDefine:                          q.XActiveDefine.clone(db),
		XActiveDefineSort:                      q.XActiveDefineSort.clone(db),
		XActiveRedeemcodeRecord:                q.XActiveRedeemcodeRecord.clone(db),
		XActiveSellerDefine:                    q.XActiveSellerDefine.clone(db),
		XAdsActiveDailyStat:                    q.XAdsActiveDailyStat.clone(db),
		XAdsDictEvent:                          q.XAdsDictEvent.clone(db),
		XAdsEventTrackingRecord:                q.XAdsEventTrackingRecord.clone(db),
		XAdsFunctionInteractionButtonDailyStat: q.XAdsFunctionInteractionButtonDailyStat.clone(db),
		XAdsFunctionInteractionTabDailyStat:    q.XAdsFunctionInteractionTabDailyStat.clone(db),
		XAdsGameDailyStat:                      q.XAdsGameDailyStat.clone(db),
		XAdsKeywordDailyStat:                   q.XAdsKeywordDailyStat.clone(db),
		XAdsPageDailyStat:                      q.XAdsPageDailyStat.clone(db),
		XAdsRechargeDailyStat:                  q.XAdsRechargeDailyStat.clone(db),
		XAdsUserLabelConfig:                    q.XAdsUserLabelConfig.clone(db),
		XAdsUserProfile:                        q.XAdsUserProfile.clone(db),
		XAdsWithdrawalDailyStat:                q.XAdsWithdrawalDailyStat.clone(db),
		XAgent:                                 q.XAgent.clone(db),
		XAgentBlacklist:                        q.XAgentBlacklist.clone(db),
		XAgentChild:                            q.XAgentChild.clone(db),
		XAgentCode:                             q.XAgentCode.clone(db),
		XAgentCodeT1:                           q.XAgentCodeT1.clone(db),
		XAgentCommissionConfig:                 q.XAgentCommissionConfig.clone(db),
		XAgentCommissionLevelDefine:            q.XAgentCommissionLevelDefine.clone(db),
		XAgentCommissionScheme:                 q.XAgentCommissionScheme.clone(db),
		XAgentCommissionTeamDefine:             q.XAgentCommissionTeamDefine.clone(db),
		XAgentDailly:                           q.XAgentDailly.clone(db),
		XAgentDataDate:                         q.XAgentDataDate.clone(db),
		XAgentGameCommissionConfig:             q.XAgentGameCommissionConfig.clone(db),
		XAgentGameDailly:                       q.XAgentGameDailly.clone(db),
		XAgentGameDefine:                       q.XAgentGameDefine.clone(db),
		XAgentIndependence:                     q.XAgentIndependence.clone(db),
		XAgentIndependenceFenchengHistory:      q.XAgentIndependenceFenchengHistory.clone(db),
		XAgentIndependentCommissionDetail:      q.XAgentIndependentCommissionDetail.clone(db),
		XAgentModeLog:                          q.XAgentModeLog.clone(db),
		XAmountChangeLog:                       q.XAmountChangeLog.clone(db),
		XBlackBlock:                            q.XBlackBlock.clone(db),
		XBonusTaskTemplate:                     q.XBonusTaskTemplate.clone(db),
		XBonusTaskUser:                         q.XBonusTaskUser.clone(db),
		XCaijingDetail:                         q.XCaijingDetail.clone(db),
		XChannel:                               q.XChannel.clone(db),
		XChannelGameList:                       q.XChannelGameList.clone(db),
		XChannelHost:                           q.XChannelHost.clone(db),
		XChatHongbaoCondition:                  q.XChatHongbaoCondition.clone(db),
		XConfig:                                q.XConfig.clone(db),
		XDictChangeParentype:                   q.XDictChangeParentype.clone(db),
		XDictChangetype:                        q.XDictChangetype.clone(db),
		XDictGametype:                          q.XDictGametype.clone(db),
		XFinanceMethod:                         q.XFinanceMethod.clone(db),
		XFinanceSymbol:                         q.XFinanceSymbol.clone(db),
		XGame:                                  q.XGame.clone(db),
		XGameBrand:                             q.XGameBrand.clone(db),
		XGameChain:                             q.XGameChain.clone(db),
		XGameList:                              q.XGameList.clone(db),
		XGamePeriod:                            q.XGamePeriod.clone(db),
		XHomeCarouselV2:                        q.XHomeCarouselV2.clone(db),
		XHostTag:                               q.XHostTag.clone(db),
		XKefuBindHistory:                       q.XKefuBindHistory.clone(db),
		XLangGameList:                          q.XLangGameList.clone(db),
		XLangList:                              q.XLangList.clone(db),
		XLoginLog:                              q.XLoginLog.clone(db),
		XManAmountDetail:                       q.XManAmountDetail.clone(db),
		XNoticeV2:                              q.XNoticeV2.clone(db),
		XOperationsInputDataDate:               q.XOperationsInputDataDate.clone(db),
		XOrder:                                 q.XOrder.clone(db),
		XRecharge:                              q.XRecharge.clone(db),
		XRobotAdStartRecord:                    q.XRobotAdStartRecord.clone(db),
		XRobotConfig:                           q.XRobotConfig.clone(db),
		XRobotMessageTemplte:                   q.XRobotMessageTemplte.clone(db),
		XRobotMissionTemplte:                   q.XRobotMissionTemplte.clone(db),
		XRobotPostUserInfo:                     q.XRobotPostUserInfo.clone(db),
		XRobotPushActivityRecord:               q.XRobotPushActivityRecord.clone(db),
		XRobotPushMsgConfig:                    q.XRobotPushMsgConfig.clone(db),
		XRobotRedbagQuestion:                   q.XRobotRedbagQuestion.clone(db),
		XRobotRedbagReportUser:                 q.XRobotRedbagReportUser.clone(db),
		XRobotReportImportFlow:                 q.XRobotReportImportFlow.clone(db),
		XRobotReportImportFlowUpload:           q.XRobotReportImportFlowUpload.clone(db),
		XRobotStatisticsDay:                    q.XRobotStatisticsDay.clone(db),
		XSeller:                                q.XSeller.clone(db),
		XSellerInputDataDate:                   q.XSellerInputDataDate.clone(db),
		XTbBankerConfig:                        q.XTbBankerConfig.clone(db),
		XTbBankerUser:                          q.XTbBankerUser.clone(db),
		XTbRewardword:                          q.XTbRewardword.clone(db),
		XTbWinlostConfig:                       q.XTbWinlostConfig.clone(db),
		XTgAccount:                             q.XTgAccount.clone(db),
		XTgChat:                                q.XTgChat.clone(db),
		XTgMessage:                             q.XTgMessage.clone(db),
		XTgRedpacket:                           q.XTgRedpacket.clone(db),
		XTgRedpacketInviteCount:                q.XTgRedpacketInviteCount.clone(db),
		XTgRedpacketLog:                        q.XTgRedpacketLog.clone(db),
		XTgRedpacketUser:                       q.XTgRedpacketUser.clone(db),
		XTgRedpacketWithdrawLog:                q.XTgRedpacketWithdrawLog.clone(db),
		XTgRobotGuide:                          q.XTgRobotGuide.clone(db),
		XTgRobotRedpacket:                      q.XTgRobotRedpacket.clone(db),
		XTgUserRobot:                           q.XTgUserRobot.clone(db),
		XThirdDianzhi:                          q.XThirdDianzhi.clone(db),
		XThirdLive:                             q.XThirdLive.clone(db),
		XThirdLottery:                          q.XThirdLottery.clone(db),
		XThirdQipai:                            q.XThirdQipai.clone(db),
		XThirdQuwei:                            q.XThirdQuwei.clone(db),
		XThirdSport:                            q.XThirdSport.clone(db),
		XThirdTexa:                             q.XThirdTexa.clone(db),
		XTiyanjinActive:                        q.XTiyanjinActive.clone(db),
		XTiyanjing:                             q.XTiyanjing.clone(db),
		XTyjBroadcastConfig:                    q.XTyjBroadcastConfig.clone(db),
		XUser:                                  q.XUser.clone(db),
		XUserDailly:                            q.XUserDailly.clone(db),
		XVerify:                                q.XVerify.clone(db),
		XVipDefine:                             q.XVipDefine.clone(db),
		XVipGameDefine:                         q.XVipGameDefine.clone(db),
		XVipInfo:                               q.XVipInfo.clone(db),
		XWhiteBlock:                            q.XWhiteBlock.clone(db),
		XWithdraw:                              q.XWithdraw.clone(db),
		XWithdrawAddress:                       q.XWithdrawAddress.clone(db),
		XWithdrawGame:                          q.XWithdrawGame.clone(db),
		XWithdrawLimitConfig:                   q.XWithdrawLimitConfig.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                                     db,
		AdminUser:                              q.AdminUser.replaceDB(db),
		XActiveDefine:                          q.XActiveDefine.replaceDB(db),
		XActiveDefineSort:                      q.XActiveDefineSort.replaceDB(db),
		XActiveRedeemcodeRecord:                q.XActiveRedeemcodeRecord.replaceDB(db),
		XActiveSellerDefine:                    q.XActiveSellerDefine.replaceDB(db),
		XAdsActiveDailyStat:                    q.XAdsActiveDailyStat.replaceDB(db),
		XAdsDictEvent:                          q.XAdsDictEvent.replaceDB(db),
		XAdsEventTrackingRecord:                q.XAdsEventTrackingRecord.replaceDB(db),
		XAdsFunctionInteractionButtonDailyStat: q.XAdsFunctionInteractionButtonDailyStat.replaceDB(db),
		XAdsFunctionInteractionTabDailyStat:    q.XAdsFunctionInteractionTabDailyStat.replaceDB(db),
		XAdsGameDailyStat:                      q.XAdsGameDailyStat.replaceDB(db),
		XAdsKeywordDailyStat:                   q.XAdsKeywordDailyStat.replaceDB(db),
		XAdsPageDailyStat:                      q.XAdsPageDailyStat.replaceDB(db),
		XAdsRechargeDailyStat:                  q.XAdsRechargeDailyStat.replaceDB(db),
		XAdsUserLabelConfig:                    q.XAdsUserLabelConfig.replaceDB(db),
		XAdsUserProfile:                        q.XAdsUserProfile.replaceDB(db),
		XAdsWithdrawalDailyStat:                q.XAdsWithdrawalDailyStat.replaceDB(db),
		XAgent:                                 q.XAgent.replaceDB(db),
		XAgentBlacklist:                        q.XAgentBlacklist.replaceDB(db),
		XAgentChild:                            q.XAgentChild.replaceDB(db),
		XAgentCode:                             q.XAgentCode.replaceDB(db),
		XAgentCodeT1:                           q.XAgentCodeT1.replaceDB(db),
		XAgentCommissionConfig:                 q.XAgentCommissionConfig.replaceDB(db),
		XAgentCommissionLevelDefine:            q.XAgentCommissionLevelDefine.replaceDB(db),
		XAgentCommissionScheme:                 q.XAgentCommissionScheme.replaceDB(db),
		XAgentCommissionTeamDefine:             q.XAgentCommissionTeamDefine.replaceDB(db),
		XAgentDailly:                           q.XAgentDailly.replaceDB(db),
		XAgentDataDate:                         q.XAgentDataDate.replaceDB(db),
		XAgentGameCommissionConfig:             q.XAgentGameCommissionConfig.replaceDB(db),
		XAgentGameDailly:                       q.XAgentGameDailly.replaceDB(db),
		XAgentGameDefine:                       q.XAgentGameDefine.replaceDB(db),
		XAgentIndependence:                     q.XAgentIndependence.replaceDB(db),
		XAgentIndependenceFenchengHistory:      q.XAgentIndependenceFenchengHistory.replaceDB(db),
		XAgentIndependentCommissionDetail:      q.XAgentIndependentCommissionDetail.replaceDB(db),
		XAgentModeLog:                          q.XAgentModeLog.replaceDB(db),
		XAmountChangeLog:                       q.XAmountChangeLog.replaceDB(db),
		XBlackBlock:                            q.XBlackBlock.replaceDB(db),
		XBonusTaskTemplate:                     q.XBonusTaskTemplate.replaceDB(db),
		XBonusTaskUser:                         q.XBonusTaskUser.replaceDB(db),
		XCaijingDetail:                         q.XCaijingDetail.replaceDB(db),
		XChannel:                               q.XChannel.replaceDB(db),
		XChannelGameList:                       q.XChannelGameList.replaceDB(db),
		XChannelHost:                           q.XChannelHost.replaceDB(db),
		XChatHongbaoCondition:                  q.XChatHongbaoCondition.replaceDB(db),
		XConfig:                                q.XConfig.replaceDB(db),
		XDictChangeParentype:                   q.XDictChangeParentype.replaceDB(db),
		XDictChangetype:                        q.XDictChangetype.replaceDB(db),
		XDictGametype:                          q.XDictGametype.replaceDB(db),
		XFinanceMethod:                         q.XFinanceMethod.replaceDB(db),
		XFinanceSymbol:                         q.XFinanceSymbol.replaceDB(db),
		XGame:                                  q.XGame.replaceDB(db),
		XGameBrand:                             q.XGameBrand.replaceDB(db),
		XGameChain:                             q.XGameChain.replaceDB(db),
		XGameList:                              q.XGameList.replaceDB(db),
		XGamePeriod:                            q.XGamePeriod.replaceDB(db),
		XHomeCarouselV2:                        q.XHomeCarouselV2.replaceDB(db),
		XHostTag:                               q.XHostTag.replaceDB(db),
		XKefuBindHistory:                       q.XKefuBindHistory.replaceDB(db),
		XLangGameList:                          q.XLangGameList.replaceDB(db),
		XLangList:                              q.XLangList.replaceDB(db),
		XLoginLog:                              q.XLoginLog.replaceDB(db),
		XManAmountDetail:                       q.XManAmountDetail.replaceDB(db),
		XNoticeV2:                              q.XNoticeV2.replaceDB(db),
		XOperationsInputDataDate:               q.XOperationsInputDataDate.replaceDB(db),
		XOrder:                                 q.XOrder.replaceDB(db),
		XRecharge:                              q.XRecharge.replaceDB(db),
		XRobotAdStartRecord:                    q.XRobotAdStartRecord.replaceDB(db),
		XRobotConfig:                           q.XRobotConfig.replaceDB(db),
		XRobotMessageTemplte:                   q.XRobotMessageTemplte.replaceDB(db),
		XRobotMissionTemplte:                   q.XRobotMissionTemplte.replaceDB(db),
		XRobotPostUserInfo:                     q.XRobotPostUserInfo.replaceDB(db),
		XRobotPushActivityRecord:               q.XRobotPushActivityRecord.replaceDB(db),
		XRobotPushMsgConfig:                    q.XRobotPushMsgConfig.replaceDB(db),
		XRobotRedbagQuestion:                   q.XRobotRedbagQuestion.replaceDB(db),
		XRobotRedbagReportUser:                 q.XRobotRedbagReportUser.replaceDB(db),
		XRobotReportImportFlow:                 q.XRobotReportImportFlow.replaceDB(db),
		XRobotReportImportFlowUpload:           q.XRobotReportImportFlowUpload.replaceDB(db),
		XRobotStatisticsDay:                    q.XRobotStatisticsDay.replaceDB(db),
		XSeller:                                q.XSeller.replaceDB(db),
		XSellerInputDataDate:                   q.XSellerInputDataDate.replaceDB(db),
		XTbBankerConfig:                        q.XTbBankerConfig.replaceDB(db),
		XTbBankerUser:                          q.XTbBankerUser.replaceDB(db),
		XTbRewardword:                          q.XTbRewardword.replaceDB(db),
		XTbWinlostConfig:                       q.XTbWinlostConfig.replaceDB(db),
		XTgAccount:                             q.XTgAccount.replaceDB(db),
		XTgChat:                                q.XTgChat.replaceDB(db),
		XTgMessage:                             q.XTgMessage.replaceDB(db),
		XTgRedpacket:                           q.XTgRedpacket.replaceDB(db),
		XTgRedpacketInviteCount:                q.XTgRedpacketInviteCount.replaceDB(db),
		XTgRedpacketLog:                        q.XTgRedpacketLog.replaceDB(db),
		XTgRedpacketUser:                       q.XTgRedpacketUser.replaceDB(db),
		XTgRedpacketWithdrawLog:                q.XTgRedpacketWithdrawLog.replaceDB(db),
		XTgRobotGuide:                          q.XTgRobotGuide.replaceDB(db),
		XTgRobotRedpacket:                      q.XTgRobotRedpacket.replaceDB(db),
		XTgUserRobot:                           q.XTgUserRobot.replaceDB(db),
		XThirdDianzhi:                          q.XThirdDianzhi.replaceDB(db),
		XThirdLive:                             q.XThirdLive.replaceDB(db),
		XThirdLottery:                          q.XThirdLottery.replaceDB(db),
		XThirdQipai:                            q.XThirdQipai.replaceDB(db),
		XThirdQuwei:                            q.XThirdQuwei.replaceDB(db),
		XThirdSport:                            q.XThirdSport.replaceDB(db),
		XThirdTexa:                             q.XThirdTexa.replaceDB(db),
		XTiyanjinActive:                        q.XTiyanjinActive.replaceDB(db),
		XTiyanjing:                             q.XTiyanjing.replaceDB(db),
		XTyjBroadcastConfig:                    q.XTyjBroadcastConfig.replaceDB(db),
		XUser:                                  q.XUser.replaceDB(db),
		XUserDailly:                            q.XUserDailly.replaceDB(db),
		XVerify:                                q.XVerify.replaceDB(db),
		XVipDefine:                             q.XVipDefine.replaceDB(db),
		XVipGameDefine:                         q.XVipGameDefine.replaceDB(db),
		XVipInfo:                               q.XVipInfo.replaceDB(db),
		XWhiteBlock:                            q.XWhiteBlock.replaceDB(db),
		XWithdraw:                              q.XWithdraw.replaceDB(db),
		XWithdrawAddress:                       q.XWithdrawAddress.replaceDB(db),
		XWithdrawGame:                          q.XWithdrawGame.replaceDB(db),
		XWithdrawLimitConfig:                   q.XWithdrawLimitConfig.replaceDB(db),
	}
}

type queryCtx struct {
	AdminUser                              *adminUserDo
	XActiveDefine                          *xActiveDefineDo
	XActiveDefineSort                      *xActiveDefineSortDo
	XActiveRedeemcodeRecord                *xActiveRedeemcodeRecordDo
	XActiveSellerDefine                    *xActiveSellerDefineDo
	XAdsActiveDailyStat                    *xAdsActiveDailyStatDo
	XAdsDictEvent                          *xAdsDictEventDo
	XAdsEventTrackingRecord                *xAdsEventTrackingRecordDo
	XAdsFunctionInteractionButtonDailyStat *xAdsFunctionInteractionButtonDailyStatDo
	XAdsFunctionInteractionTabDailyStat    *xAdsFunctionInteractionTabDailyStatDo
	XAdsGameDailyStat                      *xAdsGameDailyStatDo
	XAdsKeywordDailyStat                   *xAdsKeywordDailyStatDo
	XAdsPageDailyStat                      *xAdsPageDailyStatDo
	XAdsRechargeDailyStat                  *xAdsRechargeDailyStatDo
	XAdsUserLabelConfig                    *xAdsUserLabelConfigDo
	XAdsUserProfile                        *xAdsUserProfileDo
	XAdsWithdrawalDailyStat                *xAdsWithdrawalDailyStatDo
	XAgent                                 *xAgentDo
	XAgentBlacklist                        *xAgentBlacklistDo
	XAgentChild                            *xAgentChildDo
	XAgentCode                             *xAgentCodeDo
	XAgentCodeT1                           *xAgentCodeT1Do
	XAgentCommissionConfig                 *xAgentCommissionConfigDo
	XAgentCommissionLevelDefine            *xAgentCommissionLevelDefineDo
	XAgentCommissionScheme                 *xAgentCommissionSchemeDo
	XAgentCommissionTeamDefine             *xAgentCommissionTeamDefineDo
	XAgentDailly                           *xAgentDaillyDo
	XAgentDataDate                         *xAgentDataDateDo
	XAgentGameCommissionConfig             *xAgentGameCommissionConfigDo
	XAgentGameDailly                       *xAgentGameDaillyDo
	XAgentGameDefine                       *xAgentGameDefineDo
	XAgentIndependence                     *xAgentIndependenceDo
	XAgentIndependenceFenchengHistory      *xAgentIndependenceFenchengHistoryDo
	XAgentIndependentCommissionDetail      *xAgentIndependentCommissionDetailDo
	XAgentModeLog                          *xAgentModeLogDo
	XAmountChangeLog                       *xAmountChangeLogDo
	XBlackBlock                            *xBlackBlockDo
	XBonusTaskTemplate                     *xBonusTaskTemplateDo
	XBonusTaskUser                         *xBonusTaskUserDo
	XCaijingDetail                         *xCaijingDetailDo
	XChannel                               *xChannelDo
	XChannelGameList                       *xChannelGameListDo
	XChannelHost                           *xChannelHostDo
	XChatHongbaoCondition                  *xChatHongbaoConditionDo
	XConfig                                *xConfigDo
	XDictChangeParentype                   *xDictChangeParentypeDo
	XDictChangetype                        *xDictChangetypeDo
	XDictGametype                          *xDictGametypeDo
	XFinanceMethod                         *xFinanceMethodDo
	XFinanceSymbol                         *xFinanceSymbolDo
	XGame                                  *xGameDo
	XGameBrand                             *xGameBrandDo
	XGameChain                             *xGameChainDo
	XGameList                              *xGameListDo
	XGamePeriod                            *xGamePeriodDo
	XHomeCarouselV2                        *xHomeCarouselV2Do
	XHostTag                               *xHostTagDo
	XKefuBindHistory                       *xKefuBindHistoryDo
	XLangGameList                          *xLangGameListDo
	XLangList                              *xLangListDo
	XLoginLog                              *xLoginLogDo
	XManAmountDetail                       *xManAmountDetailDo
	XNoticeV2                              *xNoticeV2Do
	XOperationsInputDataDate               *xOperationsInputDataDateDo
	XOrder                                 *xOrderDo
	XRecharge                              *xRechargeDo
	XRobotAdStartRecord                    *xRobotAdStartRecordDo
	XRobotConfig                           *xRobotConfigDo
	XRobotMessageTemplte                   *xRobotMessageTemplteDo
	XRobotMissionTemplte                   *xRobotMissionTemplteDo
	XRobotPostUserInfo                     *xRobotPostUserInfoDo
	XRobotPushActivityRecord               *xRobotPushActivityRecordDo
	XRobotPushMsgConfig                    *xRobotPushMsgConfigDo
	XRobotRedbagQuestion                   *xRobotRedbagQuestionDo
	XRobotRedbagReportUser                 *xRobotRedbagReportUserDo
	XRobotReportImportFlow                 *xRobotReportImportFlowDo
	XRobotReportImportFlowUpload           *xRobotReportImportFlowUploadDo
	XRobotStatisticsDay                    *xRobotStatisticsDayDo
	XSeller                                *xSellerDo
	XSellerInputDataDate                   *xSellerInputDataDateDo
	XTbBankerConfig                        *xTbBankerConfigDo
	XTbBankerUser                          *xTbBankerUserDo
	XTbRewardword                          *xTbRewardwordDo
	XTbWinlostConfig                       *xTbWinlostConfigDo
	XTgAccount                             *xTgAccountDo
	XTgChat                                *xTgChatDo
	XTgMessage                             *xTgMessageDo
	XTgRedpacket                           *xTgRedpacketDo
	XTgRedpacketInviteCount                *xTgRedpacketInviteCountDo
	XTgRedpacketLog                        *xTgRedpacketLogDo
	XTgRedpacketUser                       *xTgRedpacketUserDo
	XTgRedpacketWithdrawLog                *xTgRedpacketWithdrawLogDo
	XTgRobotGuide                          *xTgRobotGuideDo
	XTgRobotRedpacket                      *xTgRobotRedpacketDo
	XTgUserRobot                           *xTgUserRobotDo
	XThirdDianzhi                          *xThirdDianzhiDo
	XThirdLive                             *xThirdLiveDo
	XThirdLottery                          *xThirdLotteryDo
	XThirdQipai                            *xThirdQipaiDo
	XThirdQuwei                            *xThirdQuweiDo
	XThirdSport                            *xThirdSportDo
	XThirdTexa                             *xThirdTexaDo
	XTiyanjinActive                        *xTiyanjinActiveDo
	XTiyanjing                             *xTiyanjingDo
	XTyjBroadcastConfig                    *xTyjBroadcastConfigDo
	XUser                                  *xUserDo
	XUserDailly                            *xUserDaillyDo
	XVerify                                *xVerifyDo
	XVipDefine                             *xVipDefineDo
	XVipGameDefine                         *xVipGameDefineDo
	XVipInfo                               *xVipInfoDo
	XWhiteBlock                            *xWhiteBlockDo
	XWithdraw                              *xWithdrawDo
	XWithdrawAddress                       *xWithdrawAddressDo
	XWithdrawGame                          *xWithdrawGameDo
	XWithdrawLimitConfig                   *xWithdrawLimitConfigDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		AdminUser:                              q.AdminUser.WithContext(ctx),
		XActiveDefine:                          q.XActiveDefine.WithContext(ctx),
		XActiveDefineSort:                      q.XActiveDefineSort.WithContext(ctx),
		XActiveRedeemcodeRecord:                q.XActiveRedeemcodeRecord.WithContext(ctx),
		XActiveSellerDefine:                    q.XActiveSellerDefine.WithContext(ctx),
		XAdsActiveDailyStat:                    q.XAdsActiveDailyStat.WithContext(ctx),
		XAdsDictEvent:                          q.XAdsDictEvent.WithContext(ctx),
		XAdsEventTrackingRecord:                q.XAdsEventTrackingRecord.WithContext(ctx),
		XAdsFunctionInteractionButtonDailyStat: q.XAdsFunctionInteractionButtonDailyStat.WithContext(ctx),
		XAdsFunctionInteractionTabDailyStat:    q.XAdsFunctionInteractionTabDailyStat.WithContext(ctx),
		XAdsGameDailyStat:                      q.XAdsGameDailyStat.WithContext(ctx),
		XAdsKeywordDailyStat:                   q.XAdsKeywordDailyStat.WithContext(ctx),
		XAdsPageDailyStat:                      q.XAdsPageDailyStat.WithContext(ctx),
		XAdsRechargeDailyStat:                  q.XAdsRechargeDailyStat.WithContext(ctx),
		XAdsUserLabelConfig:                    q.XAdsUserLabelConfig.WithContext(ctx),
		XAdsUserProfile:                        q.XAdsUserProfile.WithContext(ctx),
		XAdsWithdrawalDailyStat:                q.XAdsWithdrawalDailyStat.WithContext(ctx),
		XAgent:                                 q.XAgent.WithContext(ctx),
		XAgentBlacklist:                        q.XAgentBlacklist.WithContext(ctx),
		XAgentChild:                            q.XAgentChild.WithContext(ctx),
		XAgentCode:                             q.XAgentCode.WithContext(ctx),
		XAgentCodeT1:                           q.XAgentCodeT1.WithContext(ctx),
		XAgentCommissionConfig:                 q.XAgentCommissionConfig.WithContext(ctx),
		XAgentCommissionLevelDefine:            q.XAgentCommissionLevelDefine.WithContext(ctx),
		XAgentCommissionScheme:                 q.XAgentCommissionScheme.WithContext(ctx),
		XAgentCommissionTeamDefine:             q.XAgentCommissionTeamDefine.WithContext(ctx),
		XAgentDailly:                           q.XAgentDailly.WithContext(ctx),
		XAgentDataDate:                         q.XAgentDataDate.WithContext(ctx),
		XAgentGameCommissionConfig:             q.XAgentGameCommissionConfig.WithContext(ctx),
		XAgentGameDailly:                       q.XAgentGameDailly.WithContext(ctx),
		XAgentGameDefine:                       q.XAgentGameDefine.WithContext(ctx),
		XAgentIndependence:                     q.XAgentIndependence.WithContext(ctx),
		XAgentIndependenceFenchengHistory:      q.XAgentIndependenceFenchengHistory.WithContext(ctx),
		XAgentIndependentCommissionDetail:      q.XAgentIndependentCommissionDetail.WithContext(ctx),
		XAgentModeLog:                          q.XAgentModeLog.WithContext(ctx),
		XAmountChangeLog:                       q.XAmountChangeLog.WithContext(ctx),
		XBlackBlock:                            q.XBlackBlock.WithContext(ctx),
		XBonusTaskTemplate:                     q.XBonusTaskTemplate.WithContext(ctx),
		XBonusTaskUser:                         q.XBonusTaskUser.WithContext(ctx),
		XCaijingDetail:                         q.XCaijingDetail.WithContext(ctx),
		XChannel:                               q.XChannel.WithContext(ctx),
		XChannelGameList:                       q.XChannelGameList.WithContext(ctx),
		XChannelHost:                           q.XChannelHost.WithContext(ctx),
		XChatHongbaoCondition:                  q.XChatHongbaoCondition.WithContext(ctx),
		XConfig:                                q.XConfig.WithContext(ctx),
		XDictChangeParentype:                   q.XDictChangeParentype.WithContext(ctx),
		XDictChangetype:                        q.XDictChangetype.WithContext(ctx),
		XDictGametype:                          q.XDictGametype.WithContext(ctx),
		XFinanceMethod:                         q.XFinanceMethod.WithContext(ctx),
		XFinanceSymbol:                         q.XFinanceSymbol.WithContext(ctx),
		XGame:                                  q.XGame.WithContext(ctx),
		XGameBrand:                             q.XGameBrand.WithContext(ctx),
		XGameChain:                             q.XGameChain.WithContext(ctx),
		XGameList:                              q.XGameList.WithContext(ctx),
		XGamePeriod:                            q.XGamePeriod.WithContext(ctx),
		XHomeCarouselV2:                        q.XHomeCarouselV2.WithContext(ctx),
		XHostTag:                               q.XHostTag.WithContext(ctx),
		XKefuBindHistory:                       q.XKefuBindHistory.WithContext(ctx),
		XLangGameList:                          q.XLangGameList.WithContext(ctx),
		XLangList:                              q.XLangList.WithContext(ctx),
		XLoginLog:                              q.XLoginLog.WithContext(ctx),
		XManAmountDetail:                       q.XManAmountDetail.WithContext(ctx),
		XNoticeV2:                              q.XNoticeV2.WithContext(ctx),
		XOperationsInputDataDate:               q.XOperationsInputDataDate.WithContext(ctx),
		XOrder:                                 q.XOrder.WithContext(ctx),
		XRecharge:                              q.XRecharge.WithContext(ctx),
		XRobotAdStartRecord:                    q.XRobotAdStartRecord.WithContext(ctx),
		XRobotConfig:                           q.XRobotConfig.WithContext(ctx),
		XRobotMessageTemplte:                   q.XRobotMessageTemplte.WithContext(ctx),
		XRobotMissionTemplte:                   q.XRobotMissionTemplte.WithContext(ctx),
		XRobotPostUserInfo:                     q.XRobotPostUserInfo.WithContext(ctx),
		XRobotPushActivityRecord:               q.XRobotPushActivityRecord.WithContext(ctx),
		XRobotPushMsgConfig:                    q.XRobotPushMsgConfig.WithContext(ctx),
		XRobotRedbagQuestion:                   q.XRobotRedbagQuestion.WithContext(ctx),
		XRobotRedbagReportUser:                 q.XRobotRedbagReportUser.WithContext(ctx),
		XRobotReportImportFlow:                 q.XRobotReportImportFlow.WithContext(ctx),
		XRobotReportImportFlowUpload:           q.XRobotReportImportFlowUpload.WithContext(ctx),
		XRobotStatisticsDay:                    q.XRobotStatisticsDay.WithContext(ctx),
		XSeller:                                q.XSeller.WithContext(ctx),
		XSellerInputDataDate:                   q.XSellerInputDataDate.WithContext(ctx),
		XTbBankerConfig:                        q.XTbBankerConfig.WithContext(ctx),
		XTbBankerUser:                          q.XTbBankerUser.WithContext(ctx),
		XTbRewardword:                          q.XTbRewardword.WithContext(ctx),
		XTbWinlostConfig:                       q.XTbWinlostConfig.WithContext(ctx),
		XTgAccount:                             q.XTgAccount.WithContext(ctx),
		XTgChat:                                q.XTgChat.WithContext(ctx),
		XTgMessage:                             q.XTgMessage.WithContext(ctx),
		XTgRedpacket:                           q.XTgRedpacket.WithContext(ctx),
		XTgRedpacketInviteCount:                q.XTgRedpacketInviteCount.WithContext(ctx),
		XTgRedpacketLog:                        q.XTgRedpacketLog.WithContext(ctx),
		XTgRedpacketUser:                       q.XTgRedpacketUser.WithContext(ctx),
		XTgRedpacketWithdrawLog:                q.XTgRedpacketWithdrawLog.WithContext(ctx),
		XTgRobotGuide:                          q.XTgRobotGuide.WithContext(ctx),
		XTgRobotRedpacket:                      q.XTgRobotRedpacket.WithContext(ctx),
		XTgUserRobot:                           q.XTgUserRobot.WithContext(ctx),
		XThirdDianzhi:                          q.XThirdDianzhi.WithContext(ctx),
		XThirdLive:                             q.XThirdLive.WithContext(ctx),
		XThirdLottery:                          q.XThirdLottery.WithContext(ctx),
		XThirdQipai:                            q.XThirdQipai.WithContext(ctx),
		XThirdQuwei:                            q.XThirdQuwei.WithContext(ctx),
		XThirdSport:                            q.XThirdSport.WithContext(ctx),
		XThirdTexa:                             q.XThirdTexa.WithContext(ctx),
		XTiyanjinActive:                        q.XTiyanjinActive.WithContext(ctx),
		XTiyanjing:                             q.XTiyanjing.WithContext(ctx),
		XTyjBroadcastConfig:                    q.XTyjBroadcastConfig.WithContext(ctx),
		XUser:                                  q.XUser.WithContext(ctx),
		XUserDailly:                            q.XUserDailly.WithContext(ctx),
		XVerify:                                q.XVerify.WithContext(ctx),
		XVipDefine:                             q.XVipDefine.WithContext(ctx),
		XVipGameDefine:                         q.XVipGameDefine.WithContext(ctx),
		XVipInfo:                               q.XVipInfo.WithContext(ctx),
		XWhiteBlock:                            q.XWhiteBlock.WithContext(ctx),
		XWithdraw:                              q.XWithdraw.WithContext(ctx),
		XWithdrawAddress:                       q.XWithdrawAddress.WithContext(ctx),
		XWithdrawGame:                          q.XWithdrawGame.WithContext(ctx),
		XWithdrawLimitConfig:                   q.XWithdrawLimitConfig.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
