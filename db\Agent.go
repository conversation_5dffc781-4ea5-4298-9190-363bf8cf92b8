package db

import (
	"context"
	"encoding/json"
	"fmt"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
)

type Agent struct {
	Id                  int     `gorm:"column:Id"`                  //
	UserId              int     `gorm:"column:UserId"`              //代理id
	SellerId            int     `gorm:"column:SellerId"`            //
	IsTopAgent          int     `gorm:"column:IsTopAgent"`          //是否是顶级代理 1是 2不是
	ChildCount          int     `gorm:"column:ChildCount"`          //团队总人数
	DictChildCount      int     `gorm:"column:DictChildCount"`      //直属总人数
	TotalLiuSuiTrx      float64 `gorm:"column:TotalLiuSuiTrx"`      //总流水trx
	TotalLiuSuiUsdt     float64 `gorm:"column:TotalLiuSuiUsdt"`     //总流水usdt
	TotalBetTrx         float64 `gorm:"column:TotalBetTrx"`         //总下注trx
	TotalBetUsdt        float64 `gorm:"column:TotalBetUsdt"`        //总下注usdt
	TotalRewardTrx      float64 `gorm:"column:TotalRewardTrx"`      //总返奖trx
	TotalRewardUsdt     float64 `gorm:"column:TotalRewardUsdt"`     //总返奖usdt
	SelfLiuSuiTrx       float64 `gorm:"column:SelfLiuSuiTrx"`       //自身流水trx
	SelfLiuSuiUsdt      float64 `gorm:"column:SelfLiuSuiUsdt"`      //自身流水usdt
	SelfBetTrx          float64 `gorm:"column:SelfBetTrx"`          //自身下注trx
	SelfBetUsdt         float64 `gorm:"column:SelfBetUsdt"`         //自身下注usdt
	SelfRewardTrx       float64 `gorm:"column:SelfRewardTrx"`       //自身返奖trx
	SelfRewardUsdt      float64 `gorm:"column:SelfRewardUsdt"`      //自身返奖usdt
	DictLiuSuiTrx       float64 `gorm:"column:DictLiuSuiTrx"`       //直属下级总流水trx
	DictLiuSuiUsdt      float64 `gorm:"column:DictLiuSuiUsdt"`      //直属下级总流水usdt
	DictBetTrx          float64 `gorm:"column:DictBetTrx"`          //直属下级总下注trx
	DictBetUsdt         float64 `gorm:"column:DictBetUsdt"`         //直属下级总下注usdt
	DictRewardTrx       float64 `gorm:"column:DictRewardTrx"`       //直属下级总返奖trx
	DictRewardUsdt      float64 `gorm:"column:DictRewardUsdt"`      //直属下级总返奖usdt
	FenCheng            string  `gorm:"column:FenCheng"`            //分成
	TotalCommissionTrx  float64 `gorm:"column:TotalCommissionTrx"`  //历史总佣金
	TotalCommissionUsdt float64 `gorm:"column:TotalCommissionUsdt"` //历史总佣金
	GetedCommissionTrx  float64 `gorm:"column:GetedCommissionTrx"`  //已领取佣金
	GetedCommissionUsdt float64 `gorm:"column:GetedCommissionUsdt"` //已领取佣金
	AuditCommissionTrx  float64 `gorm:"column:AuditCommissionTrx"`  //审核中的佣金
	AuditCommissionUsdt float64 `gorm:"column:AuditCommissionUsdt"` //审核中的佣金
	FineCommissionTrx   float64 `gorm:"column:FineCommissionTrx"`   //罚没佣金
	FineCommissionUsdt  float64 `gorm:"column:FineCommissionUsdt"`  //罚没佣金

	NewLiuSui                 float64 `gorm:"column:NewLiuSui"`
	NewLiuSuiDict             float64 `gorm:"column:NewLiuSuiDict"`
	NewLiuSuiHaXi             float64 `gorm:"column:NewLiuSuiHaXi"`
	NewLiuSuiHaXiDict         float64 `gorm:"column:NewLiuSuiHaXiDict"`
	NewLiuSuiLottery          float64 `gorm:"column:NewLiuSuiLottery"`
	NewLiuSuiLotteryDict      float64 `gorm:"column:NewLiuSuiLotteryDict"`
	NewLiuSuiQiPai            float64 `gorm:"column:NewLiuSuiQiPai"`
	NewLiuSuiQiPaiDict        float64 `gorm:"column:NewLiuSuiQiPaiDict"`
	NewLiuSuiDianZhi          float64 `gorm:"column:NewLiuSuiDianZhi"`
	NewLiuSuiDianZhiDict      float64 `gorm:"column:NewLiuSuiDianZhiDict"`
	NewLiuSuiXiaoYouXi        float64 `gorm:"column:NewLiuSuiXiaoYouXi"`
	NewLiuSuiXiaoYouXiDict    float64 `gorm:"column:NewLiuSuiXiaoYouXiDict"`
	NewLiuSuiLive             float64 `gorm:"column:NewLiuSuiLive"`
	NewLiuSuiLiveDict         float64 `gorm:"column:NewLiuSuiLiveDict"`
	NewLiuSuiSport            float64 `gorm:"column:NewLiuSuiSport"`
	NewLiuSuiSportDict        float64 `gorm:"column:NewLiuSuiSportDict"`
	NewLiuSuiTexas            float64 `gorm:"column:NewLiuSuiTexas"`
	NewLiuSuiTexasDict        float64 `gorm:"column:NewLiuSuiTexasDict"`
	NewLiuSuiLowLottery       float64 `gorm:"column:NewLiuSuiLowLottery"`
	NewLiuSuiLowLotteryDict   float64 `gorm:"column:NewLiuSuiLowLotteryDict"`
	NewLiuSuiCryptoMarket     float64 `gorm:"column:NewLiuSuiCryptoMarket"`
	NewLiuSuiCryptoMarketDict float64 `gorm:"column:NewLiuSuiCryptoMarketDict"`
	NewCommissionTrx          float64 `gorm:"column:NewCommissionTrx"`
	NewCommission             float64 `gorm:"column:NewCommission"`
	AgentType                 float64 `gorm:"column:AgentType"`

	TotalCommissionTrx_t1      float64 `gorm:"column:TotalCommissionTrx_t1"`
	FineCommissionTrx_t1       float64 `gorm:"column:FineCommissionTrx_t1"`
	AvailableCommissionTrx_t1  float64 `gorm:"column:AvailableCommissionTrx_t1"`
	BackCommissionTrx_t1       float64 `gorm:"column:BackCommissionTrx_t1"`
	GetedCommissionTrx_t1      float64 `gorm:"column:GetedCommissionTrx_t1"`
	TotalCommissionUsdt_t1     float64 `gorm:"column:TotalCommissionUsdt_t1"`
	FineCommissionUsdt_t1      float64 `gorm:"column:FineCommissionUsdt_t1"`
	AvailableCommissionUsdt_t1 float64 `gorm:"column:AvailableCommissionUsdt_t1"`
	BackCommissionUsdt_t1      float64 `gorm:"column:BackCommissionUsdt_t1"`
	GetedCommissionUsdt_t1     float64 `gorm:"column:GetedCommissionUsdt_t1"`

	AgentLevel int    `gorm:"column:AgentLevel"` //代理层级
	AgentId    int    `gorm:"column:AgentId"`
	TopAgentId int    `gorm:"column:TopAgentId"`
	ChildLevel int    `gorm:"column:ChildLevel"`
	Address    string `gorm:"column:Address"`
	ChannelId  int    `gorm:"column:ChannelId"`

	Data string `gorm:"column:Data"`

	AgentNickName string `gorm:"column:AgentNickName"`
	RegisterTime  string `gorm:"column:RegisterTime"` //注册时间
	AgentShortUrl string // 添加此字段
}

func (*Agent) TableName() string {
	return "x_agent"
}

type AgentWithShortUrl struct {
	Agent
	AgentShortUrl string `gorm:"column:AgentShortUrl"` // 添加 gorm 标签
}

func Agent_Page_Data(Page int, PageSize int, SellerId int, UserId int, IsTopAgent int, ChannelId int, Address string, AgentShortUrl string) (int, []AgentWithShortUrl) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "Id desc"
	PageKey := "Id"
	data := Agent{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	u := User{}
	joinsql := fmt.Sprintf("LEFT JOIN %s AS u ON u.UserId = %s.UserId", u.TableName(), data.TableName())
	dbtable = dbtable.Joins(joinsql)
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".UserId", "=", UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".IsTopAgent", "=", IsTopAgent, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".ChannelId", "=", ChannelId, 0)
	server.Db().AddWhere(&sql, &params, "and", data.TableName()+".Address", "=", Address, "")
	if AgentShortUrl != "" {
		server.Db().AddWhere(&sql, &params, "and", "u.AgentShortUrl", "=", AgentShortUrl, nil)
	}

	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s.%s) as Total", data.TableName(), PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []AgentWithShortUrl{}
	}
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s.%s as MinValue", data.TableName(), PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []AgentWithShortUrl{}

	// 修改分页查询
	offset := (Page - 1) * PageSize
	selectsql := "x_agent.*, u.AgentShortUrl, u.TopAgentId, u.AgentId, u.FenCheng, u.Address"
	dbtable.Select(selectsql).
		Where(sql, params...).
		Order(OrderBy).
		Offset(offset).
		Limit(PageSize).
		Find(&result)

	return dt.Total, result
}

func Agent_Page_Data_independence(Page, PageSize, SellerId, UserId, AgentId, TopAgentId, ChannelId int) (int64, []Agent) {
	if Page <= 0 {
		Page = 1
	}
	if PageSize <= 0 {
		PageSize = 10
	}
	data := Agent{}
	user := User{}
	result := []Agent{}
	joinsql := fmt.Sprintf("inner join %s on %s.UserId =%s.UserId LEFT JOIN x_agent_independence ON x_user.UserId = x_agent_independence.UserId LEFT JOIN x_agent_commission_config ON x_agent_independence.AgentUseId = x_agent_commission_config.id", user.TableName(), user.TableName(), data.TableName())
	dbtable := server.Db().Gorm().Table(data.TableName())
	dbtable = dbtable.Joins(joinsql)
	if SellerId > 0 {
		dbtable = dbtable.Where("x_agent.SellerId = ?", SellerId)
	}
	if ChannelId > 0 {
		dbtable = dbtable.Where("x_agent.ChannelId = ?", ChannelId)
	}
	dbtable = dbtable.Where("(x_user.TopAgentId IN (SELECT UserId FROM `x_agent_independence` )) OR (x_user.UserId IN (SELECT UserId FROM `x_agent_independence` ))")
	if UserId > 0 {
		dbtable = dbtable.Where("x_agent.UserId = ?", UserId)
	}
	if AgentId > 0 {
		dbtable = dbtable.Where("x_user.AgentId = ?", AgentId)
	}
	if TopAgentId > 0 {
		dbtable = dbtable.Where("x_user.TopAgentId = ?", TopAgentId)
	}
	var count int64
	dbtable.Count(&count)
	configtb := server.DaoxHashGame().XConfig
	configdb := server.DaoxHashGame().XConfig.WithContext(context.Background())
	configdata, err := configdb.Where(configtb.ID.Eq(22)).First()
	rate := "0.13"
	if err == nil && configdata != nil {
		rate = configdata.ConfigValue
	}
	ordersql := fmt.Sprintf("x_agent.TotalCommissionUsdt_t1 + x_agent.TotalCommissionTrx_t1 * %s desc", rate)
	dbtable.Select("*").Order(ordersql).Offset((Page - 1) * PageSize).Limit(PageSize).Find(&result)

	for k, v := range result {
		if v.Data != "" {
			fencheng := make(map[string]interface{})
			err := json.Unmarshal([]byte(v.Data), &fencheng)
			if err != nil {
				logs.Error(err)
			} else {
				if fencheng["rate"] != nil {
					logs.Info(v.UserId, fencheng["rate"])
					tbyte, _ := json.Marshal(fencheng["rate"])
					result[k].FenCheng = string(tbyte)
				}
			}
		}
	}

	return count, result
}

func Agent_Child_Data(UserId int) []Agent {
	sql := "SELECT x_agent_child.Child AS UserId,x_user.TopAgentId,x_user.FenCheng,x_user.AgentId,x_user.Address,x_user.IsAgent,x_agent.* FROM x_agent_child INNER JOIN x_user ON x_user.UserId =  x_agent_child.Child INNER JOIN x_agent ON x_agent.UserId =  x_agent_child.Child  WHERE x_agent_child.UserId = ? and ChildLevel = 0"
	dbresult, _ := server.Db().Conn().Query(sql, UserId)
	result := []Agent{}
	for dbresult.Next() {
		d := Agent{}
		abugo.GetDbResult(dbresult, &d)
		result = append(result, d)
	}
	return result
}

func Agent_Child_Data_All(UserId int) []Agent {
	sql := "SELECT x_agent_child.Child,x_agent_child.ChildLevel,x_user.TopAgentId,x_user.AgentId,x_user.Address,x_user.IsAgent,x_user.FenCheng,JSON_LENGTH(x_user.Agents) AS AgentLevel ,x_agent.* FROM x_agent_child INNER JOIN x_user ON x_user.UserId =  x_agent_child.Child INNER JOIN x_agent ON x_agent.UserId =  x_agent_child.Child  WHERE x_agent_child.UserId = ?  ORDER BY x_agent_child.ChildLevel ASC,x_agent_child.Id DESC"
	dbresult, _ := server.Db().Conn().Query(sql, UserId)
	result := []Agent{}
	for dbresult.Next() {
		d := Agent{}
		abugo.GetDbResult(dbresult, &d)
		result = append(result, d)
	}
	return result
}
