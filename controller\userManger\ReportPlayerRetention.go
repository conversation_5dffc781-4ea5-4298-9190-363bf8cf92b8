package userManger

import (
	"fmt"
	"log"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// 请求参数
type PlayerRetentionReq struct {
	Page       int   `json:"page" form:"page"` // 页码
	PageSize   int   `json:"pageSize"`         // 页大小
	SellerId   int   `json:"sellerId"`         // 运营商
	ChannelId  []int `json:"channelId"`        // 渠道ID
	TopAgentId []int `json:"topAgentId"`       // 顶级代理

	StartTime int64 `json:"startTime"` // 开始日期
	EndTime   int64 `json:"endTime"`   // 结束日期
	KeepType  int   `json:"keepType"`  // 留存分类 1登录留存 2投注留存 3有效投注留存 4首充登录留存 5首充复充留存 6新增留存
	Export    int   `json:"export"`    // 是否导出
}

// 返回结果
type PlayerRetentionResp struct {
	SellerId   int        `gorm:"column:SellerId" json:"sellerId"`     // 运营商
	ChannelId  int        `gorm:"column:ChannelId" json:"channelId"`   // 渠道ID
	TopAgentId int        `gorm:"column:TopAgentId" json:"topAgentId"` // 顶级代理
	KeepType   int        `gorm:"column:KeepType" json:"keepType"`     // 留存分类 1登录留存 2投注留存 3有效投注留存 4首充登录留存 5首充复充留存 6新增留存
	RecordDate *time.Time `gorm:"column:RecordDate" json:"recordDate"` // 日期

	DayUsers  int `gorm:"column:DayUsers" json:"dayUsers"`   // 当日人数
	Day2Users int `gorm:"column:Day2Users" json:"day2Users"` // 次日人数
	Day3Users int `gorm:"column:Day3Users" json:"day3Users"` // 3日人数
	Day4Users int `gorm:"column:Day4Users" json:"day4Users"` // 3日人数
	Day5Users int `gorm:"column:Day5Users" json:"day5Users"` // 3日人数

	Day6Users  int `gorm:"column:Day6Users" json:"day6Users"`   // 3日人数
	Day7Users  int `gorm:"column:Day7Users" json:"day7Users"`   // 3日人数
	Day8Users  int `gorm:"column:Day8Users" json:"day8Users"`   // 3日人数
	Day9Users  int `gorm:"column:Day9Users" json:"day9Users"`   // 3日人数
	Day10Users int `gorm:"column:Day10Users" json:"day10Users"` // 3日人数

	Day11Users int `gorm:"column:Day11Users" json:"day11Users"` // 3日人数
	Day12Users int `gorm:"column:Day12Users" json:"day12Users"` // 3日人数
	Day13Users int `gorm:"column:Day13Users" json:"day13Users"` // 3日人数
	Day14Users int `gorm:"column:Day14Users" json:"day14Users"` // 3日人数
	Day15Users int `gorm:"column:Day15Users" json:"day15Users"` // 3日人数

	Day16Users int `gorm:"column:Day16Users" json:"day16Users"` // 3日人数
	Day17Users int `gorm:"column:Day17Users" json:"day17Users"` // 3日人数
	Day18Users int `gorm:"column:Day18Users" json:"day18Users"` // 3日人数
	Day19Users int `gorm:"column:Day19Users" json:"day19Users"` // 3日人数
	Day20Users int `gorm:"column:Day20Users" json:"day20Users"` // 3日人数

	Day21Users int `gorm:"column:Day21Users" json:"day21Users"` // 3日人数
	Day22Users int `gorm:"column:Day22Users" json:"day22Users"` // 3日人数
	Day23Users int `gorm:"column:Day23Users" json:"day23Users"` // 3日人数
	Day24Users int `gorm:"column:Day24Users" json:"day24Users"` // 3日人数
	Day25Users int `gorm:"column:Day25Users" json:"day25Users"` // 3日人数

	Day26Users int `gorm:"column:Day26Users" json:"day26Users"` // 3日人数
	Day27Users int `gorm:"column:Day27Users" json:"day27Users"` // 3日人数
	Day28Users int `gorm:"column:Day28sers" json:"day28Users"`  // 3日人数
	Day29Users int `gorm:"column:Day29Users" json:"day29Users"` // 3日人数
	Day30Users int `gorm:"column:Day30Users" json:"day30Users"` // 3日人数
}

type PlayerRetentionController struct {
}

func (this *PlayerRetentionController) Init() {
	group := server.Http().NewGroup("/api/player_retention")
	{
		group.Post("/list", this.list)
	}

}

// list 用户列表查询接口
func (this *PlayerRetentionController) list(ctx *abugo.AbuHttpContent) {
	//defer recover()

	var total int64
	errcode := 0
	reqdata := PlayerRetentionReq{}
	var list []*PlayerRetentionResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家留存", "登录留存", "查", "查看玩家留存")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	//if token.ChannelId > 0 {
	//	reqdata.ChannelId = token.ChannelId
	//}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	if 1 == reqdata.Export {
		// 处理时间和导出参数
		if reqdata.EndTime > 0 {
			reqdata.EndTime += 86400000
		}
		reqdata.Page = 1
		reqdata.PageSize = 10000
	}

	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	tdb := server.Db().GormDao().Table("x_agent_keep_data_date")
	tdb.Select("KeepType",
		//"SellerId",
		//"ChannelId",
		//"TopAgentId",
		"RecordDate",
		"SUM(DayUsers) AS DayUsers",
		"SUM(Day2Users) AS Day2Users",
		"SUM(Day3Users) AS Day3Users",
		"SUM(Day4Users) AS Day4Users",
		"SUM(Day5Users) AS Day5Users",
		"SUM(Day6Users) AS Day6Users",
		"SUM(Day7Users) AS Day7Users",
		"SUM(Day8Users) AS Day8Users",
		"SUM(Day9Users) AS Day9Users",
		"SUM(Day10Users) AS Day10Users",
		"SUM(Day11Users) AS Day11Users",
		"SUM(Day12Users) AS Day12Users",
		"SUM(Day13Users) AS Day13Users",
		"SUM(Day14Users) AS Day14Users",
		"SUM(Day15Users) AS Day15Users",
		"SUM(Day16Users) AS Day16Users",
		"SUM(Day17Users) AS Day17Users",
		"SUM(Day18Users) AS Day18Users",
		"SUM(Day19Users) AS Day19Users",
		"SUM(Day20Users) AS Day20Users",
		"SUM(Day21Users) AS Day21Users",
		"SUM(Day22Users) AS Day22Users",
		"SUM(Day23Users) AS Day23Users",
		"SUM(Day24Users) AS Day24Users",
		"SUM(Day25Users) AS Day25Users",
		"SUM(Day26Users) AS Day26Users",
		"SUM(Day27Users) AS Day27Users",
		"SUM(Day28Users) AS Day28Users",
		"SUM(Day29Users) AS Day29Users",
		"SUM(Day30Users) AS Day30Users",
	).Group("RecordDate").Order("RecordDate DESC")
	iRet := this.GetWhere(&reqdata, tdb)
	// 在能确认是空数据的情况
	if iRet > 0 {
		ctx.Put("data", make([]*PlayerRetentionResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	if 1 == reqdata.Export {
		filename, err := this.ExportExcel(list)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	return
}

// 获取where 条件
func (this *PlayerRetentionController) GetWhere(req *PlayerRetentionReq, db *gorm.DB) int {
	iRet := 0
	// 商户id
	if req.SellerId > 0 {
		db.Where("SellerId = ?", req.SellerId)
	}
	// 渠道id
	if len(req.ChannelId) > 0 {
		db.Where("ChannelId in ?", req.ChannelId)
	}
	// 报表类型
	if req.KeepType > 0 {
		db.Where("KeepType = ?", req.KeepType)
	}

	// 顶级代理id
	if len(req.TopAgentId) > 0 {
		db.Where("TopAgentId in ?", req.TopAgentId)
	}
	// 时间区间
	if req.StartTime > 0 {
		db.Where("RecordDate >= ?", abugo.TimeStampToLocalTime(req.StartTime))
	}
	// 时间区间
	if req.EndTime > 0 && req.EndTime > req.StartTime {
		db.Where("RecordDate <= ?", abugo.TimeStampToLocalTime(req.EndTime))
	}
	return iRet
}

// 导出Excel文件
func (this *PlayerRetentionController) ExportExcel(list []*PlayerRetentionResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"运营商", "渠道", "顶级代理", "留存类型", "日期",
		"人数", "次日", "3日", "4日",
		"5日", "6日", "7日", "8日", "9日",
		"10日", "11日", "12日", "13日", "14日",
		"15日", "16日", "17日", "18日", "19日",
		"20日", "21日", "22日", "23日", "24日",
		"25日", "26日", "27日", "28日", "29日",
		"30日"}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				row := []interface{}{
					d.SellerId, d.ChannelId, d.TopAgentId, d.KeepType, d.RecordDate,
					d.DayUsers, d.Day2Users, d.Day3Users, d.Day4Users,
					d.Day5Users, d.Day6Users, d.Day7Users, d.Day8Users, d.Day9Users,
					d.Day10Users, d.Day11Users, d.Day12Users, d.Day13Users, d.Day14Users,
					d.Day15Users, d.Day16Users, d.Day17Users, d.Day18Users, d.Day19Users,
					d.Day20Users, d.Day21Users, d.Day22Users, d.Day23Users, d.Day24Users,
					d.Day25Users, d.Day26Users, d.Day27Users, d.Day28Users, d.Day29Users,
					d.Day30Users,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "export_user_" + time.Now().Format("20060102150405") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}
