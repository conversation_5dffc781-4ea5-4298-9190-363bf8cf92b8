// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentCommissionScheme = "x_agent_commission_scheme"

// XAgentCommissionScheme 三级返佣方案
type XAgentCommissionScheme struct {
	SchemeID            int32     `gorm:"column:SchemeId;primaryKey;autoIncrement:true;comment:方案Id" json:"SchemeId"`            // 方案Id
	SchemeName          string    `gorm:"column:SchemeName;not null;comment:方案名" json:"SchemeName"`                              // 方案名
	GetType             int32     `gorm:"column:GetType;default:1;comment:发放方式 1人工发放 2自动发放" json:"GetType"`                      // 发放方式 1人工发放 2自动发放
	GetAmountType       int32     `gorm:"column:GetAmountType;default:1;comment:发放钱包 1真金钱包 2bonus钱包" json:"GetAmountType"`       // 发放钱包 1真金钱包 2bonus钱包
	RollingTimes        float64   `gorm:"column:RollingTimes;default:0.000000;comment:打码倍数" json:"RollingTimes"`                 // 打码倍数
	IsSumTeam           int32     `gorm:"column:IsSumTeam;default:1;comment:自身流水是否累计团队:1累计 2不累计" json:"IsSumTeam"`               // 自身流水是否累计团队:1累计 2不累计
	ValidRechargeAmount float64   `gorm:"column:ValidRechargeAmount;default:0.000000;comment:有效充值金额" json:"ValidRechargeAmount"` // 有效充值金额
	ValidLiuShui        float64   `gorm:"column:ValidLiuShui;default:0.000000;comment:有效流水" json:"ValidLiuShui"`                 // 有效流水
	Status              int32     `gorm:"column:Status;default:1;comment:方案状态 1启用 2禁用" json:"Status"`                            // 方案状态 1启用 2禁用
	CreateTime          time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`   // 创建时间
	UpdateTime          time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`   // 更新时间
}

// TableName XAgentCommissionScheme's table name
func (*XAgentCommissionScheme) TableName() string {
	return TableNameXAgentCommissionScheme
}
