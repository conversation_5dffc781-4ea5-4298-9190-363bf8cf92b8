// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/examples/dal/model"
	"gorm.io/gen/field"
)

func newMytable(db *gorm.DB) mytable {
	_mytable := mytable{}

	_mytable.mytableDo.UseDB(db)
	_mytable.mytableDo.UseModel(&model.Mytable{})

	tableName := _mytable.mytableDo.TableName()
	_mytable.ALL = field.NewField(tableName, "*")
	_mytable.ID = field.NewInt32(tableName, "ID")
	_mytable.Username = field.NewString(tableName, "username")
	_mytable.Age = field.NewInt32(tableName, "age")
	_mytable.Phone = field.NewString(tableName, "phone")

	_mytable.fillFieldMap()

	return _mytable
}

type mytable struct {
	mytableDo mytableDo

	ALL      field.Field
	ID       field.Int32
	Username field.String
	Age      field.Int32
	Phone    field.String

	fieldMap map[string]field.Expr
}

func (m mytable) As(alias string) *mytable {
	m.mytableDo.DO = *(m.mytableDo.As(alias).(*gen.DO))

	m.ALL = field.NewField(alias, "*")
	m.ID = field.NewInt32(alias, "ID")
	m.Username = field.NewString(alias, "username")
	m.Age = field.NewInt32(alias, "age")
	m.Phone = field.NewString(alias, "phone")

	m.fillFieldMap()

	return &m
}

func (m *mytable) WithContext(ctx context.Context) *mytableDo { return m.mytableDo.WithContext(ctx) }

func (m mytable) TableName() string { return m.mytableDo.TableName() }

func (m *mytable) GetFieldByName(fieldName string) (field.Expr, bool) {
	field, ok := m.fieldMap[fieldName]
	return field, ok
}

func (m *mytable) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 4)
	m.fieldMap["ID"] = m.ID
	m.fieldMap["username"] = m.Username
	m.fieldMap["age"] = m.Age
	m.fieldMap["phone"] = m.Phone
}

func (m mytable) clone(db *gorm.DB) mytable {
	m.mytableDo.ReplaceDB(db)
	return m
}

type mytableDo struct{ gen.DO }

func (m mytableDo) Debug() *mytableDo {
	return m.withDO(m.DO.Debug())
}

func (m mytableDo) WithContext(ctx context.Context) *mytableDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mytableDo) Clauses(conds ...clause.Expression) *mytableDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mytableDo) Not(conds ...gen.Condition) *mytableDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mytableDo) Or(conds ...gen.Condition) *mytableDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mytableDo) Select(conds ...field.Expr) *mytableDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mytableDo) Where(conds ...gen.Condition) *mytableDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mytableDo) Order(conds ...field.Expr) *mytableDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mytableDo) Distinct(cols ...field.Expr) *mytableDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mytableDo) Omit(cols ...field.Expr) *mytableDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mytableDo) Join(table schema.Tabler, on ...field.Expr) *mytableDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mytableDo) LeftJoin(table schema.Tabler, on ...field.Expr) *mytableDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mytableDo) RightJoin(table schema.Tabler, on ...field.Expr) *mytableDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mytableDo) Group(cols ...field.Expr) *mytableDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mytableDo) Having(conds ...gen.Condition) *mytableDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mytableDo) Limit(limit int) *mytableDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mytableDo) Offset(offset int) *mytableDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mytableDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *mytableDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mytableDo) Unscoped() *mytableDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mytableDo) Create(values ...*model.Mytable) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mytableDo) CreateInBatches(values []*model.Mytable, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mytableDo) Save(values ...*model.Mytable) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mytableDo) First() (*model.Mytable, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Mytable), nil
	}
}

func (m mytableDo) Take() (*model.Mytable, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Mytable), nil
	}
}

func (m mytableDo) Last() (*model.Mytable, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Mytable), nil
	}
}

func (m mytableDo) Find() ([]*model.Mytable, error) {
	result, err := m.DO.Find()
	return result.([]*model.Mytable), err
}

func (m mytableDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Mytable, err error) {
	buf := make([]*model.Mytable, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mytableDo) FindInBatches(result *[]*model.Mytable, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mytableDo) Attrs(attrs ...field.AssignExpr) *mytableDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mytableDo) Assign(attrs ...field.AssignExpr) *mytableDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mytableDo) Joins(field field.RelationField) *mytableDo {
	return m.withDO(m.DO.Joins(field))
}

func (m mytableDo) Preload(field field.RelationField) *mytableDo {
	return m.withDO(m.DO.Preload(field))
}

func (m mytableDo) FirstOrInit() (*model.Mytable, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Mytable), nil
	}
}

func (m mytableDo) FirstOrCreate() (*model.Mytable, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Mytable), nil
	}
}

func (m mytableDo) FindByPage(offset int, limit int) (result []*model.Mytable, count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	result, err = m.Offset(offset).Limit(limit).Find()
	return
}

func (m mytableDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m *mytableDo) withDO(do gen.Dao) *mytableDo {
	m.DO = *do.(*gen.DO)
	return m
}
