package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
	"gorm.io/gorm"
)

func (c *AgentController) get_config_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int `json:"Page"`
		PageSize int `json:"PageSize"`
		Name     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金方案", "查"), &errcode, "权限不足") {
		return
	}

	type XAgentGameCommissionConfig struct {

		//XgID          int32     `json:"XgID"`
		//Brand         string    `json:"Brand"`        // 品牌
		GameId     string  `json:"GameId"`     // 游戏Id
		CatId      int32   `son:"CatId"`       // 彩票大类ID
		RewardRate float64 `json:"RewardRate"` // 返佣比例
		//XgMemo       string    `json:"xgMemo"`       // 备注
		//XgCreateTime time.Time `json:"XgCreateTime"` // 创建时间
		//UpdateTime   time.Time `json:"UpdateTime"`   // 更新时间
	}

	type XGameArr []XAgentGameCommissionConfig

	// 联合查询98代理佣金配置（包括香港六合彩）
	type ResponseResult struct {
		Id            int32  `json:"Id"`      // id
		Name          string `json:"Name"`    // 方案名称
		ModelID       int32  `json:"ModelId"` // 模式Id
		Data          string `json:"Data"`    // 方案数据
		State         int32  `json:"State"`   // 1启用,2禁用
		CreateTime    string `json:"CreateTime"`
		CreateAccount string `json:"CreateAccount"`
		Memo          string `json:"Memo"` // 备注
		UseCount      int32
		XGameArr      XGameArr
	}
	type ResponseDataArr []ResponseResult
	responseDataArr := ResponseDataArr{}

	where := abugo.AbuDbWhere{}
	where.Add("and", "Name", "=", reqdata.Name, "")
	total, data := server.Db().Table("x_agent_commission_config").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	dataPtr := *data
	for _, v := range dataPtr {
		dataId := v["Id"]
		logs.Info("获取到x_agent_commission_config的Id：", dataId)
		tempResponseData := ResponseResult{}
		tempResponseData.Id = int32(ChangeInt(v, "Id"))
		tempResponseData.Name = v["Name"].(string)
		tempResponseData.ModelID = int32(ChangeInt(v, "ModelId"))
		tempResponseData.Data = v["Data"].(string)
		tempResponseData.State = int32(ChangeInt(v, "State"))
		tempResponseData.UseCount = int32(ChangeInt(v, "UseCount"))
		tempResponseData.CreateAccount = v["CreateAccount"].(string)
		tempResponseData.Memo = v["Memo"].(string)
		tempResponseData.CreateTime = v["CreateTime"].(string)

		// 根据id获取香港六合彩返佣配置数据
		xgWhere := abugo.AbuDbWhere{}
		xgWhere.Add("and", "Id", "=", dataId, "")
		rows, err := server.Db().Table("x_agent_game_commission_config").Where(xgWhere).OrderBy("CatId asc").GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}
		for j := 0; j < len(*rows); j++ {
			gameConfig := (*rows)[j]
			var tempGameConfig XAgentGameCommissionConfig
			tempGameConfig.CatId = int32(ChangeInt(gameConfig, "CatId"))
			tempGameConfig.GameId = gameConfig["GameId"].(string)
			tempGameConfig.RewardRate = gameConfig["RewardRate"].(float64)
			tempResponseData.XGameArr = append(tempResponseData.XGameArr, tempGameConfig)
		}
		responseDataArr = append(responseDataArr, tempResponseData)
	}

	ctx.Put("data", responseDataArr)
	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("查询佣金方案", ctx, reqdata)
}

func (c *AgentController) add_config_t1(ctx *abugo.AbuHttpContent) {
	// x_agent_game_commission_config
	type XAgentGameCommissionConfig struct {
		//Brand      string  `json:"Brand"`
		//GameId     string  `json:"GameId"`
		CatId      int32   `json:"CatId"`
		RewardRate float64 `json:"RewardRate"`
	}
	type XGameCommArr []XAgentGameCommissionConfig
	type RequestData struct {
		Name         string       `json:"Name"`
		ModelId      int          `json:"ModelId"`
		Data         string       `json:"Data"`
		State        int          `json:"State"`
		Memo         string       `json:"Memo"`
		GoogleCode   string       `json:"GoogleCode"`
		XGameCommArr XGameCommArr `json:"XGameCommArr"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金方案", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	traErr := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		id, err := server.Db().Table("x_agent_commission_config").Insert(map[string]interface{}{
			"Name":          reqdata.Name,
			"ModelId":       reqdata.ModelId,
			"Data":          reqdata.Data,
			"Memo":          reqdata.Memo,
			"State":         reqdata.State,
			"CreateAccount": token.Account,
		})
		logs.Info("新增98返佣生成的id：", id)
		agentGameData := make([]*model.XAgentGameCommissionConfig, 0, len(reqdata.XGameCommArr))
		for _, gameComm := range reqdata.XGameCommArr {
			gameCommInsert := &model.XAgentGameCommissionConfig{
				ID:         int32(id),                // 香港六合彩的98返佣id使用98返佣的id，值保持一致
				Brand:      "gfg",                    // 品牌
				GameID:     "1",                      // 游戏Id
				CatID:      gameComm.CatId,           // 彩票大类ID
				RewardRate: gameComm.RewardRate,      // 返佣比例
				Memo:       "",                       // 备注
				CreateTime: time.Now().In(agentUTC8), // 创建时间
				UpdateTime: time.Now().In(agentUTC8),
			}
			agentGameData = append(agentGameData, gameCommInsert)
		}
		agentGameDb := tx.XAgentGameCommissionConfig.WithContext(context.Background())
		gErr := agentGameDb.CreateInBatches(agentGameData, len(agentGameData))
		if gErr != nil {
			logs.Error("新增香港彩98返佣配置错误， err:", err)
			ctx.RespErr(gErr, &errcode)
			return gErr
		}
		logs.Info("新增香港彩98返佣配置成功， 新增记录：", len(agentGameData))

		return nil
	})
	if traErr != nil {
		logs.Error("新增98返佣配置错误 err:", err)
		ctx.RespErr(traErr, &errcode)
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("新增佣金方案成功", ctx, reqdata)
}

func (c *AgentController) modify_config_t1(ctx *abugo.AbuHttpContent) {
	type XAgentGameCommissionConfig struct {
		CatId      int32   `json:"CatId"`
		RewardRate float64 `json:"RewardRate"`
	}
	type XGameCommArr []XAgentGameCommissionConfig
	type RequestData struct {
		Id           int          `json:"Id"`
		Name         string       `json:"Name"`
		ModelId      int          `json:"ModelId"`
		Data         string       `json:"Data"`
		Memo         string       `json:"Memo"`
		State        int          `json:"State"`
		GoogleCode   string       `json:"GoogleCode"`
		XGameCommArr XGameCommArr `json:"XGameCommArr"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金方案", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	cfgdata, _ := server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.Id).First()
	if cfgdata.Int("UseCount") > 0 {
		ctx.RespErrString(true, &errcode, "该方案已被使用，无法修改")
		return
	}
	server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 修改原98佣金方案
		_, aErr := server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.Id).Update(map[string]interface{}{
			"Name":          reqdata.Name,
			"ModelId":       reqdata.ModelId,
			"Data":          reqdata.Data,
			"Memo":          reqdata.Memo,
			"State":         reqdata.State,
			"CreateAccount": token.Account,
		})
		if aErr != nil {
			logs.Error("更新98返佣配置出现错误: aErr:", aErr)
			return aErr
		}
		// 修改香港六合彩98佣金方案的每个玩法大类的返佣值(香港id和原佣金方案表id一致)
		for _, gameComm := range reqdata.XGameCommArr {
			_, xGErr := server.XDb().Table("x_agent_game_commission_config").Where("Id = ? and CatId = ?", reqdata.Id, gameComm.CatId).Update(map[string]interface{}{
				"RewardRate": gameComm.RewardRate,
				"UpdateTime": time.Now().In(agentUTC8),
			})
			if xGErr != nil {
				logs.Error("更新香港六合彩98返佣配置出现错误: xGErr:", xGErr)
				return xGErr
			}
		}
		return nil
	})

	ctx.RespOK()
	server.WriteAdminLog("修改佣金方案", ctx, reqdata)
}

func (c *AgentController) delete_config_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id         int    `json:"Id"`
		GoogleCode string `json:"GoogleCode"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金方案", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	cfgdata, _ := server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.Id).First()
	if cfgdata.Int("UseCount") > 0 {
		ctx.RespErrString(true, &errcode, "该方案已被使用，无法删除")
		return
	}
	trErr := server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		server.XDb().Table("x_agent_commission_config").Where("Id = ?", reqdata.Id).Delete()
		server.XDb().Table("x_agent_game_commission_config").Where("Id = ?", reqdata.Id).Delete()
		return nil
	})
	if trErr != nil {
		logs.Error("删除佣金方案、香港彩佣金方案失败")
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("删除佣金方案", ctx, reqdata)
}

func (c *AgentController) get_blacklist_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		Page     int    `json:"Page"`
		PageSize int    `json:"PerPage"`
		UserId   int    `json:"UserId"`
		Address  string `json:"Address"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金黑名单", "查"), &errcode, "权限不足") {
		return
	}
	users := ""
	if reqdata.Address != "" {
		users += "("
		xwhere := abugo.AbuDbWhere{}
		xwhere.Add("and", "Address", "=", reqdata.Address, 0)
		userwallet, _ := server.Db().Table("x_user_wallet").Where(xwhere).GetList()
		for i := 0; i < len(*userwallet); i++ {
			users += fmt.Sprint(int(abugo.GetInt64FromInterface((*userwallet)[i]["UserId"])))
			if i != len(*userwallet)-1 {
				users += ","
			}
		}
		users += ")"
	}
	where := abugo.AbuDbWhere{}
	if reqdata.UserId > 0 {
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
	} else if users != "" {
		where.Add("and", "UserId", "in", users, 0)
	}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	total, data := server.Db().Table("x_agent_blacklist").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *AgentController) add_blacklist_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId     []int  `json:"UserId"`
		GoogleCode string `json:"GoogleCode"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金黑名单", "增"), &errcode, "权限不足") {
		return
	}

	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	for i := 0; i < len(reqdata.UserId); i++ {
		userdata, _ := server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId[0]).First()
		if ctx.RespErrString(userdata == nil, &errcode, "用户不存在") {
			return
		}
		address, _ := server.XDb().Table("x_user_wallet").Where("UserId = ?", reqdata.UserId[0]).Find()
		arr := []string{}
		address.ForEach(func(item *xgo.XMap) bool {
			arr = append(arr, item.String("Address"))
			return true
		})
		bytes, _ := json.Marshal(arr)
		server.XDb().Table("x_agent_blacklist").Insert(map[string]interface{}{
			"UserId":        reqdata.UserId[0],
			"SellerId":      userdata.Int("SellerId"),
			"ChannelId":     userdata.Int("ChannelId"),
			"TopAgentId":    userdata.Int("TopAgentId"),
			"AgentId":       userdata.Int("AgentId"),
			"Address":       string(bytes),
			"CreateAccount": token.Account,
			"CreateTime":    xgo.GetLocalTime(),
		})
	}
	ctx.RespOK()
	server.WriteAdminLog("添加佣金黑名单", ctx, reqdata)
}

func (c *AgentController) delete_blacklist_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId     []int  `json:"UserId"`
		GoogleCode string `json:"GoogleCode"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金黑名单", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	for i := 0; i < len(reqdata.UserId); i++ {
		server.XDb().Table("x_agent_blacklist").Where("UserId = ?", reqdata.UserId[i]).Delete()
	}
	ctx.RespOK()
	server.WriteAdminLog("删除佣金黑名单", ctx, reqdata)
}

func (c *AgentController) commission_list_t1(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		Id        int
		UserId    int
		Symbol    string
		State     int
		StartTime int64
		EndTime   int64
		ChannelId int
		Address   string
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金审核", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_佣金审核_%s", time.Now().Format("20060102150405")))
	//var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("Id", "订单号")
		xlsx.SetTitle("UserId", "代理ID")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("Address", "钱包地址")
		xlsx.SetTitle("AmountUsdt", "佣金金额usdt")
		xlsx.SetTitle("AmountTrx", "佣金金额trx")
		xlsx.SetTitle("FinalAmount", "到账金额")
		xlsx.SetTitle("CreateTime", "申请时间")
		xlsx.SetTitle("Memo", "审核备注")
		xlsx.SetTitle("AuditAccount", "审核员")
		xlsx.SetTitle("AuditTime", "审核时间")
		xlsx.SetTitle("SendAccount", "发放员")
		xlsx.SetTitle("SendTime", "发放时间")
		xlsx.SetTitle("State", "状态")
		xlsx.SetTitleStyle()
	}

	total, data, finalamount, amounttrx, ammounusdt := db.CommissionAudit_Page_DataEx(reqdata.Page, reqdata.PageSize, reqdata.Id, reqdata.SellerId, reqdata.UserId, reqdata.State, reqdata.StartTime, reqdata.EndTime, reqdata.ChannelId, reqdata.Address)
	if reqdata.Export != 1 {
		ctx.Put("data", data)
		ctx.Put("total", total)
		ctx.Put("finalamount", finalamount)
		ctx.Put("amounttrx", amounttrx)
		ctx.Put("ammounusdt", ammounusdt)
	} else {
		for i := 0; i < len(data); i++ {
			xlsx.SetValue("Id", data[i].Id, int64(i+2))
			xlsx.SetValue("UserId", data[i].UserId, int64(i+2))
			xlsx.SetValue("ChannelId", ChannelName(data[i].ChannelId), int64(i+2))
			xlsx.SetValue("Address", data[i].Address, int64(i+2))
			xlsx.SetValue("AmountUsdt", data[i].AmountUsdt, int64(i+2))
			xlsx.SetValue("AmountTrx", data[i].AmountTrx, int64(i+2))
			xlsx.SetValue("CreateTime", data[i].CreateTime, int64(i+2))
			xlsx.SetValue("Memo", data[i].Memo, int64(i+2))
			xlsx.SetValue("AuditAccount", data[i].AuditAccount, int64(i+2))
			xlsx.SetValue("AuditTime", data[i].AuditTime, int64(i+2))
			xlsx.SetValue("SendAccount", data[i].SendAccount, int64(i+2))
			xlsx.SetValue("SendTime", data[i].SendTime, int64(i+2))
			xlsx.SetValue("FinalAmount", data[i].FinalAmount, int64(i+2))

			if data[i].State == 1 {
				xlsx.SetValue("State", "待审核", int64(i+2))
			} else if data[i].State == 2 {
				xlsx.SetValue("State", "审核拒绝", int64(i+2))
			} else if data[i].State == 3 {
				xlsx.SetValue("State", "审核通过", int64(i+2))
			} else if data[i].State == 4 {
				xlsx.SetValue("State", "已发放", int64(i+2))
			} else {
				xlsx.SetValue("State", fmt.Sprint(data[i].State), int64(i+2))
			}
		}
		//xlsx.SetValue("Id", "合计", int64(total+2))
		// xlsx.SetValue("Amount", totalamount, int64(total+2))
		xlsx.SetValueStyle(int64(total + 2))
	}

	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
}

func (c *AgentController) commission_result_t1(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Id         int    `validate:"required"`
		State      int    `validate:"required"`
		Memo       string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金审核", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var dberrcode int
	var dberrmsg string
	server.Db().QueryScan("call x_admin_commission_audit_t1(?,?,?,?)", []interface{}{reqdata.Id, reqdata.State, token.Account, reqdata.Memo}, &dberrcode, &dberrmsg)
	if ctx.RespErrString(dberrcode > 0, &errcode, dberrmsg) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("佣金审核", ctx, reqdata)
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}

func (c *AgentController) commission_result_batch_t1(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Ids        []int  `validate:"required"`
		State      int    `validate:"required"`
		Memo       string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "佣金审核", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	for _, id := range reqdata.Ids {
		var dberrcode int
		var dberrmsg string
		server.Db().QueryScan("call x_admin_commission_audit_t1(?,?,?,?)", []interface{}{id, reqdata.State, token.Account, reqdata.Memo}, &dberrcode, &dberrmsg)
	}
	ctx.RespOK()
	server.WriteAdminLog("佣金审核", ctx, reqdata)
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}

func (c *AgentController) checkAgentUserId(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	userTb := server.DaoxHashGame().XUser
	userDb := userTb.WithContext(ctx.Gin())
	agentIndependenceTb := server.DaoxHashGame().XAgentIndependence
	type ResultData struct {
		Id        int32  `gorm:"column:Id"`
		UserId    int32  `gorm:"column:UserId"`
		RegURL    string `gorm:"column:RegUrl"`
		SellerId  int32  `gorm:"column:SellerId"`
		ChannelId int32  `gorm:"column:ChannelId"`
	}
	var res ResultData
	err = userDb.Select(userTb.UserID, agentIndependenceTb.ID, userTb.RegURL,
		userTb.SellerID, userTb.ChannelID).
		Where(userTb.UserID.Eq(int32(reqdata.UserId))).
		LeftJoin(agentIndependenceTb, userTb.UserID.EqCol(agentIndependenceTb.UserID)).
		Scan(&res)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if res.UserId == 0 {
		ctx.RespErrString(true, &errcode, "会员ID不存在")
		return
	}
	if res.Id > 0 {
		ctx.RespErrString(true, &errcode, "此会员ID已经是独立代理")
		return
	}
	agentCodeTb := server.DaoxHashGame().XAgentCode
	agentCodeDb := agentCodeTb.WithContext(ctx.Gin())
	agentCode, err := agentCodeDb.Where(agentCodeTb.UserID.Eq(res.UserId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if agentCode == nil {
		ctx.RespErrString(true, &errcode, "该玩家尚未生成邀请码,请先生成邀请码")
		return
	}
	data := gin.H{
		"PromotionHost": fmt.Sprintf(utils.TopAgentPromotionHost, res.RegURL, agentCode.AgentCode),
		"SellerId":      res.SellerId,
		"ChannelId":     res.ChannelId,
		"UserId":        res.UserId,
	}
	ctx.RespOK(data)
}

func (c *AgentController) checkAgentHost(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		Host     string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	agentIndependenceTb := server.DaoxHashGame().XAgentIndependence
	agentIndependenceDb := server.DaoxHashGame().XAgentIndependence.WithContext(ctx.Gin())
	agentIndependenceHost, err := agentIndependenceDb.Where(agentIndependenceTb.Host.Eq(reqdata.Host)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if agentIndependenceHost != nil {
		ctx.RespErrString(true, &errcode, "此域名已经绑定过独立代理")
		return
	}
	ctx.RespOK()
}

func (c *AgentController) addAgentUser(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		Host     string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	type Result struct {
		SellerId  int32
		ChannelId int32
	}
	var result Result
	channelHostTb := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostTb.WithContext(ctx.Gin())
	xChannel := server.DaoxHashGame().XChannel
	err = channelHostDb.Select(xChannel.SellerID, xChannel.ChannelID).
		Where(channelHostTb.Host.Eq(reqdata.Host)).
		Where(channelHostTb.State.Eq(1)).
		Join(xChannel, xChannel.ChannelID.EqCol(channelHostTb.ChannelID)).
		Scan(&result)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if result.SellerId <= 0 || result.ChannelId <= 0 {
		ctx.RespErrString(true, &errcode, "域名不存在或者已关闭")
		return
	}
	account := xgo.RandomString(16)
	password, err := xgo.GenerateRandomPassword(9)
	if err != nil {
		ctx.RespErrString(true, &errcode, "生成密码错误，请稍后重试")
		return
	}
	hashPwd := xgo.Md5(password)
	type RegisterReq struct {
		SellerId     int32  `validate:"required"` //运营商
		Account      string `validate:"required"` //账号
		Password     string // 密码
		AccountType  int    // 1:账号, 2:手机号, 3:Email, 4:Google, 5:TG机器人自动注册
		AgentCode    string
		VerifyCode   string //validate:"required" //验证码
		Validate     string `validate:"required"`
		TgName       string
		Wallet       string // 钱包地址
		TgRobotToken string
		Host         string
		TgChatId     int64
		Lang         string
	}
	registerReq := RegisterReq{Account: account, Password: hashPwd, AccountType: 6, AgentCode: "", SellerId: result.SellerId,
		Validate: "123", TgName: "", TgRobotToken: "", Host: reqdata.Host, TgChatId: 0, Lang: "",
	}
	req.Debug = true
	resp, err := req.Post(server.ClientApi()+"/user/register", req.BodyJSON(registerReq))
	if err != nil {
		logs.Error("addAgentUser call clientapi err:", err)
		ctx.RespErrString(true, &errcode, "注册失败,请稍后再试")
		return
	}
	logs.Debug("addAgentUser /api/user/register resp:", resp)
	res := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Errcode   int    `json:"errcode,omitempty"`
			Errmsg    string `json:"errmsg,omitempty"`
			UserId    int32  `json:"UserId"`
			AgentCode string `json:"AgentCode"`
			Account   string `json:"Account"`
		} `json:"data,omitempty"`
	}{}
	err = resp.ToJSON(&res)
	logs.Debug("addAgentUser /api/user/register res:", res)
	if err != nil {
		logs.Error("addAgentUser resp.ToJSON err:", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if res.Code != 200 {
		ctx.RespErrString(true, &errcode, errors.New(res.Msg).Error())
		return
	}
	if res.Data.Errcode != 0 {
		ctx.RespErrString(true, &errcode, errors.New(res.Data.Errmsg).Error())
		return
	}
	if res.Data.UserId <= 0 || res.Data.AgentCode == "" || res.Data.Account == "" {
		ctx.RespErrString(true, &errcode, errors.New("注册失败！").Error())
		return
	}
	dataRes := struct {
		UserId        int32  `json:"UserId"`
		PromotionHost string `json:"PromotionHost"`
		Account       string `json:"Account"`
		Password      string `json:"Password"`
	}{}
	dataRes.UserId = res.Data.UserId
	dataRes.Account = res.Data.Account
	dataRes.PromotionHost = fmt.Sprintf(utils.TopAgentPromotionHost, reqdata.Host, res.Data.AgentCode)
	dataRes.Password = password
	ctx.RespOK(dataRes)
}

func (c *AgentController) getAgentAdminUser(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		Id       int32 `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	agentIndependenceTb := server.DaoxHashGame().XAgentIndependence
	agentIndependenceDb := server.DaoxHashGame().XAgentIndependence.WithContext(ctx.Gin())
	agentIndependence, err := agentIndependenceDb.Where(agentIndependenceTb.ID.Eq(reqdata.Id)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if agentIndependence == nil {
		ctx.RespErrString(true, &errcode, "独立代理不存在")
		return
	}
	var data = make(map[string]interface{})
	data["Account"] = fmt.Sprintf(utils.TopAgentAdminUserAccount, agentIndependence.SellerID, agentIndependence.UserID)
	data["Password"] = utils.TopAgentDefaultPassword
	ctx.RespOK(data)
}

func (c *AgentController) getChannelHost(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		ChannelId int32 `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	type Result struct {
		Host string `gorm:"column:Host"`
		Id   int32  `gorm:"column:Id"`
	}
	var result []Result
	channelHostTb := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostTb.WithContext(ctx.Gin())
	xAgentIndependence := server.DaoxHashGame().XAgentIndependence
	err = channelHostDb.Select(channelHostTb.Host, xAgentIndependence.ID).
		Where(channelHostTb.ChannelID.Eq(reqdata.ChannelId)).
		LeftJoin(xAgentIndependence, xAgentIndependence.Host.EqCol(channelHostTb.Host)).
		Scan(&result)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	res := make([]string, 0, len(result))
	for _, v := range result {
		if v.Id == 0 {
			res = append(res, v.Host)
		}
	}
	ctx.RespOK(res)
}
