// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentGameDailly(db *gorm.DB, opts ...gen.DOOption) xAgentGameDailly {
	_xAgentGameDailly := xAgentGameDailly{}

	_xAgentGameDailly.xAgentGameDaillyDo.UseDB(db, opts...)
	_xAgentGameDailly.xAgentGameDaillyDo.UseModel(&model.XAgentGameDailly{})

	tableName := _xAgentGameDailly.xAgentGameDaillyDo.TableName()
	_xAgentGameDailly.ALL = field.NewAsterisk(tableName)
	_xAgentGameDailly.ID = field.NewInt32(tableName, "Id")
	_xAgentGameDailly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xAgentGameDailly.UserID = field.NewInt32(tableName, "UserId")
	_xAgentGameDailly.Brand = field.NewString(tableName, "Brand")
	_xAgentGameDailly.GameID = field.NewString(tableName, "GameId")
	_xAgentGameDailly.CatID = field.NewInt32(tableName, "CatId")
	_xAgentGameDailly.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentGameDailly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentGameDailly.NewSelfLiuSui = field.NewFloat64(tableName, "NewSelfLiuSui")
	_xAgentGameDailly.Reward = field.NewFloat64(tableName, "Reward")
	_xAgentGameDailly.Memo = field.NewString(tableName, "Memo")
	_xAgentGameDailly.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentGameDailly.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentGameDailly.fillFieldMap()

	return _xAgentGameDailly
}

type xAgentGameDailly struct {
	xAgentGameDaillyDo xAgentGameDaillyDo

	ALL           field.Asterisk
	ID            field.Int32
	RecordDate    field.Time
	UserID        field.Int32
	Brand         field.String // 品牌
	GameID        field.String // 游戏Id
	CatID         field.Int32  // 彩票大类ID
	SellerID      field.Int32
	ChannelID     field.Int32
	NewSelfLiuSui field.Float64
	Reward        field.Float64
	Memo          field.String // 备注
	CreateTime    field.Time   // 创建时间
	UpdateTime    field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentGameDailly) Table(newTableName string) *xAgentGameDailly {
	x.xAgentGameDaillyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentGameDailly) As(alias string) *xAgentGameDailly {
	x.xAgentGameDaillyDo.DO = *(x.xAgentGameDaillyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentGameDailly) updateTableName(table string) *xAgentGameDailly {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.CatID = field.NewInt32(table, "CatId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.NewSelfLiuSui = field.NewFloat64(table, "NewSelfLiuSui")
	x.Reward = field.NewFloat64(table, "Reward")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentGameDailly) WithContext(ctx context.Context) *xAgentGameDaillyDo {
	return x.xAgentGameDaillyDo.WithContext(ctx)
}

func (x xAgentGameDailly) TableName() string { return x.xAgentGameDaillyDo.TableName() }

func (x xAgentGameDailly) Alias() string { return x.xAgentGameDaillyDo.Alias() }

func (x xAgentGameDailly) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentGameDaillyDo.Columns(cols...)
}

func (x *xAgentGameDailly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentGameDailly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["CatId"] = x.CatID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["NewSelfLiuSui"] = x.NewSelfLiuSui
	x.fieldMap["Reward"] = x.Reward
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentGameDailly) clone(db *gorm.DB) xAgentGameDailly {
	x.xAgentGameDaillyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentGameDailly) replaceDB(db *gorm.DB) xAgentGameDailly {
	x.xAgentGameDaillyDo.ReplaceDB(db)
	return x
}

type xAgentGameDaillyDo struct{ gen.DO }

func (x xAgentGameDaillyDo) Debug() *xAgentGameDaillyDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentGameDaillyDo) WithContext(ctx context.Context) *xAgentGameDaillyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentGameDaillyDo) ReadDB() *xAgentGameDaillyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentGameDaillyDo) WriteDB() *xAgentGameDaillyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentGameDaillyDo) Session(config *gorm.Session) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentGameDaillyDo) Clauses(conds ...clause.Expression) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentGameDaillyDo) Returning(value interface{}, columns ...string) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentGameDaillyDo) Not(conds ...gen.Condition) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentGameDaillyDo) Or(conds ...gen.Condition) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentGameDaillyDo) Select(conds ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentGameDaillyDo) Where(conds ...gen.Condition) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentGameDaillyDo) Order(conds ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentGameDaillyDo) Distinct(cols ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentGameDaillyDo) Omit(cols ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentGameDaillyDo) Join(table schema.Tabler, on ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentGameDaillyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentGameDaillyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentGameDaillyDo) Group(cols ...field.Expr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentGameDaillyDo) Having(conds ...gen.Condition) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentGameDaillyDo) Limit(limit int) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentGameDaillyDo) Offset(offset int) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentGameDaillyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentGameDaillyDo) Unscoped() *xAgentGameDaillyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentGameDaillyDo) Create(values ...*model.XAgentGameDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentGameDaillyDo) CreateInBatches(values []*model.XAgentGameDailly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentGameDaillyDo) Save(values ...*model.XAgentGameDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentGameDaillyDo) First() (*model.XAgentGameDailly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDailly), nil
	}
}

func (x xAgentGameDaillyDo) Take() (*model.XAgentGameDailly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDailly), nil
	}
}

func (x xAgentGameDaillyDo) Last() (*model.XAgentGameDailly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDailly), nil
	}
}

func (x xAgentGameDaillyDo) Find() ([]*model.XAgentGameDailly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentGameDailly), err
}

func (x xAgentGameDaillyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentGameDailly, err error) {
	buf := make([]*model.XAgentGameDailly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentGameDaillyDo) FindInBatches(result *[]*model.XAgentGameDailly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentGameDaillyDo) Attrs(attrs ...field.AssignExpr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentGameDaillyDo) Assign(attrs ...field.AssignExpr) *xAgentGameDaillyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentGameDaillyDo) Joins(fields ...field.RelationField) *xAgentGameDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentGameDaillyDo) Preload(fields ...field.RelationField) *xAgentGameDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentGameDaillyDo) FirstOrInit() (*model.XAgentGameDailly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDailly), nil
	}
}

func (x xAgentGameDaillyDo) FirstOrCreate() (*model.XAgentGameDailly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameDailly), nil
	}
}

func (x xAgentGameDaillyDo) FindByPage(offset int, limit int) (result []*model.XAgentGameDailly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentGameDaillyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentGameDaillyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentGameDaillyDo) Delete(models ...*model.XAgentGameDailly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentGameDaillyDo) withDO(do gen.Dao) *xAgentGameDaillyDo {
	x.DO = *do.(*gen.DO)
	return x
}
