package server

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"github.com/shopspring/decimal"
	"strings"
)

const (
	InlineButtonType_URL    = 1
	InlineButtonType_WebApp = 2
)

type InlineButton struct {
	Type              int    `json:"type"`                          // 按钮类型 1链接 2小程序 3分享 4回调
	Text              string `json:"text"`                          // 按钮文字
	URL               string `json:"url,omitempty"`                 // 跳转链接
	SwitchInlineQuery string `json:"switch_inline_query,omitempty"` // 分享
	CallbackData      string `json:"callback_data,omitempty"`       // 回调数据
}

type TextVariable struct {
	TRX      decimal.Decimal
	USDT     decimal.Decimal
	Kefu     string
	GameUrl  string
	Nickname string
	IsNew    bool
}

type Message struct {
	LangId     int32            `json:"lang_id"`               // 语言id (x_lang_list.id)
	Type       int              `json:"type,omitempty"`        // 文案类型：1:文案 2：图片+文案 3：视频+文案
	Url        string           `json:"url,omitempty"`         // 图片链接或视频链接
	Text       string           `json:"text"`                  // 文案
	IsMarkdown bool             `json:"is_markdown,omitempty"` // 是否使用Markdown语法
	Buttons    [][]InlineButton `json:"buttons,omitempty"`     // 内联按钮
	Others     []Message        `json:"others,omitempty"`      // 其他语言的文案配置
}

type Robot struct {
	Bot      *tgbotapi.BotAPI
	_message Message
}

func (r Robot) SendMessage(chatId int64, lang string, message string) error {
	var originMsg Message
	err := json.Unmarshal([]byte(message), &originMsg)
	if err != nil {
		return err
	}

	r._message = originMsg
	ChatConfig := tgbotapi.ChatConfig{ChatID: chatId}
	chat, err := r.Bot.GetChat(tgbotapi.ChatInfoConfig{
		ChatConfig: ChatConfig,
	})

	if err != nil {
		logs.Error("无法获取聊天信息:", err)
	} else {
		// 检查聊天类型并输出名称
		if chat.IsPrivate() {
			fmt.Printf("发送消息给用户: %s %s (@%s)\n", chat.FirstName, chat.LastName, chat.UserName)
		} else if chat.IsGroup() || chat.IsSuperGroup() {
			fmt.Printf("发送消息给群组: %s\n", chat.Title)
		} else if chat.IsChannel() {
			fmt.Printf("发送消息给频道: %s\n", chat.Title)
		}
	}

	// 从原始信息中获取获取对应语言的内容
	msgCfg := r.getMsgByLanguageTag(lang)
	// 设置按钮
	markup := r.makeInlineKeyboardMarkup(msgCfg.Buttons)

	msg := r.makeChatMsg(chatId, msgCfg, "", markup)
	//msgInit := tgbotapi.NewMessage(chatId, msg)
	if _, err := r.Bot.Send(msg); err != nil {
		return err
	}

	return nil
}

func (r Robot) getMsgByLanguageTag(tag string) Message {
	if tag == "zh-hant" || tag == "zh-HK" {
		tag = "zh-TW"
	} else {
		tag, _, _ = strings.Cut(tag, "-")
	}
	xLangList := DaoxHashGame().XLangList
	tb, err := xLangList.WithContext(context.Background()).Where(xLangList.LangAlisa.Eq(tag)).First()
	if err != nil {
		return r._message
	}
	if tb.ID == r._message.LangId {
		return r._message
	}
	for _, v := range r._message.Others {
		if v.LangId == tb.ID {
			return v
		}
	}
	return r._message
}

func (r Robot) makeChatMsg(chatId int64, msgCfg Message, text string, markup interface{}) tgbotapi.Chattable {
	msgType := msgCfg.Type
	isMarkdown := msgCfg.IsMarkdown
	if text == "" {
		text = msgCfg.Text
	}

	if msgType == 1 { // 文本
		msg := tgbotapi.NewMessage(chatId, text)
		msg.ReplyMarkup = markup
		if isMarkdown {
			msg.ParseMode = "Markdown"
		}
		return msg
	} else if msgType == 2 { // 图片+文本
		var file tgbotapi.RequestFileData
		if msgCfg.Url != "" {
			file = tgbotapi.FileURL(msgCfg.Url)
		}
		msg := tgbotapi.NewPhoto(chatId, file)
		msg.Caption = text
		msg.ReplyMarkup = markup
		if isMarkdown {
			msg.ParseMode = "Markdown"
		}
		return msg
	} else if msgType == 3 { // 视频+文本
		var file tgbotapi.RequestFileData
		if msgCfg.Url != "" {
			file = tgbotapi.FileURL(msgCfg.Url)
		}
		msg := tgbotapi.NewVideo(chatId, file)
		msg.Caption = text
		msg.ReplyMarkup = markup
		if isMarkdown {
			msg.ParseMode = "Markdown"
		}
		return msg
	}
	msg := tgbotapi.NewMessage(chatId, text)
	msg.ReplyMarkup = markup
	if isMarkdown {
		msg.ParseMode = "Markdown"
	}
	return msg
}

func (r Robot) makeInlineKeyboardMarkup(buttons [][]InlineButton) (markup tgbotapi.InlineKeyboardMarkup) {
	markup = tgbotapi.NewInlineKeyboardMarkup()

	for _, r := range buttons {
		row := tgbotapi.NewInlineKeyboardRow()
		for _, btn := range r {
			if btn.Type == InlineButtonType_URL {
				row = append(row, tgbotapi.NewInlineKeyboardButtonURL(btn.Text, btn.URL))
			} else if btn.Type == InlineButtonType_WebApp {
				//loginURL := bot.MakeLoginUrl(msg, nil)
				row = append(row, tgbotapi.NewInlineKeyboardButtonURL(btn.Text, "https://www.baidu.com"))
			}
		}
		markup.InlineKeyboard = append(markup.InlineKeyboard, row)
	}
	if markup.InlineKeyboard == nil {
		markup.InlineKeyboard = make([][]tgbotapi.InlineKeyboardButton, 0)
	}
	return markup
	//
	//inlineKeyboard := tgbotapi.NewInlineKeyboardMarkup(
	//	tgbotapi.NewInlineKeyboardRow(
	//		tgbotapi.NewInlineKeyboardButtonURL("Visit Website", "https://example.com"),
	//	),
	//	tgbotapi.NewInlineKeyboardRow(
	//		tgbotapi.NewInlineKeyboardButtonWebApp("Visit Website", tgbotapi.WebAppInfo{URL: "https://thirdgametest.98qf.vip"}),
	//	),
	//)
	//
	//return inlineKeyboard
}
