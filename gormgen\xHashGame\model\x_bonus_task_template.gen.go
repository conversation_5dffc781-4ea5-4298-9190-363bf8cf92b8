// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXBonusTaskTemplate = "x_bonus_task_template"

// XBonusTaskTemplate mapped from table <x_bonus_task_template>
type XBonusTaskTemplate struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Typ        int32     `gorm:"column:Typ;not null;comment:1、充值 2、流水 3、亏损 4、邀请" json:"Typ"` // 1、充值 2、流水 3、亏损 4、邀请
	Nick       string    `gorm:"column:Nick;not null" json:"Nick"`
	Bonus      float64   `gorm:"column:Bonus;not null" json:"Bonus"`
	LiuShuiOdd int32     `gorm:"column:<PERSON><PERSON>huiOdd;not null;comment:几倍流水" json:"<PERSON><PERSON>huiOdd"` // 几倍流水
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
	Condition1 int64     `gorm:"column:Condition1" json:"Condition1"`
	Condition2 int64     `gorm:"column:Condition2" json:"Condition2"`
	Condition3 int64     `gorm:"column:Condition3" json:"Condition3"`
}

// TableName XBonusTaskTemplate's table name
func (*XBonusTaskTemplate) TableName() string {
	return TableNameXBonusTaskTemplate
}
