// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXManAmountDetail(db *gorm.DB, opts ...gen.DOOption) xManAmountDetail {
	_xManAmountDetail := xManAmountDetail{}

	_xManAmountDetail.xManAmountDetailDo.UseDB(db, opts...)
	_xManAmountDetail.xManAmountDetailDo.UseModel(&model.XManAmountDetail{})

	tableName := _xManAmountDetail.xManAmountDetailDo.TableName()
	_xManAmountDetail.ALL = field.NewAsterisk(tableName)
	_xManAmountDetail.ID = field.NewInt32(tableName, "Id")
	_xManAmountDetail.UserID = field.NewInt32(tableName, "UserId")
	_xManAmountDetail.AmountType = field.NewInt32(tableName, "AmountType")
	_xManAmountDetail.SType = field.NewString(tableName, "SType")
	_xManAmountDetail.Symbol = field.NewString(tableName, "Symbol")
	_xManAmountDetail.Amount = field.NewFloat64(tableName, "Amount")
	_xManAmountDetail.MinLiuShui = field.NewFloat64(tableName, "MinLiuShui")
	_xManAmountDetail.CSGroup = field.NewString(tableName, "CSGroup")
	_xManAmountDetail.CSID = field.NewString(tableName, "CSId")
	_xManAmountDetail.CreateTime = field.NewTime(tableName, "CreateTime")
	_xManAmountDetail.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xManAmountDetail.Memo = field.NewString(tableName, "Memo")
	_xManAmountDetail.Account = field.NewString(tableName, "Account")

	_xManAmountDetail.fillFieldMap()

	return _xManAmountDetail
}

// xManAmountDetail 金额扣减表
type xManAmountDetail struct {
	xManAmountDetailDo xManAmountDetailDo

	ALL        field.Asterisk
	ID         field.Int32
	UserID     field.Int32   // 用户Id
	AmountType field.Int32   // 余额分类 1=真金，2=Bonus币
	SType      field.String  // 金额扣减名称
	Symbol     field.String  // 货币类型
	Amount     field.Float64 // 备注
	MinLiuShui field.Float64 // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	CSGroup    field.String
	CSID       field.String
	CreateTime field.Time
	TopAgentID field.Int32
	Memo       field.String // 备注
	Account    field.String // 账号

	fieldMap map[string]field.Expr
}

func (x xManAmountDetail) Table(newTableName string) *xManAmountDetail {
	x.xManAmountDetailDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xManAmountDetail) As(alias string) *xManAmountDetail {
	x.xManAmountDetailDo.DO = *(x.xManAmountDetailDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xManAmountDetail) updateTableName(table string) *xManAmountDetail {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.AmountType = field.NewInt32(table, "AmountType")
	x.SType = field.NewString(table, "SType")
	x.Symbol = field.NewString(table, "Symbol")
	x.Amount = field.NewFloat64(table, "Amount")
	x.MinLiuShui = field.NewFloat64(table, "MinLiuShui")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.Memo = field.NewString(table, "Memo")
	x.Account = field.NewString(table, "Account")

	x.fillFieldMap()

	return x
}

func (x *xManAmountDetail) WithContext(ctx context.Context) *xManAmountDetailDo {
	return x.xManAmountDetailDo.WithContext(ctx)
}

func (x xManAmountDetail) TableName() string { return x.xManAmountDetailDo.TableName() }

func (x xManAmountDetail) Alias() string { return x.xManAmountDetailDo.Alias() }

func (x xManAmountDetail) Columns(cols ...field.Expr) gen.Columns {
	return x.xManAmountDetailDo.Columns(cols...)
}

func (x *xManAmountDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xManAmountDetail) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["AmountType"] = x.AmountType
	x.fieldMap["SType"] = x.SType
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["MinLiuShui"] = x.MinLiuShui
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Account"] = x.Account
}

func (x xManAmountDetail) clone(db *gorm.DB) xManAmountDetail {
	x.xManAmountDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xManAmountDetail) replaceDB(db *gorm.DB) xManAmountDetail {
	x.xManAmountDetailDo.ReplaceDB(db)
	return x
}

type xManAmountDetailDo struct{ gen.DO }

func (x xManAmountDetailDo) Debug() *xManAmountDetailDo {
	return x.withDO(x.DO.Debug())
}

func (x xManAmountDetailDo) WithContext(ctx context.Context) *xManAmountDetailDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xManAmountDetailDo) ReadDB() *xManAmountDetailDo {
	return x.Clauses(dbresolver.Read)
}

func (x xManAmountDetailDo) WriteDB() *xManAmountDetailDo {
	return x.Clauses(dbresolver.Write)
}

func (x xManAmountDetailDo) Session(config *gorm.Session) *xManAmountDetailDo {
	return x.withDO(x.DO.Session(config))
}

func (x xManAmountDetailDo) Clauses(conds ...clause.Expression) *xManAmountDetailDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xManAmountDetailDo) Returning(value interface{}, columns ...string) *xManAmountDetailDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xManAmountDetailDo) Not(conds ...gen.Condition) *xManAmountDetailDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xManAmountDetailDo) Or(conds ...gen.Condition) *xManAmountDetailDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xManAmountDetailDo) Select(conds ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xManAmountDetailDo) Where(conds ...gen.Condition) *xManAmountDetailDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xManAmountDetailDo) Order(conds ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xManAmountDetailDo) Distinct(cols ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xManAmountDetailDo) Omit(cols ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xManAmountDetailDo) Join(table schema.Tabler, on ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xManAmountDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xManAmountDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xManAmountDetailDo) Group(cols ...field.Expr) *xManAmountDetailDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xManAmountDetailDo) Having(conds ...gen.Condition) *xManAmountDetailDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xManAmountDetailDo) Limit(limit int) *xManAmountDetailDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xManAmountDetailDo) Offset(offset int) *xManAmountDetailDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xManAmountDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xManAmountDetailDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xManAmountDetailDo) Unscoped() *xManAmountDetailDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xManAmountDetailDo) Create(values ...*model.XManAmountDetail) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xManAmountDetailDo) CreateInBatches(values []*model.XManAmountDetail, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xManAmountDetailDo) Save(values ...*model.XManAmountDetail) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xManAmountDetailDo) First() (*model.XManAmountDetail, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XManAmountDetail), nil
	}
}

func (x xManAmountDetailDo) Take() (*model.XManAmountDetail, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XManAmountDetail), nil
	}
}

func (x xManAmountDetailDo) Last() (*model.XManAmountDetail, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XManAmountDetail), nil
	}
}

func (x xManAmountDetailDo) Find() ([]*model.XManAmountDetail, error) {
	result, err := x.DO.Find()
	return result.([]*model.XManAmountDetail), err
}

func (x xManAmountDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XManAmountDetail, err error) {
	buf := make([]*model.XManAmountDetail, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xManAmountDetailDo) FindInBatches(result *[]*model.XManAmountDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xManAmountDetailDo) Attrs(attrs ...field.AssignExpr) *xManAmountDetailDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xManAmountDetailDo) Assign(attrs ...field.AssignExpr) *xManAmountDetailDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xManAmountDetailDo) Joins(fields ...field.RelationField) *xManAmountDetailDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xManAmountDetailDo) Preload(fields ...field.RelationField) *xManAmountDetailDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xManAmountDetailDo) FirstOrInit() (*model.XManAmountDetail, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XManAmountDetail), nil
	}
}

func (x xManAmountDetailDo) FirstOrCreate() (*model.XManAmountDetail, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XManAmountDetail), nil
	}
}

func (x xManAmountDetailDo) FindByPage(offset int, limit int) (result []*model.XManAmountDetail, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xManAmountDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xManAmountDetailDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xManAmountDetailDo) Delete(models ...*model.XManAmountDetail) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xManAmountDetailDo) withDO(do gen.Dao) *xManAmountDetailDo {
	x.DO = *do.(*gen.DO)
	return x
}
