package controller

import (
	"context"
	"fmt"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	daoGrom "gorm.io/gorm"
)

type NoticeController struct {
}

func (c *NoticeController) Init() {
	server.Http().Post("/api/notice/list", c.list)
	server.Http().Post("/api/notice/add", c.add)
	server.Http().Post("/api/notice/modify", c.modify)
	server.Http().Post("/api/notice/delete", c.delete)

	server.Http().Post("/api/notice/list_v2", c.list_v2)
	server.Http().Post("/api/notice/add_v2", c.add_v2)
	server.Http().Post("/api/notice/delete_v2", c.delete_v2)
	server.Http().Post("/api/notice/open_v2", c.open_v2)
}

func (c *NoticeController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		ChannelId int
		LangId    int
		Type      int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "查", "查看活动")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "LangId", "=", reqdata.LangId, 0)
	where.Add("and", "Type", "=", reqdata.Type, 0)
	presult, _ := server.Db().Table("x_notice").Where(where).OrderBy("sort desc").GetList()
	ctx.RespOK(*presult)
}

func (c *NoticeController) list_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		ChannelId int
		LangId    int
		Type      int
		Id        int
		Nick      string
		GameType  string
		Page      int
		PageSize  int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "查", "查看活动")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	// 设置默认分页参数
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 15 // 默认每页15条
	}

	// 使用 GORM 直接进行数据库分页查询
	gormDb := server.Db().GormDao().Table(model.TableNameXNoticeV2)

	// 添加查询条件
	if reqdata.SellerId > 0 {
		gormDb = gormDb.Where("SellerId = ?", reqdata.SellerId)
	}
	if reqdata.ChannelId > 0 {
		gormDb = gormDb.Where("ChannelId = ?", reqdata.ChannelId)
	}
	if reqdata.LangId > 0 {
		gormDb = gormDb.Where("LangId = ?", reqdata.LangId)
	}
	if reqdata.Type > 0 {
		gormDb = gormDb.Where("Type = ?", reqdata.Type)
	}
	if reqdata.Id > 0 {
		gormDb = gormDb.Where("Id = ?", reqdata.Id)
	}
	if len(reqdata.Nick) > 0 {
		gormDb = gormDb.Where("Nick LIKE ?", fmt.Sprintf("%%%s%%", reqdata.Nick))
	}
	if reqdata.GameType != "" {
		gormDb = gormDb.Where("GameType = ?", reqdata.GameType)
	}

	// 查询记录总数
	var total int64
	err := gormDb.Count(&total).Error
	if err != nil {
		logs.Error("list_v2 count error: %v", err)
		ctx.RespErrString(true, &errcode, "查询总数失败")
		return
	}

	// 计算分页参数
	offset := (reqdata.Page - 1) * reqdata.PageSize

	// 查询分页数据
	var results []model.XNoticeV2
	err = gormDb.Order("channelid desc, id desc, LangId").
		Offset(offset).
		Limit(reqdata.PageSize).
		Find(&results).Error

	if err != nil {
		logs.Error("list_v2 find error: %v", err)
		ctx.RespErrString(true, &errcode, "查询列表失败")
		return
	}

	// 返回分页结果
	ctx.RespOK(map[string]interface{}{
		"list":  results,
		"total": total,
	})
}

func (c *NoticeController) modify(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int `validate:"required"`
		Id       int `validate:"required"`
		Title    string
		Content  string
		State    int `validate:"required"`
		Sort     int
		Type     int `validate:"required"`
		Img      string
		Link     string
		IsNew    int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "改", "修改活动")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	sql := "update x_notice set Title = ?,Content = ?,State = ?,UpdateTime = now(),Sort = ?,Type = ?,Img = ?,Link = ?, IsNew = ? where id = ?"
	server.Db().QueryNoResult(sql, reqdata.Title, reqdata.Content, reqdata.State, reqdata.Sort, reqdata.Type, reqdata.Img, reqdata.Link, reqdata.IsNew, reqdata.Id)
	ctx.RespOK()
}

func (c *NoticeController) add(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId  int    `validate:"required"`
		ChannelId int    `validate:"required"`
		LangId    int    `validate:"required"`
		Title     string `validate:"required"`
		Content   string `validate:"required"`
		State     int    `validate:"required"`
		Sort      int
		Type      int `validate:"required"`
		Img       string
		Link      string
		IsNew     int `validate:"required"`
	}{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "增", "新增活动")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	sql := "insert into x_notice (SellerId,ChannelId,LangId,Title,Content,State,Sort,UpdateTime,Type,Img,Link,IsNew) values (?,?,?,?,?,?,?,now(),?,?,?,?)"
	err := server.Db().QueryNoResult(sql, reqdata.SellerId, reqdata.ChannelId, reqdata.LangId, reqdata.Title, reqdata.Content, reqdata.State, reqdata.Sort,
		reqdata.Type, reqdata.Img, reqdata.Link, reqdata.IsNew)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *NoticeController) add_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type Item struct {
		Title   string `validate:"required"`
		Content string `validate:"required"`
		Img     string
		Link    string
		Sort    int `validate:"required"`
		PcLink  string
	}
	type RequestData struct {
		Id        int
		SellerId  int          `validate:"required"`
		ChannelId int          `validate:"required"`
		Nick      string       `validate:"required"`
		State     int          `validate:"required"`
		Type      int          `validate:"required"`
		IsNew     int          `validate:"required"`
		IsPop     int          `validate:"required"`
		GameType  string       // 游戏类型
		Lang      map[int]Item `validate:"required"`
	}
	type RRequestData struct {
		SellerId  int
		ChannelId int
		LangId    int
		Nick      string
		Title     string
		Id        int
		Content   string
		State     int
		Sort      int
		Type      int
		Img       string
		Link      string
		IsNew     int
		IsPop     int
		PcLink    string
		GameType  string // 游戏类型
	}

	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "增", "新增活动")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	var rdatas []*RRequestData
	for k, v := range reqdata.Lang {
		t := &RRequestData{
			SellerId:  reqdata.SellerId,
			ChannelId: reqdata.ChannelId,
			Id:        reqdata.Id,
			State:     reqdata.State,
			Type:      reqdata.Type,
			IsNew:     reqdata.IsNew,
			IsPop:     reqdata.IsPop,
			LangId:    k,
			Nick:      reqdata.Nick,
			GameType:  reqdata.GameType, // 添加游戏类型
		}
		t.Img = v.Img
		t.Link = v.Link
		t.Sort = v.Sort
		t.Content = v.Content
		t.Title = v.Title
		t.PcLink = v.PcLink
		rdatas = append(rdatas, t)
	}
	for _, rdata := range rdatas {
		err := server.Db().GormDao().Transaction(func(tx *daoGrom.DB) error {
			do := tx.Table(model.TableNameXNoticeV2).Where("ChannelId = ? and LangId = ?", rdata.ChannelId, rdata.LangId)
			if rdata.Id > 0 {
				do = do.Where("Id = ?", rdata.Id)
			} else {
				do = do.Where("Nick = ?", rdata.Nick)
			}
			reModel := model.XNoticeV2{}
			result := do.First(&reModel)
			if result.Error != nil && result.Error != daoGrom.ErrRecordNotFound {
				return result.Error
			}
			if result.RowsAffected > 0 {
				// 使用 Select 方法强制更新所有字段，包括空值
				result2 := tx.Table(model.TableNameXNoticeV2).Where("ChannelId = ? and LangId = ? and Id = ?", reModel.ChannelID, reModel.LangID, reModel.ID).
					Select("Nick", "Title", "Content", "State", "Sort", "Type", "Img", "Link", "IsNew", "IsPop", "PcLink", "GameType").
					Updates(model.XNoticeV2{
						Nick:     rdata.Nick,
						Title:    rdata.Title,
						Content:  rdata.Content,
						State:    int32(rdata.State),
						Sort:     int32(rdata.Sort),
						Type:     int32(rdata.Type),
						Img:      rdata.Img,
						Link:     rdata.Link,
						IsNew:    int32(rdata.IsNew),
						IsPop:    int32(rdata.IsPop),
						PcLink:   rdata.PcLink,
						GameType: rdata.GameType, // 现在可以更新为空值
					})
				if result2.Error != nil {
					return result2.Error
				}
				// if result2.RowsAffected < 1 {
				// 	return fmt.Errorf("update RowsAffected 0")
				// }
			} else {
				do := tx.Table(model.TableNameXNoticeV2).Where("ChannelId = ?", rdata.ChannelId)
				if rdata.Id > 0 {
					do = do.Where("Id = ?", rdata.Id)
				} else {
					do = do.Where("Nick = ?", rdata.Nick)
				}
				reModel := model.XNoticeV2{}
				result := do.First(&reModel)
				if result.Error != nil && result.Error != daoGrom.ErrRecordNotFound {
					return result.Error
				}
				var id int32
				if result.RowsAffected > 0 {
					id = reModel.ID
				} else {
					sql := `
					WITH t AS (
					SELECT channelid, id FROM x_notice_v2 GROUP BY channelid, id
					)
					SELECT COUNT(*) as id FROM t
				`
					result := tx.Raw(sql).Scan(&id)
					if result.Error != nil {
						return result.Error
					}
					id = id + 1
				}
				result = tx.Table(model.TableNameXNoticeV2).Create(&model.XNoticeV2{
					SellerID:  int32(rdata.SellerId),
					ChannelID: int32(rdata.ChannelId),
					ID:        id,
					LangID:    int32(rdata.LangId),
					Nick:      rdata.Nick,
					Title:     rdata.Title,
					Content:   rdata.Content,
					State:     int32(rdata.State),
					Sort:      int32(rdata.Sort),
					Type:      int32(rdata.Type),
					Img:       rdata.Img,
					Link:      rdata.Link,
					IsNew:     int32(rdata.IsNew),
					IsPop:     int32(rdata.IsPop),
					PcLink:    rdata.PcLink,
					GameType:  rdata.GameType, // 添加游戏类型
				})
				if result.Error != nil {
					return result.Error
				}
				if result.RowsAffected < 1 {
					return fmt.Errorf("create RowsAffected 0")
				}
			}
			return nil
		})
		if err != nil {
			logs.Error("notice add_v2 error ", err)
			ctx.RespErrString(true, &errcode, "数据库错误")
			return
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("增加公告", ctx, reqdata)
}

func (c *NoticeController) delete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId int
		Ids      []int
	}{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "删", "删除活动")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	for _, id := range reqdata.Ids {
		sql := "delete from x_notice where id = ?"
		server.Db().QueryNoResult(sql, id)
	}
	ctx.RespOK()
}

func (c *NoticeController) delete_v2(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId int
		UIds     []int
	}{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "删", "删除活动")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	for _, uid := range reqdata.UIds {
		sql := "delete from x_notice_v2 where UId = ?"
		server.Db().QueryNoResult(sql, uid)
	}
	ctx.RespOK()
}

func (c *NoticeController) open_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"`
		ChannelId int `validate:"required"`
		Id        int `validate:"required"`
		State     int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "公告管理", "改", "修改公告管理-好")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	tb := server.DaoxHashGame().XNoticeV2
	db := tb.WithContext(context.Background())
	db = db.Where(tb.ChannelID.Eq(int32(reqdata.ChannelId))).Where(tb.ID.Eq(int32(reqdata.Id)))
	result, err := db.UpdateSimple(tb.State.Value(int32(reqdata.State)))
	if err != nil {
		logs.Error("open_v2 update ", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if result.RowsAffected < 1 {
		// ctx.RespErrString(true, &errcode, "RowsAffected 0")
		// return
	}
	ctx.RespOK()
	server.WriteAdminLog("禁用首页轮播图", ctx, reqdata)
}
