// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotMessageTemplte = "x_robot_message_templte"

// XRobotMessageTemplte 推送消息发送 模板
type XRobotMessageTemplte struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                     // id
	Name          string    `gorm:"column:name;not null;comment:名称" json:"name"`                                      // 名称
	FullName      string    `gorm:"column:full_name;comment:全称" json:"full_name"`                                     // 全称
	MessageType   int32     `gorm:"column:message_type;not null;comment:消息类型1 模板回复类型  2.主动推送" json:"message_type"`    // 消息类型1 模板回复类型  2.主动推送
	MessageObject int32     `gorm:"column:message_object;comment:消息对象 0私发 1：群发" json:"message_object"`                // 消息对象 0私发 1：群发
	UserType      int32     `gorm:"column:user_type;comment:推送用户类型：0： 全部 1：在库，2：不在库，3；领U用户，4领Trx用户" json:"user_type"` // 推送用户类型：0： 全部 1：在库，2：不在库，3；领U用户，4领Trx用户
	RobotType     int32     `gorm:"column:robot_type;comment:机器人配置类型：1 接待2 广告" json:"robot_type"`                     // 机器人配置类型：1 接待2 广告
	DataText      string    `gorm:"column:data_text;comment:json字段" json:"data_text"`                                 // json字段
	IsDel         int32     `gorm:"column:is_del;comment:删除" json:"is_del"`                                           // 删除
	CreateTime    time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建日期" json:"create_time"`     // 创建日期
	UpdateTime    time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新日期" json:"update_time"`     // 更新日期
}

// TableName XRobotMessageTemplte's table name
func (*XRobotMessageTemplte) TableName() string {
	return TableNameXRobotMessageTemplte
}
