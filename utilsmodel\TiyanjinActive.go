package utilsmodel

import (
	"github.com/shopspring/decimal"
)

type TiyanjinActiveRewardConfig struct {
	Index                 int             // 第几份
	RewardConditionType   int             // 发放条件 0:全部 1:不在库 2:在库 3:上一份礼金满足流水 4:上一份投注次数满足 5:按发放规则人工审核
	LastMultiple          int             // 上一份流水倍数
	LastBetCount          int             // 上一份投注次数
	AmountType            int             // 1:定额 2:不定额
	Amount                decimal.Decimal // 固定金额
	MinAmount             decimal.Decimal // 最小金额
	MaxAmount             decimal.Decimal // 最大金额
	Symbol                string          // 币种 (USDT 或 TRX)
	WithdrawConditionType int             // 礼金提现要求 1:礼金满足流水 2:投注次数满足 3:按发放规则人工审核
	Multiple              int             // 流水倍数
	BetCount              int             // 投注次数
}
