// Package active 处理活动相关的工具函数
package active

import (
	"encoding/json"
	"errors"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

// SocialMediaFollowBaseConfig 社交媒体活动配置
type SocialMediaFollowBaseConfig struct {
	// 社交媒体链接配置
	YoutubeLink   string `json:"YoutubeLink"`   // Youtube链接
	InstagramLink string `json:"InstagramLink"` // Instagram链接
	FacebookLink  string `json:"FacebookLink"`  // Facebook链接
	XLink         string `json:"XLink"`         // X链接
	TwitchLink    string `json:"TwitchLink"`    // Twitch链接
	DiscordLink   string `json:"DiscordLink"`   // Discord链接
	TiktokLink    string `json:"TiktokLink"`    // Tiktok链接
	TelegramLink  string `json:"TelegramLink"`  // Telegram链接
}

// SocialMediaFollowCheckParameter 社交媒体活动参数校验
func SocialMediaFollowCheckParameter(reqdata DefineModReq) error {
	// 检查基础配置
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		// 尝试解析为嵌套对象格式
		var langConfig SocialMediaFollowLanguageConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &langConfig)
		if err == nil {
			// 验证每个语言的配置
			if len(langConfig) == 0 {
				return errors.New("至少需要一个语言配置")
			}
			return nil
		}

		// 如果不是嵌套对象格式，尝试解析为单个配置
		var baseConfig SocialMediaFollowBaseConfig
		err = json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}
	}

	// 不需要奖励配置
	return nil
}

// HandleSocialMediaFollowAdd 处理社交媒体关注活动添加
func HandleSocialMediaFollowAdd(ctx *abugo.AbuHttpContent, reqdata struct {
	model.XActiveDefine
	GoogleCode string
}, errcode *int) {
	// 验证参数
	err := SocialMediaFollowCheckParameter(DefineModReq{
		Id:              int(reqdata.ID),
		SellerId:        int(reqdata.SellerID),
		ChannelId:       int(reqdata.ChannelID),
		ActiveId:        int(reqdata.ActiveID),
		Memo:            reqdata.Memo,
		AuditType:       int(reqdata.AuditType),
		State:           int(reqdata.State),
		Sort:            int(reqdata.Sort),
		EffectStartTime: reqdata.EffectStartTime,
		EffectEndTime:   reqdata.EffectEndTime,
		Title:           reqdata.Title,
		TitleImg:        reqdata.TitleImg,
		TitleImgLang:    reqdata.TitleImgLang,
		TopImg:          reqdata.TopImg,
		TopImgLang:      reqdata.TopImgLang,
		GameType:        reqdata.GameType,
		BaseConfig:      reqdata.BaseConfig,
		Config:          reqdata.Config,
	})
	if ctx.RespErr(err, errcode) {
		return
	}

	// 设置为自动审核/自动发放
	reqdata.AuditType = 2

	// 创建活动记录
	tb := &reqdata.XActiveDefine
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	err = xActiveDefine.WithContext(ctx.Gin()).Create(tb)
	if ctx.RespErr(err, errcode) {
		return
	}

	// 保存活动排序
	err = SaveActiveSort(tb.ID, tb.Sort, tb.TopSort)
	if ctx.RespErr(err, errcode) {
		return
	}

	// 由调用方统一处理响应，这里不需要调用 RespOK
	// ctx.RespOK()
}

// HandleSocialMediaFollowMod 处理社交媒体关注活动修改
func HandleSocialMediaFollowMod(ctx *abugo.AbuHttpContent, defineData *model.XActiveDefine, errcode *int) error {
	// 验证参数
	err := SocialMediaFollowCheckParameter(DefineModReq{
		Id:              int(defineData.ID),
		SellerId:        int(defineData.SellerID),
		ChannelId:       int(defineData.ChannelID),
		ActiveId:        int(defineData.ActiveID),
		Memo:            defineData.Memo,
		AuditType:       int(defineData.AuditType),
		State:           int(defineData.State),
		Sort:            int(defineData.Sort),
		EffectStartTime: defineData.EffectStartTime,
		EffectEndTime:   defineData.EffectEndTime,
		Title:           defineData.Title,
		TitleImg:        defineData.TitleImg,
		TitleImgLang:    defineData.TitleImgLang,
		TopImg:          defineData.TopImg,
		TopImgLang:      defineData.TopImgLang,
		GameType:        defineData.GameType,
		BaseConfig:      defineData.BaseConfig,
		Config:          defineData.Config,
	})
	if err != nil {
		ctx.RespErrString(true, errcode, err.Error())
		return err
	}

	// 更新活动记录
	dao := server.DaoxHashGame().XActiveDefine
	_, err = dao.WithContext(ctx.Gin()).
		Where(dao.ID.Eq(defineData.ID)).
		Omit(dao.SellerID, dao.ChannelID, dao.ActiveID).
		Updates(defineData)
	if err != nil {
		ctx.RespErr(err, errcode)
		return err
	}

	// 保存活动排序
	err = SaveActiveSort(defineData.ID, defineData.Sort, defineData.TopSort)
	if err != nil {
		ctx.RespErr(err, errcode)
		return err
	}
	return nil
}

// SocialMediaFollowLanguageConfig 社交媒体活动多语言配置
type SocialMediaFollowLanguageConfig map[string]SocialMediaFollowBaseConfig

// GetSocialMediaFollowConfigByLanguage 根据语言获取社交媒体配置
// 如果找不到指定语言的配置，则返回默认配置（default键对应的配置）
func GetSocialMediaFollowConfigByLanguage(baseConfigStr string, language string) (*SocialMediaFollowBaseConfig, error) {
	if baseConfigStr == "" {
		return nil, errors.New("基础配置为空")
	}

	// 尝试解析为嵌套对象格式
	var langConfig SocialMediaFollowLanguageConfig
	err := json.Unmarshal([]byte(baseConfigStr), &langConfig)
	if err == nil {
		// 查找指定语言的配置
		if config, ok := langConfig[language]; ok {
			return &config, nil
		}

		// 如果找不到指定语言，尝试使用默认配置
		if config, ok := langConfig["default"]; ok {
			return &config, nil
		}

		// 如果没有默认配置，但有其他配置，返回第一个找到的配置
		for _, config := range langConfig {
			return &config, nil
		}

		return nil, errors.New("未找到任何配置")
	}

	// 如果不是嵌套对象格式，尝试解析为单个配置
	var baseConfig SocialMediaFollowBaseConfig
	err = json.Unmarshal([]byte(baseConfigStr), &baseConfig)
	if err == nil {
		return &baseConfig, nil
	}

	// 如果都不是，返回错误
	return nil, errors.New("基础配置格式错误")
}

// GetAllSocialMediaFollowConfigs 获取所有语言的社交媒体配置
// 返回一个映射，键为语言代码，值为对应的配置
func GetAllSocialMediaFollowConfigs(baseConfigStr string) (map[string]SocialMediaFollowBaseConfig, error) {
	if baseConfigStr == "" {
		return nil, errors.New("基础配置为空")
	}

	// 尝试解析为嵌套对象格式
	var langConfig SocialMediaFollowLanguageConfig
	err := json.Unmarshal([]byte(baseConfigStr), &langConfig)
	if err == nil {
		return langConfig, nil
	}

	// 如果不是嵌套对象格式，尝试解析为单个配置
	var baseConfig SocialMediaFollowBaseConfig
	err = json.Unmarshal([]byte(baseConfigStr), &baseConfig)
	if err == nil {
		// 如果是单个配置，创建一个只有一个元素的映射
		result := make(map[string]SocialMediaFollowBaseConfig)
		result["default"] = baseConfig
		return result, nil
	}

	// 如果都不是，返回错误
	return nil, errors.New("基础配置格式错误")
}
