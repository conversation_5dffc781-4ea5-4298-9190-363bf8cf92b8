// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsEventTrackingRecord(db *gorm.DB, opts ...gen.DOOption) xAdsEventTrackingRecord {
	_xAdsEventTrackingRecord := xAdsEventTrackingRecord{}

	_xAdsEventTrackingRecord.xAdsEventTrackingRecordDo.UseDB(db, opts...)
	_xAdsEventTrackingRecord.xAdsEventTrackingRecordDo.UseModel(&model.XAdsEventTrackingRecord{})

	tableName := _xAdsEventTrackingRecord.xAdsEventTrackingRecordDo.TableName()
	_xAdsEventTrackingRecord.ALL = field.NewAsterisk(tableName)
	_xAdsEventTrackingRecord.ID = field.NewInt64(tableName, "id")
	_xAdsEventTrackingRecord.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsEventTrackingRecord.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsEventTrackingRecord.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsEventTrackingRecord.UserID = field.NewInt64(tableName, "user_id")
	_xAdsEventTrackingRecord.Os = field.NewInt32(tableName, "os")
	_xAdsEventTrackingRecord.Network = field.NewInt32(tableName, "network")
	_xAdsEventTrackingRecord.EventType = field.NewInt32(tableName, "event_type")
	_xAdsEventTrackingRecord.EventName = field.NewString(tableName, "event_name")
	_xAdsEventTrackingRecord.EventParams = field.NewString(tableName, "event_params")
	_xAdsEventTrackingRecord.EventDate = field.NewTime(tableName, "event_date")
	_xAdsEventTrackingRecord.CreateTime = field.NewTime(tableName, "create_time")

	_xAdsEventTrackingRecord.fillFieldMap()

	return _xAdsEventTrackingRecord
}

// xAdsEventTrackingRecord 埋点事件记录表
type xAdsEventTrackingRecord struct {
	xAdsEventTrackingRecordDo xAdsEventTrackingRecordDo

	ALL         field.Asterisk
	ID          field.Int64  // 主键ID
	SellerID    field.Int32  // 运营商ID
	ChannelID   field.Int32  // 渠道ID
	TopAgentID  field.Int64  // 顶级代理ID
	UserID      field.Int64  // 用户ID
	Os          field.Int32  // 系统: 1 -- PC  2 -- h5  3 -- ios  4 -- android 5 -- 其他
	Network     field.Int32  // 网络: 1 -- WIFI   2 -- 流量
	EventType   field.Int32  // 事件类型
	EventName   field.String // 事件名称
	EventParams field.String // 事件参数
	EventDate   field.Time   // 事件日期
	CreateTime  field.Time   // 创建时间

	fieldMap map[string]field.Expr
}

func (x xAdsEventTrackingRecord) Table(newTableName string) *xAdsEventTrackingRecord {
	x.xAdsEventTrackingRecordDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsEventTrackingRecord) As(alias string) *xAdsEventTrackingRecord {
	x.xAdsEventTrackingRecordDo.DO = *(x.xAdsEventTrackingRecordDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsEventTrackingRecord) updateTableName(table string) *xAdsEventTrackingRecord {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.UserID = field.NewInt64(table, "user_id")
	x.Os = field.NewInt32(table, "os")
	x.Network = field.NewInt32(table, "network")
	x.EventType = field.NewInt32(table, "event_type")
	x.EventName = field.NewString(table, "event_name")
	x.EventParams = field.NewString(table, "event_params")
	x.EventDate = field.NewTime(table, "event_date")
	x.CreateTime = field.NewTime(table, "create_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsEventTrackingRecord) WithContext(ctx context.Context) *xAdsEventTrackingRecordDo {
	return x.xAdsEventTrackingRecordDo.WithContext(ctx)
}

func (x xAdsEventTrackingRecord) TableName() string { return x.xAdsEventTrackingRecordDo.TableName() }

func (x xAdsEventTrackingRecord) Alias() string { return x.xAdsEventTrackingRecordDo.Alias() }

func (x xAdsEventTrackingRecord) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsEventTrackingRecordDo.Columns(cols...)
}

func (x *xAdsEventTrackingRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsEventTrackingRecord) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["user_id"] = x.UserID
	x.fieldMap["os"] = x.Os
	x.fieldMap["network"] = x.Network
	x.fieldMap["event_type"] = x.EventType
	x.fieldMap["event_name"] = x.EventName
	x.fieldMap["event_params"] = x.EventParams
	x.fieldMap["event_date"] = x.EventDate
	x.fieldMap["create_time"] = x.CreateTime
}

func (x xAdsEventTrackingRecord) clone(db *gorm.DB) xAdsEventTrackingRecord {
	x.xAdsEventTrackingRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsEventTrackingRecord) replaceDB(db *gorm.DB) xAdsEventTrackingRecord {
	x.xAdsEventTrackingRecordDo.ReplaceDB(db)
	return x
}

type xAdsEventTrackingRecordDo struct{ gen.DO }

func (x xAdsEventTrackingRecordDo) Debug() *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsEventTrackingRecordDo) WithContext(ctx context.Context) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsEventTrackingRecordDo) ReadDB() *xAdsEventTrackingRecordDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsEventTrackingRecordDo) WriteDB() *xAdsEventTrackingRecordDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsEventTrackingRecordDo) Session(config *gorm.Session) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsEventTrackingRecordDo) Clauses(conds ...clause.Expression) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsEventTrackingRecordDo) Returning(value interface{}, columns ...string) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsEventTrackingRecordDo) Not(conds ...gen.Condition) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsEventTrackingRecordDo) Or(conds ...gen.Condition) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsEventTrackingRecordDo) Select(conds ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsEventTrackingRecordDo) Where(conds ...gen.Condition) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsEventTrackingRecordDo) Order(conds ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsEventTrackingRecordDo) Distinct(cols ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsEventTrackingRecordDo) Omit(cols ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsEventTrackingRecordDo) Join(table schema.Tabler, on ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsEventTrackingRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsEventTrackingRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsEventTrackingRecordDo) Group(cols ...field.Expr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsEventTrackingRecordDo) Having(conds ...gen.Condition) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsEventTrackingRecordDo) Limit(limit int) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsEventTrackingRecordDo) Offset(offset int) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsEventTrackingRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsEventTrackingRecordDo) Unscoped() *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsEventTrackingRecordDo) Create(values ...*model.XAdsEventTrackingRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsEventTrackingRecordDo) CreateInBatches(values []*model.XAdsEventTrackingRecord, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsEventTrackingRecordDo) Save(values ...*model.XAdsEventTrackingRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsEventTrackingRecordDo) First() (*model.XAdsEventTrackingRecord, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsEventTrackingRecord), nil
	}
}

func (x xAdsEventTrackingRecordDo) Take() (*model.XAdsEventTrackingRecord, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsEventTrackingRecord), nil
	}
}

func (x xAdsEventTrackingRecordDo) Last() (*model.XAdsEventTrackingRecord, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsEventTrackingRecord), nil
	}
}

func (x xAdsEventTrackingRecordDo) Find() ([]*model.XAdsEventTrackingRecord, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsEventTrackingRecord), err
}

func (x xAdsEventTrackingRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsEventTrackingRecord, err error) {
	buf := make([]*model.XAdsEventTrackingRecord, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsEventTrackingRecordDo) FindInBatches(result *[]*model.XAdsEventTrackingRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsEventTrackingRecordDo) Attrs(attrs ...field.AssignExpr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsEventTrackingRecordDo) Assign(attrs ...field.AssignExpr) *xAdsEventTrackingRecordDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsEventTrackingRecordDo) Joins(fields ...field.RelationField) *xAdsEventTrackingRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsEventTrackingRecordDo) Preload(fields ...field.RelationField) *xAdsEventTrackingRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsEventTrackingRecordDo) FirstOrInit() (*model.XAdsEventTrackingRecord, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsEventTrackingRecord), nil
	}
}

func (x xAdsEventTrackingRecordDo) FirstOrCreate() (*model.XAdsEventTrackingRecord, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsEventTrackingRecord), nil
	}
}

func (x xAdsEventTrackingRecordDo) FindByPage(offset int, limit int) (result []*model.XAdsEventTrackingRecord, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsEventTrackingRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsEventTrackingRecordDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsEventTrackingRecordDo) Delete(models ...*model.XAdsEventTrackingRecord) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsEventTrackingRecordDo) withDO(do gen.Dao) *xAdsEventTrackingRecordDo {
	x.DO = *do.(*gen.DO)
	return x
}
