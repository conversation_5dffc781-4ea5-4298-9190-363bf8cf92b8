package controller

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"
	"xserver/utils"

	"github.com/shopspring/decimal"

	"github.com/xuri/excelize/v2"
)

var third_brand map[string]interface{}

type ThirdController struct {
}

func (c *ThirdController) Init() {
	server.Http().Post("/api/third/lottery_order", c.lottery_order)
	server.Http().Post("/api/third/xiaoyouxi_order", c.xiaoyouxi_order)
	server.Http().Post("/api/third/qipai_order", c.qipai_order)
	server.Http().Post("/api/third/pg_order", c.pg_order)
	server.Http().Post("/api/third/pp_order", c.pp_order)
	server.Http().Post("/api/third/evo_order", c.evo_order)
	server.Http().Post("/api/third/wm_order", c.wm_order)
	server.Http().Post("/api/third/order_list", c.order_list)
	server.Http().Post("/api/third/sport_resettle_list", c.sport_resettle_list)
}

// 查询体育结算历史记录
func (c *ThirdController) sport_resettle_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ThirdId string
		Brand   string
	}
	errcode := 0
	reqdata := RequestData{}
	//token := server.GetToken(ctx)
	//if ctx.RespErrString(!server.Auth2(token, "三方游戏", "体育注单", "查"), &errcode, "权限不足") {
	//	return
	//}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "体育注单", "查", "查看体育注单")
	if token == nil {
		return
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", reqdata.ThirdId, 0)
	where.Add("and", "Brand", "=", reqdata.Brand, 0)
	presult, _ := server.Db().Table("x_third_sport_resettle_log").Where(where).OrderBy("id desc").GetList()
	ctx.RespOK(*presult)
}

func (c *ThirdController) lottery_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page         int
		PageSize     int
		SellerId     int
		ChannelId    int
		UserId       int
		GameId       int
		OrderId      string
		StartTime    int64
		EndTime      int64
		TopAgentId   int
		SpecialAgent int
		Export       int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "彩票记录", "查", "查看彩票订单")
	if token == nil {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_彩票记录_%s", time.Now().Format("**************")))
	var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("OrderNumber", "订单编号")
		xlsx.SetTitle("NumberOfPeriod", "期数")
		xlsx.SetTitle("GameName", "游戏名字")
		xlsx.SetTitle("IpAddress", "IP")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("GamePlayName", "玩法名称")
		xlsx.SetTitle("Times", "倍数")
		xlsx.SetTitle("BetNumber", "注数")
		xlsx.SetTitle("ODDS", "赔率")
		xlsx.SetTitle("Content", "下注内容")
		xlsx.SetTitle("Result", "开奖结果")
		xlsx.SetTitle("WinningStatus", "中奖状态")
		xlsx.SetTitle("TotalAmount", "投注金额")
		xlsx.SetTitle("Status", "注单状态")
		xlsx.SetTitle("CreateTime", "投注时间")
		xlsx.SetTitle("Platform", "下注平台")
		xlsx.SetTitle("BettingBalance", "中奖金额")
		xlsx.SetTitle("TotalKickback", "退水金额")
		xlsx.SetTitle("LotteryCreateTime", "开奖时间")
		xlsx.SetTitle("SettleTime", "结算时间")
		xlsx.SetTitleStyle()
	}

	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "GameId", "=", reqdata.GameId, 0)
		where.Add("and", "OrderNumber", "=", reqdata.OrderId, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		where.Add("and", "TopAgentId", "=", reqdata.TopAgentId, 0)
		where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
		total, presult := server.Db().Table("x_lottery_api_order").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
		if reqdata.Export != 1 {
			ctx.Put("total", total)
			ctx.Put("data", *presult)
		} else {
			for i := 0; i < len(*presult); i++ {
				for k, v := range (*presult)[i] {
					if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "WinningStatus" {
						if abugo.GetInt64FromInterface(v) == 0 {
							xlsx.SetValue(k, "输", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "平", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 2 {
							xlsx.SetValue(k, "赢", int64(i+2))
						} else {
							xlsx.SetValue(k, "未定义", int64(i+2))
						}
					} else if k == "Status" {
						if abugo.GetInt64FromInterface(v) == 0 {
							xlsx.SetValue(k, "未结算", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "已结算", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 2 {
							xlsx.SetValue(k, "取消", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 4 {
							xlsx.SetValue(k, "下注失败", int64(i+2))
						} else {
							xlsx.SetValue(k, "未定义", int64(i+2))
						}
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
			}
		}
		totalSize = total
	}
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "GameId", "=", reqdata.GameId, 0)
		where.Add("and", "OrderNumber", "=", reqdata.OrderId, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		sum := "sum(TotalAmount) as TotalAmount,sum(BettingBalance) as BettingBalance,sum(TotalKickback) as TotalKickback"
		presult, _ := server.Db().Table("x_lottery_api_order").Select(sum).Where(where).GetList()
		if reqdata.Export != 1 {
			ctx.Put("totaldata", (*presult)[0])
		} else {
			xlsx.SetValue("ChannelId", "合计", totalSize+2)
			xlsx.SetValue("TotalAmount", (*presult)[0]["TotalAmount"], totalSize+2)
			xlsx.SetValue("BettingBalance", (*presult)[0]["BettingBalance"], totalSize+2)
			xlsx.SetValue("TotalKickback", (*presult)[0]["TotalKickback"], totalSize+2)
			xlsx.SetValueStyle(totalSize + 2)
		}
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
}

func (c *ThirdController) xiaoyouxi_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page         int
		PageSize     int
		SellerId     int
		ChannelId    int
		UserId       int
		GameId       string
		OrderId      string
		StartTime    int64
		EndTime      int64
		TopAgentId   int
		SpecialAgent int
		Export       int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "趣味记录", "查", "查看趣味记录")
	if token == nil {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_趣味记录_%s", time.Now().Format("**************")))
	var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UniqueId", "订单编号")
		xlsx.SetTitle("Period", "期号")
		xlsx.SetTitle("GameCode", "游戏名字")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("Status", "下注状态")
		xlsx.SetTitle("BetAmount", "下注金额")
		xlsx.SetTitle("PayoutAmount", "中奖金额")
		xlsx.SetTitle("BetDetail", "投注")
		xlsx.SetTitle("ResultDetail", "开奖")
		xlsx.SetTitle("BetTime", "下注时间")
		xlsx.SetTitle("PayoutTime", "派彩时间")
		xlsx.SetTitle("GameFinishTime", "游戏结束时间")
		xlsx.SetTitleStyle()
	}

	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "GameCode", "=", reqdata.GameId, "")
		where.Add("and", "UniqueId", "=", reqdata.OrderId, "")
		where.Add("and", "BetTime", ">=", int(StartTime/1000), 0)
		where.Add("and", "BetTime", "<", int(EndTime/1000), 0)
		total, presult := server.Db().Table("x_xiaoyouxi_order").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
		if reqdata.Export != 1 {
			ctx.Put("total", total)
			ctx.Put("data", *presult)
		} else {
			gameNameSlice, _ := server.Db().Table("x_xiaoyouxi_gamename").GetList()
			gameNameMap := make(map[string]string)
			for i := 0; i < len(*gameNameSlice); i++ {
				gameNameMap[abugo.GetStringFromInterface((*gameNameSlice)[i]["GameCode"])] = abugo.GetStringFromInterface((*gameNameSlice)[i]["GameName"])
			}
			for i := 0; i < len(*presult); i++ {
				gameCode := ""
				betDetail := ""
				resultDetail := ""
				for k, v := range (*presult)[i] {
					if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "Status" {
						if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "已投注", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 4 {
							xlsx.SetValue(k, "中奖", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 5 {
							xlsx.SetValue(k, "未中奖", int64(i+2))
						} else {
							xlsx.SetValue(k, "未定义", int64(i+2))
						}
					} else if k == "GameCode" {
						gameCode = abugo.GetStringFromInterface(v)
						if gameName, ok := gameNameMap[abugo.GetStringFromInterface(v)]; ok {
							xlsx.SetValue(k, gameName, int64(i+2))
						} else {
							xlsx.SetValue(k, "未定义", int64(i+2))
						}

					} else if k == "BetDetail" {
						betDetail = abugo.GetStringFromInterface(v)
					} else if k == "ResultDetail" {
						resultDetail = abugo.GetStringFromInterface(v)
					} else if k == "BetTime" {
						xlsx.SetValue(k, time.Unix(abugo.GetInt64FromInterface(v), 0).Format(abugo.TimeLayout), int64(i+2))

					} else if k == "PayoutTime" {
						xlsx.SetValue(k, time.Unix(abugo.GetInt64FromInterface(v), 0).Format(abugo.TimeLayout), int64(i+2))

					} else if k == "GameFinishTime" {
						xlsx.SetValue(k, time.Unix(abugo.GetInt64FromInterface(v), 0).Format(abugo.TimeLayout), int64(i+2))

					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
				xlsx.SetValue("BetDetail", getBettingDetails(1, gameCode, betDetail), int64(i+2))
				xlsx.SetValue("ResultDetail", getBettingDetails(2, gameCode, resultDetail), int64(i+2))
			}
		}
		totalSize = total
	}
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "GameCode", "=", reqdata.GameId, "")
		where.Add("and", "UniqueId", "=", reqdata.OrderId, "")
		where.Add("and", "BetTime", ">=", int(StartTime/1000), 0)
		where.Add("and", "BetTime", "<", int(EndTime/1000), 0)
		sum := "sum(BetAmount) as BetAmount,sum(PayoutAmount) as PayoutAmount"
		presult, _ := server.Db().Table("x_xiaoyouxi_order").Select(sum).Where(where).GetList()
		if reqdata.Export != 1 {
			ctx.Put("totaldata", (*presult)[0])
		} else {
			xlsx.SetValue("ChannelId", "合计", totalSize+2)
			xlsx.SetValue("BetAmount", (*presult)[0]["BetAmount"], totalSize+2)
			xlsx.SetValue("PayoutAmount", (*presult)[0]["PayoutAmount"], totalSize+2)
			xlsx.SetValueStyle(totalSize + 2)
		}
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
}

func (c *ThirdController) qipai_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page         int
		PageSize     int
		SellerId     int
		ChannelId    int
		UserId       int
		GameId       int
		OrderId      string
		StartTime    int64
		EndTime      int64
		TopAgentId   int
		SpecialAgent int
		Export       int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "棋牌记录", "查", "查看棋牌记录")
	if token == nil {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_棋牌记录_%s", time.Now().Format("**************")))
	var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("RoundId", "局号")
		xlsx.SetTitle("GameId", "游戏名字")
		xlsx.SetTitle("AccountId", "三方玩家ID")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("GameResult", "输赢")
		xlsx.SetTitle("FieldId", "场ID")
		xlsx.SetTitle("FiledName", "场名称")
		xlsx.SetTitle("TableId", "桌子ID")
		xlsx.SetTitle("Chair", "座位号")
		xlsx.SetTitle("Bet", "下注金额")
		xlsx.SetTitle("ValidBet", "有效下注")
		xlsx.SetTitle("Win", "派奖金额")
		xlsx.SetTitle("Lose", "盈亏金额")
		xlsx.SetTitle("Fee", "服务费")
		xlsx.SetTitle("EnterMoney", "初始金额")
		xlsx.SetTitle("CreateTime", "创建时间")
		xlsx.SetTitle("RoundBeginTime", "游戏开始时间")
		xlsx.SetTitle("RoundEndTime", "游戏结束时间")
		xlsx.SetTitleStyle()
	}

	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "GameId", "=", reqdata.GameId, 0)
		where.Add("and", "RoundId", "=", reqdata.OrderId, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		total, presult := server.Db().Table("x_qipai_order2").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
		if reqdata.Export != 1 {
			ctx.Put("total", total)
			ctx.Put("data", *presult)
		} else {
			gameNameSlice, _ := server.Db().Table("x_qipai_gamename").GetList()
			gameNameMap := make(map[int64]string)
			for i := 0; i < len(*gameNameSlice); i++ {
				gameNameMap[abugo.GetInt64FromInterface((*gameNameSlice)[i]["GameId"])] = abugo.GetStringFromInterface((*gameNameSlice)[i]["GameName"])
			}

			for i := 0; i < len(*presult); i++ {
				for k, v := range (*presult)[i] {
					if k == "ChannelId" {
						xlsx.SetValue(k, ChannelName(int(abugo.GetInt64FromInterface(v))), int64(i+2))
					} else if k == "GameResult" {
						if abugo.GetInt64FromInterface(v) == -1 {
							xlsx.SetValue(k, "输", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 0 {
							xlsx.SetValue(k, "平", int64(i+2))
						} else if abugo.GetInt64FromInterface(v) == 1 {
							xlsx.SetValue(k, "赢", int64(i+2))
						} else {
							xlsx.SetValue(k, "未定义", int64(i+2))
						}
					} else if k == "GameId" {
						if gameName, ok := gameNameMap[abugo.GetInt64FromInterface(v)]; ok {
							xlsx.SetValue(k, gameName, int64(i+2))
						} else {
							xlsx.SetValue(k, "未定义", int64(i+2))
						}
					} else {
						xlsx.SetValue(k, v, int64(i+2))
					}
				}
			}
		}
		totalSize = total
	}
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "GameId", "=", reqdata.GameId, 0)
		where.Add("and", "RoundId", "=", reqdata.OrderId, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		sum := "sum(Bet) as Bet,sum(ValidBet) as ValidBet,sum(Win) as Win,sum(Lose) as Lose,sum(Fee) as Fee"
		presult, _ := server.Db().Table("x_qipai_order2").Select(sum).Where(where).GetList()
		if reqdata.Export != 1 {
			ctx.Put("totaldata", (*presult)[0])
		} else {
			xlsx.SetValue("ChannelId", "合计", totalSize+2)
			xlsx.SetValue("Bet", (*presult)[0]["Bet"], totalSize+2)
			xlsx.SetValue("ValidBet", (*presult)[0]["ValidBet"], totalSize+2)
			xlsx.SetValue("Win", (*presult)[0]["Win"], totalSize+2)
			xlsx.SetValue("Lose", (*presult)[0]["Lose"], totalSize+2)
			xlsx.SetValue("Fee", (*presult)[0]["Fee"], totalSize+2)
			xlsx.SetValueStyle(totalSize + 2)
		}
	}
	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
}

func getBettingDetails(t int, gameId, data string) interface{} {
	if data == "" || data == "{}" {
		return "--"
	}

	switch gameId {
	case "crash":
		obj := struct {
			Cashout     interface{} `json:"cashout"`
			ResultPoint interface{} `json:"result_point"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Cashout
		} else {
			return obj.ResultPoint
		}
	case "double":
		obj := struct {
			Color       interface{} `json:"color"`
			ResultColor interface{} `json:"result_color"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Color
		} else {
			return obj.ResultColor
		}
	case "dice":
		obj := struct {
			Point       interface{} `json:"point"`
			ResultPoint interface{} `json:"result_point"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Point
		} else {
			return obj.ResultPoint
		}
	case "limbo":
		obj := struct {
			Point       interface{} `json:"point"`
			ResultPoint interface{} `json:"result_point"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Point
		} else {
			return obj.ResultPoint
		}
	case "plinko":
		obj := struct {
			Level          string `json:"level"`
			Layer          string `json:"layer"`
			ResultOdds     string `json:"result_odds"`
			ResultPosition string `json:"result_position"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Level + "_" + obj.Layer
		} else {
			return obj.ResultOdds + "_" + obj.ResultPosition
		}
	case "keno":
		obj := struct {
			Number       interface{} `json:"number"`
			ResultNumber interface{} `json:"result_number"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Number
		} else {
			return obj.ResultNumber
		}
		// "crash":"暴力弹",
		// "double":"轮盘",
		// "dice":"骰宝",
		// "limbo":"凌波弹",
		// "plinko":"叮咚球",
		// "keno":"基诺球",
		// "mine":"挖矿弹",
		// "crypto":"宝石",
		// "triple":"三联爆",
		// "hilo":"西洛",
		// "coin":"猜硬币",
		// "tower":"爬塔",
	case "mine":
		obj := struct {
			Position       interface{} `json:"position"`
			ResultPosition interface{} `json:"result_Position"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Position
		} else {
			return obj.ResultPosition
		}
	case "crypto":
		obj := struct {
			Content       interface{} `json:"content"`
			ResultContent interface{} `json:"result_content"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Content
		} else {
			return obj.ResultContent
		}
	case "triple":
		obj := struct {
			Position      interface{} `json:"position"`
			ResultContent interface{} `json:"result_content"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Position
		} else {
			return obj.ResultContent
		}
	case "hilo":
		obj := struct {
			Content       interface{} `json:"content"`
			ResultContent interface{} `json:"result_content"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Content
		} else {
			return obj.ResultContent
		}
	case "coin":
		obj := struct {
			Position       interface{} `json:"position"`
			ResultPosition interface{} `json:"result_position"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Position
		} else {
			return obj.ResultPosition
		}
	case "tower":
		obj := struct {
			Position       interface{} `json:"position"`
			ResultPosition interface{} `json:"result_position"`
		}{}
		json.Unmarshal([]byte(data), &obj)
		if t == 1 {
			return obj.Position
		} else {
			return obj.ResultPosition
		}
	default:
		return data
	}
}

func (c *ThirdController) pg_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page         int
		PageSize     int
		SellerId     int
		ChannelId    int
		UserId       int
		GameId       int
		BetId        string
		StartTime    int64
		EndTime      int64
		Export       int //0表示分页查询，1表示导出报表
		Id           int
		TopAgentId   int
		SpecialAgent int
		GameName     string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "PG记录", "查", "查看pG记录")
	if token == nil {
		return
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "Id", "=", reqdata.Id, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "GameId", "=", reqdata.GameId, 0)
	where.Add("and", "BetId", "=", reqdata.BetId, "")
	where.Add("and", "GameName", "=", reqdata.GameName, "")
	where.Add("and", "BetTime", ">=", StartTime, int64(0))
	where.Add("and", "BetTime", "<", EndTime, int64(0))
	totaldata, _ := server.Db().Table("x_pg_order").Select("sum(BetAmount) as BetAmount,sum(WinAmount) as WinAmount").Where(where).GetList()
	total, presult := server.Db().Table("x_pg_order").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("totaldata", (*totaldata)[0])
		ctx.Put("total", total)
		ctx.Put("data", *presult)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"注单号", "玩家Id", "渠道", "子单号", "母单号", "游戏Id", "游戏名称", "币种", "下注金额", "返奖金额", "交易前金额", "交易后金额", "投注状态", "开始游戏时间", "结束游戏时间"})
		data := *presult
		for i, d := range data {
			HandsStatus := "最后一手"
			if abugo.GetInt64FromInterface(d["HandsStatus"]) == 1 {
				HandsStatus = "非最后一手"
			}
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["UserId"],
				ChannelName(int(abugo.GetInt64FromInterface(d["ChannelId"]))),
				d["BetId"],
				d["ParentBetId"],
				d["GameId"],
				d["GameName"],
				d["Currency"],
				d["BetAmount"],
				d["WinAmount"],
				d["BalanceBefore"],
				d["BalanceAfter"],
				HandsStatus,
				abugo.TimeStampToLocalTime(abugo.GetInt64FromInterface(d["BetTime"])),
				abugo.TimeStampToLocalTime(abugo.GetInt64FromInterface(d["BetEndTime"])),
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出pg订单", ctx, reqdata)
	}

}

func (c *ThirdController) pp_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page          int
		PageSize      int
		SellerId      int
		ChannelId     int
		UserId        int
		GameId        string
		PlaySessionID int64
		StartTime     int64
		EndTime       int64
		Export        int //0表示分页查询，1表示导出报表
		Id            int
		TopAgentId    int
		SpecialAgent  int
		GameName      string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "PP记录", "查", "查看pp记录")
	if token == nil {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "Id", "=", reqdata.Id, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "GameId", "=", reqdata.GameId, "")
	where.Add("and", "GameName", "=", reqdata.GameName, "")
	where.Add("and", "PlaySessionID", "=", reqdata.PlaySessionID, int64(0))
	where.Add("and", "StartTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	where.Add("and", "StartTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	totaldata, _ := server.Db().Table("x_pp_order").Select("sum(BetAmount) as BetAmount,sum(WinAmount) as WinAmount").Where(where).GetList()
	total, presult := server.Db().Table("x_pp_order").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("totaldata", (*totaldata)[0])
		ctx.Put("total", total)
		ctx.Put("data", *presult)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"注单号", "玩家Id", "渠道", "子单号", "母单号", "游戏Id", "游戏名称", "币种", "下注金额", "返奖金额", "开始游戏时间", "结束游戏时间"})
		data := *presult
		for i, d := range data {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["UserId"],
				ChannelName(int(abugo.GetInt64FromInterface(d["ChannelId"]))),
				d["PlaySessionID"],
				d["ParentSessionID"],
				d["GameID"],
				d["GameName"],
				d["Currency"],
				d["BetAmount"],
				d["WinAmount"],
				d["StartTime"],
				d["EndTime"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出pp订单", ctx, reqdata)
	}
}

func (c *ThirdController) evo_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page         int
		PageSize     int
		SellerId     int
		ChannelId    int
		UserId       int
		GameName     string
		RoundId      string
		StartTime    int64
		EndTime      int64
		Id           int
		TopAgentId   int
		SpecialAgent int
		Export       int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "EVO记录", "查", "查看evo记录")
	if token == nil {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "Id", "=", reqdata.Id, 0)
	where.Add("and", "RoundId", "=", reqdata.RoundId, "")
	where.Add("and", "GameName", "=", reqdata.GameName, "")
	where.Add("and", "SettledAt", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	where.Add("and", "SettledAt", "<", abugo.TimeStampToLocalTime(EndTime), "")
	totaldata, _ := server.Db().Table("x_evo_order").Select("sum(BetAmount) as BetAmount,sum(WinAmount) as WinAmount").Where(where).GetList()
	total, presult := server.Db().Table("x_evo_order").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("totaldata", (*totaldata)[0])
		ctx.Put("total", total)
		ctx.Put("data", *presult)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"注单号", "玩家Id", "渠道", "桌子Id", "牌局号", "游戏名称", "币种", "下注金额", "返奖金额", "时间", "原始数据"})
		data := *presult
		for i, d := range data {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["UserId"],
				ChannelName(int(abugo.GetInt64FromInterface(d["ChannelId"]))),
				d["TableId"],
				d["RoundId"],
				d["GameName"],
				d["Currency"],
				d["BetAmount"],
				d["WinAmount"],
				d["SettledAt"],
				d["RawData"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出evo订单", ctx, reqdata)
	}
}

func (c *ThirdController) wm_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		ChannelId int
		UserId    int
		StartTime int64
		EndTime   int64
		BetId     string
		Id        int
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "三方游戏", "WM记录", "查", "查看wm记录")
	if token == nil {
		return
	}
	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "Id", "=", reqdata.Id, 0)
	where.Add("and", "BetId", "=", reqdata.BetId, "")
	where.Add("and", "SetTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	where.Add("and", "SetTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	totaldata, _ := server.Db().Table("x_wm_order").Select("sum(Bet) as Bet,sum(WinLoss) as WinLoss,sum(ValidBet) as ValidBet").Where(where).GetList()
	total, presult := server.Db().Table("x_wm_order").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if reqdata.Export != 1 {
		ctx.Put("totaldata", (*totaldata)[0])
		ctx.Put("total", total)
		ctx.Put("data", *presult)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"注单号", "玩家Id", "渠道", "三方单号", "游戏名称", "台桌编号", "下注金额", "输赢金额", "下注内容", "开奖结果", "结算时间"})
		data := *presult
		for i, d := range data {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				d["Id"],
				d["UserId"],
				ChannelName(int(abugo.GetInt64FromInterface(d["ChannelId"]))),
				d["BetId"],
				d["Gname"],
				d["TableId"],
				d["Bet"],
				d["WinLoss"],
				d["BetResult"],
				d["GameResult"],
				d["SetTime"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出wm订单", ctx, reqdata)
	}
}

func (c *ThirdController) order_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		SellerId []int32
		Export   int

		GameType int    //1彩票,2棋牌,3电子,4小游戏,5真人,6体育
		Brand    string //彩票(gfg_hash哈希彩票) 棋牌(gfg大运棋牌) 电子(gfg大运电子,pp pp电子,pg pg电子) 小游戏(laobanniang 老板娘) 真人(evo真人,wm真人) 体育()

		ChannelId []int
		Id        int
		UserId    int
		ThirdId   string
		StartTime int64
		EndTime   int64

		BetStartTime int64 //体育可按投注时间搜索
		BetEndTime   int64

		SettleStartTime int64 //体育按结算时间搜索
		SettleEndTime   int64

		Currency string
		GameName string
		GameId   string

		TopAgentId   []int
		SpecialAgent int

		Settle    []string //true已结算false还未结算
		PlatState int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	submenu := ""
	dbtable := ""
	pgGameRstToken := ""
	switch reqdata.GameType {
	case 1:
		submenu = "彩票注单"
		//if reqdata.Settle[0] == "1" && len(reqdata.Settle) == 1 {
		//	dbtable = "x_third_lottery"
		//} else {
		//	dbtable = "x_third_lottery_pre_order"
		//}
		dbtable = "x_third_lottery_pre_order"
	case 2:
		submenu = "棋牌注单"
		dbtable = "x_third_qipai"
	case 3:
		submenu = "电子注单"
		dbtable = "x_third_dianzhi"
		// pg只有电子注单
		pgGameRstToken = getPgOperatorSessionForGameRst()
	case 4:
		submenu = "小游戏注单"
		dbtable = "x_third_quwei"
	case 5:
		submenu = "真人注单"
		dbtable = "x_third_live"
	case 6:
		submenu = "体育注单"
		//if reqdata.Settle[0] == "1" && len(reqdata.Settle) == 1 {
		//	dbtable = "x_third_sport"
		//} else {
		//	dbtable = "x_third_sport_pre_order"
		//}
		dbtable = "x_third_sport_pre_order"
	case 7:
		submenu = "德州注单"
		dbtable = "x_third_texas"
	}

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "三方游戏", submenu, "查"), &errcode, "权限不足") {
		return
	}
	if token.SellerId > 0 {
		if len(reqdata.SellerId) == 0 || reqdata.SellerId[0] != int32(token.SellerId) {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}

	StartTime := reqdata.StartTime
	EndTime := reqdata.EndTime
	if EndTime > 0 {
		EndTime += 1000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}

	where := abugo.AbuDbWhere{}
	//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	// 运营商多选
	sellerIdStr := utils.ToSellers(reqdata.SellerId)
	if len(sellerIdStr) > 0 {
		where.Add("and", dbtable+".SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
	}
	channelid := ""
	for i := 0; i < len(reqdata.ChannelId); i++ {
		channelid = channelid + fmt.Sprintf("%d,", reqdata.ChannelId[i])
	}
	if len(channelid) > 0 {
		channelid = channelid[0 : len(channelid)-1]
		where.Add("and", dbtable+".ChannelId", "in", fmt.Sprintf("(%s)", channelid), nil)
	}
	where.Add("and", dbtable+".UserId", "=", reqdata.UserId, 0)
	where.Add("and", dbtable+".Id", "=", reqdata.Id, 0)
	where.Add("and", dbtable+".Brand", "=", reqdata.Brand, "")
	where.Add("and", dbtable+".ThirdId", "=", reqdata.ThirdId, "")
	where.Add("and", dbtable+".GameId", "=", reqdata.GameId, "")
	where.Add("and", dbtable+".GameName", "=", reqdata.GameName, "")
	where.Add("and", dbtable+".Currency", "=", reqdata.Currency, "")
	where.Add("and", dbtable+".ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	where.Add("and", dbtable+".ThirdTime", "<", abugo.TimeStampToLocalTime(EndTime), "")

	if reqdata.GameType == 6 { //体育可按照结算时间 和 投注时间搜索
		BetStartTime := reqdata.BetStartTime
		BetEndTime := reqdata.BetEndTime
		if BetEndTime > 0 {
			BetEndTime += 1000
		}
		SettleStartTime := reqdata.SettleStartTime
		SettleEndTime := reqdata.SettleEndTime
		if BetEndTime > 0 {
			BetEndTime += 1000
		}

		where.Add("and", dbtable+".BetTime", ">=", abugo.TimeStampToLocalTime(BetStartTime), "")
		where.Add("and", dbtable+".BetTime", "<", abugo.TimeStampToLocalTime(BetEndTime), "")
		where.Add("and", dbtable+".SettleTime", ">=", abugo.TimeStampToLocalTime(SettleStartTime), "")
		where.Add("and", dbtable+".SettleTime", "<", abugo.TimeStampToLocalTime(SettleEndTime), "")
	}
	//if !reqdata.Settle {
	//	where.Add("and", "DataState", "=", -1, "") //还未开奖的
	//}

	if len(reqdata.Settle) > 0 {
		settle := strings.Join(reqdata.Settle, ",")
		where.Add("and", dbtable+".DataState", "in", fmt.Sprintf("(%s)", settle), nil) //还未开奖的
	}

	// 输赢平查询
	if reqdata.PlatState != 0 {
		switch reqdata.PlatState {
		case 1:
			wheresq := fmt.Sprintf("(%v.BetAmount - %v.WinAmount + %v.Fee)", dbtable, dbtable, dbtable)
			where.Add("and", wheresq, "<", 0, reqdata.PlatState)
			where.Add("and", dbtable+".DataState", ">", 0, reqdata.PlatState)

		case 2:
			wheresq := fmt.Sprintf("(%v.BetAmount - %v.WinAmount + %v.Fee)", dbtable, dbtable, dbtable)
			where.Add("and", wheresq, ">", 0, reqdata.PlatState)
			where.Add("and", dbtable+".DataState", ">", 0, reqdata.PlatState)
		case 3:
			wheresq := fmt.Sprintf("(%v.BetAmount - %v.WinAmount + %v.Fee)", dbtable, dbtable, dbtable)
			where.Add("and", wheresq, "=", 0, reqdata.PlatState)
			where.Add("and", dbtable+".DataState", ">", 0, reqdata.PlatState)
		}
	}

	topagentid := ""
	for i := 0; i < len(reqdata.TopAgentId); i++ {
		topagentid = topagentid + fmt.Sprintf("%d,", reqdata.TopAgentId[i])
	}
	if len(topagentid) > 0 {
		topagentid = topagentid[0 : len(topagentid)-1]
		where.Add("and", dbtable+".TopAgentId", "in", fmt.Sprintf("(%s)", topagentid), nil)
	}

	selectFields := "sum(BetAmount) as BetAmount, sum(WinAmount) as WinAmount, sum(ValidBet) as ValidBet, round(sum(BetAmount-WinAmount),6) as PlatformProfit, sum(Fee) as Fee, sum(BetAmount-BonusBetAmount) as RealBetAmount, sum(BonusBetAmount) as BonusBetAmount, sum(WinAmount-BonusWinAmount) as RealWinAmount, sum(BonusWinAmount) as BonusWinAmount, round(sum((BetAmount-BonusBetAmount)-(WinAmount-BonusWinAmount)),6) as RealProfit, round(sum(BonusBetAmount-BonusWinAmount),6) as BonusProfit, round(sum(BetAmount-WinAmount),6) as TotalProfit"
	if reqdata.GameType == 4 { // updown trading 需要把手续费加到平台盈亏中减去反水金额 所以这里如果是查小游戏注单，统计盈亏的语句用另外一个, RefundAmount 反水金额
		selectFields = "sum(BetAmount) as BetAmount, sum(WinAmount) as WinAmount, sum(ValidBet) as ValidBet, round(sum(BetAmount-WinAmount+Fee),6) as PlatformProfit, sum(Fee) as Fee, sum(RefundAmount) as RefundAmount, sum(BetAmount-BonusBetAmount) as RealBetAmount, sum(BonusBetAmount) as BonusBetAmount, sum(WinAmount-BonusWinAmount) as RealWinAmount, sum(BonusWinAmount) as BonusWinAmount, round(sum((BetAmount-BonusBetAmount)-(WinAmount-BonusWinAmount)),6) as RealProfit, round(sum(BonusBetAmount-BonusWinAmount),6) as BonusProfit, round(sum(BetAmount-WinAmount+Fee),6) as TotalProfit"
	}
	where.Add("and", dbtable+".SpecialAgent", "=", reqdata.SpecialAgent, 0)
	joinSql := fmt.Sprintf("left join x_user on %v.userid = x_user.userid", dbtable)
	totaldata, _ := server.Db().Table(dbtable).Select(selectFields).Where(where).GetList()
	selectSql := fmt.Sprintf("%v.*,ifnull(%v.BetCtxType, 1) BetCtxType, x_user.RegisterIP as RegisterIP, x_user.RegLang as RegLang", dbtable, dbtable)
	presult, total := server.Db().Table(dbtable).Select(selectSql).
		Where(where).Join(joinSql).PageDataEx(reqdata.Page, reqdata.PageSize, dbtable+".ThirdTime", "desc")
	//userIds := make([]int32, 0, total)
	//var userId int32
	for item, _ := range *presult {
		//(*presult)[item]["RegisterIP"] = ""
		//(*presult)[item]["RegLang"] = ""
		//userId = int32(abugo.GetInt64FromInterface((*presult)[item]["UserId"]))
		//if userId > 0 {
		//	userIds = append(userIds, userId)
		//}
		// 新增平台盈亏 解决精度问题
		//platformProfit :=

		platformProfit := abugo.GetFloat64FromInterface((*presult)[item]["BetAmount"]) - abugo.GetFloat64FromInterface((*presult)[item]["WinAmount"])
		if reqdata.GameType == 4 { // updown trading 需要把手续费加到平台盈亏中
			platformProfit += abugo.GetFloat64FromInterface((*presult)[item]["Fee"])
		}
		platformProfit, _ = decimal.NewFromFloat(platformProfit).Round(6).Float64()

		(*presult)[item]["PlatformProfit"] = platformProfit

		// 添加真金下注、Bonus币下注、真金派彩、Bonus币派彩字段
		bonusBetAmount := abugo.GetFloat64FromInterface((*presult)[item]["BonusBetAmount"])
		realBetAmount := abugo.GetFloat64FromInterface((*presult)[item]["BetAmount"]) - bonusBetAmount
		bonusWinAmount := abugo.GetFloat64FromInterface((*presult)[item]["BonusWinAmount"])
		realWinAmount := abugo.GetFloat64FromInterface((*presult)[item]["WinAmount"]) - bonusWinAmount

		// 计算平台真金输赢、平台Bonus币输赢、平台合计输赢
		realProfit := realBetAmount - realWinAmount
		bonusProfit := bonusBetAmount - bonusWinAmount
		totalProfit := platformProfit

		realBetAmount, _ = decimal.NewFromFloat(realBetAmount).Round(2).Float64()
		realWinAmount, _ = decimal.NewFromFloat(realWinAmount).Round(2).Float64()
		realProfit, _ = decimal.NewFromFloat(realProfit).Round(2).Float64()
		bonusProfit, _ = decimal.NewFromFloat(bonusProfit).Round(2).Float64()

		(*presult)[item]["RealBetAmount"] = realBetAmount
		(*presult)[item]["BonusBetAmount"] = bonusBetAmount
		(*presult)[item]["RealWinAmount"] = realWinAmount
		(*presult)[item]["BonusWinAmount"] = bonusWinAmount
		(*presult)[item]["RealProfit"] = realProfit
		(*presult)[item]["BonusProfit"] = bonusProfit
		(*presult)[item]["TotalProfit"] = totalProfit
		dataState := abugo.GetInt64FromInterface((*presult)[item]["DataState"])
		// 新增平台状态
		platState := ""

		if dataState == 1 || dataState == 2 {
			if platformProfit > 0 {
				platState = "赢"
			} else if platformProfit < 0 {
				platState = "输"
			} else {
				platState = "平"
			}
		}
		if dataState == -1 {
			platState = "未开奖"
		}
		if dataState == -2 {
			platState = "已撤单"
			platformProfit = 0 //撤单状态利润应该为0
		}
		(*presult)[item]["PlatState"] = platState
		// pg游戏游戏下注内容开奖结果内容添加请求令牌
		if abugo.GetStringFromInterface((*presult)[item]["Brand"]) == "pg" && abugo.GetInt64FromInterface((*presult)[item]["BetCtxType"]) == 2 {
			betCtx := abugo.GetStringFromInterface((*presult)[item]["BetCtx"])
			gameRst := abugo.GetStringFromInterface((*presult)[item]["GameRst"])
			if betCtx != "" {
				(*presult)[item]["BetCtx"] = fmt.Sprintf(betCtx, pgGameRstToken)
			}
			if gameRst != "" {
				(*presult)[item]["GameRst"] = fmt.Sprintf(gameRst, pgGameRstToken)
			}
		}
	}
	//if len(userIds) > 0 {
	//	userTb := server.DaoxHashGame().XUser
	//	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	//	userLists, err := userDb.Select(userTb.UserID, userTb.RegisterIP, userTb.RegLang).
	//		Where(userTb.UserID.In(userIds...)).Find()
	//	if err != nil {
	//		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
	//		return
	//	}
	//	for item, _ := range *presult {
	//		userId = int32(abugo.GetInt64FromInterface((*presult)[item]["UserId"]))
	//		for _, user := range userLists {
	//			if user.UserID == userId {
	//				(*presult)[item]["RegisterIP"] = user.RegisterIP
	//				(*presult)[item]["RegLang"] = user.RegLang
	//				break
	//			}
	//		}
	//	}
	//}

	if reqdata.Export != 1 {
		ctx.Put("totaldata", (*totaldata)[0])
		ctx.Put("total", total)
		ctx.Put("data", *presult)
		ctx.RespOK()
	} else {
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"运营商", "注单号", "玩家Id", "注册渠道", "下注渠道", "游戏厂商", "三方单号", "游戏Id", "游戏名称", "币种", "下注金额", "派奖金额", "有效下注", "平台盈亏", "真金下注金额", "Bonus币下注金额", "真金派彩金额", "Bonus币派彩金额", "平台真金输赢", "平台Bonus币输赢", "平台合计输赢", "平台状态", "订单时间", "状态", "原始数据"})
		data := *presult
		sellerNameMap, err := InitSellerNameMap()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		for i, d := range data {
			if d["Brand"] == "laobanniang" {
				d["Brand"] = "T1"
			}
			// 平台盈亏
			platformProfit := abugo.GetFloat64FromInterface(d["BetAmount"]) - abugo.GetFloat64FromInterface(d["WinAmount"])
			if reqdata.GameType == 4 { // updown trading 需要把手续费加到平台盈亏中
				platformProfit += abugo.GetFloat64FromInterface(d["Fee"])
			}
			platformProfit, _ = decimal.NewFromFloat(platformProfit).Round(6).Float64()

			// 添加真金下注、Bonus币下注、真金派彩、Bonus币派彩字段
			bonusBetAmount := abugo.GetFloat64FromInterface(d["BonusBetAmount"])
			realBetAmount := abugo.GetFloat64FromInterface(d["BetAmount"]) - bonusBetAmount
			bonusWinAmount := abugo.GetFloat64FromInterface(d["BonusWinAmount"])
			realWinAmount := abugo.GetFloat64FromInterface(d["WinAmount"]) - bonusWinAmount

			// 计算平台真金输赢、平台Bonus币输赢、平台合计输赢
			realProfit := realBetAmount - realWinAmount
			bonusProfit := bonusBetAmount - bonusWinAmount
			totalProfit := platformProfit

			realBetAmount, _ = decimal.NewFromFloat(realBetAmount).Round(2).Float64()
			realWinAmount, _ = decimal.NewFromFloat(realWinAmount).Round(2).Float64()
			realProfit, _ = decimal.NewFromFloat(realProfit).Round(2).Float64()
			bonusProfit, _ = decimal.NewFromFloat(bonusProfit).Round(2).Float64()
			dataState := abugo.GetInt64FromInterface(d["DataState"])
			// 平台状态
			platState := ""
			if dataState == 1 || dataState == 2 {
				if platformProfit > 0 {
					platState = "赢"
				} else if platformProfit < 0 {
					platState = "输"
				} else {
					platState = "平"
				}
			}
			if dataState == -1 {
				platState = "未开奖"
			}
			if dataState == -2 {
				platState = "已撤单"
				platformProfit = 0 //撤单状态利润应该为0
			}

			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(d["SellerId"]))),
				d["Id"],
				d["UserId"],
				ChannelName(int(abugo.GetInt64FromInterface(d["ChannelId"]))),
				ChannelName(int(abugo.GetInt64FromInterface(d["BetChannelId"]))),
				d["Brand"],
				d["ThirdId"],
				d["GameId"],
				d["GameName"],
				d["Currency"],
				d["BetAmount"],
				d["WinAmount"],
				d["ValidBet"],
				platformProfit,
				realBetAmount,
				bonusBetAmount,
				realWinAmount,
				bonusWinAmount,
				realProfit,
				bonusProfit,
				totalProfit,
				platState,
				d["ThirdTime"],
				d["State"],
				d["RawData"],
			})
		}
		filename := "export_order_" + time.Now().Format("**************") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出三方注单", ctx, reqdata)
	}
}

// pg获取运营商令牌
func getPgOperatorSessionForGameRst() (t string) {
	// 获取pg令牌缓存
	cacheKeyOpSession := fmt.Sprintf("%v:%v:cache:pg:opsession", server.Project(), server.Module())
	if cacheValue := server.Redis().Get(cacheKeyOpSession); cacheValue != nil {
		t = string(cacheValue.([]byte))
		// fmt.Println("INFO PG 获取运营商session缓存成功:  cacheKeyOpSession = ", cacheKeyOpSession, " t = ", t)
		return
	}

	defer func() {
		if t != "" {
			// 设置pg令牌缓存 30分钟过期 我们取15分钟短 测试先用缓存60秒 上线用缓存900秒
			if e := server.Redis().SetStringEx(cacheKeyOpSession, 900, t); e != nil {
				fmt.Println("ERROR PG 设置opsession错误:  cacheKeyOpSession = ", cacheKeyOpSession, e)
			}
		}
	}()

	type ThirdConfig struct {
		Brand       string `json:"Brand"`
		ConfigValue string `json:"ConfigValue"`
	}
	cfg := ThirdConfig{}
	err := server.Db().GormDao().Table("x_third_config").Where("Brand=?", "pgsignle").First(&cfg).Error
	if err != nil {
		fmt.Println("ERROR PG 获取运营商session错误0 err=", err.Error())
		return
	}
	type ThirdConfigPgSignle struct {
		OperatorToken string `json:"operator_token"`
		SecretKey     string `json:"secret_key"`
		Url           string `json:"url"`
	}
	cfgPgSignle := ThirdConfigPgSignle{}
	err = json.Unmarshal([]byte(cfg.ConfigValue), &cfgPgSignle)
	if err != nil {
		fmt.Println("ERROR PG 获取运营商session错误00 err=", err.Error())
		return
	}

	type ResponseData struct {
		Data struct {
			OperatorSession string `json:"operator_session"`
		} `json:"data"`
		Error string `json:"error"`
	}
	rsp := ResponseData{}

	urlreq := fmt.Sprintf("%s/external/Login/v1/LoginProxy?trace_id=%s", cfgPgSignle.Url, abugo.GetUuid())
	postData := url.Values{}
	postData.Set("operator_token", cfgPgSignle.OperatorToken)
	postData.Set("secret_key", cfgPgSignle.SecretKey)
	reqBytes := []byte(postData.Encode())

	client := &http.Client{}
	payload := strings.NewReader(string(reqBytes))
	req, _ := http.NewRequest(http.MethodPost, urlreq, payload)
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("ERROR PG 获取运营商session错误 err=", err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("ERROR PG 获取运营商session错误2 err=", err.Error())
		return
	}
	// fmt.Println("INFOPG 获取运营商session成功 respBytes=", string(respBytes))
	err = json.Unmarshal(respBytes, &rsp)
	if err != nil {
		fmt.Println("ERROR PG 获取运营商session错误3 err=", err.Error())
		return
	}
	if rsp.Error != "" {
		fmt.Println("ERROR PG 获取运营商session错误4 err=", rsp.Error)
		return
	}
	if rsp.Data.OperatorSession != "" {
		t = rsp.Data.OperatorSession
		// fmt.Println("INFO PG 获取运营商session成功 OperatorSession=", rsp.Data.OperatorSession)
	} else {
		fmt.Println("ERROR PG 获取运营商session错误5 OperatorSession=", rsp.Data.OperatorSession)
	}
	return
}
