// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgRedpacketInviteCount(db *gorm.DB, opts ...gen.DOOption) xTgRedpacketInviteCount {
	_xTgRedpacketInviteCount := xTgRedpacketInviteCount{}

	_xTgRedpacketInviteCount.xTgRedpacketInviteCountDo.UseDB(db, opts...)
	_xTgRedpacketInviteCount.xTgRedpacketInviteCountDo.UseModel(&model.XTgRedpacketInviteCount{})

	tableName := _xTgRedpacketInviteCount.xTgRedpacketInviteCountDo.TableName()
	_xTgRedpacketInviteCount.ALL = field.NewAsterisk(tableName)
	_xTgRedpacketInviteCount.ID = field.NewInt32(tableName, "Id")
	_xTgRedpacketInviteCount.TgUserID = field.NewInt64(tableName, "TgUserId")
	_xTgRedpacketInviteCount.TgID = field.NewInt32(tableName, "TgId")
	_xTgRedpacketInviteCount.InviteCount = field.NewInt32(tableName, "InviteCount")
	_xTgRedpacketInviteCount.CountDate = field.NewTime(tableName, "CountDate")
	_xTgRedpacketInviteCount.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgRedpacketInviteCount.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTgRedpacketInviteCount.fillFieldMap()

	return _xTgRedpacketInviteCount
}

type xTgRedpacketInviteCount struct {
	xTgRedpacketInviteCountDo xTgRedpacketInviteCountDo

	ALL         field.Asterisk
	ID          field.Int32
	TgUserID    field.Int64 // Tg用户ID
	TgID        field.Int32 // Tg后台ID
	InviteCount field.Int32 // 邀请次数
	CountDate   field.Time  // 统计日期
	CreateTime  field.Time
	UpdateTime  field.Time

	fieldMap map[string]field.Expr
}

func (x xTgRedpacketInviteCount) Table(newTableName string) *xTgRedpacketInviteCount {
	x.xTgRedpacketInviteCountDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRedpacketInviteCount) As(alias string) *xTgRedpacketInviteCount {
	x.xTgRedpacketInviteCountDo.DO = *(x.xTgRedpacketInviteCountDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRedpacketInviteCount) updateTableName(table string) *xTgRedpacketInviteCount {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.TgUserID = field.NewInt64(table, "TgUserId")
	x.TgID = field.NewInt32(table, "TgId")
	x.InviteCount = field.NewInt32(table, "InviteCount")
	x.CountDate = field.NewTime(table, "CountDate")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTgRedpacketInviteCount) WithContext(ctx context.Context) *xTgRedpacketInviteCountDo {
	return x.xTgRedpacketInviteCountDo.WithContext(ctx)
}

func (x xTgRedpacketInviteCount) TableName() string { return x.xTgRedpacketInviteCountDo.TableName() }

func (x xTgRedpacketInviteCount) Alias() string { return x.xTgRedpacketInviteCountDo.Alias() }

func (x xTgRedpacketInviteCount) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRedpacketInviteCountDo.Columns(cols...)
}

func (x *xTgRedpacketInviteCount) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRedpacketInviteCount) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 7)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["TgUserId"] = x.TgUserID
	x.fieldMap["TgId"] = x.TgID
	x.fieldMap["InviteCount"] = x.InviteCount
	x.fieldMap["CountDate"] = x.CountDate
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTgRedpacketInviteCount) clone(db *gorm.DB) xTgRedpacketInviteCount {
	x.xTgRedpacketInviteCountDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRedpacketInviteCount) replaceDB(db *gorm.DB) xTgRedpacketInviteCount {
	x.xTgRedpacketInviteCountDo.ReplaceDB(db)
	return x
}

type xTgRedpacketInviteCountDo struct{ gen.DO }

func (x xTgRedpacketInviteCountDo) Debug() *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRedpacketInviteCountDo) WithContext(ctx context.Context) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRedpacketInviteCountDo) ReadDB() *xTgRedpacketInviteCountDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRedpacketInviteCountDo) WriteDB() *xTgRedpacketInviteCountDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRedpacketInviteCountDo) Session(config *gorm.Session) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRedpacketInviteCountDo) Clauses(conds ...clause.Expression) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRedpacketInviteCountDo) Returning(value interface{}, columns ...string) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRedpacketInviteCountDo) Not(conds ...gen.Condition) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRedpacketInviteCountDo) Or(conds ...gen.Condition) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRedpacketInviteCountDo) Select(conds ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRedpacketInviteCountDo) Where(conds ...gen.Condition) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRedpacketInviteCountDo) Order(conds ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRedpacketInviteCountDo) Distinct(cols ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRedpacketInviteCountDo) Omit(cols ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRedpacketInviteCountDo) Join(table schema.Tabler, on ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRedpacketInviteCountDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRedpacketInviteCountDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRedpacketInviteCountDo) Group(cols ...field.Expr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRedpacketInviteCountDo) Having(conds ...gen.Condition) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRedpacketInviteCountDo) Limit(limit int) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRedpacketInviteCountDo) Offset(offset int) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRedpacketInviteCountDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRedpacketInviteCountDo) Unscoped() *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRedpacketInviteCountDo) Create(values ...*model.XTgRedpacketInviteCount) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRedpacketInviteCountDo) CreateInBatches(values []*model.XTgRedpacketInviteCount, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRedpacketInviteCountDo) Save(values ...*model.XTgRedpacketInviteCount) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRedpacketInviteCountDo) First() (*model.XTgRedpacketInviteCount, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketInviteCount), nil
	}
}

func (x xTgRedpacketInviteCountDo) Take() (*model.XTgRedpacketInviteCount, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketInviteCount), nil
	}
}

func (x xTgRedpacketInviteCountDo) Last() (*model.XTgRedpacketInviteCount, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketInviteCount), nil
	}
}

func (x xTgRedpacketInviteCountDo) Find() ([]*model.XTgRedpacketInviteCount, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRedpacketInviteCount), err
}

func (x xTgRedpacketInviteCountDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRedpacketInviteCount, err error) {
	buf := make([]*model.XTgRedpacketInviteCount, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRedpacketInviteCountDo) FindInBatches(result *[]*model.XTgRedpacketInviteCount, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRedpacketInviteCountDo) Attrs(attrs ...field.AssignExpr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRedpacketInviteCountDo) Assign(attrs ...field.AssignExpr) *xTgRedpacketInviteCountDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRedpacketInviteCountDo) Joins(fields ...field.RelationField) *xTgRedpacketInviteCountDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRedpacketInviteCountDo) Preload(fields ...field.RelationField) *xTgRedpacketInviteCountDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRedpacketInviteCountDo) FirstOrInit() (*model.XTgRedpacketInviteCount, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketInviteCount), nil
	}
}

func (x xTgRedpacketInviteCountDo) FirstOrCreate() (*model.XTgRedpacketInviteCount, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketInviteCount), nil
	}
}

func (x xTgRedpacketInviteCountDo) FindByPage(offset int, limit int) (result []*model.XTgRedpacketInviteCount, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRedpacketInviteCountDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRedpacketInviteCountDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRedpacketInviteCountDo) Delete(models ...*model.XTgRedpacketInviteCount) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRedpacketInviteCountDo) withDO(do gen.Dao) *xTgRedpacketInviteCountDo {
	x.DO = *do.(*gen.DO)
	return x
}
