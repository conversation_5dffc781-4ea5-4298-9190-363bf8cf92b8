// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotReportImportFlow(db *gorm.DB, opts ...gen.DOOption) xRobotReportImportFlow {
	_xRobotReportImportFlow := xRobotReportImportFlow{}

	_xRobotReportImportFlow.xRobotReportImportFlowDo.UseDB(db, opts...)
	_xRobotReportImportFlow.xRobotReportImportFlowDo.UseModel(&model.XRobotReportImportFlow{})

	tableName := _xRobotReportImportFlow.xRobotReportImportFlowDo.TableName()
	_xRobotReportImportFlow.ALL = field.NewAsterisk(tableName)
	_xRobotReportImportFlow.ID = field.NewInt64(tableName, "id")
	_xRobotReportImportFlow.DateTime = field.NewTime(tableName, "date_time")
	_xRobotReportImportFlow.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotReportImportFlow.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotReportImportFlow.UserID = field.NewInt64(tableName, "user_id")
	_xRobotReportImportFlow.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xRobotReportImportFlow.BandingTrxAddress = field.NewString(tableName, "banding_trx_address")
	_xRobotReportImportFlow.IsIndb = field.NewInt32(tableName, "is_indb")
	_xRobotReportImportFlow.StartCnt = field.NewInt32(tableName, "start_cnt")
	_xRobotReportImportFlow.GiftUsdtStatus = field.NewInt32(tableName, "gift_usdt_status")
	_xRobotReportImportFlow.GiftTrxStatus = field.NewInt32(tableName, "gift_trx_status")
	_xRobotReportImportFlow.GiftUsdtAmount = field.NewInt32(tableName, "gift_usdt_amount")
	_xRobotReportImportFlow.GiftTrxAmount = field.NewInt32(tableName, "gift_trx_amount")
	_xRobotReportImportFlow.RechargeCnt = field.NewInt32(tableName, "recharge_cnt")
	_xRobotReportImportFlow.RechargeAmount = field.NewFloat64(tableName, "recharge_amount")
	_xRobotReportImportFlow.WithdrawCnt = field.NewInt32(tableName, "withdraw_cnt")
	_xRobotReportImportFlow.WithdrawAmount = field.NewFloat64(tableName, "withdraw_amount")
	_xRobotReportImportFlow.FirstRechargeAmount = field.NewFloat64(tableName, "first_recharge_amount")
	_xRobotReportImportFlow.FirstRechargeTime = field.NewTime(tableName, "first_recharge_time")
	_xRobotReportImportFlow.HashBalanceBetAmount = field.NewFloat64(tableName, "hash_balance_bet_amount")
	_xRobotReportImportFlow.HashBalanceWinAmount = field.NewFloat64(tableName, "hash_balance_win_amount")
	_xRobotReportImportFlow.HashBalanceBetCount = field.NewInt32(tableName, "hash_balance_bet_count")
	_xRobotReportImportFlow.HashBalanceWinCount = field.NewInt32(tableName, "hash_balance_win_count")
	_xRobotReportImportFlow.HashBalanceFirstBet = field.NewFloat64(tableName, "hash_balance_first_bet")
	_xRobotReportImportFlow.HashTransferTrxBetAmount = field.NewFloat64(tableName, "hash_transfer_trx_bet_amount")
	_xRobotReportImportFlow.HashTransferTrxWinAmount = field.NewFloat64(tableName, "hash_transfer_trx_win_amount")
	_xRobotReportImportFlow.HashTransferTrxBetCount = field.NewInt32(tableName, "hash_transfer_trx_bet_count")
	_xRobotReportImportFlow.HashTransferTrxWinCount = field.NewInt32(tableName, "hash_transfer_trx_win_count")
	_xRobotReportImportFlow.HashTransferTrxFirstBet = field.NewFloat64(tableName, "hash_transfer_trx_first_bet")
	_xRobotReportImportFlow.HashTransferUsdtBetAmount = field.NewFloat64(tableName, "hash_transfer_usdt_bet_amount")
	_xRobotReportImportFlow.HashTransferUsdtWinAmount = field.NewFloat64(tableName, "hash_transfer_usdt_win_amount")
	_xRobotReportImportFlow.HashTransferUsdtBetCount = field.NewInt32(tableName, "hash_transfer_usdt_bet_count")
	_xRobotReportImportFlow.HashTransferUsdtWinCount = field.NewInt32(tableName, "hash_transfer_usdt_win_count")
	_xRobotReportImportFlow.HashTransferUsdtFirstBet = field.NewFloat64(tableName, "hash_transfer_usdt_first_bet")
	_xRobotReportImportFlow.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotReportImportFlow.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotReportImportFlow.fillFieldMap()

	return _xRobotReportImportFlow
}

type xRobotReportImportFlow struct {
	xRobotReportImportFlowDo xRobotReportImportFlowDo

	ALL                       field.Asterisk
	ID                        field.Int64   // pk
	DateTime                  field.Time    // 日期
	SellerID                  field.Int32   // 经销商ID
	ChannelID                 field.Int32   // 渠道ID
	UserID                    field.Int64   // 用户id
	TopAgentID                field.Int64   // 顶级代理用户ID
	BandingTrxAddress         field.String  // 总投注人数
	IsIndb                    field.Int32   // 0不在库;1在库
	StartCnt                  field.Int32   // 启动机器人
	GiftUsdtStatus            field.Int32   // usdt体验金
	GiftTrxStatus             field.Int32   // trx体验金
	GiftUsdtAmount            field.Int32   // usdt体验金金额
	GiftTrxAmount             field.Int32   // trx金额
	RechargeCnt               field.Int32   // 充值次数
	RechargeAmount            field.Float64 // 充值金额
	WithdrawCnt               field.Int32   // 提款次数
	WithdrawAmount            field.Float64 // 提款金额
	FirstRechargeAmount       field.Float64 // 首次充值金额
	FirstRechargeTime         field.Time    // 首次充值时间
	HashBalanceBetAmount      field.Float64 // 余额投注额
	HashBalanceWinAmount      field.Float64 // 余额赢
	HashBalanceBetCount       field.Int32   // 投注次数
	HashBalanceWinCount       field.Int32   // 赢次数
	HashBalanceFirstBet       field.Float64 // 首次投注
	HashTransferTrxBetAmount  field.Float64 // 转账trx投注额
	HashTransferTrxWinAmount  field.Float64 // 转账trx赢
	HashTransferTrxBetCount   field.Int32   // 转账trx投注次数
	HashTransferTrxWinCount   field.Int32   // 转账trx赢次数
	HashTransferTrxFirstBet   field.Float64 // 首次投注
	HashTransferUsdtBetAmount field.Float64 // 转账usdt投注额
	HashTransferUsdtWinAmount field.Float64 // 转账usdt赢
	HashTransferUsdtBetCount  field.Int32   // 转账usdt投注次数
	HashTransferUsdtWinCount  field.Int32   // 转账usdt赢次数
	HashTransferUsdtFirstBet  field.Float64 // 首次投注
	CreateTime                field.Time    // 创建
	UpdateTime                field.Time    // 更新

	fieldMap map[string]field.Expr
}

func (x xRobotReportImportFlow) Table(newTableName string) *xRobotReportImportFlow {
	x.xRobotReportImportFlowDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotReportImportFlow) As(alias string) *xRobotReportImportFlow {
	x.xRobotReportImportFlowDo.DO = *(x.xRobotReportImportFlowDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotReportImportFlow) updateTableName(table string) *xRobotReportImportFlow {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.DateTime = field.NewTime(table, "date_time")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.UserID = field.NewInt64(table, "user_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.BandingTrxAddress = field.NewString(table, "banding_trx_address")
	x.IsIndb = field.NewInt32(table, "is_indb")
	x.StartCnt = field.NewInt32(table, "start_cnt")
	x.GiftUsdtStatus = field.NewInt32(table, "gift_usdt_status")
	x.GiftTrxStatus = field.NewInt32(table, "gift_trx_status")
	x.GiftUsdtAmount = field.NewInt32(table, "gift_usdt_amount")
	x.GiftTrxAmount = field.NewInt32(table, "gift_trx_amount")
	x.RechargeCnt = field.NewInt32(table, "recharge_cnt")
	x.RechargeAmount = field.NewFloat64(table, "recharge_amount")
	x.WithdrawCnt = field.NewInt32(table, "withdraw_cnt")
	x.WithdrawAmount = field.NewFloat64(table, "withdraw_amount")
	x.FirstRechargeAmount = field.NewFloat64(table, "first_recharge_amount")
	x.FirstRechargeTime = field.NewTime(table, "first_recharge_time")
	x.HashBalanceBetAmount = field.NewFloat64(table, "hash_balance_bet_amount")
	x.HashBalanceWinAmount = field.NewFloat64(table, "hash_balance_win_amount")
	x.HashBalanceBetCount = field.NewInt32(table, "hash_balance_bet_count")
	x.HashBalanceWinCount = field.NewInt32(table, "hash_balance_win_count")
	x.HashBalanceFirstBet = field.NewFloat64(table, "hash_balance_first_bet")
	x.HashTransferTrxBetAmount = field.NewFloat64(table, "hash_transfer_trx_bet_amount")
	x.HashTransferTrxWinAmount = field.NewFloat64(table, "hash_transfer_trx_win_amount")
	x.HashTransferTrxBetCount = field.NewInt32(table, "hash_transfer_trx_bet_count")
	x.HashTransferTrxWinCount = field.NewInt32(table, "hash_transfer_trx_win_count")
	x.HashTransferTrxFirstBet = field.NewFloat64(table, "hash_transfer_trx_first_bet")
	x.HashTransferUsdtBetAmount = field.NewFloat64(table, "hash_transfer_usdt_bet_amount")
	x.HashTransferUsdtWinAmount = field.NewFloat64(table, "hash_transfer_usdt_win_amount")
	x.HashTransferUsdtBetCount = field.NewInt32(table, "hash_transfer_usdt_bet_count")
	x.HashTransferUsdtWinCount = field.NewInt32(table, "hash_transfer_usdt_win_count")
	x.HashTransferUsdtFirstBet = field.NewFloat64(table, "hash_transfer_usdt_first_bet")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotReportImportFlow) WithContext(ctx context.Context) *xRobotReportImportFlowDo {
	return x.xRobotReportImportFlowDo.WithContext(ctx)
}

func (x xRobotReportImportFlow) TableName() string { return x.xRobotReportImportFlowDo.TableName() }

func (x xRobotReportImportFlow) Alias() string { return x.xRobotReportImportFlowDo.Alias() }

func (x xRobotReportImportFlow) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotReportImportFlowDo.Columns(cols...)
}

func (x *xRobotReportImportFlow) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotReportImportFlow) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 36)
	x.fieldMap["id"] = x.ID
	x.fieldMap["date_time"] = x.DateTime
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["user_id"] = x.UserID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["banding_trx_address"] = x.BandingTrxAddress
	x.fieldMap["is_indb"] = x.IsIndb
	x.fieldMap["start_cnt"] = x.StartCnt
	x.fieldMap["gift_usdt_status"] = x.GiftUsdtStatus
	x.fieldMap["gift_trx_status"] = x.GiftTrxStatus
	x.fieldMap["gift_usdt_amount"] = x.GiftUsdtAmount
	x.fieldMap["gift_trx_amount"] = x.GiftTrxAmount
	x.fieldMap["recharge_cnt"] = x.RechargeCnt
	x.fieldMap["recharge_amount"] = x.RechargeAmount
	x.fieldMap["withdraw_cnt"] = x.WithdrawCnt
	x.fieldMap["withdraw_amount"] = x.WithdrawAmount
	x.fieldMap["first_recharge_amount"] = x.FirstRechargeAmount
	x.fieldMap["first_recharge_time"] = x.FirstRechargeTime
	x.fieldMap["hash_balance_bet_amount"] = x.HashBalanceBetAmount
	x.fieldMap["hash_balance_win_amount"] = x.HashBalanceWinAmount
	x.fieldMap["hash_balance_bet_count"] = x.HashBalanceBetCount
	x.fieldMap["hash_balance_win_count"] = x.HashBalanceWinCount
	x.fieldMap["hash_balance_first_bet"] = x.HashBalanceFirstBet
	x.fieldMap["hash_transfer_trx_bet_amount"] = x.HashTransferTrxBetAmount
	x.fieldMap["hash_transfer_trx_win_amount"] = x.HashTransferTrxWinAmount
	x.fieldMap["hash_transfer_trx_bet_count"] = x.HashTransferTrxBetCount
	x.fieldMap["hash_transfer_trx_win_count"] = x.HashTransferTrxWinCount
	x.fieldMap["hash_transfer_trx_first_bet"] = x.HashTransferTrxFirstBet
	x.fieldMap["hash_transfer_usdt_bet_amount"] = x.HashTransferUsdtBetAmount
	x.fieldMap["hash_transfer_usdt_win_amount"] = x.HashTransferUsdtWinAmount
	x.fieldMap["hash_transfer_usdt_bet_count"] = x.HashTransferUsdtBetCount
	x.fieldMap["hash_transfer_usdt_win_count"] = x.HashTransferUsdtWinCount
	x.fieldMap["hash_transfer_usdt_first_bet"] = x.HashTransferUsdtFirstBet
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotReportImportFlow) clone(db *gorm.DB) xRobotReportImportFlow {
	x.xRobotReportImportFlowDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotReportImportFlow) replaceDB(db *gorm.DB) xRobotReportImportFlow {
	x.xRobotReportImportFlowDo.ReplaceDB(db)
	return x
}

type xRobotReportImportFlowDo struct{ gen.DO }

func (x xRobotReportImportFlowDo) Debug() *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotReportImportFlowDo) WithContext(ctx context.Context) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotReportImportFlowDo) ReadDB() *xRobotReportImportFlowDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotReportImportFlowDo) WriteDB() *xRobotReportImportFlowDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotReportImportFlowDo) Session(config *gorm.Session) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotReportImportFlowDo) Clauses(conds ...clause.Expression) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotReportImportFlowDo) Returning(value interface{}, columns ...string) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotReportImportFlowDo) Not(conds ...gen.Condition) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotReportImportFlowDo) Or(conds ...gen.Condition) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotReportImportFlowDo) Select(conds ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotReportImportFlowDo) Where(conds ...gen.Condition) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotReportImportFlowDo) Order(conds ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotReportImportFlowDo) Distinct(cols ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotReportImportFlowDo) Omit(cols ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotReportImportFlowDo) Join(table schema.Tabler, on ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotReportImportFlowDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotReportImportFlowDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotReportImportFlowDo) Group(cols ...field.Expr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotReportImportFlowDo) Having(conds ...gen.Condition) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotReportImportFlowDo) Limit(limit int) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotReportImportFlowDo) Offset(offset int) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotReportImportFlowDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotReportImportFlowDo) Unscoped() *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotReportImportFlowDo) Create(values ...*model.XRobotReportImportFlow) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotReportImportFlowDo) CreateInBatches(values []*model.XRobotReportImportFlow, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotReportImportFlowDo) Save(values ...*model.XRobotReportImportFlow) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotReportImportFlowDo) First() (*model.XRobotReportImportFlow, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlow), nil
	}
}

func (x xRobotReportImportFlowDo) Take() (*model.XRobotReportImportFlow, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlow), nil
	}
}

func (x xRobotReportImportFlowDo) Last() (*model.XRobotReportImportFlow, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlow), nil
	}
}

func (x xRobotReportImportFlowDo) Find() ([]*model.XRobotReportImportFlow, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotReportImportFlow), err
}

func (x xRobotReportImportFlowDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotReportImportFlow, err error) {
	buf := make([]*model.XRobotReportImportFlow, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotReportImportFlowDo) FindInBatches(result *[]*model.XRobotReportImportFlow, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotReportImportFlowDo) Attrs(attrs ...field.AssignExpr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotReportImportFlowDo) Assign(attrs ...field.AssignExpr) *xRobotReportImportFlowDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotReportImportFlowDo) Joins(fields ...field.RelationField) *xRobotReportImportFlowDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotReportImportFlowDo) Preload(fields ...field.RelationField) *xRobotReportImportFlowDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotReportImportFlowDo) FirstOrInit() (*model.XRobotReportImportFlow, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlow), nil
	}
}

func (x xRobotReportImportFlowDo) FirstOrCreate() (*model.XRobotReportImportFlow, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlow), nil
	}
}

func (x xRobotReportImportFlowDo) FindByPage(offset int, limit int) (result []*model.XRobotReportImportFlow, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotReportImportFlowDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotReportImportFlowDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotReportImportFlowDo) Delete(models ...*model.XRobotReportImportFlow) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotReportImportFlowDo) withDO(do gen.Dao) *xRobotReportImportFlowDo {
	x.DO = *do.(*gen.DO)
	return x
}
