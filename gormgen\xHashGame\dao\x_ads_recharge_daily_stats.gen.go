// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsRechargeDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsRechargeDailyStat {
	_xAdsRechargeDailyStat := xAdsRechargeDailyStat{}

	_xAdsRechargeDailyStat.xAdsRechargeDailyStatDo.UseDB(db, opts...)
	_xAdsRechargeDailyStat.xAdsRechargeDailyStatDo.UseModel(&model.XAdsRechargeDailyStat{})

	tableName := _xAdsRechargeDailyStat.xAdsRechargeDailyStatDo.TableName()
	_xAdsRechargeDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsRechargeDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsRechargeDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsRechargeDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsRechargeDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsRechargeDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsRechargeDailyStat.RechargeType = field.NewInt32(tableName, "recharge_type")
	_xAdsRechargeDailyStat.RechargeChannel = field.NewString(tableName, "recharge_channel")
	_xAdsRechargeDailyStat.RechargeCountPc = field.NewInt32(tableName, "recharge_count_pc")
	_xAdsRechargeDailyStat.RechargeCountH5 = field.NewInt32(tableName, "recharge_count_h5")
	_xAdsRechargeDailyStat.RechargeCountSuccessPc = field.NewInt32(tableName, "recharge_count_success_pc")
	_xAdsRechargeDailyStat.RechargeCountSuccessH5 = field.NewInt32(tableName, "recharge_count_success_h5")
	_xAdsRechargeDailyStat.RechargeCount100Pc = field.NewInt32(tableName, "recharge_count_100_pc")
	_xAdsRechargeDailyStat.RechargeCount100H5 = field.NewInt32(tableName, "recharge_count_100_h5")
	_xAdsRechargeDailyStat.RechargeCount100500Pc = field.NewInt32(tableName, "recharge_count_100_500_pc")
	_xAdsRechargeDailyStat.RechargeCount100500H5 = field.NewInt32(tableName, "recharge_count_100_500_h5")
	_xAdsRechargeDailyStat.RechargeCount5001000Pc = field.NewInt32(tableName, "recharge_count_500_1000_pc")
	_xAdsRechargeDailyStat.RechargeCount5001000H5 = field.NewInt32(tableName, "recharge_count_500_1000_h5")
	_xAdsRechargeDailyStat.RechargeCount1000Pc = field.NewInt32(tableName, "recharge_count_1000_pc")
	_xAdsRechargeDailyStat.RechargeCount1000H5 = field.NewInt32(tableName, "recharge_count_1000_h5")
	_xAdsRechargeDailyStat.RechargeRatePc = field.NewFloat32(tableName, "recharge_rate_pc")
	_xAdsRechargeDailyStat.RechargeRateH5 = field.NewFloat32(tableName, "recharge_rate_h5")
	_xAdsRechargeDailyStat.RechargeSuccessRatePc = field.NewFloat32(tableName, "recharge_success_rate_pc")
	_xAdsRechargeDailyStat.RechargeSuccessRateH5 = field.NewFloat32(tableName, "recharge_success_rate_h5")
	_xAdsRechargeDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsRechargeDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsRechargeDailyStat.fillFieldMap()

	return _xAdsRechargeDailyStat
}

// xAdsRechargeDailyStat 充值偏好每日统计表
type xAdsRechargeDailyStat struct {
	xAdsRechargeDailyStatDo xAdsRechargeDailyStatDo

	ALL                    field.Asterisk
	ID                     field.Int64   // 主键ID
	SellerID               field.Int32   // 运营商ID
	ChannelID              field.Int32   // 渠道ID
	TopAgentID             field.Int64   // 顶级代理ID
	StatDate               field.Time    // 统计日期
	RechargeType           field.Int32   // 充值类型: 1:法币 2:加密货币
	RechargeChannel        field.String  // 充值渠道
	RechargeCountPc        field.Int32   // 充值次数pc
	RechargeCountH5        field.Int32   // 充值次数h5
	RechargeCountSuccessPc field.Int32   // 充值成功次数pc
	RechargeCountSuccessH5 field.Int32   // 充值成功次数h5
	RechargeCount100Pc     field.Int32   // 充值≤100 U次数 pc
	RechargeCount100H5     field.Int32   // 充值≤100 U次数 h5
	RechargeCount100500Pc  field.Int32   // 充值100-500 U次数 pc
	RechargeCount100500H5  field.Int32   // 充值100-500 U次数 h5
	RechargeCount5001000Pc field.Int32   // 充值500-1000 U次数 pc
	RechargeCount5001000H5 field.Int32   // 充值500-1000 U次数 h5
	RechargeCount1000Pc    field.Int32   // 充值＞1000 U次数 pc
	RechargeCount1000H5    field.Int32   // 充值＞1000  U次数 h5
	RechargeRatePc         field.Float32 // 充值占比pc
	RechargeRateH5         field.Float32 // 充值占比h5
	RechargeSuccessRatePc  field.Float32 // 充值成功率 pc
	RechargeSuccessRateH5  field.Float32 // 充值成功率 h5
	CreateTime             field.Time    // 创建时间
	UpdateTime             field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsRechargeDailyStat) Table(newTableName string) *xAdsRechargeDailyStat {
	x.xAdsRechargeDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsRechargeDailyStat) As(alias string) *xAdsRechargeDailyStat {
	x.xAdsRechargeDailyStatDo.DO = *(x.xAdsRechargeDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsRechargeDailyStat) updateTableName(table string) *xAdsRechargeDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.StatDate = field.NewTime(table, "stat_date")
	x.RechargeType = field.NewInt32(table, "recharge_type")
	x.RechargeChannel = field.NewString(table, "recharge_channel")
	x.RechargeCountPc = field.NewInt32(table, "recharge_count_pc")
	x.RechargeCountH5 = field.NewInt32(table, "recharge_count_h5")
	x.RechargeCountSuccessPc = field.NewInt32(table, "recharge_count_success_pc")
	x.RechargeCountSuccessH5 = field.NewInt32(table, "recharge_count_success_h5")
	x.RechargeCount100Pc = field.NewInt32(table, "recharge_count_100_pc")
	x.RechargeCount100H5 = field.NewInt32(table, "recharge_count_100_h5")
	x.RechargeCount100500Pc = field.NewInt32(table, "recharge_count_100_500_pc")
	x.RechargeCount100500H5 = field.NewInt32(table, "recharge_count_100_500_h5")
	x.RechargeCount5001000Pc = field.NewInt32(table, "recharge_count_500_1000_pc")
	x.RechargeCount5001000H5 = field.NewInt32(table, "recharge_count_500_1000_h5")
	x.RechargeCount1000Pc = field.NewInt32(table, "recharge_count_1000_pc")
	x.RechargeCount1000H5 = field.NewInt32(table, "recharge_count_1000_h5")
	x.RechargeRatePc = field.NewFloat32(table, "recharge_rate_pc")
	x.RechargeRateH5 = field.NewFloat32(table, "recharge_rate_h5")
	x.RechargeSuccessRatePc = field.NewFloat32(table, "recharge_success_rate_pc")
	x.RechargeSuccessRateH5 = field.NewFloat32(table, "recharge_success_rate_h5")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsRechargeDailyStat) WithContext(ctx context.Context) *xAdsRechargeDailyStatDo {
	return x.xAdsRechargeDailyStatDo.WithContext(ctx)
}

func (x xAdsRechargeDailyStat) TableName() string { return x.xAdsRechargeDailyStatDo.TableName() }

func (x xAdsRechargeDailyStat) Alias() string { return x.xAdsRechargeDailyStatDo.Alias() }

func (x xAdsRechargeDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsRechargeDailyStatDo.Columns(cols...)
}

func (x *xAdsRechargeDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsRechargeDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 25)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["recharge_type"] = x.RechargeType
	x.fieldMap["recharge_channel"] = x.RechargeChannel
	x.fieldMap["recharge_count_pc"] = x.RechargeCountPc
	x.fieldMap["recharge_count_h5"] = x.RechargeCountH5
	x.fieldMap["recharge_count_success_pc"] = x.RechargeCountSuccessPc
	x.fieldMap["recharge_count_success_h5"] = x.RechargeCountSuccessH5
	x.fieldMap["recharge_count_100_pc"] = x.RechargeCount100Pc
	x.fieldMap["recharge_count_100_h5"] = x.RechargeCount100H5
	x.fieldMap["recharge_count_100_500_pc"] = x.RechargeCount100500Pc
	x.fieldMap["recharge_count_100_500_h5"] = x.RechargeCount100500H5
	x.fieldMap["recharge_count_500_1000_pc"] = x.RechargeCount5001000Pc
	x.fieldMap["recharge_count_500_1000_h5"] = x.RechargeCount5001000H5
	x.fieldMap["recharge_count_1000_pc"] = x.RechargeCount1000Pc
	x.fieldMap["recharge_count_1000_h5"] = x.RechargeCount1000H5
	x.fieldMap["recharge_rate_pc"] = x.RechargeRatePc
	x.fieldMap["recharge_rate_h5"] = x.RechargeRateH5
	x.fieldMap["recharge_success_rate_pc"] = x.RechargeSuccessRatePc
	x.fieldMap["recharge_success_rate_h5"] = x.RechargeSuccessRateH5
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsRechargeDailyStat) clone(db *gorm.DB) xAdsRechargeDailyStat {
	x.xAdsRechargeDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsRechargeDailyStat) replaceDB(db *gorm.DB) xAdsRechargeDailyStat {
	x.xAdsRechargeDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsRechargeDailyStatDo struct{ gen.DO }

func (x xAdsRechargeDailyStatDo) Debug() *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsRechargeDailyStatDo) WithContext(ctx context.Context) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsRechargeDailyStatDo) ReadDB() *xAdsRechargeDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsRechargeDailyStatDo) WriteDB() *xAdsRechargeDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsRechargeDailyStatDo) Session(config *gorm.Session) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsRechargeDailyStatDo) Clauses(conds ...clause.Expression) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsRechargeDailyStatDo) Returning(value interface{}, columns ...string) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsRechargeDailyStatDo) Not(conds ...gen.Condition) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsRechargeDailyStatDo) Or(conds ...gen.Condition) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsRechargeDailyStatDo) Select(conds ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsRechargeDailyStatDo) Where(conds ...gen.Condition) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsRechargeDailyStatDo) Order(conds ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsRechargeDailyStatDo) Distinct(cols ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsRechargeDailyStatDo) Omit(cols ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsRechargeDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsRechargeDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsRechargeDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsRechargeDailyStatDo) Group(cols ...field.Expr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsRechargeDailyStatDo) Having(conds ...gen.Condition) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsRechargeDailyStatDo) Limit(limit int) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsRechargeDailyStatDo) Offset(offset int) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsRechargeDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsRechargeDailyStatDo) Unscoped() *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsRechargeDailyStatDo) Create(values ...*model.XAdsRechargeDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsRechargeDailyStatDo) CreateInBatches(values []*model.XAdsRechargeDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsRechargeDailyStatDo) Save(values ...*model.XAdsRechargeDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsRechargeDailyStatDo) First() (*model.XAdsRechargeDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsRechargeDailyStat), nil
	}
}

func (x xAdsRechargeDailyStatDo) Take() (*model.XAdsRechargeDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsRechargeDailyStat), nil
	}
}

func (x xAdsRechargeDailyStatDo) Last() (*model.XAdsRechargeDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsRechargeDailyStat), nil
	}
}

func (x xAdsRechargeDailyStatDo) Find() ([]*model.XAdsRechargeDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsRechargeDailyStat), err
}

func (x xAdsRechargeDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsRechargeDailyStat, err error) {
	buf := make([]*model.XAdsRechargeDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsRechargeDailyStatDo) FindInBatches(result *[]*model.XAdsRechargeDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsRechargeDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsRechargeDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsRechargeDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsRechargeDailyStatDo) Joins(fields ...field.RelationField) *xAdsRechargeDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsRechargeDailyStatDo) Preload(fields ...field.RelationField) *xAdsRechargeDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsRechargeDailyStatDo) FirstOrInit() (*model.XAdsRechargeDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsRechargeDailyStat), nil
	}
}

func (x xAdsRechargeDailyStatDo) FirstOrCreate() (*model.XAdsRechargeDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsRechargeDailyStat), nil
	}
}

func (x xAdsRechargeDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsRechargeDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsRechargeDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsRechargeDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsRechargeDailyStatDo) Delete(models ...*model.XAdsRechargeDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsRechargeDailyStatDo) withDO(do gen.Dao) *xAdsRechargeDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
