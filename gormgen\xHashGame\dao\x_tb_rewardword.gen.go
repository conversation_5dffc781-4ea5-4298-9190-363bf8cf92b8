// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTbRewardword(db *gorm.DB, opts ...gen.DOOption) xTbRewardword {
	_xTbRewardword := xTbRewardword{}

	_xTbRewardword.xTbRewardwordDo.UseDB(db, opts...)
	_xTbRewardword.xTbRewardwordDo.UseModel(&model.XTbRewardword{})

	tableName := _xTbRewardword.xTbRewardwordDo.TableName()
	_xTbRewardword.ALL = field.NewAsterisk(tableName)
	_xTbRewardword.ID = field.NewInt32(tableName, "Id")
	_xTbRewardword.Content = field.NewString(tableName, "Content")
	_xTbRewardword.RewardType = field.NewInt32(tableName, "RewardType")
	_xTbRewardword.Status = field.NewInt32(tableName, "Status")
	_xTbRewardword.Memo = field.NewString(tableName, "Memo")
	_xTbRewardword.Operator = field.NewString(tableName, "Operator")
	_xTbRewardword.OperUserID = field.NewInt32(tableName, "OperUserID")
	_xTbRewardword.DeviceType = field.NewInt32(tableName, "DeviceType")
	_xTbRewardword.DeviceID = field.NewString(tableName, "DeviceID")
	_xTbRewardword.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTbRewardword.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTbRewardword.fillFieldMap()

	return _xTbRewardword
}

// xTbRewardword 激励文字
type xTbRewardword struct {
	xTbRewardwordDo xTbRewardwordDo

	ALL        field.Asterisk
	ID         field.Int32
	Content    field.String // 内容
	RewardType field.Int32  // 激励分类 1中奖(系统) 2未中奖(系统)  11中奖(自定义)  21未中奖(自定义)
	Status     field.Int32  // 0无效 1有效
	Memo       field.String // 描述
	Operator   field.String // 操作员
	OperUserID field.Int32  // 操作员ID
	DeviceType field.Int32  // 设备类型
	DeviceID   field.String // 设备ID
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTbRewardword) Table(newTableName string) *xTbRewardword {
	x.xTbRewardwordDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTbRewardword) As(alias string) *xTbRewardword {
	x.xTbRewardwordDo.DO = *(x.xTbRewardwordDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTbRewardword) updateTableName(table string) *xTbRewardword {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Content = field.NewString(table, "Content")
	x.RewardType = field.NewInt32(table, "RewardType")
	x.Status = field.NewInt32(table, "Status")
	x.Memo = field.NewString(table, "Memo")
	x.Operator = field.NewString(table, "Operator")
	x.OperUserID = field.NewInt32(table, "OperUserID")
	x.DeviceType = field.NewInt32(table, "DeviceType")
	x.DeviceID = field.NewString(table, "DeviceID")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTbRewardword) WithContext(ctx context.Context) *xTbRewardwordDo {
	return x.xTbRewardwordDo.WithContext(ctx)
}

func (x xTbRewardword) TableName() string { return x.xTbRewardwordDo.TableName() }

func (x xTbRewardword) Alias() string { return x.xTbRewardwordDo.Alias() }

func (x xTbRewardword) Columns(cols ...field.Expr) gen.Columns {
	return x.xTbRewardwordDo.Columns(cols...)
}

func (x *xTbRewardword) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTbRewardword) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Content"] = x.Content
	x.fieldMap["RewardType"] = x.RewardType
	x.fieldMap["Status"] = x.Status
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Operator"] = x.Operator
	x.fieldMap["OperUserID"] = x.OperUserID
	x.fieldMap["DeviceType"] = x.DeviceType
	x.fieldMap["DeviceID"] = x.DeviceID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTbRewardword) clone(db *gorm.DB) xTbRewardword {
	x.xTbRewardwordDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTbRewardword) replaceDB(db *gorm.DB) xTbRewardword {
	x.xTbRewardwordDo.ReplaceDB(db)
	return x
}

type xTbRewardwordDo struct{ gen.DO }

func (x xTbRewardwordDo) Debug() *xTbRewardwordDo {
	return x.withDO(x.DO.Debug())
}

func (x xTbRewardwordDo) WithContext(ctx context.Context) *xTbRewardwordDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTbRewardwordDo) ReadDB() *xTbRewardwordDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTbRewardwordDo) WriteDB() *xTbRewardwordDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTbRewardwordDo) Session(config *gorm.Session) *xTbRewardwordDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTbRewardwordDo) Clauses(conds ...clause.Expression) *xTbRewardwordDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTbRewardwordDo) Returning(value interface{}, columns ...string) *xTbRewardwordDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTbRewardwordDo) Not(conds ...gen.Condition) *xTbRewardwordDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTbRewardwordDo) Or(conds ...gen.Condition) *xTbRewardwordDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTbRewardwordDo) Select(conds ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTbRewardwordDo) Where(conds ...gen.Condition) *xTbRewardwordDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTbRewardwordDo) Order(conds ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTbRewardwordDo) Distinct(cols ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTbRewardwordDo) Omit(cols ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTbRewardwordDo) Join(table schema.Tabler, on ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTbRewardwordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTbRewardwordDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTbRewardwordDo) Group(cols ...field.Expr) *xTbRewardwordDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTbRewardwordDo) Having(conds ...gen.Condition) *xTbRewardwordDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTbRewardwordDo) Limit(limit int) *xTbRewardwordDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTbRewardwordDo) Offset(offset int) *xTbRewardwordDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTbRewardwordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTbRewardwordDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTbRewardwordDo) Unscoped() *xTbRewardwordDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTbRewardwordDo) Create(values ...*model.XTbRewardword) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTbRewardwordDo) CreateInBatches(values []*model.XTbRewardword, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTbRewardwordDo) Save(values ...*model.XTbRewardword) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTbRewardwordDo) First() (*model.XTbRewardword, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbRewardword), nil
	}
}

func (x xTbRewardwordDo) Take() (*model.XTbRewardword, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbRewardword), nil
	}
}

func (x xTbRewardwordDo) Last() (*model.XTbRewardword, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbRewardword), nil
	}
}

func (x xTbRewardwordDo) Find() ([]*model.XTbRewardword, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTbRewardword), err
}

func (x xTbRewardwordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTbRewardword, err error) {
	buf := make([]*model.XTbRewardword, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTbRewardwordDo) FindInBatches(result *[]*model.XTbRewardword, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTbRewardwordDo) Attrs(attrs ...field.AssignExpr) *xTbRewardwordDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTbRewardwordDo) Assign(attrs ...field.AssignExpr) *xTbRewardwordDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTbRewardwordDo) Joins(fields ...field.RelationField) *xTbRewardwordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTbRewardwordDo) Preload(fields ...field.RelationField) *xTbRewardwordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTbRewardwordDo) FirstOrInit() (*model.XTbRewardword, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbRewardword), nil
	}
}

func (x xTbRewardwordDo) FirstOrCreate() (*model.XTbRewardword, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbRewardword), nil
	}
}

func (x xTbRewardwordDo) FindByPage(offset int, limit int) (result []*model.XTbRewardword, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTbRewardwordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTbRewardwordDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTbRewardwordDo) Delete(models ...*model.XTbRewardword) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTbRewardwordDo) withDO(do gen.Dao) *xTbRewardwordDo {
	x.DO = *do.(*gen.DO)
	return x
}
