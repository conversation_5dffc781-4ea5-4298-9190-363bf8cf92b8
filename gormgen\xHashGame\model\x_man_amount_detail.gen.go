// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXManAmountDetail = "x_man_amount_detail"

// XManAmountDetail 金额扣减表
type XManAmountDetail struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID     int32     `gorm:"column:UserId;comment:用户Id" json:"UserId"`                                                    // 用户Id
	AmountType int32     `gorm:"column:AmountType;not null;default:1;comment:余额分类 1=真金，2=Bonus币" json:"AmountType"`           // 余额分类 1=真金，2=Bonus币
	SType      string    `gorm:"column:SType;comment:金额扣减名称" json:"SType"`                                                    // 金额扣减名称
	Symbol     string    `gorm:"column:Symbol;comment:货币类型" json:"Symbol"`                                                    // 货币类型
	Amount     float64   `gorm:"column:Amount;comment:备注" json:"Amount"`                                                      // 备注
	MinLiuShui float64   `gorm:"column:MinLiuShui;default:0.000000;comment:提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比" json:"MinLiuShui"` // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	CSGroup    string    `gorm:"column:CSGroup" json:"CSGroup"`
	CSID       string    `gorm:"column:CSId" json:"CSId"`
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	TopAgentID int32     `gorm:"column:TopAgentId" json:"TopAgentId"`
	Memo       string    `gorm:"column:Memo;comment:备注" json:"Memo"`       // 备注
	Account    string    `gorm:"column:Account;comment:账号" json:"Account"` // 账号
}

// TableName XManAmountDetail's table name
func (*XManAmountDetail) TableName() string {
	return TableNameXManAmountDetail
}
