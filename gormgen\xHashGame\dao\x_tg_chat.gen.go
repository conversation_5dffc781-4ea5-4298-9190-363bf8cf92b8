// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgChat(db *gorm.DB, opts ...gen.DOOption) xTgChat {
	_xTgChat := xTgChat{}

	_xTgChat.xTgChatDo.UseDB(db, opts...)
	_xTgChat.xTgChatDo.UseModel(&model.XTgChat{})

	tableName := _xTgChat.xTgChatDo.TableName()
	_xTgChat.ALL = field.NewAsterisk(tableName)
	_xTgChat.ID = field.NewInt32(tableName, "Id")
	_xTgChat.Type = field.NewString(tableName, "Type")
	_xTgChat.GuideID = field.NewInt64(tableName, "GuideId")
	_xTgChat.TgRobotID = field.NewInt64(tableName, "TgRobotId")
	_xTgChat.ChatID = field.NewInt64(tableName, "ChatId")
	_xTgChat.TgID = field.NewInt64(tableName, "TgId")
	_xTgChat.Lang = field.NewString(tableName, "Lang")
	_xTgChat.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgChat.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTgChat.fillFieldMap()

	return _xTgChat
}

// xTgChat Tg聊天室
type xTgChat struct {
	xTgChatDo xTgChatDo

	ALL        field.Asterisk
	ID         field.Int32
	Type       field.String // 聊天室类型 private: 私人 group:普通群组 supergroup:超级群组 channel:频道
	GuideID    field.Int64  // 接待机器人ID
	TgRobotID  field.Int64  // Tg机器人ID
	ChatID     field.Int64  // 聊天室ID
	TgID       field.Int64  // Tg用户/群组ID
	Lang       field.String // 语言
	CreateTime field.Time
	UpdateTime field.Time

	fieldMap map[string]field.Expr
}

func (x xTgChat) Table(newTableName string) *xTgChat {
	x.xTgChatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgChat) As(alias string) *xTgChat {
	x.xTgChatDo.DO = *(x.xTgChatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgChat) updateTableName(table string) *xTgChat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Type = field.NewString(table, "Type")
	x.GuideID = field.NewInt64(table, "GuideId")
	x.TgRobotID = field.NewInt64(table, "TgRobotId")
	x.ChatID = field.NewInt64(table, "ChatId")
	x.TgID = field.NewInt64(table, "TgId")
	x.Lang = field.NewString(table, "Lang")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTgChat) WithContext(ctx context.Context) *xTgChatDo { return x.xTgChatDo.WithContext(ctx) }

func (x xTgChat) TableName() string { return x.xTgChatDo.TableName() }

func (x xTgChat) Alias() string { return x.xTgChatDo.Alias() }

func (x xTgChat) Columns(cols ...field.Expr) gen.Columns { return x.xTgChatDo.Columns(cols...) }

func (x *xTgChat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgChat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Type"] = x.Type
	x.fieldMap["GuideId"] = x.GuideID
	x.fieldMap["TgRobotId"] = x.TgRobotID
	x.fieldMap["ChatId"] = x.ChatID
	x.fieldMap["TgId"] = x.TgID
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTgChat) clone(db *gorm.DB) xTgChat {
	x.xTgChatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgChat) replaceDB(db *gorm.DB) xTgChat {
	x.xTgChatDo.ReplaceDB(db)
	return x
}

type xTgChatDo struct{ gen.DO }

func (x xTgChatDo) Debug() *xTgChatDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgChatDo) WithContext(ctx context.Context) *xTgChatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgChatDo) ReadDB() *xTgChatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgChatDo) WriteDB() *xTgChatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgChatDo) Session(config *gorm.Session) *xTgChatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgChatDo) Clauses(conds ...clause.Expression) *xTgChatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgChatDo) Returning(value interface{}, columns ...string) *xTgChatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgChatDo) Not(conds ...gen.Condition) *xTgChatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgChatDo) Or(conds ...gen.Condition) *xTgChatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgChatDo) Select(conds ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgChatDo) Where(conds ...gen.Condition) *xTgChatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgChatDo) Order(conds ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgChatDo) Distinct(cols ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgChatDo) Omit(cols ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgChatDo) Join(table schema.Tabler, on ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgChatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgChatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgChatDo) Group(cols ...field.Expr) *xTgChatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgChatDo) Having(conds ...gen.Condition) *xTgChatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgChatDo) Limit(limit int) *xTgChatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgChatDo) Offset(offset int) *xTgChatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgChatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgChatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgChatDo) Unscoped() *xTgChatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgChatDo) Create(values ...*model.XTgChat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgChatDo) CreateInBatches(values []*model.XTgChat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgChatDo) Save(values ...*model.XTgChat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgChatDo) First() (*model.XTgChat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgChat), nil
	}
}

func (x xTgChatDo) Take() (*model.XTgChat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgChat), nil
	}
}

func (x xTgChatDo) Last() (*model.XTgChat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgChat), nil
	}
}

func (x xTgChatDo) Find() ([]*model.XTgChat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgChat), err
}

func (x xTgChatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgChat, err error) {
	buf := make([]*model.XTgChat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgChatDo) FindInBatches(result *[]*model.XTgChat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgChatDo) Attrs(attrs ...field.AssignExpr) *xTgChatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgChatDo) Assign(attrs ...field.AssignExpr) *xTgChatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgChatDo) Joins(fields ...field.RelationField) *xTgChatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgChatDo) Preload(fields ...field.RelationField) *xTgChatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgChatDo) FirstOrInit() (*model.XTgChat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgChat), nil
	}
}

func (x xTgChatDo) FirstOrCreate() (*model.XTgChat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgChat), nil
	}
}

func (x xTgChatDo) FindByPage(offset int, limit int) (result []*model.XTgChat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgChatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgChatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgChatDo) Delete(models ...*model.XTgChat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgChatDo) withDO(do gen.Dao) *xTgChatDo {
	x.DO = *do.(*gen.DO)
	return x
}
