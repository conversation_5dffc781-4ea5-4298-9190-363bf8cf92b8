package controller

import (
	"errors"
	"fmt"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type BlackBlockController struct {
}

func (c *BlackBlockController) Init() {
	group := server.Http().NewGroup("/api/blackblock")
	{
		group.Post("/list", c.list)
		group.Post("/create", c.create)
		group.Post("/delete", c.delete)
		group.Post("/batchDelete", c.batchDelete)
	}
}

func (c *BlackBlockController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page       int
		PageSize   int
		Type       int
		Info       []string
		BlockMaker []string
	}

	errcode := 0
	reqdata := RequestData{Type: 1}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块黑名单", "查", "区块黑名单列表查询")
	if token == nil {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	xBlackBlock := server.DaoxHashGame().XBlackBlock
	db := xBlackBlock.WithContext(ctx.Gin())
	xUser := server.DaoxHashGame().XUser
	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	xGamePeriod := server.DaoxHashGame().XGamePeriod
	if len(reqdata.Info) > 0 {
		db = db.Where(xBlackBlock.Info.In(reqdata.Info...))
	}

	if len(reqdata.BlockMaker) > 0 {
		db = db.Where(xBlackBlock.BlockMaker.In(reqdata.BlockMaker...))
	}

	if reqdata.Type == 1 {
		type Result struct {
			model.XBlackBlock
			SellerName  string
			ChannelName string
			TopAgentId  int
			AgentId     int
			MakeTotal   int64
		}
		var list []*Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName, xChannel.ChannelName, xUser.TopAgentID, xUser.AgentID).
			LeftJoin(xUser, xUser.UserID.EqCol(xBlackBlock.Info)).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xUser.SellerID)).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xUser.ChannelID)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		for _, v := range list {
			blockmaker := strings.TrimSpace(v.BlockMaker)
			extraData := struct {
				MakeTotal int64
			}{}

			_ = xGamePeriod.WithContext(ctx.Gin()).Select(xGamePeriod.MakeTotal.As("MakeTotal")).Where(xGamePeriod.BlockMaker.Eq(blockmaker), xGamePeriod.MakeTotal.Gt(0)).Order(xGamePeriod.ID.Desc()).Limit(1).Scan(&extraData)
			v.MakeTotal = extraData.MakeTotal
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 2 {
		type Result struct {
			model.XBlackBlock
			MakeTotal int64
		}
		var list []*Result
		total, err := db.
			Select(xBlackBlock.ALL).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		for _, v := range list {
			blockmaker := strings.TrimSpace(v.BlockMaker)
			extraData := struct {
				MakeTotal int64
			}{}

			_ = xGamePeriod.WithContext(ctx.Gin()).Select(xGamePeriod.MakeTotal.As("MakeTotal")).Where(xGamePeriod.BlockMaker.Eq(blockmaker), xGamePeriod.MakeTotal.Gt(0)).Order(xGamePeriod.ID.Desc()).Limit(1).Scan(&extraData)
			v.MakeTotal = extraData.MakeTotal
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 3 {
		type Result struct {
			model.XBlackBlock
			SellerName  string
			ChannelName string
			TopAgentId  int
			AgentId     int
			MakeTotal   int64
		}
		var list []*Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName, xChannel.ChannelName, xUser.TopAgentID, xUser.AgentID).
			LeftJoin(xUser, xUser.UserID.EqCol(xBlackBlock.Info)).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xUser.SellerID)).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xUser.ChannelID)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		for _, v := range list {
			blockmaker := strings.TrimSpace(v.BlockMaker)
			extraData := struct {
				MakeTotal int64
			}{}

			_ = xGamePeriod.WithContext(ctx.Gin()).Select(xGamePeriod.MakeTotal.As("MakeTotal")).Where(xGamePeriod.BlockMaker.Eq(blockmaker), xGamePeriod.MakeTotal.Gt(0)).Order(xGamePeriod.ID.Desc()).Limit(1).Scan(&extraData)
			v.MakeTotal = extraData.MakeTotal
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 4 {
		type Result struct {
			model.XBlackBlock
			SellerName string
			MakeTotal  int64
		}
		var list []*Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xBlackBlock.Info)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		for _, v := range list {
			blockmaker := strings.TrimSpace(v.BlockMaker)
			extraData := struct {
				MakeTotal int64
			}{}

			_ = xGamePeriod.WithContext(ctx.Gin()).Select(xGamePeriod.MakeTotal.As("MakeTotal")).Where(xGamePeriod.BlockMaker.Eq(blockmaker), xGamePeriod.MakeTotal.Gt(0)).Order(xGamePeriod.ID.Desc()).Limit(1).Scan(&extraData)
			v.MakeTotal = extraData.MakeTotal
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 5 {
		type Result struct {
			model.XBlackBlock
			SellerName  string
			ChannelName string
			MakeTotal   int64
		}
		var list []*Result
		total, err := db.
			Select(xBlackBlock.ALL, xSeller.SellerName, xChannel.ChannelName).
			LeftJoin(xChannel, xChannel.ChannelID.EqCol(xBlackBlock.Info)).
			LeftJoin(xSeller, xSeller.SellerID.EqCol(xChannel.SellerID)).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		for _, v := range list {
			blockmaker := strings.TrimSpace(v.BlockMaker)
			extraData := struct {
				MakeTotal int64
			}{}

			_ = xGamePeriod.WithContext(ctx.Gin()).Select(xGamePeriod.MakeTotal.As("MakeTotal")).Where(xGamePeriod.BlockMaker.Eq(blockmaker), xGamePeriod.MakeTotal.Gt(0)).Order(xGamePeriod.ID.Desc()).Limit(1).Scan(&extraData)
			v.MakeTotal = extraData.MakeTotal
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	if reqdata.Type == 6 {
		type Result struct {
			model.XBlackBlock
			MakeTotal int64
		}
		var list []*Result
		total, err := db.
			Select(xBlackBlock.ALL).
			Where(xBlackBlock.Type.Eq(int32(reqdata.Type))).
			Order(xBlackBlock.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		for _, v := range list {
			blockmaker := strings.TrimSpace(v.BlockMaker)
			extraData := struct {
				MakeTotal int64
			}{}

			_ = xGamePeriod.WithContext(ctx.Gin()).Select(xGamePeriod.MakeTotal.As("MakeTotal")).Where(xGamePeriod.BlockMaker.Eq(blockmaker), xGamePeriod.MakeTotal.Gt(0)).Order(xGamePeriod.ID.Desc()).Limit(1).Scan(&extraData)
			v.MakeTotal = extraData.MakeTotal
		}

		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

}

func (c *BlackBlockController) create(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Type       int
		Info       string
		BlockMaker string
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块黑名单", "改", "区块黑名单添加")
	if token == nil {
		return
	}

	if strings.Contains(reqdata.Info, "，") {
		ctx.RespErr(errors.New("多个ID请使用英文逗号(,)隔开，不要使用中文逗号(，)"), &errcode)
		return
	}

	if strings.Contains(reqdata.BlockMaker, "，") {
		ctx.RespErr(errors.New("多个出快者请使用英文逗号(,)隔开，不要使用中文逗号(，)"), &errcode)
		return
	}

	blackInfos := strings.Split(reqdata.Info, ",")
	blockMakers := strings.Split(reqdata.BlockMaker, ",")

	uniqueBlockMakers := removeDuplicates(blockMakers)
	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		for _, blackInfo := range blackInfos {
			for _, blockMaker := range uniqueBlockMakers {
				_, err := tx.XBlackBlock.WithContext(ctx.Gin()).Where(tx.XBlackBlock.Info.Eq(blackInfo), tx.XBlackBlock.Type.Eq(int32(reqdata.Type)), tx.XBlackBlock.BlockMaker.Eq(strings.TrimSpace(blockMaker))).First()
				if err == nil {
					return errors.New(fmt.Sprintf("黑名单列表中已存%s-%s", blockMaker, blackInfo))
				}

				err = tx.XBlackBlock.WithContext(ctx.Gin()).Create(&model.XBlackBlock{
					BlockMaker: strings.TrimSpace(blockMaker),
					Info:       blackInfo,
					Type:       int32(reqdata.Type),
				})
				if err != nil {
					return err
				}
			}
		}
		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

func (c *BlackBlockController) delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块黑名单", "删", "区块黑名单删除")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XBlackBlock
	db := dao.WithContext(ctx.Gin())

	_, err := db.Where(dao.ID.Eq(int32(reqdata.Id))).Delete()
	if err != nil {
		return
	}

	ctx.RespOK()
}

func (c *BlackBlockController) batchDelete(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		Ids []int32
	}

	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "风控系统", "区块黑名单", "删", "区块黑名单删除")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XBlackBlock
	db := dao.WithContext(ctx.Gin())

	_, err := db.Where(dao.ID.In(reqdata.Ids...)).Delete()
	if err != nil {
		return
	}

	ctx.RespOK()
}

func removeDuplicates(elements []string) []string {
	// 使用map来记录元素是否已经存在
	encountered := map[string]bool{}
	// 用于保存去重后的元素
	result := []string{}

	for v := range elements {

		if encountered[strings.TrimSpace(elements[v])] == false {
			// 如果元素没有被记录，添加到结果切片
			encountered[strings.TrimSpace(elements[v])] = true
			result = append(result, strings.TrimSpace(elements[v]))
		}
	}
	// 返回去重后的结果切片
	return result
}
