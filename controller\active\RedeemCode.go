package active

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net"
	"regexp"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
)

// 兑换码活动ID列表
var RedeemCodeActivityIDs = []int32{
	int32(utils.RedeemCodeGift_1003), // 主要兑换码活动
	int32(utils.RedeemCodeGift_1015), // 兑换码活动1015
	int32(utils.RedeemCodeGift_1016), // 兑换码活动1016
	int32(utils.RedeemCodeGift_1017), // 兑换码活动1017
}

// 生成随机兑换码
func generateRedeemCode(length int, prefix string, suffix string, charset string) string {
	if charset == "" {
		charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	}

	// 将字符集分为三类：大写字母、小写字母和数字
	var upperChars, lowerChars, digitChars []rune
	for _, char := range charset {
		if char >= 'A' && char <= 'Z' {
			upperChars = append(upperChars, char)
		} else if char >= 'a' && char <= 'z' {
			lowerChars = append(lowerChars, char)
		} else if char >= '0' && char <= '9' {
			digitChars = append(digitChars, char)
		}
		// 忽略特殊符号
	}

	// 使用更新的随机数生成方法
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 确保至少包含一个大写字母、一个小写字母和一个数字
	code := make([]rune, length)

	// 如果长度小于3，无法包含所有类型的字符，则使用原始方法
	if length < 3 {
		for i := range code {
			code[i] = rune(charset[rng.Intn(len(charset))])
		}
	} else {
		// 确保至少包含一个大写字母
		if len(upperChars) > 0 {
			code[0] = upperChars[rng.Intn(len(upperChars))]
		}

		// 确保至少包含一个小写字母
		if len(lowerChars) > 0 {
			code[1] = lowerChars[rng.Intn(len(lowerChars))]
		}

		// 确保至少包含一个数字
		if len(digitChars) > 0 {
			code[2] = digitChars[rng.Intn(len(digitChars))]
		}

		// 填充剩余位置
		for i := 3; i < length; i++ {
			code[i] = rune(charset[rng.Intn(len(charset))])
		}

		// 打乱顺序，避免固定模式
		for i := length - 1; i > 0; i-- {
			j := rng.Intn(i + 1)
			code[i], code[j] = code[j], code[i]
		}
	}

	return prefix + string(code) + suffix
}

// RedeemCodeBaseConfig 兑换码活动会员参与条件限制
type RedeemCodeBaseConfig struct {
	RegisterDays         int      `json:"RegisterDays"`         // 注册多少天内的会员可兑换，留空或填0表示不限制
	IsBindEmail          bool     `json:"IsBindEmail"`          // 是否已绑定邮箱地址
	IsActiveWallet       bool     `json:"IsActiveWallet"`       // 是否有激活的钱包地址
	BlockedIPList        string   `json:"BlockedIPList"`        // 被限制参与的IP，多个用英文逗号隔开，如：127.0.0.1,*******
	SpecificPlayerIDs    string   `json:"SpecificPlayerIDs"`    // 指定玩家可领取，多个ID用英文逗号隔开，如：123,4553
	ApplicableGames      int      `json:"ApplicableGames"`      // 参与活动的场馆，0=全站通用，1=指定场馆
	GameTypes            []string `json:"GameTypes"`            // 指定游戏类型列表，仅当ApplicableGames=1时有效
	VIPLevels            []int32  `json:"VIPLevels"`            // VIP层级，可多选，如：[1,2,3,4,5]，空数组表示全选
	DailyFlowAmount      float64  `json:"DailyFlowAmount"`      // 当日流水达到
	HistoryDepositAmount float64  `json:"HistoryDepositAmount"` // 历史累积充值金额
	HistoryDepositCount  int      `json:"HistoryDepositCount"`  // 历史累积充值次数
	HistoryDepositType   string   `json:"HistoryDepositType"`   // 历史累积充值类型，"金额"或"次数"
}

// RedeemCodeItem 单个兑换码配置项
type RedeemCodeItem struct {
	RedeemCode      string  `json:"RedeemCode"`      // 兑换码，仅包含数字和字母（大小写），长度3-12位
	ExchangeCount   *int    `json:"ExchangeCount"`   // 此码可兑换次数，不填则不限次数
	MinRewardAmount float64 `json:"MinRewardAmount"` // 奖励金额下限
	MaxRewardAmount float64 `json:"MaxRewardAmount"` // 奖励金额上限
	ExchangeTimes   int     `json:"ExchangeTimes"`   // 已经兑换次数
}

// RedeemCodeConfig 兑换码相关配置
type RedeemCodeConfig struct {
	// 兑换码列表
	RedeemCodes []RedeemCodeItem `json:"RedeemCodes"` // 多个兑换码配置项

	// 奖励提取配置
	FlowMultiple float64 `json:"FlowMultiple"` // 提取奖励需打流水倍数
}

// RedeemCodeAwardConfig 兑换码彩金配置（嵌套在AwardData中）
type RedeemCodeAwardConfig struct {
	RedeemCodes  []RedeemCodeItem `json:"RedeemCodes"`  // 兑换码列表
	FlowMultiple float64          `json:"FlowMultiple"` // 提取奖励需打流水倍数
}

type RedeemCodeAwardData struct {
	AwardBetType int                   `json:"AwardBetType"` // 奖励打码倍数类型 1: 真金 2: 彩金 3: 彩金+真金
	AwardConfig  RedeemCodeAwardConfig `json:"AwardConfig"`  // 奖励配置，包含兑换码和流水倍数
	// 激活期限配置
	ActivationType           int   `json:"ActivationType"`           // 激活期限类型 1 无期限 2 倒计时 3 自定义
	ActivetionCountdownCount int   `json:"ActivetionCountdownCount"` // 激活期限，倒计时数值
	ActivetionCountdownType  int   `json:"ActivetionCountdownType"`  // 激活期限 倒计时类型 1 天 2 小时
	ActivetionDate           int64 `json:"ActivetionDate"`           // 激活期限 自定义结束时间(毫秒)

	// 流水完成期限配置
	TurnoverType           int   `json:"TurnoverType"`           // 流水完成期限类型 1 无期限 2 倒计时 3 自定义
	TurnoverCountdownCount int   `json:"TurnoverCountdownCount"` // 流水完成期限，倒计时数值
	TurnoverCountdownType  int   `json:"TurnoverCountdownType"`  // 流水完成期限 倒计时类型 1 天 2 小时
	TurnoverDate           int64 `json:"TurnoverDate"`           // 流水完成期限 自定义结束时间(毫秒)

	// 领取期限配置
	ReceiveType           int   `json:"ReceiveType"`           // 领取期限类型 1 无期限 2 倒计时 3 自定义
	ReceiveCountdownCount int   `json:"ReceiveCountdownCount"` // 领取期限，倒计时数值
	ReceiveCountdownType  int   `json:"ReceiveCountdownType"`  // 领取期限 倒计时类型 1 天 2 小时
	ReceiveDate           int64 `json:"ReceiveDate"`           // 领取期限 自定义结束时间(毫秒)

	// 投注限制配置
	MinBetAmount int `json:"MinBetAmount"` // 奖励单笔投注限制 最小值
	MaxBetAmount int `json:"MaxBetAmount"` // 奖励单笔投注限制 最大值

	// 游戏限制配置
	LimitGameType     int    `json:"LimitGameType"`     // 奖励投注游戏限制 1 不限制 2 限制
	AwardVenueCfg     string `json:"AwardVenueCfg"`     // LimitGameType==2时，可以计算打码的游戏配置，json字符串
	LimitGameIds      string `json:"LimitGameIds"`      // 被限制参与的游戏ID列表 (需要特殊限制的游戏ID，限制后这些游戏不参与活动打码)
	LimitMaxWinAmount int    `json:"LimitMaxWinAmount"` // 奖励投注最高盈利金额限制

	// 其他配置
	IsCalcAwardValid int `json:"IsCalcAwardValid"` // 奖励投注金额是否计入有效流水 1: 是 0: 否
}

// RedeemCodeParameterCheck 兑换码活动参数校验
func RedeemCodeParameterCheck(reqdata *model.XActiveDefine) error {
	if reqdata.BaseConfig == "" {
		return fmt.Errorf("会员参与条件限制不能为空")
	}
	if reqdata.Config == "" {
		return fmt.Errorf("兑换码相关配置不能为空")
	}

	// 解析基础配置
	var baseConfig RedeemCodeBaseConfig
	err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
	if err != nil {
		logs.Debug(err)
		return fmt.Errorf("会员参与条件限制格式错误")
	}

	// 解析奖励配置
	var rewardConfig RedeemCodeConfig

	// 处理 ExchangeCount 字段
	configStr := reqdata.Config

	// 处理空字符串，将其替换为 null
	configStr = strings.Replace(configStr, "\"ExchangeCount\":\"\"", "\"ExchangeCount\":null", -1)

	// 使用正则表达式处理字符串形式的数字，如 "ExchangeCount":"2"
	re := regexp.MustCompile(`"ExchangeCount":"(\d+)"`)
	configStr = re.ReplaceAllString(configStr, `"ExchangeCount":$1`)

	err = json.Unmarshal([]byte(configStr), &rewardConfig)
	if err != nil {
		logs.Debug(err)
		return fmt.Errorf("兑换码相关配置格式错误")
	}

	// 检查图片是否上传
	if reqdata.TopImgLang == "" || reqdata.TitleImgLang == "" {
		return fmt.Errorf("请上传中英文默认图片")
	}

	// 设置默认开始时间
	if reqdata.EffectStartTime == 0 {
		reqdata.EffectStartTime = time.Now().UnixMilli()
	}

	// 检查结束时间是否合法
	if reqdata.EffectEndTime != 0 && reqdata.EffectEndTime < reqdata.EffectStartTime {
		return fmt.Errorf("结束时间不可早于开始时间")
	}

	// 检查会员参与条件限制
	// 1. 检查IP黑名单格式
	if baseConfig.BlockedIPList != "" {
		ipList := strings.Split(baseConfig.BlockedIPList, ",")
		for _, ip := range ipList {
			if net.ParseIP(ip) == nil {
				return fmt.Errorf("被限制参与的IP格式错误: %s，请输入正确的IP地址", ip)
			}
		}
	}

	// 2. 检查游戏类型
	if baseConfig.ApplicableGames == 1 {
		if reqdata.GameType == "" {
			return fmt.Errorf("当参与活动的场馆为指定场馆时，必须选择至少一种游戏类型")
		}
		baseConfig.GameTypes = strings.Split(reqdata.GameType, ",")
	}

	// 3. 检查VIP层级
	if len(baseConfig.VIPLevels) > 0 {
		// VIP层级已选择，无需额外验证
	}

	// 4. 检查指定玩家可领取
	if baseConfig.SpecificPlayerIDs != "" {
		// 验证玩家ID格式
		playerIDs := strings.Split(baseConfig.SpecificPlayerIDs, ",")
		for _, id := range playerIDs {
			if id == "" {
				return fmt.Errorf("指定玩家ID不能为空")
			}
			// 可以添加更多的ID格式验证逻辑
		}
	}

	// 5. 检查当日流水达到
	if baseConfig.DailyFlowAmount > 0 {
		// 当日流水金额已设置，无需额外验证
	}

	// 6. 检查历史累积充值
	if baseConfig.HistoryDepositType == "金额" {
		if baseConfig.HistoryDepositAmount < 0 {
			return fmt.Errorf("历史累积充值金额必须大于0")
		}
	} else if baseConfig.HistoryDepositType == "次数" {
		if baseConfig.HistoryDepositCount <= 0 {
			return fmt.Errorf("历史累积充值次数必须大于0")
		}
	} else if baseConfig.HistoryDepositType != "" {
		return fmt.Errorf("历史累积充值类型必须为\"金额\"或\"次数\"")
	}

	// 检查流水倍数
	if rewardConfig.FlowMultiple <= 0 {
		return fmt.Errorf("流水倍数必须大于0")
	}

	// 检查兑换码设置
	if reqdata.ID > 0 {
		// 修改操作时，至少需要一个兑换码
		if len(rewardConfig.RedeemCodes) == 0 {
			return fmt.Errorf("修改活动时至少需要一个兑换码")
		}

		// 检查每个兑换码的格式
		for i, codeItem := range rewardConfig.RedeemCodes {
			if codeItem.RedeemCode == "" {
				return fmt.Errorf("第 %d 个兑换码不能为空", i+1)
			}

			// 检查兑换码格式
			if len(codeItem.RedeemCode) < 3 || len(codeItem.RedeemCode) > 12 {
				return fmt.Errorf("第 %d 个兑换码长度必须在3-12位之间", i+1)
			}

			// 检查兑换码是否只包含数字和字母（大小写）
			for _, char := range codeItem.RedeemCode {
				if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z')) {
					return fmt.Errorf("第 %d 个兑换码只能包含数字和字母（大小写）", i+1)
				}
			}

			// 检查奖励金额
			if codeItem.MinRewardAmount <= 0 {
				return fmt.Errorf("第 %d 个兑换码的奖励金额下限必须大于0", i+1)
			}
			if codeItem.MaxRewardAmount < codeItem.MinRewardAmount {
				return fmt.Errorf("第 %d 个兑换码的奖励金额上限不能小于下限", i+1)
			}
		}
	}
	// 新增活动时不需要提供兑换码，将在后续步骤中自动生成

	// 检查兑换码是否已存在（按运营商和渠道限制）
	// 查询同一运营商和渠道下的兑换码活动
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	var existingActivities []*model.XActiveDefine

	// 查询同一运营商和渠道下的启用状态兑换码活动
	err = xActiveDefine.WithContext(context.TODO()).Where(xActiveDefine.ActiveID.In(RedeemCodeActivityIDs...)).
		Where(xActiveDefine.State.Eq(1)).
		Where(xActiveDefine.SellerID.Eq(reqdata.SellerID)).
		Where(xActiveDefine.ChannelID.Eq(reqdata.ChannelID)).
		Scan(&existingActivities)
	if err != nil {
		logs.Error("查询兑换码活动失败: %v", err)
		return fmt.Errorf("检查兑换码唯一性时发生错误")
	}

	// 检查是否有重复的兑换码
	currentID := reqdata.ID // 当前活动ID，用于修改时排除自身

	// 创建一个映射，用于存储所有现有的兑换码
	existingCodes := make(map[string]bool)

	// 收集所有现有活动中的兑换码
	for _, activity := range existingActivities {
		// 跳过当前正在修改的活动
		if activity.ID == currentID {
			continue
		}

		var existingConfig RedeemCodeConfig
		if err := json.Unmarshal([]byte(activity.Config), &existingConfig); err != nil {
			logs.Error("解析兑换码配置失败: %v", err)
			continue
		}

		// 收集所有兑换码
		for _, codeItem := range existingConfig.RedeemCodes {
			existingCodes[codeItem.RedeemCode] = true
		}
	}

	// 检查当前配置中的兑换码是否已存在
	for i, codeItem := range rewardConfig.RedeemCodes {
		if existingCodes[codeItem.RedeemCode] {
			// 查找具体是哪个活动使用了这个兑换码
			conflictActivityTitle := ""
			conflictActivityID := int32(0)

			for _, activity := range existingActivities {
				if activity.ID == currentID {
					continue
				}

				var existingConfig RedeemCodeConfig
				if err := json.Unmarshal([]byte(activity.Config), &existingConfig); err != nil {
					continue
				}

				for _, existingCodeItem := range existingConfig.RedeemCodes {
					if existingCodeItem.RedeemCode == codeItem.RedeemCode {
						conflictActivityTitle = activity.Title
						conflictActivityID = activity.ID
						break
					}
				}
				if conflictActivityTitle != "" {
					break
				}
			}

			if conflictActivityTitle != "" {
				return fmt.Errorf("第 %d 个兑换码 %s 已存在于活动「%s」(ID:%d)中，请使用不同的兑换码", i+1, codeItem.RedeemCode, conflictActivityTitle, conflictActivityID)
			} else if conflictActivityID > 0 {
				return fmt.Errorf("第 %d 个兑换码 %s 已存在于活动ID:%d中，请使用不同的兑换码", i+1, codeItem.RedeemCode, conflictActivityID)
			} else {
				return fmt.Errorf("第 %d 个兑换码 %s 已存在于其他活动中，请使用不同的兑换码", i+1, codeItem.RedeemCode)
			}
		}
	}

	// 验证彩金钱包相关配置
	if reqdata.GiftWallet == 2 { // 彩金钱包
		// 验证彩金钱包下的活动奖励配置数据
		if reqdata.AwardData != "" {
			var awardData RedeemCodeAwardData
			if err := json.Unmarshal([]byte(reqdata.AwardData), &awardData); err != nil {
				return fmt.Errorf("彩金钱包活动奖励配置格式错误")
			}

			// 验证奖励打码倍数类型（必须是彩金类型）
			if awardData.AwardBetType != 2 {
				return fmt.Errorf("彩金钱包活动的打码类型必须为（彩金）")
			}

			// 验证奖励配置
			if len(awardData.AwardConfig.RedeemCodes) == 0 {
				return fmt.Errorf("彩金钱包活动的奖励配置不能为空")
			}

			// 验证流水倍数
			if awardData.AwardConfig.FlowMultiple <= 0 {
				return fmt.Errorf("彩金钱包活动的流水倍数必须大于0")
			}

			for i, v := range awardData.AwardConfig.RedeemCodes {
				if v.RedeemCode == "" {
					return fmt.Errorf("第 %d 个兑换码不能为空", i+1)
				}
				if v.MinRewardAmount <= 0 {
					return fmt.Errorf("第 %d 个奖励配置的最小奖励金额必须大于0", i+1)
				}
				if v.MaxRewardAmount < v.MinRewardAmount {
					return fmt.Errorf("第 %d 个奖励配置的最大奖励金额不能小于最小奖励金额", i+1)
				}
			}
		}
	}

	// 更新配置
	b, _ := json.Marshal(baseConfig)
	reqdata.BaseConfig = string(b)
	c, _ := json.Marshal(rewardConfig)
	reqdata.Config = string(c)

	return nil
}

// HandleRedeemCodeAdd 处理兑换码活动的添加
func HandleRedeemCodeAdd(ctx *abugo.AbuHttpContent, reqdata struct {
	model.XActiveDefine
	GoogleCode string
}, errcode *int) {
	// 设置活动ID
	reqdata.ActiveID = int32(utils.RedeemCodeGift_1003)

	// 检查参数
	err := RedeemCodeParameterCheck(&reqdata.XActiveDefine)
	if err != nil {
		ctx.RespErrString(true, errcode, err.Error())
		return
	}

	// 创建活动记录
	tb := &reqdata.XActiveDefine
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	err = xActiveDefine.WithContext(ctx.Gin()).Create(tb)
	if ctx.RespErr(err, errcode) {
		return
	}

	// 保存活动排序
	err = SaveActiveSort(tb.ID, tb.Sort, tb.TopSort)
	if ctx.RespErr(err, errcode) {
		return
	}

	// 解析基础配置和奖励配置
	var baseConfig RedeemCodeBaseConfig
	err = json.Unmarshal([]byte(tb.BaseConfig), &baseConfig)
	if err != nil {
		ctx.RespErrString(true, errcode, "解析会员参与条件限制失败")
		return
	}

	var rewardConfig RedeemCodeConfig
	err = json.Unmarshal([]byte(tb.Config), &rewardConfig)
	if err != nil {
		ctx.RespErrString(true, errcode, "解析兑换码相关配置失败")
		return
	}

	// 自动生成兑换码
	// 使用默认设置生成兑换码
	defaultCodeSettings := struct {
		CodeLength  int32  `json:"CodeLength"`
		CodePrefix  string `json:"CodePrefix"`
		CodeSuffix  string `json:"CodeSuffix"`
		CodeCharset string `json:"CodeCharset"`
	}{
		CodeLength: 12, // 默认12位，确保有足够的长度包含各类字符
		CodePrefix: "",
		CodeSuffix: "",
		// 仅包含大写字母、小写字母和数字的字符集
		CodeCharset: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",
	}

	// 生成一个唯一的兑换码
	code, err := generateUniqueRedeemCode(tb.ID, tb.SellerID, tb.ChannelID, defaultCodeSettings)
	if err != nil {
		ctx.RespErrString(true, errcode, err.Error())
		return
	}

	// 创建一个新的兑换码配置项
	newCodeItem := RedeemCodeItem{
		RedeemCode:      code,
		ExchangeCount:   nil, // 默认不限制兑换次数
		MinRewardAmount: 10,  // 默认奖励金额下限10
		MaxRewardAmount: 100, // 默认奖励金额上限100
		ExchangeTimes:   0,   // 设置已兑换次数默认值为0
	}

	// 将新的兑换码配置项添加到兑换码列表中
	rewardConfig.RedeemCodes = []RedeemCodeItem{newCodeItem}

	configJSON, err := json.Marshal(rewardConfig)
	if err != nil {
		ctx.RespErrString(true, errcode, "序列化兑换码配置失败")
		return
	}

	// 更新活动记录
	_, err = xActiveDefine.WithContext(ctx.Gin()).Where(xActiveDefine.ID.Eq(tb.ID)).
		Update(xActiveDefine.Config, string(configJSON))
	if ctx.RespErr(err, errcode) {
		return
	}

	logs.Info("成功生成兑换码: %s, 活动ID: %d", code, tb.ID)
	ctx.RespOK()
}

// HandleRedeemCodeMod 处理兑换码活动的修改
func HandleRedeemCodeMod(ctx *abugo.AbuHttpContent, reqdata *model.XActiveDefine, errcode *int) error {
	// 检查参数
	err := RedeemCodeParameterCheck(reqdata)
	if err != nil {
		ctx.RespErrString(true, errcode, err.Error())
		return err
	}

	// 处理 ExchangeCount 字段
	configStr := reqdata.Config

	// 处理空字符串，将其替换为 null
	configStr = strings.Replace(configStr, "\"ExchangeCount\":\"\"", "\"ExchangeCount\":null", -1)

	// 使用正则表达式处理字符串形式的数字，如 "ExchangeCount":"2"
	re := regexp.MustCompile(`"ExchangeCount":"(\d+)"`)
	configStr = re.ReplaceAllString(configStr, `"ExchangeCount":$1`)

	// 更新活动记录
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	_, err = xActiveDefine.WithContext(ctx.Gin()).Where(xActiveDefine.ID.Eq(reqdata.ID)).Updates(map[string]any{
		"Title":           reqdata.Title,
		"Memo":            reqdata.Memo,
		"AuditType":       reqdata.AuditType,
		"State":           reqdata.State,
		"Sort":            reqdata.Sort,
		"EffectStartTime": reqdata.EffectStartTime,
		"EffectEndTime":   reqdata.EffectEndTime,
		"TitleImg":        reqdata.TitleImg,
		"TitleLang":       reqdata.TitleLang,
		"TitleImgLang":    reqdata.TitleImgLang,
		"TopImg":          reqdata.TopImg,
		"TopImgLang":      reqdata.TopImgLang,
		"GameType":        reqdata.GameType,
		"BaseConfig":      reqdata.BaseConfig,
		"Config":          configStr, // 使用处理后的配置字符串
		"AwardType":       reqdata.AwardType,
		"AwardTab":        reqdata.AwardTab,
		"GiftWallet":      reqdata.GiftWallet,
		"AwardData":       reqdata.AwardData,
	})
	if err != nil {
		ctx.RespErr(err, errcode)
		return err
	}

	// 保存活动排序
	err = SaveActiveSort(reqdata.ID, reqdata.Sort, reqdata.TopSort)
	if err != nil {
		ctx.RespErr(err, errcode)
		return err
	}
	ctx.RespOK()
	return nil
}

// generateUniqueRedeemCode 生成唯一的兑换码（按运营商和渠道限制）
func generateUniqueRedeemCode(activeID int32, sellerID int32, channelID int32, codeSettings struct {
	CodeLength  int32  `json:"CodeLength"`  // 兑换码长度，默认8位
	CodePrefix  string `json:"CodePrefix"`  // 兑换码前缀，可为空
	CodeSuffix  string `json:"CodeSuffix"`  // 兑换码后缀，可为空
	CodeCharset string `json:"CodeCharset"` // 兑换码字符集，默认为数字和大写字母
}) (string, error) {
	// 查询同一运营商和渠道下的现有兑换码活动
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	var existingActivities []*model.XActiveDefine

	err := xActiveDefine.WithContext(context.Background()).Where(xActiveDefine.ActiveID.In(RedeemCodeActivityIDs...)).
		Where(xActiveDefine.State.Eq(1)).
		Where(xActiveDefine.SellerID.Eq(sellerID)).
		Where(xActiveDefine.ChannelID.Eq(channelID)).
		Scan(&existingActivities)
	if err != nil {
		logs.Error("查询兑换码活动失败: %v", err)
		return "", fmt.Errorf("检查兑换码唯一性时发生错误")
	}

	// 提取所有现有的兑换码
	existingCodes := make(map[string]bool)
	for _, activity := range existingActivities {
		// 跳过当前活动
		if activity.ID == activeID {
			continue
		}

		var config RedeemCodeConfig
		if err := json.Unmarshal([]byte(activity.Config), &config); err != nil {
			logs.Error("解析兑换码配置失败: %v", err)
			continue
		}

		// 收集所有兑换码
		for _, codeItem := range config.RedeemCodes {
			existingCodes[codeItem.RedeemCode] = true
		}
	}

	// 尝试最多100次生成唯一的兑换码
	for attempts := 0; attempts < 100; attempts++ {
		code := generateRedeemCode(int(codeSettings.CodeLength), codeSettings.CodePrefix, codeSettings.CodeSuffix, codeSettings.CodeCharset)

		// 检查是否已存在
		if !existingCodes[code] {
			return code, nil
		}
	}

	// 如果尝试了100次仍然没有生成唯一的兑换码，返回错误
	return "", fmt.Errorf("无法生成唯一的兑换码，请尝试使用不同的前缀或后缀")
}

// GenerateRedeemCodeRequest 生成兑换码请求参数
type GenerateRedeemCodeRequest struct {
	CodeLength  int32  `json:"CodeLength"`  // 兑换码长度，默认8位
	CodePrefix  string `json:"CodePrefix"`  // 兑换码前缀，可为空
	CodeSuffix  string `json:"CodeSuffix"`  // 兑换码后缀，可为空
	CodeCharset string `json:"CodeCharset"` // 兑换码字符集，默认为数字和大写字母
}

// GenerateRedeemCodeResponse 生成兑换码响应
type GenerateRedeemCodeResponse struct {
	RedeemCode string `json:"RedeemCode"` // 生成的兑换码
}

// HandleGenerateRedeemCode 处理生成兑换码请求
func HandleGenerateRedeemCode(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 从请求中获取运营商和渠道信息
	type RequestData struct {
		SellerId  int // 运营商ID
		ChannelId int // 渠道ID
	}

	requestData := RequestData{}
	err := ctx.RequestData(&requestData)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 使用默认值，不需要前端传递参数
	reqdata := GenerateRedeemCodeRequest{
		CodeLength: 12, // 默认12位，确保有足够的长度包含各类字符
		CodePrefix: "", // 默认无前缀
		CodeSuffix: "", // 默认无后缀
		// 仅包含大写字母、小写字母和数字的字符集
		CodeCharset: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",
	}

	// 查询同一运营商和渠道下的现有兑换码活动
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	var existingActivities []*model.XActiveDefine

	query := xActiveDefine.WithContext(context.Background()).Where(xActiveDefine.ActiveID.In(RedeemCodeActivityIDs...)).
		Where(xActiveDefine.State.Eq(1))

	// 如果提供了运营商和渠道信息，则按这些条件过滤
	if requestData.SellerId > 0 {
		query = query.Where(xActiveDefine.SellerID.Eq(int32(requestData.SellerId)))
	}
	if requestData.ChannelId > 0 {
		query = query.Where(xActiveDefine.ChannelID.Eq(int32(requestData.ChannelId)))
	}

	err = query.Scan(&existingActivities)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 提取所有现有的兑换码
	existingCodes := make(map[string]bool)
	for _, activity := range existingActivities {
		var config RedeemCodeConfig
		if err := json.Unmarshal([]byte(activity.Config), &config); err != nil {
			logs.Error("解析兑换码配置失败: %v", err)
			continue
		}

		// 收集所有兑换码
		for _, codeItem := range config.RedeemCodes {
			existingCodes[codeItem.RedeemCode] = true
		}
	}

	// 尝试最多100次生成唯一的兑换码
	var generatedCode string
	for attempts := 0; attempts < 100; attempts++ {
		code := generateRedeemCode(int(reqdata.CodeLength), reqdata.CodePrefix, reqdata.CodeSuffix, reqdata.CodeCharset)

		// 检查是否已存在
		if !existingCodes[code] {
			generatedCode = code
			break
		}
	}

	// 如果尝试了100次仍然没有生成唯一的兑换码，返回错误
	if generatedCode == "" {
		ctx.RespErrString(true, &errcode, "无法生成唯一的兑换码，请尝试使用不同的前缀或后缀")
		return
	}

	// 返回生成的兑换码
	response := GenerateRedeemCodeResponse{
		RedeemCode: generatedCode,
	}

	logs.Info("成功生成兑换码: %s", generatedCode)
	ctx.RespOK(response)
}
