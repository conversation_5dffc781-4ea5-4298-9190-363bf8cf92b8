package controller

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/url"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/zhms/xgo/xgo"

	"path"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
	"github.com/spf13/viper"
	"github.com/xuri/excelize/v2"
	"github.com/yinheli/qqwry"
	"gorm.io/gorm"
)

const USER_LOGIN_LOG_OPTIONS = `[
{"field":"Id","name":"序号"},
{"field":"UserId","name":"玩家ID"},
{"field":"SellerId","name":"运营商"},
{"field":"ChannelId","name":"渠道"},
{"field":"Account","name":"玩家账号"},
{"field":"Lang","name":"登录语言"},
{"field":"IpAddr","name":"登录地区"},
{"field":"Ip","name":"登录IP"},
{"field":"RegisterIp","name":"注册IP"},
{"field":"Address","name":"玩家地址"},
{"field":"RegisterTime","name":"注册时间"},
{"field":"LoginTime","name":"登录时间"},
{"field":"DeviceId","name":"登录设备ID"},
{"field":"DeviceType","name":"登录设备类型"}]`

// 添加批处理相关常量
const (
	batchSize = 15 // 每批处理的请求数量
)

type UserController struct {
}

func (c *UserController) Init() {
	group := server.Http().NewGroup("/api/user")
	{
		group.Post("/list", c.list)
		group.Post("/list_batch", c.list_batch)
		group.Post("/set_state", c.set_state)
		group.Post("/modify_password", c.modify_password)
		group.Post("/modify_wallet_password", c.modify_wallet_password)
		group.Post("/modify_address", c.modify_address)
		group.Post("/add_child", c.add_child)
		group.Post("/bind_agentcode", c.bind_agentcode)
		group.Post("/login_log", c.login_log)
		group.Post("/agent_bind_log", c.agent_bind_log)
		group.Post("/address_modify_log", c.address_modify_log)
		group.Post("/reset_telegram", c.reset_telegram)
		group.Post("/add_amount", c.add_amount)
		group.Post("/set_gamefee", c.set_gamefee)
		group.Post("/sync_gamefee", c.sync_gamefee)
		group.Post("/wallet_address", c.wallet_address)
		group.Post("/wallet_delete", c.wallet_delete)
		group.Post("/set_jptype", c.set_jptype)
		group.Post("/modify_vip_level", c.modify_vip_level)
		group.Post("/modify_pwd_verification_type", c.modifyPwdVerificationType)
		group.Post("/clear_withdrawliushui", c.clear_withdrawliushui)

		group.Post("/set_audit_amount", c.set_audit_amount)

		group.Post("/reset_max_bet", c.reset_max_bet)

		group.Post("/set_test", c.set_test)

		group.Post("/agent_generate_lose_code", c.agent_generate_lose_code)

		group.Post("/set_blockmaker", c.set_blockmaker)
		group.Post("/ingore_winjiangpei", c.ingore_winjiangpei)

		group.Post("/set_audit", c.set_audit)
		group.Post("/set_gamelimit", c.set_gamelimit)

		group.Post("/set_withward_needliusui", c.set_withward_needliusui)
		group.Post("/remove_email", c.removeEmail)
		group.Post("/remove_phone", c.removePhone)

		group.PostNoAuth("/update_user_IsInResourceDb", c.update_user_IsInResourceDb)
		group.Post("/info", c.info)
		group.Post("/add_agent", c.AddAgent)
		group.Post("/save_agent_info", c.SaveAgentInfo)
		group.Post("/generate_promotion_urls", c.generatePromotionUrls)
		group.Post("/export_agent_urls", c.ExportAgentUrls)
		group.Post("/check_verify_code", c.checkVerifyCode)

	}
}

// 生成负盈利代理code
func (c *UserController) agent_generate_lose_code(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int    `validate:"required"`
		GoogleCode string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	xUserTb := server.DaoxHashGame().XUser
	xUserDb := xUserTb.WithContext(context.Background())
	user, err := xUserDb.Where(xUserTb.UserID.Eq(int32(reqdata.UserId))).First()
	if err != nil {
		logs.Error("logs agent_generate_lose_code user ", reqdata, err)
		ctx.RespErrString(true, &errcode, "用户不存在")
		return
	}
	if user.AgentID > 0 || user.TopAgentID > 0 {
		ctx.RespErrString(true, &errcode, "已存在上级")
		return
	}
	r := struct {
		AgentCode int
	}{}
	server.Db().GormDao().Raw(`call x_api_agent_get_info(?, "usdt")`, reqdata.UserId).Scan(&r)
	if r.AgentCode == 0 {
		ctx.RespErrString(true, &errcode, "生成code失败")
		return
	}

	url := user.RegURL + "/#/?AgentCode=" + abugo.GetStringFromInterface2(r.AgentCode)
	ctx.Put("agentUrl", url)
	ctx.RespOK()
	server.WriteAdminLog("生成负盈利代理链接", ctx, reqdata)
}

func (c *UserController) list_batch(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		UserId    []int32
		Account   []string
		Address   []string
		HostTagId []int32
	}
	type Result struct {
		model.XUser
		LoginIPCount       int64
		LoginDeviceIdCount int64
		TagName            []string
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "查", "查询玩家列表")
	if token == nil {
		return
	}
	if token.SellerId != 0 {
		reqdata.SellerId = token.SellerId
	}

	if len(reqdata.UserId) == 0 && len(reqdata.Account) == 0 && len(reqdata.Address) == 0 {
		ctx.RespErrString(true, &errcode, "请输入查询条件")
		return
	}
	limit := 100
	if len(reqdata.UserId) > limit || len(reqdata.Account) > limit || len(reqdata.Address) > limit {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("每次查询不能大于%d条用户信息", limit))
		return
	}
	xUser := server.DaoxHashGame().XUser
	xChannelHost := server.DaoxHashGame().XChannelHost
	xHostTag := server.DaoxHashGame().XHostTag
	dbUser := xUser.WithContext(ctx.Gin())
	if reqdata.SellerId != 0 {
		dbUser = dbUser.Where(xUser.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if reqdata.ChannelId != 0 {
		dbUser = dbUser.Where(xUser.ChannelID.Eq(int32(reqdata.ChannelId)))
	}
	if len(reqdata.UserId) > 0 {
		dbUser = dbUser.Where(xUser.UserID.In(reqdata.UserId...))
	}
	if len(reqdata.Account) > 0 {
		dbUser = dbUser.Where(xUser.Account.In(reqdata.Account...))
	}
	if len(reqdata.Address) > 0 {
		dbUser = dbUser.Where(xUser.Address.In(reqdata.Address...))
	}
	if len(reqdata.HostTagId) > 0 {
		dbUser = dbUser.Where(xUser.WithContext(ctx.Gin()).Columns(xUser.ChannelID).In(
			xChannelHost.WithContext(ctx.Gin()).Select(xChannelHost.ChannelID.Distinct()).
				Where(xChannelHost.HostTagID.In(reqdata.HostTagId...)),
		))
	}

	userList, err := dbUser.Limit(limit).Find()
	if ctx.RespErr(err, &errcode) {
		return
	}

	list := make([]Result, 0)
	for _, user := range userList {
		var TagName []string
		LoginIPCount, _ := xUser.WithContext(ctx.Gin()).Where(xUser.LoginIP.Eq(user.LoginIP)).Count()
		LoginDeviceIdCount, _ := xUser.WithContext(ctx.Gin()).Where(xUser.LoginDeviceID.Eq(user.LoginDeviceID)).Count()
		_ = xHostTag.WithContext(ctx.Gin()).Where(xHostTag.WithContext(ctx.Gin()).Columns(xHostTag.ID).In(
			xChannelHost.WithContext(ctx.Gin()).Where(xChannelHost.ChannelID.Eq(user.ChannelID)).Select(xChannelHost.HostTagID),
		)).Pluck(xHostTag.TagName, &TagName)

		list = append(list, Result{
			XUser:              *user,
			LoginIPCount:       LoginIPCount,
			LoginDeviceIdCount: LoginDeviceIdCount,
			TagName:            TagName,
		})
	}

	ctx.Put("list", list)
	ctx.RespOK()
}

func getUserStateString(state int) string {
	if state == 1 {
		return "正常"
	} else {
		return "禁用"
	}
}

func (c *UserController) reset_max_bet(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.Db().Query(`update x_user set MaxBet = '{"usdt":0,"trx":0}' , MaxBetTime = now() where UserId = ?`, []interface{}{reqdata.UserId})
	ctx.RespOK()
	server.WriteAdminLog("重置玩家MaxBet", ctx, reqdata)
}

func (c *UserController) set_state(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		State      int // 1启用 2禁用 3冻结金额
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	db.User_Set_State(reqdata.UserId, reqdata.State)
	if reqdata.State == 2 {
		server.KickOutClientLogin(reqdata.UserId)
	}
	ctx.RespOK()
	server.WriteAdminLog("修改玩家状态", ctx, reqdata)
}
func (c *UserController) modify_password(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		Password   string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	hash := md5.New()
	hash.Write([]byte(reqdata.Password))
	reqdata.Password = fmt.Sprintf("%x", hash.Sum(nil))

	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.KickOutClientLogin(reqdata.UserId)
	db.User_Set_Password(reqdata.UserId, reqdata.Password)
	ctx.RespOK()
	server.WriteAdminLog("修改玩家密码", ctx, reqdata)
}
func (c *UserController) modify_wallet_password(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		Password   string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	{
		hash := md5.New()
		hash.Write([]byte(reqdata.Password))
		reqdata.Password = fmt.Sprintf("%x", hash.Sum(nil))
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	//server.KickOutClientLogin(reqdata.UserId)
	db.User_Set_WalletPassword(reqdata.UserId, reqdata.Password)
	ctx.RespOK()
	server.WriteAdminLog("修改玩家提现密码", ctx, reqdata)
}
func (c *UserController) modify_address(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		Address    string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var address string
	sql := "select address from x_user where address = ?"
	server.Db().QueryScan(sql, []interface{}{reqdata.Address}, &address)
	if ctx.RespErrString(len(address) > 0, &errcode, "修改失败,地址已注册") {
		return
	}
	sql = "select address from x_guest where address = ?"
	server.Db().QueryScan(sql, []interface{}{reqdata.Address}, &address)
	if ctx.RespErrString(len(address) > 0, &errcode, "修改失败,地址已注册") {
		return
	}
	var ChannelId int
	sql = "select Address,ChannelId from x_user where UserId = ?"
	server.Db().QueryScan(sql, []interface{}{reqdata.UserId}, &address, &ChannelId)
	sql = "insert into  x_address_modify_history(SellerId,UserId,BeforeAddress,AfterAddress,OptAccount,CreateTime,ChannelId)values(?,?,?,?,?,now(),?)"
	server.Db().QueryNoResult(sql, reqdata.SellerId, reqdata.UserId, address, reqdata.Address, token.Account, ChannelId)
	db.User_Set_Address(reqdata.UserId, reqdata.Address)
	server.KickOutClientLogin(reqdata.UserId)
	ctx.RespOK()
	server.WriteAdminLog("修改钱包地址", ctx, reqdata)
}
func (c *UserController) add_child(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int    `validate:"required"`
		AgentCode  string `validate:"required"`
		Address    string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var dberrcode int
	var dberrmsg string
	server.Db().QueryScan("call x_admin_bind_address(?,?,?,?)", []interface{}{reqdata.UserId, reqdata.AgentCode, reqdata.Address, token.Account}, &dberrcode, &dberrmsg)
	if ctx.RespErrString(dberrcode > 0, &errcode, dberrmsg) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("添加下级地址", ctx, reqdata)
}
func (c *UserController) bind_agentcode(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int    `validate:"required"`
		AgentCode  string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	var dberrcode int
	var dberrmsg string
	server.Db().QueryScan("call x_api_agent_bind(?,?,?)", []interface{}{reqdata.UserId, reqdata.AgentCode, token.Account}, &dberrcode, &dberrmsg)
	if ctx.RespErrString(dberrcode > 0, &errcode, dberrmsg) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("绑定邀请码", ctx, reqdata)
}

func (c *UserController) login_log(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page       int
		PageSize   int
		SellerId   int
		UserId     int
		StartTime  int64
		EndTime    int64
		RegisterIp string
		LoginIp    string
		ChannelId  int
		Address    string
		DeviceId   string
		DeviceType string
		Export     int
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "登录日志", "查", "查看登录日志")
	if token == nil {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	where.Add("and", "Ip", "=", reqdata.LoginIp, "")
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "RegisterIp", "=", reqdata.RegisterIp, "")
	where.Add("and", "Address", "=", reqdata.Address, "")
	where.Add("and", "DeviceId", "=", reqdata.DeviceId, "")
	where.Add("and", "DeviceType", "=", reqdata.DeviceType, "")
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	where.Add("and", "LoginTime", ">=", abugo.TimeStampToLocalDate(reqdata.StartTime), "")
	where.Add("and", "LoginTime", "<", abugo.TimeStampToLocalDate(reqdata.EndTime), "")
	total, presult := server.Db().Table("x_login_log").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	q := qqwry.NewQQwry("./config/ipdata.dat")
	for i := 0; i < len(*presult); i++ {
		ip := abugo.GetStringFromInterface((*presult)[i]["Ip"])
		if strings.Index(ip, ".") > 0 {
			q.Find(ip)
			(*presult)[i]["IpAddr"] = fmt.Sprintf("%s %s", q.Country, q.City)
		} else {
			(*presult)[i]["IpAddr"] = ""
		}
	}

	if reqdata.Export == 1 {
		xdata := &xgo.XMaps{}
		xdata.RawData = []xgo.XMap{}
		for _, v := range *presult {
			xdata.RawData = append(xdata.RawData, xgo.XMap{RawData: v})
		}
		filename := "export_login_log_" + time.Now().Format("**************")
		xgo.Export(server.ExportDir()+"/"+filename, xdata, USER_LOGIN_LOG_OPTIONS)
		ctx.Put("filename", "/exports/"+filename+".xlsx")
	} else {
		ctx.Put("data", *presult)
		ctx.Put("total", total)
	}

	ctx.RespOK()
}

func (c *UserController) agent_bind_log(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		UserId    int
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "绑定记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	total, presult := server.Db().Table("x_bind_history_ex").Where(where).OrderBy("Id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *presult)
	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("查看绑定邀请码记录", ctx, reqdata)
}

func (c *UserController) address_modify_log(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		UserId    int
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "钱包更换记录", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "UserId", "=", reqdata.UserId, 0)
	total, presult := server.Db().Table("x_address_modify_history").Where(where).OrderBy("Id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *presult)
	ctx.Put("total", total)
	ctx.RespOK()
	server.WriteAdminLog("查看更换地址记录", ctx, reqdata)
}
func (c *UserController) reset_telegram(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	server.Db().Query("update x_user set TgChatId = 0,TgUserName = null,Account = ? where userid = ?", []interface{}{fmt.Sprintf("auto%d", reqdata.UserId), reqdata.UserId})
	ctx.RespOK()
	server.WriteAdminLog("解绑tg", ctx, reqdata)
}

func (c *UserController) add_amount(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId        int
		UserIds         []int
		Amount          float64
		Memo            string
		AmountType      int //0默认类型非彩金  1彩金类型,2余额清零,3Bonus币,4Bonus币清零
		LiushuiMultiple float64
		CaiJingName     string
		GoogleCode      string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "增资"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if ctx.RespErrString(reqdata.Amount > utils.ArtificialCapitalIncrease, &errcode, fmt.Sprintf("增资额不能大于%d", utils.ArtificialCapitalIncrease)) {
		return
	}
	if ctx.RespErrString(reqdata.AmountType == 1 && reqdata.CaiJingName == "", &errcode, "彩金名称不能为空") {
		return
	}
	// 处理余额清零和Bonus币清零的情况
	if reqdata.AmountType == 2 || reqdata.AmountType == 4 {
		reqdata.Amount = 0
	}
	{
		seen := make(map[int]bool)
		for _, uid := range reqdata.UserIds {
			if seen[uid] {
				ctx.RespErrString(true, &errcode, "玩家Id重复")
				return
			}
			seen[uid] = true
		}
	}
	{
		for _, uid := range reqdata.UserIds {
			if uid <= 0 {
				ctx.RespErrString(true, &errcode, "玩家Id不正确")
				return
			}
		}
	}
	for _, userid := range reqdata.UserIds {
		presult, err := server.Db().CallProcedure("x_admin_add_amount", userid, reqdata.Amount, token.Account, reqdata.Memo, reqdata.AmountType, reqdata.CaiJingName, reqdata.LiushuiMultiple)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if ctx.RespProcedureErr(presult) {
			return
		}
	}
	ctx.RespOK()
	server.WriteAdminLog("玩家增资", ctx, reqdata)
}

func (c *UserController) set_gamefee(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		GameFee    string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	isGameFeeValidStr := check_user_game_fee_valid(reqdata.GameFee)
	if ctx.RespErrString(isGameFeeValidStr != "", &errcode, isGameFeeValidStr) {
		return
	}
	var useraddress string
	server.Db().QueryScan("select Address from x_user where  UserId = ?", []interface{}{reqdata.UserId}, &useraddress)
	if len(useraddress) > 0 {
		server.Db().Conn().Exec("update x_user set GameFee = ? where Address = ?", reqdata.GameFee, useraddress)
		server.Db().Conn().Exec("update x_guest set GameFee = ? where Address = ?", reqdata.GameFee, useraddress)
	} else {
		server.Db().Conn().Exec("update x_user set GameFee = ? where UserId = ?", reqdata.GameFee, reqdata.UserId)
	}
	ctx.RespOK()
	server.WriteAdminLog("设置玩家游戏赔率", ctx, reqdata)
}

func check_user_game_fee_valid(GameFee string) string {
	type DataValue struct {
		State int     `json:"s"`
		Value float64 `json:"v"`
	}
	type DaXiaoDanShuangXinYun struct {
		Room1 DataValue `json:"1"`
		Room2 DataValue `json:"2"`
		Room3 DataValue `json:"3"`
	}
	type ZhuangXianRoom struct {
		FeiHe DataValue `json:"1"`
		He    DataValue `json:"2"`
	}
	type ZhuangXian struct {
		Room1 ZhuangXianRoom `json:"1"`
		Room2 ZhuangXianRoom `json:"2"`
		Room3 ZhuangXianRoom `json:"3"`
	}
	type NiuNiuRoom struct {
		Niu1   DataValue `json:"1"`
		Niu2   DataValue `json:"2"`
		Niu3   DataValue `json:"3"`
		Niu4   DataValue `json:"4"`
		Niu5   DataValue `json:"5"`
		Niu6   DataValue `json:"6"`
		Niu7   DataValue `json:"7"`
		Niu8   DataValue `json:"8"`
		Niu9   DataValue `json:"9"`
		NiuNiu DataValue `json:"0"`
	}
	type NiuNiu struct {
		Room1 NiuNiuRoom `json:"1"`
		Room2 NiuNiuRoom `json:"2"`
		Room3 NiuNiuRoom `json:"3"`
	}
	type FeeData struct {
		ZhuanZhangDaXiao     DaXiaoDanShuangXinYun `json:"1"`
		YiFenDaXiao          DaXiaoDanShuangXinYun `json:"101"`
		YuEDaXiao            DaXiaoDanShuangXinYun `json:"201"`
		ZhuanZhangDanShuang  DaXiaoDanShuangXinYun `json:"2"`
		YiFenDanShuang       DaXiaoDanShuangXinYun `json:"102"`
		YuEDanShuang         DaXiaoDanShuangXinYun `json:"202"`
		ZhuanZhangXingYun    DaXiaoDanShuangXinYun `json:"3"`
		YiFenXingYun         DaXiaoDanShuangXinYun `json:"103"`
		YuEXingYun           DaXiaoDanShuangXinYun `json:"203"`
		ZhuanZhangZhuangXian ZhuangXian            `json:"4"`
		YiFenZhuangXian      ZhuangXian            `json:"104"`
		YuEZhuangXian        ZhuangXian            `json:"204"`
		ZhuanZhangNiuNiu     NiuNiu                `json:"5"`
		YiFenNiuNiu          NiuNiu                `json:"105"`
		YuENiuNiu            NiuNiu                `json:"205"`
	}
	fd := FeeData{}
	err := json.Unmarshal([]byte(GameFee), &fd)
	if err != nil {
		logs.Error("GameFee格式不正确 err=", err.Error())
		return "GameFee格式不正确"
	}
	if fd.ZhuanZhangDaXiao.Room1.Value < 0 || fd.ZhuanZhangDaXiao.Room2.Value < 0 || fd.ZhuanZhangDaXiao.Room3.Value < 0 ||
		fd.ZhuanZhangDanShuang.Room1.Value < 0 || fd.ZhuanZhangDanShuang.Room2.Value < 0 || fd.ZhuanZhangDanShuang.Room3.Value < 0 ||
		fd.ZhuanZhangXingYun.Room1.Value < 0 || fd.ZhuanZhangXingYun.Room2.Value < 0 || fd.ZhuanZhangXingYun.Room3.Value < 0 ||
		fd.ZhuanZhangZhuangXian.Room1.FeiHe.Value < 0 || fd.ZhuanZhangZhuangXian.Room1.He.Value < 0 ||
		fd.ZhuanZhangZhuangXian.Room2.FeiHe.Value < 0 || fd.ZhuanZhangZhuangXian.Room2.He.Value < 0 ||
		fd.ZhuanZhangZhuangXian.Room3.FeiHe.Value < 0 || fd.ZhuanZhangZhuangXian.Room3.He.Value < 0 ||
		fd.ZhuanZhangNiuNiu.Room1.Niu1.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu2.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu3.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu4.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu5.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu6.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu7.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu8.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.Niu9.Value < 0 || fd.ZhuanZhangNiuNiu.Room1.NiuNiu.Value < 0 ||
		fd.ZhuanZhangNiuNiu.Room2.Niu1.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu2.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu3.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu4.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu5.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu6.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu7.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu8.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.Niu9.Value < 0 || fd.ZhuanZhangNiuNiu.Room2.NiuNiu.Value < 0 ||
		fd.ZhuanZhangNiuNiu.Room3.Niu1.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu2.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu3.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu4.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu5.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu6.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu7.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu8.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.Niu9.Value < 0 || fd.ZhuanZhangNiuNiu.Room3.NiuNiu.Value < 0 {
		return "参数不正确2"
	}
	if fd.YiFenDaXiao.Room1.Value < 0 || fd.YiFenDaXiao.Room2.Value < 0 || fd.YiFenDaXiao.Room3.Value < 0 ||
		fd.YiFenDanShuang.Room1.Value < 0 || fd.YiFenDanShuang.Room2.Value < 0 || fd.YiFenDanShuang.Room3.Value < 0 ||
		fd.YiFenXingYun.Room1.Value < 0 || fd.YiFenXingYun.Room2.Value < 0 || fd.YiFenXingYun.Room3.Value < 0 ||
		fd.YiFenZhuangXian.Room1.FeiHe.Value < 0 || fd.YiFenZhuangXian.Room1.He.Value < 0 ||
		fd.YiFenZhuangXian.Room2.FeiHe.Value < 0 || fd.YiFenZhuangXian.Room2.He.Value < 0 ||
		fd.YiFenZhuangXian.Room3.FeiHe.Value < 0 || fd.YiFenZhuangXian.Room3.He.Value < 0 ||
		fd.YiFenNiuNiu.Room1.Niu1.Value < 0 || fd.YiFenNiuNiu.Room1.Niu2.Value < 0 || fd.YiFenNiuNiu.Room1.Niu3.Value < 0 || fd.YiFenNiuNiu.Room1.Niu4.Value < 0 || fd.YiFenNiuNiu.Room1.Niu5.Value < 0 || fd.YiFenNiuNiu.Room1.Niu6.Value < 0 || fd.YiFenNiuNiu.Room1.Niu7.Value < 0 || fd.YiFenNiuNiu.Room1.Niu8.Value < 0 || fd.YiFenNiuNiu.Room1.Niu9.Value < 0 || fd.YiFenNiuNiu.Room1.NiuNiu.Value < 0 ||
		fd.YiFenNiuNiu.Room2.Niu1.Value < 0 || fd.YiFenNiuNiu.Room2.Niu2.Value < 0 || fd.YiFenNiuNiu.Room2.Niu3.Value < 0 || fd.YiFenNiuNiu.Room2.Niu4.Value < 0 || fd.YiFenNiuNiu.Room2.Niu5.Value < 0 || fd.YiFenNiuNiu.Room2.Niu6.Value < 0 || fd.YiFenNiuNiu.Room2.Niu7.Value < 0 || fd.YiFenNiuNiu.Room2.Niu8.Value < 0 || fd.YiFenNiuNiu.Room2.Niu9.Value < 0 || fd.YiFenNiuNiu.Room2.NiuNiu.Value < 0 ||
		fd.YiFenNiuNiu.Room3.Niu1.Value < 0 || fd.YiFenNiuNiu.Room3.Niu2.Value < 0 || fd.YiFenNiuNiu.Room3.Niu3.Value < 0 || fd.YiFenNiuNiu.Room3.Niu4.Value < 0 || fd.YiFenNiuNiu.Room3.Niu5.Value < 0 || fd.YiFenNiuNiu.Room3.Niu6.Value < 0 || fd.YiFenNiuNiu.Room3.Niu7.Value < 0 || fd.YiFenNiuNiu.Room3.Niu8.Value < 0 || fd.YiFenNiuNiu.Room3.Niu9.Value < 0 || fd.YiFenNiuNiu.Room3.NiuNiu.Value < 0 {
		return "参数不正确3"
	}
	if fd.YuEDaXiao.Room1.Value < 0 || fd.YuEDaXiao.Room2.Value < 0 || fd.YuEDaXiao.Room3.Value < 0 ||
		fd.YuEDanShuang.Room1.Value < 0 || fd.YuEDanShuang.Room2.Value < 0 || fd.YuEDanShuang.Room3.Value < 0 ||
		fd.YuEXingYun.Room1.Value < 0 || fd.YuEXingYun.Room2.Value < 0 || fd.YuEXingYun.Room3.Value < 0 ||
		fd.YuEZhuangXian.Room1.FeiHe.Value < 0 || fd.YuEZhuangXian.Room1.He.Value < 0 ||
		fd.YuEZhuangXian.Room2.FeiHe.Value < 0 || fd.YuEZhuangXian.Room2.He.Value < 0 ||
		fd.YuEZhuangXian.Room3.FeiHe.Value < 0 || fd.YuEZhuangXian.Room3.He.Value < 0 ||
		fd.YuENiuNiu.Room1.Niu1.Value < 0 || fd.YuENiuNiu.Room1.Niu2.Value < 0 || fd.YuENiuNiu.Room1.Niu3.Value < 0 || fd.YuENiuNiu.Room1.Niu4.Value < 0 || fd.YuENiuNiu.Room1.Niu5.Value < 0 || fd.YuENiuNiu.Room1.Niu6.Value < 0 || fd.YuENiuNiu.Room1.Niu7.Value < 0 || fd.YuENiuNiu.Room1.Niu8.Value < 0 || fd.YuENiuNiu.Room1.Niu9.Value < 0 || fd.YuENiuNiu.Room1.NiuNiu.Value < 0 ||
		fd.YuENiuNiu.Room2.Niu1.Value < 0 || fd.YuENiuNiu.Room2.Niu2.Value < 0 || fd.YuENiuNiu.Room2.Niu3.Value < 0 || fd.YuENiuNiu.Room2.Niu4.Value < 0 || fd.YuENiuNiu.Room2.Niu5.Value < 0 || fd.YuENiuNiu.Room2.Niu6.Value < 0 || fd.YuENiuNiu.Room2.Niu7.Value < 0 || fd.YuENiuNiu.Room2.Niu8.Value < 0 || fd.YuENiuNiu.Room2.Niu9.Value < 0 || fd.YuENiuNiu.Room2.NiuNiu.Value < 0 ||
		fd.YuENiuNiu.Room3.Niu1.Value < 0 || fd.YuENiuNiu.Room3.Niu2.Value < 0 || fd.YuENiuNiu.Room3.Niu3.Value < 0 || fd.YuENiuNiu.Room3.Niu4.Value < 0 || fd.YuENiuNiu.Room3.Niu5.Value < 0 || fd.YuENiuNiu.Room3.Niu6.Value < 0 || fd.YuENiuNiu.Room3.Niu7.Value < 0 || fd.YuENiuNiu.Room3.Niu8.Value < 0 || fd.YuENiuNiu.Room3.Niu9.Value < 0 || fd.YuENiuNiu.Room3.NiuNiu.Value < 0 {
		return "参数不正确4"
	}
	if fd.ZhuanZhangDaXiao.Room1.Value > 0.5 || fd.ZhuanZhangDaXiao.Room2.Value > 0.5 || fd.ZhuanZhangDaXiao.Room3.Value > 0.5 ||
		fd.ZhuanZhangDanShuang.Room1.Value > 0.5 || fd.ZhuanZhangDanShuang.Room2.Value > 0.5 || fd.ZhuanZhangDanShuang.Room3.Value > 0.5 ||
		fd.ZhuanZhangXingYun.Room1.Value > 0.5 || fd.ZhuanZhangXingYun.Room2.Value > 0.5 || fd.ZhuanZhangXingYun.Room3.Value > 0.5 ||
		fd.ZhuanZhangZhuangXian.Room1.FeiHe.Value > 0.5 ||
		fd.ZhuanZhangZhuangXian.Room2.FeiHe.Value > 0.5 ||
		fd.ZhuanZhangZhuangXian.Room3.FeiHe.Value > 0.5 ||
		fd.ZhuanZhangNiuNiu.Room1.Niu1.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu2.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu3.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu4.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu5.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu6.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu7.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu8.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.Niu9.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room1.NiuNiu.Value > 0.5 ||
		fd.ZhuanZhangNiuNiu.Room2.Niu1.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu2.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu3.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu4.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu5.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu6.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu7.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu8.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.Niu9.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room2.NiuNiu.Value > 0.5 ||
		fd.ZhuanZhangNiuNiu.Room3.Niu1.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu2.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu3.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu4.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu5.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu6.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu7.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu8.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.Niu9.Value > 0.5 || fd.ZhuanZhangNiuNiu.Room3.NiuNiu.Value > 0.5 {
		return "参数不正确5"
	}
	if fd.YiFenDaXiao.Room1.Value > 0.5 || fd.YiFenDaXiao.Room2.Value > 0.5 || fd.YiFenDaXiao.Room3.Value > 0.5 ||
		fd.YiFenDanShuang.Room1.Value > 0.5 || fd.YiFenDanShuang.Room2.Value > 0.5 || fd.YiFenDanShuang.Room3.Value > 0.5 ||
		fd.YiFenXingYun.Room1.Value > 0.5 || fd.YiFenXingYun.Room2.Value > 0.5 || fd.YiFenXingYun.Room3.Value > 0.5 ||
		fd.YiFenZhuangXian.Room1.FeiHe.Value > 0.5 ||
		fd.YiFenZhuangXian.Room2.FeiHe.Value > 0.5 ||
		fd.YiFenZhuangXian.Room3.FeiHe.Value > 0.5 ||
		fd.YiFenNiuNiu.Room1.Niu1.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu2.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu3.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu4.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu5.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu6.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu7.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu8.Value > 0.5 || fd.YiFenNiuNiu.Room1.Niu9.Value > 0.5 || fd.YiFenNiuNiu.Room1.NiuNiu.Value > 0.5 ||
		fd.YiFenNiuNiu.Room2.Niu1.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu2.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu3.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu4.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu5.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu6.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu7.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu8.Value > 0.5 || fd.YiFenNiuNiu.Room2.Niu9.Value > 0.5 || fd.YiFenNiuNiu.Room2.NiuNiu.Value > 0.5 ||
		fd.YiFenNiuNiu.Room3.Niu1.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu2.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu3.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu4.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu5.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu6.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu7.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu8.Value > 0.5 || fd.YiFenNiuNiu.Room3.Niu9.Value > 0.5 || fd.YiFenNiuNiu.Room3.NiuNiu.Value > 0.5 {
		return "参数不正确6"
	}
	if fd.YuEDaXiao.Room1.Value > 0.5 || fd.YuEDaXiao.Room2.Value > 0.5 || fd.YuEDaXiao.Room3.Value > 0.5 ||
		fd.YuEDanShuang.Room1.Value > 0.5 || fd.YuEDanShuang.Room2.Value > 0.5 || fd.YuEDanShuang.Room3.Value > 0.5 ||
		fd.YuEXingYun.Room1.Value > 0.5 || fd.YuEXingYun.Room2.Value > 0.5 || fd.YuEXingYun.Room3.Value > 0.5 ||
		fd.YuEZhuangXian.Room1.FeiHe.Value > 0.5 ||
		fd.YuEZhuangXian.Room2.FeiHe.Value > 0.5 ||
		fd.YuEZhuangXian.Room3.FeiHe.Value > 0.5 ||
		fd.YuENiuNiu.Room1.Niu1.Value > 0.5 || fd.YuENiuNiu.Room1.Niu2.Value > 0.5 || fd.YuENiuNiu.Room1.Niu3.Value > 0.5 || fd.YuENiuNiu.Room1.Niu4.Value > 0.5 || fd.YuENiuNiu.Room1.Niu5.Value > 0.5 || fd.YuENiuNiu.Room1.Niu6.Value > 0.5 || fd.YuENiuNiu.Room1.Niu7.Value > 0.5 || fd.YuENiuNiu.Room1.Niu8.Value > 0.5 || fd.YuENiuNiu.Room1.Niu9.Value > 0.5 || fd.YuENiuNiu.Room1.NiuNiu.Value > 0.5 ||
		fd.YuENiuNiu.Room2.Niu1.Value > 0.5 || fd.YuENiuNiu.Room2.Niu2.Value > 0.5 || fd.YuENiuNiu.Room2.Niu3.Value > 0.5 || fd.YuENiuNiu.Room2.Niu4.Value > 0.5 || fd.YuENiuNiu.Room2.Niu5.Value > 0.5 || fd.YuENiuNiu.Room2.Niu6.Value > 0.5 || fd.YuENiuNiu.Room2.Niu7.Value > 0.5 || fd.YuENiuNiu.Room2.Niu8.Value > 0.5 || fd.YuENiuNiu.Room2.Niu9.Value > 0.5 || fd.YuENiuNiu.Room2.NiuNiu.Value > 0.5 ||
		fd.YuENiuNiu.Room3.Niu1.Value > 0.5 || fd.YuENiuNiu.Room3.Niu2.Value > 0.5 || fd.YuENiuNiu.Room3.Niu3.Value > 0.5 || fd.YuENiuNiu.Room3.Niu4.Value > 0.5 || fd.YuENiuNiu.Room3.Niu5.Value > 0.5 || fd.YuENiuNiu.Room3.Niu6.Value > 0.5 || fd.YuENiuNiu.Room3.Niu7.Value > 0.5 || fd.YuENiuNiu.Room3.Niu8.Value > 0.5 || fd.YuENiuNiu.Room3.Niu9.Value > 0.5 || fd.YuENiuNiu.Room3.NiuNiu.Value > 0.5 {
		return "参数不正确7"
	}
	if fd.ZhuanZhangZhuangXian.Room1.He.Value > 8 || fd.ZhuanZhangZhuangXian.Room2.He.Value > 8 || fd.ZhuanZhangZhuangXian.Room3.He.Value > 8 ||
		fd.YiFenZhuangXian.Room1.He.Value > 8 || fd.YiFenZhuangXian.Room2.He.Value > 8 || fd.YiFenZhuangXian.Room3.He.Value > 8 ||
		fd.YuEZhuangXian.Room1.He.Value > 8 || fd.YuEZhuangXian.Room2.He.Value > 8 || fd.YuEZhuangXian.Room3.He.Value > 8 {
		return "参数不正确8"
	}
	return ""
}

func (c *UserController) sync_gamefee(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	go func() {
		fmt.Println("user 开始同步")
		var addrmap map[string]int = make(map[string]int)
		var useraddress string
		var gamefee string
		server.Db().QueryScan("select Address,GameFee from x_user where  UserId = ?", []interface{}{reqdata.UserId}, &useraddress, &gamefee)
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "Address", "=", useraddress, 0)
			users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
			for i := 0; i < len(*users); i++ {
				server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", (*users)[i]["UserId"], gamefee)
			}
		}
		start := 0
		for {
			url := fmt.Sprintf("https://apilist.tronscanapi.com/api/transfer?limit=20&sort=timestamp&address=%s&start=%d", useraddress, start)
			getresult, err := req.Get(url)
			if err != nil {
				continue
			}
			var data map[string]interface{} = make(map[string]interface{})
			bytedata, _ := getresult.ToBytes()
			json.Unmarshal(bytedata, &data)
			if data["data"] == nil {
				break
			}
			dataarr := data["data"].([]interface{})
			if len(dataarr) == 0 {
				break
			}
			for i := 0; i < len(dataarr); i++ {
				data = dataarr[i].(map[string]interface{})
				FromAddress := data["transferFromAddress"].(string)
				ToAddress := data["transferToAddress"].(string)
				exists := addrmap[FromAddress]
				if FromAddress != useraddress && exists == 0 {
					fmt.Println("trx同步:", FromAddress)
					addrmap[FromAddress] = 1
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, FromAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", FromAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
				exists = addrmap[ToAddress]
				if ToAddress != useraddress && exists == 0 {
					fmt.Println("trx同步:", ToAddress)
					addrmap[ToAddress] = 1
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, ToAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", ToAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
			}
			start += 20
		}
		start = 0
		for {
			url := fmt.Sprintf("https://apilist.tronscanapi.com/api/new/token_trc20/transfers?limit=20&start=%d&filterTokenValue=1&relatedAddress=%s", start, useraddress)
			getresult, err := req.Get(url)
			if err != nil {
				continue
			}
			var data map[string]interface{} = make(map[string]interface{})
			bytedata, _ := getresult.ToBytes()
			json.Unmarshal(bytedata, &data)
			if data["token_transfers"] == nil {
				break
			}
			dataarr := data["token_transfers"].([]interface{})
			if len(dataarr) == 0 {
				break
			}
			for i := 0; i < len(dataarr); i++ {
				data = dataarr[i].(map[string]interface{})
				FromAddress := data["from_address"].(string)
				ToAddress := data["to_address"].(string)
				exists := addrmap[FromAddress]
				if FromAddress != useraddress && exists == 0 {
					fmt.Println("usdt同步:", FromAddress)
					addrmap[FromAddress] = 1
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, FromAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", FromAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
				exists = addrmap[ToAddress]
				if ToAddress != useraddress && exists == 0 {
					fmt.Println("usdt同步:", ToAddress)
					addrmap[ToAddress] = 1
					server.Db().QueryNoResult("update x_guest set GameFee = ? where Address = ?", gamefee, ToAddress)
					{
						where := abugo.AbuDbWhere{}
						where.Add("and", "Address", "=", ToAddress, 0)
						users, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
						for j := 0; j < len(*users); j++ {
							server.Db().Conn().Query("update x_user set GameFee = ? where UserId = ?", gamefee, (*users)[j]["UserId"])
						}
					}
				}
			}
			start += 20
		}
		fmt.Println("user 同步结束")
	}()
	ctx.RespOK()
}

func (c *UserController) wallet_address(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int
		UserId   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "查"), &errcode, "权限不足") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", reqdata.UserId, "")
	data, _ := server.Db().Table("x_user_wallet").Where(where).GetList()
	ctx.RespOK(data)
}

func (c *UserController) wallet_delete(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int
		Address    string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	go func() {
		server.Db().Conn().Exec("delete from x_user_wallet where SellerId = ? and UserId = ? and Address = ?", reqdata.SellerId, reqdata.UserId, reqdata.Address)
		server.Db().Conn().Exec("delete from x_withdraw_address where UserId = ? and Address = ?", reqdata.UserId, reqdata.Address)
	}()

	go func() {
		server.Db().Conn().Exec("update x_user set Address = ? where UserId = ? and Address = ?", fmt.Sprintf("%v", reqdata.UserId), reqdata.UserId, reqdata.Address)
	}()
	ctx.RespOK()
}

func (c *UserController) set_jptype(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int
		JpType     int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.Db().Conn().Exec("update x_user set JpType = ? where SellerId = ? and UserId = ?", reqdata.JpType, reqdata.SellerId, reqdata.UserId)
	ctx.RespOK()
}

func (c *UserController) modify_vip_level(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int
		VipLevel   int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	//修改vip等级，不需要把用户踢下线
	//server.KickOutClientLogin(reqdata.UserId)
	_, err = server.Db().CallProcedure("x_admin_vip_set_level", reqdata.UserId, reqdata.VipLevel, token.Account)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("修改玩家VIP等级", ctx, reqdata)
}

func (c *UserController) modifyPwdVerificationType(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId            int
		UserId              int
		PwdVerificationType int
		GoogleCode          string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	db.User_Set_PwdVerificationType(reqdata.UserId, reqdata.PwdVerificationType)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("修改玩家PwdVerificationType", ctx, reqdata)
}

func (c *UserController) clear_withdrawliushui(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set WithdrawLiuSui=0, TotalLiuSui=0 where UserId=?", reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 清除活动流水
	err = server.Db().QueryNoResult("update x_active_reward_audit set MinLiuShui = 0  where  UserId = ?", reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("清零玩家提现流水", ctx, reqdata)
}

func (c *UserController) set_audit_amount(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId        int
		UserId          int `validate:"required"`
		AuditAmountUsdt float64
		AuditAmountTrx  float64
		GoogleCode      string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set AuditAmountUsdt = ?,AuditAmountTrx = ? where UserId = ?", reqdata.AuditAmountUsdt, reqdata.AuditAmountTrx, reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置玩家审核金额", ctx, reqdata)
}

func (c *UserController) set_test(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set IsTest = 1 where UserId = ?", reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置测试玩家", ctx, reqdata)
}

func (c *UserController) set_blockmaker(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		BlackMaker string
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set BlackMaker = ? where UserId = ?", reqdata.BlackMaker, reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("玩家出块黑名单", ctx, reqdata)
}

func (c *UserController) ingore_winjiangpei(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId          int
		UserId            int `validate:"required"`
		IgnoreWinJiangPei int //忽略盈利降赔 1是,2否
		GoogleCode        string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set IgnoreWinJiangPei = ? where UserId = ?", reqdata.IgnoreWinJiangPei, reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置玩家是否参与盈利降赔", ctx, reqdata)
}

func (c *UserController) set_audit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		AuditUsdt  float32
		AuditTrx   float32
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set AuditUsdt = ?,AuditTrx = ? where UserId = ?", reqdata.AuditUsdt, reqdata.AuditTrx, reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置玩家审核额度", ctx, reqdata)
}

func (c *UserController) set_gamelimit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int    `validate:"required"`
		GameLimit  string `validate:"required"`
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set GameLimit = ? where UserId = ?", reqdata.GameLimit, reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
	server.WriteAdminLog("设置玩家游戏限额", ctx, reqdata)
}

func (c *UserController) set_withward_needliusui(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		NeedLiuSui int `validate:"required"` //是否需要流水 1需要,2不需要
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	err = server.Db().QueryNoResult("update x_user set WithwardNeedLiuSui = ? where UserId = ?", reqdata.NeedLiuSui, reqdata.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.NeedLiuSui == 2 {
		server.Db().QueryNoResult("update x_user set WithdrawLiuSui = 0,TotalLiuSui = 0 where UserId = ?", reqdata.UserId)
	}
	ctx.RespOK()
	server.WriteAdminLog("设置提现是否需要流水", ctx, reqdata)
}

func (c *UserController) removeEmail(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		GoogleCode string
		RemoveType int // 0:解绑注册邮箱 1:解绑绑定邮箱
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "解绑绑定邮箱", "解绑用户邮箱")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	emailField := userTb.Email
	if reqdata.RemoveType == 1 {
		emailField = userTb.BindEmail
	}

	user, err := userDb.Select(userTb.UserID, emailField, userTb.AccountType).Where(userTb.UserID.Eq(int32(reqdata.UserId))).First()
	if err != nil {
		logs.Error("UserController Select", err)
		errmsg := "系统错误,请稍后再试"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			errmsg = "账号不存在"
		}
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}
	emailFieldValue := user.Email
	if reqdata.RemoveType == 1 {
		emailFieldValue = user.BindEmail
	}
	if emailFieldValue == "" {
		ctx.RespErrString(true, &errcode, "没有绑定邮箱无法解绑")
		return
	}
	//if user.AccountType == 3 {
	//	ctx.RespErrString(true, &errcode, "邮箱注册无法解绑")
	//	return
	//}
	row, err := userDb.Select(emailField).Where(userTb.UserID.Eq(user.UserID)).Update(emailField, nil)
	if err != nil || row.RowsAffected == 0 {
		logs.Error("removeEmail Update", err)
		ctx.RespErrString(true, &errcode, "解绑失败")
		return
	}
	server.WriteAdminLog("解绑绑定邮箱", ctx, reqdata)
	ctx.RespOK()
}

func (c *UserController) removePhone(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		UserId     int `validate:"required"`
		GoogleCode string
		RemoveType int // 0:解绑注册手机号 1:解绑绑定手机号
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "解绑绑定手机号", "解绑用户绑定手机号")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	phoneNumField := userTb.PhoneNum
	if reqdata.RemoveType == 1 {
		phoneNumField = userTb.BindPhoneNum
	}
	user, err := userDb.Select(userTb.UserID, phoneNumField, userTb.AccountType).Where(userTb.UserID.Eq(int32(reqdata.UserId))).First()
	if err != nil {
		logs.Error("UserController Select", err)
		errmsg := "系统错误,请稍后再试"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			errmsg = "账号不存在"
		}
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}
	phoneNumFieldValue := user.PhoneNum
	if reqdata.RemoveType == 1 {
		phoneNumFieldValue = user.BindPhoneNum
	}
	if phoneNumFieldValue == "" {
		ctx.RespErrString(true, &errcode, "没有绑定手机号无法解绑")
		return
	}

	row, err := userDb.Select(phoneNumField).Where(userTb.UserID.Eq(user.UserID)).Update(phoneNumField, nil)
	if err != nil || row.RowsAffected == 0 {
		logs.Error("removePhone Update", err)
		ctx.RespErrString(true, &errcode, "解绑失败")
		return
	}
	server.WriteAdminLog("解绑绑定手机号", ctx, reqdata)
	ctx.RespOK()
}

func (c *UserController) update_user_IsInResourceDb(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int32
	}
	errcode := 0
	//reqdata := RequestData{}
	//token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "更新用户是否在库", "改", "更新用户是否在库")
	//if token == nil {
	//	return
	//}

	xUser := server.DaoxHashGame().XUser
	db := xUser.WithContext(ctx.Gin())
	rows, err := db.Where(xUser.AccountType.Eq(5)).Where(xUser.IsInResourceDb.IsNull()).
		Where(xUser.TgChatID.Gt(0)).Where(xUser.TgUserName.Neq("")).
		Select(xUser.ID, xUser.TgChatID, xUser.TgUserName).
		Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}
	clientapi := viper.GetString("apis.clientapi")
	logs.Debug("clientapi: ", clientapi)

	for rows.Next() {
		var uid int32
		var chatId int64
		var tgUserName string
		err = rows.Scan(&uid, &chatId, &tgUserName)
		if ctx.RespErr(err, &errcode) {
			logs.Error("update_user_IsInResourceDb err 1:", err)
			return
		}
		reqData := struct {
			TgUserId int64
			TgName   string
		}{
			TgUserId: chatId,
			TgName:   tgUserName,
		}
		resp, err := req.Post(clientapi+"/user/checkUserInResourceDb", req.BodyJSON(reqData))
		if ctx.RespErr(err, &errcode) {
			logs.Error("update_user_IsInResourceDb err 2:", err)
			return
		}
		logs.Debug("update_user_IsInResourceDb resp: ", resp.String())
		res := struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
			Data struct {
				RemarkStatus int  `json:"RemarkStatus"`
				Exist        bool `json:"exist"`
				IsEffective  bool `json:"isEffective"`
			} `json:"data"`
		}{}
		err = resp.ToJSON(&res)
		if ctx.RespErr(err, &errcode) {
			logs.Error("update_user_IsInResourceDb err 3:", err)
			return
		}
		if res.Code != 200 {
			ctx.RespErrString(true, &errcode, res.Msg)
			logs.Error("update_user_IsInResourceDb err 4:", res.Msg)
			return
		}
		IsInResourceDb := 0
		if res.Data.Exist {
			IsInResourceDb = 1
		}
		_, err = db.Where(xUser.ID.Eq(uid)).Update(xUser.IsInResourceDb, IsInResourceDb)
		if ctx.RespErr(err, &errcode) {
			logs.Error("update_user_IsInResourceDb err 5:", err)
			return
		}
	}
	_ = rows.Close()
	ctx.RespOK()
}

func (c *UserController) info(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
		UserId   int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "玩家管理", "玩家列表", "增资"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Where(userTb.UserID.Eq(int32(reqdata.UserId))).Omit(userTb.Password, userTb.Token).First()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	server.WriteAdminLog("获取用户详情", ctx, reqdata)
	ctx.RespOK(user)
}

// list 用户列表查询接口
func (c *UserController) list(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 1. 参数验证和初始化
	type RequestData struct {
		Page          int    `json:"page"`
		PageSize      int    `json:"pageSize"`
		SellerId      int    `json:"sellerId"`
		UserId        int    `json:"userId"`
		Account       string `json:"account"`
		Address       string `json:"address"`
		ChannelId     int    `json:"channelId"`
		TopAgentId    int    `json:"topAgentId"`
		StartTime     int64  `json:"startTime"`
		EndTime       int64  `json:"endTime"`
		Export        int    `json:"export"`
		AgentCode     string `json:"agentCode"`
		SpecialAgent  int    `json:"specialAgent"`
		IsTest        int    `json:"isTest"`
		DeviceId      string `json:"deviceId"`
		DeviceType    string `json:"deviceType"`
		Email         string `json:"email"`
		PhoneNum      string `json:"phoneNum"`
		AgentShortUrl string `json:"agentShortUrl"`
		Fb            int    `json:"fb"`
	}

	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "玩家管理", "玩家列表", "查", "查看玩家列表")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = token.ChannelId
	}

	// 处理时间和导出参数
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 86400000
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}

	// 2. 构建查询条件
	users := "("
	if reqdata.Address != "" {
		xwhere := abugo.AbuDbWhere{}
		xwhere.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		xwhere.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		xwhere.Add("and", "Address", "=", reqdata.Address, 0)
		userwallet, _ := server.Db().Table("x_user_wallet").Where(xwhere).GetList()
		for i := 0; i < len(*userwallet); i++ {
			users += fmt.Sprint(int(abugo.GetInt64FromInterface((*userwallet)[i]["UserId"])))
			if i != len(*userwallet)-1 {
				users += ","
			}
		}
		users += ")"
	}

	// 3. 添加基本查询条件
	where := abugo.AbuDbWhere{}
	where.Add("and", "x_user.SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "x_user.UserId", "=", reqdata.UserId, 0)
	where.Add("and", "x_user.IsTest", "=", reqdata.IsTest, 0)
	where.Add("and", "x_user.Account", "=", reqdata.Account, "")
	where.Add("and", "x_user.ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "x_user.TopAgentId", "=", reqdata.TopAgentId, 0)
	where.Add("and", "x_user.AgentCode", "=", reqdata.AgentCode, "")
	where.Add("and", "x_user.SpecialAgent", "=", reqdata.SpecialAgent, 0)
	where.Add("and", "x_user.RegDeviceId", "=", reqdata.DeviceId, "")
	where.Add("and", "x_user.RegDeviceType", "=", reqdata.DeviceType, "")
	where.Add("and", "x_user.RegisterTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "x_user.RegisterTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	if reqdata.Fb == 1 {
		where.Add("and", "x_user.Fbc", "!=", "", 1)
	}

	//where.Add("and", "x_user.Email", "=", reqdata.Email, "")
	if len(reqdata.Email) > 0 {
		where.Add("and", "(x_user.Email", "=", reqdata.Email, "")
		where.Add("or", "x_user.BindEmail", "=", reqdata.Email, "")
		where.End(")")
	}

	where.Add("and", "x_user.PhoneNum", "=", reqdata.PhoneNum, "")
	where.Add("and", "x_user.AgentShortUrl", "=", reqdata.AgentShortUrl, "")
	if reqdata.Address != "" {
		if users != "()" {
			where.End("and x_user.UserId in " + users)
		} else {
			where.Add("and", "x_user.UserId", "=", -1, 0)
		}
	}

	// 4. 执行查询
	selectsql := `IF(ac.AgentCode IS NULL, "", CONCAT(IFNULL(x_user.RegUrl, ""), "/#/?AgentCode=", IFNULL(ac.agentcode, ""))) as AgentUrl`
	join := "left join x_vip_info on x_user.UserId = x_vip_info.UserId LEFT JOIN ( SELECT UserId, AgentCode, CreateTime FROM x_agent_code a1 WHERE id = (SELECT MAX(id) FROM x_agent_code a2 WHERE a2.UserId = a1.UserId) ) ac  ON x_user.UserId = ac.UserId"
	total, data := server.Db().Table("x_user").Select("x_user.*,x_vip_info.VipLevel,x_user.BonusAmount,"+selectsql).
		Join(join).Where(where).OrderBy("x_user.id desc").PageData(reqdata.Page, reqdata.PageSize)

	// 5. 处理权限和数据脱敏
	if !server.Auth2(token, "玩家管理", "玩家列表", "查看TG") {
		for i := 0; i < len(*data); i++ {
			(*data)[i]["TgUserName"] = "***"
		}
	}
	if !server.Auth2(token, "玩家管理", "玩家列表", "查看注册手机号") {
		for i := 0; i < len(*data); i++ {
			(*data)[i]["PhoneNum"] = "***"
		}
	}
	if !server.Auth2(token, "玩家管理", "玩家列表", "查看绑定手机号") {
		for i := 0; i < len(*data); i++ {
			(*data)[i]["BindPhoneNum"] = "***"
		}
	}
	if !server.Auth2(token, "玩家管理", "玩家列表", "查看注册邮箱") {
		for i := 0; i < len(*data); i++ {
			(*data)[i]["Email"] = "***"
		}
	}
	if !server.Auth2(token, "玩家管理", "玩家列表", "查看绑定邮箱") {
		for i := 0; i < len(*data); i++ {
			(*data)[i]["BindEmail"] = "***"
		}
	}

	// 6. 处理数据脱敏和清理
	isExport := server.Auth2(token, "玩家管理", "玩家列表", "导出注册邮箱")
	isExportBindEmail := server.Auth2(token, "玩家管理", "玩家列表", "导出绑定邮箱")
	isExportRegPhone := server.Auth2(token, "玩家管理", "玩家列表", "导出注册手机")
	isExportBindPhone := server.Auth2(token, "玩家管理", "玩家列表", "导出绑定手机")
	for _, item := range *data {
		// 处理AgentId，如果为空则用0代替
		if item["AgentId"] == nil {
			item["AgentId"] = int64(0)
		}

		// 处理用户等级
		item["Level"] = 0
		AgentIdObj := item["TopAgentId"]
		if AgentIdObj != nil && abugo.GetInt64FromInterface(AgentIdObj) > 0 {
			sqlex := "select ChildLevel from x_agent_child where userid = ? and child = ?"
			plevel, err := server.Db().Query(sqlex, []interface{}{AgentIdObj, item["UserId"]})
			if err != nil {
				logs.Error("err=", err)
			}
			if len(*plevel) > 0 {
				if _, exist := (*plevel)[0]["ChildLevel"]; exist {
					item["Level"] = int32(abugo.GetInt64FromInterface((*plevel)[0]["ChildLevel"])) + 1
				}
			}
		}

		// 处理邮箱脱敏
		if reqdata.Export != 1 {
			email := abugo.GetStringFromInterface(item["Email"])
			if len(email) > 6 {
				item["Email"] = utils.MaskEmail(email)
			}
			email2 := abugo.GetStringFromInterface(item["BindEmail"])
			if len(email) > 6 {
				item["BindEmail"] = utils.MaskEmail(email2)
			}
		} else {
			if !isExport {
				item["Email"] = "***"
			}
			if !isExportBindEmail {
				item["BindEmail"] = "***"
			}
			if !isExportRegPhone {
				item["PhoneNum"] = "***"
			}
			if !isExportBindPhone {
				item["BindPhoneNum"] = "***"
			}
		}

		// 移除敏感数据
		delete(item, "WalletPassword")
		delete(item, "Password")
		delete(item, "Token")
	}

	// 7. 处理IP地理位置
	// q := qqwry.NewQQwry("./config/ipdata.dat")
	for i := 0; i < len(*data); i++ {
		(*data)[i]["RegLocation"] = (*data)[i]["RegisterRegion"]
	}

	// 8. 处理返回结果
	if reqdata.Export != 1 {
		// 计算总金额
		where = abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		where.Add("and", "IsTest", "=", reqdata.IsTest, 0)
		where.Add("and", "Account", "=", reqdata.Account, "")
		where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
		where.Add("and", "TopAgentId", "=", reqdata.TopAgentId, 0)
		where.Add("and", "AgentCode", "=", reqdata.AgentCode, "")
		where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "RegDeviceId", "=", reqdata.DeviceId, "")
		where.Add("and", "RegDeviceType", "=", reqdata.DeviceType, "")
		where.Add("and", "RegisterTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("and", "RegisterTime", "<", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
		//where.Add("and", "Email", "=", reqdata.Email, "")
		if len(reqdata.Email) > 0 {
			where.Add("and", "(x_user.Email", "=", reqdata.Email, "")
			where.Add("or", "x_user.BindEmail", "=", reqdata.Email, "")
			where.End(")")
		}

		where.Add("and", "AgentShortUrl", "=", reqdata.AgentShortUrl, "")
		if reqdata.Address != "" {
			if users != "()" {
				where.End("and UserId in " + users)

			} else {
				where.Add("and", "UserId", "=", -1, 0)
			}
		}
		pcount, _ := server.Db().Table("x_user").Select("sum(Amount) as Amount, sum(BonusAmount) as BonusAmount").Where(where).GetOne()
		amount := abugo.GetStringFromInterface2((*pcount)["Amount"])
		bonusAmount := abugo.GetStringFromInterface2((*pcount)["BonusAmount"])

		// 返回正常查询结果
		ctx.Put("amount", amount)
		ctx.Put("BonusAmount", bonusAmount)
		ctx.Put("data", *data)
		ctx.Put("total", total)
		ctx.RespOK()
	} else {
		filename, err := exportExcel(*data)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	}
}

// 获取 map 数据的安全值
func getSafeValue(data map[string]interface{}, key string, defaultValue interface{}) interface{} {
	if value, exists := data[key]; exists {
		return value
	}
	return defaultValue
}

func exportExcel(data []map[string]interface{}) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"id", "玩家id", "账号", "账号类型", "玩家来源", "手机号", "邮箱",
		"TGID", "TG名称", "渠道", "钱包地址", "状态", "层级", "VIP等级",
		"平台钱包余额", "BonusAmount钱包余额", "提现流水要求", "当前提现流水", "总业绩(U/T)",
		"总投注(U/T)", "总返奖(U/T)", "顶级Id", "上级Id", "注册代理",
		"注册语言", "注册Ip", "Ip地区", "注册时间", "最后登录时间",
		"总投注天数", "推广链接", "最后下注时间", "注册设备ID", "注册设备类型",
		"登录设备ID", "登录设备类型", "登录IP", "审核金额(U/T)", "是否测试",
		"是否代理", "是否特殊代理", "代理短链接", "代理名称", "代理域名"}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(data)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := data[j]
				rowIndex := j + 2 // Excel 从 A2 开始

				// 处理账号类型
				accounttype := "正式账号"
				if abugo.GetInt64FromInterface(d["IsTest"]) == 1 {
					accounttype = "测试账号"
				}

				// 安全地获取数据，避免nil值
				betUsdt := abugo.GetFloat64FromInterface(d["BetUsdt"])
				betTrx := abugo.GetFloat64FromInterface(d["BetTrx"])
				rewardUsdt := abugo.GetFloat64FromInterface(d["RewardUsdt"])
				rewardTrx := abugo.GetFloat64FromInterface(d["RewardTrx"])
				liuSuiUsdt := abugo.GetFloat64FromInterface(d["LiuSuiUsdt"])
				liuSuiTrx := abugo.GetFloat64FromInterface(d["LiuSuiTrx"])
				auditAmountUsdt := abugo.GetFloat64FromInterface(d["AuditAmountUsdt"])
				auditAmountTrx := abugo.GetFloat64FromInterface(d["AuditAmountTrx"])

				// 处理布尔值
				isTest := "否"
				if abugo.GetInt64FromInterface(d["IsTest"]) == 1 {
					isTest = "是"
				}
				isAgent := "否"
				if abugo.GetInt64FromInterface(d["IsAgent"]) == 1 {
					isAgent = "是"
				}
				isSpecialAgent := "否"
				if abugo.GetInt64FromInterface(d["SpecialAgent"]) > 0 {
					isSpecialAgent = "是"
				}

				row := []interface{}{
					d["Id"], d["UserId"], d["Account"], accounttype,
					specialAgentName(int(abugo.GetInt64FromInterface(d["SpecialAgent"]))),
					d["PhoneNum"], d["Email"], d["TGChatId"], d["TGUserName"],
					ChannelName(int(abugo.GetInt64FromInterface(d["ChannelId"]))),
					d["Address"], getUserStateString(int(abugo.GetInt64FromInterface(d["State"]))),
					d["Level"], fmt.Sprintf("VIP%v", d["VipLevel"]),
					d["Amount"], d["BonusAmount"], d["WithdrawLiuSui"], d["TotalLiuSui"],
					fmt.Sprintf("%.2f/%.2f", liuSuiUsdt, liuSuiTrx),
					fmt.Sprintf("%.2f/%.2f", betUsdt, betTrx),
					fmt.Sprintf("%.2f/%.2f", rewardUsdt, rewardTrx),
					d["TopAgentId"], d["AgentId"], d["AgentCode"], d["RegLang"],
					d["RegisterIp"], d["RegisterRegion"], d["RegisterTime"], d["LoginTime"],
					d["BetDays"], d["AgentUrl"], d["LastBetTime"],
					d["RegDeviceId"], d["RegDeviceType"],
					d["LoginDeviceId"], d["LoginDeviceType"], d["LoginIp"],
					fmt.Sprintf("%.2f/%.2f", auditAmountUsdt, auditAmountTrx),
					isTest, isAgent, isSpecialAgent,
					d["AgentShortUrl"], d["AgentName"], d["AgentHost"],
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "export_user_" + time.Now().Format("**************") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

// AddAgent 批量新增代理用户
// 功能：根据指定的运营商、渠道和域名批量生成代理账号
// 参数：
// - SellerId: 运营商ID
// - ChannelId: 渠道ID
// - Host: 域名(必填)
// - Count: 需要生成的代理账号数量(默认1,最大100)
func (c *UserController) AddAgent(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 定义请求参数结构
	type RequestData struct {
		SellerId  int    `validate:"required"`
		Host      string `validate:"required"`
		Count     int
		ChannelId int32 `validate:"required"`
	}

	// 错误码初始化和参数验证
	errcode := 0
	reqdata := RequestData{}
	if err := ctx.RequestData(&reqdata); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 设置默认生成数量并验证范围
	if reqdata.Count <= 0 {
		reqdata.Count = 1 // 默认生成1个
	}
	if reqdata.Count > 1000 {
		ctx.RespErrString(true, &errcode, "生成数量不能超过1000")
		return
	}

	// 验证操作权限
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "独立代理", "独立代理", "增"), &errcode, "权限不足") {
		return
	}
	// 验证运营商权限
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	// 定义渠道信息结构
	type ChannelInfo struct {
		SellerId  int32 // 销售商ID
		ChannelId int32 // 渠道ID
	}
	var channelInfo ChannelInfo

	// 获取数据库表对象
	channelHostTb := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostTb.WithContext(ctx.Gin())
	xChannel := server.DaoxHashGame().XChannel

	// 验证域名有效性并获取对应的销售商和渠道信息
	err := channelHostDb.Select(xChannel.SellerID, xChannel.ChannelID).
		Where(channelHostTb.Host.Eq(reqdata.Host)).           // 匹配域名
		Where(channelHostTb.State.Eq(1)).                     // 域名状态为启用
		Where(channelHostTb.ChannelID.Eq(reqdata.ChannelId)). // 匹配渠道ID
		Join(xChannel, xChannel.ChannelID.EqCol(channelHostTb.ChannelID)).
		Scan(&channelInfo)

	// 处理查询错误
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	// 验证域名和渠道是否有效
	if channelInfo.SellerId <= 0 || channelInfo.ChannelId <= 0 {
		ctx.RespErrString(true, &errcode, "域名不存在或已关闭")
		return
	}

	// 检查域名是否已被其他代理使用
	agentIndependenceTb := server.DaoxHashGame().XAgentIndependence
	agentIndependenceDb := agentIndependenceTb.WithContext(ctx.Gin())
	existingAgent, err := agentIndependenceDb.Where(agentIndependenceTb.Host.Eq(reqdata.Host)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if existingAgent != nil {
		ctx.RespErrString(true, &errcode, "此域名已经绑定过独立代理") // 修改错误提示,与checkAgentHost保持一致
		return
	}

	// 定义代理账号信息结构
	type AgentInfo struct {
		UserId        int32  `json:"UserId"`        // 用户ID
		Account       string `json:"Account"`       // 账号
		Password      string `json:"Password"`      // 密码
		PromotionHost string `json:"PromotionHost"` // 推广链接
		AgentHost     string `json:"AgentHost"`     // 代理域名
		AgentShortUrl string `json:"AgentShortUrl"` // 代理短链接
		AgentName     string `json:"AgentName"`     // 代理名称
	}
	// 初始化结果数组
	results := make([]AgentInfo, 0, reqdata.Count)

	// 循环生成指定数量的代理账号
	for i := 0; i < reqdata.Count; i++ {
		// 生成随机账号(16位)和密码(9位)
		account := xgo.RandomString(16)
		password, err := xgo.GenerateRandomPassword(9)
		if err != nil {
			ctx.RespErrString(true, &errcode, "生成密码错误，请稍后重试")
			return
		}
		hashPwd := xgo.Md5(password)

		// 构造注册请求参数
		registerReq := struct {
			SellerId    int32  `validate:"required"` // 销售商ID
			Account     string `validate:"required"` // 账号
			Password    string // 密码
			AccountType int    // 账号类型(6:代理账号)
			AgentCode   string // 代理码
			Validate    string `validate:"required"` // 验证码
			Host        string // 域名
			Lang        string // 语言
		}{
			Account:     account,
			Password:    hashPwd,
			AccountType: 6,
			AgentCode:   "",
			SellerId:    channelInfo.SellerId,
			Validate:    "123",
			Host:        reqdata.Host,
			Lang:        "",
		}

		// 调用注册API
		resp, err := req.Post(server.ClientApi()+"/user/register", req.BodyJSON(registerReq))
		if err != nil {
			logs.Error("AddAgent call clientapi err:", err)
			ctx.RespErrString(true, &errcode, "注册失败,请稍后再试")
			return
		}

		// 定义注册响应结构
		type RegisterResponse struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
			Data struct {
				Errcode   int    `json:"errcode,omitempty"`
				Errmsg    string `json:"errmsg,omitempty"`
				UserId    int32  `json:"UserId"`
				AgentCode string `json:"AgentCode"`
				Account   string `json:"Account"`
			} `json:"data,omitempty"`
		}

		// 解析响应数据
		var res RegisterResponse
		if err := resp.ToJSON(&res); err != nil {
			logs.Error("AddAgent resp.ToJSON err:", err)
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}

		// 验证注册结果
		if res.Code != 200 {
			ctx.RespErrString(true, &errcode, res.Msg)
			return
		}
		if res.Data.Errcode != 0 {
			ctx.RespErrString(true, &errcode, res.Data.Errmsg)
			return
		}
		if res.Data.UserId <= 0 || res.Data.AgentCode == "" || res.Data.Account == "" {
			ctx.RespErrString(true, &errcode, "注册失败")
			return
		}

		// 将新生成的代理账号信息添加到结果列表
		results = append(results, AgentInfo{
			UserId:        res.Data.UserId,
			Account:       res.Data.Account,
			Password:      password, // 返回明文密码
			PromotionHost: fmt.Sprintf(utils.TopAgentPromotionHost, reqdata.Host, res.Data.AgentCode),
			AgentHost:     "", // 新增字段，初始为空
			AgentShortUrl: "", // 新增字段，初始为空
			AgentName:     "", // 新增字段，初始为空
		})
	}

	// 在设置返回数据前，批量更新IsAgent字段
	if len(results) > 0 {
		// 收集所有新创建的用户ID
		var userIds []int32
		for _, result := range results {
			userIds = append(userIds, result.UserId)
		}

		// 批量更新IsAgent字段
		xUserTb := server.DaoxHashGame().XUser
		xUserDb := xUserTb.WithContext(ctx.Gin())
		_, err = xUserDb.Where(xUserTb.UserID.In(userIds...)).
			Select(xUserTb.IsAgent).
			Update(xUserTb.IsAgent, 1)

		if err != nil {
			logs.Error("AddAgent batch update IsAgent failed:", err)
			ctx.RespErrString(true, &errcode, "更新代理状态失败")
			return
		}
	}

	// 设置返回数据
	ctx.Put("total", len(results)) // 总数
	ctx.Put("data", results)       // 代理账号列表
	ctx.RespOK()
	// 记录操作日志
	server.WriteAdminLog("批量新增代理用户", ctx, reqdata)
}

// validateAndFormatURL 验证并格式化URL
// 功能：
// 1. 验证URL格式的有效性
// 2. 确保URL包含必要的协议前缀和主机名
// 3. 清理URL中的特殊字符
// 参数：
//   - rawURL: 原始URL字符串
//
// 返回：
//   - string: 格式化后的URL
//   - error: 验证失败时返回错误信息
func validateAndFormatURL(rawURL string) (string, error) {
	// 确保URL有协议前缀（http或https）
	// 如果没有协议前缀，默认添加https://
	if !strings.HasPrefix(rawURL, "http://") && !strings.HasPrefix(rawURL, "https://") {
		rawURL = "https://" + rawURL
	}

	// 解析URL字符串为URL对象
	// 这一步会验证URL的基本格式是否正确
	u, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}

	// 验证URL是否包含有效的主机名
	// 没有主机名的URL是无效的
	if u.Host == "" {
		return "", fmt.Errorf("invalid host in URL")
	}

	// 清理URL中的特殊字符
	// 1. 移除首尾的空白字符
	// 2. 将URL中的空格替换为%20
	cleanURL := strings.TrimSpace(u.String())
	cleanURL = strings.ReplaceAll(cleanURL, " ", "%20")

	return cleanURL, nil
}

// generatePromotionUrls 批量生成推广短链接
// 功能：
// 1. 支持批量为多个用户生成推广短链接(最多10个用户)
// 2. 根据用户的代理码生成唯一的推广链接
// 3. 生成后自动保存到用户表的AgentShortUrl字段
func (c *UserController) generatePromotionUrls(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 定义请求参数结构
	type RequestData struct {
		SellerId    int      `validate:"required"` // 运营商ID
		ChannelId   int32    `validate:"required"` // 渠道ID
		UserIds     []int32  `validate:"required"` // 用户ID列表，支持批量生成
		DefaultUrls []string `validate:"required"` // 默认推广链接模板列表
		Export      int      // 是否导出，1=导出
	}

	// 定义返回结果结构
	type UrlResult struct {
		UserId      int32  `json:"userId"`          // 用户ID
		OriginalUrl string `json:"originalUrl"`     // 原始推广链接
		ShortUrl    string `json:"shortUrl"`        // 生成的短链接
		Error       string `json:"error,omitempty"` // 错误信息，成功时为空
	}

	// 解析并验证请求参数
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	if ctx.RespErrString(len(reqdata.DefaultUrls) == 0, &errcode, "默认推广链接不能为空") {
		return
	}

	// 查询所有用户信息
	xUserTb := server.DaoxHashGame().XUser
	xUserDb := xUserTb.WithContext(ctx.Gin())
	users, err := xUserDb.Where(xUserTb.UserID.In(reqdata.UserIds...)).Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "获取用户信息失败")
		return
	}

	// 创建用户映射，方便后续查找
	userMap := make(map[int32]*model.XUser)
	for _, user := range users {
		userMap[user.UserID] = user
	}

	// 初始化结果和请求数组
	var results = make([]UrlResult, len(reqdata.UserIds)) // 预分配空间，保持顺序
	var requests []utils.CreateBitlinkRequest
	var requestMap = make(map[int]int)
	client := utils.NewClient()

	// 先初始化所有结果
	for i, userId := range reqdata.UserIds {
		results[i] = UrlResult{
			UserId: userId,
		}
	}

	// 处理每个用户的链接生成
	for i, userId := range reqdata.UserIds {
		// 获取用户信息
		user, exists := userMap[userId]
		if !exists {
			results[i].Error = "用户不存在"
			continue
		}

		// 验证代理码
		if user.AgentCode == "" {
			results[i].Error = "用户代理码为空"
			continue
		}

		// 获取基础URL模板
		var baseUrl string
		if i < len(reqdata.DefaultUrls) {
			baseUrl = reqdata.DefaultUrls[i]
		} else if len(reqdata.DefaultUrls) > 0 {
			baseUrl = reqdata.DefaultUrls[len(reqdata.DefaultUrls)-1]
		} else {
			results[i].Error = "没有可用的推广链接模板"
			continue
		}

		// 从模板中提取基础URL（移除原有的AgentCode）
		if idx := strings.Index(strings.ToLower(baseUrl), "agentcode="); idx != -1 {
			baseUrl = baseUrl[:strings.LastIndex(baseUrl, "AgentCode=")]
			baseUrl = strings.TrimSuffix(baseUrl, "&")
			baseUrl = strings.TrimSuffix(baseUrl, "?")
			baseUrl = strings.TrimSuffix(baseUrl, "#/")
			baseUrl = strings.TrimSuffix(baseUrl, "#")
			baseUrl = strings.TrimSuffix(baseUrl, "/")
		}

		// 构造用户的专属推广链接
		userPromotionUrl := baseUrl
		if !strings.HasPrefix(strings.ToLower(userPromotionUrl), "http://") && !strings.HasPrefix(strings.ToLower(userPromotionUrl), "https://") {
			userPromotionUrl = "https://" + userPromotionUrl
		}
		userPromotionUrl = strings.TrimSuffix(userPromotionUrl, "/") + "/#/?AgentCode=" + user.AgentCode

		// 设置原始链接为模板链接（用于显示）
		results[i].OriginalUrl = reqdata.DefaultUrls[i]

		// 验证并格式化URL
		formattedURL, err := validateAndFormatURL(userPromotionUrl) // 使用用户的专属推广链接
		if err != nil {
			results[i].Error = "无效的URL格式"
			continue
		}

		// 添加到生成请求列表
		requestIndex := len(requests)
		requests = append(requests, utils.CreateBitlinkRequest{
			LongURL: formattedURL, // 使用用户的专属推广链接生成短链接
		})
		requestMap[requestIndex] = i
	}

	// 批量生成短链接
	if len(requests) > 0 {
		// 计算需要处理的批次数
		totalRequests := len(requests)
		batchCount := (totalRequests + batchSize - 1) / batchSize

		// 分批处理请求
		for batchIndex := 0; batchIndex < batchCount; batchIndex++ {
			// 计算当前批次的起始和结束索引
			start := batchIndex * batchSize
			end := start + batchSize
			if end > totalRequests {
				end = totalRequests
			}

			// 获取当前批次的请求
			batchRequests := requests[start:end]

			// 处理当前批次
			batchResults := client.BatchCreateBitlinks(batchRequests, utils.DefaultBatchOptions())

			// 处理当前批次的结果
			for i, result := range batchResults {
				globalIndex := start + i // 计算在整体请求中的索引
				if resultIndex, ok := requestMap[globalIndex]; ok {
					// 添加日志以跟踪短链接生成结果
					logs.Info("处理短链接结果: index=%d, userId=%v", resultIndex, results[resultIndex].UserId)

					if result.Error != nil {
						logs.Error("生成短链接失败: %v", result.Error)
						results[resultIndex].Error = result.Error.Error()
						results[resultIndex].ShortUrl = requests[globalIndex].LongURL
					} else if result.Result == nil {
						logs.Error("生成结果为空")
						results[resultIndex].Error = "生成失败"
						results[resultIndex].ShortUrl = requests[globalIndex].LongURL
					} else {
						logs.Info("生成短链接成功: %s", result.Result.Link)
						// 生成成功，保存到数据库
						results[resultIndex].ShortUrl = result.Result.Link

						// 保存短链接到用户表
						if result.Result != nil && result.Result.Link != "" {
							err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
								userTb := tx.XUser
								userDb := tx.XUser.WithContext(context.Background())

								result, err := userDb.Where(userTb.UserID.Eq(results[resultIndex].UserId)).
									Select(userTb.AgentShortURL).
									Updates(map[string]interface{}{
										"AgentShortUrl": result.Result.Link,
									})
								if err != nil {
									return fmt.Errorf("更新用户短链接失败: %v", err)
								}
								if result.RowsAffected == 0 {
									exists, err := userDb.Where(userTb.UserID.Eq(results[resultIndex].UserId)).Count()
									if err != nil {
										return fmt.Errorf("检查用户失败: %v", err)
									}
									if exists == 0 {
										return fmt.Errorf("用户不存在: %d", results[resultIndex].UserId)
									}
								}
								// 添加保存成功的日志
								logs.Info("短链接保存成功: userId=%v, shortUrl=%v",
									results[resultIndex].UserId, results[resultIndex].ShortUrl)
								return nil
							})

							if err != nil {
								logs.Error("保存短链接失败: userId=%v, shortUrl=%v, error=%v",
									results[resultIndex].UserId, results[resultIndex].ShortUrl, err)
								results[resultIndex].Error = "保存短链接失败"
								results[resultIndex].ShortUrl = requests[globalIndex].LongURL
							}
						}
					}
				}

				// 记录处理进度
				logs.Info("短链接生成进度: %d/%d", (batchIndex+1)*len(batchResults), totalRequests)

				// 在批次之间添加短暂延时，避免请求过于密集
				if batchIndex < batchCount-1 {
					time.Sleep(100 * time.Millisecond)
				}
			}
		}
	}
	// 返回结果
	ctx.Put("total", len(results))
	ctx.Put("data", results)
	ctx.RespOK()
	// 记录操作日志
	server.WriteAdminLog("生成推广短链接", ctx, reqdata)
}

// SaveAgentInfo 保存代理信息
// 功能：
// 1. 更新用户表中的代理名称和短链接
// 2. 更新代理表中的代理名称和域名
// 3. 使用事务确保数据一致性
// 参数说明：
//   - ctx: HTTP上下文，包含请求和响应信息
//
// 请求参数：
//   - UserId: 用户ID
//   - AgentName: 代理名称
//   - AgentHost: 代理域名
//   - AgentShortUrl: 代理短链接
func (c *UserController) SaveAgentInfo(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 定义请求参数结构
	type RequestData struct {
		SellerId      int    `validate:"required"` // 运营商ID
		UserId        int    `validate:"required"` // 用户ID
		OriginalUrl   string // 默认推广链接
		AgentHost     string // 专属域名
		AgentShortUrl string // 短链接
		AgentName     string // 代理名称
		Export        int    // 是否导出，1=导出
	}

	// 解析并验证请求参数
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "独立代理", "独立代理", "改", "修改代理信息")
	if token == nil {
		return
	}

	// 验证运营商权限
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	// 清理域名字符串
	reqdata.AgentHost = strings.TrimSpace(reqdata.AgentHost)

	// 开始事务
	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		// 先查询用户是否存在且是代理用户
		userTb := tx.XUser
		userDb := tx.XUser.WithContext(context.Background())
		user, err := userDb.Where(userTb.UserID.Eq(int32(reqdata.UserId))).First()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("用户不存在")
			}
			return fmt.Errorf("查询用户信息失败: %v", err)
		}

		// 验证用户是否为代理用户
		if user.AccountType != 6 {
			return fmt.Errorf("该用户不是代理用户")
		}

		// 更新用户表代理信息
		if reqdata.AgentName != "" || reqdata.AgentShortUrl != "" {
			result, err := userDb.Where(userTb.UserID.Eq(int32(reqdata.UserId))).
				Select(userTb.AgentShortURL). // 使用生成的字段选择器
				Updates(map[string]interface{}{
					"AgentShortUrl": reqdata.AgentShortUrl,
					"AgentName":     reqdata.AgentName,
				})
			if err != nil {
				return fmt.Errorf("更新用户代理信息失败: %v", err)
			}

			// 如果更新失败，检查用户是否存在
			if result.RowsAffected == 0 {
				// 检查用户是否存在
				exists, err := userDb.Where(userTb.UserID.Eq(int32(reqdata.UserId))).Count()
				if err != nil {
					return fmt.Errorf("检查用户失败: %v", err)
				}
				if exists == 0 {
					return fmt.Errorf("用户不存在: %d", reqdata.UserId)
				}
				// 用户存在但没有行被更新，说明值没有变化，这是正常的
			}

			// 同步更新游戏表中的代理名称
			if reqdata.AgentName != "" {
				_, err := tx.XGame.WithContext(context.Background()).
					Where(tx.XGame.TopAgentID.Eq(int32(reqdata.UserId))).
					UpdateSimple(tx.XGame.AgentName.Value(reqdata.AgentName))
				if err != nil {
					return fmt.Errorf("更新游戏表代理名称失败: %v", err)
				}
			}
		}

		// 更新代理表信息
		if reqdata.AgentName != "" || reqdata.AgentHost != "" {
			agentTb := tx.XAgentIndependence
			agentDb := tx.XAgentIndependence.WithContext(context.Background())

			// 先检查代理记录是否存在
			exists, err := agentDb.Where(agentTb.UserID.Eq(int32(reqdata.UserId))).Count()
			if err != nil {
				return fmt.Errorf("查询代理信息失败: %v", err)
			}

			if exists > 0 {
				// 构建更新数据
				agentUpdates := make(map[string]interface{})
				if reqdata.AgentName != "" {
					agentUpdates["agent_name"] = reqdata.AgentName
				}
				if reqdata.AgentHost != "" {
					agentUpdates["host"] = reqdata.AgentHost
				}

				// 更新代理表
				if len(agentUpdates) > 0 {
					_, err = agentDb.Where(agentTb.UserID.Eq(int32(reqdata.UserId))).Updates(agentUpdates)
					if err != nil {
						return fmt.Errorf("更新代理表信息失败: %v", err)
					}
				}
			}
		}

		return nil
	})

	// 处理事务执行结果
	if err != nil {
		logs.Error("SaveAgentInfo failed: %v", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	// 返回成功响应
	ctx.RespOK()
	// 记录操作日志
	server.WriteAdminLog("保存代理信息", ctx, reqdata)
}

// AgentUserInfo 代理用户信息
type AgentUserInfo struct {
	UserId    int32  `json:"userId"`          // 用户ID
	SellerId  int    `json:"sellerId"`        // 运营商ID
	ChannelId int32  `json:"channelId"`       // 渠道ID
	Error     string `json:"error,omitempty"` // 错误信息
}

// ExportAgentUrls 导出代理推广链接
func (c *UserController) ExportAgentUrls(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 定义请求参数结构
	type RequestData struct {
		Data []struct {
			UserId        int32  `json:"userId"`        // 用户ID
			Account       string `json:"account"`       // 账号
			Password      string `json:"password"`      // 密码
			PromotionHost string `json:"promotionHost"` // 默认推广链接
			AgentHost     string `json:"agentHost"`     // 绑定专属域名
			AgentShortUrl string `json:"agentShortUrl"` // 短链接
			AgentName     string `json:"agentName"`     // 代理名称
		} `json:"data" validate:"required"`
	}

	// 解析并验证请求参数
	errcode := 0
	reqdata := RequestData{}
	if err := ctx.RequestData(&reqdata); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 创建Excel对象
	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_agent_urls_%s", time.Now().Format("**************")))
	defer xlsx.Close()
	xlsx.Open()

	// 设置表头
	xlsx.SetTitle("UserId", "用户ID")
	xlsx.SetTitle("Account", "账号")
	xlsx.SetTitle("Password", "密码")
	xlsx.SetTitle("PromotionHost", "默认推广链接")
	xlsx.SetTitle("AgentHost", "绑定专属域名")
	xlsx.SetTitle("AgentShortUrl", "短链接")
	xlsx.SetTitle("AgentName", "代理名称")
	xlsx.SetTitleStyle()

	// 设置列宽
	xlsx.SetColumnWidth("UserId", 15)
	xlsx.SetColumnWidth("Account", 25)
	xlsx.SetColumnWidth("Password", 20)
	xlsx.SetColumnWidth("PromotionHost", 60)
	xlsx.SetColumnWidth("AgentHost", 30)
	xlsx.SetColumnWidth("AgentShortUrl", 40)
	xlsx.SetColumnWidth("AgentName", 25)

	// 写入数据
	for i, item := range reqdata.Data {
		xlsx.SetValue("UserId", item.UserId, int64(i+2))
		xlsx.SetValue("Account", item.Account, int64(i+2))
		xlsx.SetValue("Password", item.Password, int64(i+2))
		xlsx.SetValue("PromotionHost", item.PromotionHost, int64(i+2))
		xlsx.SetValue("AgentHost", item.AgentHost, int64(i+2))
		xlsx.SetValue("AgentShortUrl", item.AgentShortUrl, int64(i+2))
		xlsx.SetValue("AgentName", item.AgentName, int64(i+2))
	}

	// 生成文件
	filePath, err := xlsx.ProduceFile()
	if err != nil {
		logs.Error("生成Excel文件失败:", err)
		ctx.RespErrString(true, &errcode, "导出文件失败")
		return
	}

	ctx.Put("filename", "/exports/"+path.Base(filePath))
	ctx.RespOK()
}

func (c *UserController) checkVerifyCode(ctx *abugo.AbuHttpContent) {
	// 定义请求参数结构
	type RequestData struct {
		Email    string
		Page     int
		PageSize int
	}

	// 解析并验证请求参数
	errcode := 0
	reqdata := RequestData{}
	if err := ctx.RequestData(&reqdata); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	xVerify := server.DaoxHashGame().XVerify
	db := xVerify.WithContext(nil)

	query := db.Select(xVerify.ALL)
	if reqdata.Email != "" {
		query.Where(xVerify.Account.Eq(reqdata.Email))
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	var result []*model.XVerify
	total, err := query.Order(xVerify.CreateTime.Desc()).ScanByPage(&result, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.Put("data", result)
	ctx.Put("total", total)
	ctx.RespOK()

}
