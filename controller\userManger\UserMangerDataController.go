package userManger

import (
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	//"github.com/beego/beego/logs"
	//"github.com/go-resty/resty/v2"
	//"github.com/spf13/viper"
)

//  用户玩家分类数据，用户标签  ，用户输赢数据等

type UserManagerDataController struct{}

func (c *UserManagerDataController) Init() {
	group := server.Http().NewGroup("/api/user/manager")
	{
		group.Post("/label_config/list", c.listByLabelConfig)
		group.Post("/label_config/create", c.createByLabelConfig)
		group.Post("/label_config/update", c.updateByLabelConfig)
		group.Post("/label_config/delete", c.deleteByLabelConfig)
	}
}

func (c *UserManagerDataController) listByLabelConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page      int   `json:"page"`
		PageSize  int   `json:"page_size"` //SellerID int   `json:"seller_id"`
		LabelID   int64 `json:"label_id"`
		LabelType int32 `json:"label_type"`
		IsGroup   int32 `json:"is_group"`
	}
	type Result struct {
		model.XAdsUserLabelConfig
		SellerName string `json:"seller_name"`
	}
	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "用户管理", "用户分类", "查", "查询用户分类标签配置")
	if token == nil {
		return
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	var res []*Result
	dao := server.DaoxHashGame().XAdsUserLabelConfig
	query := dao.WithContext(nil).Select(dao.ALL)
	if req.LabelID != 0 {
		query.Where(dao.ID.Eq(req.LabelID))
	}
	if req.LabelType != 0 {
		query.Where(dao.LabelType.Eq(req.LabelType))
	}
	if req.IsGroup != 0 {
		query.Where(dao.LabelType.Eq(1), dao.Name.Eq(""))
	} else {
		query.Where(dao.Name.Neq(""))
	}
	//xSeller := server.DaoxHashGame().XSeller
	total, err := query.WithContext(nil).
		Select(dao.ALL).
		Order(dao.ID.Desc()).
		ScanByPage(&res, offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	ctx.Put("total", total)
	ctx.Put("results", res)
	ctx.RespOK()
}

func (c *UserManagerDataController) createByLabelConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	req := model.XAdsUserLabelConfig{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "用户管理", "用户分类", "增", "创建用户分类标签配置")
	if token == nil {
		return
	}
	err := server.DaoxHashGame().XAdsUserLabelConfig.WithContext(nil).Create(&req)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

func (c *UserManagerDataController) updateByLabelConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	req := model.XAdsUserLabelConfig{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "用户管理", "用户分类", "改", "更新用户分类标签配置")
	if token == nil {
		return
	}
	err := server.DaoxHashGame().XAdsUserLabelConfig.
		WithContext(nil).
		Where(server.DaoxHashGame().XAdsUserLabelConfig.ID.Eq(req.ID)).
		Save(&req)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

func (c *UserManagerDataController) deleteByLabelConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	req := model.XAdsUserLabelConfig{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "用户管理", "用户分类", "删", "删除用户分类标签配置")
	if token == nil {
		return
	}
	_, err := server.DaoxHashGame().XAdsUserLabelConfig.
		WithContext(nil).
		Where(server.DaoxHashGame().XAdsUserLabelConfig.ID.Eq(req.ID)).
		Delete()
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}
