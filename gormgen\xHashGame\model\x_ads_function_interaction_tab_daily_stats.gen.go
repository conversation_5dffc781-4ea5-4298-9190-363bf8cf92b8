// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsFunctionInteractionTabDailyStat = "x_ads_function_interaction_tab_daily_stats"

// XAdsFunctionInteractionTabDailyStat 功能交互TAB每日统计表
type XAdsFunctionInteractionTabDailyStat struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                        // 主键ID
	SellerID      int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                       // 运营商ID
	ChannelID     int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                      // 渠道ID
	TopAgentID    int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                // 顶级代理ID
	StatDate      time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                               // 统计日期
	ButtonName    string    `gorm:"column:button_name;not null;comment:按钮名称" json:"button_name"`                           // 按钮名称
	TabName       string    `gorm:"column:tab_name;not null;comment:TAB名称" json:"tab_name"`                                // TAB名称
	ClickCountPc  int32     `gorm:"column:click_count_pc;not null;comment:点击次数 pc" json:"click_count_pc"`                  // 点击次数 pc
	ClickCountH5  int32     `gorm:"column:click_count_h5;not null;comment:点击次数 h5" json:"click_count_h5"`                  // 点击次数 h5
	ClickCountAll int32     `gorm:"column:click_count_all;not null;comment:点击次数 all" json:"click_count_all"`               // 点击次数 all
	TabRatePc     float32   `gorm:"column:tab_rate_pc;not null;comment:占比: 与所有tab之和的比值 pc" json:"tab_rate_pc"`             // 占比: 与所有tab之和的比值 pc
	TabRateH5     float32   `gorm:"column:tab_rate_h5;not null;comment:占比: 与所有tab之和的比值 h5" json:"tab_rate_h5"`             // 占比: 与所有tab之和的比值 h5
	TabRateAll    float32   `gorm:"column:tab_rate_all;not null;comment:占比: 与所有tab之和的比值" json:"tab_rate_all"`              // 占比: 与所有tab之和的比值
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XAdsFunctionInteractionTabDailyStat's table name
func (*XAdsFunctionInteractionTabDailyStat) TableName() string {
	return TableNameXAdsFunctionInteractionTabDailyStat
}
