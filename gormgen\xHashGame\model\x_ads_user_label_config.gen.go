// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsUserLabelConfig = "x_ads_user_label_config"

// XAdsUserLabelConfig 用户标签
type XAdsUserLabelConfig struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                 // id
	Name       string    `gorm:"column:name;comment:名称" json:"name"`                                           // 名称
	GroupName  string    `gorm:"column:group_name;comment:群组名称" json:"group_name"`                             // 群组名称
	LabelType  int32     `gorm:"column:label_type;comment:标签类型1：群组标签2：普通标签，" json:"label_type"`                // 标签类型1：群组标签2：普通标签，
	JSON       string    `gorm:"column:json;comment:数据字段" json:"json"`                                         // 数据字段
	Remark     string    `gorm:"column:remark;comment:备注" json:"remark"`                                       // 备注
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建日期" json:"create_time"` // 创建日期
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XAdsUserLabelConfig's table name
func (*XAdsUserLabelConfig) TableName() string {
	return TableNameXAdsUserLabelConfig
}
