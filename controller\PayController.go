package controller

import (
	"context"
	"strings"
	"xserver/abugo"
	"xserver/server"

	"github.com/zhms/xgo/xgo"
)

type PayController struct {
}

func (this *PayController) Init() {
	server.Http().Post("/api/pay/get_symbol_list", this.get_symbol_list)
	server.Http().Post("/api/pay/modify_symbol", this.modify_symbol)
	server.Http().Post("/api/pay/modify_symbol_rate", this.modify_symbol_rate)
	server.Http().Post("/api/pay/get_changelog", this.get_changelog)
	server.Http().Post("/api/pay/get_pay_method", this.get_pay_method)
	server.Http().Post("/api/pay/add_pay_method", this.add_pay_method)
	server.Http().Post("/api/pay/modify_pay_method", this.modify_pay_method)
	server.Http().Post("/api/pay/delete_pay_method", this.delete_pay_method)
	server.Http().Post("/api/pay/symbol_update_status", this.symbol_update_status)
	server.Http().Post("/api/pay/get_currency", this.get_currency)
}

func (c *PayController) get_symbol_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		Symbol    string
		State     int32
		ShowIndex int32
		FType     int32
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "财务管理", "币种管理", "查", "查看充提币种")
	if token == nil {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	dao := server.DaoxHashGame().XFinanceSymbol
	db := dao.WithContext(ctx.Gin())
	if reqdata.Symbol != "" {
		db = db.Where(dao.Symbol.Eq(reqdata.Symbol))
	}
	if reqdata.SellerId != 0 {
		db = db.Where(dao.SellerID.Eq(int32(reqdata.SellerId)))
	}
	if reqdata.State != 0 {
		db = db.Where(dao.State.Eq(reqdata.State))
	}
	if reqdata.ShowIndex != 0 {
		db = db.Where(dao.ShowIndex.Eq(reqdata.ShowIndex))
	}
	if reqdata.FType != 0 {
		db = db.Where(dao.FType.Eq(reqdata.FType))
	}
	db = db.Order(dao.Sort.Desc())
	list, count, err := db.FindByPage(offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", count)
	ctx.Put("url", server.ImageUrl())
	ctx.RespOK()
}

func (c *PayController) modify_symbol(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId        int
		Symbol          string  `validate:"required"`
		RateSource      int     `validate:"required"` //汇率来源 1币安
		RechargeFixType int     `validate:"required"` //充值汇率偏差类型 1固定 2比例
		RechargeFix     float32 //充值汇率偏差
		WithwardFixType int     `validate:"required"` //提现汇率偏差类型 1固定 2比例
		WithwardFix     float32 //提现汇率偏差
		State           int     `validate:"required"` //状态 1启用 2禁用
		AutoState       int     `validate:"required"` //是否开启自动汇率 1开启 2关闭
		Icon            string  `validate:"required"` //图标
		Sort            int
		GoogleCode      string
		RechargeMin     float32 //最小充值
		RechargeMax     float32 //最大充值
		WithwardMin     float32 //最小提现
		WithwardMax     float32 //最大提现
		RechargeRateEx  float32 //手动设置充值汇率
		WithwardRateEx  float32 //手动设置提现汇率
		ShowIndex       int
		NetJson         string // 网络协议
		ColdWallet      string // 冷钱包
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.RechargeFix < 0 {
		ctx.RespErrString(true, &errcode, "充值汇率偏差错误")
		return
	}
	if reqdata.WithwardFix < 0 {
		ctx.RespErrString(true, &errcode, "提现汇率偏差错误")
		return
	}
	symbol := strings.ToUpper(reqdata.Symbol)
	if symbol == "TRX" {
		if reqdata.RechargeRateEx < 0.01 || reqdata.RechargeRateEx > 0.5 {
			ctx.RespErrString(true, &errcode, "手动设置充值汇率错误")
			return
		}
		if reqdata.WithwardRateEx < 0.01 || reqdata.WithwardRateEx > 0.5 {
			ctx.RespErrString(true, &errcode, "手动设置提现汇率错误")
			return
		}
	} else if symbol == "USDT" {
		if reqdata.RechargeRateEx < 0.1 || reqdata.RechargeRateEx > 1 {
			ctx.RespErrString(true, &errcode, "手动设置充值汇率错误")
			return
		}
		if reqdata.WithwardRateEx < 0.1 || reqdata.WithwardRateEx > 1 {
			ctx.RespErrString(true, &errcode, "手动设置提现汇率错误")
			return
		}
	} else if symbol == "CNY" {
		if reqdata.RechargeRateEx < 6 || reqdata.RechargeRateEx > 8 {
			ctx.RespErrString(true, &errcode, "手动设置充值汇率错误")
			return
		}
		if reqdata.WithwardRateEx < 6 || reqdata.WithwardRateEx > 8 {
			ctx.RespErrString(true, &errcode, "手动设置提现汇率错误")
			return
		}
	} else if symbol == "BRL" {
		if reqdata.RechargeRateEx < 4 || reqdata.RechargeRateEx > 5 {
			ctx.RespErrString(true, &errcode, "手动设置充值汇率错误")
			return
		}
		if reqdata.WithwardRateEx < 4 || reqdata.WithwardRateEx > 5 {
			ctx.RespErrString(true, &errcode, "手动设置提现汇率错误")
			return
		}
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "财务管理", "币种管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	objdata := xgo.ObjectToMap(reqdata)
	delete(*objdata, "SellerId")
	delete(*objdata, "GoogleCode")
	(*objdata)["UpdateTime"] = xgo.GetLocalTime()
	(*objdata)["UpdateAccount"] = token.Account
	(*objdata)["Symbol"] = strings.ToUpper(xgo.ToString((*objdata)["Symbol"]))
	server.XDb().Table("x_finance_symbol").Where("SellerId = ?", reqdata.SellerId).Where("Symbol = ?", reqdata.Symbol).Update(objdata)
	ctx.RespOK()
	server.WriteAdminLog("修改充提币种", ctx, reqdata)
}

func (c *PayController) modify_symbol_rate(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId     int
		Symbol       string  `validate:"required"`
		RechargeRate float32 `validate:"required"` //充值汇率
		WithwardRate float32 `validate:"required"` //提现汇率
		GoogleCode   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "财务管理", "币种管理", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.XDb().Table("x_finance_symbol").Where("SellerId = ?", reqdata.SellerId).Where("Symbol = ?", reqdata.Symbol).Update(xgo.H{
		"RateUpdateTime":    xgo.GetLocalTime(),
		"RateUpdateAccount": token.Account,
		"RechargeRateEx":    reqdata.RechargeRate,
		"WithwardRateEx":    reqdata.WithwardRate,
	})
	ctx.RespOK()
	server.WriteAdminLog("设置充提基础汇率", ctx, reqdata)
}

func (c *PayController) get_changelog(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page      int
		PageSize  int
		SellerId  int
		Symbol    string
		StartTime int64
		EndTime   int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "财务管理", "币种管理", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if reqdata.EndTime > 0 {
		reqdata.EndTime += 1000
	}
	//fmt.Println("get_changelog: ", xgo.TimeStampToLocalTime(reqdata.StartTime), xgo.TimeStampToLocalTime(reqdata.EndTime))
	table := server.XDb().Table("x_finance_changelog").Where("Symbol = ?", reqdata.Symbol, "")
	table = table.Where("CreateTime >= ?", xgo.TimeStampToLocalTime(reqdata.StartTime/1000), "")
	table = table.Where("CreateTime < ?", xgo.TimeStampToLocalTime(reqdata.EndTime/1000), "")
	if reqdata.SellerId > 0 {
		table = table.Where("SellerId = ?", reqdata.SellerId, 0)
	}
	total, _ := table.Count()
	data, _ := table.OrderBy("Id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("total", total)
	ctx.Put("data", data.Maps())
	ctx.RespOK()
}

func (c *PayController) get_pay_method(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		SellerId int
		Name     string //支代付名
		Symbol   string //币种
		Brand    string //支付代付类型
		PayMode  int    //支付模式 1 仅充值 2 仅提现 3 充值提现
		State    int    //状态 1启用 2禁用
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "财务管理", "支付代付", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	table := server.XDb().Table("x_finance_method").OrderBy("sort desc")
	table = table.Where("SellerId = ?", reqdata.SellerId, 0)
	table = table.Where("Brand = ?", reqdata.Brand, "")
	table = table.Where("Name = ?", reqdata.Name, "")
	table = table.Where("Symbol = ?", reqdata.Symbol, "")
	table = table.Where("State = ?", reqdata.State, 0)
	if reqdata.PayMode == 1 {
		table = table.Where("IsRecharge = ?", 1)
	}
	if reqdata.PayMode == 2 {
		table = table.Where("IsWithdraw = ?", 1)
	}
	if reqdata.PayMode == 3 {
		table = table.Where("IsRecharge = ? and IsWithdraw = ?", 1, 1)
	}
	total, _ := table.Count()
	data, _ := table.PageData(reqdata.Page, reqdata.PageSize)
	data.ForEach(func(x *xgo.XMap) bool {
		x.Set("Brand", strings.ToUpper(x.String("Brand")))
		return true
	})
	ctx.Put("total", total)
	ctx.Put("data", data.Maps())
	ctx.Put("url", server.ImageUrl())
	ctx.RespOK()
}

func (c *PayController) add_pay_method(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId              int
		Brand                 string  //支付代付类型
		Name                  string  //支代付名
		Symbol                string  //币种
		IsRecharge            int     //是否充值
		IsWithdraw            int     //是否提现
		MinRecharge           float32 //最小充值
		MaxRecharge           float32 //最大充值
		MinWithdraw           float32 //最小提现
		MaxWithdraw           float32 //最大提现
		RechargeAmountOptions string  //充值金额选项
		WithdrawAmountOptions string  //提现金额选项
		IsInputRechargeAmount int     //是否输入充值金额
		IsInputWithdrawAmount int     //是否输入提现金额
		State                 int     //状态 1启用 2禁用
		ApiInfo               string  //接口信息
		RechargeFreeRate      float32 //充值手续费率
		WithdrawFreeRate      float32 //提现手续费率
		RechargeDaillyLimit   float32 //充值日限额
		WithdrawDaillyLimit   float32 //提现日限额
		RechargeDailly        float32 //今日已充值金额
		WithdrawDailly        float32 //今日已提现金额
		Icon                  string
		GoogleCode            string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "财务管理", "支付代付", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	objdata := xgo.ObjectToMap(reqdata)
	delete(*objdata, "GoogleCode")
	server.XDb().Table("x_finance_method").Insert(objdata)
	ctx.RespOK()
	server.WriteAdminLog("添加支付通道", ctx, reqdata)
}

func (c *PayController) modify_pay_method(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id                    int
		SellerId              int
		Brand                 string  //支付代付类型
		Name                  string  //支代付名
		Symbol                string  //币种
		IsRecharge            int     //是否充值
		IsWithdraw            int     //是否提现
		MinRecharge           float32 //最小充值
		MaxRecharge           float32 //最大充值
		MinWithdraw           float32 //最小提现
		MaxWithdraw           float32 //最大提现
		RechargeAmountOptions string  //充值金额选项
		WithdrawAmountOptions string  //提现金额选项
		IsInputRechargeAmount int     //是否输入充值金额
		IsInputWithdrawAmount int     //是否输入提现金额
		State                 int     //状态 1启用 2禁用
		ApiInfo               string  //接口信息
		RechargeFreeRate      float32 //充值手续费率
		WithdrawFreeRate      float32 //提现手续费率
		RechargeDaillyLimit   float32 //充值日限额
		WithdrawDaillyLimit   float32 //提现日限额
		RechargeDailly        float32 //今日已充值金额
		WithdrawDailly        float32 //今日已提现金额
		Icon                  string
		Sort                  int
		GoogleCode            string
		PoundSign             string
		PayType               string
		WPayType              string
		Rtype                 int `validate:"required"`
		Hv                    int `validate:"gte=1" errField:"权重必须大于等于1"`
		JumpType              int // 跳转类型
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "财务管理", "支付代付", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	objdata := xgo.ObjectToMap(reqdata)
	delete(*objdata, "GoogleCode")
	delete(*objdata, "Brand")
	delete(*objdata, "Name")
	delete(*objdata, "Symbol")

	server.XDb().Table("x_finance_method").Where("Id = ? and SellerId = ? and Brand = ? and Name = ? and Symbol = ?", reqdata.Id, reqdata.SellerId, reqdata.Brand, reqdata.Name, reqdata.Symbol).Update(objdata)
	ctx.RespOK()
	server.WriteAdminLog("修改支付通道", ctx, reqdata)
}

func (c *PayController) delete_pay_method(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		Brand      string //支付代付类型
		Name       string //支代付名
		Symbol     string //币种
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "财务管理", "支付代付", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	server.XDb().Table("x_finance_method").Where("SellerId = ? and Brand = ? and Name = ? and Symbol = ?", reqdata.SellerId, reqdata.Brand, reqdata.Name, reqdata.Symbol).Delete()
	ctx.RespOK()
	server.WriteAdminLog("删除支付通道", ctx, reqdata)
}

func (c *PayController) symbol_update_status(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id         []int32 `validate:"required,gt=0"`
		SellerId   int
		GoogleCode string
		Type       int `validate:"oneof=1 2"`
		Status     int `validate:"oneof=1 2"`
	}
	errcode := 0
	reqdata := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "财务管理", "币种管理", "改", "修改币种管理")
	if token == nil {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	financeSymbolTb := server.DaoxHashGame().XFinanceSymbol
	financeSymbolDb := server.DaoxHashGame().XFinanceSymbol.WithContext(context.Background())
	data := make(map[string]interface{})
	if reqdata.Type == 1 { // 修改状态
		data["State"] = reqdata.Status
		financeSymbolDb = financeSymbolDb.Select(financeSymbolTb.State, financeSymbolTb.UpdateTime, financeSymbolTb.UpdateAccount)
	} else { // 自动汇率
		data["AutoState"] = reqdata.Status
		financeSymbolDb = financeSymbolDb.Select(financeSymbolTb.AutoState, financeSymbolTb.UpdateTime, financeSymbolTb.UpdateAccount)
	}
	data["UpdateTime"] = xgo.GetLocalTime()
	data["UpdateAccount"] = token.Account
	_, err := financeSymbolDb.Where(financeSymbolTb.ID.In(reqdata.Id...)).Updates(data)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.RespOK()
}

func (c *PayController) get_currency(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	financeSymbolTb := server.DaoxHashGame().XFinanceSymbol
	financeSymbolDb := server.DaoxHashGame().XFinanceSymbol.WithContext(context.Background())
	if reqdata.SellerId > 0 {
		financeSymbolDb = financeSymbolDb.Where(financeSymbolTb.SellerID.Eq(int32(reqdata.SellerId)))
	}
	list, err := financeSymbolDb.Select(financeSymbolTb.Symbol.Distinct().As(financeSymbolTb.Symbol.ColumnName().String())).Find()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("list", list)
	server.WriteAdminLog("获取币种列表", ctx, reqdata)
	ctx.RespOK()
}
