// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTyjBroadcastConfig(db *gorm.DB, opts ...gen.DOOption) xTyjBroadcastConfig {
	_xTyjBroadcastConfig := xTyjBroadcastConfig{}

	_xTyjBroadcastConfig.xTyjBroadcastConfigDo.UseDB(db, opts...)
	_xTyjBroadcastConfig.xTyjBroadcastConfigDo.UseModel(&model.XTyjBroadcastConfig{})

	tableName := _xTyjBroadcastConfig.xTyjBroadcastConfigDo.TableName()
	_xTyjBroadcastConfig.ALL = field.NewAsterisk(tableName)
	_xTyjBroadcastConfig.ID = field.NewInt32(tableName, "Id")
	_xTyjBroadcastConfig.SellerID = field.NewInt32(tableName, "SellerId")
	_xTyjBroadcastConfig.Symbol = field.NewString(tableName, "Symbol")
	_xTyjBroadcastConfig.Title = field.NewString(tableName, "Title")
	_xTyjBroadcastConfig.Content = field.NewString(tableName, "Content")
	_xTyjBroadcastConfig.Status = field.NewInt32(tableName, "Status")
	_xTyjBroadcastConfig.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTyjBroadcastConfig.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTyjBroadcastConfig.fillFieldMap()

	return _xTyjBroadcastConfig
}

// xTyjBroadcastConfig 体验金语言播报配置表
type xTyjBroadcastConfig struct {
	xTyjBroadcastConfigDo xTyjBroadcastConfigDo

	ALL        field.Asterisk
	ID         field.Int32  // 自增id
	SellerID   field.Int32  // 运营商
	Symbol     field.String // 体验金币种
	Title      field.String // 标题
	Content    field.String // 内容
	Status     field.Int32  // 状态（1开启 2关闭）
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTyjBroadcastConfig) Table(newTableName string) *xTyjBroadcastConfig {
	x.xTyjBroadcastConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTyjBroadcastConfig) As(alias string) *xTyjBroadcastConfig {
	x.xTyjBroadcastConfigDo.DO = *(x.xTyjBroadcastConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTyjBroadcastConfig) updateTableName(table string) *xTyjBroadcastConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.Symbol = field.NewString(table, "Symbol")
	x.Title = field.NewString(table, "Title")
	x.Content = field.NewString(table, "Content")
	x.Status = field.NewInt32(table, "Status")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTyjBroadcastConfig) WithContext(ctx context.Context) *xTyjBroadcastConfigDo {
	return x.xTyjBroadcastConfigDo.WithContext(ctx)
}

func (x xTyjBroadcastConfig) TableName() string { return x.xTyjBroadcastConfigDo.TableName() }

func (x xTyjBroadcastConfig) Alias() string { return x.xTyjBroadcastConfigDo.Alias() }

func (x xTyjBroadcastConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xTyjBroadcastConfigDo.Columns(cols...)
}

func (x *xTyjBroadcastConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTyjBroadcastConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Title"] = x.Title
	x.fieldMap["Content"] = x.Content
	x.fieldMap["Status"] = x.Status
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTyjBroadcastConfig) clone(db *gorm.DB) xTyjBroadcastConfig {
	x.xTyjBroadcastConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTyjBroadcastConfig) replaceDB(db *gorm.DB) xTyjBroadcastConfig {
	x.xTyjBroadcastConfigDo.ReplaceDB(db)
	return x
}

type xTyjBroadcastConfigDo struct{ gen.DO }

func (x xTyjBroadcastConfigDo) Debug() *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xTyjBroadcastConfigDo) WithContext(ctx context.Context) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTyjBroadcastConfigDo) ReadDB() *xTyjBroadcastConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTyjBroadcastConfigDo) WriteDB() *xTyjBroadcastConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTyjBroadcastConfigDo) Session(config *gorm.Session) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTyjBroadcastConfigDo) Clauses(conds ...clause.Expression) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTyjBroadcastConfigDo) Returning(value interface{}, columns ...string) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTyjBroadcastConfigDo) Not(conds ...gen.Condition) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTyjBroadcastConfigDo) Or(conds ...gen.Condition) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTyjBroadcastConfigDo) Select(conds ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTyjBroadcastConfigDo) Where(conds ...gen.Condition) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTyjBroadcastConfigDo) Order(conds ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTyjBroadcastConfigDo) Distinct(cols ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTyjBroadcastConfigDo) Omit(cols ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTyjBroadcastConfigDo) Join(table schema.Tabler, on ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTyjBroadcastConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTyjBroadcastConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTyjBroadcastConfigDo) Group(cols ...field.Expr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTyjBroadcastConfigDo) Having(conds ...gen.Condition) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTyjBroadcastConfigDo) Limit(limit int) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTyjBroadcastConfigDo) Offset(offset int) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTyjBroadcastConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTyjBroadcastConfigDo) Unscoped() *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTyjBroadcastConfigDo) Create(values ...*model.XTyjBroadcastConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTyjBroadcastConfigDo) CreateInBatches(values []*model.XTyjBroadcastConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTyjBroadcastConfigDo) Save(values ...*model.XTyjBroadcastConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTyjBroadcastConfigDo) First() (*model.XTyjBroadcastConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTyjBroadcastConfig), nil
	}
}

func (x xTyjBroadcastConfigDo) Take() (*model.XTyjBroadcastConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTyjBroadcastConfig), nil
	}
}

func (x xTyjBroadcastConfigDo) Last() (*model.XTyjBroadcastConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTyjBroadcastConfig), nil
	}
}

func (x xTyjBroadcastConfigDo) Find() ([]*model.XTyjBroadcastConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTyjBroadcastConfig), err
}

func (x xTyjBroadcastConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTyjBroadcastConfig, err error) {
	buf := make([]*model.XTyjBroadcastConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTyjBroadcastConfigDo) FindInBatches(result *[]*model.XTyjBroadcastConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTyjBroadcastConfigDo) Attrs(attrs ...field.AssignExpr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTyjBroadcastConfigDo) Assign(attrs ...field.AssignExpr) *xTyjBroadcastConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTyjBroadcastConfigDo) Joins(fields ...field.RelationField) *xTyjBroadcastConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTyjBroadcastConfigDo) Preload(fields ...field.RelationField) *xTyjBroadcastConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTyjBroadcastConfigDo) FirstOrInit() (*model.XTyjBroadcastConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTyjBroadcastConfig), nil
	}
}

func (x xTyjBroadcastConfigDo) FirstOrCreate() (*model.XTyjBroadcastConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTyjBroadcastConfig), nil
	}
}

func (x xTyjBroadcastConfigDo) FindByPage(offset int, limit int) (result []*model.XTyjBroadcastConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTyjBroadcastConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTyjBroadcastConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTyjBroadcastConfigDo) Delete(models ...*model.XTyjBroadcastConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTyjBroadcastConfigDo) withDO(do gen.Dao) *xTyjBroadcastConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
