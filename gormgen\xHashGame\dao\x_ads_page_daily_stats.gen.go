// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsPageDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsPageDailyStat {
	_xAdsPageDailyStat := xAdsPageDailyStat{}

	_xAdsPageDailyStat.xAdsPageDailyStatDo.UseDB(db, opts...)
	_xAdsPageDailyStat.xAdsPageDailyStatDo.UseModel(&model.XAdsPageDailyStat{})

	tableName := _xAdsPageDailyStat.xAdsPageDailyStatDo.TableName()
	_xAdsPageDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsPageDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsPageDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsPageDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsPageDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsPageDailyStat.PageName = field.NewString(tableName, "page_name")
	_xAdsPageDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsPageDailyStat.VisitCountPc = field.NewInt32(tableName, "visit_count_pc")
	_xAdsPageDailyStat.VisitCountH5 = field.NewInt32(tableName, "visit_count_h5")
	_xAdsPageDailyStat.VisitorCountPc = field.NewInt32(tableName, "visitor_count_pc")
	_xAdsPageDailyStat.VisitorCountH5 = field.NewInt32(tableName, "visitor_count_h5")
	_xAdsPageDailyStat.CountLoadPcWifi = field.NewInt32(tableName, "count_load_pc_wifi")
	_xAdsPageDailyStat.CountLoadPcFlow = field.NewInt32(tableName, "count_load_pc_flow")
	_xAdsPageDailyStat.CountLoadH5Wifi = field.NewInt32(tableName, "count_load_h5_wifi")
	_xAdsPageDailyStat.CountLoadH5Flow = field.NewInt32(tableName, "count_load_h5_flow")
	_xAdsPageDailyStat.TotalStayDurationPc = field.NewFloat32(tableName, "total_stay_duration_pc")
	_xAdsPageDailyStat.TotalStayDurationH5 = field.NewFloat32(tableName, "total_stay_duration_h5")
	_xAdsPageDailyStat.TotalLoadDurationPcWifi = field.NewFloat32(tableName, "total_load_duration_pc_wifi")
	_xAdsPageDailyStat.TotalLoadDurationPcFlow = field.NewFloat32(tableName, "total_load_duration_pc_flow")
	_xAdsPageDailyStat.TotalLoadDurationH5Wifi = field.NewFloat32(tableName, "total_load_duration_h5_wifi")
	_xAdsPageDailyStat.TotalLoadDurationH5Flow = field.NewFloat32(tableName, "total_load_duration_h5_flow")
	_xAdsPageDailyStat.AvgLoadDurationPcWifi = field.NewFloat32(tableName, "avg_load_duration_pc_wifi")
	_xAdsPageDailyStat.AvgLoadDurationPcFlow = field.NewFloat32(tableName, "avg_load_duration_pc_flow")
	_xAdsPageDailyStat.AvgLoadDurationH5Wifi = field.NewFloat32(tableName, "avg_load_duration_h5_wifi")
	_xAdsPageDailyStat.AvgLoadDurationH5Flow = field.NewFloat32(tableName, "avg_load_duration_h5_flow")
	_xAdsPageDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsPageDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsPageDailyStat.fillFieldMap()

	return _xAdsPageDailyStat
}

// xAdsPageDailyStat 页面访问每日统计表
type xAdsPageDailyStat struct {
	xAdsPageDailyStatDo xAdsPageDailyStatDo

	ALL                     field.Asterisk
	ID                      field.Int64   // 主键ID
	SellerID                field.Int32   // 运营商ID
	ChannelID               field.Int32   // 渠道ID
	TopAgentID              field.Int64   // 顶级代理ID
	PageName                field.String  // 页面名称
	StatDate                field.Time    // 统计日期
	VisitCountPc            field.Int32   // 访问次数pc
	VisitCountH5            field.Int32   // 访问次数h5
	VisitorCountPc          field.Int32   // 访问人数pc
	VisitorCountH5          field.Int32   // 访问人数h5
	CountLoadPcWifi         field.Int32   // 加载次数 pc wifi
	CountLoadPcFlow         field.Int32   // 加载次数 pc flow
	CountLoadH5Wifi         field.Int32   // 加载次数 h5 wifi
	CountLoadH5Flow         field.Int32   // 加载次数 h5 flow
	TotalStayDurationPc     field.Float32 // 总停留时长(秒)pc
	TotalStayDurationH5     field.Float32 // 总停留时长(秒)h5
	TotalLoadDurationPcWifi field.Float32 // 总加载时长(秒)pc wifi
	TotalLoadDurationPcFlow field.Float32 // 总加载时长(秒)pc flow
	TotalLoadDurationH5Wifi field.Float32 // 总加载时长(秒)h5 wifi
	TotalLoadDurationH5Flow field.Float32 // 总加载时长(秒)h5 flow
	AvgLoadDurationPcWifi   field.Float32 // 平均加载时长(秒)pc wifi
	AvgLoadDurationPcFlow   field.Float32 // 平均加载时长(秒)pc flow
	AvgLoadDurationH5Wifi   field.Float32 // 平均加载时长(秒)h5 wifi
	AvgLoadDurationH5Flow   field.Float32 // 平均加载时长(秒)h5 flow
	CreateTime              field.Time    // 创建时间
	UpdateTime              field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsPageDailyStat) Table(newTableName string) *xAdsPageDailyStat {
	x.xAdsPageDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsPageDailyStat) As(alias string) *xAdsPageDailyStat {
	x.xAdsPageDailyStatDo.DO = *(x.xAdsPageDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsPageDailyStat) updateTableName(table string) *xAdsPageDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.PageName = field.NewString(table, "page_name")
	x.StatDate = field.NewTime(table, "stat_date")
	x.VisitCountPc = field.NewInt32(table, "visit_count_pc")
	x.VisitCountH5 = field.NewInt32(table, "visit_count_h5")
	x.VisitorCountPc = field.NewInt32(table, "visitor_count_pc")
	x.VisitorCountH5 = field.NewInt32(table, "visitor_count_h5")
	x.CountLoadPcWifi = field.NewInt32(table, "count_load_pc_wifi")
	x.CountLoadPcFlow = field.NewInt32(table, "count_load_pc_flow")
	x.CountLoadH5Wifi = field.NewInt32(table, "count_load_h5_wifi")
	x.CountLoadH5Flow = field.NewInt32(table, "count_load_h5_flow")
	x.TotalStayDurationPc = field.NewFloat32(table, "total_stay_duration_pc")
	x.TotalStayDurationH5 = field.NewFloat32(table, "total_stay_duration_h5")
	x.TotalLoadDurationPcWifi = field.NewFloat32(table, "total_load_duration_pc_wifi")
	x.TotalLoadDurationPcFlow = field.NewFloat32(table, "total_load_duration_pc_flow")
	x.TotalLoadDurationH5Wifi = field.NewFloat32(table, "total_load_duration_h5_wifi")
	x.TotalLoadDurationH5Flow = field.NewFloat32(table, "total_load_duration_h5_flow")
	x.AvgLoadDurationPcWifi = field.NewFloat32(table, "avg_load_duration_pc_wifi")
	x.AvgLoadDurationPcFlow = field.NewFloat32(table, "avg_load_duration_pc_flow")
	x.AvgLoadDurationH5Wifi = field.NewFloat32(table, "avg_load_duration_h5_wifi")
	x.AvgLoadDurationH5Flow = field.NewFloat32(table, "avg_load_duration_h5_flow")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsPageDailyStat) WithContext(ctx context.Context) *xAdsPageDailyStatDo {
	return x.xAdsPageDailyStatDo.WithContext(ctx)
}

func (x xAdsPageDailyStat) TableName() string { return x.xAdsPageDailyStatDo.TableName() }

func (x xAdsPageDailyStat) Alias() string { return x.xAdsPageDailyStatDo.Alias() }

func (x xAdsPageDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsPageDailyStatDo.Columns(cols...)
}

func (x *xAdsPageDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsPageDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 26)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["page_name"] = x.PageName
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["visit_count_pc"] = x.VisitCountPc
	x.fieldMap["visit_count_h5"] = x.VisitCountH5
	x.fieldMap["visitor_count_pc"] = x.VisitorCountPc
	x.fieldMap["visitor_count_h5"] = x.VisitorCountH5
	x.fieldMap["count_load_pc_wifi"] = x.CountLoadPcWifi
	x.fieldMap["count_load_pc_flow"] = x.CountLoadPcFlow
	x.fieldMap["count_load_h5_wifi"] = x.CountLoadH5Wifi
	x.fieldMap["count_load_h5_flow"] = x.CountLoadH5Flow
	x.fieldMap["total_stay_duration_pc"] = x.TotalStayDurationPc
	x.fieldMap["total_stay_duration_h5"] = x.TotalStayDurationH5
	x.fieldMap["total_load_duration_pc_wifi"] = x.TotalLoadDurationPcWifi
	x.fieldMap["total_load_duration_pc_flow"] = x.TotalLoadDurationPcFlow
	x.fieldMap["total_load_duration_h5_wifi"] = x.TotalLoadDurationH5Wifi
	x.fieldMap["total_load_duration_h5_flow"] = x.TotalLoadDurationH5Flow
	x.fieldMap["avg_load_duration_pc_wifi"] = x.AvgLoadDurationPcWifi
	x.fieldMap["avg_load_duration_pc_flow"] = x.AvgLoadDurationPcFlow
	x.fieldMap["avg_load_duration_h5_wifi"] = x.AvgLoadDurationH5Wifi
	x.fieldMap["avg_load_duration_h5_flow"] = x.AvgLoadDurationH5Flow
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsPageDailyStat) clone(db *gorm.DB) xAdsPageDailyStat {
	x.xAdsPageDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsPageDailyStat) replaceDB(db *gorm.DB) xAdsPageDailyStat {
	x.xAdsPageDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsPageDailyStatDo struct{ gen.DO }

func (x xAdsPageDailyStatDo) Debug() *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsPageDailyStatDo) WithContext(ctx context.Context) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsPageDailyStatDo) ReadDB() *xAdsPageDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsPageDailyStatDo) WriteDB() *xAdsPageDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsPageDailyStatDo) Session(config *gorm.Session) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsPageDailyStatDo) Clauses(conds ...clause.Expression) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsPageDailyStatDo) Returning(value interface{}, columns ...string) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsPageDailyStatDo) Not(conds ...gen.Condition) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsPageDailyStatDo) Or(conds ...gen.Condition) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsPageDailyStatDo) Select(conds ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsPageDailyStatDo) Where(conds ...gen.Condition) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsPageDailyStatDo) Order(conds ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsPageDailyStatDo) Distinct(cols ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsPageDailyStatDo) Omit(cols ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsPageDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsPageDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsPageDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsPageDailyStatDo) Group(cols ...field.Expr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsPageDailyStatDo) Having(conds ...gen.Condition) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsPageDailyStatDo) Limit(limit int) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsPageDailyStatDo) Offset(offset int) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsPageDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsPageDailyStatDo) Unscoped() *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsPageDailyStatDo) Create(values ...*model.XAdsPageDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsPageDailyStatDo) CreateInBatches(values []*model.XAdsPageDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsPageDailyStatDo) Save(values ...*model.XAdsPageDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsPageDailyStatDo) First() (*model.XAdsPageDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsPageDailyStat), nil
	}
}

func (x xAdsPageDailyStatDo) Take() (*model.XAdsPageDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsPageDailyStat), nil
	}
}

func (x xAdsPageDailyStatDo) Last() (*model.XAdsPageDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsPageDailyStat), nil
	}
}

func (x xAdsPageDailyStatDo) Find() ([]*model.XAdsPageDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsPageDailyStat), err
}

func (x xAdsPageDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsPageDailyStat, err error) {
	buf := make([]*model.XAdsPageDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsPageDailyStatDo) FindInBatches(result *[]*model.XAdsPageDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsPageDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsPageDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsPageDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsPageDailyStatDo) Joins(fields ...field.RelationField) *xAdsPageDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsPageDailyStatDo) Preload(fields ...field.RelationField) *xAdsPageDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsPageDailyStatDo) FirstOrInit() (*model.XAdsPageDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsPageDailyStat), nil
	}
}

func (x xAdsPageDailyStatDo) FirstOrCreate() (*model.XAdsPageDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsPageDailyStat), nil
	}
}

func (x xAdsPageDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsPageDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsPageDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsPageDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsPageDailyStatDo) Delete(models ...*model.XAdsPageDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsPageDailyStatDo) withDO(do gen.Dao) *xAdsPageDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
