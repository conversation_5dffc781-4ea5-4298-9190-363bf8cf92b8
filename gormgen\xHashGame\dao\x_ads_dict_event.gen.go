// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsDictEvent(db *gorm.DB, opts ...gen.DOOption) xAdsDictEvent {
	_xAdsDictEvent := xAdsDictEvent{}

	_xAdsDictEvent.xAdsDictEventDo.UseDB(db, opts...)
	_xAdsDictEvent.xAdsDictEventDo.UseModel(&model.XAdsDictEvent{})

	tableName := _xAdsDictEvent.xAdsDictEventDo.TableName()
	_xAdsDictEvent.ALL = field.NewAsterisk(tableName)
	_xAdsDictEvent.ID = field.NewInt64(tableName, "id")
	_xAdsDictEvent.EventType = field.NewInt32(tableName, "event_type")
	_xAdsDictEvent.EventName = field.NewString(tableName, "event_name")
	_xAdsDictEvent.ParamsTpl = field.NewString(tableName, "params_tpl")
	_xAdsDictEvent.Desc = field.NewString(tableName, "desc")
	_xAdsDictEvent.Status = field.NewInt32(tableName, "status")
	_xAdsDictEvent.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsDictEvent.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsDictEvent.fillFieldMap()

	return _xAdsDictEvent
}

// xAdsDictEvent 埋点统计事件
type xAdsDictEvent struct {
	xAdsDictEventDo xAdsDictEventDo

	ALL        field.Asterisk
	ID         field.Int64  // 主键ID
	EventType  field.Int32  // 事件类型
	EventName  field.String // 事件名称
	ParamsTpl  field.String // 参数tpl
	Desc       field.String // 描述
	Status     field.Int32  // 0无效 1有效
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsDictEvent) Table(newTableName string) *xAdsDictEvent {
	x.xAdsDictEventDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsDictEvent) As(alias string) *xAdsDictEvent {
	x.xAdsDictEventDo.DO = *(x.xAdsDictEventDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsDictEvent) updateTableName(table string) *xAdsDictEvent {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.EventType = field.NewInt32(table, "event_type")
	x.EventName = field.NewString(table, "event_name")
	x.ParamsTpl = field.NewString(table, "params_tpl")
	x.Desc = field.NewString(table, "desc")
	x.Status = field.NewInt32(table, "status")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsDictEvent) WithContext(ctx context.Context) *xAdsDictEventDo {
	return x.xAdsDictEventDo.WithContext(ctx)
}

func (x xAdsDictEvent) TableName() string { return x.xAdsDictEventDo.TableName() }

func (x xAdsDictEvent) Alias() string { return x.xAdsDictEventDo.Alias() }

func (x xAdsDictEvent) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsDictEventDo.Columns(cols...)
}

func (x *xAdsDictEvent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsDictEvent) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["id"] = x.ID
	x.fieldMap["event_type"] = x.EventType
	x.fieldMap["event_name"] = x.EventName
	x.fieldMap["params_tpl"] = x.ParamsTpl
	x.fieldMap["desc"] = x.Desc
	x.fieldMap["status"] = x.Status
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsDictEvent) clone(db *gorm.DB) xAdsDictEvent {
	x.xAdsDictEventDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsDictEvent) replaceDB(db *gorm.DB) xAdsDictEvent {
	x.xAdsDictEventDo.ReplaceDB(db)
	return x
}

type xAdsDictEventDo struct{ gen.DO }

func (x xAdsDictEventDo) Debug() *xAdsDictEventDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsDictEventDo) WithContext(ctx context.Context) *xAdsDictEventDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsDictEventDo) ReadDB() *xAdsDictEventDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsDictEventDo) WriteDB() *xAdsDictEventDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsDictEventDo) Session(config *gorm.Session) *xAdsDictEventDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsDictEventDo) Clauses(conds ...clause.Expression) *xAdsDictEventDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsDictEventDo) Returning(value interface{}, columns ...string) *xAdsDictEventDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsDictEventDo) Not(conds ...gen.Condition) *xAdsDictEventDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsDictEventDo) Or(conds ...gen.Condition) *xAdsDictEventDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsDictEventDo) Select(conds ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsDictEventDo) Where(conds ...gen.Condition) *xAdsDictEventDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsDictEventDo) Order(conds ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsDictEventDo) Distinct(cols ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsDictEventDo) Omit(cols ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsDictEventDo) Join(table schema.Tabler, on ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsDictEventDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsDictEventDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsDictEventDo) Group(cols ...field.Expr) *xAdsDictEventDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsDictEventDo) Having(conds ...gen.Condition) *xAdsDictEventDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsDictEventDo) Limit(limit int) *xAdsDictEventDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsDictEventDo) Offset(offset int) *xAdsDictEventDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsDictEventDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsDictEventDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsDictEventDo) Unscoped() *xAdsDictEventDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsDictEventDo) Create(values ...*model.XAdsDictEvent) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsDictEventDo) CreateInBatches(values []*model.XAdsDictEvent, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsDictEventDo) Save(values ...*model.XAdsDictEvent) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsDictEventDo) First() (*model.XAdsDictEvent, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsDictEvent), nil
	}
}

func (x xAdsDictEventDo) Take() (*model.XAdsDictEvent, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsDictEvent), nil
	}
}

func (x xAdsDictEventDo) Last() (*model.XAdsDictEvent, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsDictEvent), nil
	}
}

func (x xAdsDictEventDo) Find() ([]*model.XAdsDictEvent, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsDictEvent), err
}

func (x xAdsDictEventDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsDictEvent, err error) {
	buf := make([]*model.XAdsDictEvent, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsDictEventDo) FindInBatches(result *[]*model.XAdsDictEvent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsDictEventDo) Attrs(attrs ...field.AssignExpr) *xAdsDictEventDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsDictEventDo) Assign(attrs ...field.AssignExpr) *xAdsDictEventDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsDictEventDo) Joins(fields ...field.RelationField) *xAdsDictEventDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsDictEventDo) Preload(fields ...field.RelationField) *xAdsDictEventDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsDictEventDo) FirstOrInit() (*model.XAdsDictEvent, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsDictEvent), nil
	}
}

func (x xAdsDictEventDo) FirstOrCreate() (*model.XAdsDictEvent, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsDictEvent), nil
	}
}

func (x xAdsDictEventDo) FindByPage(offset int, limit int) (result []*model.XAdsDictEvent, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsDictEventDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsDictEventDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsDictEventDo) Delete(models ...*model.XAdsDictEvent) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsDictEventDo) withDO(do gen.Dao) *xAdsDictEventDo {
	x.DO = *do.(*gen.DO)
	return x
}
