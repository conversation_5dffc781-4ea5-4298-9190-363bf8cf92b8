package controller

import (
	"fmt"
	"path"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/db"
	"xserver/server"
	"xserver/utils"

	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/xuri/excelize/v2"
)

var GameName map[int]string
var RoomName map[int]string
var OrderStateName map[int]string
var RewardType map[int]string

type OrderController struct {
}

func (c *OrderController) Init() {
	GameName = make(map[int]string)
	RoomName = make(map[int]string)
	OrderStateName = make(map[int]string)
	RewardType = make(map[int]string)
	GameName[1] = "哈希大小"
	GameName[2] = "哈希单双"
	GameName[3] = "幸运哈希"
	GameName[4] = "幸运庄闲"
	GameName[5] = "哈希牛牛"
	GameName[6] = "哈希快3"
	GameName[7] = "哈希pk10"
	GameName[11] = "和值大小"
	GameName[12] = "和值单双"
	GameName[101] = "一分大小"
	GameName[102] = "一分单双"
	GameName[103] = "一分幸运"
	GameName[104] = "一分庄闲"
	GameName[105] = "一分牛牛"

	GameName[131] = "三分大小"
	GameName[132] = "三分单双"
	GameName[133] = "三分幸运"
	GameName[134] = "三分庄闲"
	GameName[135] = "三分牛牛"

	GameName[201] = "余额大小"
	GameName[202] = "余额单双"
	GameName[203] = "幸运余额"
	GameName[204] = "余额庄闲"
	GameName[205] = "余额牛牛"
	GameName[301] = "一分哈希大小"
	GameName[302] = "一分哈希单双"
	GameName[331] = "三分哈希大小"
	GameName[332] = "三分哈希单双"
	RoomName[1] = "初级场"
	RoomName[2] = "中级场"
	RoomName[3] = "高级场"
	OrderStateName[1] = "房间不存在"
	OrderStateName[2] = "运营商不存在"
	OrderStateName[3] = "低于房间下注"
	OrderStateName[4] = "高于房间下注"
	OrderStateName[5] = "无效下注"
	OrderStateName[10] = "未中奖"
	OrderStateName[11] = "牛牛和局"
	OrderStateName[12] = "待审核"
	OrderStateName[13] = "审核拒绝"
	OrderStateName[14] = "审核通过"
	OrderStateName[15] = "审核通过待处理"
	OrderStateName[16] = "黑名单没收"
	OrderStateName[200] = "中奖"
	OrderStateName[201] = "开奖失败"
	OrderStateName[202] = "限红"
	OrderStateName[301] = "特殊区块"
	OrderStateName[500] = "待开奖"
	RewardType[2] = "个人降赔"
	RewardType[3] = "代理降赔"
	RewardType[4] = "游戏降赔"
	RewardType[5] = "正常赔率"
	RewardType[10] = "个人赔率"
	RewardType[11] = "盈利降赔"
	group := server.Http().NewGroup("/api/order")
	{
		group.Post("/list", c.list)
		group.Post("/audit_list", c.audit_list)
		group.Post("/audit_pass", c.audit_pass)
		group.Post("/audit_refuse", c.audit_refuse)

		group.Post("/get_winaudit", c.get_winaudit)
		group.Post("/add_winaudit", c.add_winaudit)
		group.Post("/modify_winaudit", c.modify_winaudit)
		group.Post("/delete_winaudit", c.delete_winaudit)

		//group.Post("/audit_list_new", c.audit_list_new)
		// group.PostNoAuth("/no_reward", c.no_reward)
		// group.PostNoAuth("/reward_order", c.reward_order)
	}
}
func (c *OrderController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId     []int32
		Page         int
		PageSize     int
		Id           int
		UserId       int
		FromAddress  string
		TxId         string
		RewardType   int
		State        int
		Symbol       string
		StartTime    int64
		EndTime      int64
		Export       int //是否是导出,1是,2不是
		GameType     int
		GameId       []int
		RoomLevel    int
		TopAgentId   int
		KouChu       int
		ToAddress    string
		ChannelId    []int
		Period       string
		BlockMaker   string
		IsTest       int
		SpecialAgent int
		ChainType    int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "注单列表", "查"), &errcode, "权限不足") {
		return
	}
	if token.SellerId > 0 {
		if len(reqdata.SellerId) == 0 || reqdata.SellerId[0] != int32(token.SellerId) {
			ctx.RespErrString(true, &errcode, "运营商不正确")
			return
		}
	}
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
	}
	if reqdata.KouChu == 1 {
		StartTime := abugo.LocalDateToTimeStamp(abugo.GetLocalDate()) * 1000
		if reqdata.StartTime == 0 {
			reqdata.StartTime = StartTime
		} else {
			if reqdata.StartTime < StartTime {
				reqdata.StartTime = StartTime
			}
		}
	}
	total, data := db.Order_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId,
		reqdata.Id, reqdata.UserId, reqdata.FromAddress, reqdata.TxId, reqdata.RewardType,
		reqdata.State, reqdata.Symbol, reqdata.StartTime, reqdata.EndTime, reqdata.GameId,
		reqdata.RoomLevel, reqdata.TopAgentId, reqdata.ToAddress, reqdata.KouChu, reqdata.ChannelId,
		reqdata.Period, reqdata.BlockMaker, reqdata.IsTest, reqdata.SpecialAgent, reqdata.GameType, reqdata.ChainType)
	if reqdata.Export != 1 {
		StartTime := reqdata.StartTime
		EndTime := reqdata.EndTime
		where := abugo.AbuDbWhere{}
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
		where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
		//where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
		// 运营商多选
		sellerIdStr := utils.ToSellers(reqdata.SellerId)
		if len(sellerIdStr) > 0 {
			where.Add("and", "SellerId", "in", fmt.Sprintf("(%s)", sellerIdStr), 0)
		}
		where.Add("and", "Id", "=", reqdata.Id, 0)
		where.Add("and", "UserId", "=", reqdata.UserId, 0)
		if reqdata.GameType == -1 { //转账游戏
			where.Add("and", "GameId", "in", "(1, 2, 3, 4, 5, 6, 7, 11, 12,13, 301, 302, 303, 313, 323, 331, 332, 333)", 0)
		} else if reqdata.GameType == -2 { //余额游戏
			where.Add("and", "GameId", "in", "(101,102,103,104,105,106,116,126,131,132,133,134,135,136,201,202,203,204,205,206)", 0)
		}
		if len(reqdata.GameId) > 0 {
			in := ""
			for _, v := range reqdata.GameId {
				if v == 0 {
					continue
				}
				in += fmt.Sprintf("%d,", v)
			}
			if len(in) > 0 {
				in = in[0 : len(in)-1]
				where.Add("and", "GameId", "in", fmt.Sprintf("(%s)", in), 0)
			}
		}
		// 渠道多选
		channelIdStr := utils.ToChannels(reqdata.ChannelId)
		if len(channelIdStr) > 0 {
			where.Add("and", "ChannelId", "in", fmt.Sprintf("(%s)", channelIdStr), 0)
		}
		where.Add("and", "RoomLevel", "=", reqdata.RoomLevel, 0)
		where.Add("and", "FromAddress", "=", reqdata.FromAddress, "")
		where.Add("and", "ToAddress", "=", reqdata.ToAddress, "")
		where.Add("and", "TxId", "=", reqdata.TxId, "")
		where.Add("and", "Symbol", "=", reqdata.Symbol, "")
		if reqdata.IsTest == 3 {
			where.Add("and", "IsPanda", "=", 1, 0)
		} else {
			where.Add("and", "IsTest", "=", reqdata.IsTest, 0)
		}
		where.Add("and", "RewardType", "=", reqdata.RewardType, 0)
		where.Add("and", "State", "=", reqdata.State, 0)
		where.Add("and", "TopAgentId", "=", reqdata.TopAgentId, 0)
		where.Add("and", "Period", "=", reqdata.Period, "")
		where.Add("and", "BlockMaker", "=", reqdata.BlockMaker, "")

		where.Add("and", "SpecialAgent", "=", reqdata.SpecialAgent, 0)
		where.Add("and", "ChainType", "=", reqdata.ChainType, 0)
		wheresql, wheredata := where.Sql()
		var pcount *[]map[string]interface{}
		if wheresql == "" {
			sql := `select sum(RewardAmount) as RewardAmount, sum(Amount) as Amount,sum(BonusAmount) as BonusAmount,sum(BonusRewardAmount) as BonusRewardAmount, sum(Amount - BonusAmount) as RealAmount, sum(RewardAmount - BonusRewardAmount) as RealRewardAmount from x_order`
			pcount, _ = server.Db().Query(sql, wheredata)
		} else {
			sql := fmt.Sprintf(`select sum(RewardAmount) as RewardAmount, sum(Amount) as Amount, sum(BonusAmount) as BonusAmount,sum(BonusRewardAmount) as BonusRewardAmount, sum(Amount - BonusAmount) as RealAmount, sum(RewardAmount - BonusRewardAmount) as RealRewardAmount from x_order where %s`, wheresql)
			pcount, _ = server.Db().Query(sql, wheredata)
		}
		// paddresscount, _ := server.Db().Query(fmt.Sprintf("SELECT DISTINCT count( DISTINCT FromAddress) AS Count FROM x_order where %s", wheresql), wheredata)
		// ctx.Put("addr", paddresscount)
		ctx.Put("count", pcount)
		ctx.Put("addr", []interface{}{map[string]interface{}{
			"Count": 0,
		}})
		// ctx.Put("count", []interface{}{map[string]interface{}{
		// 	"Amount":       0,
		// 	"RewardAmount": 0,
		// }})
		ctx.Put("data", data)
		ctx.Put("total", total)
		ctx.RespOK()
		server.WriteAdminLog("查看订单", ctx, reqdata)
	} else {
		sellerNameMap, err := InitSellerNameMap()
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		excel := excelize.NewFile()
		excel.SetSheetRow("Sheet1", "A1", &[]string{"运营商", "注单号", "渠道", "下注渠道", "玩家id", "来源", "顶级Id", "投注地址", "玩家余额", "官方地址", "下注链类型", "区块哈希", "下一区块哈希", "币种", "游戏类型", "游戏", "赔率", "下注", "开奖", "投注金额", "返奖金额", "真金投注金额", "真金返奖金额", "Bonus投注金额", "Bonus返奖金额", "平台盈亏", "处理时间", "区块号", "出块者", "注单时间", "状态", "赔率类型", "出款状态", "返奖订单", "返奖哈希", "投注哈希"})
		for i, d := range data {
			excel.SetSheetRow("Sheet1", fmt.Sprintf("A%d", i+2), &[]interface{}{
				SellerIDName(sellerNameMap, int(abugo.GetInt64FromInterface(d.SellerId))),
				d.Id,
				ChannelName(d.ChannelId),
				ChannelName(d.BetChannelId),
				d.UserId,
				specialAgentName(d.SpecialAgent),
				d.TopAgentId,
				d.FromAddress,
				d.UserAmount,
				d.ToAddress,
				utils.GetBettingToChain(d.ChainType),
				d.BlockHash,
				d.NextBlockHash,
				d.Symbol,
				getGameTypeByGameId(d.GameId),
				GameName[d.GameId] + "(" + RoomName[d.RoomLevel] + ")",
				d.RewardRate,
				d.BetArea,
				d.OpenArea,
				d.Amount,
				d.RewardAmount,
				d.RealAmount,
				d.RealRewardAmount,
				d.BonusAmount,
				d.BonusRewardAmount,
				d.Amount - d.RewardAmount,
				getDealTime(d.CreateTime, d.RewardTime, d.IsWin, d.State, d.ExceedLimit),
				d.BlockNum,
				d.BlockMaker,
				d.CreateTime,
				OrderStateName[d.State],
				RewardType[d.RewardType],
				getRewardState(d.RewardOrder, d.RewardTxId, d.State, d.NowTime, d.CreateTime, d.AuditTime),
				d.RewardOrder,
				d.RewardTxId,
				d.TxId,
			})
		}
		filename := "export_order_" + time.Now().Format("20060102150405") + ".xlsx"
		excel.SaveAs(server.ExportDir() + "/" + filename)
		ctx.Put("filename", "/exports/"+filename)
		ctx.RespOK()
		server.WriteAdminLog("导出订单", ctx, reqdata)
	}
}

func getRewardState(rewardOrder, rewardTxId string, state int, nowTime, createTime, auditTime string) string {
	if len(rewardOrder) > 0 {
		if len(rewardTxId) > 0 {
			return "出款完成"
		} else {
			diff := abugo.LocalTimeToTimeStamp(nowTime) - abugo.LocalTimeToTimeStamp(createTime)
			if state == 14 {
				diff = abugo.LocalTimeToTimeStamp(nowTime) - abugo.LocalTimeToTimeStamp(auditTime)
			}
			if diff > 20 {
				return "出款异常"
			} else {
				return "出款中"
			}
		}
	} else {
		return "--"
	}
}

func getGameTypeByGameId(gId int) string {
	if gId < 100 || gId > 300 {
		return "哈希游戏"
	} else {
		return "余额哈希"
	}
}

func getDealTime(createTime, rewardTime string, isWin, state, exceedLimit int) string {
	if isWin == 1 {
		if len(createTime) > 0 && len(rewardTime) > 0 {
			if state == 14 {
				tmp := abugo.LocalTimeToTimeStamp(rewardTime) - abugo.LocalTimeToTimeStamp(createTime)
				h := tmp / 3600
				m := (tmp % 3600) / 60
				s := (tmp % 3600) % 60
				return fmt.Sprintf("%d:%d:%d", h, m, s)
			} else {
				return "自动出款"
			}
		} else {
			return "--"
		}
	} else {
		stateString := ""
		if state == 3 {
			stateString = "低于限额没收"
		} else if state == 4 {
			stateString = "高于限额"
		} else if state == 12 {
			stateString = "待审核"
		} else if state == 13 {
			stateString = "审核拒绝"
		} else if state == 14 {
			stateString = "审核通过"
		}
		if len(stateString) > 0 && exceedLimit == 1 {
			if len(createTime) > 0 && len(rewardTime) > 0 {
				if state == 14 {
					tmp := abugo.LocalTimeToTimeStamp(rewardTime) - abugo.LocalTimeToTimeStamp(createTime)
					h := tmp / 3600
					m := (tmp % 3600) / 60
					s := (tmp % 3600) % 60
					return fmt.Sprintf("超额中奖退回-%d:%d:%d", h, m, s)
				} else {
					return "自动退回"
				}
			} else {
				if state == 4 {
					return "出款中"
				} else {
					return stateString
				}
			}
		} else {
			if state == 10 && exceedLimit == 1 {
				return "超额未中奖没收"
			}
			return OrderStateName[state]
		}
	}
}

func (c *OrderController) audit_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Page      int
		PageSize  int
		UserId    int
		GameId    int
		Symbol    string //币种 trx,usdt
		State     int    //状态 12 待审核,13审核拒绝,14审核通过
		StartTime int64
		EndTime   int64
		ChannelId int
		Export    int //0表示分页查询，1表示导出报表
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "注单列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}

	xlsx := abugo.NewSimpleXlsxBuilder(server.ExportDir(), fmt.Sprintf("export_返奖审核_%s", time.Now().Format("20060102150405")))
	//var totalSize int64
	defer xlsx.Close()
	if reqdata.Export == 1 {
		reqdata.Page = 1
		reqdata.PageSize = 500000
		xlsx.Open()
		//设置表头
		xlsx.SetTitle("Id", "注单编号")
		xlsx.SetTitle("ChannelId", "渠道")
		xlsx.SetTitle("UserId", "玩家ID")
		xlsx.SetTitle("GameNameRoom", "游戏")
		xlsx.SetTitle("Symbol", "币种")
		xlsx.SetTitle("RewardRate", "赔率")
		xlsx.SetTitle("Amount", "下注金额")
		xlsx.SetTitle("RewardAmount", "返奖金额")
		xlsx.SetTitle("FromAddress", "投注地址")
		xlsx.SetTitle("ToAddress", "官方地址")
		xlsx.SetTitle("BlockNum", "区块号")
		xlsx.SetTitle("BlockHash", "区块哈希")
		xlsx.SetTitle("TxId", "交易哈希")
		xlsx.SetTitle("CreateTime", "订单时间")
		xlsx.SetTitle("State", "状态")
		xlsx.SetTitle("AuditAccount", "审核人")
		xlsx.SetTitle("AuditState", "审核状态")
		xlsx.SetTitle("AuditTime", "审核时间")
		xlsx.SetTitle("TodayYingkui", "今日盈亏(U/T)")
		xlsx.SetTitle("HistoryYingkui", "历史盈亏(U/T)")
		xlsx.SetTitle("TodayLiushui", "今日流水(U/T)")
		xlsx.SetTitle("HistoryLiushui", "历史流水(U/T)")
		xlsx.SetTitleStyle()
	}

	total, data := db.Order_Audit_Page_Data(reqdata.Page, reqdata.PageSize, reqdata.SellerId, reqdata.UserId, reqdata.Symbol, reqdata.State, reqdata.StartTime, reqdata.EndTime, 0, reqdata.GameId, reqdata.ChannelId)
	_, audit_orders := db.Order_Audit_Page_Data(0, 100, reqdata.SellerId, 0, "", 12, 0, 0, 0, 0, 0)
	if reqdata.Export != 1 {
		ctx.Put("audit_orders", audit_orders)
		ctx.Put("data", data)
		ctx.Put("total", total)
	} else {
		j := int64(2)
		for i := 0; i < len(audit_orders); i++ {
			xlsx.SetValue("Id", audit_orders[i].Id, j)
			xlsx.SetValue("ChannelId", ChannelName(audit_orders[i].ChannelId), j)
			xlsx.SetValue("UserId", audit_orders[i].UserId, j)
			xlsx.SetValue("GameNameRoom", GameName[audit_orders[i].GameId]+"("+RoomName[audit_orders[i].RoomLevel]+")", j)
			xlsx.SetValue("Symbol", strings.ToUpper(audit_orders[i].Symbol), j)
			xlsx.SetValue("RewardRate", audit_orders[i].RewardRate, j)
			xlsx.SetValue("Amount", audit_orders[i].Amount, j)
			xlsx.SetValue("RewardAmount", audit_orders[i].RewardAmount, j)
			xlsx.SetValue("FromAddress", audit_orders[i].FromAddress, j)
			xlsx.SetValue("ToAddress", audit_orders[i].ToAddress, j)
			xlsx.SetValue("BlockNum", audit_orders[i].BlockNum, j)
			xlsx.SetValue("BlockHash", audit_orders[i].BlockHash, j)
			xlsx.SetValue("TxId", audit_orders[i].TxId, j)
			xlsx.SetValue("CreateTime", audit_orders[i].CreateTime, j)
			xlsx.SetValue("AuditAccount", audit_orders[i].AuditAccount, j)
			if audit_orders[i].State == 12 {
				xlsx.SetValue("State", "待审核", j)
				xlsx.SetValue("AuditState", "待审核", j)
			} else if audit_orders[i].State == 13 {
				xlsx.SetValue("State", "审核拒绝", j)
				xlsx.SetValue("AuditState", "审核拒绝", j)
			} else if audit_orders[i].State == 14 {
				xlsx.SetValue("State", "审核通过", j)
				xlsx.SetValue("AuditState", "审核通过", j)
			} else {
				xlsx.SetValue("State", audit_orders[i].State, j)
				xlsx.SetValue("AuditState", "自动通过", j)
			}
			xlsx.SetValue("AuditTime", audit_orders[i].AuditTime, j)

			xlsx.SetValue("TodayYingkui", fmt.Sprintf("%f/%f", audit_orders[i].TodayBetUsdt-audit_orders[i].TodayRewardUsdt, audit_orders[i].TodayBetTrx-audit_orders[i].TodayRewardTrx), j)
			xlsx.SetValue("HistoryYingkui", fmt.Sprintf("%f/%f", audit_orders[i].BetUsdt-audit_orders[i].RewardUsdt, audit_orders[i].BetTrx-audit_orders[i].RewardTrx), j)
			xlsx.SetValue("TodayLiushui", fmt.Sprintf("%f/%f", audit_orders[i].TodayLiuSuiUsdt, audit_orders[i].TodayLiuSuiTrx), j)
			xlsx.SetValue("HistoryLiushui", fmt.Sprintf("%f/%f", audit_orders[i].LiuSuiUsdt, audit_orders[i].LiuSuiTrx), j)
			j++
		}
		j++ //中间留一个空白行

		for i := 0; i < len(data); i++ {
			xlsx.SetValue("Id", data[i].Id, j)
			xlsx.SetValue("ChannelId", ChannelName(data[i].ChannelId), j)
			xlsx.SetValue("UserId", data[i].UserId, j)
			xlsx.SetValue("GameNameRoom", GameName[data[i].GameId]+"("+RoomName[data[i].RoomLevel]+")", j)
			xlsx.SetValue("Symbol", strings.ToUpper(data[i].Symbol), j)
			xlsx.SetValue("RewardRate", data[i].RewardRate, j)
			xlsx.SetValue("Amount", data[i].Amount, j)
			xlsx.SetValue("RewardAmount", data[i].RewardAmount, j)
			xlsx.SetValue("FromAddress", data[i].FromAddress, j)
			xlsx.SetValue("ToAddress", data[i].ToAddress, j)
			xlsx.SetValue("BlockNum", data[i].BlockNum, j)
			xlsx.SetValue("BlockHash", data[i].BlockHash, j)
			xlsx.SetValue("TxId", data[i].TxId, j)
			xlsx.SetValue("CreateTime", data[i].CreateTime, j)
			xlsx.SetValue("AuditAccount", data[i].AuditAccount, j)
			if data[i].State == 12 {
				xlsx.SetValue("State", "待审核", j)
				xlsx.SetValue("AuditState", "待审核", j)
			} else if data[i].State == 13 {
				xlsx.SetValue("State", "审核拒绝", j)
				xlsx.SetValue("AuditState", "审核拒绝", j)
			} else if data[i].State == 14 {
				xlsx.SetValue("State", "审核通过", j)
				xlsx.SetValue("AuditState", "审核通过", j)
			} else {
				xlsx.SetValue("State", data[i].State, j)
				xlsx.SetValue("AuditState", "自动通过", j)
			}
			xlsx.SetValue("AuditTime", data[i].AuditTime, j)

			xlsx.SetValue("TodayYingkui", fmt.Sprintf("%f/%f", data[i].TodayBetUsdt-data[i].TodayRewardUsdt, data[i].TodayBetTrx-data[i].TodayRewardTrx), j)
			xlsx.SetValue("HistoryYingkui", fmt.Sprintf("%f/%f", data[i].BetUsdt-data[i].RewardUsdt, data[i].BetTrx-data[i].RewardTrx), j)
			xlsx.SetValue("TodayLiushui", fmt.Sprintf("%f/%f", data[i].TodayLiuSuiUsdt, data[i].TodayLiuSuiTrx), j)
			xlsx.SetValue("HistoryLiushui", fmt.Sprintf("%f/%f", data[i].LiuSuiUsdt, data[i].LiuSuiTrx), j)
			j++
		}
		xlsx.SetValueStyle(j)
	}

	if reqdata.Export == 1 {
		filePath, err := xlsx.ProduceFile()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("filename", "/exports/"+path.Base(filePath))
	}
	ctx.RespOK()
	server.WriteAdminLog("查看审核订单", ctx, reqdata)
}
func (c *OrderController) audit_pass(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Id         int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "注单列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "call x_admin_order_audit(?,?,?)"
	var errmsg string
	var state int
	server.Db().QueryScan(sql, []interface{}{reqdata.Id, 15, token.Account}, &errmsg, &state)
	ctx.Put("msg", errmsg)
	ctx.Put("state", state)
	ctx.RespOK()
	server.WriteAdminLog("订单审核通过", ctx, reqdata)
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}
func (c *OrderController) audit_refuse(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int
		Id         int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "注单列表", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	sql := "call x_admin_order_audit(?,?,?)"
	var errmsg string
	var state int
	server.Db().QueryScan(sql, []interface{}{reqdata.Id, 13, token.Account}, &errmsg, &state)
	ctx.Put("msg", errmsg)
	ctx.Put("state", state)
	ctx.RespOK()
	server.WriteAdminLog("订单审核拒绝", ctx, reqdata)
	req.Post("http://127.0.0.1:" + fmt.Sprint(server.Http().Port()) + "/api/nmxgqtpmlbrn")
}
func (c *OrderController) audit_list_new(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int
		Id        int
		AuditId   int
		ChannelId int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "注单列表", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	_, orders := db.Order_Audit_Page_Data(0, 30, reqdata.SellerId, 0, "", 0, 0, 0, reqdata.Id, 0, reqdata.ChannelId)
	ctx.Put("orders", orders)

	_, audit_orders := db.Order_Audit_Page_Data(0, 100, reqdata.SellerId, 0, "", 12, 0, 0, reqdata.AuditId, 0, 0)
	ctx.Put("audit_orders", audit_orders)

	ctx.Put("id", reqdata.Id)
	ctx.Put("audit_id", reqdata.AuditId)
	ctx.RespOK()
}

func (c *OrderController) no_reward(ctx *abugo.AbuHttpContent) {
	defer recover()
	orders, _ := server.Db().Query("select * from x_order where RewardAmount > 0 and RewardTxId is null order by id asc limit 1", []interface{}{})
	if orders != nil && len(*orders) > 0 {
		ctx.Put("data", (*orders)[0])
	}
	ctx.RespOK()
}

func (c *OrderController) reward_order(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id   int
		TxId string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	server.Db().QueryNoResult("update x_order set RewardTxId = ? where Id = ?", reqdata.TxId, reqdata.Id)
	ctx.RespOK()
}

func (c *OrderController) get_winaudit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int
		ChannelId int
		Symbol    string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "连赢审核", "查"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErrString(token.SellerId > 0 && reqdata.SellerId != token.SellerId, &errcode, "运营商不正确") {
		return
	}
	reqdata.SellerId = token.SellerId
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	data, _ := server.Db().Table("x_win_audit").OrderBy("amount asc").Where(where).GetList()
	ctx.RespOK(data)
}

func (c *OrderController) add_winaudit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		ChannelId  int
		Symbol     string
		Amount     float32
		WinCount   int
		State      int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "连赢审核", "增"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	//reqdata.SellerId = token.SellerId
	server.Db().Table("x_win_audit").Insert(gin.H{"SellerId": reqdata.SellerId, "ChannelId": reqdata.ChannelId, "Symbol": reqdata.Symbol, "Amount": reqdata.Amount, "WinCount": reqdata.WinCount, "State": reqdata.State})
	ctx.RespOK()
}

func (c *OrderController) modify_winaudit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		ChannelId  int
		Id         int
		Amount     float32
		WinCount   int
		State      int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "连赢审核", "改"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	//reqdata.SellerId = token.SellerId
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", reqdata.SellerId, 0)
	where.Add("and", "ChannelId", "=", reqdata.ChannelId, 0)
	where.Add("and", "Id", "=", reqdata.Id, "")
	server.Db().Table("x_win_audit").Where(where).Update(gin.H{"ChannelId": reqdata.ChannelId, "Amount": reqdata.Amount, "WinCount": reqdata.WinCount, "State": reqdata.State})
	ctx.RespOK()
}

func (c *OrderController) delete_winaudit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int
		ChannelId  int
		Id         int
		GoogleCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(!server.Auth2(token, "游戏管理", "连赢审核", "删"), &errcode, "权限不足") {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	//reqdata.SellerId = token.SellerId
	if reqdata.SellerId > 0 {
		server.Db().QueryNoResult("delete from x_win_audit where SellerId = ? and ChannelId = ? and Id = ?", reqdata.SellerId, reqdata.ChannelId, reqdata.Id)
	} else {
		server.Db().QueryNoResult("delete from x_win_audit where ChannelId = ? and Id = ?", reqdata.ChannelId, reqdata.Id)
	}
	ctx.RespOK()
}
