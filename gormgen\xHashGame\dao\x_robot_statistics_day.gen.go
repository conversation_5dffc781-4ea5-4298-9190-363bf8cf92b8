// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotStatisticsDay(db *gorm.DB, opts ...gen.DOOption) xRobotStatisticsDay {
	_xRobotStatisticsDay := xRobotStatisticsDay{}

	_xRobotStatisticsDay.xRobotStatisticsDayDo.UseDB(db, opts...)
	_xRobotStatisticsDay.xRobotStatisticsDayDo.UseModel(&model.XRobotStatisticsDay{})

	tableName := _xRobotStatisticsDay.xRobotStatisticsDayDo.TableName()
	_xRobotStatisticsDay.ALL = field.NewAsterisk(tableName)
	_xRobotStatisticsDay.ID = field.NewInt64(tableName, "id")
	_xRobotStatisticsDay.StatisticDate = field.NewTime(tableName, "statistic_date")
	_xRobotStatisticsDay.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotStatisticsDay.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotStatisticsDay.RobotType = field.NewInt32(tableName, "robot_type")
	_xRobotStatisticsDay.Name = field.NewString(tableName, "name")
	_xRobotStatisticsDay.Token = field.NewString(tableName, "token")
	_xRobotStatisticsDay.UserID = field.NewInt64(tableName, "user_id")
	_xRobotStatisticsDay.TgChatID = field.NewInt64(tableName, "tg_chat_id")
	_xRobotStatisticsDay.TgUserName = field.NewString(tableName, "tg_user_name")
	_xRobotStatisticsDay.LoginTime = field.NewTime(tableName, "login_time")
	_xRobotStatisticsDay.RegisterTime = field.NewTime(tableName, "register_time")
	_xRobotStatisticsDay.FirstRechargeTime = field.NewTime(tableName, "first_recharge_time")
	_xRobotStatisticsDay.IsInResourceDb = field.NewInt32(tableName, "is_in_resource_db")
	_xRobotStatisticsDay.BetCount = field.NewInt32(tableName, "bet_count")
	_xRobotStatisticsDay.TransferBetCount = field.NewInt64(tableName, "transfer_bet_count")
	_xRobotStatisticsDay.GiftUsdtStatus = field.NewInt32(tableName, "gift_usdt_status")
	_xRobotStatisticsDay.GiftTrxStatus = field.NewInt32(tableName, "gift_trx_status")
	_xRobotStatisticsDay.IsGiftUsdt = field.NewInt32(tableName, "is_gift_usdt")
	_xRobotStatisticsDay.IsGiftTrx = field.NewInt32(tableName, "is_gift_trx")
	_xRobotStatisticsDay.MissionGiftUsdt = field.NewInt32(tableName, "mission_gift_usdt")
	_xRobotStatisticsDay.JoinGroupStatus = field.NewInt32(tableName, "join_group_status")
	_xRobotStatisticsDay.BandingTrxAddress = field.NewString(tableName, "banding_trx_address")
	_xRobotStatisticsDay.ShardLinkCnt = field.NewInt32(tableName, "shard_link_cnt")
	_xRobotStatisticsDay.AgentCount = field.NewInt32(tableName, "agent_count")
	_xRobotStatisticsDay.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotStatisticsDay.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotStatisticsDay.fillFieldMap()

	return _xRobotStatisticsDay
}

type xRobotStatisticsDay struct {
	xRobotStatisticsDayDo xRobotStatisticsDayDo

	ALL               field.Asterisk
	ID                field.Int64  // pk
	StatisticDate     field.Time   // 统计日期
	SellerID          field.Int32  // 运营商ID
	ChannelID         field.Int32  // 渠道ID
	RobotType         field.Int32  // 机器人类型
	Name              field.String // 机器人名称
	Token             field.String // token
	UserID            field.Int64  // 用户id
	TgChatID          field.Int64  // 飞机ID
	TgUserName        field.String // 飞机账号
	LoginTime         field.Time   // 登录日期
	RegisterTime      field.Time   // 注册时间
	FirstRechargeTime field.Time   // 首充时间
	IsInResourceDb    field.Int32  // 是否在库
	BetCount          field.Int32  // 投注次数
	TransferBetCount  field.Int64  // 转账次数
	GiftUsdtStatus    field.Int32  // usdt礼金状态
	GiftTrxStatus     field.Int32  // trx领取状态
	IsGiftUsdt        field.Int32  // 是否是领取U玩家
	IsGiftTrx         field.Int32  // 领取T
	MissionGiftUsdt   field.Int32  // 任务赠送U
	JoinGroupStatus   field.Int32  // 加入群组状态
	BandingTrxAddress field.String // 绑定trx地址
	ShardLinkCnt      field.Int32  // 点击分享次数
	AgentCount        field.Int32  // 邀请人数2
	CreateTime        field.Time   // 创建
	UpdateTime        field.Time   // 更新

	fieldMap map[string]field.Expr
}

func (x xRobotStatisticsDay) Table(newTableName string) *xRobotStatisticsDay {
	x.xRobotStatisticsDayDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotStatisticsDay) As(alias string) *xRobotStatisticsDay {
	x.xRobotStatisticsDayDo.DO = *(x.xRobotStatisticsDayDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotStatisticsDay) updateTableName(table string) *xRobotStatisticsDay {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.StatisticDate = field.NewTime(table, "statistic_date")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.RobotType = field.NewInt32(table, "robot_type")
	x.Name = field.NewString(table, "name")
	x.Token = field.NewString(table, "token")
	x.UserID = field.NewInt64(table, "user_id")
	x.TgChatID = field.NewInt64(table, "tg_chat_id")
	x.TgUserName = field.NewString(table, "tg_user_name")
	x.LoginTime = field.NewTime(table, "login_time")
	x.RegisterTime = field.NewTime(table, "register_time")
	x.FirstRechargeTime = field.NewTime(table, "first_recharge_time")
	x.IsInResourceDb = field.NewInt32(table, "is_in_resource_db")
	x.BetCount = field.NewInt32(table, "bet_count")
	x.TransferBetCount = field.NewInt64(table, "transfer_bet_count")
	x.GiftUsdtStatus = field.NewInt32(table, "gift_usdt_status")
	x.GiftTrxStatus = field.NewInt32(table, "gift_trx_status")
	x.IsGiftUsdt = field.NewInt32(table, "is_gift_usdt")
	x.IsGiftTrx = field.NewInt32(table, "is_gift_trx")
	x.MissionGiftUsdt = field.NewInt32(table, "mission_gift_usdt")
	x.JoinGroupStatus = field.NewInt32(table, "join_group_status")
	x.BandingTrxAddress = field.NewString(table, "banding_trx_address")
	x.ShardLinkCnt = field.NewInt32(table, "shard_link_cnt")
	x.AgentCount = field.NewInt32(table, "agent_count")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotStatisticsDay) WithContext(ctx context.Context) *xRobotStatisticsDayDo {
	return x.xRobotStatisticsDayDo.WithContext(ctx)
}

func (x xRobotStatisticsDay) TableName() string { return x.xRobotStatisticsDayDo.TableName() }

func (x xRobotStatisticsDay) Alias() string { return x.xRobotStatisticsDayDo.Alias() }

func (x xRobotStatisticsDay) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotStatisticsDayDo.Columns(cols...)
}

func (x *xRobotStatisticsDay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotStatisticsDay) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 27)
	x.fieldMap["id"] = x.ID
	x.fieldMap["statistic_date"] = x.StatisticDate
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["robot_type"] = x.RobotType
	x.fieldMap["name"] = x.Name
	x.fieldMap["token"] = x.Token
	x.fieldMap["user_id"] = x.UserID
	x.fieldMap["tg_chat_id"] = x.TgChatID
	x.fieldMap["tg_user_name"] = x.TgUserName
	x.fieldMap["login_time"] = x.LoginTime
	x.fieldMap["register_time"] = x.RegisterTime
	x.fieldMap["first_recharge_time"] = x.FirstRechargeTime
	x.fieldMap["is_in_resource_db"] = x.IsInResourceDb
	x.fieldMap["bet_count"] = x.BetCount
	x.fieldMap["transfer_bet_count"] = x.TransferBetCount
	x.fieldMap["gift_usdt_status"] = x.GiftUsdtStatus
	x.fieldMap["gift_trx_status"] = x.GiftTrxStatus
	x.fieldMap["is_gift_usdt"] = x.IsGiftUsdt
	x.fieldMap["is_gift_trx"] = x.IsGiftTrx
	x.fieldMap["mission_gift_usdt"] = x.MissionGiftUsdt
	x.fieldMap["join_group_status"] = x.JoinGroupStatus
	x.fieldMap["banding_trx_address"] = x.BandingTrxAddress
	x.fieldMap["shard_link_cnt"] = x.ShardLinkCnt
	x.fieldMap["agent_count"] = x.AgentCount
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotStatisticsDay) clone(db *gorm.DB) xRobotStatisticsDay {
	x.xRobotStatisticsDayDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotStatisticsDay) replaceDB(db *gorm.DB) xRobotStatisticsDay {
	x.xRobotStatisticsDayDo.ReplaceDB(db)
	return x
}

type xRobotStatisticsDayDo struct{ gen.DO }

func (x xRobotStatisticsDayDo) Debug() *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotStatisticsDayDo) WithContext(ctx context.Context) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotStatisticsDayDo) ReadDB() *xRobotStatisticsDayDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotStatisticsDayDo) WriteDB() *xRobotStatisticsDayDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotStatisticsDayDo) Session(config *gorm.Session) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotStatisticsDayDo) Clauses(conds ...clause.Expression) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotStatisticsDayDo) Returning(value interface{}, columns ...string) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotStatisticsDayDo) Not(conds ...gen.Condition) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotStatisticsDayDo) Or(conds ...gen.Condition) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotStatisticsDayDo) Select(conds ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotStatisticsDayDo) Where(conds ...gen.Condition) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotStatisticsDayDo) Order(conds ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotStatisticsDayDo) Distinct(cols ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotStatisticsDayDo) Omit(cols ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotStatisticsDayDo) Join(table schema.Tabler, on ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotStatisticsDayDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotStatisticsDayDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotStatisticsDayDo) Group(cols ...field.Expr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotStatisticsDayDo) Having(conds ...gen.Condition) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotStatisticsDayDo) Limit(limit int) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotStatisticsDayDo) Offset(offset int) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotStatisticsDayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotStatisticsDayDo) Unscoped() *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotStatisticsDayDo) Create(values ...*model.XRobotStatisticsDay) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotStatisticsDayDo) CreateInBatches(values []*model.XRobotStatisticsDay, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotStatisticsDayDo) Save(values ...*model.XRobotStatisticsDay) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotStatisticsDayDo) First() (*model.XRobotStatisticsDay, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotStatisticsDay), nil
	}
}

func (x xRobotStatisticsDayDo) Take() (*model.XRobotStatisticsDay, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotStatisticsDay), nil
	}
}

func (x xRobotStatisticsDayDo) Last() (*model.XRobotStatisticsDay, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotStatisticsDay), nil
	}
}

func (x xRobotStatisticsDayDo) Find() ([]*model.XRobotStatisticsDay, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotStatisticsDay), err
}

func (x xRobotStatisticsDayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotStatisticsDay, err error) {
	buf := make([]*model.XRobotStatisticsDay, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotStatisticsDayDo) FindInBatches(result *[]*model.XRobotStatisticsDay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotStatisticsDayDo) Attrs(attrs ...field.AssignExpr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotStatisticsDayDo) Assign(attrs ...field.AssignExpr) *xRobotStatisticsDayDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotStatisticsDayDo) Joins(fields ...field.RelationField) *xRobotStatisticsDayDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotStatisticsDayDo) Preload(fields ...field.RelationField) *xRobotStatisticsDayDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotStatisticsDayDo) FirstOrInit() (*model.XRobotStatisticsDay, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotStatisticsDay), nil
	}
}

func (x xRobotStatisticsDayDo) FirstOrCreate() (*model.XRobotStatisticsDay, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotStatisticsDay), nil
	}
}

func (x xRobotStatisticsDayDo) FindByPage(offset int, limit int) (result []*model.XRobotStatisticsDay, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotStatisticsDayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotStatisticsDayDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotStatisticsDayDo) Delete(models ...*model.XRobotStatisticsDay) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotStatisticsDayDo) withDO(do gen.Dao) *xRobotStatisticsDayDo {
	x.DO = *do.(*gen.DO)
	return x
}
