package controller

import (
	"encoding/json"
	"errors"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utilsmodel"
)

type TiyanjinActiveController struct{}

func (c *TiyanjinActiveController) Init() {
	group := server.Http().NewGroup("/api/tiyanjin_active")
	{
		group.Post("/creat", c.creat)
		group.Post("/list", c.list)
		group.Post("/info", c.info)
		group.Post("/update", c.update)
		group.Post("/update_status", c.update_status)
		group.Post("/delete", c.delete)
	}
}

func (c *TiyanjinActiveController) creat(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTiyanjinActive
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "体验金活动", "增", "体验金活动创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	var rewardConfig []utilsmodel.TiyanjinActiveRewardConfig
	err := json.Unmarshal([]byte(reqdata.RewardConfig), &rewardConfig)
	if ctx.RespErr(err, &errcode) {
		return
	}

	reqdata.Creator = token.Account
	reqdata.Operator = token.Account

	dao := server.DaoxHashGame().XTiyanjinActive
	db := dao.WithContext(ctx.Gin())
	err = db.Create(&reqdata.XTiyanjinActive)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *TiyanjinActiveController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
		model.XTiyanjinActive
	}
	type Result struct {
		model.XTiyanjinActive
		SellerName string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "体验金活动", "查", "体验金活动查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTiyanjinActive
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}
	if reqdata.Status != 0 {
		db = db.Where(dao.Status.Eq(reqdata.Status))
	}
	var list []Result
	xSeller := server.DaoxHashGame().XSeller
	if limit > 0 {
		total, err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
			Select(dao.ALL, xSeller.SellerName).Order(dao.ID.Desc()).
			ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		ctx.Put("total", total)
	} else {
		err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
			Select(dao.ALL, xSeller.SellerName).Order(dao.ID.Desc()).
			Scan(&list)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	}
	ctx.Put("list", list)
	ctx.RespOK()
}

func (c *TiyanjinActiveController) info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id       int32
		SellerID int32 `json:"SellerId"`
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "体验金活动", "查", "体验金活动查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTiyanjinActive
	db := dao.WithContext(ctx.Gin())
	xSeller := server.DaoxHashGame().XSeller
	var data struct {
		model.XTiyanjinActive
		SellerName string
	}
	err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Where(dao.ID.Eq(reqdata.Id)).Select(dao.ALL, xSeller.SellerName).
		Scan(&data)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *TiyanjinActiveController) update(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTiyanjinActive
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "体验金活动", "改", "修改体验金活动")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	var rewardConfig []utilsmodel.TiyanjinActiveRewardConfig
	err := json.Unmarshal([]byte(reqdata.RewardConfig), &rewardConfig)
	if ctx.RespErr(err, &errcode) {
		return
	}
	reqdata.Operator = token.Account

	dao := server.DaoxHashGame().XTiyanjinActive
	db := dao.WithContext(ctx.Gin())
	info, err := db.Where(dao.ID.Eq(reqdata.ID)).
		Omit(dao.ID, dao.SellerID, dao.CreateTime, dao.Creator).
		Updates(&reqdata.XTiyanjinActive)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}

func (c *TiyanjinActiveController) update_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTiyanjinActive
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "体验金活动", "改", "修改体验金活动状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.Operator = token.Account

	dao := server.DaoxHashGame().XTiyanjinActive
	db := dao.WithContext(ctx.Gin())
	info, err := db.Where(dao.ID.Eq(reqdata.ID)).Select(dao.Status, dao.Operator).Update(dao.Status, reqdata.Status)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}

func (c *TiyanjinActiveController) delete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTiyanjinActive
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "活动管理", "体验金活动", "删", "删除体验金活动")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	reqdata.Operator = token.Account

	dao := server.DaoxHashGame().XTiyanjinActive
	db := dao.WithContext(ctx.Gin())
	info, err := db.Where(dao.ID.Eq(reqdata.ID)).Delete()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("RowsAffected", info.RowsAffected)
	ctx.RespOK()
}
