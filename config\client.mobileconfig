<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ConsentText</key>
	<dict>
		<key>default</key>
		<string>Please Install Me</string>
	</dict>
	<key>PayloadContent</key>
	<array>
		<dict>
			<key>FullScreen</key>
			<true/>
			<key>Icon</key>
			<data>
                {{.Icon}}
			</data>
			<key>IsRemovable</key>
			<true/>
			<key>Label</key>
			<string>{{.AppName}}</string>
			<key>PayloadDescription</key>
			<string>Adds a Web Clip.</string>
			<key>PayloadDisplayName</key>
			<string>Web Clip ({{.AppName}})</string>
			<key>PayloadIdentifier</key>
			<string>{{.AppName}}.webclip1</string>
			<key>PayloadOrganization</key>
			<string>com.yydsplus.noticehash</string>
			<key>PayloadType</key>
			<string>com.apple.webClip.managed</string>
			<key>PayloadUUID</key>
			<string>B98A9696-9961-4F2B-82F2-1C06781AAFD9</string>
			<key>PayloadVersion</key>
			<integer>1</integer>
			<key>URL</key>
			<string>{{.Host}}</string>
		</dict>
	</array>
	<key>PayloadDescription</key>
	<string>An exciting game</string>
	<key>PayloadDisplayName</key>
	<string>{{.AppName}}</string>
	<key>PayloadIdentifier</key>
	<string>{{.AppName}}</string>
	<key>PayloadOrganization</key>
	<string>com.yydsplus.noticehash</string>
	<key>PayloadRemovalDisallowed</key>
	<false/>
	<key>PayloadType</key>
	<string>Configuration</string>
	<key>PayloadUUID</key>
	<string>47CAB68E-D570-4E26-83A1-D13FDEA082A8</string>
	<key>PayloadVersion</key>
	<integer>1</integer>
</dict>
</plist>
