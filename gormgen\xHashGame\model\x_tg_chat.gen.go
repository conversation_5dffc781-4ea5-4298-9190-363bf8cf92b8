// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgChat = "x_tg_chat"

// XTgChat Tg聊天室
type XTgChat struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Type       string    `gorm:"column:Type;comment:聊天室类型 private: 私人 group:普通群组 supergroup:超级群组 channel:频道" json:"Type"` // 聊天室类型 private: 私人 group:普通群组 supergroup:超级群组 channel:频道
	GuideID    int64     `gorm:"column:GuideId;primaryKey;comment:接待机器人ID" json:"GuideId"`                                // 接待机器人ID
	TgRobotID  int64     `gorm:"column:TgRobotId;primaryKey;comment:Tg机器人ID" json:"TgRobotId"`                            // Tg机器人ID
	ChatID     int64     `gorm:"column:ChatId;primaryKey;comment:聊天室ID" json:"ChatId"`                                    // 聊天室ID
	TgID       int64     `gorm:"column:TgId;primaryKey;comment:Tg用户/群组ID" json:"TgId"`                                    // Tg用户/群组ID
	Lang       string    `gorm:"column:Lang;comment:语言" json:"Lang"`                                                      // 语言
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
}

// TableName XTgChat's table name
func (*XTgChat) TableName() string {
	return TableNameXTgChat
}
