// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentCode(db *gorm.DB, opts ...gen.DOOption) xAgentCode {
	_xAgentCode := xAgentCode{}

	_xAgentCode.xAgentCodeDo.UseDB(db, opts...)
	_xAgentCode.xAgentCodeDo.UseModel(&model.XAgentCode{})

	tableName := _xAgentCode.xAgentCodeDo.TableName()
	_xAgentCode.ALL = field.NewAsterisk(tableName)
	_xAgentCode.ID = field.NewInt32(tableName, "Id")
	_xAgentCode.UserID = field.NewInt32(tableName, "UserId")
	_xAgentCode.AgentCode = field.NewString(tableName, "AgentCode")
	_xAgentCode.FenCheng = field.NewFloat64(tableName, "FenCheng")
	_xAgentCode.RegisterCount = field.NewInt32(tableName, "RegisterCount")
	_xAgentCode.BetCount = field.NewInt32(tableName, "BetCount")
	_xAgentCode.CreateTime = field.NewTime(tableName, "CreateTime")

	_xAgentCode.fillFieldMap()

	return _xAgentCode
}

type xAgentCode struct {
	xAgentCodeDo xAgentCodeDo

	ALL           field.Asterisk
	ID            field.Int32   // id
	UserID        field.Int32   // 玩家id
	AgentCode     field.String  // 推广码
	FenCheng      field.Float64 // 分成比例
	RegisterCount field.Int32   // 注册人数
	BetCount      field.Int32   // 投注人数
	CreateTime    field.Time    // 创建时间

	fieldMap map[string]field.Expr
}

func (x xAgentCode) Table(newTableName string) *xAgentCode {
	x.xAgentCodeDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentCode) As(alias string) *xAgentCode {
	x.xAgentCodeDo.DO = *(x.xAgentCodeDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentCode) updateTableName(table string) *xAgentCode {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.AgentCode = field.NewString(table, "AgentCode")
	x.FenCheng = field.NewFloat64(table, "FenCheng")
	x.RegisterCount = field.NewInt32(table, "RegisterCount")
	x.BetCount = field.NewInt32(table, "BetCount")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentCode) WithContext(ctx context.Context) *xAgentCodeDo {
	return x.xAgentCodeDo.WithContext(ctx)
}

func (x xAgentCode) TableName() string { return x.xAgentCodeDo.TableName() }

func (x xAgentCode) Alias() string { return x.xAgentCodeDo.Alias() }

func (x xAgentCode) Columns(cols ...field.Expr) gen.Columns { return x.xAgentCodeDo.Columns(cols...) }

func (x *xAgentCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentCode) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 7)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["AgentCode"] = x.AgentCode
	x.fieldMap["FenCheng"] = x.FenCheng
	x.fieldMap["RegisterCount"] = x.RegisterCount
	x.fieldMap["BetCount"] = x.BetCount
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xAgentCode) clone(db *gorm.DB) xAgentCode {
	x.xAgentCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentCode) replaceDB(db *gorm.DB) xAgentCode {
	x.xAgentCodeDo.ReplaceDB(db)
	return x
}

type xAgentCodeDo struct{ gen.DO }

func (x xAgentCodeDo) Debug() *xAgentCodeDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentCodeDo) WithContext(ctx context.Context) *xAgentCodeDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentCodeDo) ReadDB() *xAgentCodeDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentCodeDo) WriteDB() *xAgentCodeDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentCodeDo) Session(config *gorm.Session) *xAgentCodeDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentCodeDo) Clauses(conds ...clause.Expression) *xAgentCodeDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentCodeDo) Returning(value interface{}, columns ...string) *xAgentCodeDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentCodeDo) Not(conds ...gen.Condition) *xAgentCodeDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentCodeDo) Or(conds ...gen.Condition) *xAgentCodeDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentCodeDo) Select(conds ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentCodeDo) Where(conds ...gen.Condition) *xAgentCodeDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentCodeDo) Order(conds ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentCodeDo) Distinct(cols ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentCodeDo) Omit(cols ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentCodeDo) Join(table schema.Tabler, on ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentCodeDo) Group(cols ...field.Expr) *xAgentCodeDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentCodeDo) Having(conds ...gen.Condition) *xAgentCodeDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentCodeDo) Limit(limit int) *xAgentCodeDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentCodeDo) Offset(offset int) *xAgentCodeDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentCodeDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentCodeDo) Unscoped() *xAgentCodeDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentCodeDo) Create(values ...*model.XAgentCode) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentCodeDo) CreateInBatches(values []*model.XAgentCode, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentCodeDo) Save(values ...*model.XAgentCode) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentCodeDo) First() (*model.XAgentCode, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCode), nil
	}
}

func (x xAgentCodeDo) Take() (*model.XAgentCode, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCode), nil
	}
}

func (x xAgentCodeDo) Last() (*model.XAgentCode, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCode), nil
	}
}

func (x xAgentCodeDo) Find() ([]*model.XAgentCode, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentCode), err
}

func (x xAgentCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentCode, err error) {
	buf := make([]*model.XAgentCode, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentCodeDo) FindInBatches(result *[]*model.XAgentCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentCodeDo) Attrs(attrs ...field.AssignExpr) *xAgentCodeDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentCodeDo) Assign(attrs ...field.AssignExpr) *xAgentCodeDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentCodeDo) Joins(fields ...field.RelationField) *xAgentCodeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentCodeDo) Preload(fields ...field.RelationField) *xAgentCodeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentCodeDo) FirstOrInit() (*model.XAgentCode, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCode), nil
	}
}

func (x xAgentCodeDo) FirstOrCreate() (*model.XAgentCode, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCode), nil
	}
}

func (x xAgentCodeDo) FindByPage(offset int, limit int) (result []*model.XAgentCode, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentCodeDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentCodeDo) Delete(models ...*model.XAgentCode) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentCodeDo) withDO(do gen.Dao) *xAgentCodeDo {
	x.DO = *do.(*gen.DO)
	return x
}
