// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsUserProfile = "x_ads_user_profile"

// XAdsUserProfile 用户画像
type XAdsUserProfile struct {
	UserID                         int64     `gorm:"column:user_id;primaryKey;comment:用户ID" json:"user_id"`                                                           // 用户ID
	SellerID                       int32     `gorm:"column:seller_id;comment:运营商" json:"seller_id"`                                                                   // 运营商
	ChannelID                      int32     `gorm:"column:channel_id;comment:渠道" json:"channel_id"`                                                                  // 渠道
	TgChatID                       int64     `gorm:"column:tg_chat_id;comment:用户飞机ID" json:"tg_chat_id"`                                                              // 用户飞机ID
	Account                        string    `gorm:"column:account;comment:用户账号" json:"account"`                                                                      // 用户账号
	AccountType                    int32     `gorm:"column:account_type;comment: 1:账号, 2:手机号, 3:Email, 4:Google, 5:Telegram" json:"account_type"`                     //  1:账号, 2:手机号, 3:Email, 4:Google, 5:Telegram
	UserBetTypes                   int32     `gorm:"column:user_bet_types;comment:用户类型 0:未投注,1:综合,2:哈希,3.电子,4:彩票,5:棋牌,6:体育,7:真人" json:"user_bet_types"`               // 用户类型 0:未投注,1:综合,2:哈希,3.电子,4:彩票,5:棋牌,6:体育,7:真人
	IsAgent                        int32     `gorm:"column:is_agent;comment:是否代理 1是,2不是" json:"is_agent"`                                                             // 是否代理 1是,2不是
	TopAgentID                     int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                                          // 顶级代理ID
	Amount                         float64   `gorm:"column:amount;default:0.00;comment:用户游戏账户余额" json:"amount"`                                                       // 用户游戏账户余额
	BonusAmount                    float64   `gorm:"column:bonus_amount;default:0.00;comment:用户额外奖金余额" json:"bonus_amount"`                                           // 用户额外奖金余额
	BetCount                       int32     `gorm:"column:bet_count;comment:用户投注次数" json:"bet_count"`                                                                // 用户投注次数
	MaxStreakBet                   int32     `gorm:"column:max_streak_bet;comment:用户最大连胜投注数" json:"max_streak_bet"`                                                   // 用户最大连胜投注数
	RegisterTime                   time.Time `gorm:"column:register_time;comment:用户注册时间" json:"register_time"`                                                        // 用户注册时间
	FirstBetTime                   time.Time `gorm:"column:first_bet_time;comment:用户首次投注时间" json:"first_bet_time"`                                                    // 用户首次投注时间
	LastBetTime                    time.Time `gorm:"column:last_bet_time;comment:用户最后投注时间" json:"last_bet_time"`                                                      // 用户最后投注时间
	FirstRechargeTime              time.Time `gorm:"column:first_recharge_time;comment:首次充值时间" json:"first_recharge_time"`                                            // 首次充值时间
	LastRechargeTime               time.Time `gorm:"column:last_recharge_time;comment:最后充值时间" json:"last_recharge_time"`                                              // 最后充值时间
	RechargeMaxStreakBetCnt        int32     `gorm:"column:recharge_max_streak_bet_cnt;comment:充值后连续多少天投注" json:"recharge_max_streak_bet_cnt"`                        // 充值后连续多少天投注
	RechargeMaxStreakNotBetCnt     int32     `gorm:"column:recharge_max_streak_not_bet_cnt;comment:充值后连续多少天未投注" json:"recharge_max_streak_not_bet_cnt"`               // 充值后连续多少天未投注
	FirstWithdrawFinishTime        time.Time `gorm:"column:first_withdraw_finish_time;comment:首次出款时间" json:"first_withdraw_finish_time"`                              // 首次出款时间
	LastWithdrawFinishTime         time.Time `gorm:"column:last_withdraw_finish_time;comment:最后出款时间" json:"last_withdraw_finish_time"`                                // 最后出款时间
	WithdrawSuccessCount           int32     `gorm:"column:withdraw_success_count;comment:出款次数" json:"withdraw_success_count"`                                        // 出款次数
	LastRechargeSinceDays          int32     `gorm:"column:last_recharge_since_days;comment:充值后多少天未复充" json:"last_recharge_since_days"`                               // 充值后多少天未复充
	IsInResourceDb                 int32     `gorm:"column:is_in_resource_db;comment:是否在库(0:否 1:是)" json:"is_in_resource_db"`                                         // 是否在库(0:否 1:是)
	IsUseRobot                     int32     `gorm:"column:is_use_robot;comment:是否使用机器人0：否 1是" json:"is_use_robot"`                                                   // 是否使用机器人0：否 1是
	AllBet                         float64   `gorm:"column:all_bet;default:0.00;comment:总投注" json:"all_bet"`                                                          // 总投注
	AllWin                         float64   `gorm:"column:all_win;default:0.00;comment:玩家总赢" json:"all_win"`                                                         // 玩家总赢
	AllWinLoss                     float64   `gorm:"column:all_win_loss;default:0.00;comment:总输赢" json:"all_win_loss"`                                                // 总输赢
	UsdtWinLoss                    float64   `gorm:"column:usdt_win_loss;default:0.00;comment:usdt输赢" json:"usdt_win_loss"`                                           // usdt输赢
	TrxWinLoss                     float64   `gorm:"column:trx_win_loss;default:0.00;comment:trx输赢" json:"trx_win_loss"`                                              // trx输赢
	GiftUsdtStat                   int32     `gorm:"column:gift_usdt_stat;comment:用户体验金U  领取状态 0：没领取" json:"gift_usdt_stat"`                                          // 用户体验金U  领取状态 0：没领取
	GiftTrxStat                    int32     `gorm:"column:gift_trx_stat;comment:用户体验金Trx 领取状态 0：没领取" json:"gift_trx_stat"`                                           // 用户体验金Trx 领取状态 0：没领取
	RechargeAmount                 float64   `gorm:"column:recharge_amount;default:0.00;comment:充值额" json:"recharge_amount"`                                          // 充值额
	RechargeCount                  int32     `gorm:"column:recharge_count;comment:充值次数" json:"recharge_count"`                                                        // 充值次数
	WithdrawCount                  int32     `gorm:"column:withdraw_count;comment:兑换次数" json:"withdraw_count"`                                                        // 兑换次数
	WithdrawAmount                 float64   `gorm:"column:withdraw_amount;default:0.00;comment:兑换金额" json:"withdraw_amount"`                                         // 兑换金额
	FirstRechargeAmount            float64   `gorm:"column:first_recharge_amount;default:0.00;comment:首充金额" json:"first_recharge_amount"`                             // 首充金额
	FirstRechargeTime1             time.Time `gorm:"column:first_recharge_time1;comment:首充时间" json:"first_recharge_time1"`                                            // 首充时间
	HashBalanceBetCount            int32     `gorm:"column:hash_balance_bet_count;comment:哈希余额游戏总投注次数" json:"hash_balance_bet_count"`                                 // 哈希余额游戏总投注次数
	HashBalanceWinCount            int32     `gorm:"column:hash_balance_win_count;comment:哈希余额游戏赢次数" json:"hash_balance_win_count"`                                   // 哈希余额游戏赢次数
	HashBalanceBetAmount           float64   `gorm:"column:hash_balance_bet_amount;default:0.00;comment:哈希余额游戏总投注额" json:"hash_balance_bet_amount"`                   // 哈希余额游戏总投注额
	HashBalanceWinAmount           float64   `gorm:"column:hash_balance_win_amount;default:0.00;comment:哈希余额游戏赢额" json:"hash_balance_win_amount"`                     // 哈希余额游戏赢额
	HashBalanceValidBetAmount      float64   `gorm:"column:hash_balance_valid_bet_amount;default:0.00;comment:哈希余额有效" json:"hash_balance_valid_bet_amount"`           // 哈希余额有效
	HashBalanceFee                 float64   `gorm:"column:hash_balance_fee;default:0.00;comment:哈希余额手续费" json:"hash_balance_fee"`                                    // 哈希余额手续费
	HashTransferUsdtWinLoss        float64   `gorm:"column:hash_transfer_usdt_win_loss;default:0.00;comment:转账u输赢" json:"hash_transfer_usdt_win_loss"`                // 转账u输赢
	HashTransferTrxWinLoss         float64   `gorm:"column:hash_transfer_trx_win_loss;default:0.00;comment:转账trx输赢" json:"hash_transfer_trx_win_loss"`                // 转账trx输赢
	HashTransferUsdtBetCount       int32     `gorm:"column:hash_transfer_usdt_bet_count;comment:转账usdt次数" json:"hash_transfer_usdt_bet_count"`                        // 转账usdt次数
	HashTransferTrxBetCount        int32     `gorm:"column:hash_transfer_trx_bet_count;comment:转账trx投注次数" json:"hash_transfer_trx_bet_count"`                         // 转账trx投注次数
	HashTransferUsdtWinCount       int32     `gorm:"column:hash_transfer_usdt_win_count;comment:转账赢次数usdt" json:"hash_transfer_usdt_win_count"`                       // 转账赢次数usdt
	HashTransferTrxWinCount        int32     `gorm:"column:hash_transfer_trx_win_count;comment:转账赢次数" json:"hash_transfer_trx_win_count"`                             // 转账赢次数
	HashTransferUsdtBetAmount      float64   `gorm:"column:hash_transfer_usdt_bet_amount;default:0.00;comment:转账总投注usdt" json:"hash_transfer_usdt_bet_amount"`        // 转账总投注usdt
	HashTransferTrxBetAmountU      float64   `gorm:"column:hash_transfer_trx_bet_amount_u;default:0.00;comment:trx转账总投注 算U单位" json:"hash_transfer_trx_bet_amount_u"`  // trx转账总投注 算U单位
	HashTransferTrxBetAmount       float64   `gorm:"column:hash_transfer_trx_bet_amount;default:0.00;comment:转账总投注" json:"hash_transfer_trx_bet_amount"`              // 转账总投注
	HashTransferTrxWinAmountU      float64   `gorm:"column:hash_transfer_trx_win_amount_u;default:0.00;comment:转账赢 U单位" json:"hash_transfer_trx_win_amount_u"`        // 转账赢 U单位
	HashTransferUsdtWinAmount      float64   `gorm:"column:hash_transfer_usdt_win_amount;default:0.00;comment:转账赢次数" json:"hash_transfer_usdt_win_amount"`            // 转账赢次数
	HashTransferTrxWinAmount       float64   `gorm:"column:hash_transfer_trx_win_amount;default:0.00;comment:转账赢次数" json:"hash_transfer_trx_win_amount"`              // 转账赢次数
	HashTransferUsdtValidBetAmount float64   `gorm:"column:hash_transfer_usdt_valid_bet_amount;default:0.00;comment:转账有效" json:"hash_transfer_usdt_valid_bet_amount"` // 转账有效
	HashTransferTrxValidBetAmount  float64   `gorm:"column:hash_transfer_trx_valid_bet_amount;default:0.00;comment:转账有效" json:"hash_transfer_trx_valid_bet_amount"`   // 转账有效
	HashTransferUsdtFee            float64   `gorm:"column:hash_transfer_usdt_fee;default:0.00;comment:转账玩法手续费" json:"hash_transfer_usdt_fee"`                        // 转账玩法手续费
	HashTransferTrxFee             float64   `gorm:"column:hash_transfer_trx_fee;default:0.00;comment:转账玩法手续费" json:"hash_transfer_trx_fee"`                          // 转账玩法手续费
	ThirdElecBetCount              int32     `gorm:"column:third_elec_bet_count;comment:三方电子投注次数" json:"third_elec_bet_count"`                                        // 三方电子投注次数
	ThirdElecWinCount              int32     `gorm:"column:third_elec_win_count;comment:三方电子赢次数" json:"third_elec_win_count"`                                         // 三方电子赢次数
	ThirdElecBetAmount             float64   `gorm:"column:third_elec_bet_amount;default:0.00;comment:三方电子投注" json:"third_elec_bet_amount"`                           // 三方电子投注
	ThirdElecWinAmount             float64   `gorm:"column:third_elec_win_amount;default:0.00;comment:三方电子赢额" json:"third_elec_win_amount"`                           // 三方电子赢额
	ThirdElecValidBetAmount        float64   `gorm:"column:third_elec_valid_bet_amount;default:0.00;comment:三方电子有效投注额" json:"third_elec_valid_bet_amount"`            // 三方电子有效投注额
	ThirdElecFee                   float64   `gorm:"column:third_elec_fee;default:0.00;comment:三方电子手续费" json:"third_elec_fee"`                                        // 三方电子手续费
	ThirdLotteryBetCount           int32     `gorm:"column:third_lottery_bet_count;comment:三方彩票" json:"third_lottery_bet_count"`                                      // 三方彩票
	ThirdLotteryWinCount           int32     `gorm:"column:third_lottery_win_count;comment:三方彩票" json:"third_lottery_win_count"`                                      // 三方彩票
	ThirdLotteryBetAmount          float64   `gorm:"column:third_lottery_bet_amount;default:0.00;comment:三方彩票" json:"third_lottery_bet_amount"`                       // 三方彩票
	ThirdLotteryWinAmount          float64   `gorm:"column:third_lottery_win_amount;default:0.00;comment:三方彩票" json:"third_lottery_win_amount"`                       // 三方彩票
	ThirdLotteryValidBetAmount     float64   `gorm:"column:third_lottery_valid_bet_amount;default:0.00;comment:三方彩票" json:"third_lottery_valid_bet_amount"`           // 三方彩票
	ThirdLotteryFee                float64   `gorm:"column:third_lottery_fee;default:0.00;comment:三方彩票手续费" json:"third_lottery_fee"`                                  // 三方彩票手续费
	ThirdChessBetCount             int32     `gorm:"column:third_chess_bet_count;comment:棋牌" json:"third_chess_bet_count"`                                            // 棋牌
	ThirdChessWinCount             int32     `gorm:"column:third_chess_win_count;comment:棋牌" json:"third_chess_win_count"`                                            // 棋牌
	ThirdChessBetAmount            float64   `gorm:"column:third_chess_bet_amount;default:0.00;comment:棋牌" json:"third_chess_bet_amount"`                             // 棋牌
	ThirdChessWinAmount            float64   `gorm:"column:third_chess_win_amount;default:0.00;comment:棋牌" json:"third_chess_win_amount"`                             // 棋牌
	ThirdChessValidBetAmount       float64   `gorm:"column:third_chess_valid_bet_amount;default:0.00;comment:棋牌" json:"third_chess_valid_bet_amount"`                 // 棋牌
	ThirdChessFee                  float64   `gorm:"column:third_chess_fee;default:0.00;comment:棋牌" json:"third_chess_fee"`                                           // 棋牌
	ThirdSmallBetCount             int32     `gorm:"column:third_small_bet_count;comment:小游戏" json:"third_small_bet_count"`                                           // 小游戏
	ThirdSmallWinCount             int32     `gorm:"column:third_small_win_count;comment:小游戏" json:"third_small_win_count"`                                           // 小游戏
	ThirdSmallBetAmount            float64   `gorm:"column:third_small_bet_amount;default:0.00;comment:小游戏" json:"third_small_bet_amount"`                            // 小游戏
	ThirdSmallWinAmount            float64   `gorm:"column:third_small_win_amount;default:0.00;comment:小游戏" json:"third_small_win_amount"`                            // 小游戏
	ThirdSmallValidBetAmount       float64   `gorm:"column:third_small_valid_bet_amount;default:0.00;comment:小游戏" json:"third_small_valid_bet_amount"`                // 小游戏
	ThirdSmallFee                  float64   `gorm:"column:third_small_fee;default:0.00;comment:小游戏" json:"third_small_fee"`                                          // 小游戏
	ThirdLiveBetCount              int32     `gorm:"column:third_live_bet_count;comment:真人" json:"third_live_bet_count"`                                              // 真人
	ThirdLiveWinCount              int32     `gorm:"column:third_live_win_count;comment:真人" json:"third_live_win_count"`                                              // 真人
	ThirdLiveBetAmount             float64   `gorm:"column:third_live_bet_amount;default:0.00;comment:真人" json:"third_live_bet_amount"`                               // 真人
	ThirdLiveWinAmount             float64   `gorm:"column:third_live_win_amount;default:0.00;comment:真人" json:"third_live_win_amount"`                               // 真人
	ThirdLiveValidBetAmount        float64   `gorm:"column:third_live_valid_bet_amount;default:0.00;comment:真人" json:"third_live_valid_bet_amount"`                   // 真人
	ThirdLiveFee                   float64   `gorm:"column:third_live_fee;default:0.00;comment:真人" json:"third_live_fee"`                                             // 真人
	ThirdSportBetCount             int32     `gorm:"column:third_sport_bet_count;comment:体育" json:"third_sport_bet_count"`                                            // 体育
	ThirdSportWinCount             int32     `gorm:"column:third_sport_win_count;comment:体育" json:"third_sport_win_count"`                                            // 体育
	ThirdSportBetAmount            float64   `gorm:"column:third_sport_bet_amount;default:0.00;comment:体育" json:"third_sport_bet_amount"`                             // 体育
	ThirdSportWinAmount            float64   `gorm:"column:third_sport_win_amount;default:0.00;comment:体育" json:"third_sport_win_amount"`                             // 体育
	ThirdSportValidBetAmount       float64   `gorm:"column:third_sport_valid_bet_amount;default:0.00;comment:体育" json:"third_sport_valid_bet_amount"`                 // 体育
	ThirdSportFee                  float64   `gorm:"column:third_sport_fee;default:0.00;comment:体育" json:"third_sport_fee"`                                           // 体育
	CreateTime                     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:数据创建日期" json:"create_time"`                                  // 数据创建日期
	UpdateTime                     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:数据更新日期" json:"update_time"`                                  // 数据更新日期
}

// TableName XAdsUserProfile's table name
func (*XAdsUserProfile) TableName() string {
	return TableNameXAdsUserProfile
}
