package controller

import (
	"encoding/json"
	"fmt"
	"strconv"
	"xserver/abugo"
	"xserver/server"
)

type SetOnlineCntController struct {
}

type SetOnlineCntData struct {
	Display    int32 `json:"display"`    // 在玩人数是否显示
	DefaultMin int32 `json:"defaultMin"` // 为0时默认最小值
	DefaultMax int32 `json:"defaultMax"` // 为0时默认最大值
	Data       []struct {
		GameType int     `json:"gameType"`
		Brand    string  `json:"brand"`
		Multiple float32 `json:"multiple"`
	} `json:"data"`
}

type SetOnlineCntRequestData struct {
	Display    int32               `json:"Display"`
	DefaultMin int32               `json:"DefaultMin"`
	DefaultMax int32               `json:"DefaultMax"`
	GameType   int                 `json:"GameType"`
	Multiple   float32             `json:"Multiple"`
	Brand      string              `json:"Brand"`
	AllBrands  map[string][]string `json:"AllBrands"`
	GoogleCode string
}

func (c *SetOnlineCntController) Init() {
	group := server.Http().NewGroup("/api/setOnline")
	{
		group.Post("/index", c.index)
		group.Post("/update", c.update)
	}
}

func (c *SetOnlineCntController) index(ctx *abugo.AbuHttpContent) {
	var setOnlineCntData SetOnlineCntData
	// 从redis hash list中获取配置数据
	redisData := server.Redis().HGet("CONFIG", "SET_ONLINE_CNT")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)

		// 反序列化为 setOnlineCntData
		if err := json.Unmarshal([]byte(dataStr), &setOnlineCntData); err != nil {
			fmt.Println("JSON 反序列化失败:", err)
			return
		}
	}

	ctx.RespOK(setOnlineCntData)
}

func (c *SetOnlineCntController) update(ctx *abugo.AbuHttpContent) {
	reqdata := SetOnlineCntRequestData{}
	errcode := 0

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "运营管理", "在玩人数配置", "改", "修改在玩人数配置")
	if token == nil {
		return
	}

	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	var setOnlineCntData SetOnlineCntData
	// 从redis hash list中获取配置数据
	redisData := server.Redis().HGet("CONFIG", "SET_ONLINE_CNT")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)
		json.Unmarshal([]byte(dataStr), &setOnlineCntData)
	}

	setOnlineCntData.Display = reqdata.Display
	setOnlineCntData.DefaultMin = reqdata.DefaultMin
	setOnlineCntData.DefaultMax = reqdata.DefaultMax

	if reqdata.GameType != 999 && reqdata.Brand != "all" {
		// 查看配置是否不为空
		if setOnlineCntData.Data != nil {
			isNew := true
			for i, v := range setOnlineCntData.Data {
				// 如果配置存在
				if v.Brand == reqdata.Brand && v.GameType == reqdata.GameType {
					setOnlineCntData.Data[i].Multiple = reqdata.Multiple
					isNew = false
					break
				}
			}

			if isNew {
				setOnlineCntData.Data = append(setOnlineCntData.Data, struct {
					GameType int     `json:"gameType"`
					Brand    string  `json:"brand"`
					Multiple float32 `json:"multiple"`
				}{
					GameType: reqdata.GameType,
					Brand:    reqdata.Brand,
					Multiple: reqdata.Multiple,
				})
			}

		} else {
			// 新增配置
			setOnlineCntData.Data = append(setOnlineCntData.Data, struct {
				GameType int     `json:"gameType"`
				Brand    string  `json:"brand"`
				Multiple float32 `json:"multiple"`
			}{
				GameType: reqdata.GameType,
				Brand:    reqdata.Brand,
				Multiple: reqdata.Multiple,
			})
		}
	}

	if reqdata.GameType == 999 && reqdata.Brand == "all" && reqdata.AllBrands != nil {
		// 将setOnlineCntData.Data置空
		setOnlineCntData.Data = nil
		for gameType, brands := range reqdata.AllBrands {
			gameType, _ := strconv.Atoi(gameType)
			for _, brand := range brands {
				setOnlineCntData.Data = append(setOnlineCntData.Data, struct {
					GameType int     `json:"gameType"`
					Brand    string  `json:"brand"`
					Multiple float32 `json:"multiple"`
				}{
					GameType: gameType,
					Brand:    brand,
					Multiple: reqdata.Multiple,
				})
			}
		}
	}

	if reqdata.GameType != 999 && reqdata.Brand == "all" && reqdata.AllBrands != nil {
		// 删除setOnlineCntData.Data中Gametype为reqdata.GameType的数据
		for i := len(setOnlineCntData.Data) - 1; i >= 0; i-- {
			if setOnlineCntData.Data[i].GameType == reqdata.GameType {
				setOnlineCntData.Data = append(setOnlineCntData.Data[:i], setOnlineCntData.Data[i+1:]...)
			}
		}
		// 将allbrands中的数据新增到配置中
		for _, brand := range reqdata.AllBrands[strconv.Itoa(reqdata.GameType)] {
			setOnlineCntData.Data = append(setOnlineCntData.Data, struct {
				GameType int     `json:"gameType"`
				Brand    string  `json:"brand"`
				Multiple float32 `json:"multiple"`
			}{
				GameType: reqdata.GameType,
				Brand:    brand,
				Multiple: reqdata.Multiple,
			})
		}

	}

	// 更新redis
	server.Redis().HSet("CONFIG", "SET_ONLINE_CNT", setOnlineCntData)

	ctx.RespOK()
}
