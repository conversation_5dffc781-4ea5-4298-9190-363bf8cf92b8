package userManger

import (
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"log"
	"os"
	"path/filepath"
	"runtime/debug"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/server"
)

const (
	// 转化条件: 注册-领取体验金(投注)当日未充值
	TransCond1 int = 1
	// 转化条件: 注册-领取体验金(投注)次日未充值
	TransCond2 int = 2
	// 转化条件: 注册-领取体验金(投注)3日未充值
	TransCond3 int = 3
	// 转化条件: 注册-领取体验金(投注)7日未充值
	TransCond4 int = 4
	// 转化条件: 注册-领取体验金(投注)15日未充值
	TransCond5 int = 5
	// 转化条件: 注册-领取体验金(未投注)当日未充值
	TransCond6 int = 6
	// 转化条件:注册-领取体验金(未投注)次日未充值
	TransCond7 int = 7
	// 转化条件:注册-领取体验金(未投注)3日未充值
	TransCond8 int = 8
	// 转化条件:注册-领取体验金(未投注)7日未充值
	TransCond9 int = 9
	// 转化条件:注册-领取体验金(未投注)15日未充值
	TransCond10 int = 10
	// 转化条件: 注册-未领取体验金-当日未充值
	TransCond11 int = 11
	// 转化条件: 注册-未领取体验金-次日未充值
	TransCond12 int = 12
	// 转化条件: 注册-未领取体验金-3日未充值
	TransCond13 int = 13
	// 转化条件: 注册-未领取体验金-7日未充值
	TransCond14 int = 14
	// 转化条件: 注册-未领取体验金-15日未充值
	TransCond15 int = 15
)

const (
	// 留存条件: 次日未登录
	RetentionCond1 int = 1
	// 留存条件: 3日未登录
	RetentionCond2 int = 2
	// 留存条件: 7日未登录
	RetentionCond3 int = 3
	// 留存条件: 15日未登录
	RetentionCond4 int = 4
)

const (
	// 充值属性: 多次充值但低频(累计充值>=2次,近7天未充值)
	RechargeAttr1 int = 1
	// 充值属性: 高频小额充值(近7日充值>=3次,每次< 50 USDT)
	RechargeAttr2 int = 2
	// 充值属性: 高频大额充值(近7日充值>=3次,均值>= 100 USDT)
	RechargeAttr3 int = 3
	// 充值属性: 高频投注未中奖(连续投注3天, 中奖< 总投注10%)
	RechargeAttr4 int = 4
)

//var rdb = server.Rdb

type UserClassController struct {
}

func (this *UserClassController) Init() {
	group := server.Http().NewGroup("/api/user/class")
	{
		group.Post("/list", this.list)
	}

}

// 1. 参数验证和初始化
type ClassReq struct {
	Page      int `json:"page" form:"page"` // 页码
	PageSize  int `json:"pageSize"`         // 页大小
	SellerId  int `json:"sellerId"`         // 运营商
	ChannelId int `json:"channelId"`        // 渠道ID
	UserId    int `json:"userId"`           // 用户ID

	TopAgentId    int     `json:"topAgentId"`    // 顶级代理
	RechargeStart float64 `json:"rechargeStart"` // 充值范围下限
	RechargeEnd   float64 `json:"rechargeEnd"`   // 充值范围上限
	BetStart      float64 `json:"betStart"`      // 投注范围下限
	BetEnd        float64 `json:"betEnd"`        // 投注范围上限

	ProfitStart float64 `json:"profitStart"` // 盈亏范围下限
	ProfitEnd   float64 `json:"profitEnd"`   // 盈亏范围上限
	StartTime   int64   `json:"startTime"`   // 开始日期
	EndTime     int64   `json:"endTime"`     // 结束日期
	TransCond   int     `json:"transCond"`   // 转化条件

	RetentionCond int    `json:"retentionCond"` // 留存条件
	VipLv         int    `json:"vipLv"`         // VIP等级
	BetType       int    `json:"betType"`       // 投注类型
	UserLabelName string `json:"userLabelName"` // 用户标签ID
	RechargeType  int    `json:"rechargeType"`  // 充值属性类型

	IsBot       int `json:"isBot"`       // 是否为机器人
	HasRecharge int `json:"hasRecharge"` // 是否有充值条件
	HasBet      int `json:"hasBet"`      // 是否有投注条件
	HasProfit   int `json:"hasProfit"`   // 是否有盈亏条件

	Export int `json:"export"` // 是否导出
}

// 返回结果
type ClassResp struct {
	SellerId    int      `gorm:"column:SellerId" json:"sellerId"`             // 运营商
	ChannelId   int      `gorm:"column:ChannelId" json:"channelId"`           // 渠道ID
	SellerName  string   `json:"seller_name"`                                 // 运营商
	ChannelName string   `json:"channel_name"`                                // 渠道ID
	UserId      int64    `gorm:"column:UserId" json:"userId"`                 // 用户ID
	UserLabel   []string `json:"user_label_name"`                             // 用户标签
	BetTypes    int      `gorm:"column:user_bet_types" json:"user_bet_types"` // 投注类型

	Email       string `gorm:"column:Email" json:"email"`             // 用户邮箱
	PhoneNum    string `gorm:"column:PhoneNum" json:"phoneNum"`       // 手机号
	TopAgentId  int    `gorm:"column:TopAgentId" json:"topAgentId"`   // 顶级代理
	VipLv       int    `gorm:"column:VipLevel" json:"vipLevel"`       // VIP等级
	AccountType int    `gorm:"column:AccountType" json:"accountType"` // 是否机器人
	TGUserName  string `gorm:"column:tgUserName" json:"tgUserName"`   // 飞机用户名

	RegisterTime   *time.Time `gorm:"column:RegisterTime" json:"registerTime"`       // 注册时间
	LoginTime      *time.Time `gorm:"column:LoginTime" json:"loginTime"`             // 最后登录时间
	LastBetTime    *time.Time `gorm:"column:last_bet_time" json:"lastBetTime"`       // 最后下注时间
	RechargeAmount float64    `gorm:"column:recharge_amount" json:"recharge_amount"` // 充值金额
	RechargeCount  int        `gorm:"column:recharge_count" json:"recharge_count"`   // 充值次数

	AllBet         float64 `gorm:"column:all_bet" json:"all_bet"`                 // 总投注金额
	WithdrawAmount float64 `gorm:"column:withdraw_amount" json:"withdraw_amount"` // 提款金额
	WithdrawCount  int     `gorm:"column:withdraw_count" json:"withdraw_count"`   // 提款次数
	CtDiff         float64 `gorm:"column:ct_diff" json:"ct_diff"`                 // 充提差
	//PromotionsT    float64 `json:"PromotionsT"`     // 活动优惠T

	RewardAmount             float64 `gorm:"column:RewardAmount" json:"rewardAmount"`                             // 活动优惠
	VipRewardAmount          float64 `gorm:"column:VipRewardAmount" json:"vipRewardAmount"`                       // VIP 返水
	GetCommissionAmount      float64 `gorm:"column:GetCommissionAmount" json:"getCommissionAmount"`               // 佣金
	AgentId                  int     `gorm:"column:AgentId" json:"agentId"`                                       // 上级
	HashTransferTrxBetAmount float64 `gorm:"column:hash_transfer_trx_bet_amount" json:"hashTransferTrxBetAmount"` // TRX转账投注额

	HashTransferTrxBetAmount2U float64 `gorm:"column:hash_transfer_trx_bet_amount_u" json:"hashTransferTrxBetAmount2U"` // TRX转成U转账投注额
	HashTransferTrxWinAmount2U float64 `gorm:"column:hash_transfer_trx_win_loss_u" json:"hashTransferTrxWinAmount2U"`   // TRX转成U转账平台收益
	HashTransferUsdtBetAmount  float64 `gorm:"column:hash_transfer_usdt_bet_amount" json:"hashTransferUsdtBetAmount"`   // USDT转账投注额
	HashTransferUsdtWinAmount  float64 `gorm:"column:hash_transfer_usdt_win_loss" json:"hashTransferUsdtWinAmount"`     // USDT转账平台收益

	BalanceBetAmount float64 `gorm:"column:hash_balance_bet_amount" json:"hash_balance_bet_amount"` // 余额投注金额

	BalanceWinAmount float64 `gorm:"column:hash_balance_win_lose" json:"hash_balance_win_amount"`     // 余额平台收益
	LiveBetAmount    float64 `gorm:"column:third_live_bet_amount" json:"third_live_bet_amount"`       // 真人投注额
	LiveWinAmount    float64 `gorm:"column:third_live_win_lose" json:"third_live_win_amount"`         // 真人平台收益
	LotteryBetAmount float64 `gorm:"column:third_lottery_bet_amount" json:"third_lottery_bet_amount"` // 彩票投注额
	LotteryWinAmount float64 `gorm:"column:third_lottery_win_lose" json:"third_lottery_win_amount"`   // 彩票平台收益

	ElecBetAmount  float64 `gorm:"column:third_elec_bet_amount" json:"third_elec_bet_amount"`   // 电子投注额
	ElecWinAmount  float64 `gorm:"column:third_elec_win_lose" json:"third_elec_win_amount"`     // 电子平台收益
	SportBetAmount float64 `gorm:"column:third_sport_bet_amount" json:"third_sport_bet_amount"` // 体育投注额
	SportWinAmount float64 `gorm:"column:third_sport_win_lose" json:"third_sport_win_amount"`   // 体育平台收益
	SmallBetAmount float64 `gorm:"column:third_small_bet_amount" json:"third_small_bet_amount"` // 加密游戏投注额

	SmallWinAmount float64 `gorm:"column:third_small_win_lose" json:"third_small_win_amount"`   // 加密游戏平台收益
	ChessBetAmount float64 `gorm:"column:third_chess_bet_amount" json:"third_chess_bet_amount"` // 棋牌投注额
	ChessWinAmount float64 `gorm:"column:third_chess_win_lose" json:"third_chess_win_amount"`   // 棋牌平台收益
}

// list 用户列表查询接口
func (this *UserClassController) list(ctx *abugo.AbuHttpContent) {
	//defer recover()

	var total int64
	errcode := 0
	reqdata := ClassReq{}
	var list []*ClassResp
	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqdata.ChannelId = token.ChannelId
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	if 1 == reqdata.Export {
		// 处理时间和导出参数
		if reqdata.EndTime > 0 {
			reqdata.EndTime += ********
		}
		reqdata.Page = 1
		reqdata.PageSize = 1000

	}

	tdb := server.Db().GormDao().Table("x_user")
	tdb.Begin()
	defer tdb.Commit()
	//tdb.Model(&daoModel.XUser{})
	tdb.Select("x_user.SellerId",
		"x_seller.SellerName",
		"x_user.ChannelId",
		"x_channel.ChannelName",
		"x_user.UserId",
		"x_user.tgUserName",
		`"" as user_label_name`,
		"x_ads_user_profile.user_bet_types",
		"x_user.Email",
		"x_user.PhoneNum",
		"x_user.TopAgentId",
		"x_vip_info.VipLevel",
		"x_user.AccountType",
		"x_user.RegisterTime",
		"x_user.LoginTime",
		"x_ads_user_profile.last_bet_time",
		"x_ads_user_profile.recharge_amount",
		"x_ads_user_profile.recharge_count",
		"x_ads_user_profile.all_bet",
		"x_ads_user_profile.withdraw_amount",
		"x_ads_user_profile.withdraw_count",
		"x_ads_user_profile.recharge_amount - x_ads_user_profile.withdraw_amount AS ct_diff",
		"x_user_reward_commission.RewardAmount + x_user_reward_commission.TyUsdtRewardAmount + x_user_reward_commission.TyTrxRewardAmount * x_user_reward_commission.TyTrxRate AS RewardAmount ",
		"x_user_reward_commission.VipRewardAmount",
		"x_user_reward_commission.GetCommissionAmount",
		"x_user.AgentId",
		"x_ads_user_profile.hash_transfer_trx_bet_amount",
		"x_ads_user_profile.hash_transfer_trx_bet_amount_u",
		"x_ads_user_profile.hash_transfer_trx_bet_amount_u - x_ads_user_profile.hash_transfer_trx_win_amount_u AS hash_transfer_trx_win_loss_u",
		"x_ads_user_profile.hash_transfer_usdt_bet_amount",
		"x_ads_user_profile.hash_transfer_usdt_win_loss",
		"x_ads_user_profile.hash_balance_bet_amount",
		"x_ads_user_profile.hash_balance_bet_amount - x_ads_user_profile.hash_balance_win_amount AS hash_balance_win_lose",
		"x_ads_user_profile.third_live_bet_amount",
		"x_ads_user_profile.third_live_bet_amount - x_ads_user_profile.third_live_win_amount  AS third_live_win_lose",
		"x_ads_user_profile.third_lottery_bet_amount",
		"x_ads_user_profile.third_lottery_bet_amount - x_ads_user_profile.third_lottery_win_amount AS third_lottery_win_lose",
		"x_ads_user_profile.third_elec_bet_amount",
		"x_ads_user_profile.third_elec_bet_amount - x_ads_user_profile.third_elec_win_amount AS third_elec_win_lose",
		"x_ads_user_profile.third_sport_bet_amount",
		"x_ads_user_profile.third_sport_bet_amount - x_ads_user_profile.third_sport_win_amount  AS third_sport_win_lose",
		"x_ads_user_profile.third_small_bet_amount",
		"x_ads_user_profile.third_small_bet_amount - x_ads_user_profile.third_small_win_amount AS third_small_win_lose",
		"x_ads_user_profile.third_chess_bet_amount",
		"x_ads_user_profile.third_chess_bet_amount - x_ads_user_profile.third_chess_win_amount AS third_chess_win_lose",
	).Joins("left join x_ads_user_profile on x_user.UserId = x_ads_user_profile.user_id").
		Joins("left join x_vip_info on x_user.UserId = x_vip_info.UserId").
		Joins("left join x_user_reward_commission on x_user.UserId = x_user_reward_commission.UserId").
		Joins("left join x_seller on x_user.SellerID = x_seller.SellerID").
		Joins("left join x_channel on x_user.ChannelID = x_channel.ChannelID")

	iRet := this.GetWhere(&reqdata, tdb)
	// 在能确认是空数据的情况
	if iRet > 0 {
		ctx.Put("data", make([]*ClassResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}

	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	//if !server.Auth2(token, "玩家管理", "玩家列表", "查看手机号") {
	//	for i := 0; i < len(list); i++ {
	//		list[i].PhoneNum = "***"
	//	}
	//}

	for _, sub := range list {
		sub.UserLabel = GetUserLabelDataByOne(sub.UserId)
	}

	if 1 == reqdata.Export {
		headerLine := []string{
			"运营商", "渠道", "玩家ID", "用户标签", "投注类型", "邮箱", "手机号",
			"顶级代理", "VIP等级", "机器人玩家", "TG用户名", "注册时间", "最后登录", "最后下注", "充值金额",
			"充值次数", "总投注额", "提款金额", "提款次数", "充提差",
			"活动优惠", "VIP返水", "佣金", "上级",
			"余额投注额", "余额平台收益", "真人投注额", "真人平台收益", "彩票投注额",
			"彩票平台收益", "电子投注额", "电子平台收益", "体育投注额", "体育平台收益",
			"加密游戏投注额", "加密游戏平台收益", "棋牌投注额", "棋牌平台收益",
		}

		filterMap := map[string]string{
			"运营商": "SellerName", "渠道": "ChannelName", "玩家ID": "UserId", "用户标签": "UserLabel",
			"投注类型": "BetTypes", "邮箱": "Email", "手机号": "PhoneNum", "顶级代理": "TopAgentId", "VIP等级": "VipLv",
			"机器人玩家": "AccountType", "TG用户名": "TGUserName", "注册时间": "RegisterTime", "最后登录": "LoginTime",
			"最后下注": "LastBetTime", "充值金额": "RechargeAmount", "充值次数": "RechargeCount", "总投注额": "AllBet",
			"提款金额": "WithdrawAmount", "提款次数": "WithdrawCount", "充提差": "CtDiff",
			"活动优惠": "RewardAmount", "VIP返水": "VipRewardAmount", "佣金": "GetCommissionAmount", "上级": "AgentId",

			"余额投注额": "BalanceBetAmount", "余额平台收益": "BalanceWinAmount", "真人投注额": "LiveBetAmount",
			"真人平台收益": "LiveWinAmount", "彩票投注额": "LotteryBetAmount", "彩票平台收益": "LotteryWinAmount",
			"电子投注额": "ElecBetAmount", "电子平台收益": "ElecWinAmount", "体育投注额": "SportBetAmount",
			"体育平台收益": "SportWinAmount", "加密游戏投注额": "SmallBetAmount", "加密游戏平台收益": "SmallWinAmount",
			"棋牌投注额": "ChessBetAmount", "棋牌平台收益": "ChessWinAmount",
		}
		var result []map[string]interface{}

		for _, d := range list {
			betTypeName := map[int]string{
				0: "未投注", 1: "综合", 2: "哈希", 3: "电子", 4: "彩票", 5: "棋牌", 6: "体育", 7: "真人",
			}[d.BetTypes]

			row := map[string]interface{}{
				"SellerName":          d.SellerName,
				"ChannelName":         d.ChannelName,
				"UserId":              d.UserId,
				"UserLabel":           d.UserLabel,
				"BetTypes":            betTypeName,
				"Email":               d.Email,
				"PhoneNum":            d.PhoneNum,
				"TopAgentId":          d.TopAgentId,
				"VipLv":               d.VipLv,
				"AccountType":         d.AccountType,
				"TGUserName":          d.TGUserName,
				"RegisterTime":        d.RegisterTime,
				"LoginTime":           d.LoginTime,
				"LastBetTime":         d.LastBetTime,
				"RechargeAmount":      d.RechargeAmount,
				"RechargeCount":       d.RechargeCount,
				"AllBet":              d.AllBet,
				"WithdrawAmount":      d.WithdrawAmount,
				"WithdrawCount":       d.WithdrawCount,
				"CtDiff":              d.CtDiff,
				"RewardAmount":        d.RewardAmount,
				"VipRewardAmount":     d.VipRewardAmount,
				"GetCommissionAmount": d.GetCommissionAmount,
				"AgentId":             d.AgentId,
				"BalanceBetAmount":    d.BalanceBetAmount,
				"BalanceWinAmount":    d.BalanceWinAmount,
				"LiveBetAmount":       d.LiveBetAmount,
				"LiveWinAmount":       d.LiveWinAmount,
				"LotteryBetAmount":    d.LotteryBetAmount,
				"LotteryWinAmount":    d.LotteryWinAmount,
				"ElecBetAmount":       d.ElecBetAmount,
				"ElecWinAmount":       d.ElecWinAmount,
				"SportBetAmount":      d.SportBetAmount,
				"SportWinAmount":      d.SportWinAmount,
				"SmallBetAmount":      d.SmallBetAmount,
				"SmallWinAmount":      d.SmallWinAmount,
				"ChessBetAmount":      d.ChessBetAmount,
				"ChessWinAmount":      d.ChessWinAmount,
			}
			result = append(result, row)
		}

		filename, err := this.exportCSV(&result, headerLine, filterMap)
		if err != nil {
			ctx.RespErrString(true, &errcode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
	}

	return
}

// 获取转化条件
func (this *UserClassController) GetTransCond(id int, db *gorm.DB) {
	strSql := ""
	switch id {
	case TransCond1:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count > 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE DATE(x_user.RegisterTime) = x_user_recharge_withard_date.RecordDate " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond2:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count > 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 1 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond3:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count > 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 2 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond4:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count > 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 6 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond5:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count > 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 14 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond6:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count = 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE DATE(x_user.RegisterTime) = x_user_recharge_withard_date.RecordDate " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond7:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count = 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 1 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond8:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count = 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 2 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond9:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count = 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 6 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond10:
		strSql = "(x_ads_user_profile.gift_usdt_stat = 2 OR x_ads_user_profile.gift_trx_stat = 2) AND x_ads_user_profile.bet_count = 0 AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 14 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond11:
		strSql = "x_ads_user_profile.gift_usdt_stat != 2 AND x_ads_user_profile.gift_trx_stat != 2  AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE DATE(x_user.RegisterTime) = x_user_recharge_withard_date.RecordDate " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond12:
		strSql = "x_ads_user_profile.gift_usdt_stat != 2 AND x_ads_user_profile.gift_trx_stat != 2  AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE  " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 1 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond13:
		strSql = "x_ads_user_profile.gift_usdt_stat != 2 AND x_ads_user_profile.gift_trx_stat != 2  AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE  " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 2 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond14:
		strSql = "x_ads_user_profile.gift_usdt_stat != 2 AND x_ads_user_profile.gift_trx_stat != 2  AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE  " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 6 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	case TransCond15:
		strSql = "x_ads_user_profile.gift_usdt_stat != 2 AND x_ads_user_profile.gift_trx_stat != 2  AND " +
			"NOT EXISTS ( SELECT 1 FROM x_user_recharge_withard_date WHERE  " +
			"DATE(x_user_recharge_withard_date.RecordDate) BETWEEN DATE(x_user.RegisterTime) AND DATE_ADD(x_user.RegisterTime, INTERVAL 14 DAY ) " +
			"AND x_user_recharge_withard_date.UserId = x_user.UserId AND x_user_recharge_withard_date.RechargeCount > 0 )"
	default:
	}
	if len(strSql) > 0 {
		db.Where(strSql)
	}
}

// 获取留存条件
func (this *UserClassController) GetRetentionCond(id int, db *gorm.DB) {
	strSql := ""
	switch id {
	case RetentionCond1:
		strSql = "NOT EXISTS ( SELECT 1 FROM x_user_online WHERE x_user_online.UserId = x_user.UserId AND x_user_online.Day = DATE(x_user.RegisterTime) + INTERVAL 1 DAY )"
	case RetentionCond2:
		strSql = "NOT EXISTS ( SELECT 1 FROM x_user_online WHERE x_user_online.UserId = x_user.UserId AND x_user_online.Day BETWEEN DATE(x_user.RegisterTime) AND DATE(x_user.RegisterTime) + INTERVAL 2 DAY )"
	case RetentionCond3:
		strSql = "NOT EXISTS ( SELECT 1 FROM x_user_online WHERE x_user_online.UserId = x_user.UserId AND x_user_online.Day BETWEEN DATE(x_user.RegisterTime) AND DATE(x_user.RegisterTime) + INTERVAL 6 DAY )"
	case RetentionCond4:
		strSql = "NOT EXISTS ( SELECT 1 FROM x_user_online WHERE x_user_online.UserId = x_user.UserId AND x_user_online.Day BETWEEN DATE(x_user.RegisterTime) AND DATE(x_user.RegisterTime) + INTERVAL 14 DAY )"
	default:
	}

	if len(strSql) > 0 {
		db.Where(strSql)
	}
}

// 获取充值属性
func (this *UserClassController) RechargeAttr(id int, db *gorm.DB) {
	strSql := ""
	switch id {
	case RechargeAttr1:
		strSql = "x_ads_user_profile.user_id = x_user.UserId AND x_ads_user_profile.recharge_count >= 2 AND DATE(x_ads_user_profile.last_recharge_time) < DATE_SUB(CURDATE(), INTERVAL 7 DAY)"
	case RechargeAttr2:
		strSql = "x_ads_user_profile.user_id = x_user.UserId AND x_user.UserId IN (select x_recharge.UserId from x_recharge where PayTime >= DATE_FORMAT(TIMESTAMPADD(DAY,-7,NOW()),'%Y-%m-%d') " +
			"AND State = 5 AND RealAmount < 50 GROUP BY UserID Having COUNT(1) >= 3 ) "
	case RechargeAttr3:
		strSql = "x_ads_user_profile.user_id = x_user.UserId AND x_user.UserId IN (select x_recharge.UserId from x_recharge where PayTime >= DATE_FORMAT(TIMESTAMPADD(DAY,-7,NOW()),'%Y-%m-%d') " +
			"AND State = 5  GROUP BY UserID Having COUNT(1) >= 3 AND AVG(RealAmount) >= 100 ) "
	case RechargeAttr4:
		strSql = "x_ads_user_profile.user_id = x_user.UserId AND x_ads_user_profile.max_streak_bet >= 3 AND " +
			"x_ads_user_profile.all_win < (x_ads_user_profile.all_bet * 0.10) "
	default:

	}
	if len(strSql) > 0 {
		db.Where(strSql)
	}
}

// 获取where 条件
func (this *UserClassController) GetWhere(req *ClassReq, db *gorm.DB) int {
	iRet := 0
	// 商户id
	if req.SellerId > 0 {
		db.Where("x_user.SellerId = ?", req.SellerId)
	}
	// 渠道id
	if req.ChannelId > 0 {
		db.Where("x_user.ChannelId = ?", req.ChannelId)
	}
	// 用户id
	if req.UserId > 0 {
		db.Where("x_user.UserId = ?", req.UserId)
	}

	// 顶级代理id
	if req.TopAgentId > 0 {
		db.Where("x_user.TopAgentId = ?", req.TopAgentId)
	}
	// vip等级
	if req.VipLv > 0 {
		db.Where("x_vip_info.VipLevel = ?", req.VipLv)
	}
	// 注册时间区间
	if req.StartTime > 0 {
		db.Where("x_user.RegisterTime >= ?", abugo.TimeStampToLocalTime(req.StartTime))
	}
	// 注册时间区间
	if req.EndTime > 0 && req.EndTime > req.StartTime {
		db.Where("x_user.RegisterTime <= ?", abugo.TimeStampToLocalTime(req.EndTime))
	}
	// 充值区间
	switch req.HasRecharge {
	case 1:
		db.Where("x_ads_user_profile.recharge_amount >= ?", req.RechargeStart)
	case 2:
		db.Where("x_ads_user_profile.recharge_amount <= ?", req.RechargeEnd)
	case 3:
		if req.RechargeEnd == req.RechargeStart {
			db.Where("x_ads_user_profile.recharge_amount = ?", req.RechargeStart)
		} else {
			db.Where("x_ads_user_profile.recharge_amount >= ?", req.RechargeStart)
			db.Where("x_ads_user_profile.recharge_amount <= ?", req.RechargeEnd)
		}
	default:
	}

	// 投注区间
	switch req.HasBet {
	case 1:
		db.Where("x_ads_user_profile.all_bet >= ?", req.BetStart)
	case 2:
		db.Where("x_ads_user_profile.all_bet <= ?", req.BetEnd)
	case 3:
		if req.BetEnd == req.BetStart {
			db.Where("x_ads_user_profile.all_bet = ?", req.BetStart)
		} else {
			db.Where("x_ads_user_profile.all_bet >= ?", req.BetStart)
			db.Where("x_ads_user_profile.all_bet <= ?", req.BetEnd)
		}
	}
	// 盈亏区间
	switch req.HasProfit {
	case 1:
		db.Where("x_ads_user_profile.all_win_loss >= ?", req.ProfitStart)
	case 2:
		db.Where("x_ads_user_profile.all_win_loss <= ?", req.ProfitEnd)
	case 3:
		if req.ProfitEnd == req.ProfitStart {
			db.Where("x_ads_user_profile.all_win_loss = ?", req.ProfitStart)
		} else {
			db.Where("x_ads_user_profile.all_win_loss >= ?", req.ProfitStart)
			db.Where("x_ads_user_profile.all_win_loss <= ?", req.ProfitEnd)
		}
	}
	// 用户标签
	if req.UserLabelName != "" {

		uids := GetUserLabelDataByTypes(db, req.UserLabelName)
		if len(uids) <= 0 {
			iRet = 1
			return iRet
		}
		//db.Where("x_ads_user_profile.user_id in (?)", uids)
		// 改用 JOIN tmp_ids 替代 IN
		db = db.Joins("JOIN x_hash_game.tmp_ids ON tmp_ids.id = x_ads_user_profile.user_id")
	}
	// 投注类型
	if req.BetType > 0 {
		db.Where("x_ads_user_profile.user_bet_types = ?", req.BetType)
	}

	// 是否为机器人
	if req.IsBot > 0 {
		switch req.IsBot {
		case 1:
			db.Where("x_user.AccountType = 5")
		case 2:
			db.Where("x_user.AccountType != 5")
		default:
		}
	}

	// 转化条件
	if req.TransCond > 0 {
		this.GetTransCond(req.TransCond, db)
	}
	// 留存条件
	if req.RetentionCond > 0 {
		this.GetRetentionCond(req.RetentionCond, db)
	}
	// 充值属性
	if req.RechargeType > 0 {
		this.RechargeAttr(req.RechargeType, db)
	}
	return iRet
}

// 导出Excel文件
func (this *UserClassController) ExportExcel(list []*ClassResp) (string, error) {
	excel := excelize.NewFile()
	sheetName := "Sheet1"
	excel.SetSheetName("Sheet1", sheetName)

	// 设置表头
	header := []string{"运营商", "渠道", "玩家ID", "用户标签", "投注类型", "邮箱", "手机号",
		"顶级代理", "VIP等级", "机器人玩家", "TG用户名", "注册时间", "最后登录", "最后下注", "充值金额",
		"充值次数", "总投注额", "提款金额", "提款次数", "充提差",
		"活动优惠T", "活动优惠U", "VIP返水", "佣金", "上级",
		"余额投注额", "余额平台收益", "真人投注额", "真人平台收益", "彩票投注额",
		"彩票平台收益", "电子投注额", "电子平台收益", "体育投注额", "体育平台收益",
		"加密游戏投注额", "加密游戏平台收益", "棋牌投注额", "棋牌平台收益"}
	if err := excel.SetSheetRow(sheetName, "A1", &header); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", nil
	}

	// 并发写入数据
	var wg sync.WaitGroup
	batchSize := 500 // 每批处理的行数
	totalRecords := len(list)

	for i := 0; i < totalRecords; i += batchSize {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			end := start + batchSize
			if end > totalRecords {
				end = totalRecords
			}

			// 处理当前批次数据
			for j := start; j < end; j++ {
				d := list[j]
				rowIndex := j + 2 // Excel 从 A2 开始
				betTypeName := ""
				// 用户类型 0:未投注,1:综合,2:哈希,3.电子,4:彩票,5:棋牌,6:体育,7:真人
				switch d.BetTypes {
				case 0:
					betTypeName = "未投注"
				case 1:
					betTypeName = "综合"
				case 2:
					betTypeName = "哈希"
				case 3:
					betTypeName = "电子"
				case 4:
					betTypeName = "彩票"
				case 5:
					betTypeName = "棋牌"
				case 6:
					betTypeName = "体育"
				case 7:
					betTypeName = "真人"
				default:
					betTypeName = "未知"
				}
				row := []interface{}{
					d.SellerName, d.ChannelName, d.UserId, d.UserLabel,
					betTypeName, d.Email, d.PhoneNum, d.TopAgentId, d.VipLv,
					d.AccountType, d.TGUserName, d.RegisterTime, d.LoginTime, d.LastBetTime,
					d.RechargeAmount, d.RechargeCount, d.AllBet, d.WithdrawAmount,
					d.WithdrawCount, d.CtDiff, d.RewardAmount,
					d.VipRewardAmount, d.GetCommissionAmount, d.AgentId,
					d.BalanceBetAmount, d.BalanceWinAmount, d.LiveBetAmount, d.LiveWinAmount,
					d.LotteryBetAmount, d.LotteryWinAmount,
					d.ElecBetAmount, d.ElecWinAmount,
					d.SportBetAmount, d.SportWinAmount,
					d.SmallBetAmount, d.SmallWinAmount,
					d.ChessBetAmount, d.ChessWinAmount,
				}

				// 写入 Excel
				cell := fmt.Sprintf("A%d", rowIndex)
				if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
					log.Println("❌ 写入 Excel 失败:", err)
				}
			}
		}(i)
	}

	wg.Wait() // 等待所有 goroutine 完成

	filename := "export_user_" + time.Now().Format("**************") + ".xlsx"
	// 保存 Excel
	if err := excel.SaveAs(server.ExportDir() + "/" + filename); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功！")
	return filename, nil
}

func (this *UserClassController) exportCSV(list *[]map[string]interface{},
	headerLine []string, filterMap map[string]string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("❌ 导出 CSV panic: %v\n%s", r, debug.Stack())
		}
	}()
	exportDir := server.ExportDir()
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return "", fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 跨平台路径拼接
	filename := fmt.Sprintf("export_data_%s.csv", time.Now().Format("**************"))
	filePath := filepath.Join(exportDir, filename)

	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
		}
	}(file)
	// 写入表头
	var headerLineBuilder strings.Builder
	for i, h := range headerLine {
		if i > 0 {
			headerLineBuilder.WriteString(",")
		}
		headerLineBuilder.WriteString(`"` + strings.ReplaceAll(h, `"`, `""`) + `"`)
	}
	headerLineBuilder.WriteString("\n")
	if _, err := file.WriteString(headerLineBuilder.String()); err != nil {
		return "", fmt.Errorf("写入表头失败: %v", err)
	}

	// 逐行写入数据
	for i, data := range *list {
		var rowBuilder strings.Builder
		for j, h := range headerLine {
			if j > 0 {
				rowBuilder.WriteString(",")
			}
			if filterMap != nil {
				dbField := filterMap[h]
				val := fmt.Sprintf("%v", data[dbField])

				// 处理值中的特殊字符（引号、逗号）
				if strings.ContainsAny(val, `,"\n`) {
					val = `"` + strings.ReplaceAll(val, `"`, `""`) + `"`
				}
				rowBuilder.WriteString(val)
			}
		}
		rowBuilder.WriteString("\n")

		if _, err := file.WriteString(rowBuilder.String()); err != nil {
			return "", fmt.Errorf("写入数据失败: %v", err)
		}

		// 每100行强制刷盘
		if i%100 == 0 {
			if err := file.Sync(); err != nil {
				return "", fmt.Errorf("文件同步失败: %v", err)
			}
		}
	}

	// 最终强制刷盘一次
	if err := file.Sync(); err != nil {
		return "", fmt.Errorf("最终文件同步失败: %v", err)
	}

	return filename, nil
}

// 通过userid 返回 类型标签
func GetUserLabelDataByOne(userID int64) (list []string) {
	key := "hx_game:user:user_tags:" + cast.ToString(userID)
	ctx := context.Background()
	list, err := server.Rdb.SMembers(ctx, key).Result()
	if err != nil {
		return
	}
	return
}

// 按照用户分类
func GetUserLabelDataByTypes(db *gorm.DB, label string) []string {
	retArr := make([]string, 0)
	labelArr := strings.Split(label, ",")
	labelMap := make(map[string]bool)
	for _, v := range labelArr {
		labelMap[v] = true
	}
	if len(labelMap) <= 0 {
		return retArr
	}
	tagToUsers := make(map[string][]string)
	pattern := "hx_game:user:user_tags:*" // 匹配所有用户标签键
	ctx := context.Background()
	var cursor uint64
	for {
		// 每次迭代获取一批键
		keys, nextCursor, err := server.Rdb.Scan(ctx, cursor, pattern, 5000).Result()
		if err != nil {
			return nil
		}

		pipe := server.Rdb.Pipeline()
		smembersCmds := make(map[string]*redis.StringSliceCmd)
		for _, key := range keys {
			smembersCmds[key] = pipe.SMembers(ctx, key)
		}
		_, _ = pipe.Exec(ctx)

		for key, cmd := range smembersCmds {
			tags, err := cmd.Result()
			if err != nil {
				return nil
			}
			userID := strings.Split(key, ":")[3]
			for _, tag := range tags {
				//if tag != label {
				if _, ok := labelMap[tag]; !ok {
					continue
				}
				tagToUsers[tag] = append(tagToUsers[tag], userID)
			}
		}

		// 更新游标
		cursor = nextCursor
		// 迭代结束条件
		if cursor == 0 {
			break
		}
	}

	resultIds := make([]string, 0)
	//ids, _ := tagToUsers[label]
	resultMap := make(map[string]bool)
	for tmLabel, _ := range labelMap {
		ids, _ := tagToUsers[tmLabel]
		for _, id := range ids {
			if _, ok := resultMap[id]; !ok {
				resultMap[id] = true
				resultIds = append(resultIds, id)
			}
		}
	}

	//userIDs := strings.Join(ids, ",")
	//if userIDs == "" {
	//	return nil
	//}
	//return []string{userIDs}
	//  写入临时表

	err := QueryWithTempTable(db, resultIds)
	if err != nil {
		fmt.Println(err)
	}
	//var ids []int64
	//err = db.Raw("SELECT id FROM x_hash_game.tmp_ids").Scan(&ids).Error
	//if err != nil {
	//	fmt.Println("查询临时表出错:", err)
	//} else {
	//	fmt.Println("临时表数据:", ids)
	//}
	return resultIds
}

func QueryWithTempTable(db *gorm.DB, ids []string, args ...any) error {

	stat := db.Exec(`CREATE TEMPORARY TABLE x_hash_game.tmp_ids (id BIGINT PRIMARY KEY)`)
	if stat.Error != nil {
		fmt.Println(stat)
		return fmt.Errorf("create temp table: %#v", stat.Error)
	}

	const batchSize = 10000
	for i := 0; i < len(ids); i += batchSize {
		end := i + batchSize
		if end > len(ids) {
			end = len(ids)
		}

		placeholders := make([]string, 0, end-i)
		params := make([]any, 0, end-i)
		for _, id := range ids[i:end] {
			placeholders = append(placeholders, "(?)")
			params = append(params, id)
		}

		insertSQL := fmt.Sprintf("INSERT INTO x_hash_game.tmp_ids (id) VALUES %s", strings.Join(placeholders, ","))
		if stat := db.Exec(insertSQL, params...); stat.Error != nil {
			fmt.Printf("insert temp table: %v", stat.Error)
		}
	}
	return nil
}
