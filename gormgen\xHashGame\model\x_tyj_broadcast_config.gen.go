// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTyjBroadcastConfig = "x_tyj_broadcast_config"

// XTyjBroadcastConfig 体验金语言播报配置表
type XTyjBroadcastConfig struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:自增id" json:"Id"`             // 自增id
	SellerID   int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                // 运营商
	Symbol     string    `gorm:"column:Symbol;comment:体验金币种" json:"Symbol"`                                  // 体验金币种
	Title      string    `gorm:"column:Title;comment:标题" json:"Title"`                                       // 标题
	Content    string    `gorm:"column:Content;comment:内容" json:"Content"`                                   // 内容
	Status     int32     `gorm:"column:Status;comment:状态（1开启 2关闭）" json:"Status"`                            // 状态（1开启 2关闭）
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XTyjBroadcastConfig's table name
func (*XTyjBroadcastConfig) TableName() string {
	return TableNameXTyjBroadcastConfig
}
