package controller

import (
	"errors"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type TgRobotRedPacketController struct{}

func (c *TgRobotRedPacketController) Init() {
	group := server.Http().NewGroup("/api/tgRobotRedPacket")
	{
		group.Post("/list", c.list)
		group.Post("/info", c.info)
		group.Post("/create", c.create)
		group.Post("/update", c.update)
		group.Post("/update_status", c.update_status)
	}
}

func (c *TgRobotRedPacketController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID int32
		Page     int
		PageSize int
		IsEnable int32 // 1:启用 2:禁用
	}
	type Result struct {
		model.XTgRobotRedpacket
		ChannelName string
		SellerName  string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人", "查", "红包机器人查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgRobotRedpacket
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	if reqdata.IsEnable != 0 {
		db = db.Where(dao.IsEnable.Eq(reqdata.IsEnable))
	}
	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}

	var list []Result
	xChannel := server.DaoxHashGame().XChannel
	xSeller := server.DaoxHashGame().XSeller
	total, err := db.LeftJoin(xChannel, xChannel.ChannelID.EqCol(dao.ChannelID)).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Select(dao.ALL, xChannel.ChannelName, xSeller.SellerName).ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TgRobotRedPacketController) info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id       int64
		SellerID int32
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人", "查", "红包机器人查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgRobotRedpacket
	db := dao.WithContext(ctx.Gin())
	xChannel := server.DaoxHashGame().XChannel
	xSeller := server.DaoxHashGame().XSeller
	//xTgAccount := server.DaoxHashGame().XTgAccount
	//admUser := server.DaoxHashGame().AdminUser
	var data struct {
		model.XTgRobotRedpacket
		ChannelName string
		SellerName  string
	}
	err := db.LeftJoin(xChannel, xChannel.ChannelID.EqCol(dao.ChannelID)).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Where(dao.ID.Eq(reqdata.Id)).Select(dao.ALL, xChannel.ChannelName, xSeller.SellerName).
		Scan(&data)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *TgRobotRedPacketController) create(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotRedpacket
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人", "增", "红包机器人查询")
	if token == nil {
		return
	}

	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	if reqdata.ChannelID == 0 {
		ctx.RespErr(errors.New("请选择渠道"), &errcode)
		return
	}

	dao := server.DaoxHashGame().XTgRobotRedpacket
	db := dao.WithContext(ctx.Gin())
	err := db.Create(&reqdata.XTgRobotRedpacket)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotRedPacketController) update(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotRedpacket
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人", "改", "修改红包机器人")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	if reqdata.ChannelID == 0 {
		ctx.RespErr(errors.New("请选择渠道"), &errcode)
		return
	}

	dao := server.DaoxHashGame().XTgRobotRedpacket
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).
		Updates(&reqdata.XTgRobotRedpacket)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotRedPacketController) update_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotRedpacket
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "红包机器人", "改", "修改红包机器人")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTgRobotRedpacket
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.IsEnable, reqdata.IsEnable)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}
