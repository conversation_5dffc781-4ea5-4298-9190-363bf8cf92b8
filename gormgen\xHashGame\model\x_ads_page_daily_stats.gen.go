// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsPageDailyStat = "x_ads_page_daily_stats"

// XAdsPageDailyStat 页面访问每日统计表
type XAdsPageDailyStat struct {
	ID                      int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                         // 主键ID
	SellerID                int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                                        // 运营商ID
	ChannelID               int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                                       // 渠道ID
	TopAgentID              int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                                 // 顶级代理ID
	PageName                string    `gorm:"column:page_name;not null;comment:页面名称" json:"page_name"`                                                // 页面名称
	StatDate                time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                                                // 统计日期
	VisitCountPc            int32     `gorm:"column:visit_count_pc;not null;comment:访问次数pc" json:"visit_count_pc"`                                    // 访问次数pc
	VisitCountH5            int32     `gorm:"column:visit_count_h5;not null;comment:访问次数h5" json:"visit_count_h5"`                                    // 访问次数h5
	VisitorCountPc          int32     `gorm:"column:visitor_count_pc;not null;comment:访问人数pc" json:"visitor_count_pc"`                                // 访问人数pc
	VisitorCountH5          int32     `gorm:"column:visitor_count_h5;not null;comment:访问人数h5" json:"visitor_count_h5"`                                // 访问人数h5
	CountLoadPcWifi         int32     `gorm:"column:count_load_pc_wifi;not null;comment:加载次数 pc wifi" json:"count_load_pc_wifi"`                      // 加载次数 pc wifi
	CountLoadPcFlow         int32     `gorm:"column:count_load_pc_flow;not null;comment:加载次数 pc flow" json:"count_load_pc_flow"`                      // 加载次数 pc flow
	CountLoadH5Wifi         int32     `gorm:"column:count_load_h5_wifi;not null;comment:加载次数 h5 wifi" json:"count_load_h5_wifi"`                      // 加载次数 h5 wifi
	CountLoadH5Flow         int32     `gorm:"column:count_load_h5_flow;not null;comment:加载次数 h5 flow" json:"count_load_h5_flow"`                      // 加载次数 h5 flow
	TotalStayDurationPc     float32   `gorm:"column:total_stay_duration_pc;not null;comment:总停留时长(秒)pc" json:"total_stay_duration_pc"`                // 总停留时长(秒)pc
	TotalStayDurationH5     float32   `gorm:"column:total_stay_duration_h5;not null;comment:总停留时长(秒)h5" json:"total_stay_duration_h5"`                // 总停留时长(秒)h5
	TotalLoadDurationPcWifi float32   `gorm:"column:total_load_duration_pc_wifi;not null;comment:总加载时长(秒)pc wifi" json:"total_load_duration_pc_wifi"` // 总加载时长(秒)pc wifi
	TotalLoadDurationPcFlow float32   `gorm:"column:total_load_duration_pc_flow;not null;comment:总加载时长(秒)pc flow" json:"total_load_duration_pc_flow"` // 总加载时长(秒)pc flow
	TotalLoadDurationH5Wifi float32   `gorm:"column:total_load_duration_h5_wifi;not null;comment:总加载时长(秒)h5 wifi" json:"total_load_duration_h5_wifi"` // 总加载时长(秒)h5 wifi
	TotalLoadDurationH5Flow float32   `gorm:"column:total_load_duration_h5_flow;not null;comment:总加载时长(秒)h5 flow" json:"total_load_duration_h5_flow"` // 总加载时长(秒)h5 flow
	AvgLoadDurationPcWifi   float32   `gorm:"column:avg_load_duration_pc_wifi;not null;comment:平均加载时长(秒)pc wifi" json:"avg_load_duration_pc_wifi"`    // 平均加载时长(秒)pc wifi
	AvgLoadDurationPcFlow   float32   `gorm:"column:avg_load_duration_pc_flow;not null;comment:平均加载时长(秒)pc flow" json:"avg_load_duration_pc_flow"`    // 平均加载时长(秒)pc flow
	AvgLoadDurationH5Wifi   float32   `gorm:"column:avg_load_duration_h5_wifi;not null;comment:平均加载时长(秒)h5 wifi" json:"avg_load_duration_h5_wifi"`    // 平均加载时长(秒)h5 wifi
	AvgLoadDurationH5Flow   float32   `gorm:"column:avg_load_duration_h5_flow;not null;comment:平均加载时长(秒)h5 flow" json:"avg_load_duration_h5_flow"`    // 平均加载时长(秒)h5 flow
	CreateTime              time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                  // 创建时间
	UpdateTime              time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                  // 更新时间
}

// TableName XAdsPageDailyStat's table name
func (*XAdsPageDailyStat) TableName() string {
	return TableNameXAdsPageDailyStat
}
