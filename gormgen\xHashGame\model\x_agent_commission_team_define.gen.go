// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentCommissionTeamDefine = "x_agent_commission_team_define"

// XAgentCommissionTeamDefine 三级返佣团队定义
type XAgentCommissionTeamDefine struct {
	SchemeID          int32     `gorm:"column:SchemeId;primaryKey;comment:方案Id" json:"SchemeId"`                             // 方案Id
	TeamLevel         int32     `gorm:"column:TeamLevel;primaryKey;comment:团队等级" json:"TeamLevel"`                           // 团队等级
	TotalValidUsers   int32     `gorm:"column:TotalValidUsers;comment:总有效会员数" json:"TotalValidUsers"`                        // 总有效会员数
	TotalValidLiuShui float64   `gorm:"column:TotalValidLiuShui;default:0.000000;comment:总有效流水" json:"TotalValidLiuShui"`    // 总有效流水
	CreateTime        time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime        time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XAgentCommissionTeamDefine's table name
func (*XAgentCommissionTeamDefine) TableName() string {
	return TableNameXAgentCommissionTeamDefine
}
