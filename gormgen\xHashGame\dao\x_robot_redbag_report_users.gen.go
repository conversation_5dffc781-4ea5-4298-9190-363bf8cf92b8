// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotRedbagReportUser(db *gorm.DB, opts ...gen.DOOption) xRobotRedbagReportUser {
	_xRobotRedbagReportUser := xRobotRedbagReportUser{}

	_xRobotRedbagReportUser.xRobotRedbagReportUserDo.UseDB(db, opts...)
	_xRobotRedbagReportUser.xRobotRedbagReportUserDo.UseModel(&model.XRobotRedbagReportUser{})

	tableName := _xRobotRedbagReportUser.xRobotRedbagReportUserDo.TableName()
	_xRobotRedbagReportUser.ALL = field.NewAsterisk(tableName)
	_xRobotRedbagReportUser.ID = field.NewInt64(tableName, "id")
	_xRobotRedbagReportUser.DateTime = field.NewTime(tableName, "date_time")
	_xRobotRedbagReportUser.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotRedbagReportUser.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotRedbagReportUser.UserID = field.NewInt64(tableName, "user_id")
	_xRobotRedbagReportUser.Name = field.NewString(tableName, "name")
	_xRobotRedbagReportUser.Token = field.NewString(tableName, "token")
	_xRobotRedbagReportUser.UserChatID = field.NewInt64(tableName, "user_chat_id")
	_xRobotRedbagReportUser.UserName = field.NewString(tableName, "user_name")
	_xRobotRedbagReportUser.UserFullName = field.NewString(tableName, "user_full_name")
	_xRobotRedbagReportUser.LangCode = field.NewString(tableName, "lang_code")
	_xRobotRedbagReportUser.StartFirstTime = field.NewTime(tableName, "start_first_time")
	_xRobotRedbagReportUser.StartLastTime = field.NewTime(tableName, "start_last_time")
	_xRobotRedbagReportUser.StartCnt = field.NewInt32(tableName, "start_cnt")
	_xRobotRedbagReportUser.DrawsCnt = field.NewInt32(tableName, "draws_cnt")
	_xRobotRedbagReportUser.SignDate = field.NewTime(tableName, "sign_date")
	_xRobotRedbagReportUser.SignCnt = field.NewInt32(tableName, "sign_cnt")
	_xRobotRedbagReportUser.SignPoints = field.NewInt32(tableName, "sign_points")
	_xRobotRedbagReportUser.GrabCnt = field.NewInt32(tableName, "grab_cnt")
	_xRobotRedbagReportUser.GrabPoints = field.NewInt32(tableName, "grab_points")
	_xRobotRedbagReportUser.AnswerCnt = field.NewInt32(tableName, "answer_cnt")
	_xRobotRedbagReportUser.AnswerPoints = field.NewInt32(tableName, "answer_points")
	_xRobotRedbagReportUser.ExchangeCnt = field.NewInt32(tableName, "exchange_cnt")
	_xRobotRedbagReportUser.ExchangePoints = field.NewInt32(tableName, "exchange_points")
	_xRobotRedbagReportUser.TotalPoints = field.NewInt32(tableName, "total_points")
	_xRobotRedbagReportUser.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotRedbagReportUser.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotRedbagReportUser.fillFieldMap()

	return _xRobotRedbagReportUser
}

type xRobotRedbagReportUser struct {
	xRobotRedbagReportUserDo xRobotRedbagReportUserDo

	ALL            field.Asterisk
	ID             field.Int64  // pk
	DateTime       field.Time   // 日期
	SellerID       field.Int32  // 运营商ID
	ChannelID      field.Int32  // 渠道ID
	UserID         field.Int64  // 用户ID
	Name           field.String // 机器人name
	Token          field.String // 机器人token
	UserChatID     field.Int64  // 用户chatID
	UserName       field.String // 用户名
	UserFullName   field.String // 全名
	LangCode       field.String // 用户语言
	StartFirstTime field.Time   // 首次启动时间
	StartLastTime  field.Time   // 最后启动时间
	StartCnt       field.Int32  // 启动次数
	DrawsCnt       field.Int32  // 抽奖次数
	SignDate       field.Time   // 签到日期
	SignCnt        field.Int32  // 签到次数
	SignPoints     field.Int32  // 签到积分
	GrabCnt        field.Int32  // 红包次数
	GrabPoints     field.Int32  // 红包积分
	AnswerCnt      field.Int32  // 用户答题次数
	AnswerPoints   field.Int32  // 答题积分
	ExchangeCnt    field.Int32  // 兑换次数
	ExchangePoints field.Int32  // 兑换积分
	TotalPoints    field.Int32  // 总积分
	CreateTime     field.Time   // 创建日期
	UpdateTime     field.Time   // 更新日期

	fieldMap map[string]field.Expr
}

func (x xRobotRedbagReportUser) Table(newTableName string) *xRobotRedbagReportUser {
	x.xRobotRedbagReportUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotRedbagReportUser) As(alias string) *xRobotRedbagReportUser {
	x.xRobotRedbagReportUserDo.DO = *(x.xRobotRedbagReportUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotRedbagReportUser) updateTableName(table string) *xRobotRedbagReportUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.DateTime = field.NewTime(table, "date_time")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.UserID = field.NewInt64(table, "user_id")
	x.Name = field.NewString(table, "name")
	x.Token = field.NewString(table, "token")
	x.UserChatID = field.NewInt64(table, "user_chat_id")
	x.UserName = field.NewString(table, "user_name")
	x.UserFullName = field.NewString(table, "user_full_name")
	x.LangCode = field.NewString(table, "lang_code")
	x.StartFirstTime = field.NewTime(table, "start_first_time")
	x.StartLastTime = field.NewTime(table, "start_last_time")
	x.StartCnt = field.NewInt32(table, "start_cnt")
	x.DrawsCnt = field.NewInt32(table, "draws_cnt")
	x.SignDate = field.NewTime(table, "sign_date")
	x.SignCnt = field.NewInt32(table, "sign_cnt")
	x.SignPoints = field.NewInt32(table, "sign_points")
	x.GrabCnt = field.NewInt32(table, "grab_cnt")
	x.GrabPoints = field.NewInt32(table, "grab_points")
	x.AnswerCnt = field.NewInt32(table, "answer_cnt")
	x.AnswerPoints = field.NewInt32(table, "answer_points")
	x.ExchangeCnt = field.NewInt32(table, "exchange_cnt")
	x.ExchangePoints = field.NewInt32(table, "exchange_points")
	x.TotalPoints = field.NewInt32(table, "total_points")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotRedbagReportUser) WithContext(ctx context.Context) *xRobotRedbagReportUserDo {
	return x.xRobotRedbagReportUserDo.WithContext(ctx)
}

func (x xRobotRedbagReportUser) TableName() string { return x.xRobotRedbagReportUserDo.TableName() }

func (x xRobotRedbagReportUser) Alias() string { return x.xRobotRedbagReportUserDo.Alias() }

func (x xRobotRedbagReportUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotRedbagReportUserDo.Columns(cols...)
}

func (x *xRobotRedbagReportUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotRedbagReportUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 27)
	x.fieldMap["id"] = x.ID
	x.fieldMap["date_time"] = x.DateTime
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["user_id"] = x.UserID
	x.fieldMap["name"] = x.Name
	x.fieldMap["token"] = x.Token
	x.fieldMap["user_chat_id"] = x.UserChatID
	x.fieldMap["user_name"] = x.UserName
	x.fieldMap["user_full_name"] = x.UserFullName
	x.fieldMap["lang_code"] = x.LangCode
	x.fieldMap["start_first_time"] = x.StartFirstTime
	x.fieldMap["start_last_time"] = x.StartLastTime
	x.fieldMap["start_cnt"] = x.StartCnt
	x.fieldMap["draws_cnt"] = x.DrawsCnt
	x.fieldMap["sign_date"] = x.SignDate
	x.fieldMap["sign_cnt"] = x.SignCnt
	x.fieldMap["sign_points"] = x.SignPoints
	x.fieldMap["grab_cnt"] = x.GrabCnt
	x.fieldMap["grab_points"] = x.GrabPoints
	x.fieldMap["answer_cnt"] = x.AnswerCnt
	x.fieldMap["answer_points"] = x.AnswerPoints
	x.fieldMap["exchange_cnt"] = x.ExchangeCnt
	x.fieldMap["exchange_points"] = x.ExchangePoints
	x.fieldMap["total_points"] = x.TotalPoints
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotRedbagReportUser) clone(db *gorm.DB) xRobotRedbagReportUser {
	x.xRobotRedbagReportUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotRedbagReportUser) replaceDB(db *gorm.DB) xRobotRedbagReportUser {
	x.xRobotRedbagReportUserDo.ReplaceDB(db)
	return x
}

type xRobotRedbagReportUserDo struct{ gen.DO }

func (x xRobotRedbagReportUserDo) Debug() *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotRedbagReportUserDo) WithContext(ctx context.Context) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotRedbagReportUserDo) ReadDB() *xRobotRedbagReportUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotRedbagReportUserDo) WriteDB() *xRobotRedbagReportUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotRedbagReportUserDo) Session(config *gorm.Session) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotRedbagReportUserDo) Clauses(conds ...clause.Expression) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotRedbagReportUserDo) Returning(value interface{}, columns ...string) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotRedbagReportUserDo) Not(conds ...gen.Condition) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotRedbagReportUserDo) Or(conds ...gen.Condition) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotRedbagReportUserDo) Select(conds ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotRedbagReportUserDo) Where(conds ...gen.Condition) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotRedbagReportUserDo) Order(conds ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotRedbagReportUserDo) Distinct(cols ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotRedbagReportUserDo) Omit(cols ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotRedbagReportUserDo) Join(table schema.Tabler, on ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotRedbagReportUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotRedbagReportUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotRedbagReportUserDo) Group(cols ...field.Expr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotRedbagReportUserDo) Having(conds ...gen.Condition) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotRedbagReportUserDo) Limit(limit int) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotRedbagReportUserDo) Offset(offset int) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotRedbagReportUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotRedbagReportUserDo) Unscoped() *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotRedbagReportUserDo) Create(values ...*model.XRobotRedbagReportUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotRedbagReportUserDo) CreateInBatches(values []*model.XRobotRedbagReportUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotRedbagReportUserDo) Save(values ...*model.XRobotRedbagReportUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotRedbagReportUserDo) First() (*model.XRobotRedbagReportUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagReportUser), nil
	}
}

func (x xRobotRedbagReportUserDo) Take() (*model.XRobotRedbagReportUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagReportUser), nil
	}
}

func (x xRobotRedbagReportUserDo) Last() (*model.XRobotRedbagReportUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagReportUser), nil
	}
}

func (x xRobotRedbagReportUserDo) Find() ([]*model.XRobotRedbagReportUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotRedbagReportUser), err
}

func (x xRobotRedbagReportUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotRedbagReportUser, err error) {
	buf := make([]*model.XRobotRedbagReportUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotRedbagReportUserDo) FindInBatches(result *[]*model.XRobotRedbagReportUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotRedbagReportUserDo) Attrs(attrs ...field.AssignExpr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotRedbagReportUserDo) Assign(attrs ...field.AssignExpr) *xRobotRedbagReportUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotRedbagReportUserDo) Joins(fields ...field.RelationField) *xRobotRedbagReportUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotRedbagReportUserDo) Preload(fields ...field.RelationField) *xRobotRedbagReportUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotRedbagReportUserDo) FirstOrInit() (*model.XRobotRedbagReportUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagReportUser), nil
	}
}

func (x xRobotRedbagReportUserDo) FirstOrCreate() (*model.XRobotRedbagReportUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotRedbagReportUser), nil
	}
}

func (x xRobotRedbagReportUserDo) FindByPage(offset int, limit int) (result []*model.XRobotRedbagReportUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotRedbagReportUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotRedbagReportUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotRedbagReportUserDo) Delete(models ...*model.XRobotRedbagReportUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotRedbagReportUserDo) withDO(do gen.Dao) *xRobotRedbagReportUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
