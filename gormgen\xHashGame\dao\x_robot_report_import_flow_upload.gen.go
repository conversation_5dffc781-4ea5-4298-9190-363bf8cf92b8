// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotReportImportFlowUpload(db *gorm.DB, opts ...gen.DOOption) xRobotReportImportFlowUpload {
	_xRobotReportImportFlowUpload := xRobotReportImportFlowUpload{}

	_xRobotReportImportFlowUpload.xRobotReportImportFlowUploadDo.UseDB(db, opts...)
	_xRobotReportImportFlowUpload.xRobotReportImportFlowUploadDo.UseModel(&model.XRobotReportImportFlowUpload{})

	tableName := _xRobotReportImportFlowUpload.xRobotReportImportFlowUploadDo.TableName()
	_xRobotReportImportFlowUpload.ALL = field.NewAsterisk(tableName)
	_xRobotReportImportFlowUpload.ID = field.NewInt64(tableName, "id")
	_xRobotReportImportFlowUpload.DateTime = field.NewTime(tableName, "date_time")
	_xRobotReportImportFlowUpload.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotReportImportFlowUpload.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotReportImportFlowUpload.SendCnt = field.NewInt32(tableName, "send_cnt")
	_xRobotReportImportFlowUpload.SmallAccontCnt = field.NewInt32(tableName, "small_accont_cnt")
	_xRobotReportImportFlowUpload.AccountPrice = field.NewFloat64(tableName, "account_price")
	_xRobotReportImportFlowUpload.SendCostPrice = field.NewFloat64(tableName, "send_cost_price")
	_xRobotReportImportFlowUpload.SendAvgCnt = field.NewFloat64(tableName, "send_avg_cnt")
	_xRobotReportImportFlowUpload.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotReportImportFlowUpload.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotReportImportFlowUpload.fillFieldMap()

	return _xRobotReportImportFlowUpload
}

type xRobotReportImportFlowUpload struct {
	xRobotReportImportFlowUploadDo xRobotReportImportFlowUploadDo

	ALL            field.Asterisk
	ID             field.Int64   // pk
	DateTime       field.Time    // 日期
	SellerID       field.Int32   // 经销商ID
	ChannelID      field.Int32   // 渠道ID
	SendCnt        field.Int32   // 发送数量
	SmallAccontCnt field.Int32   // 使用小号数量
	AccountPrice   field.Float64 // 号单价
	SendCostPrice  field.Float64 // 发送总费用
	SendAvgCnt     field.Float64 // 平均发送数量
	CreateTime     field.Time    // 创建
	UpdateTime     field.Time    // 更新

	fieldMap map[string]field.Expr
}

func (x xRobotReportImportFlowUpload) Table(newTableName string) *xRobotReportImportFlowUpload {
	x.xRobotReportImportFlowUploadDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotReportImportFlowUpload) As(alias string) *xRobotReportImportFlowUpload {
	x.xRobotReportImportFlowUploadDo.DO = *(x.xRobotReportImportFlowUploadDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotReportImportFlowUpload) updateTableName(table string) *xRobotReportImportFlowUpload {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.DateTime = field.NewTime(table, "date_time")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.SendCnt = field.NewInt32(table, "send_cnt")
	x.SmallAccontCnt = field.NewInt32(table, "small_accont_cnt")
	x.AccountPrice = field.NewFloat64(table, "account_price")
	x.SendCostPrice = field.NewFloat64(table, "send_cost_price")
	x.SendAvgCnt = field.NewFloat64(table, "send_avg_cnt")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotReportImportFlowUpload) WithContext(ctx context.Context) *xRobotReportImportFlowUploadDo {
	return x.xRobotReportImportFlowUploadDo.WithContext(ctx)
}

func (x xRobotReportImportFlowUpload) TableName() string {
	return x.xRobotReportImportFlowUploadDo.TableName()
}

func (x xRobotReportImportFlowUpload) Alias() string { return x.xRobotReportImportFlowUploadDo.Alias() }

func (x xRobotReportImportFlowUpload) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotReportImportFlowUploadDo.Columns(cols...)
}

func (x *xRobotReportImportFlowUpload) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotReportImportFlowUpload) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["id"] = x.ID
	x.fieldMap["date_time"] = x.DateTime
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["send_cnt"] = x.SendCnt
	x.fieldMap["small_accont_cnt"] = x.SmallAccontCnt
	x.fieldMap["account_price"] = x.AccountPrice
	x.fieldMap["send_cost_price"] = x.SendCostPrice
	x.fieldMap["send_avg_cnt"] = x.SendAvgCnt
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotReportImportFlowUpload) clone(db *gorm.DB) xRobotReportImportFlowUpload {
	x.xRobotReportImportFlowUploadDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotReportImportFlowUpload) replaceDB(db *gorm.DB) xRobotReportImportFlowUpload {
	x.xRobotReportImportFlowUploadDo.ReplaceDB(db)
	return x
}

type xRobotReportImportFlowUploadDo struct{ gen.DO }

func (x xRobotReportImportFlowUploadDo) Debug() *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotReportImportFlowUploadDo) WithContext(ctx context.Context) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotReportImportFlowUploadDo) ReadDB() *xRobotReportImportFlowUploadDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotReportImportFlowUploadDo) WriteDB() *xRobotReportImportFlowUploadDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotReportImportFlowUploadDo) Session(config *gorm.Session) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotReportImportFlowUploadDo) Clauses(conds ...clause.Expression) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotReportImportFlowUploadDo) Returning(value interface{}, columns ...string) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotReportImportFlowUploadDo) Not(conds ...gen.Condition) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotReportImportFlowUploadDo) Or(conds ...gen.Condition) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotReportImportFlowUploadDo) Select(conds ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotReportImportFlowUploadDo) Where(conds ...gen.Condition) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotReportImportFlowUploadDo) Order(conds ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotReportImportFlowUploadDo) Distinct(cols ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotReportImportFlowUploadDo) Omit(cols ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotReportImportFlowUploadDo) Join(table schema.Tabler, on ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotReportImportFlowUploadDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotReportImportFlowUploadDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotReportImportFlowUploadDo) Group(cols ...field.Expr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotReportImportFlowUploadDo) Having(conds ...gen.Condition) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotReportImportFlowUploadDo) Limit(limit int) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotReportImportFlowUploadDo) Offset(offset int) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotReportImportFlowUploadDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotReportImportFlowUploadDo) Unscoped() *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotReportImportFlowUploadDo) Create(values ...*model.XRobotReportImportFlowUpload) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotReportImportFlowUploadDo) CreateInBatches(values []*model.XRobotReportImportFlowUpload, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotReportImportFlowUploadDo) Save(values ...*model.XRobotReportImportFlowUpload) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotReportImportFlowUploadDo) First() (*model.XRobotReportImportFlowUpload, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlowUpload), nil
	}
}

func (x xRobotReportImportFlowUploadDo) Take() (*model.XRobotReportImportFlowUpload, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlowUpload), nil
	}
}

func (x xRobotReportImportFlowUploadDo) Last() (*model.XRobotReportImportFlowUpload, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlowUpload), nil
	}
}

func (x xRobotReportImportFlowUploadDo) Find() ([]*model.XRobotReportImportFlowUpload, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotReportImportFlowUpload), err
}

func (x xRobotReportImportFlowUploadDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotReportImportFlowUpload, err error) {
	buf := make([]*model.XRobotReportImportFlowUpload, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotReportImportFlowUploadDo) FindInBatches(result *[]*model.XRobotReportImportFlowUpload, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotReportImportFlowUploadDo) Attrs(attrs ...field.AssignExpr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotReportImportFlowUploadDo) Assign(attrs ...field.AssignExpr) *xRobotReportImportFlowUploadDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotReportImportFlowUploadDo) Joins(fields ...field.RelationField) *xRobotReportImportFlowUploadDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotReportImportFlowUploadDo) Preload(fields ...field.RelationField) *xRobotReportImportFlowUploadDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotReportImportFlowUploadDo) FirstOrInit() (*model.XRobotReportImportFlowUpload, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlowUpload), nil
	}
}

func (x xRobotReportImportFlowUploadDo) FirstOrCreate() (*model.XRobotReportImportFlowUpload, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotReportImportFlowUpload), nil
	}
}

func (x xRobotReportImportFlowUploadDo) FindByPage(offset int, limit int) (result []*model.XRobotReportImportFlowUpload, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotReportImportFlowUploadDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotReportImportFlowUploadDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotReportImportFlowUploadDo) Delete(models ...*model.XRobotReportImportFlowUpload) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotReportImportFlowUploadDo) withDO(do gen.Dao) *xRobotReportImportFlowUploadDo {
	x.DO = *do.(*gen.DO)
	return x
}
