// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAdsFunctionInteractionButtonDailyStat = "x_ads_function_interaction_button_daily_stats"

// XAdsFunctionInteractionButtonDailyStat 功能交互按钮每日统计表
type XAdsFunctionInteractionButtonDailyStat struct {
	ID                int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                        // 主键ID
	SellerID          int32     `gorm:"column:seller_id;comment:运营商ID" json:"seller_id"`                                       // 运营商ID
	ChannelID         int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                      // 渠道ID
	TopAgentID        int64     `gorm:"column:top_agent_id;comment:顶级代理ID" json:"top_agent_id"`                                // 顶级代理ID
	StatDate          time.Time `gorm:"column:stat_date;not null;comment:统计日期" json:"stat_date"`                               // 统计日期
	ButtonName        string    `gorm:"column:button_name;not null;comment:按钮名称" json:"button_name"`                           // 按钮名称
	ClickCountPc      int32     `gorm:"column:click_count_pc;not null;comment:点击次数 pc" json:"click_count_pc"`                  // 点击次数 pc
	ClickCountH5      int32     `gorm:"column:click_count_h5;not null;comment:点击次数 h5" json:"click_count_h5"`                  // 点击次数 h5
	ResponseTimePc    float32   `gorm:"column:response_time_pc;not null;comment:响应时长(s) pc" json:"response_time_pc"`           // 响应时长(s) pc
	ResponseTimeH5    float32   `gorm:"column:response_time_h5;not null;comment:响应时长(s) h5" json:"response_time_h5"`           // 响应时长(s) h5
	AvgResponseTimePc float32   `gorm:"column:avg_response_time_pc;not null;comment:平均响应时长(s) pc" json:"avg_response_time_pc"` // 平均响应时长(s) pc
	AvgResponseTimeH5 float32   `gorm:"column:avg_response_time_h5;not null;comment:平均响应时长(s) h5" json:"avg_response_time_h5"` // 平均响应时长(s) h5
	CreateTime        time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime        time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XAdsFunctionInteractionButtonDailyStat's table name
func (*XAdsFunctionInteractionButtonDailyStat) TableName() string {
	return TableNameXAdsFunctionInteractionButtonDailyStat
}
