package stats

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
	"github.com/zhms/xgo/xgo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"math"
	"strings"
	"time"
	"xserver/abugo"
	daoModel "xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type FnProcTask func(start, end int64)
type StatsClassController struct {
	ProcStatsTaskMap map[int]FnProcTask
}

func (this *StatsClassController) Init() {
	group := server.Http().NewGroup("/api/event")
	{
		// 埋点事件数据提交
		group.PostNoAuth("/collect", this.Collect)
	}
	group2 := server.Http().NewGroup("/api/stats")
	{
		// 埋点事件查询
		group2.Post("/event_config/list", this.ListEventConfig)
		// 埋点事件新增
		group2.Post("/event_config/create", this.CreateEventConfig)
		// 埋点事件修改
		group2.Post("/event_config/update", this.UpdateEventConfig)
		// 埋点事件删除
		group2.Post("/event_config/delete", this.DelEventConfig)
		// 人工触发执行埋点事件数据统计任务
		group2.Post("/manual_stats", this.ManualStats)
		// 页面访问列表数据
		group2.Post("/pv_list", this.PVList)
		// 关键词搜索列表数据
		group2.Post("/keyword_list", this.QueryKeywordList)
		// 活动偏好列表数据
		group2.Post("/activity_list", this.ActivityList)
		// 充值偏好列表数据
		group2.Post("/recharge_list", this.RechargeList)
		// 提现偏好列表数据
		group2.Post("/withdrawal_list", this.WithdrawalList)
		// 游戏偏好列表数据
		group2.Post("/game_list", this.GameList)
		// 功能交互按钮列表数据
		group2.Post("/function_button_list", this.FunctionButtonList)
		// 功能交互Tab列表数据
		group2.Post("/function_tab_list", this.FunctionTabList)
	}

	this.ProcStatsTaskMap = map[int]FnProcTask{
		Enum_Stats_Task_Type_PV:              this.ProcStatsTaskPV,
		Enum_Stats_Task_Type_KeyWord:         this.ProcStatsTaskKeyword,
		Enum_Stats_Task_Type_Activity:        this.ProcStatsTaskActivity,
		Enum_Stats_Task_Type_Recharge:        this.ProcStatsTaskRecharge,
		Enum_Stats_Task_Type_Withdrawal:      this.ProcStatsTaskWithdrawal,
		Enum_Stats_Task_Type_Game:            this.ProcStatsTaskGame,
		Enum_Stats_Task_Type_Function_Button: this.ProcStatsTaskFunctionButton,
		Enum_Stats_Task_Type_Function_Tab:    this.ProcStatsTaskFunctionTab,
	}

	// 启动定时任务
	this.StartPeriodicTask()
	this.RegisterStatsTaskJob()
}

// 埋点事件查询
func (this *StatsClassController) ListEventConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := EventListReq{}
	var list []*daoModel.XAdsDictEvent
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsDictEvent)
	if len(reqData.EventName) > 0 {
		tdb.Where("event_name = ?", reqData.EventName)
	}
	if reqData.EventType > 0 {
		tdb.Where("event_type = ?", reqData.EventType)
	}

	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*daoModel.XAdsDictEvent, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 埋点事件新增
func (this *StatsClassController) CreateEventConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := daoModel.XAdsDictEvent{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}
	err := server.DaoxHashGame().XAdsDictEvent.WithContext(nil).Create(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

// 埋点事件修改
func (this *StatsClassController) UpdateEventConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := daoModel.XAdsDictEvent{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}
	err := server.DaoxHashGame().XAdsDictEvent.
		WithContext(nil).
		Where(server.DaoxHashGame().XAdsDictEvent.ID.Eq(reqData.ID)).Save(&reqData)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

// 埋点事件删除
func (this *StatsClassController) DelEventConfig(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := daoModel.XAdsDictEvent{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}
	_, err := server.DaoxHashGame().XAdsDictEvent.
		WithContext(nil).
		Where(server.DaoxHashGame().XAdsDictEvent.ID.Eq(reqData.ID)).
		Delete()
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}

// 埋点事件数据提交
func (this *StatsClassController) Collect(ctx *abugo.AbuHttpContent) {
	reqData := ReportReq{}
	errCode := 0
	err := ctx.RequestData(&reqData)
	if ctx.RespErr(err, &errCode) {
		return
	}
	logs.Debug("埋点事件数据提交: %v", reqData)
	if !this.CheckEvetId(reqData.EventId) {
		errCode = 100
		ctx.RespErrString(true, &errCode, "eventId重复错误")
		return
	}
	key := RdsKeyEventList
	rdsCtx := context.Background()
	evDate := time.Now().Truncate(24 * time.Hour) // 获取当前日期，精确到天
	dataArr := make([]string, 0)
	for _, v := range reqData.Events {
		var ev daoModel.XAdsEventTrackingRecord
		ev.SellerID = reqData.SellerId
		ev.ChannelID = reqData.ChannelId
		ev.TopAgentID = reqData.TopAgentId

		ev.UserID = reqData.UserId
		ev.Os = reqData.OsType
		ev.Network = reqData.Network

		ev.EventType = v.EventType
		ev.EventName = v.EventName
		ev.EventParams = v.EventParams

		ev.EventDate = evDate
		data, err1 := json.Marshal(ev)
		if err1 != nil {
			continue
		}
		dataArr = append(dataArr, string(data))
	}
	if len(dataArr) <= 0 {
		ctx.RespOK()
		return
	}
	_, err = server.Rdb.RPush(rdsCtx, key, dataArr).Result()
	if err != nil {
		logs.Error("埋点事件数据提交push到redis错误: %v", err)
	}
	ctx.RespOK()
}

// 检测提交eventId
func (this *StatsClassController) CheckEvetId(eventId string) bool {
	ctx := context.Background()
	key := fmt.Sprintf(RdsKeyIdemEventId, eventId)

	bSuccess, _ := server.Rdb.SetNX(ctx, key, 1, time.Minute*5).Result()
	return bSuccess
}

// 启动定时任务
func (this *StatsClassController) StartPeriodicTask() {
	go func() {
		ticker := time.NewTicker(3 * time.Minute)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				err := this.SaveRedisDataToDatabaseWorker(200) // 每次处理 200 条数据
				if err != nil {
					// 记录错误日志
					logs.Error("保存埋点事件数据到数据库错误: %v", err)
				}
			}
		}
	}()
}

// 保存埋点事件数据
func (this *StatsClassController) SaveRedisDataToDatabaseWorker(batchSize int) error {
	rdsCtx := context.Background()
	key := RdsKeyEventList

	var records []daoModel.XAdsEventTrackingRecord
	dataBatch := make([]string, 0, batchSize)

	// 从 Redis 中原子性取出数据，直到达到 batchSize 或无数据
	for len(dataBatch) < batchSize {
		dataStr, err := server.Rdb.LPop(rdsCtx, key).Result()
		if err != nil {
			if err == redis.Nil {
				break // 没有更多数据
			}
			return fmt.Errorf("failed to pop data from Redis: %v", err)
		}
		dataBatch = append(dataBatch, dataStr)
	}

	if len(dataBatch) == 0 {
		return nil // 没有数据需要处理
	}

	// 解析 JSON 数据
	for _, dataStr := range dataBatch {
		var record daoModel.XAdsEventTrackingRecord
		if err := json.Unmarshal([]byte(dataStr), &record); err != nil {
			continue // 忽略解析失败的数据
		}
		records = append(records, record)
	}

	// 批量插入数据库
	if len(records) > 0 {
		var record daoModel.XAdsEventTrackingRecord
		db := server.Db().GormDao().Table(record.TableName())
		if err := db.Create(&records).Error; err != nil {
			return fmt.Errorf("failed to save batch to database: %v", err)
		}
	}

	return nil
}

// 在初始化时注册定时任务
func (this *StatsClassController) RegisterStatsTaskJob() {
	// 注册每日凌晨2点执行的任务
	spec := "0 0 2 * * *" // cron 表达式，表示每天 2:00 执行一次
	id, err := server.RegisterCronJob(spec, this.CheckStatsTaskJob)
	if err != nil {
		logs.Error("定时埋点事件数据统计任务注册失败：%v", err)
	} else {
		logs.Info("定时埋点事件数据统计任务注册成功，任务ID：%d", id)
	}
}

func (this *StatsClassController) CheckStatsTaskJob() {
	logs.Info("开始执行埋点事件数据统计任务")
	// 使用 Redis 实现分布式锁
	lockKey := RdsKeyLockStatsTask // 锁的 key
	expireTime := 10 * time.Minute // 锁的过期时间
	if Snowflakenode <= 0 {
		Snowflakenode = xgo.GetConfigInt("server.snowflakenode", true, 0)
	}
	lockValue := fmt.Sprintf("locked_by_this_instance:%d", Snowflakenode) // 锁的值（可随机生成以避免冲突）
	// 尝试获取锁
	locked, err := server.Rdb.SetNX(context.Background(), lockKey, lockValue, expireTime).Result()
	if err != nil {
		logs.Error("获取分布式锁失败：%v", err)
		return
	}

	// 如果没有获取到锁，则直接返回
	if !locked {
		logs.Info("当前实例未获取到分布式锁，跳过执行埋点事件数据统计任务")
		return
	}

	defer func() {
		// 释放锁（可以使用 Lua 脚本保证原子性）
		_, delErr := server.Rdb.Del(context.Background(), lockKey).Result()
		if delErr != nil {
			logs.Error("释放分布式锁失败：%v", delErr)
		}
	}()
	this.StatsTaskJob()
}

// 执行埋点事件数据统计任务:PV
func (this *StatsClassController) ProcStatsTaskPV(start, end int64) {
	logs.Info("执行埋点事件数据统计任务PV:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type in ?", []int{1, 2, 3})

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务:PV 查询错误: %v", err1)
		return
	}

	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务PV查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSavePVStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务PV保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// 按照日期和其他维度进行分组统计，并写入 x_ads_page_daily_stats 表
func (this *StatsClassController) ProcessAndSavePVStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}
	// PV 事件类型集合
	PVEventMap := make(map[int32]bool)
	PVEventMap[1] = true
	PVEventMap[2] = true
	PVEventMap[3] = true

	statsMap := make(map[string]*TempPVStats)

	for _, record := range list {
		if _, has := PVEventMap[record.EventType]; !has {
			continue
		}
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		pageName, ok := params["page_name"].(string)

		if !ok || pageName == "" {
			continue
		}

		userKey := fmt.Sprintf("%d_%d", record.UserID, record.Os)
		groupKey := fmt.Sprintf("%s_%d_%d_%d_%s",
			dateStr, record.SellerID, record.ChannelID, record.TopAgentID, pageName,
		)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &TempPVStats{
				SellerID:                record.SellerID,
				ChannelID:               record.ChannelID,
				TopAgentID:              record.TopAgentID,
				PageName:                pageName,
				StatDate:                record.EventDate,
				VisitCountPc:            0,
				VisitCountH5:            0,
				VisitorSetPc:            make(map[string]bool),
				VisitorSetH5:            make(map[string]bool),
				TotalStayDurationPc:     0,
				TotalStayDurationH5:     0,
				CountLoadPcWifi:         0,
				CountLoadPcFlow:         0,
				CountLoadH5Wifi:         0,
				CountLoadH5Flow:         0,
				TotalLoadDurationPcWifi: 0,
				TotalLoadDurationPcFlow: 0,
				TotalLoadDurationH5Wifi: 0,
				TotalLoadDurationH5Flow: 0,
			}
		}

		stats := statsMap[groupKey]
		switch record.EventType {
		case 1:
			{
				switch record.Os {
				case Enum_OS_Type_PC:
					stats.VisitCountPc++
					stats.VisitorSetPc[userKey] = true
				case Enum_OS_Type_H5:
					stats.VisitCountH5++
					stats.VisitorSetH5[userKey] = true
				}
			}
		case 2:
			{
				// 提取停留时间
				if duration, ok := params["duration"].(float64); ok {
					switch record.Os {
					case Enum_OS_Type_PC:
						stats.TotalStayDurationPc += float32(duration)
					case Enum_OS_Type_H5:
						stats.TotalStayDurationH5 += float32(duration)
					}
				}
			}
		case 3:
			{
				// 提取加载时间
				if loadTime, ok := params["duration"].(float64); ok {
					switch record.Os {
					case Enum_OS_Type_PC: // PC
						switch record.Network {
						case Enum_Network_Type_WIFI:
							stats.CountLoadPcWifi++
							stats.TotalLoadDurationPcWifi += float32(loadTime)
						case Enum_Network_Type_Flow:
							stats.CountLoadPcFlow++
							stats.TotalLoadDurationPcFlow += float32(loadTime)
						}
					case Enum_OS_Type_H5: // H5
						switch record.Network {
						case Enum_Network_Type_WIFI:
							stats.CountLoadH5Wifi++
							stats.TotalLoadDurationH5Wifi += float32(loadTime)
						case Enum_Network_Type_Flow:
							stats.CountLoadH5Flow++
							stats.TotalLoadDurationH5Flow += float32(loadTime)
						}
					}
				}
			}
		}

	}

	var statsList []*daoModel.XAdsPageDailyStat
	for _, stat := range statsMap {
		statsList = append(statsList, stat.ToDBModel())
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsPageDailyStat{}).TableName())

	// 使用 GORM Upsert
	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "seller_id"}, {Name: "channel_id"}, {Name: "top_agent_id"}, {Name: "page_name"}, {Name: "stat_date"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"visit_count_pc",
				"visit_count_h5",
				"visitor_count_pc",
				"visitor_count_h5",
				"total_stay_duration_pc",
				"total_stay_duration_h5",
				"count_load_pc_wifi",
				"count_load_pc_flow",
				"count_load_h5_wifi",
				"count_load_h5_flow",
				"total_load_duration_pc_wifi",
				"total_load_duration_pc_flow",
				"total_load_duration_h5_wifi",
				"total_load_duration_h5_flow",
				"avg_load_duration_pc_wifi",
				"avg_load_duration_pc_flow",
				"avg_load_duration_h5_wifi",
				"avg_load_duration_h5_flow",
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新统计数据失败：%v", err)
			return err
		}
	}

	return nil
}

// 执行埋点事件数据统计任务:keyword 搜索
func (this *StatsClassController) ProcStatsTaskKeyword(start, end int64) {
	logs.Info("执行埋点事件数据统计任务:keyword 搜索:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type = 4")

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务:keyword 搜索查询错误: %v", err1)
		return
	}
	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务:keyword查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSaveKeywordStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务:keyword保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// 关键词搜索按照日期维度进行分组统计，并写入 x_ads_keyword_daily_stats 表
func (this *StatsClassController) ProcessAndSaveKeywordStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}

	statsMap := make(map[string]*daoModel.XAdsKeywordDailyStat)

	for _, record := range list {
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		keyWord, ok := params["key_word"].(string)
		if !ok || keyWord == "" {
			continue // 跳过无关键词的数据
		}

		groupKey := fmt.Sprintf("%d_%d_%d_%s",
			record.SellerID, record.ChannelID, record.TopAgentID, dateStr,
		)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &daoModel.XAdsKeywordDailyStat{
				SellerID:   record.SellerID,
				ChannelID:  record.ChannelID,
				TopAgentID: record.TopAgentID,
				StatDate:   record.EventDate,
				KeyWord:    "",
			}
		}

		stats := statsMap[groupKey]
		if !ContainsKeyword(stats.KeyWord, keyWord) {
			if stats.KeyWord == "" {
				stats.KeyWord = keyWord
			} else {
				stats.KeyWord += "#" + keyWord
			}
		}

	}

	var statsList []*daoModel.XAdsKeywordDailyStat
	for _, stat := range statsMap {
		statsList = append(statsList, stat)
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsKeywordDailyStat{}).TableName())

	// 使用 GORM Upsert
	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "seller_id"}, {Name: "channel_id"}, {Name: "top_agent_id"}, {Name: "stat_date"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"key_word", // 更新关键词字段
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新关键词统计数据失败：%v", err)
			return err
		}
	}

	return nil
}

// 判断关键词是否已经存在
func ContainsKeyword(keywords, keyword string) bool {
	if keywords == "" || keyword == "" {
		return false
	}
	// 拆分已有关键词并遍历查找
	keyList := strings.Split(keywords, "#")
	for _, k := range keyList {
		if k == keyword {
			return true
		}
	}
	return false
}

// 执行埋点事件数据统计任务: 活动偏好统计
func (this *StatsClassController) ProcStatsTaskActivity(start, end int64) {
	logs.Info("执行埋点事件数据统计任务: 活偏好统计:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type in ?", []int{5, 6, 7, 8})

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务: 活偏好统计查询错误: %v", err1)
		return
	}
	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务: 活偏好统计查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSaveActivityStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务: 活偏好统计保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// ProcessAndSaveActivityStatsByDate 将活动偏好事件数据按天统计并保存到 x_ads_active_daily_stats 表
func (this *StatsClassController) ProcessAndSaveActivityStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}

	statsMap := make(map[string]*daoModel.XAdsActiveDailyStat)

	for _, record := range list {
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		activeName, ok := params["activity_name"].(string)
		if !ok || activeName == "" {
			continue // 跳过无活动名的数据
		}

		groupKey := fmt.Sprintf("%d_%d_%d_%s_%s",
			record.SellerID, record.ChannelID, record.TopAgentID, dateStr, activeName,
		)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &daoModel.XAdsActiveDailyStat{
				SellerID:             record.SellerID,
				ChannelID:            record.ChannelID,
				TopAgentID:           record.TopAgentID,
				ActiveName:           activeName,
				StatDate:             record.EventDate,
				ClickCountPc:         0,
				ClickCountH5:         0,
				JoinCountPc:          0,
				JoinCountH5:          0,
				CompletionCountPc:    0,
				CompletionCountH5:    0,
				ReceivedCountPc:      0,
				ReceivedCountH5:      0,
				TotalPayoutAmountUPc: 0,
				TotalPayoutAmountUH5: 0,
				TotalPayoutAmountTPc: 0,
				TotalPayoutAmountTH5: 0,
			}
		}

		stats := statsMap[groupKey]

		switch record.Os {
		case Enum_OS_Type_PC:
			{
				switch record.EventType {
				case 5:
					stats.ClickCountPc++
				case 6:
					stats.JoinCountPc++
				case 7:
					stats.CompletionCountPc++
				case 8:
					rewardAmountU, _ := params["reward_amountU"].(float64)
					rewardAmountT, _ := params["reward_amountT"].(float64)
					stats.TotalPayoutAmountUPc += float32(rewardAmountU)
					stats.TotalPayoutAmountTPc += float32(rewardAmountT)
				}
			}
		case Enum_OS_Type_H5:
			{
				switch record.EventType {
				case 5:
					stats.ClickCountH5++
				case 6:
					stats.JoinCountH5++
				case 7:
					stats.CompletionCountH5++
				case 8:
					rewardAmountU, _ := params["reward_amountU"].(float64)
					rewardAmountT, _ := params["reward_amountT"].(float64)
					stats.TotalPayoutAmountUH5 += float32(rewardAmountU)
					stats.TotalPayoutAmountTH5 += float32(rewardAmountT)
				}
			}
		}
	}

	var statsList []*daoModel.XAdsActiveDailyStat
	for _, stat := range statsMap {
		// 计算点击转化率，并保留两位小数
		if stat.JoinCountPc > 0 {
			stat.ClickCtrPc = float32(stat.ClickCountPc) / float32(stat.JoinCountPc)
		} else {
			stat.ClickCtrPc = 0
		}
		stat.ClickCtrPc = float32(math.Round(float64(stat.ClickCtrPc)*100)) / 100

		if stat.JoinCountH5 > 0 {
			stat.ClickCtrH5 = float32(stat.ClickCountH5) / float32(stat.JoinCountH5)
		} else {
			stat.ClickCtrH5 = 0
		}
		stat.ClickCtrH5 = float32(math.Round(float64(stat.ClickCtrH5)*100)) / 100

		// 计算点击完成率，并保留两位小数
		if stat.ActiveTarPc > 0 {
			stat.ActiveTarPc = float32(stat.JoinCountPc) / float32(stat.ActiveTarPc)
		} else {
			stat.ActiveTarPc = 0
		}
		stat.ActiveTarPc = float32(math.Round(float64(stat.ActiveTarPc)*100)) / 100

		if stat.ActiveTarH5 > 0 {
			stat.ActiveTarH5 = float32(stat.JoinCountH5) / float32(stat.ActiveTarH5)
		} else {
			stat.ActiveTarH5 = 0
		}
		stat.ActiveTarH5 = float32(math.Round(float64(stat.ActiveTarH5)*100)) / 100

		statsList = append(statsList, stat)
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsActiveDailyStat{}).TableName())

	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "seller_id"},
				{Name: "channel_id"},
				{Name: "top_agent_id"},
				{Name: "active_name"},
				{Name: "stat_date"},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				"click_count_pc", "click_count_h5",
				"join_count_pc", "join_count_h5",
				"completion_count_pc", "completion_count_h5",
				"received_count_pc", "received_count_h5",
				"click_ctr_pc", "click_ctr_h5",
				"active_tar_pc", "active_tar_h5",
				"total_payout_amountU_pc", "total_payout_amountU_h5",
				"total_payout_amountT_pc", "total_payout_amountT_h5",
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新活动统计数据失败：%v", err)
			return err
		}
	}

	return nil
}

// 执行埋点事件数据统计任务: 充值偏好统计
func (this *StatsClassController) ProcStatsTaskRecharge(start, end int64) {
	logs.Info("执行埋点事件数据统计任务: 充值偏好统计 搜索:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type in ?", []int{9, 10})

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务: 充值偏好统计查询错误: %v", err1)
		return
	}
	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务: 充值偏好统计查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSaveRechargeStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务: 充值偏好统计保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// 充值偏好数据按照日期维度进行分组统计，并写入 x_ads_recharge_daily_stats 表
func (this *StatsClassController) ProcessAndSaveRechargeStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}

	statsMap := make(map[string]*daoModel.XAdsRechargeDailyStat)

	for _, record := range list {
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		rechargeChannel, ok := params["recharge_channel"].(string)
		if !ok || rechargeChannel == "" {
			continue // 跳过无充值渠道的数据
		}
		rechargeType_f, ok := params["recharge_type"].(float64)
		rechargeType := int(rechargeType_f)
		if !ok || 0 == rechargeType {
			continue // 跳过无充值类型的数据
		}

		groupKey := fmt.Sprintf("%d_%d_%d_%s_%s_%d",
			record.SellerID, record.ChannelID,
			record.TopAgentID, dateStr,
			rechargeChannel, rechargeType)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &daoModel.XAdsRechargeDailyStat{
				SellerID:               record.SellerID,
				ChannelID:              record.ChannelID,
				TopAgentID:             record.TopAgentID,
				StatDate:               record.EventDate,
				RechargeType:           int32(rechargeType),
				RechargeChannel:        rechargeChannel,
				RechargeCountPc:        0,
				RechargeCountH5:        0,
				RechargeCountSuccessPc: 0,
				RechargeCountSuccessH5: 0,
				RechargeCount100Pc:     0,
				RechargeCount100H5:     0,
				RechargeCount100500Pc:  0,
				RechargeCount100500H5:  0,
				RechargeCount5001000Pc: 0,
				RechargeCount5001000H5: 0,
				RechargeCount1000Pc:    0,
				RechargeCount1000H5:    0,
			}
		}

		stats := statsMap[groupKey]

		switch record.Os {
		case Enum_OS_Type_PC:
			{
				switch record.EventType {
				case 9:
					stats.RechargeCountPc++
				case 10:
					stats.RechargeCountSuccessPc++
					priceType_f, _ := params["price_type"].(float64)
					priceType := int(priceType_f)
					switch priceType {
					case 1:
						stats.RechargeCount100Pc++
					case 2:
						stats.RechargeCount100500Pc++
					case 3:
						stats.RechargeCount5001000Pc++
					case 4:
						stats.RechargeCount1000Pc++
					}
				}
			}
		case Enum_OS_Type_H5:
			{
				switch record.EventType {
				case 9:
					stats.RechargeCountH5++
				case 10:
					stats.RechargeCountSuccessH5++
					priceType_f, _ := params["price_type"].(float64)
					priceType := int(priceType_f)
					switch priceType {
					case 1:
						stats.RechargeCount100H5++
					case 2:
						stats.RechargeCount100500H5++
					case 3:
						stats.RechargeCount5001000H5++
					case 4:
						stats.RechargeCount1000H5++
					}
				}
			}
		}
	}

	var statsList []*daoModel.XAdsRechargeDailyStat
	for _, stat := range statsMap {
		// 计算充值占比并保留两位小数
		if stat.RechargeCountPc+stat.RechargeCountH5 > 0 {
			stat.RechargeRatePc = float32(stat.RechargeCountPc) / float32(stat.RechargeCountPc+stat.RechargeCountH5)
			stat.RechargeRateH5 = float32(stat.RechargeCountH5) / float32(stat.RechargeCountPc+stat.RechargeCountH5)
			stat.RechargeRatePc = float32(math.Round(float64(stat.RechargeRatePc)*100)) / 100
			stat.RechargeRateH5 = float32(math.Round(float64(stat.RechargeRateH5)*100)) / 100
		} else {
			stat.RechargeRatePc = 0
			stat.RechargeRateH5 = 0
		}

		// 计算充值成功率并保留两位小数
		if stat.RechargeCountPc > 0 {
			stat.RechargeSuccessRatePc = float32(stat.RechargeCountSuccessPc) / float32(stat.RechargeCountPc)
		} else {
			stat.RechargeSuccessRatePc = 0
		}
		stat.RechargeSuccessRatePc = float32(math.Round(float64(stat.RechargeSuccessRatePc)*100)) / 100

		if stat.RechargeCountH5 > 0 {
			stat.RechargeSuccessRateH5 = float32(stat.RechargeCountSuccessH5) / float32(stat.RechargeCountH5)
		} else {
			stat.RechargeSuccessRateH5 = 0
		}
		stat.RechargeSuccessRateH5 = float32(math.Round(float64(stat.RechargeSuccessRateH5)*100)) / 100

		statsList = append(statsList, stat)
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsRechargeDailyStat{}).TableName())

	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "seller_id"},
				{Name: "channel_id"},
				{Name: "top_agent_id"},
				{Name: "recharge_type"},
				{Name: "recharge_channel"},
				{Name: "stat_date"},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				"recharge_count_pc", "recharge_count_h5",
				"recharge_count_success_pc", "recharge_count_success_h5",
				"recharge_count_100_pc", "recharge_count_100_h5",
				"recharge_count_100_500_pc", "recharge_count_100_500_h5",
				"recharge_count_500_1000_pc", "recharge_count_500_1000_h5",
				"recharge_count_1000_pc", "recharge_count_1000_h5",
				"recharge_rate_pc", "recharge_rate_h5",
				"recharge_success_rate_pc", "recharge_success_rate_h5",
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新充值偏好数据失败：%v", err)
			return err
		}
	}

	return nil
}

// 执行埋点事件数据统计任务: 提现偏好统计
func (this *StatsClassController) ProcStatsTaskWithdrawal(start, end int64) {
	logs.Info("执行埋点事件数据统计任务: 提现偏好统计 搜索:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type in ?", []int{11, 12})

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务: 提现偏好统计查询错误: %v", err1)
		return
	}
	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务: 提现偏好统计查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSaveWithdrawalStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务: 提现偏好统计保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// 提现偏好数据按照日期维度进行分组统计，并写入 x_ads_withdrawal_daily_stats 表
func (this *StatsClassController) ProcessAndSaveWithdrawalStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}

	statsMap := make(map[string]*daoModel.XAdsWithdrawalDailyStat)

	for _, record := range list {
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		withdrawalChannel, ok := params["withdrawal_channel"].(string)
		if !ok || withdrawalChannel == "" {
			continue // 跳过无提现渠道的数据
		}
		withdrawalType_f, ok := params["withdrawal_type"].(float64)
		withdrawalType := int(withdrawalType_f)
		if !ok || 0 == withdrawalType {
			continue // 跳过无提现类型的数据
		}

		groupKey := fmt.Sprintf("%d_%d_%d_%s_%s_%d",
			record.SellerID, record.ChannelID,
			record.TopAgentID, dateStr,
			withdrawalChannel, withdrawalType)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &daoModel.XAdsWithdrawalDailyStat{
				SellerID:                 record.SellerID,
				ChannelID:                record.ChannelID,
				TopAgentID:               record.TopAgentID,
				StatDate:                 record.EventDate,
				WithdrawalType:           int32(withdrawalType),
				WithdrawalChannel:        withdrawalChannel,
				WithdrawalCountPc:        0,
				WithdrawalCountH5:        0,
				WithdrawalCountSuccessPc: 0,
				WithdrawalCountSuccessH5: 0,
				WithdrawalRatePc:         0,
				WithdrawalRateH5:         0,
				WithdrawalSuccessRatePc:  0,
				WithdrawalSuccessRateH5:  0,
			}
		}

		stats := statsMap[groupKey]

		switch record.Os {
		case Enum_OS_Type_PC:
			{
				switch record.EventType {
				case 11:
					stats.WithdrawalCountPc++
				case 12:
					stats.WithdrawalCountSuccessPc++
				}
			}
		case Enum_OS_Type_H5:
			{
				switch record.EventType {
				case 11:
					stats.WithdrawalCountH5++
				case 12:
					stats.WithdrawalCountSuccessH5++
				}
			}
		}
	}

	var statsList []*daoModel.XAdsWithdrawalDailyStat
	for _, stat := range statsMap {
		// 计算提现占比并保留两位小数
		if stat.WithdrawalCountPc+stat.WithdrawalCountH5 > 0 {
			stat.WithdrawalRatePc = float32(stat.WithdrawalCountPc) / float32(stat.WithdrawalCountPc+stat.WithdrawalCountH5)
			stat.WithdrawalRateH5 = float32(stat.WithdrawalCountH5) / float32(stat.WithdrawalCountPc+stat.WithdrawalCountH5)
			stat.WithdrawalRatePc = float32(math.Round(float64(stat.WithdrawalRatePc)*100)) / 100
			stat.WithdrawalRateH5 = float32(math.Round(float64(stat.WithdrawalRateH5)*100)) / 100
		} else {
			stat.WithdrawalRatePc = 0
			stat.WithdrawalRateH5 = 0
		}

		// 计算提现成功率并保留两位小数
		if stat.WithdrawalCountPc > 0 {
			stat.WithdrawalSuccessRatePc = float32(stat.WithdrawalCountSuccessPc) / float32(stat.WithdrawalCountPc)
		} else {
			stat.WithdrawalSuccessRatePc = 0
		}
		stat.WithdrawalSuccessRatePc = float32(math.Round(float64(stat.WithdrawalSuccessRatePc)*100)) / 100

		if stat.WithdrawalCountH5 > 0 {
			stat.WithdrawalSuccessRateH5 = float32(stat.WithdrawalCountSuccessH5) / float32(stat.WithdrawalCountH5)
		} else {
			stat.WithdrawalSuccessRateH5 = 0
		}
		stat.WithdrawalSuccessRateH5 = float32(math.Round(float64(stat.WithdrawalSuccessRateH5)*100)) / 100

		statsList = append(statsList, stat)
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsWithdrawalDailyStat{}).TableName())

	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "seller_id"},
				{Name: "channel_id"},
				{Name: "top_agent_id"},
				{Name: "withdrawal_type"},
				{Name: "withdrawal_channel"},
				{Name: "stat_date"},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				"withdrawal_count_pc", "withdrawal_count_h5",
				"withdrawal_count_success_pc", "withdrawal_count_success_h5",
				"withdrawal_rate_pc", "withdrawal_rate_h5",
				"withdrawal_success_rate_pc", "withdrawal_success_rate_h5",
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新充值偏好数据失败：%v", err)
			return err
		}
	}

	return nil
}

// 执行埋点事件数据统计任务: 游戏偏好统计
func (this *StatsClassController) ProcStatsTaskGame(start, end int64) {
	logs.Info("执行埋点事件数据统计任务: 游戏偏好统计 搜索:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type = ?", 13)

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务: 游戏偏好统计查询错误: %v", err1)
		return
	}
	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务: 游戏偏好统计查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSaveGameStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务: 游戏偏好统计保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// 游戏偏好数据按照日期维度进行分组统计，并写入 x_ads_game_daily_stats 表
func (this *StatsClassController) ProcessAndSaveGameStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}

	statsMap := make(map[string]*daoModel.XAdsGameDailyStat)

	for _, record := range list {
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		gameTag, ok := params["game_tag"].(string)
		if !ok || gameTag == "" {
			continue // 跳过无游戏标签的数据
		}
		gameName, ok := params["game_name"].(string)
		if !ok || gameName == "" {
			continue // 跳过无游戏名称的数据
		}
		gameCompany, ok := params["game_company"].(string)
		if !ok || gameCompany == "" {
			continue // 跳过无游戏厂商的数据
		}
		amountType_f, ok := params["amount_type"].(float64)
		amountType := int(amountType_f)
		if !ok || 0 == amountType {
			continue // 跳过无下注金额类型的数据
		}
		amount, ok := params["amount"].(float64)
		if !ok || amountType <= 0 {
			continue // 跳过无金额小于0的数据
		}
		groupKey := fmt.Sprintf("%d_%d_%d_%s_%s_%s_%s",
			record.SellerID, record.ChannelID,
			record.TopAgentID, dateStr,
			gameTag, gameName, gameCompany)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &daoModel.XAdsGameDailyStat{
				SellerID:     record.SellerID,
				ChannelID:    record.ChannelID,
				TopAgentID:   record.TopAgentID,
				StatDate:     record.EventDate,
				GameTag:      gameTag,
				GameName:     gameName,
				GameCompany:  gameCompany,
				BetAmountUPc: 0,
				BetAmountUH5: 0,
				BetAmountTPc: 0,
				BetAmountTH5: 0,
				BetCountUPc:  0,
				BetCountUH5:  0,
				BetCountTPc:  0,
				BetCountTH5:  0,
			}
		}

		stats := statsMap[groupKey]

		switch record.Os {
		case Enum_OS_Type_PC:
			{
				switch amountType {
				case 1:
					stats.BetCountUPc++
					stats.BetAmountUPc += float32(amount)
				case 2:
					stats.BetCountTPc++
					stats.BetAmountTPc += float32(amount)
				}
			}
		case Enum_OS_Type_H5:
			{
				switch amountType {
				case 1:
					stats.BetCountUH5++
					stats.BetAmountUH5 += float32(amount)
				case 2:
					stats.BetCountTH5++
					stats.BetAmountTH5 += float32(amount)
				}
			}
		}
	}

	var statsList []*daoModel.XAdsGameDailyStat
	for _, stat := range statsMap {
		statsList = append(statsList, stat)
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsGameDailyStat{}).TableName())

	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "seller_id"},
				{Name: "channel_id"},
				{Name: "top_agent_id"},
				{Name: "game_tag"},
				{Name: "game_name"},
				{Name: "game_company"},
				{Name: "stat_date"},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				"bet_count_u_pc", "bet_count_u_h5",
				"bet_count_t_pc", "bet_count_t_h5",
				"bet_amount_u_pc", "bet_amount_u_h5",
				"bet_amount_t_pc", "bet_amount_t_h5",
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新游戏偏好数据失败：%v", err)
			return err
		}
	}

	return nil
}

// 执行埋点事件数据统计任务: 功能交互按钮
func (this *StatsClassController) ProcStatsTaskFunctionButton(start, end int64) {
	logs.Info("执行埋点事件数据统计任务: 功能交互按钮 搜索:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type = ?", 14)

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务:功能交互按钮统计查询错误: %v", err1)
		return
	}
	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务: 功能交互按钮统计查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSaveFunctionButtonStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务: 功能交互按钮统计保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// 功能交互按钮数据按照日期维度进行分组统计，并写入 x_ads_function_interaction_button_daily_stats 表
func (this *StatsClassController) ProcessAndSaveFunctionButtonStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}

	statsMap := make(map[string]*daoModel.XAdsFunctionInteractionButtonDailyStat)

	for _, record := range list {
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		buttonName, ok := params["button_name"].(string)
		if !ok || buttonName == "" {
			continue // 跳过无按钮名称的数据
		}
		responseTime_f, ok := params["response_time"].(float64)
		responseTime := int(responseTime_f)
		if !ok || 0 == responseTime {
			continue // 跳过无响应时间的数据
		}

		groupKey := fmt.Sprintf("%d_%d_%d_%s_%s",
			record.SellerID, record.ChannelID,
			record.TopAgentID, dateStr,
			buttonName)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &daoModel.XAdsFunctionInteractionButtonDailyStat{
				SellerID:          record.SellerID,
				ChannelID:         record.ChannelID,
				TopAgentID:        record.TopAgentID,
				StatDate:          record.EventDate,
				ButtonName:        buttonName,
				ClickCountPc:      0,
				ClickCountH5:      0,
				ResponseTimePc:    0,
				ResponseTimeH5:    0,
				AvgResponseTimePc: 0,
				AvgResponseTimeH5: 0,
			}
		}

		stats := statsMap[groupKey]

		switch record.Os {
		case Enum_OS_Type_PC:
			{
				stats.ClickCountPc++
				stats.ResponseTimePc += float32(responseTime)
			}
		case Enum_OS_Type_H5:
			{
				stats.ClickCountH5++
				stats.ResponseTimeH5 += float32(responseTime)
			}
		}
	}

	var statsList []*daoModel.XAdsFunctionInteractionButtonDailyStat
	for _, stat := range statsMap {
		// 计算平均响应时长(s)，并保留两位小数
		if stat.ResponseTimePc > 0 {
			stat.AvgResponseTimePc = stat.ResponseTimePc / float32(stat.ClickCountPc)
			stat.AvgResponseTimePc = float32(math.Round(float64(stat.AvgResponseTimePc)*100)) / 100
		} else {
			stat.AvgResponseTimePc = 0
		}
		if stat.ResponseTimeH5 > 0 {
			stat.AvgResponseTimeH5 = stat.ResponseTimeH5 / float32(stat.ClickCountH5)
			stat.AvgResponseTimeH5 = float32(math.Round(float64(stat.AvgResponseTimeH5)*100)) / 100
		} else {
			stat.AvgResponseTimeH5 = 0
		}

		statsList = append(statsList, stat)
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsFunctionInteractionButtonDailyStat{}).TableName())

	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "seller_id"},
				{Name: "channel_id"},
				{Name: "top_agent_id"},
				{Name: "button_name"},
				{Name: "stat_date"},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				"click_count_pc", "click_count_h5",
				"response_time_pc", "response_time_h5",
				"avg_response_time_pc", "avg_response_time_h5",
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新功能交互按钮数据失败：%v", err)
			return err
		}
	}

	return nil
}

// 执行埋点事件数据统计任务: 功能交互Tab
func (this *StatsClassController) ProcStatsTaskFunctionTab(start, end int64) {
	logs.Info("执行埋点事件数据统计任务: 功能交互Tab 搜索:start:%d, end:%d", start, end)
	var list []*daoModel.XAdsEventTrackingRecord
	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsEventTrackingRecord).
		Where("event_type = ?", 15)

	err1 := this.GetWhereByDate(tdb, start, end)
	if err1 != nil {
		logs.Error("执行埋点事件数据统计任务:功能交互Tab统计查询错误: %v", err1)
		return
	}
	err := tdb.Find(&list).Error
	if err != nil {
		logs.Error("执行埋点事件数据统计任务: 功能交互Tab统计查询错误: %v", err)
		return
	}
	// 按日期分类数据
	dateMap := make(map[string][]*daoModel.XAdsEventTrackingRecord)
	for _, v := range list {
		dateStr := v.EventDate.Format("2006-01-02") // 日期格式化
		if _, ok := dateMap[dateStr]; !ok {
			dateMap[dateStr] = make([]*daoModel.XAdsEventTrackingRecord, 0)
		}
		dateMap[dateStr] = append(dateMap[dateStr], v)
	}
	for dateStr, v := range dateMap {
		err1 = this.ProcessAndSaveFunctionTabStatsByDate(v)
		if err1 != nil {
			logs.Error("执行埋点事件数据统计任务: 功能交互Tab统计保存错误:%s,  %v", dateStr, err1)
		}
	}
}

// 功能交互Tab数据按照日期维度进行分组统计，并写入 x_ads_function_interaction_tab_daily_stats 表
func (this *StatsClassController) ProcessAndSaveFunctionTabStatsByDate(list []*daoModel.XAdsEventTrackingRecord) error {
	if len(list) == 0 {
		return nil
	}

	statsMap := make(map[string]*daoModel.XAdsFunctionInteractionTabDailyStat)
	total_count_all := int32(0)
	total_count_pc := int32(0)
	total_count_h5 := int32(0)

	for _, record := range list {
		dateStr := record.EventDate.Format("2006-01-02")
		params := ParseEventParams(record.EventParams)

		buttonName, ok := params["button_name"].(string)
		if !ok || buttonName == "" {
			continue // 跳过无按钮名称的数据
		}
		tabName, ok := params["tab_name"].(string)
		if !ok || tabName == "" {
			continue // 跳过无tab名称的数据
		}
		total_count_all++
		groupKey := fmt.Sprintf("%d_%d_%d_%s_%s_%s",
			record.SellerID, record.ChannelID,
			record.TopAgentID, dateStr,
			buttonName, tabName)

		if _, exists := statsMap[groupKey]; !exists {
			statsMap[groupKey] = &daoModel.XAdsFunctionInteractionTabDailyStat{
				SellerID:      record.SellerID,
				ChannelID:     record.ChannelID,
				TopAgentID:    record.TopAgentID,
				StatDate:      record.EventDate,
				ButtonName:    buttonName,
				TabName:       tabName,
				ClickCountPc:  0,
				ClickCountH5:  0,
				ClickCountAll: 0,
				TabRatePc:     0,
				TabRateH5:     0,
				TabRateAll:    0,
			}
		}

		stats := statsMap[groupKey]
		stats.ClickCountAll++
		switch record.Os {
		case Enum_OS_Type_PC:
			{
				stats.ClickCountPc++
				total_count_pc++
			}
		case Enum_OS_Type_H5:
			{
				stats.ClickCountH5++
				total_count_h5++
			}
		}
	}

	var statsList []*daoModel.XAdsFunctionInteractionTabDailyStat
	for _, stat := range statsMap {
		// 计算占比: 与所有tab之和的比值，并保留两位小数
		if total_count_all > 0 {
			stat.TabRateAll = float32(stat.ClickCountAll) / float32(total_count_all)
			stat.TabRateAll = float32(math.Round(float64(stat.TabRateAll)*100)) / 100
		} else {
			stat.TabRateAll = 0
		}
		if total_count_pc > 0 {
			stat.TabRatePc = float32(stat.ClickCountPc) / float32(total_count_pc)
			stat.TabRatePc = float32(math.Round(float64(stat.TabRatePc)*100)) / 100
		} else {
			stat.TabRatePc = 0
		}
		if total_count_h5 > 0 {
			stat.TabRateH5 = float32(stat.ClickCountH5) / float32(total_count_h5)
			stat.TabRateH5 = float32(math.Round(float64(stat.TabRateH5)*100)) / 100
		} else {
			stat.TabRateH5 = 0
		}

		statsList = append(statsList, stat)
	}

	db := server.Db().GormDao().Table((&daoModel.XAdsFunctionInteractionTabDailyStat{}).TableName())

	if len(statsList) > 0 {
		err := db.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "seller_id"},
				{Name: "channel_id"},
				{Name: "top_agent_id"},
				{Name: "button_name"},
				{Name: "tab_name"},
				{Name: "stat_date"},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				"click_count_pc", "click_count_h5",
				"click_count_all", "tab_rate_pc",
				"tab_rate_h5", "tab_rate_all",
			}),
		}).Create(&statsList).Error

		if err != nil {
			logs.Error("批量插入/更新功能交互Tab数据失败：%v", err)
			return err
		}
	}

	return nil
}

func (this *StatsClassController) GetWhereByDate(tdb *gorm.DB, start, end int64) error {
	if start == end {
		if start <= 0 {
			return errors.New("执行埋点事件数据统计任务查询时间为0错误")
		}
		tdb.Where("event_date = ?", abugo.TimeStampToLocalDate(start))
	} else {
		// 时间区间
		if start > 0 {
			tdb.Where("event_date >= ?", abugo.TimeStampToLocalDate(start))
		}
		// 时间区间
		if end > 0 && end > start {
			tdb.Where("event_date <= ?", abugo.TimeStampToLocalDate(end))
		}
	}
	return nil
}

// 辅助函数解析 event_params 字段
func ParseEventParams(params string) map[string]interface{} {
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(params), &result); err != nil {
		return map[string]interface{}{}
	}
	return result
}

func (this *StatsClassController) ProcManualStats(req *ManualStatsReq) {
	logs.Info("人工执行埋点事件数据统计任务:%d", req.StatsType)
	if proc, ok := this.ProcStatsTaskMap[req.StatsType]; ok {
		_ = ants.Submit(func() {
			proc(req.StartTime, req.EndTime)
		})
	} else {
		logs.Error("人工执行埋点事件数据统计任务:错误的统计类型")
		return
	}
}

func GetYesterdayTimestamp() int64 {
	// 获取当前时间并截断到天（即当天的 00:00:00）
	today := time.Now().Truncate(24 * time.Hour)
	// 减去 24 小时得到前一天
	yesterday := today.Add(-24 * time.Hour)
	// 返回毫秒级时间戳
	return yesterday.UnixNano() / int64(time.Millisecond)
}

func (this *StatsClassController) StatsTaskJob() {
	// 获取当前日期的前一天的日期格式到天
	start := GetYesterdayTimestamp()
	for _, v := range this.ProcStatsTaskMap {
		//_ = ants.Submit(func() {
		//	v(start, start)
		//})

		v(start, start)
	}
}

// 人工触发执行埋点事件数据统计任务
func (this *StatsClassController) ManualStats(ctx *abugo.AbuHttpContent) {
	errCode := 0
	reqData := ManualStatsReq{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}
	_ = ants.Submit(func() {
		this.ProcManualStats(&reqData)
	})
	ctx.RespOK()
	return
}

// 页面访问列表数据
func (this *StatsClassController) PVList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := PVListReq{}
	var list []*PVListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsPageDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"page_name",
		"stat_date",
		"visit_count_pc",
		"visit_count_h5",
		"visitor_count_pc",
		"visitor_count_h5",
		"total_stay_duration_pc",
		"total_stay_duration_h5",
		"avg_load_duration_pc_wifi",
		"avg_load_duration_pc_flow",
		"avg_load_duration_h5_wifi",
		"avg_load_duration_h5_flow")

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*PVListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 获取where 条件
func (this *StatsClassController) CommonGetWhere(sellerId, channelId int32,
	topAgentId, start, end int64, db *gorm.DB) {
	// 商户id
	if sellerId > 0 {
		db.Where("seller_id = ?", sellerId)
	}
	// 渠道id
	if channelId > 0 {
		db.Where("channel_id = ?", channelId)
	}

	// 顶级代理id
	if topAgentId > 0 {
		db.Where("top_agent_id = ?", topAgentId)
	}

	// 时间区间
	if start > 0 {
		db.Where("stat_date >= ?", abugo.TimeStampToLocalTime(start))
	}
	// 时间区间
	if end > 0 && end > start {
		db.Where("stat_date <= ?", abugo.TimeStampToLocalTime(end))
	}
}

// 关键词搜索列表数据
func (this *StatsClassController) QueryKeywordList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := QueryKeywordListReq{}
	var list []*QueryKeywordListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsKeywordDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"key_word",
		"stat_date")

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*QueryKeywordListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 活动偏好列表数据
func (this *StatsClassController) ActivityList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := ActivityListReq{}
	var list []*ActivityListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsActiveDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"stat_date",
		"active_name",
		"click_count_pc",
		"click_count_h5",
		"join_count_pc + join_count_h5 AS join_count",
		"completion_count_pc + completion_count_h5 AS completion_count",
		"received_count_pc + received_count_h5 AS received_count",
		"click_ctr_pc + click_ctr_h5 AS click_ctr",
		"active_tar_pc + active_tar_h5 AS active_tar",
		"total_payout_amountU_pc + total_payout_amountU_h5 AS total_payout_amountU",
		"total_payout_amountT_pc + total_payout_amountT_h5 AS total_payout_amountT")

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*ActivityListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 充值偏好列表数据
func (this *StatsClassController) RechargeList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := RechargeListReq{}
	var list []*RechargeListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsRechargeDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"stat_date",
		"recharge_channel",
		"recharge_type",
		"recharge_rate_pc",
		"recharge_rate_h5",
		"recharge_success_rate_pc",
		"recharge_success_rate_h5",
		"recharge_count_100_pc",
		"recharge_count_100_h5",
		"recharge_count_100_500_pc",
		"recharge_count_100_500_h5",
		"recharge_count_500_1000_pc",
		"recharge_count_500_1000_h5",
		"recharge_count_1000_pc",
		"recharge_count_1000_h5").Where("recharge_type = ?", reqData.RechargeType)

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*RechargeListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 提现偏好列表数据
func (this *StatsClassController) WithdrawalList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := WithdrawalListReq{}
	var list []*WithdrawalListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsWithdrawalDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"stat_date",
		"withdrawal_channel",
		"withdrawal_type",
		"withdrawal_rate_pc",
		"withdrawal_rate_h5",
		"withdrawal_success_rate_pc",
		"withdrawal_success_rate_h5").Where("withdrawal_type = ?", reqData.WithdrawalType)

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*WithdrawalListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 游戏偏好列表数据
func (this *StatsClassController) GameList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := GameListReq{}
	var list []*GameListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsGameDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"stat_date",
		"game_tag",
		"game_name",
		"game_company",
		"bet_count_u_pc",
		"bet_count_u_h5",
		"bet_count_t_pc",
		"bet_count_t_h5",
		"bet_amount_u_pc",
		"bet_amount_u_h5",
		"bet_amount_t_pc",
		"bet_amount_t_h5")

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*GameListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 功能交互按钮列表数据
func (this *StatsClassController) FunctionButtonList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := FunctionButtonListReq{}
	var list []*FunctionButtonListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsFunctionInteractionButtonDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"stat_date",
		"button_name",
		"click_count_pc",
		"click_count_h5",
		"avg_response_time_pc",
		"avg_response_time_h5")

	if len(reqData.ButtonName) > 0 {
		tdb.Where("button_name = ?", reqData.ButtonName)
	}

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*FunctionButtonListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 功能交互Tab列表数据
func (this *StatsClassController) FunctionTabList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	var total int64
	reqData := FunctionTabListReq{}
	var list []*FunctionTabListResp
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&reqData), reqData, "用户管理", "用户分类", "查", "查看用户分类")
	if token == nil {
		return
	}

	// 渠道后台只有渠道数据
	if token.ChannelId > 0 {
		reqData.ChannelId = int32(token.ChannelId)
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}
	offset := (reqData.Page - 1) * reqData.PageSize
	limit := reqData.PageSize

	tdb := server.Db().GormDao().Table(daoModel.TableNameXAdsFunctionInteractionTabDailyStat)
	tdb.Select("seller_id",
		"channel_id",
		"top_agent_id",
		"stat_date",
		"button_name",
		"tab_name",
		"click_count_all",
		"tab_rate_all")

	if len(reqData.ButtonName) > 0 {
		tdb.Where("button_name = ?", reqData.ButtonName)
	}
	if len(reqData.TabName) > 0 {
		tdb.Where("tab_name = ?", reqData.TabName)
	}

	this.CommonGetWhere(reqData.SellerId, reqData.ChannelId, reqData.TopAgentId, reqData.StartTime, reqData.EndTime, tdb)
	if err := tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	// 空数据的情况
	if total <= 0 {
		ctx.Put("data", make([]*FunctionTabListResp, 0))
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	err := tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}
