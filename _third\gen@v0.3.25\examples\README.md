# examples

***Run generate.sh***

一个简单的`GEN`最佳实践。你可以通过配置`generate.sh`中的`TARGET_DIR`值指定执行不同的代码生成命令。

A simple best practice of `GEN`. You can configure `TARGET_DIR` value in `generate.sh` to generate different code.

## TARGET_DIR

- `gen`
a slim quick start.

- `ultimate`
a ultimate quick start

- `sync_table`
a quick start to show how to sync table from database.

- `without_db`
a quick start to show how to generate code without database connection.
