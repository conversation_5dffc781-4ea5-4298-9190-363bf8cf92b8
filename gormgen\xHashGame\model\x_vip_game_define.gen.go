// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXVipGameDefine = "x_vip_game_define"

// XVipGameDefine mapped from table <x_vip_game_define>
type XVipGameDefine struct {
	ID         int32     `gorm:"column:Id;not null" json:"Id"`
	SellerID   int32     `gorm:"column:SellerId;primaryKey" json:"SellerId"`
	ChannelID  int32     `gorm:"column:ChannelId;primaryKey" json:"ChannelId"`
	VipLevel   int32     `gorm:"column:VipLevel;primaryKey;comment:vip等级" json:"VipLevel"`                            // vip等级
	Brand      string    `gorm:"column:Brand;primaryKey;comment:品牌" json:"Brand"`                                     // 品牌
	GameID     string    `gorm:"column:GameId;primaryKey;comment:游戏Id" json:"GameId"`                                 // 游戏Id
	CatID      int32     `gorm:"column:CatId;primaryKey;comment:彩票大类ID" json:"CatId"`                                 // 彩票大类ID
	RewardRate float64   `gorm:"column:RewardRate;default:0.000000;comment:反水比例" json:"RewardRate"`                   // 反水比例
	Memo       string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XVipGameDefine's table name
func (*XVipGameDefine) TableName() string {
	return TableNameXVipGameDefine
}
