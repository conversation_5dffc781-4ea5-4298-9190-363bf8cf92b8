// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentGameDailly = "x_agent_game_dailly"

// XAgentGameDailly mapped from table <x_agent_game_dailly>
type XAgentGameDailly struct {
	ID            int32     `gorm:"column:Id;not null" json:"Id"`
	RecordDate    time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	UserID        int32     `gorm:"column:UserId;primaryKey" json:"UserId"`
	Brand         string    `gorm:"column:Brand;primaryKey;comment:品牌" json:"Brand"`     // 品牌
	GameID        string    `gorm:"column:GameId;primaryKey;comment:游戏Id" json:"GameId"` // 游戏Id
	CatID         int32     `gorm:"column:CatId;primaryKey;comment:彩票大类ID" json:"CatId"` // 彩票大类ID
	SellerID      int32     `gorm:"column:SellerId;not null" json:"SellerId"`
	ChannelID     int32     `gorm:"column:ChannelId;default:1" json:"ChannelId"`
	NewSelfLiuSui float64   `gorm:"column:NewSelfLiuSui;default:0.000000" json:"NewSelfLiuSui"`
	Reward        float64   `gorm:"column:Reward;default:0.000000" json:"Reward"`
	Memo          string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	CreateTime    time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime    time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XAgentGameDailly's table name
func (*XAgentGameDailly) TableName() string {
	return TableNameXAgentGameDailly
}
