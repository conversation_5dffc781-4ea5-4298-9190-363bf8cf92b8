// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentGameCommissionConfig(db *gorm.DB, opts ...gen.DOOption) xAgentGameCommissionConfig {
	_xAgentGameCommissionConfig := xAgentGameCommissionConfig{}

	_xAgentGameCommissionConfig.xAgentGameCommissionConfigDo.UseDB(db, opts...)
	_xAgentGameCommissionConfig.xAgentGameCommissionConfigDo.UseModel(&model.XAgentGameCommissionConfig{})

	tableName := _xAgentGameCommissionConfig.xAgentGameCommissionConfigDo.TableName()
	_xAgentGameCommissionConfig.ALL = field.NewAsterisk(tableName)
	_xAgentGameCommissionConfig.ID = field.NewInt32(tableName, "Id")
	_xAgentGameCommissionConfig.Brand = field.NewString(tableName, "Brand")
	_xAgentGameCommissionConfig.GameID = field.NewString(tableName, "GameId")
	_xAgentGameCommissionConfig.CatID = field.NewInt32(tableName, "CatId")
	_xAgentGameCommissionConfig.RewardRate = field.NewFloat64(tableName, "RewardRate")
	_xAgentGameCommissionConfig.Memo = field.NewString(tableName, "Memo")
	_xAgentGameCommissionConfig.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentGameCommissionConfig.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentGameCommissionConfig.fillFieldMap()

	return _xAgentGameCommissionConfig
}

type xAgentGameCommissionConfig struct {
	xAgentGameCommissionConfigDo xAgentGameCommissionConfigDo

	ALL        field.Asterisk
	ID         field.Int32   // id
	Brand      field.String  // 品牌
	GameID     field.String  // 游戏Id
	CatID      field.Int32   // 彩票大类ID
	RewardRate field.Float64 // 返佣比例
	Memo       field.String  // 备注
	CreateTime field.Time    // 创建时间
	UpdateTime field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentGameCommissionConfig) Table(newTableName string) *xAgentGameCommissionConfig {
	x.xAgentGameCommissionConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentGameCommissionConfig) As(alias string) *xAgentGameCommissionConfig {
	x.xAgentGameCommissionConfigDo.DO = *(x.xAgentGameCommissionConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentGameCommissionConfig) updateTableName(table string) *xAgentGameCommissionConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.CatID = field.NewInt32(table, "CatId")
	x.RewardRate = field.NewFloat64(table, "RewardRate")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentGameCommissionConfig) WithContext(ctx context.Context) *xAgentGameCommissionConfigDo {
	return x.xAgentGameCommissionConfigDo.WithContext(ctx)
}

func (x xAgentGameCommissionConfig) TableName() string {
	return x.xAgentGameCommissionConfigDo.TableName()
}

func (x xAgentGameCommissionConfig) Alias() string { return x.xAgentGameCommissionConfigDo.Alias() }

func (x xAgentGameCommissionConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentGameCommissionConfigDo.Columns(cols...)
}

func (x *xAgentGameCommissionConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentGameCommissionConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["CatId"] = x.CatID
	x.fieldMap["RewardRate"] = x.RewardRate
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentGameCommissionConfig) clone(db *gorm.DB) xAgentGameCommissionConfig {
	x.xAgentGameCommissionConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentGameCommissionConfig) replaceDB(db *gorm.DB) xAgentGameCommissionConfig {
	x.xAgentGameCommissionConfigDo.ReplaceDB(db)
	return x
}

type xAgentGameCommissionConfigDo struct{ gen.DO }

func (x xAgentGameCommissionConfigDo) Debug() *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentGameCommissionConfigDo) WithContext(ctx context.Context) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentGameCommissionConfigDo) ReadDB() *xAgentGameCommissionConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentGameCommissionConfigDo) WriteDB() *xAgentGameCommissionConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentGameCommissionConfigDo) Session(config *gorm.Session) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentGameCommissionConfigDo) Clauses(conds ...clause.Expression) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentGameCommissionConfigDo) Returning(value interface{}, columns ...string) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentGameCommissionConfigDo) Not(conds ...gen.Condition) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentGameCommissionConfigDo) Or(conds ...gen.Condition) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentGameCommissionConfigDo) Select(conds ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentGameCommissionConfigDo) Where(conds ...gen.Condition) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentGameCommissionConfigDo) Order(conds ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentGameCommissionConfigDo) Distinct(cols ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentGameCommissionConfigDo) Omit(cols ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentGameCommissionConfigDo) Join(table schema.Tabler, on ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentGameCommissionConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentGameCommissionConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentGameCommissionConfigDo) Group(cols ...field.Expr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentGameCommissionConfigDo) Having(conds ...gen.Condition) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentGameCommissionConfigDo) Limit(limit int) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentGameCommissionConfigDo) Offset(offset int) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentGameCommissionConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentGameCommissionConfigDo) Unscoped() *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentGameCommissionConfigDo) Create(values ...*model.XAgentGameCommissionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentGameCommissionConfigDo) CreateInBatches(values []*model.XAgentGameCommissionConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentGameCommissionConfigDo) Save(values ...*model.XAgentGameCommissionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentGameCommissionConfigDo) First() (*model.XAgentGameCommissionConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameCommissionConfig), nil
	}
}

func (x xAgentGameCommissionConfigDo) Take() (*model.XAgentGameCommissionConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameCommissionConfig), nil
	}
}

func (x xAgentGameCommissionConfigDo) Last() (*model.XAgentGameCommissionConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameCommissionConfig), nil
	}
}

func (x xAgentGameCommissionConfigDo) Find() ([]*model.XAgentGameCommissionConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentGameCommissionConfig), err
}

func (x xAgentGameCommissionConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentGameCommissionConfig, err error) {
	buf := make([]*model.XAgentGameCommissionConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentGameCommissionConfigDo) FindInBatches(result *[]*model.XAgentGameCommissionConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentGameCommissionConfigDo) Attrs(attrs ...field.AssignExpr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentGameCommissionConfigDo) Assign(attrs ...field.AssignExpr) *xAgentGameCommissionConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentGameCommissionConfigDo) Joins(fields ...field.RelationField) *xAgentGameCommissionConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentGameCommissionConfigDo) Preload(fields ...field.RelationField) *xAgentGameCommissionConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentGameCommissionConfigDo) FirstOrInit() (*model.XAgentGameCommissionConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameCommissionConfig), nil
	}
}

func (x xAgentGameCommissionConfigDo) FirstOrCreate() (*model.XAgentGameCommissionConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentGameCommissionConfig), nil
	}
}

func (x xAgentGameCommissionConfigDo) FindByPage(offset int, limit int) (result []*model.XAgentGameCommissionConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentGameCommissionConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentGameCommissionConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentGameCommissionConfigDo) Delete(models ...*model.XAgentGameCommissionConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentGameCommissionConfigDo) withDO(do gen.Dao) *xAgentGameCommissionConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
