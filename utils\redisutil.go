package utils

import (
	"errors"
	"github.com/beego/beego/logs"
	"github.com/garyburd/redigo/redis"
)

const (
	RedisCGet    = "get"
	RedisCSet    = "set"
	RedisCDel    = "del"
	RedisCMGet   = "MGet"
	RedisCLockEX = "EX"
	RedisCLockNX = "NX"
)

func RedisHandlerByPool(pool *redis.Pool, fun func(conn redis.Conn) error) {
	conn := pool.Get()
	defer func(conn redis.Conn) {
		err := conn.Close()
		if err != nil {
			logs.Error("redis handler error", err)
		}
	}(conn)

	err := fun(conn)
	if err != nil {
		logs.Error("redis handler error", err)
	}
}

func GetString(pool *redis.Pool, key string) string {
	var result string
	RedisHandlerByPool(pool, func(conn redis.Conn) error {
		str, err := redis.String(conn.Do(RedisCGet, key))
		result = str
		return err
	})
	return result
}

func MultiGetString(pool *redis.Pool, keys []string) []string {
	var result []string
	RedisHandlerByPool(pool, func(conn redis.Conn) error {
		strs, err := redis.Strings(conn.Do(RedisCMGet, keys))
		result = strs
		return err
	})
	return result
}

//func LockFullHandler(pool *redis.Pool, key string, v string, timeout int) {
//	isGetLock := LockRedisHandler(pool, key, v, timeout)
//	if !isGetLock {
//		return
//	}
//
//}

func LockRedisHandler(pool *redis.Pool, key string, v string, timeout int, finishDelLock bool,
	fun func(conn *redis.Conn) error) {

	RedisHandlerByPool(pool, func(conn redis.Conn) error {
		lock := lockRedisHandler(conn, key, v, timeout)
		if !lock {
			return errors.New("redis锁已锁定")
		}

		err := fun(&conn)
		if err != nil {
			//输出错误，但是继续执行
			return errors.New("redis删除锁失败")
		}

		if finishDelLock {
			_, err := conn.Do(RedisCDel, key)
			if err != nil {
				return errors.New("redis删除锁失败")
			}
		}
		return nil
	})
}

func lockRedisHandler(conn redis.Conn, key string, v string, timeout int) bool {
	//var result string
	isGetLock := true
	_, err := redis.String(conn.Do(RedisCSet, key, v, RedisCLockEX, timeout, RedisCLockNX))
	if err == redis.ErrNil {
		isGetLock = false
		err = nil
	}
	return isGetLock
}
