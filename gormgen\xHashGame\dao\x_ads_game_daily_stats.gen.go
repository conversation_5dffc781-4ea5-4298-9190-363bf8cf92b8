// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsGameDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsGameDailyStat {
	_xAdsGameDailyStat := xAdsGameDailyStat{}

	_xAdsGameDailyStat.xAdsGameDailyStatDo.UseDB(db, opts...)
	_xAdsGameDailyStat.xAdsGameDailyStatDo.UseModel(&model.XAdsGameDailyStat{})

	tableName := _xAdsGameDailyStat.xAdsGameDailyStatDo.TableName()
	_xAdsGameDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsGameDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsGameDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsGameDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsGameDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsGameDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsGameDailyStat.GameTag = field.NewString(tableName, "game_tag")
	_xAdsGameDailyStat.GameName = field.NewString(tableName, "game_name")
	_xAdsGameDailyStat.GameCompany = field.NewString(tableName, "game_company")
	_xAdsGameDailyStat.BetCountUPc = field.NewInt32(tableName, "bet_count_u_pc")
	_xAdsGameDailyStat.BetCountUH5 = field.NewInt32(tableName, "bet_count_u_h5")
	_xAdsGameDailyStat.BetCountTPc = field.NewInt32(tableName, "bet_count_t_pc")
	_xAdsGameDailyStat.BetCountTH5 = field.NewInt32(tableName, "bet_count_t_h5")
	_xAdsGameDailyStat.BetAmountUPc = field.NewFloat32(tableName, "bet_amount_u_pc")
	_xAdsGameDailyStat.BetAmountUH5 = field.NewFloat32(tableName, "bet_amount_u_h5")
	_xAdsGameDailyStat.BetAmountTPc = field.NewFloat32(tableName, "bet_amount_t_pc")
	_xAdsGameDailyStat.BetAmountTH5 = field.NewFloat32(tableName, "bet_amount_t_h5")
	_xAdsGameDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsGameDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsGameDailyStat.fillFieldMap()

	return _xAdsGameDailyStat
}

// xAdsGameDailyStat 游戏偏好每日统计表
type xAdsGameDailyStat struct {
	xAdsGameDailyStatDo xAdsGameDailyStatDo

	ALL          field.Asterisk
	ID           field.Int64   // 主键ID
	SellerID     field.Int32   // 运营商ID
	ChannelID    field.Int32   // 渠道ID
	TopAgentID   field.Int64   // 顶级代理ID
	StatDate     field.Time    // 统计日期
	GameTag      field.String  // 游戏标签
	GameName     field.String  // 游戏名称
	GameCompany  field.String  // 游戏厂商
	BetCountUPc  field.Int32   // 下注次数U pc
	BetCountUH5  field.Int32   // 下注次数U h5
	BetCountTPc  field.Int32   // 下注次数T pc
	BetCountTH5  field.Int32   // 下注次数T h5
	BetAmountUPc field.Float32 // 下注金额U pc
	BetAmountUH5 field.Float32 // 下注金额U h5
	BetAmountTPc field.Float32 // 下注金额T pc
	BetAmountTH5 field.Float32 // 下注金额T h5
	CreateTime   field.Time    // 创建时间
	UpdateTime   field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsGameDailyStat) Table(newTableName string) *xAdsGameDailyStat {
	x.xAdsGameDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsGameDailyStat) As(alias string) *xAdsGameDailyStat {
	x.xAdsGameDailyStatDo.DO = *(x.xAdsGameDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsGameDailyStat) updateTableName(table string) *xAdsGameDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.StatDate = field.NewTime(table, "stat_date")
	x.GameTag = field.NewString(table, "game_tag")
	x.GameName = field.NewString(table, "game_name")
	x.GameCompany = field.NewString(table, "game_company")
	x.BetCountUPc = field.NewInt32(table, "bet_count_u_pc")
	x.BetCountUH5 = field.NewInt32(table, "bet_count_u_h5")
	x.BetCountTPc = field.NewInt32(table, "bet_count_t_pc")
	x.BetCountTH5 = field.NewInt32(table, "bet_count_t_h5")
	x.BetAmountUPc = field.NewFloat32(table, "bet_amount_u_pc")
	x.BetAmountUH5 = field.NewFloat32(table, "bet_amount_u_h5")
	x.BetAmountTPc = field.NewFloat32(table, "bet_amount_t_pc")
	x.BetAmountTH5 = field.NewFloat32(table, "bet_amount_t_h5")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsGameDailyStat) WithContext(ctx context.Context) *xAdsGameDailyStatDo {
	return x.xAdsGameDailyStatDo.WithContext(ctx)
}

func (x xAdsGameDailyStat) TableName() string { return x.xAdsGameDailyStatDo.TableName() }

func (x xAdsGameDailyStat) Alias() string { return x.xAdsGameDailyStatDo.Alias() }

func (x xAdsGameDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsGameDailyStatDo.Columns(cols...)
}

func (x *xAdsGameDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsGameDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 18)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["game_tag"] = x.GameTag
	x.fieldMap["game_name"] = x.GameName
	x.fieldMap["game_company"] = x.GameCompany
	x.fieldMap["bet_count_u_pc"] = x.BetCountUPc
	x.fieldMap["bet_count_u_h5"] = x.BetCountUH5
	x.fieldMap["bet_count_t_pc"] = x.BetCountTPc
	x.fieldMap["bet_count_t_h5"] = x.BetCountTH5
	x.fieldMap["bet_amount_u_pc"] = x.BetAmountUPc
	x.fieldMap["bet_amount_u_h5"] = x.BetAmountUH5
	x.fieldMap["bet_amount_t_pc"] = x.BetAmountTPc
	x.fieldMap["bet_amount_t_h5"] = x.BetAmountTH5
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsGameDailyStat) clone(db *gorm.DB) xAdsGameDailyStat {
	x.xAdsGameDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsGameDailyStat) replaceDB(db *gorm.DB) xAdsGameDailyStat {
	x.xAdsGameDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsGameDailyStatDo struct{ gen.DO }

func (x xAdsGameDailyStatDo) Debug() *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsGameDailyStatDo) WithContext(ctx context.Context) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsGameDailyStatDo) ReadDB() *xAdsGameDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsGameDailyStatDo) WriteDB() *xAdsGameDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsGameDailyStatDo) Session(config *gorm.Session) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsGameDailyStatDo) Clauses(conds ...clause.Expression) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsGameDailyStatDo) Returning(value interface{}, columns ...string) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsGameDailyStatDo) Not(conds ...gen.Condition) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsGameDailyStatDo) Or(conds ...gen.Condition) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsGameDailyStatDo) Select(conds ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsGameDailyStatDo) Where(conds ...gen.Condition) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsGameDailyStatDo) Order(conds ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsGameDailyStatDo) Distinct(cols ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsGameDailyStatDo) Omit(cols ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsGameDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsGameDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsGameDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsGameDailyStatDo) Group(cols ...field.Expr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsGameDailyStatDo) Having(conds ...gen.Condition) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsGameDailyStatDo) Limit(limit int) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsGameDailyStatDo) Offset(offset int) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsGameDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsGameDailyStatDo) Unscoped() *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsGameDailyStatDo) Create(values ...*model.XAdsGameDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsGameDailyStatDo) CreateInBatches(values []*model.XAdsGameDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsGameDailyStatDo) Save(values ...*model.XAdsGameDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsGameDailyStatDo) First() (*model.XAdsGameDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsGameDailyStat), nil
	}
}

func (x xAdsGameDailyStatDo) Take() (*model.XAdsGameDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsGameDailyStat), nil
	}
}

func (x xAdsGameDailyStatDo) Last() (*model.XAdsGameDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsGameDailyStat), nil
	}
}

func (x xAdsGameDailyStatDo) Find() ([]*model.XAdsGameDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsGameDailyStat), err
}

func (x xAdsGameDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsGameDailyStat, err error) {
	buf := make([]*model.XAdsGameDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsGameDailyStatDo) FindInBatches(result *[]*model.XAdsGameDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsGameDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsGameDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsGameDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsGameDailyStatDo) Joins(fields ...field.RelationField) *xAdsGameDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsGameDailyStatDo) Preload(fields ...field.RelationField) *xAdsGameDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsGameDailyStatDo) FirstOrInit() (*model.XAdsGameDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsGameDailyStat), nil
	}
}

func (x xAdsGameDailyStatDo) FirstOrCreate() (*model.XAdsGameDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsGameDailyStat), nil
	}
}

func (x xAdsGameDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsGameDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsGameDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsGameDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsGameDailyStatDo) Delete(models ...*model.XAdsGameDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsGameDailyStatDo) withDO(do gen.Dao) *xAdsGameDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
