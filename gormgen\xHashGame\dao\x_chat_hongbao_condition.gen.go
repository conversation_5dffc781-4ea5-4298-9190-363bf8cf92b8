// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXChatHongbaoCondition(db *gorm.DB, opts ...gen.DOOption) xChatHongbaoCondition {
	_xChatHongbaoCondition := xChatHongbaoCondition{}

	_xChatHongbaoCondition.xChatHongbaoConditionDo.UseDB(db, opts...)
	_xChatHongbaoCondition.xChatHongbaoConditionDo.UseModel(&model.XChatHongbaoCondition{})

	tableName := _xChatHongbaoCondition.xChatHongbaoConditionDo.TableName()
	_xChatHongbaoCondition.ALL = field.NewAsterisk(tableName)
	_xChatHongbaoCondition.ID = field.NewInt32(tableName, "Id")
	_xChatHongbaoCondition.SellerID = field.NewInt32(tableName, "SellerId")
	_xChatHongbaoCondition.DayMax = field.NewInt32(tableName, "DayMax")
	_xChatHongbaoCondition.OnlineTime = field.NewInt32(tableName, "OnlineTime")
	_xChatHongbaoCondition.RechargeNum = field.NewFloat64(tableName, "RechargeNum")
	_xChatHongbaoCondition.MemberRechargeNum = field.NewFloat64(tableName, "MemberRechargeNum")
	_xChatHongbaoCondition.MemberSum = field.NewInt32(tableName, "MemberSum")

	_xChatHongbaoCondition.fillFieldMap()

	return _xChatHongbaoCondition
}

type xChatHongbaoCondition struct {
	xChatHongbaoConditionDo xChatHongbaoConditionDo

	ALL               field.Asterisk
	ID                field.Int32
	SellerID          field.Int32
	DayMax            field.Int32
	OnlineTime        field.Int32 // minute
	RechargeNum       field.Float64
	MemberRechargeNum field.Float64
	MemberSum         field.Int32

	fieldMap map[string]field.Expr
}

func (x xChatHongbaoCondition) Table(newTableName string) *xChatHongbaoCondition {
	x.xChatHongbaoConditionDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xChatHongbaoCondition) As(alias string) *xChatHongbaoCondition {
	x.xChatHongbaoConditionDo.DO = *(x.xChatHongbaoConditionDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xChatHongbaoCondition) updateTableName(table string) *xChatHongbaoCondition {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.DayMax = field.NewInt32(table, "DayMax")
	x.OnlineTime = field.NewInt32(table, "OnlineTime")
	x.RechargeNum = field.NewFloat64(table, "RechargeNum")
	x.MemberRechargeNum = field.NewFloat64(table, "MemberRechargeNum")
	x.MemberSum = field.NewInt32(table, "MemberSum")

	x.fillFieldMap()

	return x
}

func (x *xChatHongbaoCondition) WithContext(ctx context.Context) *xChatHongbaoConditionDo {
	return x.xChatHongbaoConditionDo.WithContext(ctx)
}

func (x xChatHongbaoCondition) TableName() string { return x.xChatHongbaoConditionDo.TableName() }

func (x xChatHongbaoCondition) Alias() string { return x.xChatHongbaoConditionDo.Alias() }

func (x xChatHongbaoCondition) Columns(cols ...field.Expr) gen.Columns {
	return x.xChatHongbaoConditionDo.Columns(cols...)
}

func (x *xChatHongbaoCondition) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xChatHongbaoCondition) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 7)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["DayMax"] = x.DayMax
	x.fieldMap["OnlineTime"] = x.OnlineTime
	x.fieldMap["RechargeNum"] = x.RechargeNum
	x.fieldMap["MemberRechargeNum"] = x.MemberRechargeNum
	x.fieldMap["MemberSum"] = x.MemberSum
}

func (x xChatHongbaoCondition) clone(db *gorm.DB) xChatHongbaoCondition {
	x.xChatHongbaoConditionDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xChatHongbaoCondition) replaceDB(db *gorm.DB) xChatHongbaoCondition {
	x.xChatHongbaoConditionDo.ReplaceDB(db)
	return x
}

type xChatHongbaoConditionDo struct{ gen.DO }

func (x xChatHongbaoConditionDo) Debug() *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Debug())
}

func (x xChatHongbaoConditionDo) WithContext(ctx context.Context) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xChatHongbaoConditionDo) ReadDB() *xChatHongbaoConditionDo {
	return x.Clauses(dbresolver.Read)
}

func (x xChatHongbaoConditionDo) WriteDB() *xChatHongbaoConditionDo {
	return x.Clauses(dbresolver.Write)
}

func (x xChatHongbaoConditionDo) Session(config *gorm.Session) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Session(config))
}

func (x xChatHongbaoConditionDo) Clauses(conds ...clause.Expression) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xChatHongbaoConditionDo) Returning(value interface{}, columns ...string) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xChatHongbaoConditionDo) Not(conds ...gen.Condition) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xChatHongbaoConditionDo) Or(conds ...gen.Condition) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xChatHongbaoConditionDo) Select(conds ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xChatHongbaoConditionDo) Where(conds ...gen.Condition) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xChatHongbaoConditionDo) Order(conds ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xChatHongbaoConditionDo) Distinct(cols ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xChatHongbaoConditionDo) Omit(cols ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xChatHongbaoConditionDo) Join(table schema.Tabler, on ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xChatHongbaoConditionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xChatHongbaoConditionDo) RightJoin(table schema.Tabler, on ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xChatHongbaoConditionDo) Group(cols ...field.Expr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xChatHongbaoConditionDo) Having(conds ...gen.Condition) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xChatHongbaoConditionDo) Limit(limit int) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xChatHongbaoConditionDo) Offset(offset int) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xChatHongbaoConditionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xChatHongbaoConditionDo) Unscoped() *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xChatHongbaoConditionDo) Create(values ...*model.XChatHongbaoCondition) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xChatHongbaoConditionDo) CreateInBatches(values []*model.XChatHongbaoCondition, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xChatHongbaoConditionDo) Save(values ...*model.XChatHongbaoCondition) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xChatHongbaoConditionDo) First() (*model.XChatHongbaoCondition, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChatHongbaoCondition), nil
	}
}

func (x xChatHongbaoConditionDo) Take() (*model.XChatHongbaoCondition, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChatHongbaoCondition), nil
	}
}

func (x xChatHongbaoConditionDo) Last() (*model.XChatHongbaoCondition, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChatHongbaoCondition), nil
	}
}

func (x xChatHongbaoConditionDo) Find() ([]*model.XChatHongbaoCondition, error) {
	result, err := x.DO.Find()
	return result.([]*model.XChatHongbaoCondition), err
}

func (x xChatHongbaoConditionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XChatHongbaoCondition, err error) {
	buf := make([]*model.XChatHongbaoCondition, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xChatHongbaoConditionDo) FindInBatches(result *[]*model.XChatHongbaoCondition, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xChatHongbaoConditionDo) Attrs(attrs ...field.AssignExpr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xChatHongbaoConditionDo) Assign(attrs ...field.AssignExpr) *xChatHongbaoConditionDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xChatHongbaoConditionDo) Joins(fields ...field.RelationField) *xChatHongbaoConditionDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xChatHongbaoConditionDo) Preload(fields ...field.RelationField) *xChatHongbaoConditionDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xChatHongbaoConditionDo) FirstOrInit() (*model.XChatHongbaoCondition, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChatHongbaoCondition), nil
	}
}

func (x xChatHongbaoConditionDo) FirstOrCreate() (*model.XChatHongbaoCondition, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChatHongbaoCondition), nil
	}
}

func (x xChatHongbaoConditionDo) FindByPage(offset int, limit int) (result []*model.XChatHongbaoCondition, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xChatHongbaoConditionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xChatHongbaoConditionDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xChatHongbaoConditionDo) Delete(models ...*model.XChatHongbaoCondition) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xChatHongbaoConditionDo) withDO(do gen.Dao) *xChatHongbaoConditionDo {
	x.DO = *do.(*gen.DO)
	return x
}
