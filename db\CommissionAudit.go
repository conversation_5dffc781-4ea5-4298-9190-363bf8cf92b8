package db

import (
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

type CommissionAudit struct {
	Id           int     `gorm:"column:Id"`           //id
	UserId       int     `gorm:"column:UserId"`       //代理id
	SellerId     int     `gorm:"column:SellerId"`     //运营商
	State        int     `gorm:"column:State"`        //状态 1待审核 2 审核拒绝 3 审核通过 4已发放
	Symbol       string  `gorm:"column:Symbol"`       //币种
	Amount       float64 `gorm:"column:Amount"`       //申请金额
	CreateTime   string  `gorm:"column:CreateTime"`   //申请时间
	Memo         string  `gorm:"column:Memo"`         //备注
	AuditAccount string  `gorm:"column:AuditAccount"` //审核人
	AuditTime    string  `gorm:"column:AuditTime"`    //审核时间
	SendAccount  string  `gorm:"column:SendAccount"`  //发放人
	SendTime     string  `gorm:"column:SendTime"`     //发放时间
	Address      string  `gorm:"column:Address"`      //发放时间
	ChannelId    int     `gorm:"column:ChannelId"`
	StartDate    string  `gorm:"column:StartDate"`
	EndDate      string  `gorm:"column:EndDate"`
	AgentMode    int     `gorm:"column:AgentMode"`
}

type CommissionAuditEx struct {
	Id           int     `gorm:"column:Id"`           //id
	UserId       int     `gorm:"column:UserId"`       //代理id
	SellerId     int     `gorm:"column:SellerId"`     //运营商
	State        int     `gorm:"column:State"`        //状态 1待审核 2 审核拒绝 3 审核通过 4已发放
	AmountUsdt   float64 `gorm:"column:AmountUsdt"`   //申请金额
	AmountTrx    float64 `gorm:"column:AmountTrx"`    //申请金额
	CreateTime   string  `gorm:"column:CreateTime"`   //申请时间
	Memo         string  `gorm:"column:Memo"`         //备注
	AuditAccount string  `gorm:"column:AuditAccount"` //审核人
	AuditTime    string  `gorm:"column:AuditTime"`    //审核时间
	SendAccount  string  `gorm:"column:SendAccount"`  //发放人
	SendTime     string  `gorm:"column:SendTime"`     //发放时间
	Address      string  `gorm:"column:Address"`      //发放时间
	ChannelId    int     `gorm:"column:ChannelId"`
	FinalAmount  float64 `gorm:"column:FinalAmount"`
	TrxPrice     float64 `gorm:"column:TrxPrice"`
	StartDate    string  `gorm:"column:StartDate"`
	EndDate      string  `gorm:"column:EndDate"`
}

func (*CommissionAudit) TableName() string {
	return "x_commission_audit"
}

func CommissionAudit_Page_Data(Page int, PageSize int, Id int, SellerId int, UserId int, Symbol string, State int, StartTime int64, EndTime int64, ChannelId int, Address string, AgentMode int) (int, []CommissionAudit, float64) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "x_commission_audit.Id desc"
	PageKey := "x_commission_audit.Id"
	data := CommissionAudit{}
	dbtable := server.Db().Gorm().Table(data.TableName())
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.Id", "=", Id, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.UserId", "=", UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.Symbol", "=", Symbol, "")
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.State", "=", State, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.ChannelId", "=", ChannelId, 0)
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.Address", "=", Address, "")
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.AgentMode", "=", AgentMode, 0)

	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	if EndTime > 0 {
		EndTime += +86400000
	}
	server.Db().AddWhere(&sql, &params, "and", "x_commission_audit.CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	type DataCount struct {
		Total       int     `gorm:"column:Total"`
		TotalAmount float64 `gorm:"column:TotalAmount"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []CommissionAudit{}, 0
	}
	dbtable.Select("IFNULL(sum(Amount),0) as TotalAmount").Where(sql, params...).Scan(&dt)
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).
		Order(OrderBy).
		Where(sql, params...).
		Limit(1).Offset((Page - 1) * PageSize).
		Scan(&md)
	result := []CommissionAudit{}
	dbtable.
		Select("x_commission_audit.*").
		Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).
		Where(sql, params...).
		Limit(PageSize).Order(OrderBy).
		Find(&result)
	return dt.Total, result, dt.TotalAmount
}

func CommissionAudit_Page_DataEx(Page int, PageSize int, Id int, SellerId int, UserId int, State int, StartTime int64, EndTime int64, ChannelId int, Address string) (int, []CommissionAuditEx, float64, float64, float64) {
	if Page == 0 {
		Page = 1
	}
	if PageSize == 0 {
		PageSize = 10
	}
	OrderBy := "id desc"
	PageKey := "Id"
	dbtable := server.Db().Gorm().Table("x_commission_audit_t1")
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "Id", "=", Id, 0)
	server.Db().AddWhere(&sql, &params, "and", "SellerId", "=", SellerId, 0)
	server.Db().AddWhere(&sql, &params, "and", "UserId", "=", UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", "State", "=", State, 0)
	server.Db().AddWhere(&sql, &params, "and", "ChannelId", "=", ChannelId, 0)
	server.Db().AddWhere(&sql, &params, "and", "Address", "=", Address, "")

	server.Db().AddWhere(&sql, &params, "and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
	if EndTime > 0 {
		EndTime += +86400000
	}
	server.Db().AddWhere(&sql, &params, "and", "CreateTime", "<", abugo.TimeStampToLocalTime(EndTime), "")
	type DataCount struct {
		Total       int     `gorm:"column:Total"`
		FinalAmount float64 `gorm:"column:FinalAmount"`
		AmountTrx   float64 `gorm:"column:AmountTrx"`
		AmountUsdt  float64 `gorm:"column:AmountUsdt"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		return dt.Total, []CommissionAuditEx{}, 0, 0, 0
	}
	sel := "IFNULL(sum(FinalAmount),0) as FinalAmount,IFNULL(sum(AmountTrx),0) as AmountTrx,IFNULL(sum(AmountUsdt),0) as AmountUsdt"
	dbtable.Select(sel).Where(sql, params...).Scan(&dt)
	type MinData struct {
		MinValue int `gorm:"column:MinValue"`
	}
	md := MinData{}
	dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((Page - 1) * PageSize).Scan(&md)
	result := []CommissionAuditEx{}
	dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(PageSize).Order(OrderBy).Find(&result)
	return dt.Total, result, dt.FinalAmount, dt.AmountTrx, dt.AmountUsdt
}
