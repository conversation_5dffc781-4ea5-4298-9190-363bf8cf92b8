// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgRedpacketLog(db *gorm.DB, opts ...gen.DOOption) xTgRedpacketLog {
	_xTgRedpacketLog := xTgRedpacketLog{}

	_xTgRedpacketLog.xTgRedpacketLogDo.UseDB(db, opts...)
	_xTgRedpacketLog.xTgRedpacketLogDo.UseModel(&model.XTgRedpacketLog{})

	tableName := _xTgRedpacketLog.xTgRedpacketLogDo.TableName()
	_xTgRedpacketLog.ALL = field.NewAsterisk(tableName)
	_xTgRedpacketLog.ID = field.NewInt32(tableName, "Id")
	_xTgRedpacketLog.UserID = field.NewInt64(tableName, "UserId")
	_xTgRedpacketLog.TgID = field.NewInt32(tableName, "TgId")
	_xTgRedpacketLog.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRedpacketLog.TgName = field.NewString(tableName, "TgName")
	_xTgRedpacketLog.RedPacketID = field.NewInt32(tableName, "RedPacketId")
	_xTgRedpacketLog.Amount = field.NewFloat64(tableName, "Amount")
	_xTgRedpacketLog.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgRedpacketLog.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTgRedpacketLog.fillFieldMap()

	return _xTgRedpacketLog
}

type xTgRedpacketLog struct {
	xTgRedpacketLogDo xTgRedpacketLogDo

	ALL         field.Asterisk
	ID          field.Int32
	UserID      field.Int64
	TgID        field.Int32 // 后台TgId
	SellerID    field.Int32 // 运营商id
	TgName      field.String
	RedPacketID field.Int32
	Amount      field.Float64
	CreateTime  field.Time
	UpdateTime  field.Time

	fieldMap map[string]field.Expr
}

func (x xTgRedpacketLog) Table(newTableName string) *xTgRedpacketLog {
	x.xTgRedpacketLogDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRedpacketLog) As(alias string) *xTgRedpacketLog {
	x.xTgRedpacketLogDo.DO = *(x.xTgRedpacketLogDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRedpacketLog) updateTableName(table string) *xTgRedpacketLog {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt64(table, "UserId")
	x.TgID = field.NewInt32(table, "TgId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.TgName = field.NewString(table, "TgName")
	x.RedPacketID = field.NewInt32(table, "RedPacketId")
	x.Amount = field.NewFloat64(table, "Amount")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTgRedpacketLog) WithContext(ctx context.Context) *xTgRedpacketLogDo {
	return x.xTgRedpacketLogDo.WithContext(ctx)
}

func (x xTgRedpacketLog) TableName() string { return x.xTgRedpacketLogDo.TableName() }

func (x xTgRedpacketLog) Alias() string { return x.xTgRedpacketLogDo.Alias() }

func (x xTgRedpacketLog) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRedpacketLogDo.Columns(cols...)
}

func (x *xTgRedpacketLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRedpacketLog) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["TgId"] = x.TgID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["TgName"] = x.TgName
	x.fieldMap["RedPacketId"] = x.RedPacketID
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTgRedpacketLog) clone(db *gorm.DB) xTgRedpacketLog {
	x.xTgRedpacketLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRedpacketLog) replaceDB(db *gorm.DB) xTgRedpacketLog {
	x.xTgRedpacketLogDo.ReplaceDB(db)
	return x
}

type xTgRedpacketLogDo struct{ gen.DO }

func (x xTgRedpacketLogDo) Debug() *xTgRedpacketLogDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRedpacketLogDo) WithContext(ctx context.Context) *xTgRedpacketLogDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRedpacketLogDo) ReadDB() *xTgRedpacketLogDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRedpacketLogDo) WriteDB() *xTgRedpacketLogDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRedpacketLogDo) Session(config *gorm.Session) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRedpacketLogDo) Clauses(conds ...clause.Expression) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRedpacketLogDo) Returning(value interface{}, columns ...string) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRedpacketLogDo) Not(conds ...gen.Condition) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRedpacketLogDo) Or(conds ...gen.Condition) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRedpacketLogDo) Select(conds ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRedpacketLogDo) Where(conds ...gen.Condition) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRedpacketLogDo) Order(conds ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRedpacketLogDo) Distinct(cols ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRedpacketLogDo) Omit(cols ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRedpacketLogDo) Join(table schema.Tabler, on ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRedpacketLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRedpacketLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRedpacketLogDo) Group(cols ...field.Expr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRedpacketLogDo) Having(conds ...gen.Condition) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRedpacketLogDo) Limit(limit int) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRedpacketLogDo) Offset(offset int) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRedpacketLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRedpacketLogDo) Unscoped() *xTgRedpacketLogDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRedpacketLogDo) Create(values ...*model.XTgRedpacketLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRedpacketLogDo) CreateInBatches(values []*model.XTgRedpacketLog, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRedpacketLogDo) Save(values ...*model.XTgRedpacketLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRedpacketLogDo) First() (*model.XTgRedpacketLog, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketLog), nil
	}
}

func (x xTgRedpacketLogDo) Take() (*model.XTgRedpacketLog, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketLog), nil
	}
}

func (x xTgRedpacketLogDo) Last() (*model.XTgRedpacketLog, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketLog), nil
	}
}

func (x xTgRedpacketLogDo) Find() ([]*model.XTgRedpacketLog, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRedpacketLog), err
}

func (x xTgRedpacketLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRedpacketLog, err error) {
	buf := make([]*model.XTgRedpacketLog, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRedpacketLogDo) FindInBatches(result *[]*model.XTgRedpacketLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRedpacketLogDo) Attrs(attrs ...field.AssignExpr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRedpacketLogDo) Assign(attrs ...field.AssignExpr) *xTgRedpacketLogDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRedpacketLogDo) Joins(fields ...field.RelationField) *xTgRedpacketLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRedpacketLogDo) Preload(fields ...field.RelationField) *xTgRedpacketLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRedpacketLogDo) FirstOrInit() (*model.XTgRedpacketLog, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketLog), nil
	}
}

func (x xTgRedpacketLogDo) FirstOrCreate() (*model.XTgRedpacketLog, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRedpacketLog), nil
	}
}

func (x xTgRedpacketLogDo) FindByPage(offset int, limit int) (result []*model.XTgRedpacketLog, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRedpacketLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRedpacketLogDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRedpacketLogDo) Delete(models ...*model.XTgRedpacketLog) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRedpacketLogDo) withDO(do gen.Dao) *xTgRedpacketLogDo {
	x.DO = *do.(*gen.DO)
	return x
}
