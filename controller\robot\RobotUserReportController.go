package robot

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"log"
	"path/filepath"
	"runtime/debug"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	//"github.com/beego/beego/logs"
	//"github.com/go-resty/resty/v2"
	//"github.com/spf13/viper"
)

// 机器人报表

var fieldMapping = map[string]string{
	"日期":                                          "query_time",
	"运营商":                                        "seller_name",
	"渠道":                                          "channel_name",
	"机器人类型":                                    "robot_type_name",
	"机器人UserName":                                "name",
	"启动机器人人数":                                "run_cnt",
	"在库启动机器人人数":                            "in_db_run_cnt",
	"绑定钱包地址人数":                              "banding_trx_cnt",
	"领取TRX彩金人数":                               "gift_trx_cnt",
	"领取TRX彩金转账投注人数":                       "gift_trx_bet_cnt",
	"领取TRX彩金转账5次以上投注人数":                "gift_trx_bet_cnt_5th",
	"不在库领取USDT彩金人数":                        "not_in_db_gift_usdt_cnt",
	"不在库领取USDT彩金投注人数":                    "not_in_db_gift_usdt_bet_cnt",
	"不在库领取USDT彩金投注5次以上人数":             "not_in_db_gift_usdt_bet_cnt_5th",
	"在库地址不符合领取USDT彩金人数":                "in_db_gift_usdt_cnt",
	"在库地址不符合领取USDT彩金投注人数":            "in_db_gift_usdt_bet_cnt",
	"在库地址不符合领取USDT彩金投注5次以上人数":     "in_db_gift_usdt_bet_cnt_5th",
	"在库完成5局TRX转账领取USDT彩金人数":            "in_db_mission_gift_usdt_cnt",
	"在库完成5局TRX转账领取USDT彩金投注人数":        "in_db_mission_gift_usdt_bet_cnt",
	"在库完成5局TRX转账领取USDT彩金投注5次以上人数": "in_db_mission_gift_usdt_bet_cnt_5th",
	"新关注/加入群组":                               "join_group_cnt",
	"邀请1人人数":                                   "invite_cnt_1th",
	"邀请2人人数":                                   "invite_cnt_2th",
	"邀请3人人数":                                   "invite_cnt_3th",
	"在库充值人数":                                  "in_db_first_recharge_cnt",
	"不在库充值人数":                                "not_in_db_first_recharge_cnt",
}

// 设置表头
var header = []string{"日期", "运营商", "渠道", "机器人类型", "机器人UserName", "启动机器人人数", "在库启动机器人人数", "绑定钱包地址人数",
	"领取TRX彩金人数", "领取TRX彩金转账投注人数", "领取TRX彩金转账5次以上投注人数",
	"不在库领取USDT彩金人数", "不在库领取USDT彩金投注人数", "不在库领取USDT彩金投注5次以上人数",
	"在库地址不符合领取USDT彩金人数", "在库地址不符合领取USDT彩金投注人数", "在库地址不符合领取USDT彩金投注5次以上人数",
	"在库完成5局TRX转账领取USDT彩金人数", "在库完成5局TRX转账领取USDT彩金投注人数", "在库完成5局TRX转账领取USDT彩金投注5次以上人数",
	"点击分享次数", "新关注/加入群组", "邀请1人人数", "邀请2人人数", "邀请3人人数",
	"在库充值人数", "不在库充值人数",
}

// reportList 获取TG接待机器人列表
func (c *Router) reportList(ctx *abugo.AbuHttpContent) {
	errCode := 0

	type RequestData struct {
		Page      int    `json:"page"`
		PageSize  int    `json:"page_size"`
		StartTime int64  `json:"start_time"`
		EndTime   int64  `json:"end_time"`
		SellerID  []int  `json:"seller_id"`
		ChannelID []int  `json:"channel_id"`
		Name      string `json:"name"`
		RobotType int    `json:"robot_type"`
		IsExport  int    `json:"is_export"` // 是否导出 默认0 不导出
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "接待机器人数据统计", "查", "查询接待机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	// 创建字段映射关系：Excel表头 -> 数据库字段
	// 创建字段映射关系：Excel表头 -> 数据库字段

	type Result struct {
		model.XRobotStatisticsDay

		StartTime               string `json:"start_time"`
		EndTime                 string `json:"end_time"`
		SellerName              string `json:"seller_name"`
		ChannelName             string `json:"channel_name"`
		RunCnt                  int    `json:"run_cnt"`
		InDBRunCnt              int    `json:"in_db_run_cnt"`
		BandingTrxCnt           int    `json:"banding_trx_cnt"`
		GiftTrxCnt              int    `json:"gift_trx_cnt"`
		GiftTrxBetCnt5th        int    `json:"gift_trx_bet_cnt5th"`
		JoinGroupCnt            int    `json:"join_group_cnt"`
		InviteCnt1th            int    `json:"invite_cnt1th"`
		InviteCnt2th            int    `json:"invite_cnt2th"`
		InviteCnt3th            int    `json:"invite_cnt3th"`
		GiftCnt                 int    `json:"gift_cnt"`
		GiftBetCnt              int    `json:"gift_bet_cnt"`
		InDBRechargeCnt         int    `json:"in_db_recharge_cnt"`
		InDBFirstRechargeCnt    int    `json:"in_db_first_recharge_cnt"`
		NotInDBFirstRechargeCnt int    `json:"not_in_db_first_recharge_cnt"`

		// 返回报表结构
	}
	//var results []*Result
	where := abugo.AbuDbWhere{}
	groupSQL := "GROUP BY name , robot_type ,channel_id , seller_id "
	where.Add("and", "1", "=", "1", nil)

	if len(req.SellerID) > 0 {
		//req.SellerIDs = req.SellerIDs[0 : len(req.SellerIDs)-1]
		//str := fmt.Sprint(req.SellerIDs)
		where.Add("and", "seller_id", "in", SliceToSQLTuple(req.SellerID), nil)
	}
	if len(req.ChannelID) > 0 {
		//req.ChannelIDs = req.ChannelIDs[0 : len(req.ChannelIDs)-1]
		where.Add("and", "channel_id", "in", SliceToSQLTuple(req.ChannelID), nil)
	}
	if req.RobotType > 0 {
		where.Add("and", "robot_type", "=", req.RobotType, nil)
	}
	if req.Name != "" {
		where.Add("and", "name", "=", req.Name, nil)
		groupSQL += ", name "
	}
	tm1, tm2 := "", ""
	if req.StartTime > 0 && req.EndTime > 0 {
		where.Add("and", "statistic_date", ">=", abugo.TimeStampToLocalTime(req.StartTime), nil)
		where.Add("and", "statistic_date", "<=", abugo.TimeStampToLocalTime(req.EndTime), nil)
		tm1 = abugo.TimeStampToLocalTime(req.StartTime)
		tm2 = abugo.TimeStampToLocalTime(req.EndTime)

	}

	whereSQL, whereData := where.Sql()
	sql := fmt.Sprintf(`
	SELECT
	"%s" start_time , "%s" end_time ,
	concat("%s","/","%s")  query_time,
	statistic_date, 
	seller_id ,
	(SELECT sellername FROM x_hash_game.x_seller WHERE sellerid= seller_id) seller_name,
	channel_id,
	(SELECT channelname FROM x_hash_game.x_channel WHERE channelid= channel_id) channel_name,
	name, 
	robot_type, 
	(CASE WHEN robot_type=1 THEN "接待机器人"  ELSE "其他" END ) robot_type_name,
	COUNT(distinct  user_id ) run_cnt ,
	SUM(CASE WHEN is_in_resource_db=1 THEN 1 ELSE 0 END ) in_db_run_cnt,
	SUM(CASE WHEN banding_trx_address<>"" THEN 1 ELSE 0 END ) banding_trx_cnt,
	SUM(CASE WHEN gift_trx_status=2 THEN 1 ELSE 0 END ) gift_trx_cnt,
	SUM(CASE WHEN is_gift_trx>0 AND transfer_bet_count >0 THEN 1 ELSE 0 END ) gift_trx_bet_cnt , 
	SUM(CASE WHEN is_gift_trx>0 AND transfer_bet_count>=5 THEN 1 ELSE 0 END ) gift_trx_bet_cnt_5th , 
	
	SUM(CASE WHEN  is_in_resource_db=1 AND  gift_usdt_status =2 AND   is_gift_trx =0  THEN 1 ELSE 0 END )   in_db_gift_usdt_cnt  ,  
	SUM(CASE WHEN  is_in_resource_db=1 AND  is_gift_usdt >0 AND   is_gift_trx =0  AND bet_count>0  THEN 1 ELSE 0 END )   in_db_gift_usdt_bet_cnt  ,  
	SUM(CASE WHEN  is_in_resource_db=1 AND  is_gift_usdt >0 AND   is_gift_trx =0  AND bet_count>=5  THEN 1 ELSE 0 END )   in_db_gift_usdt_bet_cnt_5th  ,  
	
	SUM(CASE WHEN  is_in_resource_db=1 AND  mission_gift_usdt >0  THEN 1 ELSE 0 END )    in_db_mission_gift_usdt_cnt  ,
	SUM(CASE WHEN  is_in_resource_db=1 AND  mission_gift_usdt >0 AND bet_count>0  THEN 1 ELSE 0 END )    in_db_mission_gift_usdt_bet_cnt  ,
	SUM(CASE WHEN  is_in_resource_db=1 AND  mission_gift_usdt >0 AND bet_count>=5  THEN 1 ELSE 0 END )   in_db_mission_gift_usdt_bet_cnt_5th  ,

	SUM(CASE WHEN  is_in_resource_db=0 AND  gift_usdt_status =2  THEN 1 ELSE 0 END )    not_in_db_gift_usdt_cnt  ,
	SUM(CASE WHEN  is_in_resource_db=0 AND  is_gift_usdt >0 AND bet_count>0  THEN 1 ELSE 0 END )    not_in_db_gift_usdt_bet_cnt  ,
	SUM(CASE WHEN  is_in_resource_db=0 AND  is_gift_usdt >0 AND bet_count>=5  THEN 1 ELSE 0 END )   not_in_db_gift_usdt_bet_cnt_5th  ,
	
	SUM(CASE WHEN join_group_status>0   THEN 1 ELSE 0 END ) join_group_cnt , 
	SUM(shard_link_cnt ) shard_link_cnt ,
	SUM(CASE WHEN agent_count>=1 THEN 1 ELSE 0 END ) invite_cnt_1th , 
	SUM(CASE WHEN agent_count>=2 THEN 1 ELSE 0 END ) invite_cnt_2th , 
	SUM(CASE WHEN agent_count>=3 THEN 1 ELSE 0 END ) invite_cnt_3th , 
	
	SUM(CASE WHEN gift_usdt_status=2 OR gift_trx_status=2 THEN 1 ELSE 0 END ) gift_cnt ,  
	SUM(CASE WHEN (is_gift_usdt >0 OR is_gift_trx>0 ) AND bet_count>0  THEN 1 ELSE 0 END ) gift_bet_cnt ,  
	
	SUM(CASE WHEN  is_in_resource_db=1 AND  first_recharge_time >= STR_TO_DATE("%s", "%%Y-%%m-%%d")    THEN 1 ELSE 0 END )  in_db_first_recharge_cnt , 
	SUM(CASE WHEN  is_in_resource_db=0  AND first_recharge_time >= STR_TO_DATE("%s", "%%Y-%%m-%%d")   THEN 1 ELSE 0 END )  not_in_db_first_recharge_cnt  
	FROM  x_hash_game.x_robot_statistics_day 
	WHERE %s 
	   %s 
	  ORDER BY  statistic_date DESC 
  LIMIT %d OFFSET %d `, tm1, tm2, tm1, tm2, tm1, tm1, whereSQL, groupSQL, req.PageSize, (req.Page-1)*req.PageSize)

	results, err := server.Db().Query(sql, whereData)
	pTotal, err := server.Db().Query(fmt.Sprintf(`
	SELECT COUNT(1) AS total FROM (
	SELECT NAME FROM  x_hash_game.x_robot_statistics_day  where %s  %s  ) tmp 
`, whereSQL, groupSQL), whereData)

	if results == nil || pTotal == nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	total := abugo.GetInt64FromInterface((*pTotal)[0]["total"])

	if 1 == req.IsExport {
		filename, err := c.ExportExcel(results, header)
		if err != nil {
			ctx.RespErrString(true, &errCode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("total", total)
		ctx.Put("results", *results)
		ctx.RespOK()
	}
}

func (c *Router) reportListByRobotName(ctx *abugo.AbuHttpContent) {
	errCode := 0

	type RequestData struct {
		Page      int    `json:"page"`
		PageSize  int    `json:"page_size"`
		StartTime int64  `json:"start_time"`
		EndTime   int64  `json:"end_time"`
		Name      string `json:"name"`
		IsExport  int    `json:"is_export"` // 是否导出 默认0 不导出
	}
	req := RequestData{}
	results := make([]map[string]interface{}, 0)
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "接待机器人数据统计", "查", "查询接待机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	dao := server.DaoxHashGame().XRobotStatisticsDay
	query := dao.WithContext(nil).
		Select(dao.StatisticDate, dao.LoginTime,
			dao.UserID, dao.TgChatID, dao.TgUserName,
			dao.IsInResourceDb, dao.BandingTrxAddress)

	if req.Name != "" {
		query.Where(dao.Name.Eq(req.Name))
	}
	tm1 := time.Unix(0, req.StartTime*int64(time.Millisecond))
	tm2 := time.Unix(0, req.EndTime*int64(time.Millisecond))
	if !tm1.IsZero() && !tm2.IsZero() && req.StartTime != 0 && req.EndTime != 0 {
		query.Where(dao.CreateTime.Between(tm1, tm2))
	}
	total, err := query.WithContext(nil).
		Order(dao.CreateTime).
		ScanByPage(&results, offset, limit)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	if 1 == req.IsExport {
		var fileMap = map[string]string{
			"日期":                    "statistic_date",
			"登录日期":                "login_time",
			"玩家TG用户名":            "tg_user_name",
			"玩家TG账号":              "tg_chat_id",
			"玩家平台账号":            "user_id",
			"是否在库(0不在库/1在库)": "is_in_resource_db",
			"绑定地址":                "banding_trx_address",
		}
		// 设置表头
		var headerLine = []string{
			"日期", "登录日期", "玩家TG用户名", "玩家TG账号",
			"玩家平台账号", "是否在库(0不在库/1在库)", "绑定地址",
		}

		filename, err := c.exportExcel(&results, headerLine, fileMap)
		if err != nil {
			ctx.RespErrString(true, &errCode, "导出失败")
			return
		}
		ctx.Put("filename", fmt.Sprintf("/exports/%s", filename))
		ctx.RespOK()
		return
	} else {
		ctx.Put("total", total)
		ctx.Put("results", results)
		ctx.RespOK()
	}
}

type SQLTupleElem interface {
	~int | ~int32 | ~int64 | ~string
}

func SliceToSQLTuple[T SQLTupleElem](elems []T) string {
	strS := make([]string, len(elems))
	for i, v := range elems {
		switch val := any(v).(type) {
		case string:
			strS[i] = fmt.Sprintf("'%s'", strings.ReplaceAll(val, "'", "''"))
		default:
			strS[i] = fmt.Sprintf("%v", val)
		}
	}
	return fmt.Sprintf("(%s)", strings.Join(strS, ","))
}

// 导出Excel文件
func (c *Router) ExportExcel(list *[]map[string]interface{}, headerLine []string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("❌ 导出 Excel panic: %v\n%s", r, debug.Stack())
		}
	}()

	excel := excelize.NewFile()
	sheetName := "Sheet1"
	_ = excel.SetSheetName("Sheet1", sheetName)

	// 写入表头
	if err := excel.SetSheetRow(sheetName, "A1", &headerLine); err != nil {
		log.Println("❌ 设置表头失败:", err)
		return "", err
	}

	// 逐行写入数据（安全模式）
	for j, d := range *list {
		rowIndex := j + 2
		row := make([]interface{}, len(headerLine))
		for k, h := range headerLine {
			if dbField, ok := fieldMapping[h]; ok {
				val := d[dbField]
				if val == nil {
					row[k] = ""
				} else {
					row[k] = val
				}
			} else {
				row[k] = ""
			}
		}

		cell := fmt.Sprintf("A%d", rowIndex)
		if err := excel.SetSheetRow(sheetName, cell, &row); err != nil {
			log.Printf("❌ 写入第 %d 行失败: %v\n", rowIndex, err)
		}
	}

	// 保存文件
	filename := "export_report_robot_" + time.Now().Format("20060102150405") + ".xlsx"
	filePath := filepath.Join(server.ExportDir(), filename)

	if err := excel.SaveAs(filePath); err != nil {
		log.Println("❌ 保存 Excel 失败:", err)
		return "", err
	}

	log.Println("✅ Excel 导出成功:", filePath)
	return filename, nil
}
