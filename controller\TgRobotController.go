package controller

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"github.com/zhms/xgo/xgo"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

const TGROBOT_STAT_HOUR_LIST_OPTIONS = `[
{"field":"RecordTime","name":"时间"},
{"field":"RobotUserName","name":"机器人Username"},
{"field":"ChannelName","name":"渠道名称"},
{"field":"StartCount","name":"启动人数"},
{"field":"InDbStartCount","name":"在库启动人数"},
{"field":"NotInDbStartCount","name":"不在库启动人数"},
{"field":"RegCount","name":"注册成功人数"},
{"field":"GetUsdtCount","name":"领取USDT人数"},
{"field":"GetTrxCount","name":"领取TRX人数"},
{"field":"ActivationCount","name":"激活地址人数"}]`

const TGROBOT_STAT_DATE_LIST_OPTIONS = `[
{"field":"RecordDate","name":"时间"},
{"field":"RobotUserName","name":"机器人Username"},
{"field":"ChannelName","name":"渠道名称"},
{"field":"StartCount","name":"启动人数"},
{"field":"InDbStartCount","name":"在库启动人数"},
{"field":"NotInDbStartCount","name":"不在库启动人数"},
{"field":"RegCount","name":"注册成功人数"},
{"field":"GetUsdtCount","name":"领取USDT人数"},
{"field":"GetTrxCount","name":"领取TRX人数"},
{"field":"ActivationCount","name":"激活地址人数"},
{"field":"RegNoLoginCount","name":"注册成功未登录"},
{"field":"LoginNoGetUTCount","name":"进入网页未领取"},
{"field":"RechargeAndWithdrawCount","name":"注册当日充值并当日提款"},
{"field":"NoReChargeAndWithdrawCount","name":"注册当日未充值并当日提款"}]`

type TgRobotController struct{}

func (c *TgRobotController) Init() {
	group := server.Http().NewGroup("/api/tgRobot")
	{
		group.Post("/creat", c.creat)
		group.Post("/list", c.list)
		group.Post("/info", c.info)
		group.Post("/update", c.update)
		group.Post("/update_status", c.update_status)
		group.Post("/update_tiyanjin_status", c.update_tiyanjin_status)
		group.Post("/update_IsAotoTyj2", c.update_IsAotoTyj2)
		group.Post("/update_ip_restriction_status", c.update_ip_restriction_status)
		group.Post("/delete", c.delete)
		group.Post("/tyj_user_list", c.tyjUserList)
		group.Post("/kefu_tyj_report", c.kefuTyjReport)
	}
	groupStat := server.Http().NewGroup("/api/tgRobot/stat")
	{
		groupStat.Post("/hour_list", c.statHourList)
		groupStat.Post("/date_list", c.statDateList)
	}
}

func (c *TgRobotController) creat(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotGuide
		GoogleCode string
	}{}

	if reqdata.GameURL != "" {
		reqdata.GameURL = strings.TrimSpace(reqdata.GameURL)
	}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "增", "TG机器人创建")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	if reqdata.ChannelID == 0 {
		ctx.RespErr(errors.New("请选择渠道"), &errcode)
		return
	}
	domain := strings.TrimPrefix(strings.TrimSuffix(reqdata.GameURL, "/"), "https://")
	daoChan := server.DaoxHashGame().XChannelHost
	_, err := daoChan.WithContext(ctx.Gin()).Where(daoChan.Host.Eq(domain)).
		Where(daoChan.ChannelID.Eq(reqdata.ChannelID)).Where(daoChan.State.Eq(1)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErr(errors.New("请填写该渠道下的有效域名"), &errcode)
			return
		}
		ctx.RespErr(err, &errcode)
		return
	}
	_, err = tgbotapi.NewBotAPI(reqdata.TgRobotToken)
	if err != nil {
		ctx.RespErrString(true, &errcode, fmt.Sprintf("机器人token无效,请更换后再试. err:%s", err))
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	err = db.Create(&reqdata.XTgRobotGuide)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotController) list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerID    int32
		Page        int
		PageSize    int
		IsEnable    int32 // 1:启用 2:禁用
		TgRobotType int32 // 1:引客机器人 2:英文接待机器人
	}
	type Result struct {
		model.XTgRobotGuide
		ChannelName string
		SellerName  string
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "查", "TG机器人查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	if reqdata.IsEnable != 0 {
		db = db.Where(dao.IsEnable.Eq(reqdata.IsEnable))
	}
	if reqdata.SellerID > 0 {
		db = db.Where(dao.SellerID.Eq(reqdata.SellerID))
	}
	if reqdata.TgRobotType > 0 {
		db = db.Where(dao.TgRobotType.Eq(reqdata.TgRobotType))
	}
	var list []Result
	xChannel := server.DaoxHashGame().XChannel
	xSeller := server.DaoxHashGame().XSeller
	total, err := db.LeftJoin(xChannel, xChannel.ChannelID.EqCol(dao.ChannelID)).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		Select(dao.ALL, xChannel.ChannelName, xSeller.SellerName).ScanByPage(&list, offset, limit)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *TgRobotController) info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id       int64
		SellerID int32
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "查", "TG机器人查询")
	if token == nil {
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	xSeller := server.DaoxHashGame().XSeller
	xTgAccount := server.DaoxHashGame().XTgAccount
	admUser := server.DaoxHashGame().AdminUser
	var data struct {
		model.XTgRobotGuide
		SellerName  string
		KefuAccount string
		CSGroup     string
	}
	err := db.LeftJoin(xSeller, xSeller.SellerID.EqCol(dao.SellerID)).
		LeftJoin(xTgAccount, dao.TgAccountID.Neq(0), xTgAccount.ID.EqCol(dao.TgAccountID)).
		LeftJoin(admUser, dao.TgAccountID.Neq(0), admUser.Account.EqCol(xTgAccount.KefuAccount)).
		Where(dao.ID.Eq(reqdata.Id)).Select(dao.ALL, xSeller.SellerName, xTgAccount.KefuAccount, admUser.CSGroup).
		Scan(&data)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.Put("data", data)
	ctx.RespOK()
}

func (c *TgRobotController) update(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotGuide
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "改", "修改TG机器人")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}
	if reqdata.SellerID == 0 {
		ctx.RespErr(errors.New("请选择运营商"), &errcode)
		return
	}
	if reqdata.ChannelID == 0 {
		ctx.RespErr(errors.New("请选择渠道"), &errcode)
		return
	}
	domain := strings.TrimPrefix(strings.TrimSuffix(reqdata.GameURL, "/"), "https://")
	daoChan := server.DaoxHashGame().XChannelHost
	_, err := daoChan.WithContext(ctx.Gin()).Where(daoChan.Host.Eq(domain)).
		Where(daoChan.ChannelID.Eq(reqdata.ChannelID)).Where(daoChan.State.Eq(1)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErr(errors.New("请填写该渠道下的有效域名"), &errcode)
			return
		}
		ctx.RespErr(err, &errcode)
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	_, err = db.Where(dao.ID.Eq(reqdata.ID)).
		Omit(dao.ID, dao.SellerID, dao.ChannelID, dao.TgRobotUserName, dao.TgRobotToken, dao.TgAccountID).
		Updates(&reqdata.XTgRobotGuide)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotController) update_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotGuide
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "改", "修改TG机器人状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.IsEnable, reqdata.IsEnable)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotController) update_tiyanjin_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotGuide
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "改", "修改TG机器人体验金开启状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.IsTiyanjin, reqdata.IsTiyanjin)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotController) update_IsAotoTyj2(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotGuide
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "改", "修改是否自动发放体验金2状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.IsAotoTyj2, reqdata.IsAotoTyj2)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotController) update_ip_restriction_status(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotGuide
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "改", "修改TG机器人IP限制状态")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Update(dao.IsIPRestriction, reqdata.IsIPRestriction)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *TgRobotController) delete(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		model.XTgRobotGuide
		GoogleCode string
	}{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "TG引客机器人", "删", "删除TG机器人")
	if token == nil {
		return
	}
	if ctx.RespErr(server.VerifyGoogleCode(token.GoogleSecret, reqdata.GoogleCode), &errcode) {
		return
	}

	dao := server.DaoxHashGame().XTgRobotGuide
	db := dao.WithContext(ctx.Gin())
	_, err := db.Where(dao.ID.Eq(reqdata.ID)).Delete()
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

// 小时统计数据
func (c *TgRobotController) statHourList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int32
		Page      int
		PageSize  int
		StartTime *time.Time
		EndTime   *time.Time
		RobotId   int64
		Export    bool
	}
	type Count struct {
		StartCount        int `gorm:"column:StartCount"`
		InDbStartCount    int `gorm:"column:InDbStartCount"`
		NotInDbStartCount int `gorm:"column:NotInDbStartCount"`
		RegCount          int `gorm:"column:RegCount"`
		GetUsdtCount      int `gorm:"column:GetUsdtCount"`
		GetTrxCount       int `gorm:"column:GetTrxCount"`
		ActivationCount   int `gorm:"column:ActivationCount"`
	}
	type Result struct {
		RecordTime    time.Time `gorm:"column:RecordTime"`
		RobotId       int64     `gorm:"column:RobotId"`
		SellerId      int32     `gorm:"column:SellerId"`
		ChannelId     int32     `gorm:"column:ChannelId"`
		ChannelName   string    `gorm:"column:ChannelName"`
		RobotUserName string    `gorm:"column:RobotUserName"`
		Count
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "小时统计", "查", "TG机器人小时统计查询")
	if token == nil {
		return
	}

	daoStat := server.DaoxHashStat().XTgRobotStatHour
	dbStat := daoStat.WithContext(ctx.Gin())
	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}
	where := func(db gen.Dao) gen.Dao {
		if reqdata.StartTime != nil && reqdata.EndTime != nil && !reqdata.StartTime.IsZero() && !reqdata.EndTime.IsZero() {
			db = db.Where(daoStat.RecordTime.Between(*reqdata.StartTime, *reqdata.EndTime))
		}
		if reqdata.RobotId != 0 {
			db = db.Where(daoStat.RobotID.Eq(reqdata.RobotId))
		}
		if reqdata.SellerId > 0 {
			db = db.Where(daoStat.SellerID.Eq(reqdata.SellerId))
		}
		return db
	}

	conds1 := []field.Expr{
		daoStat.StartCount.Sum().As("StartCount"), daoStat.IsInResourceDb.Sum().As("InDbStartCount"),
		daoStat.IsReg.Sum().As("RegCount"), daoStat.IsGetUSDT.Sum().As("GetUsdtCount"),
		daoStat.IsGetTRX.Sum().As("GetTrxCount"), daoStat.IsActivation.Sum().As("ActivationCount"),
	}
	conds2 := append([]field.Expr{
		daoStat.RecordTime, daoStat.RobotID, daoStat.SellerID, daoStat.ChannelID,
	}, conds1...)

	var err error
	var list []Result
	var total int64
	var count []Count
	if reqdata.Export {
		err = dbStat.Scopes(where).Select(conds2...).
			Group(daoStat.RecordTime, daoStat.RobotID, daoStat.SellerID, daoStat.ChannelID).
			Order(daoStat.RecordTime.Desc()).Scan(&list)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else {
		total, err = dbStat.Scopes(where).Select(conds2...).
			Group(daoStat.RecordTime, daoStat.RobotID, daoStat.SellerID, daoStat.ChannelID).
			Order(daoStat.RecordTime.Desc()).ScanByPage(&list, offset, limit)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		err = dbStat.Scopes(where).Select(conds1...).Scan(&count)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	}

	daoChan := server.DaoxHashGame().XChannel
	dbChan := daoChan.WithContext(ctx.Gin())
	daoBot := server.DaoxHashGame().XTgRobotGuide
	dbBot := daoBot.WithContext(ctx.Gin())
	for i, v := range list {
		list[i].NotInDbStartCount = v.StartCount - v.InDbStartCount
		xChan, err := dbChan.Select(daoChan.ChannelName).Where(daoChan.ChannelID.Eq(v.ChannelId)).First()
		if err != nil {
			continue
		}
		list[i].ChannelName = xChan.ChannelName
		xBot, err := dbBot.Select(daoBot.TgRobotUserName).Where(daoBot.ID.Eq(v.RobotId)).First()
		if err != nil {
			continue
		}
		list[i].RobotUserName = xBot.TgRobotUserName
	}
	if len(count) > 0 {
		count[0].NotInDbStartCount = count[0].StartCount - count[0].InDbStartCount
	}

	if reqdata.Export {
		xdata := &xgo.XMaps{}
		xdata.RawData = []xgo.XMap{}
		for _, v := range list {
			data, _ := json.Marshal(v)
			m := make(map[string]interface{})
			_ = json.Unmarshal(data, &m)
			xdata.RawData = append(xdata.RawData, xgo.XMap{RawData: m})
		}
		filename := "export_tgRobot_StatHourList_" + time.Now().Format("**************")
		xgo.Export(server.ExportDir()+"/"+filename, xdata, TGROBOT_STAT_HOUR_LIST_OPTIONS)
		ctx.Put("filename", "/exports/"+filename+".xlsx")
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.Put("count", count[0])
	}
	ctx.RespOK()
}

// 日统计
func (c *TgRobotController) statDateList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId  int32
		Page      int
		PageSize  int
		StartTime *time.Time
		EndTime   *time.Time
		RobotIds  []int64
		Export    bool
	}
	type Count struct {
		StartCount                 int `gorm:"column:StartCount"`                 //【启动人数】当天启动注册的账号数
		InDbStartCount             int `gorm:"column:InDbStartCount"`             //【在库启动人数】当天启动注册且在库的账号数
		NotInDbStartCount          int `gorm:"column:NotInDbStartCount"`          //【不在库启动人数】当天启动注册且不在库的账号数
		RegCount                   int `gorm:"column:RegCount"`                   //【注册成功人数】当天注册成功账号数
		GetUsdtCount               int `gorm:"column:GetUsdtCount"`               //【领取USDT人数】当天领取USDT的账号数
		GetTrxCount                int `gorm:"column:GetTrxCount"`                //【领取TRX人数】当天领取TRX的账号数
		ActivationCount            int `gorm:"column:ActivationCount"`            //【激活地址人数】当天激活地址的账号数
		LoginCount                 int `gorm:"column:LoginCount"`                 // 注册成功并登陆网站的总账号数
		RegNoLoginCount            int `gorm:"column:RegNoLoginCount"`            //【注册成功未登录】当天从TG注册成功总账号数 - 注册成功并登陆网站的总账号数
		GetCount                   int `gorm:"column:GetCount"`                   // 当天领取体验金的账号
		LoginNoGetUTCount          int `gorm:"column:LoginNoGetUTCount"`          //【进入网页未领取】当天从TG注册并登陆的所有账号（包函不在库）- 当天领取体验金的账号
		RechargeAndWithdrawCount   int `gorm:"column:RechargeAndWithdrawCount"`   //【注册当日充值并当日提款】当天从TG注册且当天充值成功并且当天提款成功的账号数
		NoReChargeAndWithdrawCount int `gorm:"column:NoReChargeAndWithdrawCount"` //【注册当日未充值并当日提款】当天从TG注册且当天未充值成功并且当天提款成功的账号数
		ConcatCsCount              int `gorm:"column:ConcatCsCount"`              // 点击联系客服按钮的账号数
		FirstchargeClickCount      int `gorm:"column:FirstchargeClickCount"`      // 首充按钮点击的账号数
		FailWithIP                 int `gorm:"column:FailWithIP"`                 // 同IP领取体验金失败的账号数
		FailWithDeviceId           int `gorm:"column:FailWithDeviceId"`           // 同设备ID领取体验金失败的账号数
		FailWithAddress            int `gorm:"column:FailWithAddress"`            // 关联地址领取体验金失败的账号数
	}

	type Result struct {
		RecordDate    *time.Time `gorm:"column:RecordDate"`
		RobotId       int64      `gorm:"column:RobotId"`
		SellerId      int32      `gorm:"column:SellerId"`
		ChannelId     int32      `gorm:"column:ChannelId"`
		ChannelName   string     `gorm:"column:ChannelName"`
		RobotUserName string     `gorm:"column:RobotUserName"`
		Count
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "日统计", "查", "TG机器人日统计查询")
	if token == nil {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	if reqdata.EndTime != nil {
		// 直接对 `EndTime` 进行加时间操作
		newTime := reqdata.EndTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
		// 正确的指针赋值方式
		reqdata.EndTime = &newTime
	}

	whereList := make([]string, 0)
	whereArgs := make([]any, 0)
	if reqdata.StartTime != nil && reqdata.EndTime != nil && !reqdata.StartTime.IsZero() && !reqdata.EndTime.IsZero() {
		whereList = append(whereList, "A.RecordDate >= ? AND A.RecordDate <= ?")
		whereArgs = append(whereArgs, reqdata.StartTime, reqdata.EndTime)
	}
	if len(reqdata.RobotIds) > 0 {
		whereList = append(whereList, "A.RobotId IN (?)")
		whereArgs = append(whereArgs, reqdata.RobotIds)
	}
	if reqdata.SellerId > 0 {
		whereList = append(whereList, "A.SellerId = ?")
		whereArgs = append(whereArgs, reqdata.SellerId)
	}

	conds1 := "A.RecordDate, A.RobotId, A.SellerId, A.ChannelId, A.ChannelName, A.RobotUserName"
	conds2 := `SUM( CASE WHEN A.ActionType = 1 THEN 1 ELSE 0 END ) AS StartCount,
	SUM( CASE WHEN A.ActionType = 1 AND A.IsInResourceDb = 1 THEN 1 ELSE 0 END ) AS InDbStartCount,
	SUM( CASE WHEN A.ActionType = 2 THEN 1 ELSE 0 END ) AS RegCount,
	SUM( CASE WHEN A.ActionType = 3 THEN 1 ELSE 0 END ) AS GetUsdtCount,
	SUM( CASE WHEN A.ActionType = 4 THEN 1 ELSE 0 END ) AS GetTrxCount,
	SUM( CASE WHEN A.ActionType = 5 THEN 1 ELSE 0 END ) AS ActivationCount,
	SUM( CASE WHEN B.ActionCount > 0 THEN 1 ELSE 0 END ) AS LoginCount,
	SUM( CASE WHEN C.ActionCount > 0 THEN 1 WHEN D.ActionCount > 0 THEN 1 ELSE 0 END ) AS GetCount,
	SUM( CASE WHEN E.ActionCount > 0 AND F.ActionCount > 0 THEN 1 ELSE 0 END ) AS RechargeAndWithdrawCount,
	SUM( CASE WHEN IFNULL( E.ActionCount, 0 ) = 0 AND F.ActionCount > 0 THEN 1 ELSE 0 END ) AS NoReChargeAndWithdrawCount,
	SUM( CASE WHEN A.ActionType = 9 THEN 1 ELSE 0 END ) AS ConcatCsCount,
	SUM( CASE WHEN A.ActionType = 10 THEN 1 ELSE 0 END ) AS FirstchargeClickCount,
	SUM( CASE WHEN A.ActionType = 11 THEN 1 ELSE 0 END ) AS FailWithIP,
	SUM( CASE WHEN A.ActionType = 12 THEN 1 ELSE 0 END ) AS FailWithDeviceId,
	SUM( CASE WHEN A.ActionType = 13 THEN 1 ELSE 0 END ) AS FailWithAddress`
	selectSql := fmt.Sprintf("SELECT %s,%s", conds1, conds2)
	selectSql2 := fmt.Sprintf("SELECT %s", conds2)
	fromSql := `FROM
	x_tg_robot_stat_date AS A
	LEFT JOIN x_tg_robot_stat_date AS B ON (A.RecordDate = B.RecordDate AND A.RobotId = B.RobotId AND A.TgChatId = B.TgChatId 
	AND B.ActionType = 6 AND A.ActionType = 2) 
	LEFT JOIN x_tg_robot_stat_date AS C ON (A.RecordDate = C.RecordDate AND A.RobotId = C.RobotId AND A.TgChatId = C.TgChatId 
	AND C.ActionType IN ( 3 ) AND A.ActionType = 2)
	LEFT JOIN x_tg_robot_stat_date AS D ON (A.RecordDate = D.RecordDate AND A.RobotId = D.RobotId	AND A.TgChatId = D.TgChatId 
	AND D.ActionType IN ( 4 ) AND A.ActionType = 2)
	LEFT JOIN x_tg_robot_stat_date AS E ON (A.RecordDate = E.RecordDate AND A.RobotId = E.RobotId AND A.TgChatId = E.TgChatId 
	AND E.ActionType IN ( 7 ) AND A.ActionType = 2)
	LEFT JOIN x_tg_robot_stat_date AS F ON (A.RecordDate = F.RecordDate AND A.RobotId = F.RobotId AND A.TgChatId = F.TgChatId 
	AND F.ActionType IN ( 8 ) AND A.ActionType = 2)`
	whereSql := ""
	if len(whereList) > 0 {
		whereSql = "WHERE " + strings.Join(whereList, " AND ")
	}
	groupSql := "GROUP BY A.RecordDate, A.RobotId, A.SellerId, A.ChannelId, A.ChannelName, A.RobotUserName"
	orderSql := "ORDER BY A.RecordDate DESC"
	pageSql := "LIMIT ? OFFSET ?"

	var err error
	var list []Result
	var total int64
	var count []Count
	if reqdata.Export {
		err = server.DbStat().GormDao().
			Raw(fmt.Sprintf("%s %s %s %s %s", selectSql, fromSql, whereSql, groupSql, orderSql), whereArgs...).
			Scan(&list).Error
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else {
		err = server.DbStat().GormDao().
			Raw(fmt.Sprintf("SELECT Count(*) FROM (%s %s %s %s) AS o", selectSql, fromSql, whereSql, groupSql), whereArgs...).
			Count(&total).Error
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		args := append(whereArgs, limit, offset)
		err = server.DbStat().GormDao().
			Raw(fmt.Sprintf("%s %s %s %s %s %s", selectSql, fromSql, whereSql, groupSql, orderSql, pageSql), args...).
			Scan(&list).Error
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		err = server.DbStat().GormDao().
			Raw(fmt.Sprintf("%s %s %s", selectSql2, fromSql, whereSql), whereArgs...).
			Scan(&count).Error
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	}

	for i, v := range list {
		list[i].NotInDbStartCount = v.StartCount - v.InDbStartCount
		list[i].RegNoLoginCount = v.RegCount - v.LoginCount
		list[i].LoginNoGetUTCount = v.LoginCount - v.GetCount
	}
	if len(count) > 0 {
		count[0].NotInDbStartCount = count[0].StartCount - count[0].InDbStartCount
		count[0].RegNoLoginCount = count[0].RegCount - count[0].LoginCount
		count[0].LoginNoGetUTCount = count[0].LoginCount - count[0].GetCount
	}

	if reqdata.Export {
		xdata := &xgo.XMaps{}
		xdata.RawData = []xgo.XMap{}
		for _, v := range list {
			data, _ := json.Marshal(v)
			m := make(map[string]interface{})
			_ = json.Unmarshal(data, &m)
			xdata.RawData = append(xdata.RawData, xgo.XMap{RawData: m})
		}
		filename := "export_tgRobot_StatDateList_" + time.Now().Format("**************")
		xgo.Export(server.ExportDir()+"/"+filename, xdata, TGROBOT_STAT_DATE_LIST_OPTIONS)
		ctx.Put("filename", "/exports/"+filename+".xlsx")
	} else {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.Put("count", count[0])
	}
	ctx.RespOK()
}

// 市场接待报表
func (c *TgRobotController) kefuTyjReport(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId    int
		Page        int
		PageSize    int
		StartTime   string
		EndTime     string
		KefuAccount string
	}
	type Result struct {
		StatDate       time.Time
		KefuAccount    string
		RegUsers       int
		TrxApplyUsers  int
		TrxSendUsers   int
		UsdtSendUsers  int
		GetNotAllUsers int
		GetAllUsers    int
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "市场接待报表", "查", "查询TG市场接待报表")
	if token == nil {
		return
	}

	sellerId := strconv.Itoa(reqdata.SellerId)
	if reqdata.SellerId == 0 {
		sellerId = ""
	}

	var list []Result
	var total int64

	rows, err := server.Db().GormDao().Raw("call ReportManage_x_tg_account_GetListByDate(?,?,?,?,?,?,?)",
		reqdata.StartTime, reqdata.EndTime, sellerId, "", reqdata.KefuAccount, reqdata.Page, reqdata.PageSize).
		Rows()
	if ctx.RespErr(err, &errcode) {
		return
	}

	if rows.Next() {
		err = rows.Scan(&total)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}
	if rows.NextResultSet() {
		list = make([]Result, 0)
		for rows.Next() {
			data := Result{}
			err = rows.Scan(&data.StatDate, &data.KefuAccount, &data.RegUsers, &data.TrxApplyUsers, &data.TrxSendUsers,
				&data.UsdtSendUsers, &data.GetNotAllUsers, &data.GetAllUsers)
			if ctx.RespErr(err, &errcode) {
				return
			}
			list = append(list, data)
		}
	}

	ctx.Put("list", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 体验金激活名单
func (c *TgRobotController) tyjUserList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId        int32
		ChannelId       int32
		Page            int
		PageSize        int
		Type            int // 0:全部 1:已领取全部体验金 2:未领取全部体验金 3:刷子 4:不在库
		StartTime       string
		EndTime         string
		KefuAccount     string
		TgRobotUserName string
	}
	type Result struct {
		SellerName      string
		RegisterTime    time.Time
		ChannelName     string
		Account         string
		UserId          int32
		LoginIpCount    int32
		AddressCount    int32 // 钱包地址重复数量
		IsInResourceDb  int32 // 是否在库(0:否 1:是)
		TrxState        int32 // 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
		UsdtState       int32 // 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
		TgRobotUserName string
		KefuTgUserName  string
		KefuAccount     string
		Type            int    // 0:全部 1:已领取全部体验金 2:未领取全部体验金 3:刷子 4:不在库
		Memo            string // 客服备注
	}
	errcode := 0
	reqdata := RequestData{}

	token := server.GetRequestDataAndToken(ctx, &errcode, ctx.RequestData(&reqdata), reqdata, "机器人管理", "体验金激活名单", "查", "查询TG体验金激活名单")
	if token == nil {
		return
	}

	var limit, offset int
	limit = reqdata.PageSize
	if reqdata.Page > 0 {
		offset = (reqdata.Page - 1) * reqdata.PageSize
	}

	xUser := server.DaoxHashGame().XUser
	xSeller := server.DaoxHashGame().XSeller
	xChannel := server.DaoxHashGame().XChannel
	xTiyanjinTrx := server.DaoxHashGame().XTiyanjing.As("tyj_trx")
	xTiyanjinUsdt := server.DaoxHashGame().XTiyanjing.As("tyj_usdt")
	xTgRobot := server.DaoxHashGame().XTgRobotGuide
	xTgAccount := server.DaoxHashGame().XTgAccount
	db := xUser.WithContext(ctx.Gin()).
		LeftJoin(xSeller, xSeller.SellerID.EqCol(xUser.SellerID)).
		LeftJoin(xChannel, xChannel.ChannelID.EqCol(xUser.ChannelID)).
		LeftJoin(xTiyanjinTrx, xTiyanjinTrx.UserID.EqCol(xUser.UserID), xTiyanjinTrx.Symbol.Eq("trx")).
		LeftJoin(xTiyanjinUsdt, xTiyanjinUsdt.UserID.EqCol(xUser.UserID), xTiyanjinUsdt.Symbol.Eq("usdt")).
		LeftJoin(xTgRobot, xTgRobot.TgRobotToken.EqCol(xUser.TgRobotToken)).
		LeftJoin(xTgAccount, xTgAccount.ID.EqCol(xTgRobot.TgAccountID))

	where1 := func(db gen.Dao) gen.Dao {
		db = db.Where(xUser.AccountType.Eq(5))
		if reqdata.SellerId > 0 {
			db = db.Where(xUser.SellerID.Eq(reqdata.SellerId))
		}
		if reqdata.ChannelId > 0 {
			db = db.Where(xUser.ChannelID.Eq(reqdata.ChannelId))
		}
		return db
	}
	where2 := func(db gen.Dao) gen.Dao {
		if reqdata.Type > 0 && reqdata.Type <= 4 {
			if reqdata.Type == 1 {
				db = db.Where(xTiyanjinTrx.State.Gt(0)).Where(xTiyanjinUsdt.State.Gt(0)).Where(xTiyanjinTrx.LoginIPCount.Eq(0))
			} else if reqdata.Type == 2 {
				db = db.Where(db.Where(xTiyanjinTrx.State.IsNull()).Or(xTiyanjinUsdt.State.IsNull()).Where(xTiyanjinTrx.LoginIPCount.Eq(0))).
					Where(xUser.IsInResourceDb.Eq(1))
			} else if reqdata.Type == 3 {
				db = db.Where(db.Where(xTiyanjinTrx.LoginIPCount.Gt(0)).Or(xTiyanjinTrx.AddressCount.Gt(0)))
			} else if reqdata.Type == 4 {
				db = db.Where(xUser.IsInResourceDb.Eq(0))
			}
		}
		if reqdata.StartTime != "" && reqdata.EndTime != "" {
			db = db.Where(xUser.RegisterTime.DateFormat("%Y-%m-%d").Gte(reqdata.StartTime)).
				Where(xUser.RegisterTime.DateFormat("%Y-%m-%d").Lte(reqdata.EndTime))
		}
		if reqdata.KefuAccount != "" {
			db = db.Where(xTgAccount.KefuAccount.Eq(reqdata.KefuAccount))
		}
		if reqdata.TgRobotUserName != "" {
			db = db.Where(xTgRobot.TgRobotUserName.Eq(reqdata.TgRobotUserName))
		}
		return db
	}

	conds := []field.Expr{
		xUser.RegisterTime, xSeller.SellerName, xChannel.ChannelName, xUser.Account, xUser.UserID,
		xTiyanjinTrx.LoginIPCount.IfNull(0).As("LoginIpCount"),
		xTiyanjinTrx.AddressCount.IfNull(0).As("AddressCount"),
		xTiyanjinTrx.Memo, xUser.IsInResourceDb,
		xTiyanjinTrx.State.IfNull(0).As("TrxState"),
		xTiyanjinUsdt.State.IfNull(0).As("UsdtState"),
		xTgRobot.TgRobotUserName, xTgRobot.KefuTgUserName, xTgAccount.KefuAccount,
	}

	var err error
	var list []Result
	var total int64
	total, err = db.Scopes(where1, where2).Select(conds...).Order(xUser.ID.Desc()).ScanByPage(&list, offset, limit)
	if ctx.RespErr(err, &errcode) {
		return
	}
	for i, v := range list {
		// Type 0:全部 1:已领取全部体验金 2:未领取全部体验金 3:刷子 4:不在库
		if v.TrxState > 0 && v.UsdtState > 0 && v.LoginIpCount == 0 {
			list[i].Type = 1
		}
		if (v.TrxState == 0 || v.UsdtState == 0) && v.LoginIpCount == 0 {
			list[i].Type = 2
		}
		if v.LoginIpCount > 0 || v.AddressCount > 0 {
			list[i].Type = 3
		}
		if v.IsInResourceDb == 0 {
			list[i].Type = 4
		}
	}
	ctx.Put("list", list)
	ctx.Put("total", total)
	ctx.RespOK()
}
