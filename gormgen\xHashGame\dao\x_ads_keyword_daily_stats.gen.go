// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAdsKeywordDailyStat(db *gorm.DB, opts ...gen.DOOption) xAdsKeywordDailyStat {
	_xAdsKeywordDailyStat := xAdsKeywordDailyStat{}

	_xAdsKeywordDailyStat.xAdsKeywordDailyStatDo.UseDB(db, opts...)
	_xAdsKeywordDailyStat.xAdsKeywordDailyStatDo.UseModel(&model.XAdsKeywordDailyStat{})

	tableName := _xAdsKeywordDailyStat.xAdsKeywordDailyStatDo.TableName()
	_xAdsKeywordDailyStat.ALL = field.NewAsterisk(tableName)
	_xAdsKeywordDailyStat.ID = field.NewInt64(tableName, "id")
	_xAdsKeywordDailyStat.SellerID = field.NewInt32(tableName, "seller_id")
	_xAdsKeywordDailyStat.ChannelID = field.NewInt32(tableName, "channel_id")
	_xAdsKeywordDailyStat.TopAgentID = field.NewInt64(tableName, "top_agent_id")
	_xAdsKeywordDailyStat.KeyWord = field.NewString(tableName, "key_word")
	_xAdsKeywordDailyStat.StatDate = field.NewTime(tableName, "stat_date")
	_xAdsKeywordDailyStat.CreateTime = field.NewTime(tableName, "create_time")
	_xAdsKeywordDailyStat.UpdateTime = field.NewTime(tableName, "update_time")

	_xAdsKeywordDailyStat.fillFieldMap()

	return _xAdsKeywordDailyStat
}

// xAdsKeywordDailyStat 关键词搜索每日统计表
type xAdsKeywordDailyStat struct {
	xAdsKeywordDailyStatDo xAdsKeywordDailyStatDo

	ALL        field.Asterisk
	ID         field.Int64  // 主键ID
	SellerID   field.Int32  // 运营商ID
	ChannelID  field.Int32  // 渠道ID
	TopAgentID field.Int64  // 顶级代理ID
	KeyWord    field.String // 搜索关键词
	StatDate   field.Time   // 统计日期
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAdsKeywordDailyStat) Table(newTableName string) *xAdsKeywordDailyStat {
	x.xAdsKeywordDailyStatDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAdsKeywordDailyStat) As(alias string) *xAdsKeywordDailyStat {
	x.xAdsKeywordDailyStatDo.DO = *(x.xAdsKeywordDailyStatDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAdsKeywordDailyStat) updateTableName(table string) *xAdsKeywordDailyStat {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.TopAgentID = field.NewInt64(table, "top_agent_id")
	x.KeyWord = field.NewString(table, "key_word")
	x.StatDate = field.NewTime(table, "stat_date")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xAdsKeywordDailyStat) WithContext(ctx context.Context) *xAdsKeywordDailyStatDo {
	return x.xAdsKeywordDailyStatDo.WithContext(ctx)
}

func (x xAdsKeywordDailyStat) TableName() string { return x.xAdsKeywordDailyStatDo.TableName() }

func (x xAdsKeywordDailyStat) Alias() string { return x.xAdsKeywordDailyStatDo.Alias() }

func (x xAdsKeywordDailyStat) Columns(cols ...field.Expr) gen.Columns {
	return x.xAdsKeywordDailyStatDo.Columns(cols...)
}

func (x *xAdsKeywordDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAdsKeywordDailyStat) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["top_agent_id"] = x.TopAgentID
	x.fieldMap["key_word"] = x.KeyWord
	x.fieldMap["stat_date"] = x.StatDate
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xAdsKeywordDailyStat) clone(db *gorm.DB) xAdsKeywordDailyStat {
	x.xAdsKeywordDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAdsKeywordDailyStat) replaceDB(db *gorm.DB) xAdsKeywordDailyStat {
	x.xAdsKeywordDailyStatDo.ReplaceDB(db)
	return x
}

type xAdsKeywordDailyStatDo struct{ gen.DO }

func (x xAdsKeywordDailyStatDo) Debug() *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Debug())
}

func (x xAdsKeywordDailyStatDo) WithContext(ctx context.Context) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAdsKeywordDailyStatDo) ReadDB() *xAdsKeywordDailyStatDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAdsKeywordDailyStatDo) WriteDB() *xAdsKeywordDailyStatDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAdsKeywordDailyStatDo) Session(config *gorm.Session) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAdsKeywordDailyStatDo) Clauses(conds ...clause.Expression) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAdsKeywordDailyStatDo) Returning(value interface{}, columns ...string) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAdsKeywordDailyStatDo) Not(conds ...gen.Condition) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAdsKeywordDailyStatDo) Or(conds ...gen.Condition) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAdsKeywordDailyStatDo) Select(conds ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAdsKeywordDailyStatDo) Where(conds ...gen.Condition) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAdsKeywordDailyStatDo) Order(conds ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAdsKeywordDailyStatDo) Distinct(cols ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAdsKeywordDailyStatDo) Omit(cols ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAdsKeywordDailyStatDo) Join(table schema.Tabler, on ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAdsKeywordDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAdsKeywordDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAdsKeywordDailyStatDo) Group(cols ...field.Expr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAdsKeywordDailyStatDo) Having(conds ...gen.Condition) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAdsKeywordDailyStatDo) Limit(limit int) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAdsKeywordDailyStatDo) Offset(offset int) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAdsKeywordDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAdsKeywordDailyStatDo) Unscoped() *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAdsKeywordDailyStatDo) Create(values ...*model.XAdsKeywordDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAdsKeywordDailyStatDo) CreateInBatches(values []*model.XAdsKeywordDailyStat, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAdsKeywordDailyStatDo) Save(values ...*model.XAdsKeywordDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAdsKeywordDailyStatDo) First() (*model.XAdsKeywordDailyStat, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsKeywordDailyStat), nil
	}
}

func (x xAdsKeywordDailyStatDo) Take() (*model.XAdsKeywordDailyStat, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsKeywordDailyStat), nil
	}
}

func (x xAdsKeywordDailyStatDo) Last() (*model.XAdsKeywordDailyStat, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsKeywordDailyStat), nil
	}
}

func (x xAdsKeywordDailyStatDo) Find() ([]*model.XAdsKeywordDailyStat, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAdsKeywordDailyStat), err
}

func (x xAdsKeywordDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAdsKeywordDailyStat, err error) {
	buf := make([]*model.XAdsKeywordDailyStat, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAdsKeywordDailyStatDo) FindInBatches(result *[]*model.XAdsKeywordDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAdsKeywordDailyStatDo) Attrs(attrs ...field.AssignExpr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAdsKeywordDailyStatDo) Assign(attrs ...field.AssignExpr) *xAdsKeywordDailyStatDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAdsKeywordDailyStatDo) Joins(fields ...field.RelationField) *xAdsKeywordDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAdsKeywordDailyStatDo) Preload(fields ...field.RelationField) *xAdsKeywordDailyStatDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAdsKeywordDailyStatDo) FirstOrInit() (*model.XAdsKeywordDailyStat, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsKeywordDailyStat), nil
	}
}

func (x xAdsKeywordDailyStatDo) FirstOrCreate() (*model.XAdsKeywordDailyStat, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAdsKeywordDailyStat), nil
	}
}

func (x xAdsKeywordDailyStatDo) FindByPage(offset int, limit int) (result []*model.XAdsKeywordDailyStat, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAdsKeywordDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAdsKeywordDailyStatDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAdsKeywordDailyStatDo) Delete(models ...*model.XAdsKeywordDailyStat) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAdsKeywordDailyStatDo) withDO(do gen.Dao) *xAdsKeywordDailyStatDo {
	x.DO = *do.(*gen.DO)
	return x
}
