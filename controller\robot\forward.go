package robot

import (
	"fmt"
	"strings"
	"xserver/abugo"
	"xserver/server"
	//"github.com/beego/beego/logs"
	//"github.com/go-resty/resty/v2"
	//"github.com/spf13/viper"
)

func (c *Router) reportByForward(ctx *abugo.AbuHttpContent) {
	errCode := 0

	type RequestData struct {
		Page      int      `json:"page"`
		PageSize  int      `json:"page_size"`
		StartTime int64    `json:"start_time"`
		EndTime   int64    `json:"end_time"`
		SellerID  []int32  `json:"seller_id"`
		ChannelID []int32  `json:"channel_id"`
		Name      []string `json:"name"`
		UserName  []string `json:"user_chat_name"`
		IsExport  int      `json:"is_export"` // 是否导出 默认0 不导出
	}

	req := RequestData{}
	token := server.GetRequestDataAndToken(ctx, &errCode, ctx.RequestData(&req), req, "机器人管理", "红包机器人数据统计", "查", "查询广告机器人数据统计")
	if token == nil {
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize

	cnfDao := server.DaoxHashGame().XRobotConfig
	dataList, _ := cnfDao.WithContext(nil).
		Select(cnfDao.Keywords).
		Where(cnfDao.Name.In(req.Name...)).Find()
	if len(dataList) == 0 {
		ctx.RespErrString(true, &errCode, "缺少关键字")
	}

	where := abugo.AbuDbWhere{}
	groupSQL := "GROUP BY bot_name,group_id,group_name ,keyword "
	subSelect := ",SUM(CASE WHEN keyword = '%s' THEN total_keyword ELSE 0 END) AS cnt%d"

	where.Add("and", "1", "=", "1", nil)

	if len(req.Name) > 0 {
		where.Add("and", "bot_name", "in", SliceToSQLTuple(req.Name), nil)
	}

	subSQL := ""
	for _, data := range dataList {
		groups := strings.Split(strings.TrimSpace(data.Keywords), ",")
		if len(groups) > 0 {
			for num, keyword := range groups {
				subSQL += fmt.Sprintf(subSelect, keyword, num)
			}
		}
	}
	whereSQL, whereData := where.Sql()
	sql := fmt.Sprintf(`
	SELECT bot_name, group_id , group_name ,sum(total_keyword) total_keyword 
	%s
	FROM (
	SELECT  bot_name,group_id,group_name , keyword, COUNT(keyword) total_keyword  
	FROM  x_hash_game.x_robot_forward_keyword_statistics
	WHERE  
	%s
	%s
	) tmp 	
	GROUP BY bot_name,group_id,group_name 
	ORDER BY  group_name 
	LIMIT %d OFFSET %d `, subSQL, whereSQL, groupSQL, limit, offset)
	results, err := server.Db().Query(sql, whereData)

	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	sqlTotal := fmt.Sprintf(`
		SELECT COUNT(1) AS total FROM (
		SELECT bot_name, group_id , group_name , sum(total_keyword) total_keyword 
		%s
		FROM (
		SELECT  bot_name,group_id,group_name , keyword, COUNT(keyword) total_keyword
		FROM  x_hash_game.x_robot_forward_keyword_statistics
		WHERE  
		%s
		%s
		) tmp 	
		GROUP BY bot_name,group_id,group_name  
		ORDER BY  group_name  ) tmp1 
		`, subSQL, whereSQL, groupSQL)
	pTotal, err := server.Db().Query(sqlTotal, whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	sqlSum := fmt.Sprintf(`
	SELECT  "合计" group_name ,   sum(total_keyword) total_keyword  
	%s
	FROM (
	SELECT  bot_name,group_id,group_name , keyword, COUNT(keyword) total_keyword  
	FROM  x_hash_game.x_robot_forward_keyword_statistics
	WHERE  
	%s
	%s
	) tmp 	
	ORDER BY  group_name 
	LIMIT %d OFFSET %d `, subSQL, whereSQL, groupSQL, limit, offset)
	pSum, err := server.Db().Query(sqlSum, whereData)
	if err != nil {
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	if pTotal == nil || results == nil || len(*pTotal) < 1 || pSum == nil {
		ctx.RespErrString(true, &errCode, "数据为空")
		return
	}

	ctx.Put("total", (*pTotal)[0])
	ctx.Put("results", results)
	ctx.Put("sum", pSum)
	ctx.RespOK()
}
