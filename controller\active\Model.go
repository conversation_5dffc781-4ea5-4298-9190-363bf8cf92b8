// Package active 包含活动相关的数据模型定义
package active

// RechargeBaseConfig 首充复充活动基础配置
type RechargeBaseConfig struct {
	IsBindEmail        bool    `json:"IsBindEmail"`        // 是否需要绑定邮箱
	IsDuringReg        bool    `json:"IsDuringReg"`        // 是否活动期间内注册的账号
	IsValidWallet      bool    `json:"IsValidWallet"`      // 是否有效的钱包地址
	IsDeviceLimit      bool    `json:"IsDeviceLimit"`      // 同设备号是否可参与
	MaxIPAttempts      int     `json:"MaxIPAttempts"`      // 同一IP最大领取次数，0表示不限制
	MaxIDAttempts      int     `json:"MaxIDAttempts"`      // 同ID最大领取次数，0表示不限制
	MaxDailyIDAttempts int     `json:"MaxDailyIDAttempts"` // 同用户ID每天最大领取次数，0表示不限制
	BlockedIPList      string  `json:"BlockedIPList"`      // 被限制参与的IP，多个IP用逗号分隔
	RegisterDay        int32   `json:"RegisterDay"`        // 注册天数限制
	ReceiveDay         int32   `json:"ReceiveDay"`         // 领取天数限制
	RechargeCount      int32   `json:"RechargeCount"`      // 充值次数要求
	ActivityMethod     int32   `json:"ActivityMethod"`     // 活动方式：1=前置（先领奖励后打流水），2=后置（先打流水再领奖励)
	MinBetAmount       float32 `json:"MinBetAmount"`       // 最小投注金额限制
	MaxBetAmount       float32 `json:"MaxBetAmount"`       // 最大投注金额限制
	BetType            int32   `json:"BetType"`            // 打码条件：1=真金，2=彩金,3=彩金+真金
	IsCalcActiveWager  bool    `json:"IsCalcActiveWager"`  // 玩家参与此活动所产生的流水是否纳入会员返水及代理返佣统计，由前端传递
	RewardWalletType   int32   `json:"RewardWalletType"`   // 奖励账户类型，0=真金账户，1=彩金账户
	TotalRewardLimit   float32 `json:"TotalRewardLimit"`   // 总派发金额上限
	DailyRewardLimit   float32 `json:"DailyRewardLimit"`   // 每日派发金额上限
}

// RechargeConfig 首充复充活动奖励配置
// 可以配置多个档位（一档、二档、N档），每个档位对应一个RechargeConfig结构
type RechargeConfig struct {
	ID                   int32   `json:"Id"`                   // 档位ID，从1开始递增
	FirstChargeUstdLimit float32 `json:"FirstChargeUstdLimit"` // 首次单笔最低充值(U)
	LiushuiMultiple      int32   `json:"LiushuiMultiple"`      // 真金流水倍数
	BonusMultiple        int32   `json:"BonusMultiple"`        // 彩金流水倍数
	GiveProportion       float32 `json:"GiveProportion"`       // 赠送比例
	GiveAmount           float32 `json:"GiveAmount"`           // 赠送金额
	GiveLimit            float32 `json:"GiveLimit"`            // 赠送上限(U)
}

// AwardData 彩金钱包下的充值活动奖励配置数据
type RechargeAwardData struct {
	AwardBetType int `json:"AwardBetType"` // 奖励打码倍数类型 1: 真金 2: 彩金 3: 彩金+真金
	// 激活期限配置
	ActivationType           int   `json:"ActivationType"`           // 激活期限类型 1 无期限 2 倒计时 3 自定义
	ActivetionCountdownCount int   `json:"ActivetionCountdownCount"` // 激活期限，倒计时数值
	ActivetionCountdownType  int   `json:"ActivetionCountdownType"`  // 激活期限 倒计时类型 1 天 2 小时
	ActivetionDate           int64 `json:"ActivetionDate"`           // 激活期限 自定义结束时间(毫秒)

	// 流水完成期限配置
	TurnoverType           int   `json:"TurnoverType"`           // 流水完成期限类型 1 无期限 2 倒计时 3 自定义
	TurnoverCountdownCount int   `json:"TurnoverCountdownCount"` // 流水完成期限，倒计时数值
	TurnoverCountdownType  int   `json:"TurnoverCountdownType"`  // 流水完成期限 倒计时类型 1 天 2 小时
	TurnoverDate           int64 `json:"TurnoverDate"`           // 流水完成期限 自定义结束时间(毫秒)

	// 领取期限配置
	ReceiveType           int   `json:"ReceiveType"`           // 领取期限类型 1 无期限 2 倒计时 3 自定义
	ReceiveCountdownCount int   `json:"ReceiveCountdownCount"` // 领取期限，倒计时数值
	ReceiveCountdownType  int   `json:"ReceiveCountdownType"`  // 领取期限 倒计时类型 1 天 2 小时
	ReceiveDate           int64 `json:"ReceiveDate"`           // 领取期限 自定义结束时间(毫秒)

	// 投注限制配置
	MinBetAmount int `json:"MinBetAmount"` // 奖励单笔投注限制 最小值
	MaxBetAmount int `json:"MaxBetAmount"` // 奖励单笔投注限制 最大值

	// 游戏限制配置
	LimitGameType     int    `json:"LimitGameType"`     // 奖励投注游戏限制 1 不限制 2 限制
	AwardVenueCfg     string `json:"AwardVenueCfg"`     // LimitGameType==2时，可以计算打码的游戏配置，json字符串
	LimitGameIds      string `json:"LimitGameIds"`      // 被限制参与的游戏ID列表 (需要特殊限制的游戏ID，限制后这些游戏不参与活动打码)
	LimitMaxWinAmount int    `json:"LimitMaxWinAmount"` // 奖励投注最高盈利金额限制

	// 其他配置
	IsCalcAwardValid int `json:"IsCalcAwardValid"` // 奖励投注金额是否计入有效流水 1: 是 0: 否

	// 奖励配置数组
	AwardConfig []RechargeAwardConfig `json:"AwardConfig"` // 奖励配置列表，包含多个档位的配置
}

// RechargeAwardConfig 奖励配置结构体 对应界面中的奖励配置表格，包含档位、充值要求、流水倍数、赠送配置等
type RechargeAwardConfig struct {
	ID                   int32   `json:"Id"`                   // 档位ID，从1开始递增
	FirstChargeUstdLimit float32 `json:"FirstChargeUstdLimit"` // 首次单笔最低充值(U)
	LiushuiMultiple      int32   `json:"LiushuiMultiple"`      // 真金流水倍数
	BonusMultiple        int32   `json:"BonusMultiple"`        // 彩金流水倍数
	GiveProportion       float32 `json:"GiveProportion"`       // 赠送比例
	GiveAmount           float32 `json:"GiveAmount"`           // 赠送金额
	GiveLimit            float32 `json:"GiveLimit"`            // 赠送上限(U)
	MaxWinLimit          float32 `json:"MaxWinLimit"`          // 奖励投注最高盈利限制
}

// CumulativeWeeklyRechargeConfig 累计周充值配置
type CumulativeWeeklyRechargeConfig struct {
	ID             int32   `json:"Id"`             // 配置ID
	RechargeAmount float32 `json:"RechargeAmount"` // 充值金额
	GiveAmount     float32 `json:"GiveAmount"`     // 赠送金额
}

// SignRewardBaseConfig 签到奖励基础配置
type SignRewardBaseConfig struct {
	MixBetLimit float32 `json:"MixBetLimit"` // 最小投注限制
	TrxPrice    float32 `json:"TrxPrice"`    // TRX价格
	RemakeDay   int8    `json:"RemakeDay"`   // 重置天数
}

// SignRewardConfig 签到奖励配置
type SignRewardConfig struct {
	ID               int32   `json:"Id"`               // 配置ID
	SignDay          int32   `json:"SignDay"`          // 签到天数
	Award            float32 `json:"Award"`            // 奖励金额
	AdditionalReward float32 `json:"AdditionalReward"` // 额外奖励
}

// SignRewardAwardData 签到奖励活动奖励配置数据
type SignRewardAwardData struct {
	AwardBetType int                `json:"AwardBetType"` // 奖励打码倍数类型 1: 真金 2: 彩金 3: 彩金+真金
	AwardConfig  []SignRewardConfig `json:"AwardConfig"`  // 奖励配置列表，包含多个档位的配置
	// 激活期限配置
	ActivationType           int   `json:"ActivationType"`           // 激活期限类型 1 无期限 2 倒计时 3 自定义
	ActivetionCountdownCount int   `json:"ActivetionCountdownCount"` // 激活期限，倒计时数值
	ActivetionCountdownType  int   `json:"ActivetionCountdownType"`  // 激活期限 倒计时类型 1 天 2 小时
	ActivetionDate           int64 `json:"ActivetionDate"`           // 激活期限 自定义结束时间(毫秒)

	// 流水完成期限配置
	TurnoverType           int   `json:"TurnoverType"`           // 流水完成期限类型 1 无期限 2 倒计时 3 自定义
	TurnoverCountdownCount int   `json:"TurnoverCountdownCount"` // 流水完成期限，倒计时数值
	TurnoverCountdownType  int   `json:"TurnoverCountdownType"`  // 流水完成期限 倒计时类型 1 天 2 小时
	TurnoverDate           int64 `json:"TurnoverDate"`           // 流水完成期限 自定义结束时间(毫秒)

	// 领取期限配置
	ReceiveType           int   `json:"ReceiveType"`           // 领取期限类型 1 无期限 2 倒计时 3 自定义
	ReceiveCountdownCount int   `json:"ReceiveCountdownCount"` // 领取期限，倒计时数值
	ReceiveCountdownType  int   `json:"ReceiveCountdownType"`  // 领取期限 倒计时类型 1 天 2 小时
	ReceiveDate           int64 `json:"ReceiveDate"`           // 领取期限 自定义结束时间(毫秒)

	// 投注限制配置
	MinBetAmount int `json:"MinBetAmount"` // 奖励单笔投注限制 最小值
	MaxBetAmount int `json:"MaxBetAmount"` // 奖励单笔投注限制 最大值

	// 游戏限制配置
	LimitGameType     int    `json:"LimitGameType"`     // 奖励投注游戏限制 1 不限制 2 限制
	AwardVenueCfg     string `json:"AwardVenueCfg"`     // LimitGameType==2时，可以计算打码的游戏配置，json字符串
	LimitGameIds      string `json:"LimitGameIds"`      // 被限制参与的游戏ID列表 (需要特殊限制的游戏ID，限制后这些游戏不参与活动打码)
	LimitMaxWinAmount int    `json:"LimitMaxWinAmount"` // 奖励投注最高盈利金额限制

	// 其他配置
	IsCalcAwardValid int `json:"IsCalcAwardValid"` // 奖励投注金额是否计入有效流水 1: 是 0: 否
}

// RecommendFriendRewardBaseConfig 推荐好友多重奖励基础配置
type RecommendFriendRewardBaseConfig struct {
	RegisterDay          int32   `json:"RegisterDay"`          // 注册天数
	FirstChargeUstdLimit float32 `json:"FirstChargeUstdLimit"` // 首次存款
	Award                float32 `json:"Award"`                // 单次奖励
	Level                int32   `json:"Level"`                // vip等级
}

// RecommendFriendRewardConfig 推荐好友多重奖励配置
type RecommendFriendRewardConfig struct {
	ID               int32   `json:"Id"`               // 配置ID
	TotalMin         int32   `json:"TotalMin"`         // 积累邀请人数最小值
	TotalMax         int32   `json:"TotalMax"`         // 积累邀请人数最大值
	AdditionalReward float32 `json:"AdditionalReward"` // 额外奖励
}

// BreakThroughConfig 闯关配置
type BreakThroughConfig struct {
	ID          int32   `json:"Id"`          // 配置ID
	LimitValue  float32 `json:"LimitValue"`  // 限制值
	RewardValue float32 `json:"RewardValue"` // 奖励值

}

// BreakThroughBaseConfig 闯关基础配置
type BreakThroughBaseConfig struct {
	TrxPrice float32 `json:"TrxPrice"` // TRX价格
}

type BreakThroughAwardData struct {
	AwardBetType int                  `json:"AwardBetType"` // 奖励打码倍数类型 1: 真金 2: 彩金 3: 彩金+真金
	AwardConfig  []BreakThroughConfig `json:"AwardConfig"`  // 闯关配置
}

// TodayBreakThroughBaseConfig 今日闯关基础配置
type TodayBreakThroughBaseConfig struct {
	TrxPrice       float32 `json:"TrxPrice"`       // TRX价格
	RechargeAmount float32 `json:"RechargeAmount"` // 充值金额
}

// EnergyWhitelistBaseConfig 能量白名单基础配置
type EnergyWhitelistBaseConfig struct {
	UstdLimit float32 `json:"UstdLimit"` // 单笔投注金额限制
}

// NewFirstDepositConfig 新首存配置
type NewFirstDepositConfig struct {
	ID              int32   `json:"Id"`              // 配置ID
	GiveProportion  float32 `json:"GiveProportion"`  // 赠送比例
	GiveLimit       float32 `json:"GiveLimit"`       // 赠送上限
	LiushuiMultiple int32   `json:"LiushuiMultiple"` // 流水倍数
}

// DailyRechargeRebateBaseConfig 每日充值洗码返利基础配置
type DailyRechargeRebateBaseConfig struct {
	RechargeMultiple      float32 `json:"RechargeMultiple"`      // 充值打码倍数流水
	FirstChargeProportion float32 `json:"FirstChargeProportion"` // 首充金额返利
	GiveLimit             float32 `json:"GiveLimit"`             // 最高赠送
}

// RecommendNewMemberGiftBaseConfig 推荐新会员充值豪礼基础配置
type RecommendNewMemberGiftBaseConfig struct {
	RedbagMember    int32   `json:"RedbagMember"`    // 红包个数
	RegisterDay     int32   `json:"RegisterDay"`     // 注册后几天累计
	RechargeAmount  float32 `json:"RechargeAmount"`  // 充值金额
	FinishMultiple  float32 `json:"FinishMultiple"`  // 完成流水倍数
	RedbagAmountMin float32 `json:"RedbagAmountMin"` // 红包金额最小值
	RedbagAmountMax float32 `json:"RedbagAmountMax"` // 红包金额最大值
}

// LuckyDiceBaseConfig 幸运骰子基础配置
type LuckyDiceBaseConfig struct {
	ID     int32   `json:"Id"`     // 配置ID
	Number int32   `json:"Number"` // 1的数量
	Award  float32 `json:"Award"`  // 奖励
}

// LuckyDiceConfig 幸运骰子配置
type LuckyDiceConfig struct {
	ID         int32 `json:"Id"`         // 配置ID
	LiushuiMin int32 `json:"LiushuiMin"` // 流水区间最小值
	LiushuiMax int32 `json:"LiushuiMax"` // 流水区间最大值
	AwardDice  int32 `json:"AwardDice"`  // 奖励骰子数量
}

// BoomingRewardConfig 爆庄奖励配置
type BoomingRewardConfig struct {
	ID                 int32   `json:"Id"`                 // 配置ID
	SingleDayExplosion float32 `json:"SingleDayExplosion"` // 单日爆庄金额
	Gift               string  `json:"Gift"`               // 礼品名称
	Value              float32 `json:"Value"`              // 礼品价值
}

// PointGiftBaseConfig 积分兑豪礼基础配置
type PointGiftBaseConfig struct {
	UstdLimit float32 `json:"UstdLimit"` // 有效投注限制
	Point     int32   `json:"Point"`     // 积分要求
}

// PointGiftConfig 积分兑豪礼配置
type PointGiftConfig struct {
	ID    int32   `json:"Id"`    // 配置ID
	Gift  string  `json:"Gift"`  // 礼品名称
	Value float32 `json:"Value"` // 礼品价值
	Point int32   `json:"Point"` // 所需积分
}

// WeeklySignActiveRewardBaseConfig 周签到活跃奖励基础配置
type WeeklySignActiveRewardBaseConfig struct {
	LiushuiMultiple []int32 `json:"LiushuiMultiple"` // 每日流水达标要求
}

// WeeklySignActiveRewardConfig 周签到活跃奖励配置
type WeeklySignActiveRewardConfig struct {
	ID     int32   `json:"Id"`     // 配置ID
	Number int32   `json:"Number"` // 每周签到次数要求
	Award  float32 `json:"Award"`  // USDT奖励金额
}

// ===============================
// 辅助函数
// ===============================

// IsValidGiveWalletType 检查赠送钱包类型是否有效
func IsValidGiveWalletType(walletType int) bool {
	return walletType == 1 || walletType == 2
}

// IsValidAwardType 检查奖金类别是否有效
func IsValidAwardType(awardType int32) bool {
	return awardType >= 1 && awardType <= 7
}

// IsValidAwardTab 检查奖金页面分类是否有效
func IsValidAwardTab(awardTab int32) bool {
	return awardTab == 1 || awardTab == 2 || awardTab == 3 || awardTab == 4 || awardTab == 1000
}
