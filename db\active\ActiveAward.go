package active

import (
	"fmt"
	"github.com/shopspring/decimal"
	"xserver/utils"
)

type ActiveAwardAudit struct {
	Id            int             `json:"Id" gorm:"column:Id"`                       //id
	SellerId      int             `json:"SellerId" gorm:"column:SellerId"`           //代理id
	ChannelId     int             `json:"ChannelId" gorm:"column:ChannelId"`         //渠道商id
	UserId        int             `json:"UserId" gorm:"column:UserId"`               //用户id
	ActiveId      int             `json:"ActiveId" gorm:"column:ActiveId"`           //活动id
	ActiveLevel   int             `json:"ActiveLevel" gorm:"column:ActiveLevel"`     //活动等级
	ActiveMemo    string          `json:"ActiveMemo" gorm:"column:ActiveMemo"`       //活动额外说明
	RecordDate    utils.MyString  `json:"RecordDate" gorm:"column:RecordDate"`       //日期
	Amount        decimal.Decimal `json:"Amount" gorm:"column:Amount"`               //活动送金
	AuditState    int             `json:"AuditState" gorm:"column:AuditState"`       //审核状态 1待审核,2审核拒绝,3审核通过,4自动通过
	AuditAccount  string          `json:"AuditAccount" gorm:"column:AuditAccount"`   //审核账号
	AuditTime     utils.MyString  `json:"AuditTime" gorm:"column:AuditTime"`         //审核时间
	AuditMemo     string          `json:"AuditMemo" gorm:"column:AuditMemo"`         //审核备注
	CreateTime    utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`       //创建时间
	OrderId       utils.MyString  `json:"OrderId" gorm:"column:OrderId"`             //活动1 注单Id
	AmountTrx     decimal.Decimal `json:"AmountTrx" gorm:"column:AmountTrx"`         //活动1 送金的trx
	GasFee        decimal.Decimal `json:"GasFee" gorm:"column:GasFee"`               //活动1 派奖上链gas费
	TotalRecharge decimal.Decimal `json:"TotalRecharge" gorm:"column:TotalRecharge"` //活动2 6 当日充值金额
	LiuShui       decimal.Decimal `json:"LiuShui" gorm:"column:LiuShui"`             //流水有效投注
	Address       string          `json:"Address" gorm:"column:Address"`             //活动1 玩家地址
	TxId          string          `json:"TxId" gorm:"column:TxId"`                   //活动1 交易哈希
	NetWinLoss    decimal.Decimal `json:"NetWinLoss" gorm:"column:NetWinLoss"`       //活动6 当日净亏损
}

type ActiveDefine struct {
	Id              int             `json:"Id" gorm:"column:Id"`                           //id
	SellerId        int             `json:"SellerId" gorm:"column:SellerId"`               //代理id
	ChannelId       int             `json:"ChannelId" gorm:"column:ChannelId"`             //活动渠道id
	ActiveId        int             `json:"ActiveId" gorm:"column:ActiveId"`               //活动id
	Memo            string          `json:"memo" gorm:"column:Memo"`                       //活动说明
	AuditType       int             `json:"AuditType" gorm:"column:AuditType"`             //审核方式 1人工审核 2自动审核
	State           int             `json:"State" gorm:"column:State"`                     //状态
	Sort            int             `json:"Sort" gorm:"column:Sort"`                       //排序权重
	EffectStartTime int64           `json:"EffectStartTime" gorm:"column:EffectStartTime"` //活动开始时间
	EffectEndTime   int64           `json:"EffectEndTime" gorm:"column:EffectEndTime"`     //活动结束时间
	Title           string          `json:"Title" gorm:"column:Title"`                     //活动名称
	TitleImg        string          `json:"TitleImg" gorm:"column:TitleImg"`               //图片
	MinLiuShui      decimal.Decimal `json:"MinLiuShui" gorm:"column:MinLiuShui"`           //提现最低流水百分比
	ExtReward       decimal.Decimal `json:"ExtReward" gorm:"column:ExtReward"`             //额外奖金 闯关活动才有
	MinDeposit      decimal.Decimal `json:"MinDeposit" gorm:"column:MinDeposit"`           //最低存款
	MaxReward       decimal.Decimal `json:"MaxReward" gorm:"column:MaxReward"`             //最大返还金额
	ValidRecharge   decimal.Decimal `json:"ValidRecharge" gorm:"column:ValidRecharge"`     //有效会员最低充值
	ValidLiuShui    decimal.Decimal `json:"ValidLiuShui" gorm:"column:ValidLiuShui"`       //有效会员最低流水
	TrxPrice        decimal.Decimal `json:"TrxPrice" gorm:"column:TrxPrice"`               //Trx按多少倍计算下注价格(同后台配置VipTrxPrice)
	Config          string          `json:"Config" gorm:"column:Config"`                   //配置
}

type ActiveDefineOld struct {
	ActiveDefine
	UpdateDate utils.MyString `json:"UpdateDate" gorm:"column:UpdateDate"` //最后更改时间
}

type ACiRiConfig struct {
	MinAwardLiuShui float64 `json:"MinAwardLiuShui"` //配置
}

type ActiveInfo struct {
	Id          int             `json:"Id" gorm:"column:Id"`               //id
	SellerId    int             `json:"SellerId" gorm:"column:SellerId"`   //代理id
	ChannelId   int             `json:"ChannelId" gorm:"column:ChannelId"` //活动渠道id
	ActiveId    int             `json:"ActiveId" gorm:"column:ActiveId"`   //活动id
	Level       int             `json:"Level" gorm:"column:Level"`         //活动等级(如果是游戏投注返水 此字段为游戏ID  1haxi 2xiaoyouxi 3dianzi 4qipai 5live 6sport 7lottery)
	LimitValue  decimal.Decimal `json:"LimitValue" gorm:"LimitValue"`      //限制值(有的是数值,有的是个数)
	RewardValue decimal.Decimal `json:"RewardValue" gorm:"RewardValue"`    //返奖值(有的是直接数值有的是比例)
	RewardJson  string          `json:"RewardJson" gorm:"RewardJson"`      //降龙伏虎活动配置细节
}

type ActiveInfoOld struct {
	ActiveInfo
	UpdateDate utils.MyString `json:"UpdateDate" gorm:"column:UpdateDate"` //最后更改时间
}

type ActiveReward struct {
	Id           int             `json:"Id" gorm:"column:Id"`                     //id
	SellerId     int             `json:"SellerId" gorm:"column:SellerId"`         //
	ChannelId    int             `json:"ChannelId" gorm:"column:ChannelId"`       //
	UserId       int             `json:"UserId" gorm:"column:UserId"`             //
	State        int             `json:"State" gorm:"column:State"`               //状态 1待审核,2审核拒绝,3审核通过,4自动通过
	ActiveId     int             `json:"ActiveId" gorm:"column:ActiveId"`         //活动id
	ActiveName   string          `json:"ActiveName" gorm:"column:ActiveName"`     //活动名
	Amount       decimal.Decimal `json:"Amount" gorm:"column:Amount"`             //活动送金
	AuditAccount string          `json:"AuditAccount" gorm:"column:AuditAccount"` //审核账号
	AuditTime    utils.MyString  `json:"AuditTime" gorm:"column:AuditTime"`       //审核时间
	AuditMemo    string          `json:"AuditMemo" gorm:"column:AuditMemo"`       //审核备注
	CreateTime   utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`     //申请时间
	TopAgentId   int             `json:"TopAgentId" gorm:"column:TopAgentId"`     //顶级代理id
}

func ActiveInfoMap(list []ActiveInfo) map[string]*ActiveInfo {
	infoMap := make(map[string]*ActiveInfo)
	for _, v := range list {
		infoMap[ActiveInfoKey(v)] = &v
	}
	return infoMap
}

func ActiveDefineMap(list []ActiveDefine) map[string]*ActiveDefine {
	defineMap := make(map[string]*ActiveDefine)
	for _, v := range list {
		defineMap[ActiveDefineKey(v)] = &v
	}
	return defineMap
}
func ActiveInfoKey(info ActiveInfo) string {
	return ActiveInfoKeyByParams(info.SellerId, info.ChannelId, info.ActiveId, info.Level)
}

func ActiveInfoKeyByParams(SellerId int, ChannelId int, ActiveId int, Level int) string {
	return fmt.Sprintf("%d_%d_%d_%d", SellerId, ChannelId, ActiveId, Level)
}

func ActiveDefineKey(aDefine ActiveDefine) string {
	return ActiveDefineKeyByParams(aDefine.SellerId, aDefine.ChannelId, aDefine.ActiveId)
}

func ActiveDefineKeyByParams(SellerId int, ChannelId int, ActiveId int) string {
	return fmt.Sprintf("%d_%d_%d", SellerId, ChannelId, ActiveId)
}

//func VerifyADefine(ADefine ActiveDefine, tn utils.MyString) error {
//	//筛选出符合条件的次日奖励活动项
//	if ADefine.Id == 0 {
//		return errors.New("没有该活动")
//	}
//	//校验状态
//	if ADefine.State != utils.ActiveStateOpen {
//		return errors.New("没有开启该活动")
//	}
//	//校验时间
//	if ADefine.EffectStartTime != 0 &&
//		ADefine.EffectEndTime != 0 {
//		LTime, RTime := time.Unix(ADefine.EffectStartTime, 0), time.Unix(ADefine.EffectEndTime, 0)
//		if !utils.IsTimeBetween(tn, LTime, RTime) {
//			return errors.New("活动时间已失效")
//		}
//	}
//	return nil
//}
