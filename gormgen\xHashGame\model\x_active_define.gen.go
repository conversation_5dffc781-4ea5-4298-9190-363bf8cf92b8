// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXActiveDefine = "x_active_define"

// XActiveDefine 活动列表
type XActiveDefine struct {
	ID              int32   `gorm:"column:Id;primaryKey;autoIncrement:true;comment:活动id" json:"Id"`                                      // 活动id
	SellerID        int32   `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                                         // 运营商
	ChannelID       int32   `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                                                        // 渠道
	ActiveID        int32   `gorm:"column:ActiveId;comment:活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎" json:"ActiveId"` // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	Memo            string  `gorm:"column:Memo;comment:活动说明" json:"Memo"`                                                                // 活动说明
	AuditType       int32   `gorm:"column:AuditType;comment:审核方式 1人工审核 2自动审核" json:"AuditType"`                                          // 审核方式 1人工审核 2自动审核
	State           int32   `gorm:"column:State;comment:状态" json:"State"`                                                                // 状态
	Sort            int32   `gorm:"column:Sort;comment:排序" json:"Sort"`                                                                  // 排序
	EffectStartTime int64   `gorm:"column:EffectStartTime;comment:活动开始时间" json:"EffectStartTime"`                                        // 活动开始时间
	EffectEndTime   int64   `gorm:"column:EffectEndTime;comment:活动截止时间" json:"EffectEndTime"`                                            // 活动截止时间
	Title           string  `gorm:"column:Title;comment:活动名称" json:"Title"`                                                              // 活动名称
	TitleImg        string  `gorm:"column:TitleImg;comment:图片" json:"TitleImg"`                                                          // 图片
	TitleImgCn      string  `gorm:"column:TitleImgCn;comment:图片中文" json:"TitleImgCn"`                                                    // 图片中文
	TitleImgEn      string  `gorm:"column:TitleImgEn;comment:图片英文" json:"TitleImgEn"`                                                    // 图片英文
	TitleImgLang    string  `gorm:"column:TitleImgLang;comment:图片其他语言" json:"TitleImgLang"`                                              // 图片其他语言
	TopImg          string  `gorm:"column:TopImg;comment:置顶图片" json:"TopImg"`                                                            // 置顶图片
	TopImgEn        string  `gorm:"column:TopImgEn;comment:置顶图片英文" json:"TopImgEn"`                                                      // 置顶图片英文
	TopImgLang      string  `gorm:"column:TopImgLang;comment:置顶图片其他语言" json:"TopImgLang"`                                                // 置顶图片其他语言
	IsTop           int32   `gorm:"column:IsTop;default:2;comment:是否置顶 1是 2否" json:"IsTop"`                                              // 是否置顶 1是 2否
	TopSort         int32   `gorm:"column:TopSort;comment:置顶顺序" json:"TopSort"`                                                          // 置顶顺序
	GameType        string  `gorm:"column:GameType;comment:游戏分类" json:"GameType"`                                                        // 游戏分类
	MinLiuShui      float64 `gorm:"column:MinLiuShui;comment:提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比" json:"MinLiuShui"`                          // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	ExtReward       float64 `gorm:"column:ExtReward;comment:额外奖金 闯关活动才有的配置" json:"ExtReward"`                                            // 额外奖金 闯关活动才有的配置
	MinDeposit      float64 `gorm:"column:MinDeposit;comment:最低存款 救援金 和 首日充次日送 活动才有的配置" json:"MinDeposit"`                               // 最低存款 救援金 和 首日充次日送 活动才有的配置
	MaxReward       float64 `gorm:"column:MaxReward;comment:最大返还金额 救援金活动才有的配置 首日充次日送最大彩金上限" json:"MaxReward"`                            // 最大返还金额 救援金活动才有的配置 首日充次日送最大彩金上限
	ValidRecharge   float64 `gorm:"column:ValidRecharge;comment:有效会员最低充值" json:"ValidRecharge"`                                          // 有效会员最低充值
	ValidLiuShui    float64 `gorm:"column:ValidLiuShui;comment:有效会员最低流水" json:"ValidLiuShui"`                                            // 有效会员最低流水
	TrxPrice        float64 `gorm:"column:TrxPrice;comment:Trx按多少倍计算下注价格(同后台配置VipTrxPrice)" json:"TrxPrice"`                             // Trx按多少倍计算下注价格(同后台配置VipTrxPrice)
	Config          string  `gorm:"column:Config;comment:活动配置" json:"Config"`                                                            // 活动配置
	BaseConfig      string  `gorm:"column:BaseConfig;comment:基础配置" json:"BaseConfig"`                                                    // 基础配置
	DetailImgLang   string  `gorm:"column:DetailImgLang;comment:详情图片其他语言" json:"DetailImgLang"`                                          // 详情图片其他语言
	IsUseDetail     int32   `gorm:"column:IsUseDetail;default:2;comment:是否使用详情图片" json:"IsUseDetail"`                                    // 是否使用详情图片
	Cats            string  `gorm:"column:Cats;default:_utf8mb4\'[]\';comment:分类" json:"Cats"`                                           // 分类
	TitleLang       string  `gorm:"column:TitleLang;comment:多语言标题" json:"TitleLang"`                                                     // 多语言标题
	PcDetailImgLang string  `gorm:"column:PcDetailImgLang;comment:pc详情图多语言版本" json:"PcDetailImgLang"`                                    // pc详情图多语言版本
	CustomerURL     string  `gorm:"column:CustomerUrl;comment:客服url" json:"CustomerUrl"`                                                 // 客服url
	AwardType       int32   `gorm:"column:AwardType;comment:奖金类别：1:返水奖励，2:升级奖励，3:每月奖励，4:充值奖励，5:签到奖励，6:闯关奖励，7:特别奖励" json:"AwardType"`     // 奖金类别：1:返水奖励，2:升级奖励，3:每月奖励，4:充值奖励，5:签到奖励，6:闯关奖励，7:特别奖励
	AwardTab        int32   `gorm:"column:AwardTab;comment:奖金页面分类：1:一般奖金，2:欢迎奖金，3:VIP奖金，4:特殊奖金，1000:不显示" json:"AwardTab"`                // 奖金页面分类：1:一般奖金，2:欢迎奖金，3:VIP奖金，4:特殊奖金，1000:不显示
	GiftWallet      int32   `gorm:"column:GiftWallet;comment:钱包类型：1 真金钱包，2 彩金钱包" json:"GiftWallet"`                                      // 钱包类型：1 真金钱包，2 彩金钱包
	AwardData       string  `gorm:"column:AwardData;comment:彩金钱包下活动数据" json:"AwardData"`                                                 // 彩金钱包下活动数据
}

// TableName XActiveDefine's table name
func (*XActiveDefine) TableName() string {
	return TableNameXActiveDefine
}
