// Package active 处理活动相关的工具函数
package active

import (
	"encoding/json"
	"errors"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

// ChatRoomActivityBaseConfig 聊天室红包活动配置
type ChatRoomActivityBaseConfig struct {
	// 活动地点多语言文案
	LocationText map[string]string `json:"LocationText"` // 活动地点多语言文案，例如{"zh":"\u804a\u5929\u5ba4","en":"Chat Room"}

	// 参与奖励多语言文案
	RewardText map[string]string `json:"RewardText"` // 参与奖励多语言文案，例如{"zh":"\u53c2\u4e0e\u5373\u53ef\u83b7\u5f97\u7ea2\u5305","en":"Participate to get red packets"}
}

// 不需要奖励配置，因为原型中没有奖励配置

// ChatRoomActivityCheckParameter 聊天室红包活动参数校验
func ChatRoomActivityCheckParameter(reqdata DefineModReq) error {
	// 检查基础配置
	if reqdata.BaseConfig == "" {
		return errors.New("基础参数需要配置")
	} else {
		var baseConfig ChatRoomActivityBaseConfig
		err := json.Unmarshal([]byte(reqdata.BaseConfig), &baseConfig)
		if err != nil {
			return errors.New("基础参数格式错误")
		}

		// 检查多语言文案
		if len(baseConfig.LocationText) == 0 {
			return errors.New("活动地点多语言文案不能为空")
		}
		if len(baseConfig.RewardText) == 0 {
			return errors.New("参与奖励多语言文案不能为空")
		}
	}

	// 不需要奖励配置
	return nil
}

// HandleChatRoomActivityAdd 处理聊天室红包活动添加
func HandleChatRoomActivityAdd(ctx *abugo.AbuHttpContent, reqdata struct {
	model.XActiveDefine
	GoogleCode string
}, errcode *int) {
	// 验证参数
	err := ChatRoomActivityCheckParameter(DefineModReq{
		Id:              int(reqdata.ID),
		SellerId:        int(reqdata.SellerID),
		ChannelId:       int(reqdata.ChannelID),
		ActiveId:        int(reqdata.ActiveID),
		Memo:            reqdata.Memo,
		AuditType:       int(reqdata.AuditType),
		State:           int(reqdata.State),
		Sort:            int(reqdata.Sort),
		EffectStartTime: reqdata.EffectStartTime,
		EffectEndTime:   reqdata.EffectEndTime,
		Title:           reqdata.Title,
		TitleImg:        reqdata.TitleImg,
		TitleImgLang:    reqdata.TitleImgLang,
		TopImg:          reqdata.TopImg,
		TopImgLang:      reqdata.TopImgLang,
		GameType:        reqdata.GameType,
		BaseConfig:      reqdata.BaseConfig,
		Config:          reqdata.Config,
	})
	if ctx.RespErr(err, errcode) {
		return
	}

	// 设置为自动审核/自动发放
	reqdata.AuditType = 2

	// 创建活动记录
	tb := &reqdata.XActiveDefine
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	err = xActiveDefine.WithContext(ctx.Gin()).Create(tb)
	if ctx.RespErr(err, errcode) {
		return
	}

	// 保存活动排序
	err = SaveActiveSort(tb.ID, tb.Sort, tb.TopSort)
	if ctx.RespErr(err, errcode) {
		return
	}
}

// HandleChatRoomActivityMod 处理聊天室红包活动修改
func HandleChatRoomActivityMod(ctx *abugo.AbuHttpContent, defineData *model.XActiveDefine, errcode *int) error {
	// 验证参数
	err := ChatRoomActivityCheckParameter(DefineModReq{
		Id:              int(defineData.ID),
		SellerId:        int(defineData.SellerID),
		ChannelId:       int(defineData.ChannelID),
		ActiveId:        int(defineData.ActiveID),
		Memo:            defineData.Memo,
		AuditType:       int(defineData.AuditType),
		State:           int(defineData.State),
		Sort:            int(defineData.Sort),
		EffectStartTime: defineData.EffectStartTime,
		EffectEndTime:   defineData.EffectEndTime,
		Title:           defineData.Title,
		TitleImg:        defineData.TitleImg,
		TitleImgLang:    defineData.TitleImgLang,
		TopImg:          defineData.TopImg,
		TopImgLang:      defineData.TopImgLang,
		GameType:        defineData.GameType,
		BaseConfig:      defineData.BaseConfig,
		Config:          defineData.Config,
	})
	if err != nil {
		ctx.RespErrString(true, errcode, err.Error())
		return err
	}

	// 更新活动记录
	dao := server.DaoxHashGame().XActiveDefine
	_, err = dao.WithContext(ctx.Gin()).
		Where(dao.ID.Eq(defineData.ID)).
		Omit(dao.SellerID, dao.ChannelID, dao.ActiveID).
		Updates(defineData)
	if err != nil {
		ctx.RespErr(err, errcode)
		return err
	}

	// 保存活动排序
	err = SaveActiveSort(defineData.ID, defineData.Sort, defineData.TopSort)
	if err != nil {
		ctx.RespErr(err, errcode)
		return err
	}
	return nil
}

// VerifyChatRoomActivity 验证用户是否可以参与聊天室红包活动
func VerifyChatRoomActivity(userId int32) (bool, error) {
	// 这里实现验证逻辑，检查用户是否可以参与聊天室红包活动
	// 由于实际验证需要与聊天室系统集成，这里简化为直接返回成功
	return true, nil
}
